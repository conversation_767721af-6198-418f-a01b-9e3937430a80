'use client';

import { useState } from 'react';
import { DiagramResponse } from '@/types';
import { generateDiagram, getDiagramUrl, getExcalidrawViewUrl } from '@/lib/api';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

export default function DiagramGenerator() {
  const [description, setDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<DiagramResponse | null>(null);

  const handleSubmit = async () => {
    if (!description.trim()) {
      setError('Please enter an architecture description');
      return;
    }

    setIsLoading(true);
    setError('');
    setResult(null);

    try {
      const data = await generateDiagram({
        description: description.trim(),
        style: 'standard'
      });
      setResult(data);
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}. Make sure the backend server is running.`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSubmit();
    }
  };

  return (
    <div className="content">
      <div className="input-section">
        <label htmlFor="description">Describe your AWS architecture:</label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Example: I need a scalable web application with a load balancer, auto-scaling EC2 instances, RDS database, and S3 for static content..."
        />
        <div className="examples">
          <strong>Try these examples:</strong>
          <ul style={{ marginLeft: '20px', marginTop: '5px' }}>
            <li>A serverless API with Lambda, API Gateway, and DynamoDB</li>
            <li>A three-tier web application with load balancer, EC2 instances, and RDS</li>
            <li>A data processing pipeline with Kinesis, Lambda, and S3</li>
            <li>A microservices architecture with ECS, ALB, and multiple databases</li>
          </ul>
        </div>
      </div>

      <button onClick={handleSubmit} disabled={isLoading}>
        Generate Architecture Diagram
      </button>

      {isLoading && <LoadingSpinner />}
      {error && <ErrorMessage message={error} />}

      {result && (
        <div className="result">
          <h2>Generated Architecture</h2>
          <div className="diagram-container">
            {result.diagram_path.endsWith('.excalidraw') ? (
              <div className="excalidraw-preview">
                <div className="excalidraw-info">
                  <h3>🎨 Excalidraw Diagram Generated!</h3>
                  <p>Your architecture diagram has been created in Excalidraw format.</p>
                  <div className="excalidraw-actions">
                    <a
                      href={getDiagramUrl(result.diagram_path)}
                      download
                      className="download-btn"
                    >
                      📥 Download .excalidraw file
                    </a>
                    <a
                      href="https://excalidraw.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="open-btn"
                    >
                      🌐 Open in Excalidraw
                    </a>
                  </div>
                  <div className="instructions">
                    <h4>How to view your diagram:</h4>
                    <ol>
                      <li>Download the .excalidraw file above</li>
                      <li>Go to <a href="https://excalidraw.com" target="_blank" rel="noopener noreferrer">excalidraw.com</a></li>
                      <li>Click "Open" and select your downloaded file</li>
                      <li>Edit, export, or share as needed!</li>
                    </ol>
                  </div>
                </div>
              </div>
            ) : (
              <img
                src={getDiagramUrl(result.diagram_path)}
                alt="AWS Architecture Diagram"
              />
            )}
          </div>
          <div className="components-info">
            <h3>Architecture Components</h3>
            <p>{result.explanation}</p>
            <div className="component-list">
              {result.components?.components?.map((comp, index) => (
                <div key={index} className="component-tag">
                  {comp.name} ({comp.type.toUpperCase()})
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
