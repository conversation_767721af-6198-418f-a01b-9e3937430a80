'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import dynamic from 'next/dynamic';
import type { ExcalidrawElement, AppState, ExcalidrawImperativeAPI } from '@excalidraw/excalidraw/types';

// Dynamically import Excalidraw with SSR disabled
const Excalidraw = dynamic(
  async () => (await import('@excalidraw/excalidraw')).Excalidraw,
  { ssr: false }
);

interface ExcalidrawCanvasProps {
  diagramData: any;
  onDiagramChange?: (elements: ExcalidrawElement[], appState: AppState) => void;
}

export default function ExcalidrawCanvas({ diagramData, onDiagramChange }: ExcalidrawCanvasProps) {
  const [excalidrawAPI, setExcalidrawAPI] = useState<ExcalidrawImperativeAPI | null>(null);
  const isUpdatingRef = useRef(false);

  // Update Excalidraw when diagram data changes
  useEffect(() => {
    if (excalidrawAPI && diagramData) {
      try {
        isUpdatingRef.current = true;
        // Update the scene with new diagram data
        excalidrawAPI.updateScene({
          elements: diagramData.elements || [],
          appState: {
            ...diagramData.appState,
            viewBackgroundColor: '#ffffff'
          }
        });
        // Reset the flag after a short delay to allow the update to complete
        setTimeout(() => {
          isUpdatingRef.current = false;
        }, 0);
      } catch (error) {
        console.error('Error updating Excalidraw scene:', error);
        isUpdatingRef.current = false;
      }
    }
  }, [diagramData, excalidrawAPI]);

  const handleChange = useCallback((elements: ExcalidrawElement[], appState: AppState) => {
    if (onDiagramChange && !isUpdatingRef.current) {
      onDiagramChange(elements, appState);
    }
  }, [onDiagramChange]);

  return (
    <div className="excalidraw-container">
      <div className="excalidraw-header">
        <h2>🎨 Architecture Diagram</h2>
        <div className="excalidraw-controls">
          <button
            onClick={() => {
              if (excalidrawAPI) {
                excalidrawAPI.resetScene();
              }
            }}
            className="control-button"
            title="Clear diagram"
          >
            🗑️ Clear
          </button>
          <button
            onClick={() => {
              if (excalidrawAPI) {
                const elements = excalidrawAPI.getSceneElements();
                const appState = excalidrawAPI.getAppState();
                const dataURL = excalidrawAPI.getSceneElementsIncludingDeleted();
                
                // Create download link
                const link = document.createElement('a');
                link.download = `architecture-${Date.now()}.excalidraw`;
                link.href = URL.createObjectURL(
                  new Blob([JSON.stringify({ elements, appState })], { type: 'application/json' })
                );
                link.click();
              }
            }}
            className="control-button"
            title="Download diagram"
          >
            💾 Download
          </button>
        </div>
      </div>
      
      <div className="excalidraw-wrapper">
        <Excalidraw
          ref={(api) => setExcalidrawAPI(api)}
          onChange={handleChange}
          initialData={{
            elements: diagramData?.elements || [],
            appState: {
              viewBackgroundColor: '#ffffff',
              currentItemFontFamily: 1,
              currentItemFontSize: 16,
              ...diagramData?.appState
            }
          }}
          UIOptions={{
            canvasActions: {
              loadScene: false,
              saveToActiveFile: false,
              export: {
                saveFileToDisk: true
              }
            }
          }}
        />
      </div>
      
      {!diagramData && (
        <div className="excalidraw-placeholder">
          <div className="placeholder-content">
            <h3>🎯 Ready to Create</h3>
            <p>Start a conversation to generate your first architecture diagram!</p>
            <div className="placeholder-features">
              <div className="feature">
                <span>✨</span>
                <span>AI-generated diagrams</span>
              </div>
              <div className="feature">
                <span>🔄</span>
                <span>Iterative updates</span>
              </div>
              <div className="feature">
                <span>✏️</span>
                <span>Manual editing</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
