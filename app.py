import os
import json
import base64
from io import By<PERSON><PERSON>
from typing import Dict, List, Optional
from datetime import datetime
import re

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
import openai
from diagrams import <PERSON><PERSON>ram, <PERSON>luster, Edge
from diagrams.aws.compute import EC2, Lambda, ECS, Fargate
from diagrams.aws.database import RDS, <PERSON>db, ElastiCache
from diagrams.aws.network import ELB, ALB, CloudFront, Route53
from diagrams.aws.storage import S3
from diagrams.aws.general import General
from diagrams.aws.integration import SQS, SNS
from diagrams.aws.analytics import Kinesis
from diagrams.aws.security import IAM, Cognito
from diagrams.aws.ml import Sagemaker

app = FastAPI()

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Set your OpenAI API key (you'll need to set this as an environment variable)
openai.api_key = "********************************************************************************************************************************************************************"

class ArchitectureRequest(BaseModel):
    description: str
    style: Optional[str] = "standard"  # standard, detailed, simple

class ArchitectureResponse(BaseModel):
    diagram_path: str
    components: Dict
    explanation: str

# Mapping of component types to AWS diagram classes
AWS_COMPONENTS = {
    "ec2": EC2,
    "lambda": Lambda,
    "ecs": ECS,
    "fargate": Fargate,
    "rds": RDS,
    "dynamodb": Dynamodb,
    "elasticache": ElastiCache,
    "s3": S3,
    "elb": ELB,
    "alb": ALB,
    "cloudfront": CloudFront,
    "route53": Route53,
    "sqs": SQS,
    "sns": SNS,
    "kinesis": Kinesis,
    "iam": IAM,
    "cognito": Cognito,
    "sagemaker": Sagemaker,
}

def parse_architecture_with_llm(description: str) -> Dict:
    """Use OpenAI to parse the architecture description"""
    
    system_prompt = """You are an AWS Solutions Architect. Parse the given description and return a JSON object with the following structure:
    {
        "title": "Architecture name",
        "components": [
            {
                "type": "component_type",  // e.g., "ec2", "rds", "lambda", "s3", "alb"
                "name": "Component name",
                "count": 1,  // number of instances
                "cluster": "cluster_name"  // optional, for grouping
            }
        ],
        "connections": [
            {
                "from": "component_name_1",
                "to": "component_name_2",
                "label": "connection_label"  // optional
            }
        ],
        "clusters": [
            {
                "name": "cluster_name",
                "label": "Cluster Label"
            }
        ]
    }
    
    Available component types: ec2, lambda, ecs, fargate, rds, dynamodb, elasticache, s3, elb, alb, cloudfront, route53, sqs, sns, kinesis, iam, cognito, sagemaker
    
    Focus on creating a clean, logical architecture based on the description."""
    
    try:
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Parse this architecture description: {description}"}
            ],
            temperature=0.7,
            max_tokens=1000
        )
        
        content = response.choices[0].message.content
        # Extract JSON from the response
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())
        else:
            raise ValueError("No valid JSON found in response")
            
    except Exception as e:
        # Fallback to a simple architecture if LLM fails
        print(f"LLM parsing failed: {e}")
        return {
            "title": "Basic Web Architecture",
            "components": [
                {"type": "alb", "name": "Load Balancer", "count": 1},
                {"type": "ec2", "name": "Web Server", "count": 2, "cluster": "web_tier"},
                {"type": "rds", "name": "Database", "count": 1, "cluster": "data_tier"}
            ],
            "connections": [
                {"from": "Load Balancer", "to": "Web Server"},
                {"from": "Web Server", "to": "Database"}
            ],
            "clusters": [
                {"name": "web_tier", "label": "Web Tier"},
                {"name": "data_tier", "label": "Data Tier"}
            ]
        }

def generate_diagram(architecture: Dict, output_path: str):
    """Generate the actual diagram using the diagrams library"""
    
    with Diagram(architecture.get("title", "AWS Architecture"), 
                 filename=output_path, 
                 show=False, 
                 direction="TB",
                 graph_attr={"fontsize": "12", "bgcolor": "white"}):
        
        components_dict = {}
        clusters_dict = {}
        
        # Create clusters first
        for cluster_info in architecture.get("clusters", []):
            cluster_name = cluster_info["name"]
            cluster_label = cluster_info["label"]
            clusters_dict[cluster_name] = Cluster(cluster_label)
        
        # Create components
        for component in architecture.get("components", []):
            comp_type = component["type"].lower()
            comp_name = component["name"]
            comp_count = component.get("count", 1)
            cluster_name = component.get("cluster")
            
            if comp_type in AWS_COMPONENTS:
                ComponentClass = AWS_COMPONENTS[comp_type]
                
                if cluster_name and cluster_name in clusters_dict:
                    with clusters_dict[cluster_name]:
                        if comp_count > 1:
                            # Create multiple instances
                            instances = [ComponentClass(f"{comp_name} {i+1}") for i in range(comp_count)]
                            components_dict[comp_name] = instances
                        else:
                            components_dict[comp_name] = ComponentClass(comp_name)
                else:
                    if comp_count > 1:
                        instances = [ComponentClass(f"{comp_name} {i+1}") for i in range(comp_count)]
                        components_dict[comp_name] = instances
                    else:
                        components_dict[comp_name] = ComponentClass(comp_name)
        
        # Create connections
        for connection in architecture.get("connections", []):
            from_comp = connection["from"]
            to_comp = connection["to"]
            label = connection.get("label", "")
            
            if from_comp in components_dict and to_comp in components_dict:
                from_node = components_dict[from_comp]
                to_node = components_dict[to_comp]
                
                # Handle lists (multiple instances)
                if isinstance(from_node, list):
                    from_node = from_node[0]
                if isinstance(to_node, list):
                    for node in to_node:
                        if label:
                            from_node >> Edge(label=label) >> node
                        else:
                            from_node >> node
                else:
                    if label:
                        from_node >> Edge(label=label) >> to_node
                    else:
                        from_node >> to_node

@app.post("/generate", response_model=ArchitectureResponse)
async def generate_architecture(request: ArchitectureRequest):
    """Generate an AWS architecture diagram from a description"""
    
    try:
        # Parse the architecture using LLM
        architecture = parse_architecture_with_llm(request.description)
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"diagrams/aws_architecture_{timestamp}"
        
        # Create diagrams directory if it doesn't exist
        os.makedirs("diagrams", exist_ok=True)
        
        # Generate the diagram
        generate_diagram(architecture, output_path)
        
        # Create explanation
        explanation = f"Generated AWS architecture with {len(architecture['components'])} components"
        if architecture.get('clusters'):
            explanation += f" organized in {len(architecture['clusters'])} tiers"
        
        return ArchitectureResponse(
            diagram_path=f"{output_path}.png",
            components=architecture,
            explanation=explanation
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/diagram/{filename}")
async def get_diagram(filename: str):
    """Serve the generated diagram"""
    file_path = f"diagrams/{filename}"
    if os.path.exists(file_path):
        return FileResponse(file_path, media_type="image/png")
    else:
        raise HTTPException(status_code=404, detail="Diagram not found")

@app.get("/")
async def root():
    return {"message": "AWS Architecture Diagram Generator API"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)