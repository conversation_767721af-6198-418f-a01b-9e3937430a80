{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "02fd8d2b-fee3-4e5c-9d88-a65b03a53c68", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 100, "strokeColor": "#000000", "backgroundColor": "#FF9900", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "9bdc6d79-fdb8-453d-8ab9-671280be21d8", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 110, "y": 185, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "🌊 Data Stream", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "🌊 Data Stream", "lineHeight": 1.25, "baseline": 16}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "8d9a08e0-47dc-4a0e-bf92-bb0c404a6acc", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 300, "y": 100, "strokeColor": "#000000", "backgroundColor": "#FF9900", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "0d74f813-e605-4d4e-bfc0-06a9713d73cf", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 310, "y": 185, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "λ Data Processor", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "λ Data Processor", "lineHeight": 1.25, "baseline": 16}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "167d0201-9317-45f4-bb65-8ca81552c2e1", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 500, "y": 100, "strokeColor": "#000000", "backgroundColor": "#569A31", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "1697c331-e509-4699-bdf4-4af08e6c1461", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 510, "y": 185, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "🪣 Data Lake", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "🪣 Data Lake", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "37641d37-981c-4d05-941a-7efd621d6e19", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 0, "y": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0, "height": 0, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": {"elementId": "02fd8d2b-fee3-4e5c-9d88-a65b03a53c68", "focus": 0, "gap": 1}, "endBinding": {"elementId": "8d9a08e0-47dc-4a0e-bf92-bb0c404a6acc", "focus": 0, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [100, 0]]}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "76f9f4d5-7c61-4637-ada2-e8b1bfdf8af8", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 50, "y": -10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 64, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "triggers", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "triggers", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "1d13c946-8317-4188-9640-99243060211d", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 0, "y": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0, "height": 0, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": {"elementId": "8d9a08e0-47dc-4a0e-bf92-bb0c404a6acc", "focus": 0, "gap": 1}, "endBinding": {"elementId": "167d0201-9317-45f4-bb65-8ca81552c2e1", "focus": 0, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [100, 0]]}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "14f7d5b9-f254-41cd-9562-a431bf8c47aa", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 50, "y": -10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 168, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "stores processed data", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "stores processed data", "lineHeight": 1.25, "baseline": 16}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}