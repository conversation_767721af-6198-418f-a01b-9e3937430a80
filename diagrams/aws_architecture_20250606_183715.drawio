<mxfile host="app.diagrams.net" modified="2025-06-06T18:37:15.975373" agent="AWS Architecture Generator" version="21.1.2" type="device"><diagram name="Click Ingestion Pipeline Architecture" id="generated-diagram"><mxGraphModel dx="0" dy="0" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0"><root><mxCell id="0" /><mxCell id="1" parent="0" /><mxCell id="2" value="Click Ingestion Layer" style="group;fillColor=#f5f5f5;strokeColor=#666666;rounded=1;" vertex="1" connectable="0" parent="1"><mxGeometry x="50" y="250" width="400" height="200" as="geometry" /></mxCell><mxCell id="3" value="Message Queue" style="group;fillColor=#f5f5f5;strokeColor=#666666;rounded=1;" vertex="1" connectable="0" parent="1"><mxGeometry x="50" y="450" width="200" height="200" as="geometry" /></mxCell><mxCell id="4" value="Processing Layer" style="group;fillColor=#f5f5f5;strokeColor=#666666;rounded=1;" vertex="1" connectable="0" parent="1"><mxGeometry x="50" y="650" width="400" height="200" as="geometry" /></mxCell><mxCell id="5" value="Data Storage" style="group;fillColor=#f5f5f5;strokeColor=#666666;rounded=1;" vertex="1" connectable="0" parent="1"><mxGeometry x="50" y="850" width="200" height="200" as="geometry" /></mxCell><mxCell id="6" value="Click Ingestion API" style="shape=mxgraph.aws4.api_gateway;fillColor=#FF9900;strokeColor=#000000;" vertex="1" parent="2"><mxGeometry x="50" y="50" width="78" height="78" as="geometry" /></mxCell><mxCell id="7" value="Click Handler" style="shape=mxgraph.aws4.lambda;fillColor=#FF9900;strokeColor=#000000;" vertex="1" parent="2"><mxGeometry x="250" y="50" width="78" height="78" as="geometry" /></mxCell><mxCell id="8" value="Kafka Queue" style="shape=mxgraph.aws4.kinesis;fillColor=#FF9900;strokeColor=#000000;" vertex="1" parent="3"><mxGeometry x="50" y="50" width="78" height="78" as="geometry" /></mxCell><mxCell id="9" value="Click Counter Consumer" style="shape=mxgraph.aws4.lambda;fillColor=#FF9900;strokeColor=#000000;" vertex="1" parent="4"><mxGeometry x="50" y="50" width="78" height="78" as="geometry" /></mxCell><mxCell id="10" value="Third Party Labeler Service" style="shape=mxgraph.aws4.ec2;fillColor=#FF9900;strokeColor=#000000;" vertex="1" parent="4"><mxGeometry x="250" y="50" width="78" height="78" as="geometry" /></mxCell><mxCell id="11" value="Click Data Store" style="shape=mxgraph.aws4.dynamodb;fillColor=#4B9EDA;strokeColor=#000000;" vertex="1" parent="5"><mxGeometry x="50" y="50" width="78" height="78" as="geometry" /></mxCell><mxCell id="12" value="click events" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="6" target="7"><mxGeometry relative="1" as="geometry" /></mxCell><mxCell id="13" value="publish clicks" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="7" target="8"><mxGeometry relative="1" as="geometry" /></mxCell><mxCell id="14" value="consume clicks" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="8" target="9"><mxGeometry relative="1" as="geometry" /></mxCell><mxCell id="15" value="label request" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="9" target="10"><mxGeometry relative="1" as="geometry" /></mxCell><mxCell id="16" value="labeled data" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="10" target="9"><mxGeometry relative="1" as="geometry" /></mxCell><mxCell id="17" value="store processed data" style="edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="9" target="11"><mxGeometry relative="1" as="geometry" /></mxCell></root></mxGraphModel></diagram></mxfile>