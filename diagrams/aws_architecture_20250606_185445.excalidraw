{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "a9304147-c505-4410-9f0c-cedf09894c63", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 200, "strokeColor": "#000000", "backgroundColor": "#FF9900", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "a39994db-02d7-49e8-b55f-4c0c578786a1", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 110, "y": 285, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "🌊 Data Stream", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "🌊 Data Stream", "lineHeight": 1.25, "baseline": 16}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "f6101c39-cc2f-49bf-b6e6-aa63bd09e6bd", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 290, "y": 200, "strokeColor": "#000000", "backgroundColor": "#FF9900", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "a0cae778-8a4b-4bd2-b12b-14199ed0e7e3", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 300, "y": 285, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "λ Data Processor", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "λ Data Processor", "lineHeight": 1.25, "baseline": 16}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "b072dd79-0ca3-4139-b51a-bf8e5cf7ef02", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 480, "y": 200, "strokeColor": "#000000", "backgroundColor": "#569A31", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "76492803-c8da-4843-8419-631fde94e837", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 490, "y": 285, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "🪣 Data Lake", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "🪣 Data Lake", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "08d2cc9c-abff-4cf1-9cb3-73dd3b4067ac", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 0, "y": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0, "height": 0, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": {"elementId": "a9304147-c505-4410-9f0c-cedf09894c63", "focus": 0, "gap": 1}, "endBinding": {"elementId": "f6101c39-cc2f-49bf-b6e6-aa63bd09e6bd", "focus": 0, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [100, 0]]}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "b161d70e-9176-42d2-87b4-d0e089c34041", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 50, "y": -10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 64, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "triggers", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "triggers", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "175d9b11-25fc-4cd3-ad4d-b180ad2f7314", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 0, "y": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0, "height": 0, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": {"elementId": "f6101c39-cc2f-49bf-b6e6-aa63bd09e6bd", "focus": 0, "gap": 1}, "endBinding": {"elementId": "b072dd79-0ca3-4139-b51a-bf8e5cf7ef02", "focus": 0, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [100, 0]]}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "555703c7-e9b6-43f0-a857-598fd7d6570a", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 50, "y": -10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 168, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "stores processed data", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "stores processed data", "lineHeight": 1.25, "baseline": 16}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}