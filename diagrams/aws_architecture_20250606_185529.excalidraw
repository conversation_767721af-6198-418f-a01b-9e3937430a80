{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "58e1c44a-7374-4120-833d-f477d6ccea83", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 100, "y": 200, "strokeColor": "#000000", "backgroundColor": "#FF9900", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "4ae1c15a-2a57-4045-a81c-2a0f4f183781", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 110, "y": 285, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "🌊 Data Stream", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "🌊 Data Stream", "lineHeight": 1.25, "baseline": 16}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "ea749e7f-5e20-4378-88b6-f813b25f8aa9", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 290, "y": 200, "strokeColor": "#000000", "backgroundColor": "#FF9900", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "7c6b3d9e-d75b-45cb-9d79-7c4685b710f6", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 300, "y": 285, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "λ Data Processor", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "λ Data Processor", "lineHeight": 1.25, "baseline": 16}, {"type": "rectangle", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "f19dd343-c244-41df-a033-c3bef3480288", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 480, "y": 200, "strokeColor": "#000000", "backgroundColor": "#569A31", "width": 120, "height": 80, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 3}, "boundElements": [], "updated": 1, "link": null, "locked": false}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "693766f2-c78e-415b-87ad-98c2e0f82129", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 490, "y": 285, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 120, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "🪣 Data Lake", "textAlign": "center", "verticalAlign": "top", "containerId": null, "originalText": "🪣 Data Lake", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "f81323ee-9122-4188-8a65-9d93339b0416", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 0, "y": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0, "height": 0, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": {"elementId": "58e1c44a-7374-4120-833d-f477d6ccea83", "focus": 0, "gap": 1}, "endBinding": {"elementId": "ea749e7f-5e20-4378-88b6-f813b25f8aa9", "focus": 0, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [100, 0]]}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "7c42457f-9107-4d68-8079-fbf92cb07a59", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 50, "y": -10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 64, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "triggers", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "triggers", "lineHeight": 1.25, "baseline": 16}, {"type": "arrow", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "6b4086f4-5197-42f1-91b1-3af25527e076", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 0, "y": 0, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 0, "height": 0, "seed": 1, "groupIds": [], "frameId": null, "roundness": {"type": 2}, "boundElements": [], "updated": 1, "link": null, "locked": false, "startBinding": {"elementId": "ea749e7f-5e20-4378-88b6-f813b25f8aa9", "focus": 0, "gap": 1}, "endBinding": {"elementId": "f19dd343-c244-41df-a033-c3bef3480288", "focus": 0, "gap": 1}, "lastCommittedPoint": null, "startArrowhead": null, "endArrowhead": "arrow", "points": [[0, 0], [100, 0]]}, {"type": "text", "version": 1, "versionNonce": 1, "isDeleted": false, "id": "2ece2434-5e18-45ca-a563-f4729fec5df9", "fillStyle": "solid", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 50, "y": -10, "strokeColor": "#1e1e1e", "backgroundColor": "#ffffff", "width": 168, "height": 20, "seed": 1, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1, "link": null, "locked": false, "fontSize": 12, "fontFamily": 1, "text": "stores processed data", "textAlign": "center", "verticalAlign": "middle", "containerId": null, "originalText": "stores processed data", "lineHeight": 1.25, "baseline": 16}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}