import { DiagramResponse, GenerateRequest } from '@/types';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export async function generateDiagram(request: GenerateRequest): Promise<DiagramResponse> {
  const response = await fetch(`${API_URL}/generate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}

export function getDiagramUrl(diagramPath: string): string {
  const filename = diagramPath.split('/').pop();
  return `${API_URL}/diagram/${filename}`;
}

export function getExcalidrawViewUrl(diagramPath: string): string {
  // For Excalidraw files, we can create a direct link to view in Excalidraw
  const filename = diagramPath.split('/').pop();
  if (filename?.endsWith('.excalidraw')) {
    return `https://excalidraw.com/`;
  }
  return getDiagramUrl(diagramPath);
}
