* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 900px;
  width: 100%;
  overflow: hidden;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  background: linear-gradient(135deg, #232F3E 0%, #131A22 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.aws-logo {
  width: 50px;
  height: 50px;
  background: #FF9900;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 20px;
}

.header p {
  opacity: 0.9;
  font-size: 1.1em;
}

.content {
  padding: 40px;
}

.input-section {
  margin-bottom: 30px;
}

label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #333;
  font-size: 1.1em;
}

textarea {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e4e8;
  border-radius: 10px;
  font-size: 16px;
  resize: vertical;
  min-height: 150px;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

textarea:focus {
  outline: none;
  border-color: #667eea;
}

.examples {
  margin-top: 10px;
  font-size: 0.9em;
  color: #666;
}

.examples strong {
  color: #333;
}

button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 15px 40px;
  font-size: 18px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: block;
  margin: 30px auto;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

.loading {
  text-align: center;
  margin: 20px 0;
}

.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.result {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.result h2 {
  margin-bottom: 20px;
  color: #333;
}

.diagram-container {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.diagram-container img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
}

.error {
  padding: 15px;
  background: #fee;
  border: 1px solid #fcc;
  border-radius: 10px;
  color: #c33;
  margin-top: 20px;
}

.components-info {
  margin-top: 20px;
  padding: 15px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.components-info h3 {
  margin-bottom: 10px;
  color: #333;
}

.component-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.component-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.9em;
}

/* Excalidraw-specific styles */
.excalidraw-preview {
  text-align: center;
  padding: 30px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15px;
  border: 2px dashed #667eea;
}

.excalidraw-info h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5em;
}

.excalidraw-info p {
  color: #666;
  margin-bottom: 20px;
}

.excalidraw-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 25px;
}

.download-btn, .open-btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.download-btn {
  background: #4CAF50;
  color: white;
}

.download-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.open-btn {
  background: #667eea;
  color: white;
}

.open-btn:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.instructions {
  background: white;
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.instructions h4 {
  color: #333;
  margin-bottom: 10px;
}

.instructions ol {
  color: #666;
  line-height: 1.6;
}

.instructions ol li {
  margin-bottom: 5px;
}

.instructions a {
  color: #667eea;
  text-decoration: none;
}

.instructions a:hover {
  text-decoration: underline;
}
