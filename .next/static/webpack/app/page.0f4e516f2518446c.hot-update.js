"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateDiagram: function() { return /* binding */ generateDiagram; },\n/* harmony export */   getDiagramUrl: function() { return /* binding */ getDiagramUrl; },\n/* harmony export */   getExcalidrawViewUrl: function() { return /* binding */ getExcalidrawViewUrl; }\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\nasync function generateDiagram(request) {\n    const response = await fetch(\"\".concat(API_URL, \"/generate\"), {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        throw new Error(\"HTTP error! status: \".concat(response.status));\n    }\n    return response.json();\n}\nfunction getDiagramUrl(diagramPath) {\n    const filename = diagramPath.split(\"/\").pop();\n    return \"\".concat(API_URL, \"/diagram/\").concat(filename);\n}\nfunction getExcalidrawViewUrl(diagramPath) {\n    // For Excalidraw files, we can create a direct link to view in Excalidraw\n    const filename = diagramPath.split(\"/\").pop();\n    if (filename === null || filename === void 0 ? void 0 : filename.endsWith(\".excalidraw\")) {\n        return \"https://excalidraw.com/\";\n    }\n    return getDiagramUrl(diagramPath);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ })

});