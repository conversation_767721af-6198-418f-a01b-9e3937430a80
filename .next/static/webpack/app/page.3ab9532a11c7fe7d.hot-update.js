"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ExcalidrawCanvas.tsx":
/*!*****************************************!*\
  !*** ./components/ExcalidrawCanvas.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExcalidrawCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Dynamically import Excalidraw with SSR disabled\nconst Excalidraw = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>(await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @excalidraw/excalidraw */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/index.js\"))).Excalidraw, {\n    loadableGenerated: {\n        modules: [\n            \"components/ExcalidrawCanvas.tsx -> \" + \"@excalidraw/excalidraw\"\n        ]\n    },\n    ssr: false\n});\n_c = Excalidraw;\nfunction ExcalidrawCanvas(param) {\n    let { diagramData, onDiagramChange } = param;\n    _s();\n    const [excalidrawAPI, setExcalidrawAPI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isUpdatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Update Excalidraw when diagram data changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExcalidrawCanvas.useEffect\": ()=>{\n            if (excalidrawAPI && diagramData) {\n                try {\n                    isUpdatingRef.current = true;\n                    // Update the scene with new diagram data\n                    excalidrawAPI.updateScene({\n                        elements: diagramData.elements || [],\n                        appState: {\n                            ...diagramData.appState,\n                            viewBackgroundColor: '#ffffff'\n                        }\n                    });\n                    // Reset the flag after a short delay to allow the update to complete\n                    setTimeout({\n                        \"ExcalidrawCanvas.useEffect\": ()=>{\n                            isUpdatingRef.current = false;\n                        }\n                    }[\"ExcalidrawCanvas.useEffect\"], 0);\n                } catch (error) {\n                    console.error('Error updating Excalidraw scene:', error);\n                    isUpdatingRef.current = false;\n                }\n            }\n        }\n    }[\"ExcalidrawCanvas.useEffect\"], [\n        diagramData,\n        excalidrawAPI\n    ]);\n    const handleChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ExcalidrawCanvas.useCallback[handleChange]\": (elements, appState)=>{\n            if (onDiagramChange && !isUpdatingRef.current) {\n                onDiagramChange(elements, appState);\n            }\n        }\n    }[\"ExcalidrawCanvas.useCallback[handleChange]\"], [\n        onDiagramChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"excalidraw-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83C\\uDFA8 Architecture Diagram\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"excalidraw-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (excalidrawAPI) {\n                                        excalidrawAPI.resetScene();\n                                    }\n                                },\n                                className: \"control-button\",\n                                title: \"Clear diagram\",\n                                children: \"\\uD83D\\uDDD1️ Clear\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (excalidrawAPI) {\n                                        const elements = excalidrawAPI.getSceneElements();\n                                        const appState = excalidrawAPI.getAppState();\n                                        const dataURL = excalidrawAPI.getSceneElementsIncludingDeleted();\n                                        // Create download link\n                                        const link = document.createElement('a');\n                                        link.download = \"architecture-\".concat(Date.now(), \".excalidraw\");\n                                        link.href = URL.createObjectURL(new Blob([\n                                            JSON.stringify({\n                                                elements,\n                                                appState\n                                            })\n                                        ], {\n                                            type: 'application/json'\n                                        }));\n                                        link.click();\n                                    }\n                                },\n                                className: \"control-button\",\n                                title: \"Download diagram\",\n                                children: \"\\uD83D\\uDCBE Download\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Excalidraw, {\n                    ref: (api)=>setExcalidrawAPI(api),\n                    onChange: handleChange,\n                    initialData: {\n                        elements: (diagramData === null || diagramData === void 0 ? void 0 : diagramData.elements) || [],\n                        appState: {\n                            viewBackgroundColor: '#ffffff',\n                            currentItemFontFamily: 1,\n                            currentItemFontSize: 16,\n                            ...diagramData === null || diagramData === void 0 ? void 0 : diagramData.appState\n                        }\n                    },\n                    UIOptions: {\n                        canvasActions: {\n                            loadScene: false,\n                            saveToActiveFile: false,\n                            export: {\n                                saveFileToDisk: true\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            !diagramData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-placeholder\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"placeholder-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"\\uD83C\\uDFAF Ready to Create\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Start a conversation to generate your first architecture diagram!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"placeholder-features\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI-generated diagrams\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDD04\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Iterative updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Manual editing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(ExcalidrawCanvas, \"Moen95DtlmL/L1/1hKKoJMWHm3w=\");\n_c1 = ExcalidrawCanvas;\nvar _c, _c1;\n$RefreshReg$(_c, \"Excalidraw\");\n$RefreshReg$(_c1, \"ExcalidrawCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ExcalidrawCanvas.tsx\n"));

/***/ })

});