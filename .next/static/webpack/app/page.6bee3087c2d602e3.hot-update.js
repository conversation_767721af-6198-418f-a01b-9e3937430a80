"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/DiagramGenerator.tsx":
/*!*****************************************!*\
  !*** ./components/DiagramGenerator.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DiagramGenerator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./components/LoadingSpinner.tsx\");\n/* harmony import */ var _ErrorMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ErrorMessage */ \"(app-pages-browser)/./components/ErrorMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DiagramGenerator() {\n    var _result_components_components, _result_components;\n    _s();\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSubmit = async ()=>{\n        if (!description.trim()) {\n            setError(\"Please enter an architecture description\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setResult(null);\n        try {\n            const data = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.generateDiagram)({\n                description: description.trim(),\n                style: \"standard\"\n            });\n            setResult(data);\n        } catch (err) {\n            setError(\"Error: \".concat(err instanceof Error ? err.message : \"Unknown error\", \". Make sure the backend server is running.\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && e.ctrlKey) {\n            handleSubmit();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"content\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"input-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"description\",\n                        children: \"Describe your AWS architecture:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        id: \"description\",\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: \"Example: I need a scalable web application with a load balancer, auto-scaling EC2 instances, RDS database, and S3 for static content...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"examples\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Try these examples:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginLeft: \"20px\",\n                                    marginTop: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A serverless API with Lambda, API Gateway, and DynamoDB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A three-tier web application with load balancer, EC2 instances, and RDS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A data processing pipeline with Kinesis, Lambda, and S3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A microservices architecture with ECS, ALB, and multiple databases\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubmit,\n                disabled: isLoading,\n                children: \"Generate Architecture Diagram\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 70,\n                columnNumber: 21\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorMessage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                message: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 71,\n                columnNumber: 17\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"result\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Generated Architecture\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"diagram-container\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getDiagramUrl)(result.diagram_path),\n                            alt: \"AWS Architecture Diagram\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"components-info\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Architecture Components\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: result.explanation\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"component-list\",\n                                children: (_result_components = result.components) === null || _result_components === void 0 ? void 0 : (_result_components_components = _result_components.components) === null || _result_components_components === void 0 ? void 0 : _result_components_components.map((comp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"component-tag\",\n                                        children: [\n                                            comp.name,\n                                            \" (\",\n                                            comp.type.toUpperCase(),\n                                            \")\"\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(DiagramGenerator, \"dIeWAYUbsQhjuwv0uwwMsdivb0o=\");\n_c = DiagramGenerator;\nvar _c;\n$RefreshReg$(_c, \"DiagramGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/DiagramGenerator.tsx\n"));

/***/ })

});