"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_browser-fs-access_dist_directory-open-4ed118d0_js"],{

/***/ "(app-pages-browser)/./node_modules/browser-fs-access/dist/directory-open-4ed118d0.js":
/*!************************************************************************!*\
  !*** ./node_modules/browser-fs-access/dist/directory-open-4ed118d0.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nfunction e(r){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+\" is not an object.\"));var r=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:r}})}return e=function(e){this.s=e,this.n=e.next},e.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var r=this.s.return;return void 0===r?Promise.resolve({value:e,done:!0}):t(r.apply(this.s,arguments))},throw:function(e){var r=this.s.return;return void 0===r?Promise.reject(e):t(r.apply(this.s,arguments))}},new e(r)}const r=async(t,n,i=t.name,a)=>{const o=[],l=[];var s,u=!1,c=!1;try{for(var y,f=function(r){var t,n,i,a=2;for(\"undefined\"!=typeof Symbol&&(n=Symbol.asyncIterator,i=Symbol.iterator);a--;){if(n&&null!=(t=r[n]))return t.call(r);if(i&&null!=(t=r[i]))return new e(t.call(r));n=\"@@asyncIterator\",i=\"@@iterator\"}throw new TypeError(\"Object is not async iterable\")}(t.values());u=!(y=await f.next()).done;u=!1){const e=y.value,s=`${i}/${e.name}`;\"file\"===e.kind?l.push(e.getFile().then(r=>(r.directoryHandle=t,r.handle=e,Object.defineProperty(r,\"webkitRelativePath\",{configurable:!0,enumerable:!0,get:()=>s})))):\"directory\"!==e.kind||!n||a&&a(e)||o.push(r(e,n,s,a))}}catch(e){c=!0,s=e}finally{try{u&&null!=f.return&&await f.return()}finally{if(c)throw s}}return[...(await Promise.all(o)).flat(),...await Promise.all(l)]};var t=async(e={})=>{e.recursive=e.recursive||!1;const t=await window.showDirectoryPicker({id:e.id,startIn:e.startIn});return r(t,e.recursive,void 0,e.skipDirectory)};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2RpcmVjdG9yeS1vcGVuLTRlZDExOGQwLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxjQUFjLGNBQWMsOEVBQThFLGFBQWEsaURBQWlELE9BQU8sZ0JBQWdCLEVBQUUscUJBQXFCLHVCQUF1QixjQUFjLDhCQUE4Qix5Q0FBeUMsb0JBQW9CLG9CQUFvQixtQ0FBbUMsZ0JBQWdCLCtCQUErQixtQkFBbUIsb0JBQW9CLGtFQUFrRSxVQUFVLGdDQUFnQyxnQkFBZ0IsZ0JBQWdCLElBQUksd0JBQXdCLGNBQWMsMkVBQTJFLElBQUksRUFBRSxzQ0FBc0MsNkNBQTZDLG1DQUFtQyxvREFBb0QsYUFBYSwyQkFBMkIsTUFBTSxxQkFBcUIsRUFBRSxHQUFHLE9BQU8sRUFBRSx5SEFBeUgsd0NBQXdDLDREQUE0RCxTQUFTLFNBQVMsUUFBUSxJQUFJLG9DQUFvQyxRQUFRLGNBQWMsa0VBQWtFLGdCQUFnQixJQUFJLDRCQUE0QiwwQ0FBMEMsMEJBQTBCLEVBQUUsZ0RBQXFFIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vbm9kZV9tb2R1bGVzL2Jyb3dzZXItZnMtYWNjZXNzL2Rpc3QvZGlyZWN0b3J5LW9wZW4tNGVkMTE4ZDAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZShyKXtmdW5jdGlvbiB0KGUpe2lmKE9iamVjdChlKSE9PWUpcmV0dXJuIFByb21pc2UucmVqZWN0KG5ldyBUeXBlRXJyb3IoZStcIiBpcyBub3QgYW4gb2JqZWN0LlwiKSk7dmFyIHI9ZS5kb25lO3JldHVybiBQcm9taXNlLnJlc29sdmUoZS52YWx1ZSkudGhlbihmdW5jdGlvbihlKXtyZXR1cm57dmFsdWU6ZSxkb25lOnJ9fSl9cmV0dXJuIGU9ZnVuY3Rpb24oZSl7dGhpcy5zPWUsdGhpcy5uPWUubmV4dH0sZS5wcm90b3R5cGU9e3M6bnVsbCxuOm51bGwsbmV4dDpmdW5jdGlvbigpe3JldHVybiB0KHRoaXMubi5hcHBseSh0aGlzLnMsYXJndW1lbnRzKSl9LHJldHVybjpmdW5jdGlvbihlKXt2YXIgcj10aGlzLnMucmV0dXJuO3JldHVybiB2b2lkIDA9PT1yP1Byb21pc2UucmVzb2x2ZSh7dmFsdWU6ZSxkb25lOiEwfSk6dChyLmFwcGx5KHRoaXMucyxhcmd1bWVudHMpKX0sdGhyb3c6ZnVuY3Rpb24oZSl7dmFyIHI9dGhpcy5zLnJldHVybjtyZXR1cm4gdm9pZCAwPT09cj9Qcm9taXNlLnJlamVjdChlKTp0KHIuYXBwbHkodGhpcy5zLGFyZ3VtZW50cykpfX0sbmV3IGUocil9Y29uc3Qgcj1hc3luYyh0LG4saT10Lm5hbWUsYSk9Pntjb25zdCBvPVtdLGw9W107dmFyIHMsdT0hMSxjPSExO3RyeXtmb3IodmFyIHksZj1mdW5jdGlvbihyKXt2YXIgdCxuLGksYT0yO2ZvcihcInVuZGVmaW5lZFwiIT10eXBlb2YgU3ltYm9sJiYobj1TeW1ib2wuYXN5bmNJdGVyYXRvcixpPVN5bWJvbC5pdGVyYXRvcik7YS0tOyl7aWYobiYmbnVsbCE9KHQ9cltuXSkpcmV0dXJuIHQuY2FsbChyKTtpZihpJiZudWxsIT0odD1yW2ldKSlyZXR1cm4gbmV3IGUodC5jYWxsKHIpKTtuPVwiQEBhc3luY0l0ZXJhdG9yXCIsaT1cIkBAaXRlcmF0b3JcIn10aHJvdyBuZXcgVHlwZUVycm9yKFwiT2JqZWN0IGlzIG5vdCBhc3luYyBpdGVyYWJsZVwiKX0odC52YWx1ZXMoKSk7dT0hKHk9YXdhaXQgZi5uZXh0KCkpLmRvbmU7dT0hMSl7Y29uc3QgZT15LnZhbHVlLHM9YCR7aX0vJHtlLm5hbWV9YDtcImZpbGVcIj09PWUua2luZD9sLnB1c2goZS5nZXRGaWxlKCkudGhlbihyPT4oci5kaXJlY3RvcnlIYW5kbGU9dCxyLmhhbmRsZT1lLE9iamVjdC5kZWZpbmVQcm9wZXJ0eShyLFwid2Via2l0UmVsYXRpdmVQYXRoXCIse2NvbmZpZ3VyYWJsZTohMCxlbnVtZXJhYmxlOiEwLGdldDooKT0+c30pKSkpOlwiZGlyZWN0b3J5XCIhPT1lLmtpbmR8fCFufHxhJiZhKGUpfHxvLnB1c2gocihlLG4scyxhKSl9fWNhdGNoKGUpe2M9ITAscz1lfWZpbmFsbHl7dHJ5e3UmJm51bGwhPWYucmV0dXJuJiZhd2FpdCBmLnJldHVybigpfWZpbmFsbHl7aWYoYyl0aHJvdyBzfX1yZXR1cm5bLi4uKGF3YWl0IFByb21pc2UuYWxsKG8pKS5mbGF0KCksLi4uYXdhaXQgUHJvbWlzZS5hbGwobCldfTt2YXIgdD1hc3luYyhlPXt9KT0+e2UucmVjdXJzaXZlPWUucmVjdXJzaXZlfHwhMTtjb25zdCB0PWF3YWl0IHdpbmRvdy5zaG93RGlyZWN0b3J5UGlja2VyKHtpZDplLmlkLHN0YXJ0SW46ZS5zdGFydElufSk7cmV0dXJuIHIodCxlLnJlY3Vyc2l2ZSx2b2lkIDAsZS5za2lwRGlyZWN0b3J5KX07ZXhwb3J0e3QgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/browser-fs-access/dist/directory-open-4ed118d0.js\n"));

/***/ })

}]);