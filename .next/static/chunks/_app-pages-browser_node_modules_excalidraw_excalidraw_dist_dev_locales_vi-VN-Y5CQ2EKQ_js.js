"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_vi-VN-Y5CQ2EKQ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/vi-VN-Y5CQ2EKQ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/vi-VN-Y5CQ2EKQ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ vi_VN_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/vi-VN.json\nvar labels = {\n  paste: \"D\\xE1n\",\n  pasteAsPlaintext: \"D\\xE1n ki\\u1EC3u v\\u0103n b\\u1EA3n thu\\u1EA7n\",\n  pasteCharts: \"D\\xE1n bi\\u1EC3u \\u0111\\u1ED3\",\n  selectAll: \"Ch\\u1ECDn t\\u1EA5t c\\u1EA3\",\n  multiSelect: \"Th\\xEAm m\\u1EDBi v\\xE0o Select\",\n  moveCanvas: \"Di chuy\\u1EC3n canvas\",\n  cut: \"C\\u1EAFt\",\n  copy: \"Sao ch\\xE9p\",\n  copyAsPng: \"Sao ch\\xE9p v\\xE0o b\\u1ED9 nh\\u1EDB t\\u1EA1m d\\u01B0\\u1EDBi d\\u1EA1ng PNG\",\n  copyAsSvg: \"Sao ch\\xE9p v\\xE0o b\\u1ED9 nh\\u1EDB t\\u1EA1m d\\u01B0\\u1EDBi d\\u1EA1ng SVG\",\n  copyText: \"Sao ch\\xE9p v\\xE0o b\\u1ED9 nh\\u1EDB t\\u1EA1m d\\u01B0\\u1EDBi d\\u1EA1ng ch\\u1EEF\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"\\u0110\\u01B0a ra tr\\u01B0\\u1EDBc\",\n  sendToBack: \"H\\u1EA1 xu\\u1ED1ng d\\u01B0\\u1EDBi\",\n  bringToFront: \"\\u0110\\u01B0a ra \\u0111\\u1EA7u ti\\xEAn\",\n  sendBackward: \"H\\u1EA1 xu\\u1ED1ng cu\\u1ED1i\",\n  delete: \"X\\xF3a\",\n  copyStyles: \"Sao ch\\xE9p \\u0111\\u1ECBnh d\\u1EA1ng\",\n  pasteStyles: \"D\\xE1n \\u0111\\u1ECBnh d\\u1EA1ng\",\n  stroke: \"N\\xE9t\",\n  background: \"N\\u1EC1n\",\n  fill: \"Fill\",\n  strokeWidth: \"\\u0110\\u1ED9 d\\xE0y n\\xE9t\",\n  strokeStyle: \"Ki\\u1EC3u n\\xE9t\",\n  strokeStyle_solid: \"Kh\\u1ED1i\",\n  strokeStyle_dashed: \"G\\u1EA1ch ngang\",\n  strokeStyle_dotted: \"Nhi\\u1EC1u ch\\u1EA5m\",\n  sloppiness: \"Hoa v\\u0103n n\\xE9t\",\n  opacity: \"\\u0110\\u1ED9 trong su\\u1ED1t\",\n  textAlign: \"C\\u0103n ch\\u1EC9nh v\\u0103n b\\u1EA3n\",\n  edges: \"C\\u1EA1nh\",\n  sharp: \"Nh\\u1ECDn\",\n  round: \"Tr\\xF2n\",\n  arrowheads: \"\\u0110\\u1EA7u m\\u0169i t\\xEAn\",\n  arrowhead_none: \"Kh\\xF4ng\",\n  arrowhead_arrow: \"M\\u0169i t\\xEAn\",\n  arrowhead_bar: \"Thanh\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Tam gi\\xE1c\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"C\\u1EE1 ch\\u1EEF\",\n  fontFamily: \"Ph\\xF4ng ch\\u1EEF\",\n  addWatermark: 'L\\xE0m v\\u1EDBi Excalidraw\"',\n  handDrawn: \"V\\u1EBD tay\",\n  normal: \"B\\xECnh th\\u01B0\\u1EDDng\",\n  code: \"M\\xE3\",\n  small: \"Nh\\u1ECF\",\n  medium: \"V\\u1EEBa\",\n  large: \"L\\u1EDBn\",\n  veryLarge: \"R\\u1EA5t l\\u1EDBn\",\n  solid: \"\\u0110\\u1EB7c\",\n  hachure: \"N\\xE9t g\\u1EA1ch g\\u1EA1ch\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"N\\xE9t g\\u1EA1ch ch\\xE9o\",\n  thin: \"M\\u1ECFng\",\n  bold: \"In \\u0111\\u1EADm\",\n  left: \"Tr\\xE1i\",\n  center: \"Gi\\u1EEFa\",\n  right: \"Ph\\u1EA3i\",\n  extraBold: \"N\\xE9t si\\xEAu \\u0111\\u1EADm\",\n  architect: \"Ki\\u1EBFn tr\\xFAc s\\u01B0\",\n  artist: \"Ngh\\u1EC7 sy\\u0303\",\n  cartoonist: \"Ho\\u1EA1t h\\xECnh\",\n  fileTitle: \"T\\xEAn t\\u1EADp tin\",\n  colorPicker: \"Ch\\u1ECDn m\\xE0u\",\n  canvasColors: \"\\u0110\\xE3 d\\xF9ng tr\\xEAn canvas\",\n  canvasBackground: \"N\\u1EC1n canvas\",\n  drawingCanvas: \"Canvas v\\u1EBD\",\n  layers: \"L\\u1EDBp\",\n  actions: \"Ch\\u1EE9c n\\u0103ng\",\n  language: \"Ng\\xF4n ng\\u1EEF\",\n  liveCollaboration: \"H\\u1EE3p t\\xE1c tr\\u1EF1c ti\\u1EBFp...\",\n  duplicateSelection: \"T\\u1EA1o b\\u1EA3n sao\",\n  untitled: \"Kh\\xF4ng c\\xF3 ti\\xEAu \\u0111\\u1EC1\",\n  name: \"T\\xEAn\",\n  yourName: \"T\\xEAn c\\u1EE7a b\\u1EA1n\",\n  madeWithExcalidraw: \"L\\xE0m v\\u1EDBi Excalidraw\",\n  group: \"G\\u1ED9p nh\\xF3m l\\u1EA1i l\\u1EF1a ch\\u1ECDn\",\n  ungroup: \"T\\xE1ch nh\\xF3m l\\u1EF1a ch\\u1ECDn\",\n  collaborators: \"C\\u1ED9ng t\\xE1c vi\\xEAn\",\n  showGrid: \"Hi\\u1EC3n th\\u1ECB l\\u01B0\\u1EDBi\",\n  addToLibrary: \"Th\\xEAm v\\xE0o th\\u01B0 vi\\u1EC7n\",\n  removeFromLibrary: \"X\\xF3a kh\\u1ECFi th\\u01B0 vi\\u1EC7n\",\n  libraryLoadingMessage: \"\\u0110ang t\\u1EA3i th\\u01B0 vi\\u1EC7n\\u2026\",\n  libraries: \"Xem th\\u01B0 vi\\u1EC7n\",\n  loadingScene: \"\\u0110ang t\\u1EA3i v\\u1EC1\\u2026\",\n  align: \"C\\u0103n ch\\u1EC9nh\",\n  alignTop: \"C\\u0103n tr\\xEAn\",\n  alignBottom: \"C\\u0103n d\\u01B0\\u1EDBi\",\n  alignLeft: \"Canh tr\\xE1i\",\n  alignRight: \"Canh ph\\u1EA3i\",\n  centerVertically: \"Gi\\u01B0\\u0303a theo chi\\u1EC1u d\\u1ECDc\",\n  centerHorizontally: \"Gi\\u01B0\\u0303a theo chi\\u1EC1u ngang\",\n  distributeHorizontally: \"Ph\\xE2n b\\xF4\\u0301 theo chi\\xEA\\u0300u ngang\",\n  distributeVertically: \"Ph\\xE2n b\\xF4\\u0301 theo chi\\xEA\\u0300u do\\u0323c\",\n  flipHorizontal: \"L\\u1EADt ngang\",\n  flipVertical: \"L\\u1EADt d\\u1ECDc\",\n  viewMode: \"Ch\\u1EBF \\u0111\\u1ED9 xem\",\n  share: \"Chia s\\u1EBB\",\n  showStroke: \"Hi\\u1EC3n th\\u1ECB ch\\u1ECDn m\\xE0u\",\n  showBackground: \"Hi\\u1EC7n th\\u1ECB ch\\u1ECDn m\\xE0u n\\u1EC1n\",\n  toggleTheme: \"\",\n  personalLib: \"Th\\u01B0 vi\\u1EC7n c\\xE1 nh\\xE2n\",\n  excalidrawLib: \"Th\\u01B0 vi\\u1EC7n Excalidraw\",\n  decreaseFontSize: \"Gi\\u1EA3m c\\u1EE1 ch\\u1EEF\",\n  increaseFontSize: \"T\\u0103ng c\\u1EE1 ch\\u1EEF\",\n  unbindText: \"\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"S\\u1EEDa li\\xEAn k\\u1EBFt\",\n    editEmbed: \"\",\n    create: \"T\\u1EA1o li\\xEAn k\\u1EBFt\",\n    createEmbed: \"\",\n    label: \"Li\\xEAn k\\u1EBFt\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\\u0110i\\u1EC1u ch\\u1EC9nh n\\xE9t\",\n    exit: \"Tho\\xE1t ch\\u1EC9nh n\\xE9t\"\n  },\n  elementLock: {\n    lock: \"Kho\\xE1\",\n    unlock: \"M\\u1EDF kho\\xE1\",\n    lockAll: \"Kh\\xF3a t\\u1EA5t c\\u1EA3\",\n    unlockAll: \"M\\u1EDF kh\\xF3a t\\u1EA5t c\\u1EA3\"\n  },\n  statusPublished: \"\\u0110\\xE3 \\u0111\\u0103ng t\\u1EA3i\",\n  sidebarLock: \"Gi\\u1EEF thanh b\\xEAn lu\\xF4n m\\u1EDF\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Ch\\u01B0a c\\xF3 m\\xF3n n\\xE0o...\",\n  hint_emptyLibrary: \"Ch\\u1ECDn m\\u1ED9t m\\xF3n tr\\xEAn canvas \\u0111\\u1EC3 th\\xEAm n\\xF3 v\\xE0o \\u0111\\xE2y, ho\\u1EB7c c\\xE0i \\u0111\\u1EB7t th\\u01B0 vi\\u1EC7n t\\u1EEB kho l\\u01B0u tr\\u1EEF c\\xF4ng c\\u1ED9ng, \\u1EDF b\\xEAn d\\u01B0\\u1EDBi.\",\n  hint_emptyPrivateLibrary: \"Ch\\u1ECDn m\\u1ED9t m\\xF3n tr\\xEAn canvas \\u0111\\u1EC3 th\\xEAm n\\xF3 v\\xE0o \\u0111\\xE2y.\"\n};\nvar buttons = {\n  clearReset: \"Reset canvas\",\n  exportJSON: \"Xu\\u1EA5t ra t\\u1EADp tin\",\n  exportImage: \"Xu\\u1EA5t file \\u1EA3nh...\",\n  export: \"L\\u01B0u v\\xE0o...\",\n  copyToClipboard: \"Sao ch\\xE9p v\\xE0o b\\u1ED9 nh\\u1EDB t\\u1EA1m\",\n  save: \"L\\u01B0u v\\xE0o t\\u1EADp tin hi\\u1EC7n t\\u1EA1i\",\n  saveAs: \"L\\u01B0u th\\xE0nh\",\n  load: \"M\\u1EDF\",\n  getShareableLink: \"T\\u1EA1o li\\xEAn k\\u1EBFt \\u0111\\u1EC3 chia s\\u1EBB\",\n  close: \"\\u0110\\xF3ng\",\n  selectLanguage: \"Ch\\u1ECDn ng\\xF4n ng\\u1EEF\",\n  scrollBackToContent: \"Cu\\u1ED9n v\\u1EC1 n\\u1ED9i dung ch\\xEDnh\",\n  zoomIn: \"Ph\\xF3ng to\",\n  zoomOut: \"Thu nh\\u1ECF\",\n  resetZoom: \"\\u0110\\u1EB7t l\\u1EA1i thu ph\\xF3ng\",\n  menu: \"B\\u1EA3ng ch\\u1ECDn\",\n  done: \"Xong\",\n  edit: \"Ch\\u1EC9nh s\\u1EEDa\",\n  undo: \"Ho\\xE0n t\\xE1c\",\n  redo: \"L\\xE0m l\\u1EA1i\",\n  resetLibrary: \"\",\n  createNewRoom: \"T\\u1EA1o ph\\xF2ng m\\u1EDBi\",\n  fullScreen: \"To\\xE0n m\\xE0n h\\xECnh\",\n  darkMode: \"Ch\\u1EBF \\u0111\\u1ED9 t\\u1ED1i\",\n  lightMode: \"Ch\\u1EBF \\u0111\\u1ED9 s\\xE1ng\",\n  zenMode: \"Ch\\u1EBF \\u0111\\u1ED9 zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Tho\\xE1t ch\\u1EC3 \\u0111\\u1ED9 zen\",\n  cancel: \"Hu\\u0309y\",\n  clear: \"L\\xE0m s\\u1EA1ch\",\n  remove: \"X\\xF3a\",\n  embed: \"\",\n  publishLibrary: \"\\u0110\\u0103ng t\\u1EA3i\",\n  submit: \"G\\u1EEDi\",\n  confirm: \"X\\xE1c nh\\u1EADn\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"\\u0110i\\u1EC1u n\\xE0y s\\u1EBD d\\u1ECDn h\\u1EBFt canvas. B\\u1EA1n c\\xF3 ch\\u1EAFc kh\\xF4ng?\",\n  couldNotCreateShareableLink: \"Kh\\xF4ng th\\u1EC3 t\\u1EA1o \\u0111\\u01B0\\u1EDDng d\\u1EABn chia s\\u1EBB.\",\n  couldNotCreateShareableLinkTooBig: \"Kh\\xF4ng th\\u1EC3 t\\u1EA1o \\u0111\\u01B0\\u1EDDng d\\u1EABn chia s\\u1EBB: b\\u1EA3n v\\u1EBD qu\\xE1 l\\u1EDBn\",\n  couldNotLoadInvalidFile: \"Kh\\xF4ng th\\u1EC3 load t\\u1EADp tin kh\\xF4ng h\\u1EE3p l\\u1EC7\",\n  importBackendFailed: \"\",\n  cannotExportEmptyCanvas: \"Kh\\xF4ng th\\u1EC3 xu\\u1EA5t canvas tr\\u1ED1ng.\",\n  couldNotCopyToClipboard: \"\",\n  decryptFailed: \"Kh\\xF4ng th\\u1EC3 gi\\u1EA3i m\\xE3 d\\u1EEF li\\u1EC7u.\",\n  uploadedSecurly: \"\",\n  loadSceneOverridePrompt: \"\",\n  collabStopOverridePrompt: \"D\\u1EEBng phi\\xEAn s\\u1EBD ghi \\u0111\\xE8 l\\xEAn b\\u1EA3n v\\u1EBD \\u0111\\u01B0\\u1EE3c l\\u01B0u tr\\u1EEF c\\u1EE5c b\\u1ED9 tr\\u01B0\\u1EDBc \\u0111\\xF3 c\\u1EE7a b\\u1EA1n. B\\u1EA1n c\\xF3 ch\\u1EAFc kh\\xF4ng?\\n\\n(N\\u1EBFu b\\u1EA1n mu\\u1ED1n gi\\u1EEF b\\u1EA3n v\\u1EBD c\\u1EE5c b\\u1ED9 c\\u1EE7a m\\xECnh, ch\\u1EC9 c\\u1EA7n \\u0111\\xF3ng tab tr\\xECnh duy\\u1EC7t.)\",\n  errorAddingToLibrary: \"Kh\\xF4ng th\\u1EC3 th\\xEAm m\\xF3n v\\xE0o th\\u01B0 vi\\u1EC7n\",\n  errorRemovingFromLibrary: \"Kh\\xF4ng th\\u1EC3 xo\\xE1 m\\xF3n kh\\u1ECFi th\\u01B0 vi\\u1EC7n\",\n  confirmAddLibrary: \"H\\xECnh {{numShapes}} s\\u1EBD \\u0111\\u01B0\\u1EE3c th\\xEAm v\\xE0o th\\u01B0 vi\\u1EC7n. B\\u1EA1n ch\\u1EAFc ch\\u1EE9?\",\n  imageDoesNotContainScene: \"H\\xECnh \\u1EA3nh n\\xE0y d\\u01B0\\u1EDDng nh\\u01B0 kh\\xF4ng ch\\u1EE9a b\\u1EA5t k\\u1EF3 d\\u1EEF li\\u1EC7u c\\u1EA3nh n\\xE0o. B\\u1EA1n \\u0111\\xE3 b\\u1EADt t\\xEDnh n\\u0103ng nh\\xFAng c\\u1EA3nh khi xu\\u1EA5t ch\\u01B0a?\",\n  cannotRestoreFromImage: \"\",\n  invalidSceneUrl: \"\",\n  resetLibrary: \"\",\n  removeItemsFromsLibrary: \"Xo\\xE1 {{count}} m\\xF3n t\\u1EEB th\\u01B0 vi\\u1EC7n?\",\n  invalidEncryptionKey: \"Kh\\xF3a m\\xE3 h\\xF3a ph\\u1EA3i c\\xF3 22 k\\xFD t\\u1EF1. H\\u1EE3p t\\xE1c tr\\u1EF1c ti\\u1EBFp b\\u1ECB v\\xF4 hi\\u1EC7u h\\xF3a.\",\n  collabOfflineWarning: \"Kh\\xF4ng c\\xF3 k\\u1EBFt n\\u1ED1i internet.\\nThay \\u0111\\u1ED5i c\\u1EE7a b\\u1EA1n s\\u1EBD kh\\xF4ng \\u0111\\u01B0\\u1EE3c l\\u01B0u!\"\n};\nvar errors = {\n  unsupportedFileType: \"Lo\\u1EA1i t\\u1EADp tin kh\\xF4ng \\u0111\\u01B0\\u1EE3c h\\u1ED7 tr\\u1EE3.\",\n  imageInsertError: \"Kh\\xF4ng th\\u1EC3 th\\xEAm \\u1EA3nh. H\\xE3y th\\u1EED l\\u1EA1i sau...\",\n  fileTooBig: \"T\\u1EC7p tin qu\\xE1 l\\u1EDBn. Dung l\\u01B0\\u1EE3ng t\\u1ED1i \\u0111a cho ph\\xE9p l\\xE0 {{maxSize}}.\",\n  svgImageInsertError: \"Kh\\xF4ng th\\u1EC3 th\\xEAm \\u1EA3nh SVG. M\\xE3 SVG c\\xF3 v\\u1EBB sai.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG kh\\xF4ng h\\u1EE3p l\\u1EC7.\",\n  cannotResolveCollabServer: \"Kh\\xF4ng th\\u1EC3 k\\u1EBFt n\\u1ED1i v\\u1EDBi m\\xE1y ch\\u1EE7 h\\u1EE3p t\\xE1c. H\\xE3y t\\u1EA3i l\\u1EA1i trang v\\xE0 th\\u1EED l\\u1EA1i.\",\n  importLibraryError: \"Kh\\xF4ng th\\u1EC3 t\\u1EA3i th\\u01B0 vi\\u1EC7n\",\n  collabSaveFailed: \"Kh\\xF4ng th\\u1EC3 l\\u01B0u v\\xE0o c\\u01A1 s\\u1EDF d\\u1EEF li\\u1EC7u. N\\u1EBFu v\\u1EA5n \\u0111\\u1EC1 ti\\u1EBFp t\\u1EE5c x\\u1EA3y ra, b\\u1EA1n n\\xEAn l\\u01B0u t\\u1EC7p v\\xE0o m\\xE1y \\u0111\\u1EC3 \\u0111\\u1EA3m b\\u1EA3o b\\u1EA1n kh\\xF4ng b\\u1ECB m\\u1EA5t c\\xF4ng vi\\u1EC7c.\",\n  collabSaveFailed_sizeExceeded: \"Kh\\xF4ng th\\u1EC3 l\\u01B0u v\\xE0o c\\u01A1 s\\u1EDF d\\u1EEF li\\u1EC7u, canvas c\\xF3 v\\u1EBB qu\\xE1 l\\u1EDBn. B\\u1EA1n n\\xEAn l\\u01B0u t\\u1EC7p c\\u1EE5c b\\u1ED9 \\u0111\\u1EC3 \\u0111\\u1EA3m b\\u1EA3o b\\u1EA1n kh\\xF4ng b\\u1ECB m\\u1EA5t c\\xF4ng vi\\u1EC7c.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"L\\u1EF1a ch\\u1ECDn\",\n  image: \"Ch\\xE8n \\u1EA3nh\",\n  rectangle: \"H\\xECnh ch\\u1EEF nh\\u1EADt\",\n  diamond: \"Kim c\\u01B0\\u01A1ng\",\n  ellipse: \"H\\xECnh el\\xEDp\",\n  arrow: \"M\\u0169i t\\xEAn\",\n  line: \"\\u0110\\u01B0\\u1EDDng k\\u1EBB\",\n  freedraw: \"V\\u1EBD\",\n  text: \"V\\u0103n b\\u1EA3n\",\n  library: \"Th\\u01B0 vi\\u1EC7n\",\n  lock: \"Gi\\u1EEF d\\u1EE5ng c\\u0169 hi\\u1EC7n t\\u1EA1i sau khi v\\u1EBD\",\n  penMode: \"Ch\\u1EBF \\u0111\\u1ED9 b\\xFAt v\\u1EBD - ng\\u0103n ng\\u1EEBa ch\\u1EA1m nh\\u1EA7m\",\n  link: \"Th\\xEAm/ Ch\\u1EC9nh s\\u1EEDa li\\xEAn k\\u1EBFt cho h\\xECnh \\u0111\\u01B0\\u1EE3c ch\\u1ECDn\",\n  eraser: \"X\\xF3a\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"Tay k\\xE9o\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"H\\xE0nh \\u0111\\u1ED9ng canvas\",\n  selectedShapeActions: \"C\\xE1c h\\xE0nh \\u0111\\u1ED9ng cho h\\xECnh d\\u1EA1ng \\u0111\\xE3 ch\\u1ECDn\",\n  shapes: \"C\\xE1c h\\xECnh kh\\u1ED1i\"\n};\nvar hints = {\n  canvasPanning: \"\\u0110\\u1EC3 di chuy\\u1EC3n canvas, gi\\u1EEF con l\\u0103n chu\\u1ED9t ho\\u1EB7c ph\\xEDm c\\xE1ch trong khi k\\xE9o, ho\\u1EB7c s\\u1EED d\\u1EE5ng c\\xF4ng c\\u1EE5 c\\u1EA7m tay\",\n  linearElement: \"\\u1EA4n \\u0111\\u1EC3 b\\u1EAFt \\u0111\\u1EA7u nhi\\u1EC3m \\u0111i\\u1EC3m v\\u1EBD, k\\xE9o \\u0111\\u1EC3 v\\u1EBD m\\u1ED9t \\u0111\\u01B0\\u1EDDng th\\u1EB3ng\",\n  freeDraw: \"\\u1EA4n b\\xE0 k\\xE9o, th\\u1EA3 khi b\\u1EA1n xong\",\n  text: \"M\\u1EB9o: b\\u1EA1n c\\xF3 th\\u1EC3 th\\xEAm v\\u0103n b\\u1EA3n t\\u1EA1i b\\u1EA5t c\\u1EE9 \\u0111\\xE2u b\\u1EB1ng c\\xE1ch \\u1EA5n hai l\\u1EA7n b\\u1EB1ng tool l\\u1EF1a ch\\u1ECDn\",\n  embeddable: \"\",\n  text_selected: \"\\u1EA4n 2 l\\u1EA7n ho\\u1EB7c nh\\u1EA5n ENTER \\u0111\\u1EC3 ch\\u1EC9nh v\\u0103n b\\u1EA3n\",\n  text_editing: \"Nh\\u1EA5n Escape ho\\u1EB7c Ctrl/Cmd+ENTER \\u0111\\u1EC3 ho\\xE0n th\\xE0nh ch\\u1EC9nh s\\u1EEDa\",\n  linearElementMulti: \"Nh\\u1EA5n v\\xE0o \\u0111i\\u1EC3m cu\\u1ED1i ho\\u1EB7c nh\\u1EA5n Escape ho\\u1EB7c Enter \\u0111\\u1EC3 k\\u1EBFt th\\xFAc\",\n  lockAngle: \"B\\u1EA1n c\\xF3 th\\u1EC3 ch\\u1EC9nh l\\u1EA1i g\\xF3c b\\u1EB1ng c\\xE1ch gi\\u1EEF ph\\xEDm SHIFT\",\n  resize: \"B\\u1EA1n c\\xF3 th\\u1EC3 ch\\u1EC9nh t\\u1EF7 l\\u1EC7 b\\u1EB1ng c\\xE1ch gi\\u1EEF SHIFT khi ch\\u1EC9nh k\\xEDch c\\u1EE1,\\ngi\\u1EEF ALT \\u0111\\u1EC3 ch\\u1EC9nh k\\xEDch c\\u1EE1 t\\u1EEB trung t\\xE2m\",\n  resizeImage: \"\",\n  rotate: \"\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: 'T\\xEDnh n\\u0103ng n\\xE0y c\\xF3 th\\u1EC3 \\u0111\\u01B0\\u1EE3c b\\u1EADt b\\u1EB1ng c\\xE1ch \\u0111\\u1EB7t c\\u1EDD \"dom.events.asyncClipboard.clipboardItem\" th\\xE0nh \"true\". \\u0110\\u1EC3 thay \\u0111\\u1ED5i c\\u1EDD tr\\xECnh duy\\u1EC7t trong Firefox, h\\xE3y truy c\\u1EADp trang \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Kh\\xF4ng th\\u1EC3 xem tr\\u01B0\\u1EDBc\",\n  canvasTooBig: \"Canvas n\\xE0y c\\xF3 th\\u1EC3 h\\u01A1i l\\u1EDBn.\",\n  canvasTooBigTip: \"M\\u1EB9o: h\\xE3y th\\u1EED di chuy\\u1EC3n c\\xE1c elements nh\\u1EA5t l\\u1EA1i g\\u1EA7n nhau h\\u01A1n m\\u1ED9t ch\\xFAt.\"\n};\nvar errorSplash = {\n  headingMain: \"\",\n  clearCanvasMessage: \"N\\u1EBFu kh\\xF4ng t\\u1EA3i l\\u1EA1i \\u0111\\u01B0\\u1EE3c, h\\xE3y th\\u1EED <button>d\\u1ECDn canvas.</button>\",\n  clearCanvasCaveat: \" \\u0110i\\u1EC1u n\\xE0y s\\u1EBD d\\u1EABn \\u0111\\u1EBFn m\\u1EA5t d\\u1EEF li\\u1EC7u b\\u1EA1n \\u0111\\xE3 l\\xE0m \",\n  trackedToSentry: \"\",\n  openIssueMessage: \"\",\n  sceneContent: \"\"\n};\nvar roomDialog = {\n  desc_intro: \"\",\n  desc_privacy: \"\",\n  button_startSession: \"\",\n  button_stopSession: \"\",\n  desc_inProgressIntro: \"\",\n  desc_shareLink: \"\",\n  desc_exitSession: \"\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"\"\n};\nvar exportDialog = {\n  disk_title: \"\",\n  disk_details: \"\",\n  disk_button: \"\",\n  link_title: \"\",\n  link_details: \"\",\n  link_button: \"\",\n  excalidrawplus_description: \"\",\n  excalidrawplus_button: \"\",\n  excalidrawplus_exportError: \"\"\n};\nvar helpDialog = {\n  blog: \"\",\n  click: \"\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"\",\n  curvedLine: \"\",\n  documentation: \"\",\n  doubleClick: \"\",\n  drag: \"\",\n  editor: \"\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"\",\n  howto: \"\",\n  or: \"\",\n  preventBinding: \"\",\n  tools: \"\",\n  shortcuts: \"\",\n  textFinish: \"\",\n  textNewLine: \"\",\n  title: \"\",\n  view: \"\",\n  zoomToFit: \"\",\n  zoomToSelection: \"\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\"\n};\nvar clearCanvasDialog = {\n  title: \"D\\u1ECDn canvas\"\n};\nvar publishDialog = {\n  title: \"\",\n  itemName: \"T\\xEAn m\\xF3n\",\n  authorName: \"\",\n  githubUsername: \"\",\n  twitterUsername: \"\",\n  libraryName: \"\",\n  libraryDesc: \"\",\n  website: \"\",\n  placeholder: {\n    authorName: \"\",\n    libraryName: \"\",\n    libraryDesc: \"\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"\",\n    website: \"\"\n  },\n  noteDescription: \"\",\n  noteGuidelines: \"\",\n  noteLicense: \"\",\n  noteItems: \"T\\u1EEBng m\\xF3n trong th\\u01B0 vi\\u1EC7n ph\\u1EA3i c\\xF3 t\\xEAn ri\\xEAng \\u0111\\u1EC3 c\\xF3 th\\u1EC3 l\\u1ECDc. C\\xE1c m\\xF3n th\\u01B0 vi\\u1EC7n sau \\u0111\\xE2y s\\u1EBD th\\xEAm:\",\n  atleastOneLibItem: \"Vui l\\xF2ng ch\\u1ECDn \\xEDt nh\\u1EA5t m\\u1ED9t m\\xF3n th\\u01B0 vi\\u1EC7n \\u0111\\u1EC3 b\\u1EAFt \\u0111\\u1EA7u\",\n  republishWarning: \"L\\u01B0u \\xFD: m\\u1ED9t s\\u1ED1 m\\xF3n \\u0111\\xE3 ch\\u1ECDn \\u0111\\u01B0\\u1EE3c \\u0111\\xE1nh d\\u1EA5u l\\xE0 \\u0111\\xE3 xu\\u1EA5t b\\u1EA3n/\\u0111\\xE3 g\\u1EEDi. B\\u1EA1n ch\\u1EC9 n\\xEAn g\\u1EEDi l\\u1EA1i c\\xE1c m\\xF3n khi c\\u1EADp nh\\u1EADt th\\u01B0 vi\\u1EC7n hi\\u1EC7n c\\xF3 ho\\u1EB7c g\\u1EEDi.\"\n};\nvar publishSuccessDialog = {\n  title: \"\",\n  content: \"\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\",\n  removeItemsFromLib: \"X\\xF3a m\\xF3n \\u0111\\xE3 ch\\u1ECDn kh\\u1ECFi th\\u01B0 vi\\u1EC7n\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\",\n  link: \"\"\n};\nvar stats = {\n  angle: \"\",\n  element: \"\",\n  elements: \"\",\n  height: \"\",\n  scene: \"\",\n  selected: \"\",\n  storage: \"\",\n  title: \"\",\n  total: \"\",\n  version: \"\",\n  versionCopy: \"\",\n  versionNotAvailable: \"\",\n  width: \"\"\n};\nvar toast = {\n  addedToLibrary: \"\",\n  copyStyles: \"\",\n  copyToClipboard: \"\",\n  copyToClipboardAsPng: \"\",\n  fileSaved: \"\",\n  fileSavedToFilename: \"\",\n  canvas: \"canvas\",\n  selection: \"\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar vi_VN_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=vi-VN-Y5CQ2EKQ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/vi-VN-Y5CQ2EKQ.js\n"));

/***/ })

}]);