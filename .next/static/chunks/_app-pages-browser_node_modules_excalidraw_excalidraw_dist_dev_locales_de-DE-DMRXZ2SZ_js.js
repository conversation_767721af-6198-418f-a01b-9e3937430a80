"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_de-DE-DMRXZ2SZ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/de-DE-DMRXZ2SZ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/de-DE-DMRXZ2SZ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ de_DE_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/de-DE.json\nvar labels = {\n  paste: \"Einf\\xFCgen\",\n  pasteAsPlaintext: \"Als reinen Text einf\\xFCgen\",\n  pasteCharts: \"Diagramme einf\\xFCgen\",\n  selectAll: \"Alle ausw\\xE4hlen\",\n  multiSelect: \"Element zur Auswahl hinzuf\\xFCgen\",\n  moveCanvas: \"Leinwand verschieben\",\n  cut: \"Ausschneiden\",\n  copy: \"Kopieren\",\n  copyAsPng: \"In Zwischenablage kopieren (PNG)\",\n  copyAsSvg: \"In Zwischenablage kopieren (SVG)\",\n  copyText: \"In die Zwischenablage als Text kopieren\",\n  copySource: \"Quelle in Zwischenablage kopieren\",\n  convertToCode: \"In Code konvertieren\",\n  bringForward: \"Nach vorne\",\n  sendToBack: \"In den Hintergrund\",\n  bringToFront: \"In den Vordergrund\",\n  sendBackward: \"Nach hinten\",\n  delete: \"L\\xF6schen\",\n  copyStyles: \"Formatierung kopieren\",\n  pasteStyles: \"Formatierung \\xFCbernehmen\",\n  stroke: \"Strich\",\n  background: \"Hintergrund\",\n  fill: \"F\\xFCllung\",\n  strokeWidth: \"Strichst\\xE4rke\",\n  strokeStyle: \"Konturstil\",\n  strokeStyle_solid: \"Durchgezogen\",\n  strokeStyle_dashed: \"Gestrichelt\",\n  strokeStyle_dotted: \"Gepunktet\",\n  sloppiness: \"Sauberkeit\",\n  opacity: \"Deckkraft\",\n  textAlign: \"Textausrichtung\",\n  edges: \"Kanten\",\n  sharp: \"Scharf\",\n  round: \"Rund\",\n  arrowheads: \"Pfeilspitzen\",\n  arrowhead_none: \"Keine\",\n  arrowhead_arrow: \"Pfeil\",\n  arrowhead_bar: \"Balken\",\n  arrowhead_circle: \"Kreis\",\n  arrowhead_circle_outline: \"Kreis (Umrandung)\",\n  arrowhead_triangle: \"Dreieck\",\n  arrowhead_triangle_outline: \"Dreieck (Umrandung)\",\n  arrowhead_diamond: \"Raute\",\n  arrowhead_diamond_outline: \"Raute (Umrandung)\",\n  fontSize: \"Schriftgr\\xF6\\xDFe\",\n  fontFamily: \"Schriftfamilie\",\n  addWatermark: '\"Made with Excalidraw\" hinzuf\\xFCgen',\n  handDrawn: \"Handgezeichnet\",\n  normal: \"Normal\",\n  code: \"Code\",\n  small: \"Klein\",\n  medium: \"Mittel\",\n  large: \"Gro\\xDF\",\n  veryLarge: \"Sehr gro\\xDF\",\n  solid: \"Deckend\",\n  hachure: \"Schraffiert\",\n  zigzag: \"Zickzack\",\n  crossHatch: \"Kreuzschraffiert\",\n  thin: \"D\\xFCnn\",\n  bold: \"Fett\",\n  left: \"Links\",\n  center: \"Zentriert\",\n  right: \"Rechts\",\n  extraBold: \"Extra Fett\",\n  architect: \"Architekt\",\n  artist: \"K\\xFCnstler\",\n  cartoonist: \"Karikaturist\",\n  fileTitle: \"Dateiname\",\n  colorPicker: \"Farbausw\\xE4hler\",\n  canvasColors: \"Auf Leinwand verwendet\",\n  canvasBackground: \"Zeichenfl\\xE4chenhintergrund\",\n  drawingCanvas: \"Leinwand\",\n  layers: \"Ebenen\",\n  actions: \"Aktionen\",\n  language: \"Sprache\",\n  liveCollaboration: \"Live-Zusammenarbeit...\",\n  duplicateSelection: \"Duplizieren\",\n  untitled: \"Unbenannt\",\n  name: \"Name\",\n  yourName: \"Dein Name\",\n  madeWithExcalidraw: \"Made with Excalidraw\",\n  group: \"Auswahl gruppieren\",\n  ungroup: \"Gruppierung aufheben\",\n  collaborators: \"Mitarbeitende\",\n  showGrid: \"Raster anzeigen\",\n  addToLibrary: \"Zur Bibliothek hinzuf\\xFCgen\",\n  removeFromLibrary: \"Aus Bibliothek entfernen\",\n  libraryLoadingMessage: \"Lade Bibliothek\\u2026\",\n  libraries: \"Bibliotheken durchsuchen\",\n  loadingScene: \"Lade Zeichnung\\u2026\",\n  align: \"Ausrichten\",\n  alignTop: \"Obere Kanten\",\n  alignBottom: \"Untere Kanten\",\n  alignLeft: \"Linke Kanten\",\n  alignRight: \"Rechte Kanten\",\n  centerVertically: \"Vertikal zentrieren\",\n  centerHorizontally: \"Horizontal zentrieren\",\n  distributeHorizontally: \"Horizontal verteilen\",\n  distributeVertically: \"Vertikal verteilen\",\n  flipHorizontal: \"Horizontal spiegeln\",\n  flipVertical: \"Vertikal spiegeln\",\n  viewMode: \"Ansichtsmodus\",\n  share: \"Teilen\",\n  showStroke: \"Auswahl f\\xFCr Strichfarbe anzeigen\",\n  showBackground: \"Hintergrundfarbe ausw\\xE4hlen\",\n  toggleTheme: \"Design umschalten\",\n  personalLib: \"Pers\\xF6nliche Bibliothek\",\n  excalidrawLib: \"Excalidraw Bibliothek\",\n  decreaseFontSize: \"Schriftgr\\xF6\\xDFe verkleinern\",\n  increaseFontSize: \"Schrift vergr\\xF6\\xDFern\",\n  unbindText: \"Text l\\xF6sen\",\n  bindText: \"Text an Container binden\",\n  createContainerFromText: \"Text in Container einbetten\",\n  link: {\n    edit: \"Link bearbeiten\",\n    editEmbed: \"Link bearbeiten & einbetten\",\n    create: \"Link erstellen\",\n    createEmbed: \"Link erstellen & einbetten\",\n    label: \"Link\",\n    labelEmbed: \"Verlinken & einbetten\",\n    empty: \"Kein Link festgelegt\"\n  },\n  lineEditor: {\n    edit: \"Linie bearbeiten\",\n    exit: \"Linieneditor verlassen\"\n  },\n  elementLock: {\n    lock: \"Sperren\",\n    unlock: \"Entsperren\",\n    lockAll: \"Alle sperren\",\n    unlockAll: \"Alle entsperren\"\n  },\n  statusPublished: \"Ver\\xF6ffentlicht\",\n  sidebarLock: \"Seitenleiste offen lassen\",\n  selectAllElementsInFrame: \"Alle Elemente im Rahmen ausw\\xE4hlen\",\n  removeAllElementsFromFrame: \"Alle Elemente aus dem Rahmen entfernen\",\n  eyeDropper: \"Farbe von der Zeichenfl\\xE4che ausw\\xE4hlen\",\n  textToDiagram: \"Text zu Diagramm\",\n  prompt: \"Eingabe\"\n};\nvar library = {\n  noItems: \"Noch keine Elemente hinzugef\\xFCgt...\",\n  hint_emptyLibrary: \"W\\xE4hle ein Element auf der Zeichenfl\\xE4che, um es hier hinzuzuf\\xFCgen. Oder installiere eine Bibliothek aus dem \\xF6ffentlichen Verzeichnis.\",\n  hint_emptyPrivateLibrary: \"W\\xE4hle ein Element von der Zeichenfl\\xE4che, um es hier hinzuzuf\\xFCgen.\"\n};\nvar buttons = {\n  clearReset: \"Zeichenfl\\xE4che l\\xF6schen & Hintergrundfarbe zur\\xFCcksetzen\",\n  exportJSON: \"In Datei exportieren\",\n  exportImage: \"Exportiere Bild...\",\n  export: \"Speichern als...\",\n  copyToClipboard: \"In Zwischenablage kopieren\",\n  save: \"In aktueller Datei speichern\",\n  saveAs: \"Speichern unter\",\n  load: \"\\xD6ffnen\",\n  getShareableLink: \"Teilbaren Link erhalten\",\n  close: \"Schlie\\xDFen\",\n  selectLanguage: \"Sprache ausw\\xE4hlen\",\n  scrollBackToContent: \"Zur\\xFCck zum Inhalt\",\n  zoomIn: \"Vergr\\xF6\\xDFern\",\n  zoomOut: \"Verkleinern\",\n  resetZoom: \"Zoom zur\\xFCcksetzen\",\n  menu: \"Men\\xFC\",\n  done: \"Fertig\",\n  edit: \"Bearbeiten\",\n  undo: \"R\\xFCckg\\xE4ngig machen\",\n  redo: \"Wiederholen\",\n  resetLibrary: \"Bibliothek zur\\xFCcksetzen\",\n  createNewRoom: \"Neuen Raum erstellen\",\n  fullScreen: \"Vollbildanzeige\",\n  darkMode: \"Dunkles Design\",\n  lightMode: \"Helles Design\",\n  zenMode: \"Zen-Modus\",\n  objectsSnapMode: \"Einrasten an Objekten\",\n  exitZenMode: \"Zen-Modus verlassen\",\n  cancel: \"Abbrechen\",\n  clear: \"L\\xF6schen\",\n  remove: \"Entfernen\",\n  embed: \"Einbettung umschalten\",\n  publishLibrary: \"Ver\\xF6ffentlichen\",\n  submit: \"Absenden\",\n  confirm: \"Best\\xE4tigen\",\n  embeddableInteractionButton: \"Klicken, um zu interagieren\"\n};\nvar alerts = {\n  clearReset: \"Dies wird die ganze Zeichenfl\\xE4che l\\xF6schen. Bist du dir sicher?\",\n  couldNotCreateShareableLink: \"Konnte keinen teilbaren Link erstellen.\",\n  couldNotCreateShareableLinkTooBig: \"Konnte keinen teilbaren Link erstellen: Die Zeichnung ist zu gro\\xDF\",\n  couldNotLoadInvalidFile: \"Ung\\xFCltige Datei konnte nicht geladen werden\",\n  importBackendFailed: \"Import vom Server ist fehlgeschlagen.\",\n  cannotExportEmptyCanvas: \"Leere Zeichenfl\\xE4che kann nicht exportiert werden.\",\n  couldNotCopyToClipboard: \"Kopieren in die Zwischenablage fehlgeschlagen.\",\n  decryptFailed: \"Daten konnten nicht entschl\\xFCsselt werden.\",\n  uploadedSecurly: \"Der Upload wurde mit Ende-zu-Ende-Verschl\\xFCsselung gespeichert. Weder Excalidraw noch Dritte k\\xF6nnen den Inhalt einsehen.\",\n  loadSceneOverridePrompt: \"Das Laden einer externen Zeichnung ersetzt den vorhandenen Inhalt. M\\xF6chtest du fortfahren?\",\n  collabStopOverridePrompt: \"Das Stoppen der Sitzung wird deine vorherige, lokal gespeicherte Zeichnung \\xFCberschreiben. Bist du dir sicher?\\n\\n(Wenn du deine lokale Zeichnung behalten m\\xF6chtest, schlie\\xDFe stattdessen den Browser-Tab.)\",\n  errorAddingToLibrary: \"Das Element konnte nicht zur Bibliothek hinzugef\\xFCgt werden\",\n  errorRemovingFromLibrary: \"Das Element konnte nicht aus der Bibliothek entfernt werden\",\n  confirmAddLibrary: \"Dies f\\xFCgt {{numShapes}} Form(en) zu deiner Bibliothek hinzu. Bist du dir sicher?\",\n  imageDoesNotContainScene: \"Dieses Bild scheint keine Szenendaten zu enthalten. Hast Du das Einbetten der Szene w\\xE4hrend des Exports aktiviert?\",\n  cannotRestoreFromImage: \"Die Zeichnung konnte aus dieser Bilddatei nicht wiederhergestellt werden\",\n  invalidSceneUrl: \"Die Szene konnte nicht von der angegebenen URL importiert werden. Sie ist entweder fehlerhaft oder enth\\xE4lt keine g\\xFCltigen Excalidraw JSON-Daten.\",\n  resetLibrary: \"Dieses l\\xF6scht deine Bibliothek. Bist du sicher?\",\n  removeItemsFromsLibrary: \"{{count}} Element(e) aus der Bibliothek l\\xF6schen?\",\n  invalidEncryptionKey: \"Verschl\\xFCsselungsschl\\xFCssel muss 22 Zeichen lang sein. Die Live-Zusammenarbeit ist deaktiviert.\",\n  collabOfflineWarning: \"Keine Internetverbindung verf\\xFCgbar.\\nDeine \\xC4nderungen werden nicht gespeichert!\"\n};\nvar errors = {\n  unsupportedFileType: \"Nicht unterst\\xFCtzter Dateityp.\",\n  imageInsertError: \"Das Bild konnte nicht eingef\\xFCgt werden. Versuche es sp\\xE4ter erneut...\",\n  fileTooBig: \"Die Datei ist zu gro\\xDF. Die maximal zul\\xE4ssige Gr\\xF6\\xDFe ist {{maxSize}}.\",\n  svgImageInsertError: \"SVG-Bild konnte nicht eingef\\xFCgt werden. Das SVG-Markup sieht ung\\xFCltig aus.\",\n  failedToFetchImage: \"Bild konnte nicht abgerufen werden.\",\n  invalidSVGString: \"Ung\\xFCltige SVG.\",\n  cannotResolveCollabServer: \"Konnte keine Verbindung zum Collab-Server herstellen. Bitte lade die Seite neu und versuche es erneut.\",\n  importLibraryError: \"Bibliothek konnte nicht geladen werden\",\n  collabSaveFailed: \"Keine Speicherung in der Backend-Datenbank m\\xF6glich. Wenn die Probleme weiterhin bestehen, solltest Du Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.\",\n  collabSaveFailed_sizeExceeded: \"Keine Speicherung in der Backend-Datenbank m\\xF6glich, die Zeichenfl\\xE4che scheint zu gro\\xDF zu sein. Du solltest Deine Datei lokal speichern, um sicherzustellen, dass Du Deine Arbeit nicht verlierst.\",\n  imageToolNotSupported: \"Bilder sind deaktiviert.\",\n  brave_measure_text_error: {\n    line1: \"Sieht so aus, als ob Du den Brave-Browser verwendest und die <bold>aggressive Blockierung von Fingerabdr\\xFCcken</bold> aktiviert hast.\",\n    line2: \"Dies k\\xF6nnte dazu f\\xFChren, dass die <bold>Textelemente</bold> in Ihren Zeichnungen zerst\\xF6rt werden.\",\n    line3: \"Wir empfehlen dringend, diese Einstellung zu deaktivieren. Dazu kannst Du <link>diesen Schritten</link> folgen.\",\n    line4: \"Wenn die Deaktivierung dieser Einstellung die fehlerhafte Anzeige von Textelementen nicht behebt, \\xF6ffne bitte ein <issueLink>Ticket</issueLink> auf unserem GitHub oder schreibe uns auf <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Einbettbare Elemente k\\xF6nnen der Bibliothek nicht hinzugef\\xFCgt werden.\",\n    iframe: \"IFrame-Elemente k\\xF6nnen nicht zur Bibliothek hinzugef\\xFCgt werden.\",\n    image: \"Unterst\\xFCtzung f\\xFCr das Hinzuf\\xFCgen von Bildern in die Bibliothek kommt bald!\"\n  },\n  asyncPasteFailedOnRead: \"Einf\\xFCgen fehlgeschlagen (konnte aus der Zwischenablage des Systems nicht gelesen werden).\",\n  asyncPasteFailedOnParse: \"Einf\\xFCgen fehlgeschlagen.\",\n  copyToSystemClipboardFailed: \"Kopieren in die Zwischenablage fehlgeschlagen.\"\n};\nvar toolBar = {\n  selection: \"Auswahl\",\n  image: \"Bild einf\\xFCgen\",\n  rectangle: \"Rechteck\",\n  diamond: \"Raute\",\n  ellipse: \"Ellipse\",\n  arrow: \"Pfeil\",\n  line: \"Linie\",\n  freedraw: \"Zeichnen\",\n  text: \"Text\",\n  library: \"Bibliothek\",\n  lock: \"Ausgew\\xE4hltes Werkzeug nach Zeichnen aktiv lassen\",\n  penMode: \"Stift-Modus - Ber\\xFChrung verhindern\",\n  link: \"Link f\\xFCr ausgew\\xE4hlte Form hinzuf\\xFCgen / aktualisieren\",\n  eraser: \"Radierer\",\n  frame: \"Rahmenwerkzeug\",\n  magicframe: \"Wireframe zu Code\",\n  embeddable: \"Web-Einbettung\",\n  laser: \"Laserpointer\",\n  hand: \"Hand (Schwenkwerkzeug)\",\n  extraTools: \"Weitere Werkzeuge\",\n  mermaidToExcalidraw: \"Mermaid zu Excalidraw\",\n  magicSettings: \"KI-Einstellungen\"\n};\nvar headings = {\n  canvasActions: \"Aktionen f\\xFCr Zeichenfl\\xE4che\",\n  selectedShapeActions: \"Aktionen f\\xFCr Auswahl\",\n  shapes: \"Formen\"\n};\nvar hints = {\n  canvasPanning: \"Um die Zeichenfl\\xE4che zu verschieben, halte das Mausrad oder die Leertaste w\\xE4hrend des Ziehens, oder verwende das Hand-Werkzeug\",\n  linearElement: \"Klicken f\\xFCr Linie mit mehreren Punkten, Ziehen f\\xFCr einzelne Linie\",\n  freeDraw: \"Klicke und ziehe. Lass los, wenn du fertig bist\",\n  text: \"Tipp: Du kannst auch Text hinzuf\\xFCgen, indem du mit dem Auswahlwerkzeug auf eine beliebige Stelle doppelklickst\",\n  embeddable: \"Klicken und ziehen, um eine Webseiten-Einbettung zu erstellen\",\n  text_selected: \"Doppelklicken oder Eingabetaste dr\\xFCcken, um Text zu bearbeiten\",\n  text_editing: \"Dr\\xFCcke Escape oder CtrlOrCmd+Eingabetaste, um die Bearbeitung abzuschlie\\xDFen\",\n  linearElementMulti: \"Zum Beenden auf den letzten Punkt klicken oder Escape oder Eingabe dr\\xFCcken\",\n  lockAngle: \"Du kannst Winkel einschr\\xE4nken, indem du SHIFT gedr\\xFCckt h\\xE4ltst\",\n  resize: \"Du kannst die Proportionen einschr\\xE4nken, indem du SHIFT w\\xE4hrend der Gr\\xF6\\xDFen\\xE4nderung gedr\\xFCckt h\\xE4ltst. Halte ALT gedr\\xFCckt, um die Gr\\xF6\\xDFe vom Zentrum aus zu \\xE4ndern\",\n  resizeImage: \"Du kannst die Gr\\xF6\\xDFe frei \\xE4ndern, indem du SHIFT gedr\\xFCckt h\\xE4ltst; halte ALT, um die Gr\\xF6\\xDFe vom Zentrum aus zu \\xE4ndern\",\n  rotate: \"Du kannst Winkel einschr\\xE4nken, indem du SHIFT w\\xE4hrend der Drehung gedr\\xFCckt h\\xE4ltst\",\n  lineEditor_info: \"CtrlOrCmd halten und Doppelklick oder CtrlOrCmd + Eingabe dr\\xFCcken, um Punkte zu bearbeiten\",\n  lineEditor_pointSelected: \"Dr\\xFCcke L\\xF6schen, um Punkt(e) zu entfernen, CtrlOrCmd+D zum Duplizieren oder ziehe zum Verschieben\",\n  lineEditor_nothingSelected: \"W\\xE4hle einen zu bearbeitenden Punkt (halte SHIFT gedr\\xFCckt um mehrere Punkte auszuw\\xE4hlen),\\noder halte Alt gedr\\xFCckt und klicke um neue Punkte hinzuzuf\\xFCgen\",\n  placeImage: \"Klicken, um das Bild zu platzieren oder klicken und ziehen um seine Gr\\xF6\\xDFe manuell zu setzen\",\n  publishLibrary: \"Ver\\xF6ffentliche deine eigene Bibliothek\",\n  bindTextToElement: \"Zum Hinzuf\\xFCgen Eingabetaste dr\\xFCcken\",\n  deepBoxSelect: \"Halte CtrlOrCmd gedr\\xFCckt, um innerhalb der Gruppe auszuw\\xE4hlen, und um Ziehen zu vermeiden\",\n  eraserRevert: \"Halte Alt gedr\\xFCckt, um die zum L\\xF6schen markierten Elemente zur\\xFCckzusetzen\",\n  firefox_clipboard_write: 'Diese Funktion kann wahrscheinlich aktiviert werden, indem die Einstellung \"dom.events.asyncClipboard.clipboardItem\" auf \"true\" gesetzt wird. Um die Browsereinstellungen in Firefox zu \\xE4ndern, besuche die Seite \"about:config\".',\n  disableSnapping: \"Halte CtrlOrCmd gedr\\xFCckt, um das Einrasten zu deaktivieren\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Vorschau kann nicht angezeigt werden\",\n  canvasTooBig: \"Die Leinwand ist m\\xF6glicherweise zu gro\\xDF.\",\n  canvasTooBigTip: \"Tipp: Schiebe die am weitesten entfernten Elemente ein wenig n\\xE4her zusammen.\"\n};\nvar errorSplash = {\n  headingMain: \"Es ist ein Fehler aufgetreten. Versuche <button>die Seite neu zu laden.</button>\",\n  clearCanvasMessage: \"Wenn das Neuladen nicht funktioniert, versuche <button>die Zeichenfl\\xE4che zu l\\xF6schen.</button>\",\n  clearCanvasCaveat: \" Dies wird zum Verlust von Daten f\\xFChren \",\n  trackedToSentry: \"Der Fehler mit der Kennung {{eventId}} wurde in unserem System registriert.\",\n  openIssueMessage: \"Wir waren sehr vorsichtig und haben deine Zeichnungsinformationen nicht in die Fehlerinformationen aufgenommen. Wenn deine Zeichnung nicht privat ist, unterst\\xFCtze uns bitte \\xFCber unseren <button>Bug-Tracker</button>. Bitte teile die unten stehenden Informationen mit uns im GitHub Issue (Kopieren und Einf\\xFCgen).\",\n  sceneContent: \"Zeichnungsinhalt:\"\n};\nvar roomDialog = {\n  desc_intro: \"Du kannst Leute zu deiner aktuellen Zeichnung einladen um mit ihnen zusammenzuarbeiten.\",\n  desc_privacy: \"Keine Sorge, die Sitzung nutzt eine Ende-zu-Ende-Verschl\\xFCsselung. Alles was du zeichnest, bleibt privat. Auch unser Server sieht nicht, was du dir einfallen l\\xE4sst.\",\n  button_startSession: \"Sitzung starten\",\n  button_stopSession: \"Sitzung beenden\",\n  desc_inProgressIntro: \"Die Live-Sitzung wird nun ausgef\\xFChrt.\",\n  desc_shareLink: \"Teile diesen Link mit allen, mit denen du zusammenarbeiten m\\xF6chtest:\",\n  desc_exitSession: \"Wenn du die Sitzung beendest, wird deine Verbindung zum Raum getrennt. Du kannst jedoch lokal weiter an der Zeichnung arbeiten. Beachte, dass dies keine Auswirkungen auf andere hat und diese weiterhin gemeinsam an ihrer Version arbeiten k\\xF6nnen.\",\n  shareTitle: \"An einer Live-Kollaborationssitzung auf Excalidraw teilnehmen\"\n};\nvar errorDialog = {\n  title: \"Fehler\"\n};\nvar exportDialog = {\n  disk_title: \"Auf Festplatte speichern\",\n  disk_details: \"Exportiere die Zeichnungsdaten in eine Datei, die Du sp\\xE4ter importieren kannst.\",\n  disk_button: \"Als Datei speichern\",\n  link_title: \"Teilbarer Link\",\n  link_details: \"Als schreibgesch\\xFCtzten Link exportieren.\",\n  link_button: \"Als Link exportieren\",\n  excalidrawplus_description: \"Speichere die Szene in deinem Excalidraw+ Arbeitsbereich.\",\n  excalidrawplus_button: \"Exportieren\",\n  excalidrawplus_exportError: \"Konnte nicht nach Excalidraw+ exportieren...\"\n};\nvar helpDialog = {\n  blog: \"Lies unseren Blog\",\n  click: \"klicken\",\n  deepSelect: \"Auswahl innerhalb der Gruppe\",\n  deepBoxSelect: \"Auswahl innerhalb der Gruppe, und Ziehen vermeiden\",\n  curvedArrow: \"Gebogener Pfeil\",\n  curvedLine: \"Gebogene Linie\",\n  documentation: \"Dokumentation\",\n  doubleClick: \"doppelklicken\",\n  drag: \"ziehen\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Linien-/Pfeil-Punkte bearbeiten\",\n  editText: \"Text bearbeiten / Label hinzuf\\xFCgen\",\n  github: \"Ein Problem gefunden? Informiere uns\",\n  howto: \"Folge unseren Anleitungen\",\n  or: \"oder\",\n  preventBinding: \"Pfeil-Bindung verhindern\",\n  tools: \"Werkzeuge\",\n  shortcuts: \"Tastaturk\\xFCrzel\",\n  textFinish: \"Bearbeitung beenden (Texteditor)\",\n  textNewLine: \"Neue Zeile hinzuf\\xFCgen (Texteditor)\",\n  title: \"Hilfe\",\n  view: \"Ansicht\",\n  zoomToFit: \"Zoomen um alle Elemente einzupassen\",\n  zoomToSelection: \"Auf Auswahl zoomen\",\n  toggleElementLock: \"Auswahl sperren/entsperren\",\n  movePageUpDown: \"Seite nach oben/unten verschieben\",\n  movePageLeftRight: \"Seite nach links/rechts verschieben\"\n};\nvar clearCanvasDialog = {\n  title: \"Zeichenfl\\xE4che l\\xF6schen\"\n};\nvar publishDialog = {\n  title: \"Bibliothek ver\\xF6ffentlichen\",\n  itemName: \"Elementname\",\n  authorName: \"Name des Autors\",\n  githubUsername: \"GitHub-Benutzername\",\n  twitterUsername: \"Twitter-Benutzername\",\n  libraryName: \"Name der Bibliothek\",\n  libraryDesc: \"Beschreibung der Bibliothek\",\n  website: \"Webseite\",\n  placeholder: {\n    authorName: \"Dein Name oder Benutzername\",\n    libraryName: \"Name deiner Bibliothek\",\n    libraryDesc: \"Beschreibung deiner Bibliothek, um anderen Nutzern bei der Verwendung zu helfen\",\n    githubHandle: \"GitHub-Handle (optional), damit du die Bibliothek bearbeiten kannst, wenn sie zur \\xDCberpr\\xFCfung eingereicht wurde\",\n    twitterHandle: \"Twitter-Benutzername (optional), damit wir wissen, wen wir bei Werbung \\xFCber Twitter nennen k\\xF6nnen\",\n    website: \"Link zu deiner pers\\xF6nlichen Webseite oder zu anderer Seite (optional)\"\n  },\n  errors: {\n    required: \"Erforderlich\",\n    website: \"G\\xFCltige URL eingeben\"\n  },\n  noteDescription: \"Sende deine Bibliothek ein, um in die <link>\\xF6ffentliche Bibliotheks-Repository aufgenommen zu werden</link>damit andere Nutzer sie in ihren Zeichnungen verwenden k\\xF6nnen.\",\n  noteGuidelines: \"Die Bibliothek muss zuerst manuell freigegeben werden. Bitte lies die <link>Richtlinien</link> vor dem Absenden. Du ben\\xF6tigst ein GitHub-Konto, um zu kommunizieren und \\xC4nderungen vorzunehmen, falls erforderlich, aber es ist nicht unbedingt erforderlich.\",\n  noteLicense: \"Mit dem Absenden stimmst du zu, dass die Bibliothek unter der <link>MIT-Lizenz, </link>die zusammengefasst beinhaltet, dass jeder sie ohne Einschr\\xE4nkungen nutzen kann.\",\n  noteItems: \"Jedes Bibliothekselement muss einen eigenen Namen haben, damit es gefiltert werden kann. Die folgenden Bibliothekselemente werden hinzugef\\xFCgt:\",\n  atleastOneLibItem: \"Bitte w\\xE4hle mindestens ein Bibliothekselement aus, um zu beginnen\",\n  republishWarning: \"Hinweis: Einige der ausgew\\xE4hlten Elemente sind bereits als ver\\xF6ffentlicht/eingereicht markiert. Du solltest Elemente nur erneut einreichen, wenn Du eine existierende Bibliothek oder Einreichung aktualisierst.\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliothek \\xFCbermittelt\",\n  content: \"Vielen Dank {{authorName}}. Deine Bibliothek wurde zur \\xDCberpr\\xFCfung eingereicht. Du kannst den Status verfolgen<link>hier</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Bibliothek zur\\xFCcksetzen\",\n  removeItemsFromLib: \"Ausgew\\xE4hlte Elemente aus der Bibliothek entfernen\"\n};\nvar imageExportDialog = {\n  header: \"Bild exportieren\",\n  label: {\n    withBackground: \"Hintergrund\",\n    onlySelected: \"Nur ausgew\\xE4hlte\",\n    darkMode: \"Dunkler Modus\",\n    embedScene: \"Szene einbetten\",\n    scale: \"Skalierung\",\n    padding: \"Abstand\"\n  },\n  tooltip: {\n    embedScene: \"Die Zeichnungsdaten werden in der exportierten PNG/SVG-Datei gespeichert, sodass das Dokument sp\\xE4ter weiter bearbeitet werden kann. \\nDieses wird die exportierte Datei vergr\\xF6\\xDFern.\"\n  },\n  title: {\n    exportToPng: \"Als PNG exportieren\",\n    exportToSvg: \"Als SVG exportieren\",\n    copyPngToClipboard: \"PNG in die Zwischenablage kopieren\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"In Zwischenablage kopieren\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Da deine Zeichnungen Ende-zu-Ende verschl\\xFCsselt werden, sehen auch unsere Excalidraw-Server sie niemals.\",\n  link: \"Blogbeitrag \\xFCber Ende-zu-Ende-Verschl\\xFCsselung in Excalidraw\"\n};\nvar stats = {\n  angle: \"Winkel\",\n  element: \"Element\",\n  elements: \"Elemente\",\n  height: \"H\\xF6he\",\n  scene: \"Zeichnung\",\n  selected: \"Ausgew\\xE4hlt\",\n  storage: \"Speicher\",\n  title: \"Statistiken f\\xFCr Nerds\",\n  total: \"Gesamt\",\n  version: \"Version\",\n  versionCopy: \"Zum Kopieren klicken\",\n  versionNotAvailable: \"Version nicht verf\\xFCgbar\",\n  width: \"Breite\"\n};\nvar toast = {\n  addedToLibrary: \"Zur Bibliothek hinzugef\\xFCgt\",\n  copyStyles: \"Formatierungen kopiert.\",\n  copyToClipboard: \"In die Zwischenablage kopiert.\",\n  copyToClipboardAsPng: \"{{exportSelection}} als PNG in die Zwischenablage kopiert\\n({{exportColorScheme}})\",\n  fileSaved: \"Datei gespeichert.\",\n  fileSavedToFilename: \"Als {filename} gespeichert\",\n  canvas: \"Zeichenfl\\xE4che\",\n  selection: \"Auswahl\",\n  pasteAsSingleElement: \"Verwende {{shortcut}} , um als einzelnes Element\\neinzuf\\xFCgen oder in einen existierenden Texteditor einzuf\\xFCgen\",\n  unableToEmbed: \"Einbetten dieser URL ist derzeit nicht zul\\xE4ssig. Erstelle einen Issue auf GitHub, um die URL freigeben zu lassen\",\n  unrecognizedLinkFormat: \"Der Link, den Du eingebettet hast, stimmt nicht mit dem erwarteten Format \\xFCberein. Bitte versuche den 'embed' String einzuf\\xFCgen, der von der Quellseite zur Verf\\xFCgung gestellt wird\"\n};\nvar colors = {\n  transparent: \"Transparent\",\n  black: \"Schwarz\",\n  white: \"Wei\\xDF\",\n  red: \"Rot\",\n  pink: \"Pink\",\n  grape: \"Traube\",\n  violet: \"Violett\",\n  gray: \"Grau\",\n  blue: \"Blau\",\n  cyan: \"Cyan\",\n  teal: \"Blaugr\\xFCn\",\n  green: \"Gr\\xFCn\",\n  yellow: \"Gelb\",\n  orange: \"Orange\",\n  bronze: \"Bronze\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Alle Daten werden lokal in Deinem Browser gespeichert.\",\n    center_heading_plus: \"M\\xF6chtest du stattdessen zu Excalidraw+ gehen?\",\n    menuHint: \"Exportieren, Einstellungen, Sprachen, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportieren, Einstellungen und mehr...\",\n    center_heading: \"Diagramme. Einfach. Gemacht.\",\n    toolbarHint: \"W\\xE4hle ein Werkzeug & beginne zu zeichnen!\",\n    helpHint: \"Kurzbefehle & Hilfe\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Beliebteste benutzerdefinierte Farben\",\n  colors: \"Farben\",\n  shades: \"Schattierungen\",\n  hexCode: \"Hex-Code\",\n  noShades: \"Keine Schattierungen f\\xFCr diese Farbe verf\\xFCgbar\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Als Bild exportieren\",\n      button: \"Als Bild exportieren\",\n      description: \"Exportiere die Zeichnungsdaten als ein Bild, von dem Du sp\\xE4ter importieren kannst.\"\n    },\n    saveToDisk: {\n      title: \"Auf Festplatte speichern\",\n      button: \"Auf Festplatte speichern\",\n      description: \"Exportiere die Zeichnungsdaten in eine Datei, von der Du sp\\xE4ter importieren kannst.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Export nach Excalidraw+\",\n      description: \"Speichere die Szene in deinem Excalidraw+-Arbeitsbereich.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Aus Datei laden\",\n      button: \"Aus Datei laden\",\n      description: \"Das Laden aus einer Datei wird <bold>Deinen vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst Deine Zeichnung zuerst mit einer der folgenden Optionen sichern.\"\n    },\n    shareableLink: {\n      title: \"Aus Link laden\",\n      button: \"Meinen Inhalt ersetzen\",\n      description: \"Das Laden einer externen Zeichnung wird <bold>Deinen vorhandenen Inhalt ersetzen</bold>.<br></br>Du kannst Deine Zeichnung zuerst mit einer der folgenden Optionen sichern.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid zu Excalidraw\",\n  button: \"Einf\\xFCgen\",\n  description: \"Derzeit werden nur <flowchartLink>Flussdiagramme</flowchartLink>, <sequenceLink>Sequenzdiagramme</sequenceLink> und <classLink>Klassendiagramme</classLink> unterst\\xFCtzt. Die anderen Typen werden als Bild in Excalidraw dargestellt.\",\n  syntax: \"Mermaid-Syntax\",\n  preview: \"Vorschau\"\n};\nvar de_DE_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=de-DE-DMRXZ2SZ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/de-DE-DMRXZ2SZ.js\n"));

/***/ })

}]);