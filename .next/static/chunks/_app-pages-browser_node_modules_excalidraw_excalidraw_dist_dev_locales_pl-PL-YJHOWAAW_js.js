"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_pl-PL-YJHOWAAW_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/pl-PL-YJHOWAAW.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/pl-PL-YJHOWAAW.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ pl_PL_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/pl-PL.json\nvar labels = {\n  paste: \"Wklej\",\n  pasteAsPlaintext: \"Wklej jako zwyk\\u0142y tekst\",\n  pasteCharts: \"Wklej wykresy\",\n  selectAll: \"Zaznacz wszystko\",\n  multiSelect: \"Dodaj element do zaznaczenia\",\n  moveCanvas: \"Przesu\\u0144 obszar roboczy\",\n  cut: \"Wytnij\",\n  copy: \"Kopiuj\",\n  copyAsPng: \"Skopiuj do schowka jako plik PNG\",\n  copyAsSvg: \"Skopiuj do schowka jako plik SVG\",\n  copyText: \"Skopiuj do schowka jako tekst\",\n  copySource: \"Skopiuj \\u017Ar\\xF3d\\u0142o do schowka\",\n  convertToCode: \"Skonwertuj do kodu\",\n  bringForward: \"Przenie\\u015B wy\\u017Cej\",\n  sendToBack: \"Przenie\\u015B na sp\\xF3d\",\n  bringToFront: \"Przenie\\u015B na wierzch\",\n  sendBackward: \"Przenie\\u015B ni\\u017Cej\",\n  delete: \"Usu\\u0144\",\n  copyStyles: \"Kopiuj style\",\n  pasteStyles: \"Wklej style\",\n  stroke: \"Kolor obramowania\",\n  background: \"Kolor wype\\u0142nienia\",\n  fill: \"Wype\\u0142nienie\",\n  strokeWidth: \"Grubo\\u015B\\u0107 obramowania\",\n  strokeStyle: \"Styl obrysu\",\n  strokeStyle_solid: \"Pe\\u0142ny\",\n  strokeStyle_dashed: \"Kreskowany\",\n  strokeStyle_dotted: \"Kropkowany\",\n  sloppiness: \"Styl kreski\",\n  opacity: \"Prze\\u017Aroczysto\\u015B\\u0107\",\n  textAlign: \"Wyr\\xF3wnanie tekstu\",\n  edges: \"Kraw\\u0119dzie\",\n  sharp: \"Ostry\",\n  round: \"Zaokr\\u0105glij\",\n  arrowheads: \"Groty\",\n  arrowhead_none: \"Brak\",\n  arrowhead_arrow: \"Strza\\u0142ka\",\n  arrowhead_bar: \"Kreska\",\n  arrowhead_circle: \"Okr\\u0105g\",\n  arrowhead_circle_outline: \"Okr\\u0105g (obrys)\",\n  arrowhead_triangle: \"Tr\\xF3jk\\u0105t\",\n  arrowhead_triangle_outline: \"Tr\\xF3jk\\u0105t (obrys)\",\n  arrowhead_diamond: \"Romb\",\n  arrowhead_diamond_outline: \"Romb (obrys)\",\n  fontSize: \"Rozmiar tekstu\",\n  fontFamily: \"Kr\\xF3j pisma\",\n  addWatermark: 'Dodaj \"Zrobione w Excalidraw\"',\n  handDrawn: \"Odr\\u0119czny\",\n  normal: \"Normalny\",\n  code: \"Kod\",\n  small: \"Ma\\u0142y\",\n  medium: \"\\u015Aredni\",\n  large: \"Du\\u017Cy\",\n  veryLarge: \"Bardzo du\\u017Cy\",\n  solid: \"Pe\\u0142ne\",\n  hachure: \"Linie\",\n  zigzag: \"Zygzak\",\n  crossHatch: \"Zakre\\u015Blone\",\n  thin: \"Cienkie\",\n  bold: \"Pogrubione\",\n  left: \"Do lewej\",\n  center: \"Do \\u015Brodka\",\n  right: \"Do prawej\",\n  extraBold: \"Ekstra pogrubione\",\n  architect: \"Dok\\u0142adny\",\n  artist: \"Artystyczny\",\n  cartoonist: \"Rysunkowy\",\n  fileTitle: \"Nazwa pliku\",\n  colorPicker: \"Paleta kolor\\xF3w\",\n  canvasColors: \"U\\u017Cywane na p\\u0142\\xF3tnie\",\n  canvasBackground: \"Kolor dokumentu\",\n  drawingCanvas: \"Obszar roboczy\",\n  layers: \"Warstwy\",\n  actions: \"Akcje\",\n  language: \"J\\u0119zyk\",\n  liveCollaboration: \"Wsp\\xF3\\u0142praca w czasie rzeczywistym...\",\n  duplicateSelection: \"Powiel\",\n  untitled: \"Bez tytu\\u0142u\",\n  name: \"Nazwa\",\n  yourName: \"Twoje imi\\u0119\",\n  madeWithExcalidraw: \"Zrobione w Excalidraw\",\n  group: \"Zgrupuj wybrane\",\n  ungroup: \"Rozgrupuj wybrane\",\n  collaborators: \"Wsp\\xF3\\u0142tw\\xF3rcy\",\n  showGrid: \"Poka\\u017C siatk\\u0119\",\n  addToLibrary: \"Dodaj do biblioteki\",\n  removeFromLibrary: \"Usu\\u0144 z biblioteki\",\n  libraryLoadingMessage: \"Wczytywanie biblioteki\\u2026\",\n  libraries: \"Przegl\\u0105daj biblioteki\",\n  loadingScene: \"Wczytywanie sceny\\u2026\",\n  align: \"Wyr\\xF3wnaj\",\n  alignTop: \"Wyr\\xF3wnaj do g\\xF3ry\",\n  alignBottom: \"Wyr\\xF3wnaj do do\\u0142u\",\n  alignLeft: \"Wyr\\xF3wnaj do lewej\",\n  alignRight: \"Wyr\\xF3wnaj do prawej\",\n  centerVertically: \"Wy\\u015Brodkuj w pionie\",\n  centerHorizontally: \"Wy\\u015Brodkuj w poziomie\",\n  distributeHorizontally: \"Roz\\u0142\\xF3\\u017C poziomo\",\n  distributeVertically: \"Roz\\u0142\\xF3\\u017C pionowo\",\n  flipHorizontal: \"Odwr\\xF3\\u0107 w poziomie\",\n  flipVertical: \"Odwr\\xF3\\u0107 w pionie\",\n  viewMode: \"Tryb widoku\",\n  share: \"Udost\\u0119pnij\",\n  showStroke: \"Poka\\u017C pr\\xF3bnik kolor\\xF3w obrysu\",\n  showBackground: \"Poka\\u017C pr\\xF3bnik koloru t\\u0142a\",\n  toggleTheme: \"Prze\\u0142\\u0105cz motyw\",\n  personalLib: \"Biblioteka prywatna\",\n  excalidrawLib: \"Biblioteka Excalidraw\",\n  decreaseFontSize: \"Zmniejsz rozmiar czcionki\",\n  increaseFontSize: \"Zwi\\u0119ksz rozmiar czcionki\",\n  unbindText: \"Od\\u0142\\u0105cz tekst od kontenera\",\n  bindText: \"Po\\u0142\\u0105cz tekst z kontenerem\",\n  createContainerFromText: \"Zawijaj tekst w kontenerze\",\n  link: {\n    edit: \"Edytuj \\u0142\\u0105cze\",\n    editEmbed: \"Edytuj i osad\\u017A link\",\n    create: \"Utw\\xF3rz \\u0142\\u0105cze\",\n    createEmbed: \"Stw\\xF3rz i osad\\u017A\\xA0link\",\n    label: \"\\u0141\\u0105cze\",\n    labelEmbed: \"Podlinkuj i osad\\u017A\",\n    empty: \"Nie ustawiono linku\"\n  },\n  lineEditor: {\n    edit: \"Edytuj lini\\u0119\",\n    exit: \"Wyjd\\u017A z edytora linii\"\n  },\n  elementLock: {\n    lock: \"Zablokuj\",\n    unlock: \"Odblokuj\",\n    lockAll: \"Zablokuj wszystko\",\n    unlockAll: \"Odblokuj wszystko\"\n  },\n  statusPublished: \"Opublikowano\",\n  sidebarLock: \"Panel boczny zawsze otwarty\",\n  selectAllElementsInFrame: \"Zaznacz wszystkie elementy w ramce\",\n  removeAllElementsFromFrame: \"Usu\\u0144 wszystkie elementy z ramki\",\n  eyeDropper: \"Wybierz kolor z p\\u0142\\xF3tna\",\n  textToDiagram: \"Tekst do diagramu\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Nie dodano jeszcze \\u017Cadnych element\\xF3w...\",\n  hint_emptyLibrary: \"Wybierz element na p\\u0142\\xF3tnie, aby go tutaj doda\\u0107, lub zainstaluj bibliotek\\u0119 z poni\\u017Cszego publicznego repozytorium.\",\n  hint_emptyPrivateLibrary: \"Wybierz element, aby doda\\u0107 go tutaj.\"\n};\nvar buttons = {\n  clearReset: \"Wyczy\\u015B\\u0107 dokument i zresetuj kolor dokumentu\",\n  exportJSON: \"Eksportuj do pliku\",\n  exportImage: \"Eksportuj obraz...\",\n  export: \"Zapisz jako...\",\n  copyToClipboard: \"Skopiuj do schowka\",\n  save: \"Zapisz do bie\\u017C\\u0105cego pliku\",\n  saveAs: \"Zapisz jako\",\n  load: \"Otw\\xF3rz\",\n  getShareableLink: \"Udost\\u0119pnij\",\n  close: \"Zamknij\",\n  selectLanguage: \"Wybierz j\\u0119zyk\",\n  scrollBackToContent: \"Wr\\xF3\\u0107 do obszaru roboczego\",\n  zoomIn: \"Powi\\u0119ksz\",\n  zoomOut: \"Pomniejsz\",\n  resetZoom: \"Zresetuj powi\\u0119kszenie\",\n  menu: \"Menu\",\n  done: \"Gotowe\",\n  edit: \"Edytuj\",\n  undo: \"Cofnij\",\n  redo: \"Przywr\\xF3\\u0107\",\n  resetLibrary: \"Resetuj bibliotek\\u0119\",\n  createNewRoom: \"Utw\\xF3rz nowy pok\\xF3j\",\n  fullScreen: \"Pe\\u0142ny ekran\",\n  darkMode: \"Ciemny motyw\",\n  lightMode: \"Jasny motyw\",\n  zenMode: \"Tryb Zen\",\n  objectsSnapMode: \"Przyci\\u0105ganie do obiekt\\xF3w\",\n  exitZenMode: \"Wyjd\\u017A z trybu Zen\",\n  cancel: \"Anuluj\",\n  clear: \"Wyczy\\u015B\\u0107\",\n  remove: \"Usu\\u0144\",\n  embed: \"Prze\\u0142\\u0105cz osadzenie\",\n  publishLibrary: \"Opublikuj\",\n  submit: \"Prze\\u015Blij\",\n  confirm: \"Zatwierd\\u017A\",\n  embeddableInteractionButton: \"Kliknij, aby wej\\u015B\\u0107 w interakcj\\u0119\"\n};\nvar alerts = {\n  clearReset: \"To spowoduje usuni\\u0119cie wszystkiego z dokumentu. Czy chcesz kontynuowa\\u0107?\",\n  couldNotCreateShareableLink: \"Wyst\\u0105pi\\u0142 b\\u0142\\u0105d przy generowaniu linka do udost\\u0119pniania.\",\n  couldNotCreateShareableLinkTooBig: \"Nie mo\\u017Cna utworzy\\u0107 linku do udost\\u0119pnienia: scena jest za du\\u017Ca\",\n  couldNotLoadInvalidFile: \"Nie uda\\u0142o si\\u0119 otworzy\\u0107 pliku. Wybrany plik jest nieprawid\\u0142owy.\",\n  importBackendFailed: \"Wyst\\u0105pi\\u0142 b\\u0142\\u0105d podczas importowania pliku.\",\n  cannotExportEmptyCanvas: \"Najpierw musisz co\\u015B narysowa\\u0107, aby zapisa\\u0107 dokument.\",\n  couldNotCopyToClipboard: \"Nie uda\\u0142o si\\u0119 skopiowa\\u0107 do schowka.\",\n  decryptFailed: \"Nie uda\\u0142o si\\u0119 odszyfrowa\\u0107 danych.\",\n  uploadedSecurly: \"By zapewni\\u0107 Ci prywatno\\u015B\\u0107, udost\\u0119pnianie projektu jest zabezpieczone szyfrowaniem end-to-end, co oznacza, \\u017Ce poza tob\\u0105 i osob\\u0105 z kt\\xF3r\\u0105 podzielisz si\\u0119 linkiem, nikt nie ma dost\\u0119pu do tego co udost\\u0119pniasz.\",\n  loadSceneOverridePrompt: \"Wczytanie zewn\\u0119trznego rysunku zast\\u0105pi istniej\\u0105c\\u0105 zawarto\\u015B\\u0107. Czy chcesz kontynuowa\\u0107?\",\n  collabStopOverridePrompt: \"Zatrzymanie sesji nadpisze poprzedni, zapisany lokalnie rysunek. Czy jeste\\u015B pewien?\\n\\n(Je\\u015Bli chcesz zachowa\\u0107 sw\\xF3j lokalny rysunek, po prostu zamknij zak\\u0142adk\\u0119 przegl\\u0105darki.)\",\n  errorAddingToLibrary: \"Nie uda\\u0142o si\\u0119 doda\\u0107 elementu do biblioteki\",\n  errorRemovingFromLibrary: \"Nie uda\\u0142o si\\u0119 usun\\u0105\\u0107 elementu z biblioteki\",\n  confirmAddLibrary: \"To doda {{numShapes}} kszta\\u0142t\\xF3w do twojej biblioteki. Jeste\\u015B pewien?\",\n  imageDoesNotContainScene: \"Ten obraz nie zawiera \\u017Cadnych informacji o scenie. Czy w\\u0142\\u0105czy\\u0142e\\u015B osadzanie sceny podczas eksportu?\",\n  cannotRestoreFromImage: \"Scena nie mog\\u0142a zosta\\u0107 przywr\\xF3cona z pliku obrazu\",\n  invalidSceneUrl: \"Nie uda\\u0142o si\\u0119 zaimportowa\\u0107 sceny z podanego adresu URL. Jest ona wadliwa lub nie zawiera poprawnych danych Excalidraw w formacie JSON.\",\n  resetLibrary: \"To wyczy\\u015Bci twoj\\u0105 bibliotek\\u0119. Jeste\\u015B pewien?\",\n  removeItemsFromsLibrary: \"Usun\\u0105\\u0107 {{count}} element(\\xF3w) z biblioteki?\",\n  invalidEncryptionKey: \"Klucz szyfrowania musi sk\\u0142ada\\u0107 si\\u0119 z 22 znak\\xF3w. Wsp\\xF3\\u0142praca na \\u017Cywo jest wy\\u0142\\u0105czona.\",\n  collabOfflineWarning: \"Brak po\\u0142\\u0105czenia z Internetem.\\nTwoje zmiany nie zostan\\u0105 zapisane!\"\n};\nvar errors = {\n  unsupportedFileType: \"Nieobs\\u0142ugiwany typ pliku.\",\n  imageInsertError: \"Nie uda\\u0142o si\\u0119 wstawi\\u0107 obrazu. Spr\\xF3buj ponownie p\\xF3\\u017Aniej...\",\n  fileTooBig: \"Plik jest zbyt du\\u017Cy. Maksymalny dozwolony rozmiar to {{maxSize}}.\",\n  svgImageInsertError: \"Nie uda\\u0142o si\\u0119 wstawi\\u0107 obrazu SVG. Znacznik SVG wygl\\u0105da na nieprawid\\u0142owy.\",\n  failedToFetchImage: \"Nie uda\\u0142o si\\u0119\\xA0za\\u0142adowa\\u0107 obrazu.\",\n  invalidSVGString: \"Nieprawid\\u0142owy SVG.\",\n  cannotResolveCollabServer: \"Nie mo\\u017Cna po\\u0142\\u0105czy\\u0107 si\\u0119 z serwerem wsp\\xF3\\u0142pracy w czasie rzeczywistym. Prosz\\u0119 od\\u015Bwie\\u017Cy\\u0107 stron\\u0119 i spr\\xF3bowa\\u0107 ponownie.\",\n  importLibraryError: \"Wyst\\u0105pi\\u0142 b\\u0142\\u0105d w trakcie \\u0142adowania biblioteki\",\n  collabSaveFailed: \"Nie uda\\u0142o si\\u0119 zapisa\\u0107 w bazie danych. Je\\u015Bli problemy nie ust\\u0105pi\\u0105, zapisz plik lokalnie, aby nie utraci\\u0107 swojej pracy.\",\n  collabSaveFailed_sizeExceeded: \"Nie uda\\u0142o si\\u0119 zapisa\\u0107 w bazie danych \\u2014 dokument jest za du\\u017Cy. Zapisz plik lokalnie, aby nie utraci\\u0107 swojej pracy.\",\n  imageToolNotSupported: \"Dodawanie obraz\\xF3w jest wy\\u0142\\u0105czone.\",\n  brave_measure_text_error: {\n    line1: \"Wygl\\u0105da na to, \\u017Ce u\\u017Cywasz przegl\\u0105darki Brave z w\\u0142\\u0105czonym ustawieniem <bold>Agressively Block Fingerprinting</bold>.\",\n    line2: \"Mo\\u017Ce to doprowadzi\\u0107 do z\\u0142amania <bold>element\\xF3w tekstu</bold> na rysunkach.\",\n    line3: \"Zdecydowanie zalecamy wy\\u0142\\u0105czenie tego ustawienia. Mo\\u017Cesz wykona\\u0107 <link>te kroki</link>, aby to zrobi\\u0107.\",\n    line4: \"Je\\u015Bli wy\\u0142\\u0105czenie tego ustawienia nie naprawia wy\\u015Bwietlania element\\xF3w tekstowych, zg\\u0142o\\u015B <issueLink>problem</issueLink> na naszym GitHubie lub napisz do nas na <discordLink>Discordzie</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Elementy osadzone nie mog\\u0105 zosta\\u0107 dodane do biblioteki.\",\n    iframe: \"Elementy IFrame nie mog\\u0105 zosta\\u0107 dodane do biblioteki.\",\n    image: \"Dodawania obraz\\xF3w do biblioteki nadejdzie wkr\\xF3tce!\"\n  },\n  asyncPasteFailedOnRead: \"Nie uda\\u0142o si\\u0119 wklei\\u0107 (nie uda\\u0142o si\\u0119 odczyta\\u0107 ze schowka systemowego).\",\n  asyncPasteFailedOnParse: \"Nie uda\\u0142o si\\u0119 wklei\\u0107.\",\n  copyToSystemClipboardFailed: \"Nie uda\\u0142o si\\u0119\\xA0skopiowa\\u0107 do schowka.\"\n};\nvar toolBar = {\n  selection: \"Zaznaczenie\",\n  image: \"Wstaw obraz\",\n  rectangle: \"Prostok\\u0105t\",\n  diamond: \"Romb\",\n  ellipse: \"Elipsa\",\n  arrow: \"Strza\\u0142ka\",\n  line: \"Linia\",\n  freedraw: \"Rysuj\",\n  text: \"Tekst\",\n  library: \"Biblioteka\",\n  lock: \"Zablokuj wybrane narz\\u0119dzie\",\n  penMode: \"Tryb pi\\xF3ra \\u2014 zapobiegaj dotkni\\u0119ciom\",\n  link: \"Dodaj/aktualizuj link dla wybranego kszta\\u0142tu\",\n  eraser: \"Gumka\",\n  frame: \"Ramka\",\n  magicframe: \"Wireframe do kodu\",\n  embeddable: \"Osadzenie z internetu\",\n  laser: \"Wska\\u017Anik laserowy\",\n  hand: \"R\\u0119ka (narz\\u0119dzie do przesuwania)\",\n  extraTools: \"Wi\\u0119cej narz\\u0119dzi\",\n  mermaidToExcalidraw: \"Konwertuj diagram Mermaid do Excalidraw\",\n  magicSettings: \"Ustawienia AI\"\n};\nvar headings = {\n  canvasActions: \"Narz\\u0119dzia\",\n  selectedShapeActions: \"Wybrane narz\\u0119dzie\",\n  shapes: \"Kszta\\u0142ty\"\n};\nvar hints = {\n  canvasPanning: \"Aby przesun\\u0105\\u0107 p\\u0142\\xF3tno, przytrzymaj k\\xF3\\u0142ko myszy lub spacj\\u0119 podczas przeci\\u0105gania, albo u\\u017Cyj narz\\u0119dzia r\\u0119ki\",\n  linearElement: \"Naci\\u015Bnij, aby zrobi\\u0107 punkt, przeci\\u0105gnij, aby narysowa\\u0107 lini\\u0119\",\n  freeDraw: \"Naci\\u015Bnij i przeci\\u0105gnij by rysowa\\u0107, pu\\u015B\\u0107 kiedy sko\\u0144czysz\",\n  text: \"Wskaz\\xF3wka: mo\\u017Cesz r\\xF3wnie\\u017C doda\\u0107 tekst klikaj\\u0105c dwukrotnie gdziekolwiek za pomoc\\u0105 narz\\u0119dzia zaznaczania\",\n  embeddable: \"Kliknij i przeci\\u0105gnij, aby stworzy\\u0107\\xA0osadzenie strony\",\n  text_selected: \"Kliknij dwukrotnie lub naci\\u015Bnij ENTER, aby edytowa\\u0107 tekst\",\n  text_editing: \"Naci\\u015Bnij Escape lub Ctrl (Cmd w macOS) + ENTER, aby zako\\u0144czy\\u0107 edycj\\u0119\",\n  linearElementMulti: \"Aby zako\\u0144czy\\u0107 krzyw\\u0105, ponownie kliknij w ostatni punkt, b\\u0105d\\u017A naci\\u015Bnij Esc albo Enter\",\n  lockAngle: \"Mo\\u017Cesz ograniczy\\u0107 k\\u0105t trzymaj\\u0105c SHIFT\",\n  resize: \"Mo\\u017Cesz zachowa\\u0107 proporcj\\u0119 trzymaj\\u0105\\u0107 wcisni\\u0119ty SHIFT, przytrzymaj ALT by zmieni\\u0107 rozmiar wzgl\\u0119dem \\u015Brodka\",\n  resizeImage: \"Mo\\u017Cesz zmieni\\u0107 rozmiar swobodnie trzymaj\\u0105c SHIFT,\\nprzytrzymaj ALT, aby przeskalowa\\u0107 wzgl\\u0119dem \\u015Brodka obiektu\",\n  rotate: \"Mo\\u017Cesz obraca\\u0107\\xA0element w r\\xF3wnych odst\\u0119pach trzymaj\\u0105c wci\\u015Bni\\u0119ty SHIFT\",\n  lineEditor_info: \"Przytrzymaj CtrlOrCmd i kliknij dwukrotnie lub naci\\u015Bnij CtrlOrCmd + Enter, aby edytowa\\u0107 punkty\",\n  lineEditor_pointSelected: \"Naci\\u015Bnij przycisk Delete, aby usun\\u0105\\u0107 punkt. Ctrl/Cmd+D, aby go zduplikowa\\u0107. Przeci\\u0105gnij, aby go przenie\\u015B\\u0107\",\n  lineEditor_nothingSelected: \"Wybierz punkt do edycji (przytrzymaj SHIFT, aby wybra\\u0107 wiele),\\nlub przytrzymaj Alt i kliknij, aby doda\\u0107 nowe punkty\",\n  placeImage: \"Kliknij, aby umie\\u015Bci\\u0107 obraz, lub kliknij i przeci\\u0105gnij, aby ustawi\\u0107 jego rozmiar r\\u0119cznie\",\n  publishLibrary: \"Opublikuj w\\u0142asn\\u0105 bibliotek\\u0119\",\n  bindTextToElement: \"Wci\\u015Bnij enter, aby doda\\u0107 tekst\",\n  deepBoxSelect: \"Przytrzymaj CtrlOrCmd, aby wybra\\u0107 w obr\\u0119bie grupy i unikn\\u0105\\u0107 przeci\\u0105gania\",\n  eraserRevert: \"Przytrzymaj Alt, aby przywr\\xF3ci\\u0107 elementy oznaczone do usuni\\u0119cia\",\n  firefox_clipboard_write: 'Ta funkcja mo\\u017Ce by\\u0107 w\\u0142\\u0105czona poprzez ustawienie flagi \"dom.events.asyncClipboard.clipboardItem\" na \"true\". Aby zmieni\\u0107 flagi przegl\\u0105darki w Firefox, odwied\\u017A stron\\u0119 \"about:config\".',\n  disableSnapping: \"Przytrzymaj Ctrl lub Cmd, aby wy\\u0142\\u0105czy\\u0107\\xA0przyci\\u0105ganie\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Nie mo\\u017Cna wy\\u015Bwietli\\u0107 podgl\\u0105du\",\n  canvasTooBig: \"Obszar roboczy mo\\u017Ce by\\u0107 za du\\u017Cy.\",\n  canvasTooBigTip: \"Wskaz\\xF3wka: spr\\xF3buj nieco zbli\\u017Cy\\u0107 najdalej wysuni\\u0119te elementy.\"\n};\nvar errorSplash = {\n  headingMain: \"Wyst\\u0105pi\\u0142 b\\u0142\\u0105d. Spr\\xF3buj <button>od\\u015Bwie\\u017Cy\\u0107 stron\\u0119.</button>\",\n  clearCanvasMessage: \"Je\\u015Bli od\\u015Bwie\\u017Cenie strony nie zadzia\\u0142a\\u0142o, spr\\xF3buj <button>usun\\u0105\\u0107 wszystko z dokumentu.</button>\",\n  clearCanvasCaveat: \" Pami\\u0119taj tylko, \\u017Ce spowoduje to utrat\\u0119 ca\\u0142ej twojej pracy \",\n  trackedToSentry: \"B\\u0142\\u0105d o identyfikatorze {{eventId}} zosta\\u0142 zaraportowany w naszym systemie.\",\n  openIssueMessage: \"Szanujemy twoj\\u0105 prywatno\\u015B\\u0107 i raport nie zawiera\\u0142 \\u017Cadnych danych dotycz\\u0105cych tego nad czym pracowa\\u0142e\\u015B, natomiast je\\u017Celi jeste\\u015B w stanie podzieli\\u0107 si\\u0119 tym nad czym pracowa\\u0142e\\u015B, prosimy o dodatkowy raport poprzez <button>nasze narz\\u0119dzie do raportowania b\\u0142\\u0119d\\xF3w.</button> Prosimy o do\\u0142\\u0105czenie poni\\u017Cszej informacji poprzez skopiowanie jej i umieszczenie jej w zg\\u0142oszeniu na portalu GitHub.\",\n  sceneContent: \"Zawarto\\u015B\\u0107 dokumentu:\"\n};\nvar roomDialog = {\n  desc_intro: \"B\\u0119dziesz w stanie pracowa\\u0107\\xA0wraz z osobami kt\\xF3re zaprosisz do wsp\\xF3\\u0142pracy.\",\n  desc_privacy: \"By zapewni\\u0107 Ci prywatno\\u015B\\u0107, sesja wsp\\xF3\\u0142pracy na \\u017Cywo jest zabezpieczona szyfrowaniem end-to-end, co oznacza, \\u017Ce poza tob\\u0105 i osobami z kt\\xF3rymi podzielisz si\\u0119 linkiem, nikt nie ma dost\\u0119pu do tego co b\\u0119dziecie tworzy\\u0107.\",\n  button_startSession: \"Rozpocznij sesj\\u0119\",\n  button_stopSession: \"Zako\\u0144cz sesj\\u0119\",\n  desc_inProgressIntro: \"Sesja wsp\\xF3\\u0142pracy na \\u017Cywo w\\u0142a\\u015Bnie si\\u0119 rozpocz\\u0119\\u0142a.\",\n  desc_shareLink: \"Udost\\u0119pnij ten link osobom, z kt\\xF3rymi chcesz wsp\\xF3\\u0142pracowa\\u0107:\",\n  desc_exitSession: \"Zako\\u0144czenie sesji spowoduje od\\u0142\\u0105czenie ciebie od pokoju, ale nadal b\\u0119dziesz m\\xF3g\\u0142 lokalnie kontynuowa\\u0107 prac\\u0119. Zauwa\\u017C, \\u017Ce osoby z kt\\xF3rymi wsp\\xF3\\u0142pracowa\\u0142e\\u015B nadal b\\u0119d\\u0105 mog\\u0142y wsp\\xF3\\u0142pracowa\\u0107.\",\n  shareTitle: \"Do\\u0142\\u0105cz do sesji wsp\\xF3\\u0142pracy na \\u017Cywo w Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Wyst\\u0105pi\\u0142 b\\u0142\\u0105d\"\n};\nvar exportDialog = {\n  disk_title: \"Zapisz na dysku\",\n  disk_details: \"Eksportuj dane sceny do pliku, z kt\\xF3rego mo\\u017Cesz importowa\\u0107 p\\xF3\\u017Aniej.\",\n  disk_button: \"Zapisz do pliku\",\n  link_title: \"Link do udost\\u0119pnienia\",\n  link_details: \"Eksportuj jako link tylko do odczytu.\",\n  link_button: \"Wygeneruj link\",\n  excalidrawplus_description: \"Zapisz scen\\u0119 do swojego obszaru roboczego Excalidraw+.\",\n  excalidrawplus_button: \"Eksportuj\",\n  excalidrawplus_exportError: \"W tej chwili nie mo\\u017Cna wyeksportowa\\u0107 do Excalidraw+...\"\n};\nvar helpDialog = {\n  blog: \"Przeczytaj na naszym blogu\",\n  click: \"klikni\\u0119cie\",\n  deepSelect: \"Wyb\\xF3r w obr\\u0119bie grupy\",\n  deepBoxSelect: \"Wyb\\xF3r w obr\\u0119bie grupy i unikanie przeci\\u0105gania\",\n  curvedArrow: \"Zakrzywiona strza\\u0142ka\",\n  curvedLine: \"Zakrzywiona linia\",\n  documentation: \"Dokumentacja\",\n  doubleClick: \"podw\\xF3jne klikni\\u0119cie\",\n  drag: \"przeci\\u0105gnij\",\n  editor: \"Edytor\",\n  editLineArrowPoints: \"Edytuj punkty linii/strza\\u0142ki\",\n  editText: \"Edytuj tekst/dodaj etykiet\\u0119\",\n  github: \"Znalaz\\u0142e\\u015B problem? Prze\\u015Blij\",\n  howto: \"Skorzystaj z instrukcji\",\n  or: \"lub\",\n  preventBinding: \"Zapobiegaj wi\\u0105zaniu strza\\u0142ek\",\n  tools: \"Narz\\u0119dzia\",\n  shortcuts: \"Skr\\xF3ty klawiszowe\",\n  textFinish: \"Zako\\u0144cz edycj\\u0119 (edytor tekstu)\",\n  textNewLine: \"Dodaj nowy wiersz (edytor tekstu)\",\n  title: \"Pomoc\",\n  view: \"Widok\",\n  zoomToFit: \"Powi\\u0119ksz, aby wy\\u015Bwietli\\u0107 wszystkie elementy\",\n  zoomToSelection: \"Przybli\\u017C do zaznaczenia\",\n  toggleElementLock: \"Zablokuj/odblokuj zaznaczenie\",\n  movePageUpDown: \"Przesu\\u0144 stron\\u0119 w g\\xF3r\\u0119/w d\\xF3\\u0142\",\n  movePageLeftRight: \"Przenie\\u015B stron\\u0119 w lewo/prawo\"\n};\nvar clearCanvasDialog = {\n  title: \"Wyczy\\u015B\\u0107 p\\u0142\\xF3tno\"\n};\nvar publishDialog = {\n  title: \"Opublikuj bibliotek\\u0119\",\n  itemName: \"Nazwa elementu\",\n  authorName: \"Nazwa autora\",\n  githubUsername: \"Nazwa u\\u017Cytkownika na GitHubie\",\n  twitterUsername: \"Nazwa u\\u017Cytkownika Twitter\",\n  libraryName: \"Nazwa biblioteki\",\n  libraryDesc: \"Opis biblioteki\",\n  website: \"Strona internetowa\",\n  placeholder: {\n    authorName: \"Twoje imi\\u0119 lub nazwa u\\u017Cytkownika\",\n    libraryName: \"Nazwa twojej biblioteki\",\n    libraryDesc: \"Opis twojej biblioteki, aby pom\\xF3c innym zrozumie\\u0107 jej dzia\\u0142anie\",\n    githubHandle: \"Uchwyt GitHub (opcjonalny), dzi\\u0119ki czemu mo\\u017Cesz edytowa\\u0107 bibliotek\\u0119 po przes\\u0142aniu do sprawdzenia\",\n    twitterHandle: \"Nazwa u\\u017Cytkownika w serwisie Twitter (opcjonalna), aby wiedzie\\u0107 kogo oznaczy\\u0107 przy promowaniu na Twitterze\",\n    website: \"Link do Twojej osobistej strony internetowej lub gdzie indziej (opcjonalnie)\"\n  },\n  errors: {\n    required: \"Wymagane\",\n    website: \"Wprowad\\u017A prawid\\u0142owy adres URL\"\n  },\n  noteDescription: \"<link></link>dla innych os\\xF3b do wykorzystania w swoich rysunkach.\",\n  noteGuidelines: \"Biblioteka musi by\\u0107 najpierw zatwierdzona r\\u0119cznie. Przeczytaj <link>wytyczne</link>\",\n  noteLicense: \"Wysy\\u0142aj\\u0105c zgadzasz si\\u0119, \\u017Ce biblioteka zostanie opublikowana pod <link>Licencja MIT, </link>w skr\\xF3cie, ka\\u017Cdy mo\\u017Ce z nich korzysta\\u0107 bez ogranicze\\u0144.\",\n  noteItems: \"Ka\\u017Cdy element biblioteki musi mie\\u0107 w\\u0142asn\\u0105 nazw\\u0119, aby by\\u0142 filtrowalny. Uwzgl\\u0119dnione zostan\\u0105 nast\\u0119puj\\u0105ce elementy biblioteki:\",\n  atleastOneLibItem: \"Prosz\\u0119 wybra\\u0107 co najmniej jeden element biblioteki, by rozpocz\\u0105\\u0107\",\n  republishWarning: \"Uwaga: niekt\\xF3re z wybranych element\\xF3w s\\u0105 oznaczone jako ju\\u017C opublikowane/wys\\u0142ane. Powiniene\\u015B ponownie przes\\u0142a\\u0107 elementy tylko wtedy, gdy aktualizujesz istniej\\u0105c\\u0105 bibliotek\\u0119 lub zg\\u0142oszenie.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteka zosta\\u0142a przes\\u0142ana\",\n  content: \"Dzi\\u0119kujemy {{authorName}}. Twoja biblioteka zosta\\u0142a przes\\u0142ana do sprawdzenia. Mo\\u017Cesz \\u015Bledzi\\u0107 jej stan<link>tutaj</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Zresetuj Bibliotek\\u0119\",\n  removeItemsFromLib: \"Usu\\u0144 wybrane elementy z biblioteki\"\n};\nvar imageExportDialog = {\n  header: \"Eksportuj obraz\",\n  label: {\n    withBackground: \"T\\u0142o\",\n    onlySelected: \"Tylko wybrane\",\n    darkMode: \"Tryb ciemny\",\n    embedScene: \"Osad\\u017A scen\\u0119\",\n    scale: \"Skala\",\n    padding: \"Dope\\u0142nienie\"\n  },\n  tooltip: {\n    embedScene: \"Dane sceny zostan\\u0105 zapisane w eksportowanym pliku PNG/SVG tak, aby scena mog\\u0142a zosta\\u0107 z niego przywr\\xF3cona.\\nZwi\\u0119kszy to rozmiar eksportowanego pliku.\"\n  },\n  title: {\n    exportToPng: \"Zapisz jako PNG\",\n    exportToSvg: \"Zapisz jako SVG\",\n    copyPngToClipboard: \"Skopiuj do schowka jako PNG\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Skopiuj do schowka\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Twoje rysunki s\\u0105 zabezpieczone szyfrowaniem end-to-end, tak wi\\u0119c nawet w Excalidraw nie jeste\\u015Bmy w stanie zobaczy\\u0107 tego co tworzysz.\",\n  link: \"Wpis na blogu dotycz\\u0105cy szyfrowania end-to-end w Excalidraw\"\n};\nvar stats = {\n  angle: \"K\\u0105t\",\n  element: \"Element\",\n  elements: \"Elementy\",\n  height: \"Wysoko\\u015B\\u0107\",\n  scene: \"Scena\",\n  selected: \"Zaznaczenie\",\n  storage: \"Pami\\u0119\\u0107\",\n  title: \"Statystyki dla nerd\\xF3w\",\n  total: \"\\u0141\\u0105cznie\",\n  version: \"Wersja\",\n  versionCopy: \"Kliknij, aby skopiowa\\u0107\",\n  versionNotAvailable: \"Wersja niedost\\u0119pna\",\n  width: \"Szeroko\\u015B\\u0107\"\n};\nvar toast = {\n  addedToLibrary: \"Dodano do biblioteki\",\n  copyStyles: \"Skopiowano style.\",\n  copyToClipboard: \"Skopiowano do schowka.\",\n  copyToClipboardAsPng: \"Skopiowano {{exportSelection}} do schowka jako PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Zapisano plik.\",\n  fileSavedToFilename: \"Zapisano jako {filename}\",\n  canvas: \"p\\u0142\\xF3tno\",\n  selection: \"zaznaczenie\",\n  pasteAsSingleElement: \"U\\u017Cyj {{shortcut}}, aby wklei\\u0107 jako pojedynczy element,\\nlub wklej do istniej\\u0105cego edytora tekstu\",\n  unableToEmbed: \"Osadzenie tego linku jest obecnie niedozwolone. Zg\\u0142o\\u015B propozycj\\u0119 na portalu GitHub, aby doda\\u0107\\xA0go do listy dozwolonych wyj\\u0105tk\\xF3w\",\n  unrecognizedLinkFormat: 'Osadzony link ma niew\\u0142a\\u015Bciwy format. Spr\\xF3buj wklei\\u0107 ca\\u0142\\u0105\\xA0zawarto\\u015B\\u0107 pola \"embed\" z oryginalnej strony.'\n};\nvar colors = {\n  transparent: \"Przezroczysty\",\n  black: \"Czarny\",\n  white: \"Bia\\u0142y\",\n  red: \"Czerwony\",\n  pink: \"R\\xF3\\u017Cowy\",\n  grape: \"Winogronowy\",\n  violet: \"Fioletowy\",\n  gray: \"Szary\",\n  blue: \"Niebieski\",\n  cyan: \"Cyjanowy\",\n  teal: \"Turkusowy\",\n  green: \"Zielony\",\n  yellow: \"\\u017B\\xF3\\u0142ty\",\n  orange: \"Pomara\\u0144czowy\",\n  bronze: \"Br\\u0105zowy\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Wszystkie dane s\\u0105 zapisywane lokalnie w przegl\\u0105darce.\",\n    center_heading_plus: \"Czy zamiast tego chcesz przej\\u015B\\u0107 do Excalidraw+?\",\n    menuHint: \"Eksportuj, preferencje, j\\u0119zyki...\"\n  },\n  defaults: {\n    menuHint: \"Eksportuj, preferencje i wi\\u0119cej...\",\n    center_heading: \"Schematy uproszczone.\",\n    toolbarHint: \"Wybierz narz\\u0119dzie i zacznij rysowa\\u0107!\",\n    helpHint: \"Skr\\xF3ty klawiaturowe i pomoc\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Najcz\\u0119\\u015Bciej u\\u017Cywane kolory\",\n  colors: \"Kolory\",\n  shades: \"Odcienie\",\n  hexCode: \"Kod HEX\",\n  noShades: \"Brak dost\\u0119pnych odcieni dla tego koloru\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Eksportuj jako obraz\",\n      button: \"Eksportuj jako obraz\",\n      description: \"Eksportuj zawarto\\u015B\\u0107 sceny jako obraz z mo\\u017Cliwo\\u015Bci\\u0105\\xA0importowania.\"\n    },\n    saveToDisk: {\n      title: \"Zapisz na dysku\",\n      button: \"Zapisz na dysku\",\n      description: \"Eksportuj zawarto\\u015B\\u0107 sceny jako plik z mo\\u017Cliwo\\u015Bci\\u0105\\xA0importowania.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Eksportuj do Excalidraw+\",\n      description: \"Zapisz scen\\u0119 do\\xA0swojego obszaru roboczego Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Wczytaj z pliku\",\n      button: \"Wczytaj z pliku\",\n      description: \"Wczytanie z pliku <bold>nadpisze istniej\\u0105c\\u0105 zawarto\\u015B\\u0107</bold>.<br></br>Mo\\u017Cesz najpierw utworzy\\u0107 kopi\\u0119 zapasow\\u0105 swojego rysunku, u\\u017Cywaj\\u0105c jednej z poni\\u017Cszych opcji.\"\n    },\n    shareableLink: {\n      title: \"Wczytaj z linku\",\n      button: \"Nadpisz moj\\u0105\\xA0zawarto\\u015B\\u0107\",\n      description: \"Wczytanie zewn\\u0119trznego pliku <bold>nadpisze istniej\\u0105c\\u0105 zawarto\\u015B\\u0107</bold>.<br></br>Mo\\u017Cesz najpierw utworzy\\u0107 kopi\\u0119 zapasow\\u0105 swojego rysunku, u\\u017Cywaj\\u0105c jednej z poni\\u017Cszych opcji.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Konwertuj diagram Mermaid do Excalidraw\",\n  button: \"Wstaw\",\n  description: \"Obecnie wspierane s\\u0105 jedynie <flowchartLink>proste grafy</flowchartLink>, <sequenceLink>sekwencje</sequenceLink> i <classLink>diagramy klas</classLink>. Pozosta\\u0142e typy b\\u0119d\\u0105\\xA0wy\\u015Bwietlane jako obrazy w Excalidraw.\",\n  syntax: \"Sk\\u0142adnia diagram\\xF3w Mermaid\",\n  preview: \"Podgl\\u0105d\"\n};\nvar pl_PL_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=pl-PL-YJHOWAAW.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/pl-PL-YJHOWAAW.js\n"));

/***/ })

}]);