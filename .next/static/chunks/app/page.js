/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./components/ArchitectureWorkspace.tsx":
/*!**********************************************!*\
  !*** ./components/ArchitectureWorkspace.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArchitectureWorkspace)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(app-pages-browser)/./components/ChatInterface.tsx\");\n/* harmony import */ var _ExcalidrawCanvas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ExcalidrawCanvas */ \"(app-pages-browser)/./components/ExcalidrawCanvas.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ArchitectureWorkspace() {\n    _s();\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: Date.now().toString(),\n        messages: [],\n        current_diagram: null,\n        created_at: new Date(),\n        updated_at: new Date()\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Save session to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArchitectureWorkspace.useEffect\": ()=>{\n            localStorage.setItem('architecture-session', JSON.stringify(session));\n        }\n    }[\"ArchitectureWorkspace.useEffect\"], [\n        session\n    ]);\n    // Load session from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArchitectureWorkspace.useEffect\": ()=>{\n            const savedSession = localStorage.getItem('architecture-session');\n            if (savedSession) {\n                try {\n                    const parsed = JSON.parse(savedSession);\n                    // Convert date strings back to Date objects\n                    parsed.created_at = new Date(parsed.created_at);\n                    parsed.updated_at = new Date(parsed.updated_at);\n                    parsed.messages = parsed.messages.map({\n                        \"ArchitectureWorkspace.useEffect\": (msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            })\n                    }[\"ArchitectureWorkspace.useEffect\"]);\n                    setSession(parsed);\n                } catch (error) {\n                    console.error('Error loading saved session:', error);\n                }\n            }\n        }\n    }[\"ArchitectureWorkspace.useEffect\"], []);\n    const handleSessionUpdate = (updatedSession)=>{\n        setSession(updatedSession);\n    };\n    const handleDiagramUpdate = (diagramData)=>{\n        setSession((prev)=>({\n                ...prev,\n                current_diagram: diagramData,\n                updated_at: new Date()\n            }));\n    };\n    const handleDiagramChange = (elements, appState)=>{\n        // Update the current diagram when user manually edits\n        const updatedDiagram = {\n            ...session.current_diagram,\n            elements,\n            appState\n        };\n        setSession((prev)=>({\n                ...prev,\n                current_diagram: updatedDiagram,\n                updated_at: new Date()\n            }));\n    };\n    const handleNewSession = ()=>{\n        const newSession = {\n            id: Date.now().toString(),\n            messages: [],\n            current_diagram: null,\n            created_at: new Date(),\n            updated_at: new Date()\n        };\n        setSession(newSession);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"architecture-workspace\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"workspace-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"aws-logo\",\n                                        children: \"AWS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Architecture Designer\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Chat with Claude AI to create and iterate on architecture diagrams\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-actions\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleNewSession,\n                            className: \"new-session-btn\",\n                            children: \"✨ New Session\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"workspace-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-panel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            session: session,\n                            onSessionUpdate: handleSessionUpdate,\n                            onDiagramUpdate: handleDiagramUpdate,\n                            isLoading: isLoading,\n                            setIsLoading: setIsLoading\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"diagram-panel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExcalidrawCanvas__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            diagramData: session.current_diagram,\n                            onDiagramChange: handleDiagramChange\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(ArchitectureWorkspace, \"ib9Zpp82lhSY0PWFXXRdcAJTnw0=\");\n_c = ArchitectureWorkspace;\nvar _c;\n$RefreshReg$(_c, \"ArchitectureWorkspace\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ArchitectureWorkspace.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ChatInput.tsx":
/*!**********************************!*\
  !*** ./components/ChatInput.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ChatInput(param) {\n    let { onSendMessage, disabled = false } = param;\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSend = ()=>{\n        if (message.trim() && !disabled) {\n            onSendMessage(message);\n            setMessage('');\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"chat-input\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"input-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: disabled ? \"Generating diagram...\" : \"Describe your architecture or ask for changes...\",\n                        disabled: disabled,\n                        rows: 3,\n                        className: \"message-input\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSend,\n                        disabled: disabled || !message.trim(),\n                        className: \"send-button\",\n                        children: disabled ? '⏳' : '📤'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"input-hints\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: '\\uD83D\\uDCA1 Try: \"Add a cache layer\" or \"Replace with microservices\"'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"⌨️ Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInput, \"l8KXAebGu4sZHsyCIQX7P8si41w=\");\n_c = ChatInput;\nvar _c;\n$RefreshReg$(_c, \"ChatInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvQ2hhdElucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFZ0Q7QUFPakMsU0FBU0MsVUFBVSxLQUFtRDtRQUFuRCxFQUFFQyxhQUFhLEVBQUVDLFdBQVcsS0FBSyxFQUFrQixHQUFuRDs7SUFDaEMsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUdMLCtDQUFRQSxDQUFDO0lBRXZDLE1BQU1NLGFBQWE7UUFDakIsSUFBSUYsUUFBUUcsSUFBSSxNQUFNLENBQUNKLFVBQVU7WUFDL0JELGNBQWNFO1lBQ2RDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTUcsaUJBQWlCLENBQUNDO1FBQ3RCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtZQUNwQ0YsRUFBRUcsY0FBYztZQUNoQk47UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUNPO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUNDQyxPQUFPWjt3QkFDUGEsVUFBVSxDQUFDUixJQUFNSixXQUFXSSxFQUFFUyxNQUFNLENBQUNGLEtBQUs7d0JBQzFDRyxZQUFZWDt3QkFDWlksYUFBYWpCLFdBQVcsMEJBQTBCO3dCQUNsREEsVUFBVUE7d0JBQ1ZrQixNQUFNO3dCQUNOUCxXQUFVOzs7Ozs7a0NBRVosOERBQUNRO3dCQUNDQyxTQUFTakI7d0JBQ1RILFVBQVVBLFlBQVksQ0FBQ0MsUUFBUUcsSUFBSTt3QkFDbkNPLFdBQVU7a0NBRVRYLFdBQVcsTUFBTTs7Ozs7Ozs7Ozs7OzBCQUl0Qiw4REFBQ1U7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDVTtrQ0FBSzs7Ozs7O2tDQUNOLDhEQUFDQTtrQ0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWQ7R0E1Q3dCdkI7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9jb21wb25lbnRzL0NoYXRJbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgS2V5Ym9hcmRFdmVudCB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIENoYXRJbnB1dFByb3BzIHtcbiAgb25TZW5kTWVzc2FnZTogKG1lc3NhZ2U6IHN0cmluZykgPT4gdm9pZDtcbiAgZGlzYWJsZWQ/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDaGF0SW5wdXQoeyBvblNlbmRNZXNzYWdlLCBkaXNhYmxlZCA9IGZhbHNlIH06IENoYXRJbnB1dFByb3BzKSB7XG4gIGNvbnN0IFttZXNzYWdlLCBzZXRNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcblxuICBjb25zdCBoYW5kbGVTZW5kID0gKCkgPT4ge1xuICAgIGlmIChtZXNzYWdlLnRyaW0oKSAmJiAhZGlzYWJsZWQpIHtcbiAgICAgIG9uU2VuZE1lc3NhZ2UobWVzc2FnZSk7XG4gICAgICBzZXRNZXNzYWdlKCcnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSAoZTogS2V5Ym9hcmRFdmVudDxIVE1MVGV4dEFyZWFFbGVtZW50PikgPT4ge1xuICAgIGlmIChlLmtleSA9PT0gJ0VudGVyJyAmJiAhZS5zaGlmdEtleSkge1xuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgaGFuZGxlU2VuZCgpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY2hhdC1pbnB1dFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnB1dC1jb250YWluZXJcIj5cbiAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgdmFsdWU9e21lc3NhZ2V9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRNZXNzYWdlKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICBvbktleVByZXNzPXtoYW5kbGVLZXlQcmVzc31cbiAgICAgICAgICBwbGFjZWhvbGRlcj17ZGlzYWJsZWQgPyBcIkdlbmVyYXRpbmcgZGlhZ3JhbS4uLlwiIDogXCJEZXNjcmliZSB5b3VyIGFyY2hpdGVjdHVyZSBvciBhc2sgZm9yIGNoYW5nZXMuLi5cIn1cbiAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICAgICAgcm93cz17M31cbiAgICAgICAgICBjbGFzc05hbWU9XCJtZXNzYWdlLWlucHV0XCJcbiAgICAgICAgLz5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmR9XG4gICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkIHx8ICFtZXNzYWdlLnRyaW0oKX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJzZW5kLWJ1dHRvblwiXG4gICAgICAgID5cbiAgICAgICAgICB7ZGlzYWJsZWQgPyAn4o+zJyA6ICfwn5OkJ31cbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnB1dC1oaW50c1wiPlxuICAgICAgICA8c3Bhbj7wn5KhIFRyeTogXCJBZGQgYSBjYWNoZSBsYXllclwiIG9yIFwiUmVwbGFjZSB3aXRoIG1pY3Jvc2VydmljZXNcIjwvc3Bhbj5cbiAgICAgICAgPHNwYW4+4oyo77iPIFByZXNzIEVudGVyIHRvIHNlbmQsIFNoaWZ0K0VudGVyIGZvciBuZXcgbGluZTwvc3Bhbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiQ2hhdElucHV0Iiwib25TZW5kTWVzc2FnZSIsImRpc2FibGVkIiwibWVzc2FnZSIsInNldE1lc3NhZ2UiLCJoYW5kbGVTZW5kIiwidHJpbSIsImhhbmRsZUtleVByZXNzIiwiZSIsImtleSIsInNoaWZ0S2V5IiwicHJldmVudERlZmF1bHQiLCJkaXYiLCJjbGFzc05hbWUiLCJ0ZXh0YXJlYSIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbktleVByZXNzIiwicGxhY2Vob2xkZXIiLCJyb3dzIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ChatInput.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ChatInterface.tsx":
/*!**************************************!*\
  !*** ./components/ChatInterface.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./components/ChatMessage.tsx\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatInput */ \"(app-pages-browser)/./components/ChatInput.tsx\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface(param) {\n    let { session, onSessionUpdate, onDiagramUpdate, isLoading, setIsLoading } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        session.messages\n    ]);\n    const handleSendMessage = async (content)=>{\n        if (!content.trim() || isLoading) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: content.trim(),\n            timestamp: new Date()\n        };\n        const updatedSession = {\n            ...session,\n            messages: [\n                ...session.messages,\n                userMessage\n            ],\n            updated_at: new Date()\n        };\n        onSessionUpdate(updatedSession);\n        setIsLoading(true);\n        try {\n            // Generate/update diagram\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.generateDiagram)({\n                description: content.trim(),\n                style: 'excalidraw',\n                existing_diagram: session.current_diagram,\n                chat_history: session.messages\n            });\n            // Parse the Excalidraw data from the response\n            let diagramData = null;\n            if (response.excalidraw_path) {\n                try {\n                    // Fetch the actual Excalidraw data from the backend\n                    const filename = response.excalidraw_path.split('/').pop();\n                    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n                    const diagramResponse = await fetch(\"\".concat(API_URL, \"/diagram/\").concat(filename));\n                    if (diagramResponse.ok) {\n                        diagramData = await diagramResponse.json();\n                    }\n                } catch (error) {\n                    console.error('Error fetching diagram data:', error);\n                }\n            }\n            // Add assistant message\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: response.explanation,\n                timestamp: new Date(),\n                diagram_data: diagramData\n            };\n            const finalSession = {\n                ...updatedSession,\n                messages: [\n                    ...updatedSession.messages,\n                    assistantMessage\n                ],\n                current_diagram: diagramData || session.current_diagram,\n                updated_at: new Date()\n            };\n            onSessionUpdate(finalSession);\n            if (diagramData) {\n                onDiagramUpdate(diagramData);\n            }\n        } catch (error) {\n            console.error('Error generating diagram:', error);\n            // Add error message\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: \"Sorry, I encountered an error: \".concat(error instanceof Error ? error.message : 'Unknown error', \". Please try again.\"),\n                timestamp: new Date()\n            };\n            const errorSession = {\n                ...updatedSession,\n                messages: [\n                    ...updatedSession.messages,\n                    errorMessage\n                ],\n                updated_at: new Date()\n            };\n            onSessionUpdate(errorSession);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"chat-interface\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCAC Architecture Chat\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Describe your AWS architecture and I'll create and iterate on diagrams\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-messages\",\n                children: [\n                    session.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"welcome-message\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"\\uD83D\\uDC4B Welcome!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Start by describing your AWS architecture. For example:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '\"Create a serverless API with Lambda and DynamoDB\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '\"Add a load balancer to the front\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '\"Replace DynamoDB with RDS for better consistency\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this) : session.messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-message\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"typing-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Analyzing and updating diagram...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onSendMessage: handleSendMessage,\n                disabled: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"0epSoi03NVSoD0I0FiLK4iVNXOA=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ChatInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ChatMessage.tsx":
/*!************************************!*\
  !*** ./components/ChatMessage.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatMessageComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ChatMessageComponent(param) {\n    let { message } = param;\n    const isUser = message.role === 'user';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"chat-message \".concat(isUser ? 'user' : 'assistant'),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-avatar\",\n                children: isUser ? '👤' : '🤖'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"message-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"message-role\",\n                                children: isUser ? 'You' : 'Claude'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"message-time\",\n                                children: message.timestamp.toLocaleTimeString([], {\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"message-text\",\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    message.diagram_data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"message-diagram-indicator\",\n                        children: \"\\uD83C\\uDFA8 Diagram updated\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = ChatMessageComponent;\nvar _c;\n$RefreshReg$(_c, \"ChatMessageComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ChatMessage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ExcalidrawCanvas.tsx":
/*!*****************************************!*\
  !*** ./components/ExcalidrawCanvas.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExcalidrawCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Dynamically import Excalidraw with SSR disabled\nconst Excalidraw = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>(await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @excalidraw/excalidraw */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/index.js\"))).Excalidraw, {\n    loadableGenerated: {\n        modules: [\n            \"components/ExcalidrawCanvas.tsx -> \" + \"@excalidraw/excalidraw\"\n        ]\n    },\n    ssr: false\n});\n_c = Excalidraw;\nfunction ExcalidrawCanvas(param) {\n    let { diagramData, onDiagramChange } = param;\n    _s();\n    const [excalidrawAPI, setExcalidrawAPI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isUpdatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Update Excalidraw when diagram data changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExcalidrawCanvas.useEffect\": ()=>{\n            if (excalidrawAPI && diagramData) {\n                try {\n                    isUpdatingRef.current = true;\n                    // Update the scene with new diagram data\n                    excalidrawAPI.updateScene({\n                        elements: diagramData.elements || [],\n                        appState: {\n                            ...diagramData.appState,\n                            viewBackgroundColor: '#ffffff'\n                        }\n                    });\n                    // Reset the flag after a short delay to allow the update to complete\n                    setTimeout({\n                        \"ExcalidrawCanvas.useEffect\": ()=>{\n                            isUpdatingRef.current = false;\n                        }\n                    }[\"ExcalidrawCanvas.useEffect\"], 0);\n                } catch (error) {\n                    console.error('Error updating Excalidraw scene:', error);\n                    isUpdatingRef.current = false;\n                }\n            }\n        }\n    }[\"ExcalidrawCanvas.useEffect\"], [\n        diagramData,\n        excalidrawAPI\n    ]);\n    const handleChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ExcalidrawCanvas.useCallback[handleChange]\": (elements, appState)=>{\n            if (onDiagramChange && !isUpdatingRef.current) {\n                onDiagramChange(elements, appState);\n            }\n        }\n    }[\"ExcalidrawCanvas.useCallback[handleChange]\"], [\n        onDiagramChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"excalidraw-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83C\\uDFA8 Architecture Diagram\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"excalidraw-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (excalidrawAPI) {\n                                        excalidrawAPI.resetScene();\n                                    }\n                                },\n                                className: \"control-button\",\n                                title: \"Clear diagram\",\n                                children: \"\\uD83D\\uDDD1️ Clear\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (excalidrawAPI) {\n                                        const elements = excalidrawAPI.getSceneElements();\n                                        const appState = excalidrawAPI.getAppState();\n                                        const dataURL = excalidrawAPI.getSceneElementsIncludingDeleted();\n                                        // Create download link\n                                        const link = document.createElement('a');\n                                        link.download = \"architecture-\".concat(Date.now(), \".excalidraw\");\n                                        link.href = URL.createObjectURL(new Blob([\n                                            JSON.stringify({\n                                                elements,\n                                                appState\n                                            })\n                                        ], {\n                                            type: 'application/json'\n                                        }));\n                                        link.click();\n                                    }\n                                },\n                                className: \"control-button\",\n                                title: \"Download diagram\",\n                                children: \"\\uD83D\\uDCBE Download\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Excalidraw, {\n                    ref: (api)=>setExcalidrawAPI(api),\n                    onChange: handleChange,\n                    initialData: {\n                        elements: (diagramData === null || diagramData === void 0 ? void 0 : diagramData.elements) || [],\n                        appState: {\n                            viewBackgroundColor: '#ffffff',\n                            currentItemFontFamily: 1,\n                            currentItemFontSize: 16,\n                            ...diagramData === null || diagramData === void 0 ? void 0 : diagramData.appState\n                        }\n                    },\n                    UIOptions: {\n                        canvasActions: {\n                            loadScene: false,\n                            saveToActiveFile: false,\n                            export: {\n                                saveFileToDisk: true\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            !diagramData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-placeholder\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"placeholder-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"\\uD83C\\uDFAF Ready to Create\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Start a conversation to generate your first architecture diagram!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"placeholder-features\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI-generated diagrams\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDD04\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Iterative updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Manual editing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_s(ExcalidrawCanvas, \"Moen95DtlmL/L1/1hKKoJMWHm3w=\");\n_c1 = ExcalidrawCanvas;\nvar _c, _c1;\n$RefreshReg$(_c, \"Excalidraw\");\n$RefreshReg$(_c1, \"ExcalidrawCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ExcalidrawCanvas.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateDiagram: () => (/* binding */ generateDiagram),\n/* harmony export */   getDiagramUrl: () => (/* binding */ getDiagramUrl),\n/* harmony export */   getExcalidrawViewUrl: () => (/* binding */ getExcalidrawViewUrl)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\nasync function generateDiagram(request) {\n    var _request_chat_history;\n    // Prepare the request with chat context\n    const requestBody = {\n        description: request.description,\n        export_format: request.style || 'excalidraw',\n        existing_diagram: request.existing_diagram,\n        chat_history: (_request_chat_history = request.chat_history) === null || _request_chat_history === void 0 ? void 0 : _request_chat_history.map((msg)=>({\n                role: msg.role,\n                content: msg.content,\n                timestamp: msg.timestamp.toISOString()\n            }))\n    };\n    const response = await fetch(\"\".concat(API_URL, \"/generate\"), {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestBody)\n    });\n    if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(\"HTTP error! status: \".concat(response.status, \", message: \").concat(errorText));\n    }\n    return response.json();\n}\nfunction getDiagramUrl(diagramPath) {\n    const filename = diagramPath.split('/').pop();\n    return \"\".concat(API_URL, \"/diagram/\").concat(filename);\n}\nfunction getExcalidrawViewUrl(diagramPath) {\n    // For Excalidraw files, we can create a direct link to view in Excalidraw\n    const filename = diagramPath.split('/').pop();\n    if (filename === null || filename === void 0 ? void 0 : filename.endsWith('.excalidraw')) {\n        return \"https://excalidraw.com/\";\n    }\n    return getDiagramUrl(diagramPath);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWMnO1xuZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWMnO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtZHluYW1pYy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ArchitectureWorkspace.tsx */ \"(app-pages-browser)/./components/ArchitectureWorkspace.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZqdXN0aW5tZW5lc3RyaW5hJTJGd29ya3NwYWNlJTJGc3lzdGVtLWRlc2lnbi1sbG0lMkZjb21wb25lbnRzJTJGQXJjaGl0ZWN0dXJlV29ya3NwYWNlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBMEoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vY29tcG9uZW50cy9BcmNoaXRlY3R1cmVXb3Jrc3BhY2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgUmVhY3RcbiAqIHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qc1xuICpcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuXCJ1c2Ugc3RyaWN0XCI7XG5cInByb2R1Y3Rpb25cIiAhPT0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgJiZcbiAgKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSkge1xuICAgICAgaWYgKG51bGwgPT0gdHlwZSkgcmV0dXJuIG51bGw7XG4gICAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgcmV0dXJuIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0VcbiAgICAgICAgICA/IG51bGxcbiAgICAgICAgICA6IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8IG51bGw7XG4gICAgICBpZiAoXCJzdHJpbmdcIiA9PT0gdHlwZW9mIHR5cGUpIHJldHVybiB0eXBlO1xuICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgIGNhc2UgUkVBQ1RfRlJBR01FTlRfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJGcmFnbWVudFwiO1xuICAgICAgICBjYXNlIFJFQUNUX1BST0ZJTEVSX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiUHJvZmlsZXJcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN0cmljdE1vZGVcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN1c3BlbnNlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfTElTVF9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN1c3BlbnNlTGlzdFwiO1xuICAgICAgICBjYXNlIFJFQUNUX0FDVElWSVRZX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiQWN0aXZpdHlcIjtcbiAgICAgIH1cbiAgICAgIGlmIChcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgc3dpdGNoIChcbiAgICAgICAgICAoXCJudW1iZXJcIiA9PT0gdHlwZW9mIHR5cGUudGFnICYmXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlY2VpdmVkIGFuIHVuZXhwZWN0ZWQgb2JqZWN0IGluIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSgpLiBUaGlzIGlzIGxpa2VseSBhIGJ1ZyBpbiBSZWFjdC4gUGxlYXNlIGZpbGUgYW4gaXNzdWUuXCJcbiAgICAgICAgICAgICksXG4gICAgICAgICAgdHlwZS4kJHR5cGVvZilcbiAgICAgICAgKSB7XG4gICAgICAgICAgY2FzZSBSRUFDVF9QT1JUQUxfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiBcIlBvcnRhbFwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OVEVYVF9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuICh0eXBlLmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiKSArIFwiLlByb3ZpZGVyXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05TVU1FUl9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuICh0eXBlLl9jb250ZXh0LmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiKSArIFwiLkNvbnN1bWVyXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFOlxuICAgICAgICAgICAgdmFyIGlubmVyVHlwZSA9IHR5cGUucmVuZGVyO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuZGlzcGxheU5hbWU7XG4gICAgICAgICAgICB0eXBlIHx8XG4gICAgICAgICAgICAgICgodHlwZSA9IGlubmVyVHlwZS5kaXNwbGF5TmFtZSB8fCBpbm5lclR5cGUubmFtZSB8fCBcIlwiKSxcbiAgICAgICAgICAgICAgKHR5cGUgPSBcIlwiICE9PSB0eXBlID8gXCJGb3J3YXJkUmVmKFwiICsgdHlwZSArIFwiKVwiIDogXCJGb3J3YXJkUmVmXCIpKTtcbiAgICAgICAgICAgIHJldHVybiB0eXBlO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTUVNT19UWVBFOlxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgKGlubmVyVHlwZSA9IHR5cGUuZGlzcGxheU5hbWUgfHwgbnVsbCksXG4gICAgICAgICAgICAgIG51bGwgIT09IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgID8gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgOiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZS50eXBlKSB8fCBcIk1lbW9cIlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICBjYXNlIFJFQUNUX0xBWllfVFlQRTpcbiAgICAgICAgICAgIGlubmVyVHlwZSA9IHR5cGUuX3BheWxvYWQ7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5faW5pdDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIHJldHVybiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZShpbm5lclR5cGUpKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKHgpIHt9XG4gICAgICAgIH1cbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBmdW5jdGlvbiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpIHtcbiAgICAgIHJldHVybiBcIlwiICsgdmFsdWU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGNoZWNrS2V5U3RyaW5nQ29lcmNpb24odmFsdWUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMTtcbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITA7XG4gICAgICB9XG4gICAgICBpZiAoSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0KSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9IGNvbnNvbGU7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX3RlbXBfY29uc3QgPSBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQuZXJyb3I7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQkanNjb21wJDAgPVxuICAgICAgICAgIChcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiBTeW1ib2wgJiZcbiAgICAgICAgICAgIFN5bWJvbC50b1N0cmluZ1RhZyAmJlxuICAgICAgICAgICAgdmFsdWVbU3ltYm9sLnRvU3RyaW5nVGFnXSkgfHxcbiAgICAgICAgICB2YWx1ZS5jb25zdHJ1Y3Rvci5uYW1lIHx8XG4gICAgICAgICAgXCJPYmplY3RcIjtcbiAgICAgICAgSlNDb21waWxlcl90ZW1wX2NvbnN0LmNhbGwoXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LFxuICAgICAgICAgIFwiVGhlIHByb3ZpZGVkIGtleSBpcyBhbiB1bnN1cHBvcnRlZCB0eXBlICVzLiBUaGlzIHZhbHVlIG11c3QgYmUgY29lcmNlZCB0byBhIHN0cmluZyBiZWZvcmUgdXNpbmcgaXQgaGVyZS5cIixcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQkanNjb21wJDBcbiAgICAgICAgKTtcbiAgICAgICAgcmV0dXJuIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGdldFRhc2tOYW1lKHR5cGUpIHtcbiAgICAgIGlmICh0eXBlID09PSBSRUFDVF9GUkFHTUVOVF9UWVBFKSByZXR1cm4gXCI8PlwiO1xuICAgICAgaWYgKFxuICAgICAgICBcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSAmJlxuICAgICAgICBudWxsICE9PSB0eXBlICYmXG4gICAgICAgIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0xBWllfVFlQRVxuICAgICAgKVxuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgdHJ5IHtcbiAgICAgICAgdmFyIG5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHJldHVybiBuYW1lID8gXCI8XCIgKyBuYW1lICsgXCI+XCIgOiBcIjwuLi4+XCI7XG4gICAgICB9IGNhdGNoICh4KSB7XG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGdldE93bmVyKCkge1xuICAgICAgdmFyIGRpc3BhdGNoZXIgPSBSZWFjdFNoYXJlZEludGVybmFscy5BO1xuICAgICAgcmV0dXJuIG51bGwgPT09IGRpc3BhdGNoZXIgPyBudWxsIDogZGlzcGF0Y2hlci5nZXRPd25lcigpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBVbmtub3duT3duZXIoKSB7XG4gICAgICByZXR1cm4gRXJyb3IoXCJyZWFjdC1zdGFjay10b3AtZnJhbWVcIik7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGhhc1ZhbGlkS2V5KGNvbmZpZykge1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICB2YXIgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihjb25maWcsIFwia2V5XCIpLmdldDtcbiAgICAgICAgaWYgKGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmcpIHJldHVybiAhMTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbmZpZy5rZXk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSkge1xuICAgICAgZnVuY3Rpb24gd2FybkFib3V0QWNjZXNzaW5nS2V5KCkge1xuICAgICAgICBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biB8fFxuICAgICAgICAgICgoc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gPSAhMCksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIFwiJXM6IGBrZXlgIGlzIG5vdCBhIHByb3AuIFRyeWluZyB0byBhY2Nlc3MgaXQgd2lsbCByZXN1bHQgaW4gYHVuZGVmaW5lZGAgYmVpbmcgcmV0dXJuZWQuIElmIHlvdSBuZWVkIHRvIGFjY2VzcyB0aGUgc2FtZSB2YWx1ZSB3aXRoaW4gdGhlIGNoaWxkIGNvbXBvbmVudCwgeW91IHNob3VsZCBwYXNzIGl0IGFzIGEgZGlmZmVyZW50IHByb3AuIChodHRwczovL3JlYWN0LmRldi9saW5rL3NwZWNpYWwtcHJvcHMpXCIsXG4gICAgICAgICAgICBkaXNwbGF5TmFtZVxuICAgICAgICAgICkpO1xuICAgICAgfVxuICAgICAgd2FybkFib3V0QWNjZXNzaW5nS2V5LmlzUmVhY3RXYXJuaW5nID0gITA7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvcHMsIFwia2V5XCIsIHtcbiAgICAgICAgZ2V0OiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXksXG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITBcbiAgICAgIH0pO1xuICAgIH1cbiAgICBmdW5jdGlvbiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZygpIHtcbiAgICAgIHZhciBjb21wb25lbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHRoaXMudHlwZSk7XG4gICAgICBkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdIHx8XG4gICAgICAgICgoZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSA9ICEwKSxcbiAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICBcIkFjY2Vzc2luZyBlbGVtZW50LnJlZiB3YXMgcmVtb3ZlZCBpbiBSZWFjdCAxOS4gcmVmIGlzIG5vdyBhIHJlZ3VsYXIgcHJvcC4gSXQgd2lsbCBiZSByZW1vdmVkIGZyb20gdGhlIEpTWCBFbGVtZW50IHR5cGUgaW4gYSBmdXR1cmUgcmVsZWFzZS5cIlxuICAgICAgICApKTtcbiAgICAgIGNvbXBvbmVudE5hbWUgPSB0aGlzLnByb3BzLnJlZjtcbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbXBvbmVudE5hbWUgPyBjb21wb25lbnROYW1lIDogbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gUmVhY3RFbGVtZW50KFxuICAgICAgdHlwZSxcbiAgICAgIGtleSxcbiAgICAgIHNlbGYsXG4gICAgICBzb3VyY2UsXG4gICAgICBvd25lcixcbiAgICAgIHByb3BzLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgc2VsZiA9IHByb3BzLnJlZjtcbiAgICAgIHR5cGUgPSB7XG4gICAgICAgICQkdHlwZW9mOiBSRUFDVF9FTEVNRU5UX1RZUEUsXG4gICAgICAgIHR5cGU6IHR5cGUsXG4gICAgICAgIGtleToga2V5LFxuICAgICAgICBwcm9wczogcHJvcHMsXG4gICAgICAgIF9vd25lcjogb3duZXJcbiAgICAgIH07XG4gICAgICBudWxsICE9PSAodm9pZCAwICE9PSBzZWxmID8gc2VsZiA6IG51bGwpXG4gICAgICAgID8gT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICAgICAgZ2V0OiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZ1xuICAgICAgICAgIH0pXG4gICAgICAgIDogT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHsgZW51bWVyYWJsZTogITEsIHZhbHVlOiBudWxsIH0pO1xuICAgICAgdHlwZS5fc3RvcmUgPSB7fTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLl9zdG9yZSwgXCJ2YWxpZGF0ZWRcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogMFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdJbmZvXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnU3RhY2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdTdGFja1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdUYXNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnVGFza1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZnJlZXplICYmIChPYmplY3QuZnJlZXplKHR5cGUucHJvcHMpLCBPYmplY3QuZnJlZXplKHR5cGUpKTtcbiAgICAgIHJldHVybiB0eXBlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBqc3hERVZJbXBsKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGYsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICB2YXIgY2hpbGRyZW4gPSBjb25maWcuY2hpbGRyZW47XG4gICAgICBpZiAodm9pZCAwICE9PSBjaGlsZHJlbilcbiAgICAgICAgaWYgKGlzU3RhdGljQ2hpbGRyZW4pXG4gICAgICAgICAgaWYgKGlzQXJyYXlJbXBsKGNoaWxkcmVuKSkge1xuICAgICAgICAgICAgZm9yIChcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9IDA7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPCBjaGlsZHJlbi5sZW5ndGg7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4rK1xuICAgICAgICAgICAgKVxuICAgICAgICAgICAgICB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbltpc1N0YXRpY0NoaWxkcmVuXSk7XG4gICAgICAgICAgICBPYmplY3QuZnJlZXplICYmIE9iamVjdC5mcmVlemUoY2hpbGRyZW4pO1xuICAgICAgICAgIH0gZWxzZVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWFjdC5qc3g6IFN0YXRpYyBjaGlsZHJlbiBzaG91bGQgYWx3YXlzIGJlIGFuIGFycmF5LiBZb3UgYXJlIGxpa2VseSBleHBsaWNpdGx5IGNhbGxpbmcgUmVhY3QuanN4cyBvciBSZWFjdC5qc3hERVYuIFVzZSB0aGUgQmFiZWwgdHJhbnNmb3JtIGluc3RlYWQuXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgIGVsc2UgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW4pO1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICBjaGlsZHJlbiA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhjb25maWcpLmZpbHRlcihmdW5jdGlvbiAoaykge1xuICAgICAgICAgIHJldHVybiBcImtleVwiICE9PSBrO1xuICAgICAgICB9KTtcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9XG4gICAgICAgICAgMCA8IGtleXMubGVuZ3RoXG4gICAgICAgICAgICA/IFwie2tleTogc29tZUtleSwgXCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIlxuICAgICAgICAgICAgOiBcIntrZXk6IHNvbWVLZXl9XCI7XG4gICAgICAgIGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dIHx8XG4gICAgICAgICAgKChrZXlzID1cbiAgICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aCA/IFwie1wiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCIgOiBcInt9XCIpLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAnQSBwcm9wcyBvYmplY3QgY29udGFpbmluZyBhIFwia2V5XCIgcHJvcCBpcyBiZWluZyBzcHJlYWQgaW50byBKU1g6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyB7Li4ucHJvcHN9IC8+XFxuUmVhY3Qga2V5cyBtdXN0IGJlIHBhc3NlZCBkaXJlY3RseSB0byBKU1ggd2l0aG91dCB1c2luZyBzcHJlYWQ6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyBrZXk9e3NvbWVLZXl9IHsuLi5wcm9wc30gLz4nLFxuICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgICAgIGNoaWxkcmVuLFxuICAgICAgICAgICAga2V5cyxcbiAgICAgICAgICAgIGNoaWxkcmVuXG4gICAgICAgICAgKSxcbiAgICAgICAgICAoZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gPSAhMCkpO1xuICAgICAgfVxuICAgICAgY2hpbGRyZW4gPSBudWxsO1xuICAgICAgdm9pZCAwICE9PSBtYXliZUtleSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihtYXliZUtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBtYXliZUtleSkpO1xuICAgICAgaGFzVmFsaWRLZXkoY29uZmlnKSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihjb25maWcua2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIGNvbmZpZy5rZXkpKTtcbiAgICAgIGlmIChcImtleVwiIGluIGNvbmZpZykge1xuICAgICAgICBtYXliZUtleSA9IHt9O1xuICAgICAgICBmb3IgKHZhciBwcm9wTmFtZSBpbiBjb25maWcpXG4gICAgICAgICAgXCJrZXlcIiAhPT0gcHJvcE5hbWUgJiYgKG1heWJlS2V5W3Byb3BOYW1lXSA9IGNvbmZpZ1twcm9wTmFtZV0pO1xuICAgICAgfSBlbHNlIG1heWJlS2V5ID0gY29uZmlnO1xuICAgICAgY2hpbGRyZW4gJiZcbiAgICAgICAgZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIoXG4gICAgICAgICAgbWF5YmVLZXksXG4gICAgICAgICAgXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZVxuICAgICAgICAgICAgPyB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBcIlVua25vd25cIlxuICAgICAgICAgICAgOiB0eXBlXG4gICAgICAgICk7XG4gICAgICByZXR1cm4gUmVhY3RFbGVtZW50KFxuICAgICAgICB0eXBlLFxuICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgc2VsZixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBnZXRPd25lcigpLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgZGVidWdTdGFjayxcbiAgICAgICAgZGVidWdUYXNrXG4gICAgICApO1xuICAgIH1cbiAgICBmdW5jdGlvbiB2YWxpZGF0ZUNoaWxkS2V5cyhub2RlKSB7XG4gICAgICBcIm9iamVjdFwiID09PSB0eXBlb2Ygbm9kZSAmJlxuICAgICAgICBudWxsICE9PSBub2RlICYmXG4gICAgICAgIG5vZGUuJCR0eXBlb2YgPT09IFJFQUNUX0VMRU1FTlRfVFlQRSAmJlxuICAgICAgICBub2RlLl9zdG9yZSAmJlxuICAgICAgICAobm9kZS5fc3RvcmUudmFsaWRhdGVkID0gMSk7XG4gICAgfVxuICAgIHZhciBSZWFjdCA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3RcIiksXG4gICAgICBSRUFDVF9FTEVNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QudHJhbnNpdGlvbmFsLmVsZW1lbnRcIiksXG4gICAgICBSRUFDVF9QT1JUQUxfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wb3J0YWxcIiksXG4gICAgICBSRUFDVF9GUkFHTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZyYWdtZW50XCIpLFxuICAgICAgUkVBQ1RfU1RSSUNUX01PREVfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdHJpY3RfbW9kZVwiKSxcbiAgICAgIFJFQUNUX1BST0ZJTEVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucHJvZmlsZXJcIik7XG4gICAgU3ltYm9sLmZvcihcInJlYWN0LnByb3ZpZGVyXCIpO1xuICAgIHZhciBSRUFDVF9DT05TVU1FUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnN1bWVyXCIpLFxuICAgICAgUkVBQ1RfQ09OVEVYVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnRleHRcIiksXG4gICAgICBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZvcndhcmRfcmVmXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZVwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZV9saXN0XCIpLFxuICAgICAgUkVBQ1RfTUVNT19UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0Lm1lbW9cIiksXG4gICAgICBSRUFDVF9MQVpZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubGF6eVwiKSxcbiAgICAgIFJFQUNUX0FDVElWSVRZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuYWN0aXZpdHlcIiksXG4gICAgICBSRUFDVF9DTElFTlRfUkVGRVJFTkNFID0gU3ltYm9sLmZvcihcInJlYWN0LmNsaWVudC5yZWZlcmVuY2VcIiksXG4gICAgICBSZWFjdFNoYXJlZEludGVybmFscyA9XG4gICAgICAgIFJlYWN0Ll9fQ0xJRU5UX0lOVEVSTkFMU19ET19OT1RfVVNFX09SX1dBUk5fVVNFUlNfVEhFWV9DQU5OT1RfVVBHUkFERSxcbiAgICAgIGhhc093blByb3BlcnR5ID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eSxcbiAgICAgIGlzQXJyYXlJbXBsID0gQXJyYXkuaXNBcnJheSxcbiAgICAgIGNyZWF0ZVRhc2sgPSBjb25zb2xlLmNyZWF0ZVRhc2tcbiAgICAgICAgPyBjb25zb2xlLmNyZWF0ZVRhc2tcbiAgICAgICAgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICB9O1xuICAgIFJlYWN0ID0ge1xuICAgICAgXCJyZWFjdC1zdGFjay1ib3R0b20tZnJhbWVcIjogZnVuY3Rpb24gKGNhbGxTdGFja0ZvckVycm9yKSB7XG4gICAgICAgIHJldHVybiBjYWxsU3RhY2tGb3JFcnJvcigpO1xuICAgICAgfVxuICAgIH07XG4gICAgdmFyIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duO1xuICAgIHZhciBkaWRXYXJuQWJvdXRFbGVtZW50UmVmID0ge307XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnU3RhY2sgPSBSZWFjdFtcInJlYWN0LXN0YWNrLWJvdHRvbS1mcmFtZVwiXS5iaW5kKFxuICAgICAgUmVhY3QsXG4gICAgICBVbmtub3duT3duZXJcbiAgICApKCk7XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnVGFzayA9IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUoVW5rbm93bk93bmVyKSk7XG4gICAgdmFyIGRpZFdhcm5BYm91dEtleVNwcmVhZCA9IHt9O1xuICAgIGV4cG9ydHMuRnJhZ21lbnQgPSBSRUFDVF9GUkFHTUVOVF9UWVBFO1xuICAgIGV4cG9ydHMuanN4REVWID0gZnVuY3Rpb24gKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGZcbiAgICApIHtcbiAgICAgIHZhciB0cmFja0FjdHVhbE93bmVyID1cbiAgICAgICAgMWU0ID4gUmVhY3RTaGFyZWRJbnRlcm5hbHMucmVjZW50bHlDcmVhdGVkT3duZXJTdGFja3MrKztcbiAgICAgIHJldHVybiBqc3hERVZJbXBsKFxuICAgICAgICB0eXBlLFxuICAgICAgICBjb25maWcsXG4gICAgICAgIG1heWJlS2V5LFxuICAgICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgICBzb3VyY2UsXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHRyYWNrQWN0dWFsT3duZXJcbiAgICAgICAgICA/IEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpXG4gICAgICAgICAgOiB1bmtub3duT3duZXJEZWJ1Z1N0YWNrLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyID8gY3JlYXRlVGFzayhnZXRUYXNrTmFtZSh0eXBlKSkgOiB1bmtub3duT3duZXJEZWJ1Z1Rhc2tcbiAgICAgICk7XG4gICAgfTtcbiAgfSkoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    var _mergedOptions_loadableGenerated;\n    const loadableOptions = {};\n    if (typeof dynamicOptions === 'function') {\n        loadableOptions.loader = dynamicOptions;\n    }\n    const mergedOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    return (0, _loadable.default)({\n        ...mergedOptions,\n        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (false) {}\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoiOzs7O2dEQWNnQkE7OztlQUFBQTs7OzBDQVhrQjtBQVczQixzQkFBc0IsS0FBdUM7SUFBdkMsTUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQXFCLEdBQXZDO0lBQzNCLElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUVELE9BQU9BO0FBQ1Q7S0FOZ0JGIiwic291cmNlcyI6WyIvc3JjL3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2R5bmFtaWMtYmFpbG91dC10by1jc3IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgdHlwZSB7IFJlYWN0RWxlbWVudCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgQmFpbG91dFRvQ1NSRXJyb3IgfSBmcm9tICcuL2JhaWxvdXQtdG8tY3NyJ1xuXG5pbnRlcmZhY2UgQmFpbG91dFRvQ1NSUHJvcHMge1xuICByZWFzb246IHN0cmluZ1xuICBjaGlsZHJlbjogUmVhY3RFbGVtZW50XG59XG5cbi8qKlxuICogSWYgcmVuZGVyZWQgb24gdGhlIHNlcnZlciwgdGhpcyBjb21wb25lbnQgdGhyb3dzIGFuIGVycm9yXG4gKiB0byBzaWduYWwgTmV4dC5qcyB0aGF0IGl0IHNob3VsZCBiYWlsIG91dCB0byBjbGllbnQtc2lkZSByZW5kZXJpbmcgaW5zdGVhZC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIEJhaWxvdXRUb0NTUih7IHJlYXNvbiwgY2hpbGRyZW4gfTogQmFpbG91dFRvQ1NSUHJvcHMpIHtcbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgdGhyb3cgbmV3IEJhaWxvdXRUb0NTUkVycm9yKHJlYXNvbilcbiAgfVxuXG4gIHJldHVybiBjaGlsZHJlblxufVxuIl0sIm5hbWVzIjpbIkJhaWxvdXRUb0NTUiIsInJlYXNvbiIsImNoaWxkcmVuIiwid2luZG93IiwiQmFpbG91dFRvQ1NSRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\nconst _preloadchunks = __webpack_require__(/*! ./preload-chunks */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    // Check \"default\" prop before accessing it, as it could be client reference proxy that could break it reference.\n    // Cases:\n    // mod: { default: Component }\n    // mod: Component\n    // mod: { default: proxy(Component) }\n    // mod: proxy(Component)\n    const hasDefault = mod && 'default' in mod;\n    return {\n        default: hasDefault ? mod.default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        // If it's non-SSR or provided a loading component, wrap it in a suspense boundary\n        const hasSuspenseBoundary = !opts.ssr || !!opts.loading;\n        const Wrap = hasSuspenseBoundary ? _react.Suspense : _react.Fragment;\n        const wrapProps = hasSuspenseBoundary ? {\n            fallback: fallbackElement\n        } : {};\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n            children: [\n                 false ? /*#__PURE__*/ 0 : null,\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                    ...props\n                })\n            ]\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Wrap, {\n            ...wrapProps,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = 'LoadableComponent';\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js ***!
  \**************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"PreloadChunks\", ({\n    enumerable: true,\n    get: function() {\n        return PreloadChunks;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _reactdom = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\nconst _workasyncstorageexternal = __webpack_require__(/*! ../../../server/app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\");\nconst _encodeuripath = __webpack_require__(/*! ../encode-uri-path */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/encode-uri-path.js\");\nfunction PreloadChunks(param) {\n    let { moduleIds } = param;\n    // Early return in client compilation and only load requestStore on server side\n    if (true) {\n        return null;\n    }\n    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();\n    if (workStore === undefined) {\n        return null;\n    }\n    const allFiles = [];\n    // Search the current dynamic call unique key id in react loadable manifest,\n    // and find the corresponding CSS files to preload\n    if (workStore.reactLoadableManifest && moduleIds) {\n        const manifest = workStore.reactLoadableManifest;\n        for (const key of moduleIds){\n            if (!manifest[key]) continue;\n            const chunks = manifest[key].files;\n            allFiles.push(...chunks);\n        }\n    }\n    if (allFiles.length === 0) {\n        return null;\n    }\n    const dplId =  false ? 0 : '';\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: allFiles.map((chunk)=>{\n            const href = workStore.assetPrefix + \"/_next/\" + (0, _encodeuripath.encodeURIPath)(chunk) + dplId;\n            const isCss = chunk.endsWith('.css');\n            // If it's stylesheet we use `precedence` o help hoist with React Float.\n            // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.\n            // The `preload` for stylesheet is not optional.\n            if (isCss) {\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n                    // @ts-ignore\n                    precedence: \"dynamic\",\n                    href: href,\n                    rel: \"stylesheet\",\n                    as: \"style\"\n                }, chunk);\n            } else {\n                // If it's script we use ReactDOM.preload to preload the resources\n                (0, _reactdom.preload)(href, {\n                    as: 'script',\n                    fetchPriority: 'low'\n                });\n                return null;\n            }\n        })\n    });\n} //# sourceMappingURL=preload-chunks.js.map\n_c = PreloadChunks;\nvar _c;\n$RefreshReg$(_c, \"PreloadChunks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/async-local-storage.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    bindSnapshot: function() {\n        return bindSnapshot;\n    },\n    createAsyncLocalStorage: function() {\n        return createAsyncLocalStorage;\n    },\n    createSnapshot: function() {\n        return createSnapshot;\n    }\n});\nconst sharedAsyncLocalStorageNotAvailableError = Object.defineProperty(new Error('Invariant: AsyncLocalStorage accessed in runtime where it is not available'), \"__NEXT_ERROR_CODE\", {\n    value: \"E504\",\n    enumerable: false,\n    configurable: true\n});\nclass FakeAsyncLocalStorage {\n    disable() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    getStore() {\n        // This fake implementation of AsyncLocalStorage always returns `undefined`.\n        return undefined;\n    }\n    run() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    exit() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    enterWith() {\n        throw sharedAsyncLocalStorageNotAvailableError;\n    }\n    static bind(fn) {\n        return fn;\n    }\n}\nconst maybeGlobalAsyncLocalStorage = typeof globalThis !== 'undefined' && globalThis.AsyncLocalStorage;\nfunction createAsyncLocalStorage() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return new maybeGlobalAsyncLocalStorage();\n    }\n    return new FakeAsyncLocalStorage();\n}\nfunction bindSnapshot(fn) {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.bind(fn);\n    }\n    return FakeAsyncLocalStorage.bind(fn);\n}\nfunction createSnapshot() {\n    if (maybeGlobalAsyncLocalStorage) {\n        return maybeGlobalAsyncLocalStorage.snapshot();\n    }\n    return function(fn, ...args) {\n        return fn(...args);\n    };\n}\n\n//# sourceMappingURL=async-local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage-instance.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorageInstance\", ({\n    enumerable: true,\n    get: function() {\n        return workAsyncStorageInstance;\n    }\n}));\nconst _asynclocalstorage = __webpack_require__(/*! ./async-local-storage */ \"(shared)/./node_modules/next/dist/server/app-render/async-local-storage.js\");\nconst workAsyncStorageInstance = (0, _asynclocalstorage.createAsyncLocalStorage)();\n\n//# sourceMappingURL=work-async-storage-instance.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLDREQUEyRDtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLDJCQUEyQixtQkFBTyxDQUFDLHlHQUF1QjtBQUMxRDs7QUFFQSIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvd29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwid29ya0FzeW5jU3RvcmFnZUluc3RhbmNlXCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiB3b3JrQXN5bmNTdG9yYWdlSW5zdGFuY2U7XG4gICAgfVxufSk7XG5jb25zdCBfYXN5bmNsb2NhbHN0b3JhZ2UgPSByZXF1aXJlKFwiLi9hc3luYy1sb2NhbC1zdG9yYWdlXCIpO1xuY29uc3Qgd29ya0FzeW5jU3RvcmFnZUluc3RhbmNlID0gKDAsIF9hc3luY2xvY2Fsc3RvcmFnZS5jcmVhdGVBc3luY0xvY2FsU3RvcmFnZSkoKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay1hc3luYy1zdG9yYWdlLWluc3RhbmNlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\n"));

/***/ }),

/***/ "(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/next/dist/server/app-render/work-async-storage.external.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"workAsyncStorage\", ({\n    enumerable: true,\n    get: function() {\n        return _workasyncstorageinstance.workAsyncStorageInstance;\n    }\n}));\nconst _workasyncstorageinstance = __webpack_require__(/*! ./work-async-storage-instance */ \"(shared)/./node_modules/next/dist/server/app-render/work-async-storage-instance.js\");\n\n//# sourceMappingURL=work-async-storage.external.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNoYXJlZCkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLG9EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsRUFBQztBQUNGLGtDQUFrQyxtQkFBTyxDQUFDLHlIQUErQjs7QUFFekUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIndvcmtBc3luY1N0b3JhZ2VcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF93b3JrYXN5bmNzdG9yYWdlaW5zdGFuY2Uud29ya0FzeW5jU3RvcmFnZUluc3RhbmNlO1xuICAgIH1cbn0pO1xuY29uc3QgX3dvcmthc3luY3N0b3JhZ2VpbnN0YW5jZSA9IHJlcXVpcmUoXCIuL3dvcmstYXN5bmMtc3RvcmFnZS1pbnN0YW5jZVwiKTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9d29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(shared)/./node_modules/next/dist/server/app-render/work-async-storage.external.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);