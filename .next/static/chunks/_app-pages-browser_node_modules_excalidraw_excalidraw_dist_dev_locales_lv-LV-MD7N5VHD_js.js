"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_lv-LV-MD7N5VHD_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/lv-LV-MD7N5VHD.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/lv-LV-MD7N5VHD.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ lv_LV_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/lv-LV.json\nvar labels = {\n  paste: \"Iel\\u012Bm\\u0113t\",\n  pasteAsPlaintext: \"Iel\\u012Bm\\u0113t k\\u0101 vienk\\u0101r\\u0161u tekstu\",\n  pasteCharts: \"Iel\\u012Bm\\u0113t grafikus\",\n  selectAll: \"Atlas\\u012Bt visu\",\n  multiSelect: \"Pievienot elementu atlasei\",\n  moveCanvas: \"P\\u0101rvietot t\\u0101feli\",\n  cut: \"Izgriezt\",\n  copy: \"Kop\\u0113t\",\n  copyAsPng: \"Kop\\u0113t starpliktuv\\u0113 k\\u0101 PNG\",\n  copyAsSvg: \"Kop\\u0113t starpliktuv\\u0113 k\\u0101 SVG\",\n  copyText: \"Kop\\u0113t starpliktuv\\u0113 k\\u0101 tekstu\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"P\\u0101rvietot vienu sl\\u0101ni augst\\u0101k\",\n  sendToBack: \"P\\u0101rvietot uz zem\\u0101ko sl\\u0101ni\",\n  bringToFront: \"P\\u0101rvietot uz virs\\u0113jo sl\\u0101ni\",\n  sendBackward: \"P\\u0101rvietot par vienu sl\\u0101ni zem\\u0101k\",\n  delete: \"Dz\\u0113st\",\n  copyStyles: \"Kop\\u0113t stilus\",\n  pasteStyles: \"Iel\\u012Bm\\u0113t stilus\",\n  stroke: \"Sv\\u012Btras kr\\u0101sa\",\n  background: \"Fona kr\\u0101sa\",\n  fill: \"Aizpild\\u012Bjums\",\n  strokeWidth: \"Sv\\u012Btras platums\",\n  strokeStyle: \"Sv\\u012Btras stils\",\n  strokeStyle_solid: \"Vienlaidu\",\n  strokeStyle_dashed: \"Raust\\u012Bta l\\u012Bnija\",\n  strokeStyle_dotted: \"Punktota l\\u012Bnija\",\n  sloppiness: \"Precizit\\u0101te\",\n  opacity: \"Necaursp\\u012Bd\\u012Bgums\",\n  textAlign: \"Teksta l\\u012Bdzin\\u0101\\u0161ana\",\n  edges: \"Malas\",\n  sharp: \"Asas\",\n  round: \"Apa\\u013Cas\",\n  arrowheads: \"Bultas\",\n  arrowhead_none: \"Nek\\u0101das\",\n  arrowhead_arrow: \"Bulta\",\n  arrowhead_bar: \"Sv\\u012Btra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Trijst\\u016Bris\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Teksta lielums\",\n  fontFamily: \"Fontu saime\",\n  addWatermark: 'Pievienot \"Rad\\u012Bts ar Excalidraw\"',\n  handDrawn: \"Rokraksts\",\n  normal: \"Parasts\",\n  code: \"Kods\",\n  small: \"Mazs\",\n  medium: \"Vid\\u0113js\",\n  large: \"Liels\",\n  veryLarge: \"\\u013Boti liels\",\n  solid: \"Pilns\",\n  hachure: \"Sv\\u012Btrots\",\n  zigzag: \"Zigzagl\\u012Bnija\",\n  crossHatch: \"\\u0160\\u0137\\u0113rssv\\u012Btrots\",\n  thin: \"\\u0160aurs\",\n  bold: \"Trekns\",\n  left: \"Pa kreisi\",\n  center: \"Vid\\u016B\",\n  right: \"Pa labi\",\n  extraBold: \"\\u012Apa\\u0161i trekns\",\n  architect: \"Arhitekts\",\n  artist: \"M\\u0101kslinieks\",\n  cartoonist: \"Karikat\\u016Brists\",\n  fileTitle: \"Datnes nosaukums\",\n  colorPicker: \"Kr\\u0101su atlas\\u012Bt\\u0101js\",\n  canvasColors: \"Izmantots t\\u0101felei\",\n  canvasBackground: \"Ainas fons\",\n  drawingCanvas: \"T\\u0101fele\",\n  layers: \"Sl\\u0101\\u0146i\",\n  actions: \"Darb\\u012Bbas\",\n  language: \"Valoda\",\n  liveCollaboration: \"Sadarb\\u012Bba tie\\u0161saist\\u0113...\",\n  duplicateSelection: \"Izveidot kopiju\",\n  untitled: \"Bez nosaukuma\",\n  name: \"V\\u0101rds\",\n  yourName: \"J\\u016Bsu v\\u0101rds\",\n  madeWithExcalidraw: \"Rad\\u012Bts ar Excalidraw\",\n  group: \"Grup\\u0113t atlas\\u012Bto\",\n  ungroup: \"Atgrup\\u0113t atlas\\u012Bto\",\n  collaborators: \"Dal\\u012Bbnieki\",\n  showGrid: \"R\\u0101d\\u012Bt re\\u017E\\u0123i\",\n  addToLibrary: \"Pievienot bibliot\\u0113kai\",\n  removeFromLibrary: \"Iz\\u0146emt no bibliot\\u0113kas\",\n  libraryLoadingMessage: \"Iel\\u0101d\\u0113 bibliot\\u0113ku\\u2026\",\n  libraries: \"Apskat\\u012Bt bibliot\\u0113kas\",\n  loadingScene: \"Iel\\u0101d\\u0113 ainu\\u2026\",\n  align: \"L\\u012Bdzin\\u0101t\",\n  alignTop: \"L\\u012Bdzin\\u0101t aug\\u0161pus\\u0113\",\n  alignBottom: \"L\\u012Bdzin\\u0101t lej\\u0101\",\n  alignLeft: \"L\\u012Bdzin\\u0101t pa kreisi\",\n  alignRight: \"L\\u012Bdzin\\u0101t pa labi\",\n  centerVertically: \"Centr\\u0113t vertik\\u0101li\",\n  centerHorizontally: \"Centr\\u0113t horizont\\u0101li\",\n  distributeHorizontally: \"Izdal\\u012Bt horizont\\u0101li\",\n  distributeVertically: \"Izdal\\u012Bt vertik\\u0101li\",\n  flipHorizontal: \"Apmest horizont\\u0101li\",\n  flipVertical: \"Apmest vertik\\u0101li\",\n  viewMode: \"Skata re\\u017E\\u012Bms\",\n  share: \"Kop\\u012Bgot\",\n  showStroke: \"R\\u0101d\\u012Bt sv\\u012Btras kr\\u0101sas atlas\\u012Bt\\u0101ju\",\n  showBackground: \"R\\u0101d\\u012Bt fona kr\\u0101sas atlas\\u012Bt\\u0101ju\",\n  toggleTheme: \"P\\u0101rsl\\u0113gt kr\\u0101su t\\u0113mu\",\n  personalLib: \"Person\\u012Bg\\u0101 bibliot\\u0113ka\",\n  excalidrawLib: \"Excalidraw bibliot\\u0113ka\",\n  decreaseFontSize: \"Samazin\\u0101t fonta izm\\u0113ru\",\n  increaseFontSize: \"Palielin\\u0101t fonta izm\\u0113ru\",\n  unbindText: \"Atdal\\u012Bt tekstu\",\n  bindText: \"Piesaist\\u012Bt tekstu fig\\u016Brai\",\n  createContainerFromText: \"Ietilpin\\u0101t tekstu figur\\u0101\",\n  link: {\n    edit: \"Redi\\u0123\\u0113t saiti\",\n    editEmbed: \"\",\n    create: \"Izveidot saiti\",\n    createEmbed: \"\",\n    label: \"Saite\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Redi\\u0123\\u0113t l\\u012Bniju\",\n    exit: \"Aizv\\u0113rt l\\u012Bnijas redaktoru\"\n  },\n  elementLock: {\n    lock: \"Fiks\\u0113t\",\n    unlock: \"Atbr\\u012Bvot\",\n    lockAll: \"Fiks\\u0113t visu\",\n    unlockAll: \"Atbr\\u012Bvot visu\"\n  },\n  statusPublished: \"Public\\u0113ts\",\n  sidebarLock: \"Patur\\u0113t atv\\u0113rtu s\\u0101njoslu\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Neviena vien\\u012Bba v\\u0113l nav pievienota...\",\n  hint_emptyLibrary: \"Atlasiet objektu t\\u0101fel\\u0113, lai to \\u0161eit pievienotu, vai pievienojiet publisku bibliot\\u0113ku zem\\u0101k.\",\n  hint_emptyPrivateLibrary: \"Atlasiet objektu t\\u0101fel\\u0113, lai to \\u0161eit pievienotu.\"\n};\nvar buttons = {\n  clearReset: \"Atiestat\\u012Bt t\\u0101feli\",\n  exportJSON: \"Eksport\\u0113t k\\u0101 failu\",\n  exportImage: \"Eksport\\u0113t att\\u0113lu...\",\n  export: \"Saglab\\u0101t uz...\",\n  copyToClipboard: \"Kop\\u0113t starpliktuv\\u0113\",\n  save: \"Saglab\\u0101t pa\\u0161reiz\\u0113jo datni\",\n  saveAs: \"Saglab\\u0101t k\\u0101\",\n  load: \"Atv\\u0113rt\",\n  getShareableLink: \"Ieg\\u016Bt kop\\u012Bgo\\u0161anas saiti\",\n  close: \"Aizv\\u0113rt\",\n  selectLanguage: \"Izv\\u0113lieties valodu\",\n  scrollBackToContent: \"Atgriezties pie satura\",\n  zoomIn: \"Tuvin\\u0101t\",\n  zoomOut: \"T\\u0101lin\\u0101t\",\n  resetZoom: \"Atiestat\\u012Bt tuvin\\u0101jumu\",\n  menu: \"Izv\\u0113lne\",\n  done: \"Gatavs\",\n  edit: \"Redi\\u0123\\u0113t\",\n  undo: \"Atsaukt\",\n  redo: \"Atcelt atsauk\\u0161anu\",\n  resetLibrary: \"Atiestat\\u012Bt bibliot\\u0113ku\",\n  createNewRoom: \"Izveidot jaunu telpu\",\n  fullScreen: \"Pilnekr\\u0101na re\\u017E\\u012Bms\",\n  darkMode: \"Tum\\u0161ais re\\u017E\\u012Bms\",\n  lightMode: \"Gai\\u0161ais re\\u017E\\u012Bms\",\n  zenMode: \"Zen re\\u017E\\u012Bms\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Pamest Zen re\\u017E\\u012Bmu\",\n  cancel: \"Atcelt\",\n  clear: \"Not\\u012Br\\u012Bt\",\n  remove: \"No\\u0146emt\",\n  embed: \"\",\n  publishLibrary: \"Public\\u0113t\",\n  submit: \"Iesniegt\",\n  confirm: \"Apstiprin\\u0101t\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"\\u0160\\u012B funkcija not\\u012Br\\u012Bs visu t\\u0101feli. Vai turpin\\u0101t?\",\n  couldNotCreateShareableLink: \"Nevar\\u0113ja izveidot kop\\u012Bgojamo saiti.\",\n  couldNotCreateShareableLinkTooBig: \"Nevar\\u0113ja izveidot kop\\u012Bgojamo saiti \\u2013 aina ir par lielu\",\n  couldNotLoadInvalidFile: \"Nevar\\u0113ja iel\\u0101d\\u0113t neder\\u012Bgu datni\",\n  importBackendFailed: \"Iel\\u0101de no kr\\u0101tuves neizdev\\u0101s.\",\n  cannotExportEmptyCanvas: \"Nevar eksport\\u0113t tuk\\u0161u t\\u0101feli.\",\n  couldNotCopyToClipboard: \"Nevar\\u0113ja nokop\\u0113t starpliktuv\\u0113.\",\n  decryptFailed: \"Nevar\\u0113ja at\\u0161ifr\\u0113t datus.\",\n  uploadedSecurly: \"Aug\\u0161upl\\u0101de nodro\\u0161in\\u0101ta ar \\u0161ifr\\u0113\\u0161anu no gala l\\u012Bdz galam, kas noz\\u012Bm\\u0113, ka Excalidraw serveri un tre\\u0161\\u0101s puses nevar las\\u012Bt saturu.\",\n  loadSceneOverridePrompt: \"\\u0100r\\u0113ja satura iel\\u0101de aizst\\u0101s j\\u016Bsu pa\\u0161reiz\\u0113jo saturu. Vai v\\u0113laties turpin\\u0101t?\",\n  collabStopOverridePrompt: \"Sesijas p\\u0101rtrauk\\u0161ana p\\u0101rrakst\\u012Bs j\\u016Bsu iepriek\\u0161\\u0113jo z\\u012Bm\\u0113jumu, kas saglab\\u0101ts j\\u016Bsu p\\u0101rl\\u016Bk\\u0101. Vai turpin\\u0101t?\\n\\n(Ja v\\u0113laties patur\\u0113t z\\u012Bm\\u0113jumu, kas saglab\\u0101ts j\\u016Bsu p\\u0101rl\\u016Bk\\u0101, vienk\\u0101r\\u0161i aizveriet p\\u0101rl\\u016Bka cilni.)\",\n  errorAddingToLibrary: \"Nevar\\u0113ja pievienot vienumu bibliot\\u0113kai\",\n  errorRemovingFromLibrary: \"Nevar\\u0113ja iz\\u0146emt vienumu no bibliot\\u0113kas\",\n  confirmAddLibrary: \"\\u0160\\u012B funkcija pievienos {{numShapes}} formu(-as) j\\u016Bsu bibliot\\u0113kai. Vai turpin\\u0101t?\",\n  imageDoesNotContainScene: \"\\u0160\\u0137iet, ka att\\u0113ls nesatur ainas datus. Vai iesp\\u0113joj\\u0101t ainas iegul\\u0161anu, kad eksport\\u0113j\\u0101t?\",\n  cannotRestoreFromImage: \"Ainu nevar\\u0113ja atg\\u016Bt no att\\u0113la datnes\",\n  invalidSceneUrl: \"Nevar\\u0113ja import\\u0113t ainu no nor\\u0101d\\u012Bt\\u0101 URL. Vai nu tas ir neder\\u012Bgs, vai nesatur der\\u012Bgus Excalidraw JSON datus.\",\n  resetLibrary: \"\\u0160\\u012B funkcija iztuk\\u0161os bibliot\\u0113ku. Vai turpin\\u0101t?\",\n  removeItemsFromsLibrary: \"Vai iz\\u0146emt {{count}} vienumu(s) no bibliot\\u0113kas?\",\n  invalidEncryptionKey: \"\\u0160ifr\\u0113\\u0161anas atsl\\u0113gai j\\u0101b\\u016Bt 22 simbolus garai. Tie\\u0161saistes sadarb\\u012Bba ir izsl\\u0113gta.\",\n  collabOfflineWarning: \"Nav pieejams interneta piesl\\u0113gums.\\nJ\\u016Bsu izmai\\u0146as netiks saglab\\u0101tas!\"\n};\nvar errors = {\n  unsupportedFileType: \"Neatbalst\\u012Bts datnes veids.\",\n  imageInsertError: \"Nevar\\u0113ja ievietot att\\u0113lu. M\\u0113\\u0123iniet v\\u0113l\\u0101k...\",\n  fileTooBig: \"Datne ir par lielu. Liel\\u0101kais at\\u013Cautais izm\\u0113rs ir {{maxSize}}.\",\n  svgImageInsertError: \"Nevar\\u0113ja ievietot SVG att\\u0113lu. \\u0160\\u0137iet, ka SVG mar\\u0137\\u0113jums nav der\\u012Bgs.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Neder\\u012Bgs SVG.\",\n  cannotResolveCollabServer: \"Nevar\\u0113ja savienoties ar sadarbo\\u0161an\\u0101s serveri. L\\u016Bdzu, p\\u0101rl\\u0101d\\u0113jiet lapu un m\\u0113\\u0123iniet v\\u0113lreiz.\",\n  importLibraryError: \"Nevar\\u0113ja iel\\u0101d\\u0113t bibliot\\u0113ku\",\n  collabSaveFailed: \"Darbs nav saglab\\u0101ts datub\\u0101z\\u0113. Ja probl\\u0113ma turpin\\u0101s, saglab\\u0101jiet datni lok\\u0101laj\\u0101 kr\\u0101tuv\\u0113, lai nodro\\u0161in\\u0101tos pret darba pazaud\\u0113\\u0161anu.\",\n  collabSaveFailed_sizeExceeded: \"Darbs nav saglab\\u0101ts datub\\u0101z\\u0113, \\u0161\\u0137iet, ka t\\u0101fele ir p\\u0101r\\u0101k liela. Saglab\\u0101jiet datni lok\\u0101laj\\u0101 kr\\u0101tuv\\u0113, lai nodro\\u0161in\\u0101tos pret darba pazaud\\u0113\\u0161anu.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Atlase\",\n  image: \"Ievietot att\\u0113lu\",\n  rectangle: \"Taisnst\\u016Bris\",\n  diamond: \"Rombs\",\n  ellipse: \"Elipse\",\n  arrow: \"Bulta\",\n  line: \"L\\u012Bnija\",\n  freedraw: \"Z\\u012Bm\\u0113t\",\n  text: \"Teksts\",\n  library: \"Bibliot\\u0113ka\",\n  lock: \"Patur\\u0113t izv\\u0113l\\u0113to r\\u012Bku p\\u0113c darb\\u012Bbas\",\n  penMode: \"Pildspalvas re\\u017E\\u012Bms \\u2013 nov\\u0113rst pieskar\\u0161anos\",\n  link: \"Pievienot/redi\\u0123\\u0113t atlas\\u012Bt\\u0101s fig\\u016Bras saiti\",\n  eraser: \"Dz\\u0113\\u0161gumija\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"Roka (panoram\\u0113\\u0161anas r\\u012Bks)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"T\\u0101feles darb\\u012Bbas\",\n  selectedShapeActions: \"Izv\\u0113l\\u0113t\\u0101s formas darb\\u012Bbas\",\n  shapes: \"Formas\"\n};\nvar hints = {\n  canvasPanning: \"Lai b\\u012Bd\\u012Btu t\\u0101feli, turiet nospiestu ritin\\u0101\\u0161anas vai atstarpes tausti\\u0146u, vai izmanto rokas r\\u012Bku\",\n  linearElement: \"Klik\\u0161\\u0137iniet, lai s\\u0101ktu z\\u012Bm\\u0113t vair\\u0101kus punktus; velciet, lai z\\u012Bm\\u0113tu l\\u012Bniju\",\n  freeDraw: \"Spiediet un velciet; atlaidiet, kad pabeidzat\",\n  text: \"Ieteikums: lai pievienotu tekstu, varat ar\\u012B jebkur dubultklik\\u0161\\u0137in\\u0101t ar atlases r\\u012Bku\",\n  embeddable: \"\",\n  text_selected: \"Dubultklik\\u0161\\u0137iniet vai spiediet ievades tausti\\u0146u, lai redi\\u0123\\u0113tu tekstu\",\n  text_editing: \"Spiediet izie\\u0161anas tausti\\u0146u vai CtrlOrCmd+ENTER, lai beigtu redi\\u0123\\u0113t\",\n  linearElementMulti: \"Klik\\u0161\\u0137iniet uz p\\u0113d\\u0113j\\u0101 punkta vai spiediet izejas vai ievades tausti\\u0146u, lai pabeigtu\",\n  lockAngle: \"Varat ierobe\\u017Eot le\\u0146\\u0137i, turot nospiestu SHIFT\",\n  resize: \"Kad main\\u0101t izm\\u0113ru, varat ierobe\\u017Eot proporcijas, turot nospiestu SHIFT,\\nvai ar\\u012B ALT, lai main\\u012Btu izm\\u0113ru ap centru\",\n  resizeImage: \"Varat br\\u012Bvi main\\u012Bt izm\\u0113ru, turot nospiestu SHIFT;\\nturiet nospiestu ALT, lai main\\u012Btu izm\\u0113ru ap centru\",\n  rotate: \"Rot\\u0113jot varat ierobe\\u017Eot le\\u0146\\u0137i, turot nospiestu SHIFT\",\n  lineEditor_info: \"Turiet CtrlOrCmd un dubultklik\\u0161\\u0137iniet, vai spiediet CtrlOrCmd + Enter, lai redi\\u0123\\u0113tu punktus\",\n  lineEditor_pointSelected: \"Spiediet dz\\u0113\\u0161anas tausti\\u0146u, lai no\\u0146emtu punktus, \\u2013 CtrlOrCmd+D, lai to kop\\u0113tu, vai velciet, lai p\\u0101rvietotu\",\n  lineEditor_nothingSelected: \"Atlasiet punktu, lai labotu (turiet nospiestu SHIFT, lai atlas\\u012Btu vair\\u0101kus),\\nvai turiet Alt un clik\\u0161\\u0137iniet, lai pievienotu jaunus punktus\",\n  placeImage: \"Klik\\u0161\\u0137iniet, lai novietotu att\\u0113lu, vai spiediet un velciet, lai iestat\\u012Btu t\\u0101 izm\\u0113ru\",\n  publishLibrary: \"Public\\u0113t savu bibliot\\u0113ku\",\n  bindTextToElement: \"Spiediet ievades tausti\\u0146u, lai pievienotu tekstu\",\n  deepBoxSelect: \"Turient nospiestu Ctrl vai Cmd, lai atlas\\u012Btu dzi\\u013Cum\\u0101 un lai nepie\\u013Cautu objektu pavilk\\u0161anu\",\n  eraserRevert: \"Turiet Alt, lai no\\u0146emtu elementus no dz\\u0113s\\u0161anas atlases\",\n  firefox_clipboard_write: '\\u0160is iestat\\u012Bjums var tikt iesl\\u0113gts ar \"dom.events.asyncClipboard.clipboardItem\" mar\\u0137ieri p\\u0101rsl\\u0113gtu uz \"true\". Lai main\\u012Btu p\\u0101rl\\u016Bka mar\\u0137ierus Firefox, apmekl\\u0113 \"about:config\" lapu.',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Nevar r\\u0101d\\u012Bt priek\\u0161skat\\u012Bjumu\",\n  canvasTooBig: \"Iesp\\u0113jams, t\\u0101fele ir par lielu.\",\n  canvasTooBigTip: \"Ieteikums: m\\u0113\\u0123iniet satuvin\\u0101t pa\\u0161us t\\u0101l\\u0101kos elementus.\"\n};\nvar errorSplash = {\n  headingMain: \"Notikusi k\\u013C\\u016Bda. M\\u0113\\u0123iniet <button>p\\u0101rl\\u0101d\\u0113t lapu.</button>\",\n  clearCanvasMessage: \"Ja p\\u0101rl\\u0101d\\u0113\\u0161ana nestr\\u0101d\\u0101, m\\u0113\\u0123iniet <button>not\\u012Brot t\\u0101feli.</button>\",\n  clearCanvasCaveat: \" Tas noved\\u012Bs pie darba zaud\\u0113\\u0161anas \",\n  trackedToSentry: \"K\\u013C\\u016Bda ar kodu {{eventId}} tika noteikta m\\u016Bsu sist\\u0113m\\u0101.\",\n  openIssueMessage: \"M\\u0113s uzman\\u012Bj\\u0101mies, lai neiek\\u013Cautu j\\u016Bsu ainas inform\\u0101ciju \\u0161aj\\u0101 k\\u013C\\u016Bd\\u0101. Ja j\\u016Bsu aina nav priv\\u0101ta, l\\u016Bdzu zi\\u0146ojiet par \\u0161o k\\u013C\\u016Bdu m\\u016Bsu <button>k\\u013C\\u016Bdu uzskait\\u0113.</button> L\\u016Bdzu, miniet sekojo\\u0161o inform\\u0101ciju to kop\\u0113jot un iel\\u012Bm\\u0113jot j\\u016Bsu zi\\u0146ojum\\u0101 platform\\u0101 GitHub.\",\n  sceneContent: \"Ainas saturs:\"\n};\nvar roomDialog = {\n  desc_intro: \"Varat iel\\u016Bgt cilv\\u0113kus pa\\u0161reiz\\u0113jaj\\u0101 ain\\u0101, lai sadarbotos ar tiem.\",\n  desc_privacy: \"Neuztraucieties, sesija izmanto \\u0161ifr\\u0113\\u0161anu no gala l\\u012Bdz galam, t\\u0101tad j\\u016Bsu z\\u012Bm\\u0113jums paliks priv\\u0101ts. Pat m\\u016Bsu serveri nevar\\u0113s redz\\u0113t, ar ko esat n\\u0101cis klaj\\u0101.\",\n  button_startSession: \"S\\u0101kt sesiju\",\n  button_stopSession: \"Beigt sesiju\",\n  desc_inProgressIntro: \"Notiek tie\\u0161saistes sadarb\\u012Bbas sesija.\",\n  desc_shareLink: \"Dalieties ar \\u0161o saiti ar jebkuru, ar ko v\\u0113laties sadarboties:\",\n  desc_exitSession: \"Sesijas beig\\u0161ana j\\u016Bs atvienos no sadarbo\\u0161an\\u0101s, bet j\\u016Bs v\\u0113l joproj\\u0101m var\\u0113siet str\\u0101d\\u0101t ar ainu sav\\u0101 dator\\u0101. Iev\\u0113rojiet, ka \\u0161is neietekm\\u0113s citus dal\\u012Bbniekus, un vi\\u0146i v\\u0113l joproj\\u0101m var\\u0113s sadarboties sav\\u0101 ainas versij\\u0101.\",\n  shareTitle: \"Pievienoties tie\\u0161saistes sadarb\\u012Bbai programm\\u0101 Excalidraw\"\n};\nvar errorDialog = {\n  title: \"K\\u013C\\u016Bda\"\n};\nvar exportDialog = {\n  disk_title: \"Saglab\\u0101t disk\\u0101\",\n  disk_details: \"Eksport\\u0113t ainas datus datn\\u0113, ko v\\u0113l\\u0101k var\\u0113siet import\\u0113t.\",\n  disk_button: \"Saglab\\u0101t datn\\u0113\",\n  link_title: \"Kop\\u012Bgo\\u0161anas saite\",\n  link_details: \"Eksport\\u0113t k\\u0101 tikai las\\u0101mu saiti.\",\n  link_button: \"Eksport\\u0113t k\\u0101 saiti\",\n  excalidrawplus_description: \"Saglab\\u0101t ainu sav\\u0101 Excalidraw+ darbviet\\u0101.\",\n  excalidrawplus_button: \"Eksport\\u0113t\",\n  excalidrawplus_exportError: \"Pa\\u0161reiz nevar\\u0113ja eksport\\u0113t uz Excalidraw+...\"\n};\nvar helpDialog = {\n  blog: \"Las\\u012Bt m\\u016Bsu blogu\",\n  click: \"klik\\u0161\\u0137is\",\n  deepSelect: \"Atlas\\u012Bt dzi\\u013Cum\\u0101\",\n  deepBoxSelect: \"Atlas\\u012Bt dzi\\u013Cum\\u0101 kastes ietvaros, un nepie\\u013Caut pavilk\\u0161anu\",\n  curvedArrow: \"Liekta bulta\",\n  curvedLine: \"Liekta l\\u012Bnija\",\n  documentation: \"Dokument\\u0101cija\",\n  doubleClick: \"dubultklik\\u0161\\u0137is\",\n  drag: \"vilkt\",\n  editor: \"Redaktors\",\n  editLineArrowPoints: \"Redi\\u0123\\u0113t l\\u012Bniju/bultu punktus\",\n  editText: \"Redi\\u0123\\u0113t tekstu/pievienot birku\",\n  github: \"Sastap\\u0101t k\\u013C\\u016Bdu? Zi\\u0146ot\",\n  howto: \"Sekojiet m\\u016Bsu instrukcij\\u0101m\",\n  or: \"vai\",\n  preventBinding: \"Nov\\u0113rst bultu piesaist\\u012B\\u0161anos\",\n  tools: \"R\\u012Bki\",\n  shortcuts: \"Tastat\\u016Bras sa\\u012Bsnes\",\n  textFinish: \"Pabeigt redi\\u0123\\u0113\\u0161anu (teksta redaktor\\u0101)\",\n  textNewLine: \"N\\u0101kam\\u0101 rindi\\u0146a (teksta redaktor\\u0101)\",\n  title: \"Pal\\u012Bdz\\u012Bba\",\n  view: \"Skat\\u012Bt\",\n  zoomToFit: \"Iestat\\u012Bt m\\u0113rogu, kas iek\\u013Cauj visus elementus\",\n  zoomToSelection: \"Iestat\\u012Bt m\\u0113rogu, lai r\\u0101d\\u012Btu atlasi\",\n  toggleElementLock: \"Fiks\\u0113t/atbr\\u012Bvot atlas\\u012Bto\",\n  movePageUpDown: \"P\\u0101rvietot lapu aug\\u0161up/lejup\",\n  movePageLeftRight: \"P\\u0101rvietot lapu pa labi/kreisi\"\n};\nvar clearCanvasDialog = {\n  title: \"Not\\u012Br\\u012Bt t\\u0101feli\"\n};\nvar publishDialog = {\n  title: \"Public\\u0113t bibliot\\u0113ku\",\n  itemName: \"Vienuma nosaukums\",\n  authorName: \"Autora v\\u0101rds\",\n  githubUsername: \"GitHub lietot\\u0101jv\\u0101rds\",\n  twitterUsername: \"Twitter lietot\\u0101jv\\u0101rds\",\n  libraryName: \"Bibliot\\u0113kas nosaukums\",\n  libraryDesc: \"Bibliot\\u0113kas apraksts\",\n  website: \"M\\u0101jaslapa\",\n  placeholder: {\n    authorName: \"J\\u016Bsu v\\u0101rds vai lietot\\u0101jv\\u0101rds\",\n    libraryName: \"J\\u016Bsu bibliot\\u0113kas nosaukums\",\n    libraryDesc: \"Bibliot\\u0113kas apraksts, kas pal\\u012Bdz\\u0113s citiem saprast t\\u0101s pielietojumu\",\n    githubHandle: \"GitHub lietot\\u0101jv\\u0101rds (neoblig\\u0101ts), lai j\\u016Bs var\\u0113tu redi\\u0123\\u0113t bibliot\\u0113ku p\\u0113c t\\u0101s iesnieg\\u0161anas izskat\\u012B\\u0161anai\",\n    twitterHandle: \"Twitter lietot\\u0101jv\\u0101rds (neoblig\\u0101ts), lai m\\u0113s var\\u0113tu j\\u016Bs piemin\\u0113t k\\u0101 autoru, kad reklam\\u0113sim bibliot\\u0113ku platform\\u0101 Twitter\",\n    website: \"Saikne uz j\\u016Bsu person\\u012Bgo m\\u0101jaslapu vai k\\u0101du citu lapu (neoblig\\u0101ta)\"\n  },\n  errors: {\n    required: \"Oblig\\u0101ts\",\n    website: \"Ievadiet der\\u012Bgu URL\"\n  },\n  noteDescription: \"Iesniegt savu bibliot\\u0113ku iek\\u013Cau\\u0161anai <link>publiskaj\\u0101 bibliot\\u0113ku datub\\u0101z\\u0113</link>, lai citi to var\\u0113tu izmantot savos z\\u012Bm\\u0113jumos.\",\n  noteGuidelines: \"\\u0160ai bibliot\\u0113kai vispirms j\\u0101tiek manu\\u0101li apstiprin\\u0101tai. L\\u016Bdzu, izlasiet <link>nor\\u0101d\\u012Bjumus</link> pirms iesnieg\\u0161anas. Jums vajadz\\u0113s GitHub kontu, lai sazin\\u0101tos un veiktu izmai\\u0146as, ja t\\u0101das b\\u016Bs piepras\\u012Btas, bet tas nav absol\\u016Bti nepiecie\\u0161ams.\",\n  noteLicense: \"Iesniedzot bibliot\\u0113ku, j\\u016Bs piekr\\u012Btat t\\u0101s public\\u0113\\u0161anai saska\\u0146\\u0101 ar <link>MIT Licenci, </link>kas \\u012Bsum\\u0101 noz\\u012Bm\\u0113, ka jebkur\\u0161 to var\\u0113s izmantot bez ierobe\\u017Eojumiem.\",\n  noteItems: \"Katram bibliot\\u0113kas vienumam j\\u0101b\\u016Bt savam nosaukumam, lai to var\\u0113tu atrast filtr\\u0113jot. Tiks iek\\u013Cauti sekojo\\u0161ie bibliot\\u0113kas vienumi:\",\n  atleastOneLibItem: \"L\\u016Bdzu, atlasiet vismaz vienu bibliot\\u0113kas vienumu, lai s\\u0101ktu darbu\",\n  republishWarning: \"Iev\\u0113ro: da\\u017Ei no atz\\u012Bm\\u0113tajiem objektiem jau atz\\u012Bm\\u0113ti k\\u0101 public\\u0113ti vai iesniegti public\\u0113\\u0161anai. Tos vajadz\\u0113tu atk\\u0101rtoti iesniegt tikai tad, ja v\\u0113lies labot eso\\u0161o bibliot\\u0113ku.\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliot\\u0113ka iesniegta\",\n  content: \"Paldies, {{authorName}}! J\\u016Bsu bibliot\\u0113ka iesniegta izskat\\u012B\\u0161anai. J\\u016Bs varat izsekot iesnieguma statusam<link>\\u0161eit</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Atiestat\\u012Bt bibliot\\u0113ku\",\n  removeItemsFromLib: \"No\\u0146emt atlas\\u012Btos vienumus no bibliot\\u0113kas\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"J\\u016Bsu z\\u012Bm\\u0113jumi ir \\u0161ifr\\u0113ti no gala l\\u012Bdz galam; l\\u012Bdz ar to Excalidraw serveri tos nekad neredz\\u0113s.\",\n  link: \"Ieraksts par \\u0161ifr\\u0113\\u0161anu no gala l\\u012Bdz galam Excalidraw blog\\u0101\"\n};\nvar stats = {\n  angle: \"Le\\u0146\\u0137is\",\n  element: \"Elements\",\n  elements: \"Elementi\",\n  height: \"Augstums\",\n  scene: \"Aina\",\n  selected: \"Atlas\\u012Bti\",\n  storage: \"Kr\\u0101tuve\",\n  title: \"Statistika entuziastiem\",\n  total: \"Kop\\u0101\",\n  version: \"Versija\",\n  versionCopy: \"Klik\\u0161\\u0137iniet, lai nokop\\u0113tu\",\n  versionNotAvailable: \"Versija nav pieejama\",\n  width: \"Platums\"\n};\nvar toast = {\n  addedToLibrary: \"Pievienots bibliot\\u0113kai\",\n  copyStyles: \"Nokop\\u0113ja stilus.\",\n  copyToClipboard: \"Nokop\\u0113ja starpliktuv\\u0113.\",\n  copyToClipboardAsPng: \"Nokop\\u0113ja {{exportSelection}} starpliktuv\\u0113 k\\u0101 PNG ({{exportColorScheme}})\",\n  fileSaved: \"Datne saglab\\u0101ta.\",\n  fileSavedToFilename: \"Saglab\\u0101ts k\\u0101 {filename}\",\n  canvas: \"t\\u0101feli\",\n  selection: \"atlasi\",\n  pasteAsSingleElement: \"Izmantojiet {{shortcut}}, lai iel\\u012Bm\\u0113tu k\\u0101 jaunu elementu, vai iel\\u012Bm\\u0113tu eso\\u0161\\u0101 teksta lauci\\u0146\\u0101\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Caursp\\u012Bd\\u012Bgs\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Visi j\\u016Bsu dati tiek glab\\u0101ti uz vietas j\\u016Bsu p\\u0101rl\\u016Bk\\u0101.\",\n    center_heading_plus: \"Vai t\\u0101 viet\\u0101 v\\u0113lies doties uz Excalidraw+?\",\n    menuHint: \"Eksport\\u0113\\u0161ana, iestat\\u012Bjumi, valodas...\"\n  },\n  defaults: {\n    menuHint: \"Eksport\\u0113\\u0161ana, iestat\\u012Bjumi un v\\u0113l...\",\n    center_heading: \"Diagrammas. Izveidotas. Vienk\\u0101r\\u0161i.\",\n    toolbarHint: \"Izv\\u0113lies r\\u012Bku un s\\u0101c z\\u012Bm\\u0113t!\",\n    helpHint: \"\\u012Asce\\u013Ci un pal\\u012Bdz\\u012Bba\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar lv_LV_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=lv-LV-MD7N5VHD.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/lv-LV-MD7N5VHD.js\n"));

/***/ })

}]);