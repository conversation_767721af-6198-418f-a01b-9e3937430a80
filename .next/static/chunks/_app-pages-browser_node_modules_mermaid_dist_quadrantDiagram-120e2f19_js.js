"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_quadrantDiagram-120e2f19_js"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/quadrantDiagram-120e2f19.js":
/*!***************************************************************!*\
  !*** ./node_modules/mermaid/dist/quadrantDiagram-120e2f19.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [1, 7], $V5 = [1, 5, 13, 15, 17, 19, 20, 25, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $V6 = [1, 5, 6, 13, 15, 17, 19, 20, 25, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $V7 = [32, 33, 34], $V8 = [2, 7], $V9 = [1, 13], $Va = [1, 17], $Vb = [1, 18], $Vc = [1, 19], $Vd = [1, 20], $Ve = [1, 21], $Vf = [1, 22], $Vg = [1, 23], $Vh = [1, 24], $Vi = [1, 25], $Vj = [1, 26], $Vk = [1, 27], $Vl = [1, 30], $Vm = [1, 31], $Vn = [1, 32], $Vo = [1, 33], $Vp = [1, 34], $Vq = [1, 35], $Vr = [1, 36], $Vs = [1, 37], $Vt = [1, 38], $Vu = [1, 39], $Vv = [1, 40], $Vw = [1, 41], $Vx = [1, 42], $Vy = [1, 57], $Vz = [1, 58], $VA = [5, 22, 26, 32, 33, 34, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"SPACE\": 5, \"QUADRANT\": 6, \"document\": 7, \"line\": 8, \"statement\": 9, \"axisDetails\": 10, \"quadrantDetails\": 11, \"points\": 12, \"title\": 13, \"title_value\": 14, \"acc_title\": 15, \"acc_title_value\": 16, \"acc_descr\": 17, \"acc_descr_value\": 18, \"acc_descr_multiline_value\": 19, \"section\": 20, \"text\": 21, \"point_start\": 22, \"point_x\": 23, \"point_y\": 24, \"X-AXIS\": 25, \"AXIS-TEXT-DELIMITER\": 26, \"Y-AXIS\": 27, \"QUADRANT_1\": 28, \"QUADRANT_2\": 29, \"QUADRANT_3\": 30, \"QUADRANT_4\": 31, \"NEWLINE\": 32, \"SEMI\": 33, \"EOF\": 34, \"alphaNumToken\": 35, \"textNoTagsToken\": 36, \"STR\": 37, \"MD_STR\": 38, \"alphaNum\": 39, \"PUNCTUATION\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"COMMA\": 44, \"PLUS\": 45, \"EQUALS\": 46, \"MULT\": 47, \"DOT\": 48, \"BRKT\": 49, \"UNDERSCORE\": 50, \"MINUS\": 51, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"SPACE\", 6: \"QUADRANT\", 13: \"title\", 14: \"title_value\", 15: \"acc_title\", 16: \"acc_title_value\", 17: \"acc_descr\", 18: \"acc_descr_value\", 19: \"acc_descr_multiline_value\", 20: \"section\", 22: \"point_start\", 23: \"point_x\", 24: \"point_y\", 25: \"X-AXIS\", 26: \"AXIS-TEXT-DELIMITER\", 27: \"Y-AXIS\", 28: \"QUADRANT_1\", 29: \"QUADRANT_2\", 30: \"QUADRANT_3\", 31: \"QUADRANT_4\", 32: \"NEWLINE\", 33: \"SEMI\", 34: \"EOF\", 37: \"STR\", 38: \"MD_STR\", 40: \"PUNCTUATION\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"COMMA\", 45: \"PLUS\", 46: \"EQUALS\", 47: \"MULT\", 48: \"DOT\", 49: \"BRKT\", 50: \"UNDERSCORE\", 51: \"MINUS\" },\n    productions_: [0, [3, 2], [3, 2], [3, 2], [7, 0], [7, 2], [8, 2], [9, 0], [9, 2], [9, 1], [9, 1], [9, 1], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [12, 4], [10, 4], [10, 3], [10, 2], [10, 4], [10, 3], [10, 2], [11, 2], [11, 2], [11, 2], [11, 2], [4, 1], [4, 1], [4, 1], [21, 1], [21, 2], [21, 1], [21, 1], [39, 1], [39, 2], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [35, 1], [36, 1], [36, 1], [36, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 12:\n          this.$ = $$[$0].trim();\n          yy.setDiagramTitle(this.$);\n          break;\n        case 13:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 14:\n        case 15:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 16:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 17:\n          yy.addPoint($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          break;\n        case 18:\n          yy.setXAxisLeftText($$[$0 - 2]);\n          yy.setXAxisRightText($$[$0]);\n          break;\n        case 19:\n          $$[$0 - 1].text += \" ⟶ \";\n          yy.setXAxisLeftText($$[$0 - 1]);\n          break;\n        case 20:\n          yy.setXAxisLeftText($$[$0]);\n          break;\n        case 21:\n          yy.setYAxisBottomText($$[$0 - 2]);\n          yy.setYAxisTopText($$[$0]);\n          break;\n        case 22:\n          $$[$0 - 1].text += \" ⟶ \";\n          yy.setYAxisBottomText($$[$0 - 1]);\n          break;\n        case 23:\n          yy.setYAxisBottomText($$[$0]);\n          break;\n        case 24:\n          yy.setQuadrant1Text($$[$0]);\n          break;\n        case 25:\n          yy.setQuadrant2Text($$[$0]);\n          break;\n        case 26:\n          yy.setQuadrant3Text($$[$0]);\n          break;\n        case 27:\n          yy.setQuadrant4Text($$[$0]);\n          break;\n        case 31:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 32:\n          this.$ = { text: $$[$0 - 1].text + \"\" + $$[$0], type: $$[$0 - 1].type };\n          break;\n        case 33:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 34:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 35:\n          this.$ = $$[$0];\n          break;\n        case 36:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: 2, 5: $V0, 6: $V1, 32: $V2, 33: $V3, 34: $V4 }, { 1: [3] }, { 3: 8, 4: 2, 5: $V0, 6: $V1, 32: $V2, 33: $V3, 34: $V4 }, { 3: 9, 4: 2, 5: $V0, 6: $V1, 32: $V2, 33: $V3, 34: $V4 }, o($V5, [2, 4], { 7: 10 }), o($V6, [2, 28]), o($V6, [2, 29]), o($V6, [2, 30]), { 1: [2, 1] }, { 1: [2, 2] }, o($V7, $V8, { 8: 11, 9: 12, 10: 14, 11: 15, 12: 16, 21: 28, 35: 29, 1: [2, 3], 5: $V9, 13: $Va, 15: $Vb, 17: $Vc, 19: $Vd, 20: $Ve, 25: $Vf, 27: $Vg, 28: $Vh, 29: $Vi, 30: $Vj, 31: $Vk, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }), o($V5, [2, 5]), { 4: 43, 32: $V2, 33: $V3, 34: $V4 }, o($V7, $V8, { 10: 14, 11: 15, 12: 16, 21: 28, 35: 29, 9: 44, 5: $V9, 13: $Va, 15: $Vb, 17: $Vc, 19: $Vd, 20: $Ve, 25: $Vf, 27: $Vg, 28: $Vh, 29: $Vi, 30: $Vj, 31: $Vk, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }), o($V7, [2, 9]), o($V7, [2, 10]), o($V7, [2, 11]), { 14: [1, 45] }, { 16: [1, 46] }, { 18: [1, 47] }, o($V7, [2, 15]), o($V7, [2, 16]), { 21: 48, 35: 29, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }, { 21: 49, 35: 29, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }, { 21: 50, 35: 29, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }, { 21: 51, 35: 29, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }, { 21: 52, 35: 29, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }, { 21: 53, 35: 29, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }, { 5: $Vy, 22: [1, 54], 35: 56, 36: 55, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }, o($VA, [2, 31]), o($VA, [2, 33]), o($VA, [2, 34]), o($VA, [2, 37]), o($VA, [2, 38]), o($VA, [2, 39]), o($VA, [2, 40]), o($VA, [2, 41]), o($VA, [2, 42]), o($VA, [2, 43]), o($VA, [2, 44]), o($VA, [2, 45]), o($VA, [2, 46]), o($VA, [2, 47]), o($V5, [2, 6]), o($V7, [2, 8]), o($V7, [2, 12]), o($V7, [2, 13]), o($V7, [2, 14]), o($V7, [2, 20], { 36: 55, 35: 56, 5: $Vy, 26: [1, 59], 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 23], { 36: 55, 35: 56, 5: $Vy, 26: [1, 60], 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 24], { 36: 55, 35: 56, 5: $Vy, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 25], { 36: 55, 35: 56, 5: $Vy, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 26], { 36: 55, 35: 56, 5: $Vy, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 27], { 36: 55, 35: 56, 5: $Vy, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), { 23: [1, 61] }, o($VA, [2, 32]), o($VA, [2, 48]), o($VA, [2, 49]), o($VA, [2, 50]), o($V7, [2, 19], { 35: 29, 21: 62, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }), o($V7, [2, 22], { 35: 29, 21: 63, 37: $Vl, 38: $Vm, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx }), { 24: [1, 64] }, o($V7, [2, 18], { 36: 55, 35: 56, 5: $Vy, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 21], { 36: 55, 35: 56, 5: $Vy, 40: $Vn, 41: $Vo, 42: $Vp, 43: $Vq, 44: $Vr, 45: $Vs, 46: $Vt, 47: $Vu, 48: $Vv, 49: $Vw, 50: $Vx, 51: $Vz }), o($V7, [2, 17])],\n    defaultActions: { 8: [2, 1], 9: [2, 2] },\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 32;\n          case 3:\n            break;\n          case 4:\n            this.begin(\"title\");\n            return 13;\n          case 5:\n            this.popState();\n            return \"title_value\";\n          case 6:\n            this.begin(\"acc_title\");\n            return 15;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n          case 8:\n            this.begin(\"acc_descr\");\n            return 17;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n          case 13:\n            return 25;\n          case 14:\n            return 27;\n          case 15:\n            return 26;\n          case 16:\n            return 28;\n          case 17:\n            return 29;\n          case 18:\n            return 30;\n          case 19:\n            return 31;\n          case 20:\n            this.begin(\"md_string\");\n            break;\n          case 21:\n            return \"MD_STR\";\n          case 22:\n            this.popState();\n            break;\n          case 23:\n            this.begin(\"string\");\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return \"STR\";\n          case 26:\n            this.begin(\"point_start\");\n            return 22;\n          case 27:\n            this.begin(\"point_x\");\n            return 23;\n          case 28:\n            this.popState();\n            break;\n          case 29:\n            this.popState();\n            this.begin(\"point_y\");\n            break;\n          case 30:\n            this.popState();\n            return 24;\n          case 31:\n            return 6;\n          case 32:\n            return 43;\n          case 33:\n            return \"COLON\";\n          case 34:\n            return 45;\n          case 35:\n            return 44;\n          case 36:\n            return 46;\n          case 37:\n            return 46;\n          case 38:\n            return 47;\n          case 39:\n            return 49;\n          case 40:\n            return 50;\n          case 41:\n            return 48;\n          case 42:\n            return 41;\n          case 43:\n            return 51;\n          case 44:\n            return 42;\n          case 45:\n            return 5;\n          case 46:\n            return 33;\n          case 47:\n            return 40;\n          case 48:\n            return 34;\n        }\n      },\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?: *x-axis *)/i, /^(?: *y-axis *)/i, /^(?: *--+> *)/i, /^(?: *quadrant-1 *)/i, /^(?: *quadrant-2 *)/i, /^(?: *quadrant-3 *)/i, /^(?: *quadrant-4 *)/i, /^(?:[\"][`])/i, /^(?:[^`\"]+)/i, /^(?:[`][\"])/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\s*:\\s*\\[\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?:\\s*\\] *)/i, /^(?:\\s*,\\s*)/i, /^(?:(1)|(0(.\\d+)?))/i, /^(?: *quadrantChart *)/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s)/i, /^(?:;)/i, /^(?:[!\"#$%&'*+,-.`?\\\\_/])/i, /^(?:$)/i],\n      conditions: { \"point_y\": { \"rules\": [30], \"inclusive\": false }, \"point_x\": { \"rules\": [29], \"inclusive\": false }, \"point_start\": { \"rules\": [27, 28], \"inclusive\": false }, \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"title\": { \"rules\": [5], \"inclusive\": false }, \"md_string\": { \"rules\": [21, 22], \"inclusive\": false }, \"string\": { \"rules\": [24, 25], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 23, 26, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nconst defaultThemeVariables = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.E)();\nclass QuadrantBuilder {\n  constructor() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n  }\n  getDefaultData() {\n    return {\n      titleText: \"\",\n      quadrant1Text: \"\",\n      quadrant2Text: \"\",\n      quadrant3Text: \"\",\n      quadrant4Text: \"\",\n      xAxisLeftText: \"\",\n      xAxisRightText: \"\",\n      yAxisBottomText: \"\",\n      yAxisTopText: \"\",\n      points: []\n    };\n  }\n  getDefaultConfig() {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r;\n    return {\n      showXAxis: true,\n      showYAxis: true,\n      showTitle: true,\n      chartHeight: ((_a = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _a.chartWidth) || 500,\n      chartWidth: ((_b = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _b.chartHeight) || 500,\n      titlePadding: ((_c = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _c.titlePadding) || 10,\n      titleFontSize: ((_d = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _d.titleFontSize) || 20,\n      quadrantPadding: ((_e = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _e.quadrantPadding) || 5,\n      xAxisLabelPadding: ((_f = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _f.xAxisLabelPadding) || 5,\n      yAxisLabelPadding: ((_g = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _g.yAxisLabelPadding) || 5,\n      xAxisLabelFontSize: ((_h = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _h.xAxisLabelFontSize) || 16,\n      yAxisLabelFontSize: ((_i = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _i.yAxisLabelFontSize) || 16,\n      quadrantLabelFontSize: ((_j = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _j.quadrantLabelFontSize) || 16,\n      quadrantTextTopPadding: ((_k = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _k.quadrantTextTopPadding) || 5,\n      pointTextPadding: ((_l = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _l.pointTextPadding) || 5,\n      pointLabelFontSize: ((_m = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _m.pointLabelFontSize) || 12,\n      pointRadius: ((_n = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _n.pointRadius) || 5,\n      xAxisPosition: ((_o = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _o.xAxisPosition) || \"top\",\n      yAxisPosition: ((_p = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _p.yAxisPosition) || \"left\",\n      quadrantInternalBorderStrokeWidth: ((_q = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _q.quadrantInternalBorderStrokeWidth) || 1,\n      quadrantExternalBorderStrokeWidth: ((_r = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.quadrantChart) == null ? void 0 : _r.quadrantExternalBorderStrokeWidth) || 2\n    };\n  }\n  getDefaultThemeConfig() {\n    return {\n      quadrant1Fill: defaultThemeVariables.quadrant1Fill,\n      quadrant2Fill: defaultThemeVariables.quadrant2Fill,\n      quadrant3Fill: defaultThemeVariables.quadrant3Fill,\n      quadrant4Fill: defaultThemeVariables.quadrant4Fill,\n      quadrant1TextFill: defaultThemeVariables.quadrant1TextFill,\n      quadrant2TextFill: defaultThemeVariables.quadrant2TextFill,\n      quadrant3TextFill: defaultThemeVariables.quadrant3TextFill,\n      quadrant4TextFill: defaultThemeVariables.quadrant4TextFill,\n      quadrantPointFill: defaultThemeVariables.quadrantPointFill,\n      quadrantPointTextFill: defaultThemeVariables.quadrantPointTextFill,\n      quadrantXAxisTextFill: defaultThemeVariables.quadrantXAxisTextFill,\n      quadrantYAxisTextFill: defaultThemeVariables.quadrantYAxisTextFill,\n      quadrantTitleFill: defaultThemeVariables.quadrantTitleFill,\n      quadrantInternalBorderStrokeFill: defaultThemeVariables.quadrantInternalBorderStrokeFill,\n      quadrantExternalBorderStrokeFill: defaultThemeVariables.quadrantExternalBorderStrokeFill\n    };\n  }\n  clear() {\n    this.config = this.getDefaultConfig();\n    this.themeConfig = this.getDefaultThemeConfig();\n    this.data = this.getDefaultData();\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.info(\"clear called\");\n  }\n  setData(data) {\n    this.data = { ...this.data, ...data };\n  }\n  addPoints(points) {\n    this.data.points = [...points, ...this.data.points];\n  }\n  setConfig(config2) {\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.trace(\"setConfig called with: \", config2);\n    this.config = { ...this.config, ...config2 };\n  }\n  setThemeConfig(themeConfig) {\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.trace(\"setThemeConfig called with: \", themeConfig);\n    this.themeConfig = { ...this.themeConfig, ...themeConfig };\n  }\n  calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle) {\n    const xAxisSpaceCalculation = this.config.xAxisLabelPadding * 2 + this.config.xAxisLabelFontSize;\n    const xAxisSpace = {\n      top: xAxisPosition === \"top\" && showXAxis ? xAxisSpaceCalculation : 0,\n      bottom: xAxisPosition === \"bottom\" && showXAxis ? xAxisSpaceCalculation : 0\n    };\n    const yAxisSpaceCalculation = this.config.yAxisLabelPadding * 2 + this.config.yAxisLabelFontSize;\n    const yAxisSpace = {\n      left: this.config.yAxisPosition === \"left\" && showYAxis ? yAxisSpaceCalculation : 0,\n      right: this.config.yAxisPosition === \"right\" && showYAxis ? yAxisSpaceCalculation : 0\n    };\n    const titleSpaceCalculation = this.config.titleFontSize + this.config.titlePadding * 2;\n    const titleSpace = {\n      top: showTitle ? titleSpaceCalculation : 0\n    };\n    const quadrantLeft = this.config.quadrantPadding + yAxisSpace.left;\n    const quadrantTop = this.config.quadrantPadding + xAxisSpace.top + titleSpace.top;\n    const quadrantWidth = this.config.chartWidth - this.config.quadrantPadding * 2 - yAxisSpace.left - yAxisSpace.right;\n    const quadrantHeight = this.config.chartHeight - this.config.quadrantPadding * 2 - xAxisSpace.top - xAxisSpace.bottom - titleSpace.top;\n    const quadrantHalfWidth = quadrantWidth / 2;\n    const quadrantHalfHeight = quadrantHeight / 2;\n    const quadrantSpace = {\n      quadrantLeft,\n      quadrantTop,\n      quadrantWidth,\n      quadrantHalfWidth,\n      quadrantHeight,\n      quadrantHalfHeight\n    };\n    return {\n      xAxisSpace,\n      yAxisSpace,\n      titleSpace,\n      quadrantSpace\n    };\n  }\n  getAxisLabels(xAxisPosition, showXAxis, showYAxis, spaceData) {\n    const { quadrantSpace, titleSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const drawXAxisLabelsInMiddle = Boolean(this.data.xAxisRightText);\n    const drawYAxisLabelsInMiddle = Boolean(this.data.yAxisTopText);\n    const axisLabels = [];\n    if (this.data.xAxisLeftText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisLeftText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.xAxisRightText && showXAxis) {\n      axisLabels.push({\n        text: this.data.xAxisRightText,\n        fill: this.themeConfig.quadrantXAxisTextFill,\n        x: quadrantLeft + quadrantHalfWidth + (drawXAxisLabelsInMiddle ? quadrantHalfWidth / 2 : 0),\n        y: xAxisPosition === \"top\" ? this.config.xAxisLabelPadding + titleSpace.top : this.config.xAxisLabelPadding + quadrantTop + quadrantHeight + this.config.quadrantPadding,\n        fontSize: this.config.xAxisLabelFontSize,\n        verticalPos: drawXAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: 0\n      });\n    }\n    if (this.data.yAxisBottomText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisBottomText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    if (this.data.yAxisTopText && showYAxis) {\n      axisLabels.push({\n        text: this.data.yAxisTopText,\n        fill: this.themeConfig.quadrantYAxisTextFill,\n        x: this.config.yAxisPosition === \"left\" ? this.config.yAxisLabelPadding : this.config.yAxisLabelPadding + quadrantLeft + quadrantWidth + this.config.quadrantPadding,\n        y: quadrantTop + quadrantHalfHeight - (drawYAxisLabelsInMiddle ? quadrantHalfHeight / 2 : 0),\n        fontSize: this.config.yAxisLabelFontSize,\n        verticalPos: drawYAxisLabelsInMiddle ? \"center\" : \"left\",\n        horizontalPos: \"top\",\n        rotation: -90\n      });\n    }\n    return axisLabels;\n  }\n  getQuadrants(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHalfHeight, quadrantLeft, quadrantHalfWidth, quadrantTop } = quadrantSpace;\n    const quadrants = [\n      {\n        text: {\n          text: this.data.quadrant1Text,\n          fill: this.themeConfig.quadrant1TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant1Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant2Text,\n          fill: this.themeConfig.quadrant2TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant2Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant3Text,\n          fill: this.themeConfig.quadrant3TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant3Fill\n      },\n      {\n        text: {\n          text: this.data.quadrant4Text,\n          fill: this.themeConfig.quadrant4TextFill,\n          x: 0,\n          y: 0,\n          fontSize: this.config.quadrantLabelFontSize,\n          verticalPos: \"center\",\n          horizontalPos: \"middle\",\n          rotation: 0\n        },\n        x: quadrantLeft + quadrantHalfWidth,\n        y: quadrantTop + quadrantHalfHeight,\n        width: quadrantHalfWidth,\n        height: quadrantHalfHeight,\n        fill: this.themeConfig.quadrant4Fill\n      }\n    ];\n    for (const quadrant of quadrants) {\n      quadrant.text.x = quadrant.x + quadrant.width / 2;\n      if (this.data.points.length === 0) {\n        quadrant.text.y = quadrant.y + quadrant.height / 2;\n        quadrant.text.horizontalPos = \"middle\";\n      } else {\n        quadrant.text.y = quadrant.y + this.config.quadrantTextTopPadding;\n        quadrant.text.horizontalPos = \"top\";\n      }\n    }\n    return quadrants;\n  }\n  getQuadrantPoints(spaceData) {\n    const { quadrantSpace } = spaceData;\n    const { quadrantHeight, quadrantLeft, quadrantTop, quadrantWidth } = quadrantSpace;\n    const xAxis = (0,d3__WEBPACK_IMPORTED_MODULE_0__.scaleLinear)().domain([0, 1]).range([quadrantLeft, quadrantWidth + quadrantLeft]);\n    const yAxis = (0,d3__WEBPACK_IMPORTED_MODULE_0__.scaleLinear)().domain([0, 1]).range([quadrantHeight + quadrantTop, quadrantTop]);\n    const points = this.data.points.map((point) => {\n      const props = {\n        x: xAxis(point.x),\n        y: yAxis(point.y),\n        fill: this.themeConfig.quadrantPointFill,\n        radius: this.config.pointRadius,\n        text: {\n          text: point.text,\n          fill: this.themeConfig.quadrantPointTextFill,\n          x: xAxis(point.x),\n          y: yAxis(point.y) + this.config.pointTextPadding,\n          verticalPos: \"center\",\n          horizontalPos: \"top\",\n          fontSize: this.config.pointLabelFontSize,\n          rotation: 0\n        }\n      };\n      return props;\n    });\n    return points;\n  }\n  getBorders(spaceData) {\n    const halfExternalBorderWidth = this.config.quadrantExternalBorderStrokeWidth / 2;\n    const { quadrantSpace } = spaceData;\n    const {\n      quadrantHalfHeight,\n      quadrantHeight,\n      quadrantLeft,\n      quadrantHalfWidth,\n      quadrantTop,\n      quadrantWidth\n    } = quadrantSpace;\n    const borderLines = [\n      // top border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop\n      },\n      // right border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // bottom border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft - halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHeight,\n        x2: quadrantLeft + quadrantWidth + halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHeight\n      },\n      // left border\n      {\n        strokeFill: this.themeConfig.quadrantExternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantExternalBorderStrokeWidth,\n        x1: quadrantLeft,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // vertical inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + quadrantHalfWidth,\n        y1: quadrantTop + halfExternalBorderWidth,\n        x2: quadrantLeft + quadrantHalfWidth,\n        y2: quadrantTop + quadrantHeight - halfExternalBorderWidth\n      },\n      // horizontal inner border\n      {\n        strokeFill: this.themeConfig.quadrantInternalBorderStrokeFill,\n        strokeWidth: this.config.quadrantInternalBorderStrokeWidth,\n        x1: quadrantLeft + halfExternalBorderWidth,\n        y1: quadrantTop + quadrantHalfHeight,\n        x2: quadrantLeft + quadrantWidth - halfExternalBorderWidth,\n        y2: quadrantTop + quadrantHalfHeight\n      }\n    ];\n    return borderLines;\n  }\n  getTitle(showTitle) {\n    if (showTitle) {\n      return {\n        text: this.data.titleText,\n        fill: this.themeConfig.quadrantTitleFill,\n        fontSize: this.config.titleFontSize,\n        horizontalPos: \"top\",\n        verticalPos: \"center\",\n        rotation: 0,\n        y: this.config.titlePadding,\n        x: this.config.chartWidth / 2\n      };\n    }\n    return;\n  }\n  build() {\n    const showXAxis = this.config.showXAxis && !!(this.data.xAxisLeftText || this.data.xAxisRightText);\n    const showYAxis = this.config.showYAxis && !!(this.data.yAxisTopText || this.data.yAxisBottomText);\n    const showTitle = this.config.showTitle && !!this.data.titleText;\n    const xAxisPosition = this.data.points.length > 0 ? \"bottom\" : this.config.xAxisPosition;\n    const calculatedSpace = this.calculateSpace(xAxisPosition, showXAxis, showYAxis, showTitle);\n    return {\n      points: this.getQuadrantPoints(calculatedSpace),\n      quadrants: this.getQuadrants(calculatedSpace),\n      axisLabels: this.getAxisLabels(xAxisPosition, showXAxis, showYAxis, calculatedSpace),\n      borderLines: this.getBorders(calculatedSpace),\n      title: this.getTitle(showTitle)\n    };\n  }\n}\nconst config = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)();\nfunction textSanitizer(text) {\n  return (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.d)(text.trim(), config);\n}\nconst quadrantBuilder = new QuadrantBuilder();\nfunction setQuadrant1Text(textObj) {\n  quadrantBuilder.setData({ quadrant1Text: textSanitizer(textObj.text) });\n}\nfunction setQuadrant2Text(textObj) {\n  quadrantBuilder.setData({ quadrant2Text: textSanitizer(textObj.text) });\n}\nfunction setQuadrant3Text(textObj) {\n  quadrantBuilder.setData({ quadrant3Text: textSanitizer(textObj.text) });\n}\nfunction setQuadrant4Text(textObj) {\n  quadrantBuilder.setData({ quadrant4Text: textSanitizer(textObj.text) });\n}\nfunction setXAxisLeftText(textObj) {\n  quadrantBuilder.setData({ xAxisLeftText: textSanitizer(textObj.text) });\n}\nfunction setXAxisRightText(textObj) {\n  quadrantBuilder.setData({ xAxisRightText: textSanitizer(textObj.text) });\n}\nfunction setYAxisTopText(textObj) {\n  quadrantBuilder.setData({ yAxisTopText: textSanitizer(textObj.text) });\n}\nfunction setYAxisBottomText(textObj) {\n  quadrantBuilder.setData({ yAxisBottomText: textSanitizer(textObj.text) });\n}\nfunction addPoint(textObj, x, y) {\n  quadrantBuilder.addPoints([{ x, y, text: textSanitizer(textObj.text) }]);\n}\nfunction setWidth(width) {\n  quadrantBuilder.setConfig({ chartWidth: width });\n}\nfunction setHeight(height) {\n  quadrantBuilder.setConfig({ chartHeight: height });\n}\nfunction getQuadrantData() {\n  const config2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)();\n  const { themeVariables, quadrantChart: quadrantChartConfig } = config2;\n  if (quadrantChartConfig) {\n    quadrantBuilder.setConfig(quadrantChartConfig);\n  }\n  quadrantBuilder.setThemeConfig({\n    quadrant1Fill: themeVariables.quadrant1Fill,\n    quadrant2Fill: themeVariables.quadrant2Fill,\n    quadrant3Fill: themeVariables.quadrant3Fill,\n    quadrant4Fill: themeVariables.quadrant4Fill,\n    quadrant1TextFill: themeVariables.quadrant1TextFill,\n    quadrant2TextFill: themeVariables.quadrant2TextFill,\n    quadrant3TextFill: themeVariables.quadrant3TextFill,\n    quadrant4TextFill: themeVariables.quadrant4TextFill,\n    quadrantPointFill: themeVariables.quadrantPointFill,\n    quadrantPointTextFill: themeVariables.quadrantPointTextFill,\n    quadrantXAxisTextFill: themeVariables.quadrantXAxisTextFill,\n    quadrantYAxisTextFill: themeVariables.quadrantYAxisTextFill,\n    quadrantExternalBorderStrokeFill: themeVariables.quadrantExternalBorderStrokeFill,\n    quadrantInternalBorderStrokeFill: themeVariables.quadrantInternalBorderStrokeFill,\n    quadrantTitleFill: themeVariables.quadrantTitleFill\n  });\n  quadrantBuilder.setData({ titleText: (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.t)() });\n  return quadrantBuilder.build();\n}\nconst clear = function() {\n  quadrantBuilder.clear();\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.v)();\n};\nconst db = {\n  setWidth,\n  setHeight,\n  setQuadrant1Text,\n  setQuadrant2Text,\n  setQuadrant3Text,\n  setQuadrant4Text,\n  setXAxisLeftText,\n  setXAxisRightText,\n  setYAxisTopText,\n  setYAxisBottomText,\n  addPoint,\n  getQuadrantData,\n  clear,\n  setAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.s,\n  getAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.g,\n  setDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.q,\n  getDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.t,\n  getAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  setAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.b\n};\nconst draw = (txt, id, _version, diagObj) => {\n  var _a, _b, _c;\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"hanging\" : \"middle\";\n  }\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : \"middle\";\n  }\n  function getTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  const conf = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)();\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"Rendering quadrant chart\\n\" + txt);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"body\");\n  const svg = root.select(`[id=\"${id}\"]`);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const width = ((_a = conf.quadrantChart) == null ? void 0 : _a.chartWidth) || 500;\n  const height = ((_b = conf.quadrantChart) == null ? void 0 : _b.chartHeight) || 500;\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.i)(svg, height, width, ((_c = conf.quadrantChart) == null ? void 0 : _c.useMaxWidth) || true);\n  svg.attr(\"viewBox\", \"0 0 \" + width + \" \" + height);\n  diagObj.db.setHeight(height);\n  diagObj.db.setWidth(width);\n  const quadrantData = diagObj.db.getQuadrantData();\n  const quadrantsGroup = group.append(\"g\").attr(\"class\", \"quadrants\");\n  const borderGroup = group.append(\"g\").attr(\"class\", \"border\");\n  const dataPointGroup = group.append(\"g\").attr(\"class\", \"data-points\");\n  const labelGroup = group.append(\"g\").attr(\"class\", \"labels\");\n  const titleGroup = group.append(\"g\").attr(\"class\", \"title\");\n  if (quadrantData.title) {\n    titleGroup.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", quadrantData.title.fill).attr(\"font-size\", quadrantData.title.fontSize).attr(\"dominant-baseline\", getDominantBaseLine(quadrantData.title.horizontalPos)).attr(\"text-anchor\", getTextAnchor(quadrantData.title.verticalPos)).attr(\"transform\", getTransformation(quadrantData.title)).text(quadrantData.title.text);\n  }\n  if (quadrantData.borderLines) {\n    borderGroup.selectAll(\"line\").data(quadrantData.borderLines).enter().append(\"line\").attr(\"x1\", (data) => data.x1).attr(\"y1\", (data) => data.y1).attr(\"x2\", (data) => data.x2).attr(\"y2\", (data) => data.y2).style(\"stroke\", (data) => data.strokeFill).style(\"stroke-width\", (data) => data.strokeWidth);\n  }\n  const quadrants = quadrantsGroup.selectAll(\"g.quadrant\").data(quadrantData.quadrants).enter().append(\"g\").attr(\"class\", \"quadrant\");\n  quadrants.append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill);\n  quadrants.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text)).text((data) => data.text.text);\n  const labels = labelGroup.selectAll(\"g.label\").data(quadrantData.axisLabels).enter().append(\"g\").attr(\"class\", \"label\");\n  labels.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.horizontalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.verticalPos)).attr(\"transform\", (data) => getTransformation(data));\n  const dataPoints = dataPointGroup.selectAll(\"g.data-point\").data(quadrantData.points).enter().append(\"g\").attr(\"class\", \"data-point\");\n  dataPoints.append(\"circle\").attr(\"cx\", (data) => data.x).attr(\"cy\", (data) => data.y).attr(\"r\", (data) => data.radius).attr(\"fill\", (data) => data.fill);\n  dataPoints.append(\"text\").attr(\"x\", 0).attr(\"y\", 0).text((data) => data.text.text).attr(\"fill\", (data) => data.text.fill).attr(\"font-size\", (data) => data.text.fontSize).attr(\n    \"dominant-baseline\",\n    (data) => getDominantBaseLine(data.text.horizontalPos)\n  ).attr(\"text-anchor\", (data) => getTextAnchor(data.text.verticalPos)).attr(\"transform\", (data) => getTransformation(data.text));\n};\nconst renderer = {\n  draw\n};\nconst diagram = {\n  parser: parser$1,\n  db,\n  renderer,\n  styles: () => \"\"\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/quadrantDiagram-120e2f19.js\n"));

/***/ })

}]);