"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_journeyDiagram-49397b02_js"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/journeyDiagram-49397b02.js":
/*!**************************************************************!*\
  !*** ./node_modules/mermaid/dist/journeyDiagram-49397b02.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var _svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./svgDrawCommon-08f97a94.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 18], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 14];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"journey\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"taskName\": 18, \"taskData\": 19, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"journey\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 18: \"taskName\", 19: \"taskData\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 2]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 13:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 15, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: $V6 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 16] }, { 15: [1, 17] }, o($V0, [2, 11]), o($V0, [2, 12]), { 19: [1, 18] }, o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 13])],\n    defaultActions: {},\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n          case 6:\n            return 11;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n          case 14:\n            return 17;\n          case 15:\n            return 18;\n          case 16:\n            return 19;\n          case 17:\n            return \":\";\n          case 18:\n            return 6;\n          case 19:\n            return \"INVALID\";\n        }\n      },\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:journey\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nlet currentSection = \"\";\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\nconst clear = function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.v)();\n};\nconst addSection = function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\nconst getSections = function() {\n  return sections;\n};\nconst getTasks = function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n};\nconst updateActors = function() {\n  const tempActors = [];\n  tasks.forEach((task) => {\n    if (task.people) {\n      tempActors.push(...task.people);\n    }\n  });\n  const unique = new Set(tempActors);\n  return [...unique].sort();\n};\nconst addTask = function(descr, taskData) {\n  const pieces = taskData.substr(1).split(\":\");\n  let score = 0;\n  let peeps = [];\n  if (pieces.length === 1) {\n    score = Number(pieces[0]);\n    peeps = [];\n  } else {\n    score = Number(pieces[0]);\n    peeps = pieces[1].split(\",\");\n  }\n  const peopleList = peeps.map((s) => s.trim());\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    people: peopleList,\n    task: descr,\n    score\n  };\n  rawTasks.push(rawTask);\n};\nconst addTaskOrg = function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n};\nconst compileTasks = function() {\n  const compileTask = function(pos) {\n    return rawTasks[pos].processed;\n  };\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\nconst getActors = function() {\n  return updateActors();\n};\nconst db = {\n  getConfig: () => (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().journey,\n  clear,\n  setDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.q,\n  getDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.t,\n  setAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.s,\n  getAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.g,\n  setAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.b,\n  getAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  getActors\n};\nconst getStyles = (options) => `.label {\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n    color: ${options.textColor};\n  }\n  .mouth {\n    stroke: #666;\n  }\n\n  line {\n    stroke: ${options.textColor}\n  }\n\n  .legend {\n    fill: ${options.textColor};\n  }\n\n  .label text {\n    fill: #333;\n  }\n  .label {\n    color: ${options.textColor}\n  }\n\n  .face {\n    ${options.faceColor ? `fill: ${options.faceColor}` : \"fill: #FFF8DC\"};\n    stroke: #999;\n  }\n\n  .node rect,\n  .node circle,\n  .node ellipse,\n  .node polygon,\n  .node path {\n    fill: ${options.mainBkg};\n    stroke: ${options.nodeBorder};\n    stroke-width: 1px;\n  }\n\n  .node .label {\n    text-align: center;\n  }\n  .node.clickable {\n    cursor: pointer;\n  }\n\n  .arrowheadPath {\n    fill: ${options.arrowheadColor};\n  }\n\n  .edgePath .path {\n    stroke: ${options.lineColor};\n    stroke-width: 1.5px;\n  }\n\n  .flowchart-link {\n    stroke: ${options.lineColor};\n    fill: none;\n  }\n\n  .edgeLabel {\n    background-color: ${options.edgeLabelBackground};\n    rect {\n      opacity: 0.5;\n    }\n    text-align: center;\n  }\n\n  .cluster rect {\n  }\n\n  .cluster text {\n    fill: ${options.titleColor};\n  }\n\n  div.mermaidTooltip {\n    position: absolute;\n    text-align: center;\n    max-width: 200px;\n    padding: 2px;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n    font-size: 12px;\n    background: ${options.tertiaryColor};\n    border: 1px solid ${options.border2};\n    border-radius: 2px;\n    pointer-events: none;\n    z-index: 100;\n  }\n\n  .task-type-0, .section-type-0  {\n    ${options.fillType0 ? `fill: ${options.fillType0}` : \"\"};\n  }\n  .task-type-1, .section-type-1  {\n    ${options.fillType0 ? `fill: ${options.fillType1}` : \"\"};\n  }\n  .task-type-2, .section-type-2  {\n    ${options.fillType0 ? `fill: ${options.fillType2}` : \"\"};\n  }\n  .task-type-3, .section-type-3  {\n    ${options.fillType0 ? `fill: ${options.fillType3}` : \"\"};\n  }\n  .task-type-4, .section-type-4  {\n    ${options.fillType0 ? `fill: ${options.fillType4}` : \"\"};\n  }\n  .task-type-5, .section-type-5  {\n    ${options.fillType0 ? `fill: ${options.fillType5}` : \"\"};\n  }\n  .task-type-6, .section-type-6  {\n    ${options.fillType0 ? `fill: ${options.fillType6}` : \"\"};\n  }\n  .task-type-7, .section-type-7  {\n    ${options.fillType0 ? `fill: ${options.fillType7}` : \"\"};\n  }\n\n  .actor-0 {\n    ${options.actor0 ? `fill: ${options.actor0}` : \"\"};\n  }\n  .actor-1 {\n    ${options.actor1 ? `fill: ${options.actor1}` : \"\"};\n  }\n  .actor-2 {\n    ${options.actor2 ? `fill: ${options.actor2}` : \"\"};\n  }\n  .actor-3 {\n    ${options.actor3 ? `fill: ${options.actor3}` : \"\"};\n  }\n  .actor-4 {\n    ${options.actor4 ? `fill: ${options.actor4}` : \"\"};\n  }\n  .actor-5 {\n    ${options.actor5 ? `fill: ${options.actor5}` : \"\"};\n  }\n`;\nconst styles = getStyles;\nconst drawRect = function(elem, rectData) {\n  return (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.d)(elem, rectData);\n};\nconst drawFace = function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc$1 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.arc)().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc$1).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  function sad(face2) {\n    const arc$1 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.arc)().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc$1).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n};\nconst drawCircle = function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n};\nconst drawText = function(elem, textData) {\n  return (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.f)(elem, textData);\n};\nconst drawLabel = function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\nconst drawSection = function(elem, section, conf2) {\n  const g = elem.append(\"g\");\n  const rect = (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.g)();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf2.width * section.taskCount + // width of the tasks\n  conf2.diagramMarginX * (section.taskCount - 1);\n  rect.height = conf2.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf2)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf2,\n    section.colour\n  );\n};\nlet taskCount = -1;\nconst drawTask = function(elem, task, conf2) {\n  const center = task.x + conf2.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.g)();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf2.width;\n  rect.height = conf2.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  let xPos = task.x + 14;\n  task.people.forEach((person) => {\n    const colour = task.actors[person].color;\n    const circle = {\n      cx: xPos,\n      cy: task.y,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      title: person,\n      pos: task.actors[person].position\n    };\n    drawCircle(g, circle);\n    xPos += 10;\n  });\n  _drawTextCandidateFunc(conf2)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf2,\n    task.colour\n  );\n};\nconst drawBackgroundRect = function(elem, bounds2) {\n  (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.a)(elem, bounds2);\n};\nconst _drawTextCandidateFunc = function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2, colour) {\n    const { taskFontSize, taskFontFamily } = conf2;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nconst initGraphics = function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n};\nconst svgDraw = {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  initGraphics\n};\nconst setConf = function(cnf) {\n  const keys = Object.keys(cnf);\n  keys.forEach(function(key) {\n    conf[key] = cnf[key];\n  });\n};\nconst actors = {};\nfunction drawActorLegend(diagram2) {\n  const conf2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().journey;\n  let yPos = 60;\n  Object.keys(actors).forEach((person) => {\n    const colour = actors[person].color;\n    const circleData = {\n      cx: 20,\n      cy: yPos,\n      r: 7,\n      fill: colour,\n      stroke: \"#000\",\n      pos: actors[person].position\n    };\n    svgDraw.drawCircle(diagram2, circleData);\n    const labelData = {\n      x: 40,\n      y: yPos + 7,\n      fill: \"#666\",\n      text: person,\n      textMargin: conf2.boxTextMargin | 5\n    };\n    svgDraw.drawText(diagram2, labelData);\n    yPos += 20;\n  });\n}\nconst conf = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().journey;\nconst LEFT_MARGIN = conf.leftMargin;\nconst draw = function(text, id, version, diagObj) {\n  const conf2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().journey;\n  const securityLevel = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"body\");\n  bounds.init();\n  const diagram2 = root.select(\"#\" + id);\n  svgDraw.initGraphics(diagram2);\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getDiagramTitle();\n  const actorNames = diagObj.db.getActors();\n  for (const member in actors) {\n    delete actors[member];\n  }\n  let actorPos = 0;\n  actorNames.forEach((actorName) => {\n    actors[actorName] = {\n      color: conf2.actorColours[actorPos % conf2.actorColours.length],\n      position: actorPos\n    };\n    actorPos++;\n  });\n  drawActorLegend(diagram2);\n  bounds.insert(0, 0, LEFT_MARGIN, Object.keys(actors).length * 50);\n  drawTasks(diagram2, tasks2, 0);\n  const box = bounds.getBounds();\n  if (title) {\n    diagram2.append(\"text\").text(title).attr(\"x\", LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 25);\n  }\n  const height = box.stopy - box.starty + 2 * conf2.diagramMarginY;\n  const width = LEFT_MARGIN + box.stopx + 2 * conf2.diagramMarginX;\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.i)(diagram2, height, width, conf2.useMaxWidth);\n  diagram2.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", conf2.height * 4).attr(\"x2\", width - LEFT_MARGIN - 4).attr(\"y2\", conf2.height * 4).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  const extraVertForTitle = title ? 70 : 0;\n  diagram2.attr(\"viewBox\", `${box.startx} -25 ${width} ${height + extraVertForTitle}`);\n  diagram2.attr(\"preserveAspectRatio\", \"xMinYMin meet\");\n  diagram2.attr(\"height\", height + extraVertForTitle + 25);\n};\nconst bounds = {\n  data: {\n    startx: void 0,\n    stopx: void 0,\n    starty: void 0,\n    stopy: void 0\n  },\n  verticalPos: 0,\n  sequenceItems: [],\n  init: function() {\n    this.sequenceItems = [];\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0\n    };\n    this.verticalPos = 0;\n  },\n  updateVal: function(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  },\n  updateBounds: function(startx, starty, stopx, stopy) {\n    const conf2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().journey;\n    const _self = this;\n    let cnt = 0;\n    function updateFn(type) {\n      return function updateItemBounds(item) {\n        cnt++;\n        const n = _self.sequenceItems.length - cnt + 1;\n        _self.updateVal(item, \"starty\", starty - n * conf2.boxMargin, Math.min);\n        _self.updateVal(item, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        _self.updateVal(bounds.data, \"startx\", startx - n * conf2.boxMargin, Math.min);\n        _self.updateVal(bounds.data, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n        if (!(type === \"activation\")) {\n          _self.updateVal(item, \"startx\", startx - n * conf2.boxMargin, Math.min);\n          _self.updateVal(item, \"stopx\", stopx + n * conf2.boxMargin, Math.max);\n          _self.updateVal(bounds.data, \"starty\", starty - n * conf2.boxMargin, Math.min);\n          _self.updateVal(bounds.data, \"stopy\", stopy + n * conf2.boxMargin, Math.max);\n        }\n      };\n    }\n    this.sequenceItems.forEach(updateFn());\n  },\n  insert: function(startx, starty, stopx, stopy) {\n    const _startx = Math.min(startx, stopx);\n    const _stopx = Math.max(startx, stopx);\n    const _starty = Math.min(starty, stopy);\n    const _stopy = Math.max(starty, stopy);\n    this.updateVal(bounds.data, \"startx\", _startx, Math.min);\n    this.updateVal(bounds.data, \"starty\", _starty, Math.min);\n    this.updateVal(bounds.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(bounds.data, \"stopy\", _stopy, Math.max);\n    this.updateBounds(_startx, _starty, _stopx, _stopy);\n  },\n  bumpVerticalPos: function(bump) {\n    this.verticalPos = this.verticalPos + bump;\n    this.data.stopy = this.verticalPos;\n  },\n  getVerticalPos: function() {\n    return this.verticalPos;\n  },\n  getBounds: function() {\n    return this.data;\n  }\n};\nconst fills = conf.sectionFills;\nconst textColours = conf.sectionColours;\nconst drawTasks = function(diagram2, tasks2, verticalPos) {\n  const conf2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().journey;\n  let lastSection = \"\";\n  const sectionVHeight = conf2.height * 2 + conf2.diagramMarginY;\n  const taskPos = verticalPos + sectionVHeight;\n  let sectionNumber = 0;\n  let fill = \"#CCC\";\n  let colour = \"black\";\n  let num = 0;\n  for (const [i, task] of tasks2.entries()) {\n    if (lastSection !== task.section) {\n      fill = fills[sectionNumber % fills.length];\n      num = sectionNumber % fills.length;\n      colour = textColours[sectionNumber % textColours.length];\n      let taskInSectionCount = 0;\n      const currentSection2 = task.section;\n      for (let taskIndex = i; taskIndex < tasks2.length; taskIndex++) {\n        if (tasks2[taskIndex].section == currentSection2) {\n          taskInSectionCount = taskInSectionCount + 1;\n        } else {\n          break;\n        }\n      }\n      const section = {\n        x: i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN,\n        y: 50,\n        text: task.section,\n        fill,\n        num,\n        colour,\n        taskCount: taskInSectionCount\n      };\n      svgDraw.drawSection(diagram2, section, conf2);\n      lastSection = task.section;\n      sectionNumber++;\n    }\n    const taskActors = task.people.reduce((acc, actorName) => {\n      if (actors[actorName]) {\n        acc[actorName] = actors[actorName];\n      }\n      return acc;\n    }, {});\n    task.x = i * conf2.taskMargin + i * conf2.width + LEFT_MARGIN;\n    task.y = taskPos;\n    task.width = conf2.diagramMarginX;\n    task.height = conf2.diagramMarginY;\n    task.colour = colour;\n    task.fill = fill;\n    task.num = num;\n    task.actors = taskActors;\n    svgDraw.drawTask(diagram2, task, conf2);\n    bounds.insert(task.x, task.y, task.x + task.width + conf2.taskMargin, 300 + 5 * 30);\n  }\n};\nconst renderer = {\n  setConf,\n  draw\n};\nconst diagram = {\n  parser: parser$1,\n  db,\n  renderer,\n  styles,\n  init: (cnf) => {\n    renderer.setConf(cnf.journey);\n    db.clear();\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/journeyDiagram-49397b02.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js":
/*!*************************************************************!*\
  !*** ./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ drawBackgroundRect),\n/* harmony export */   b: () => (/* binding */ drawEmbeddedImage),\n/* harmony export */   c: () => (/* binding */ drawImage),\n/* harmony export */   d: () => (/* binding */ drawRect),\n/* harmony export */   e: () => (/* binding */ getTextObj),\n/* harmony export */   f: () => (/* binding */ drawText),\n/* harmony export */   g: () => (/* binding */ getNoteRect)\n/* harmony export */ });\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n\n\nconst drawRect = (element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  rectData.rx !== void 0 && rectElement.attr(\"rx\", rectData.rx);\n  rectData.ry !== void 0 && rectElement.attr(\"ry\", rectData.ry);\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  rectData.class !== void 0 && rectElement.attr(\"class\", rectData.class);\n  return rectElement;\n};\nconst drawBackgroundRect = (element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n};\nconst drawText = (element, textData) => {\n  const nText = textData.text.replace(_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_1__.J, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  textData.class !== void 0 && textElem.attr(\"class\", textData.class);\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n};\nconst drawImage = (elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n};\nconst drawEmbeddedImage = (element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n};\nconst getNoteRect = () => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n};\nconst getTextObj = () => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js\n"));

/***/ })

}]);