"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_oc-FR-ATFBDMF6_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/oc-FR-ATFBDMF6.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/oc-FR-ATFBDMF6.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ oc_FR_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/oc-FR.json\nvar labels = {\n  paste: \"Pegar\",\n  pasteAsPlaintext: \"Pegar en t\\xE8xt brut\",\n  pasteCharts: \"Pegar los grafics\",\n  selectAll: \"Tot seleccionar\",\n  multiSelect: \"Apondre un element a la seleccion\",\n  moveCanvas: \"Despla\\xE7ar lo canab\\xE0s\",\n  cut: \"Talhar\",\n  copy: \"Copiar\",\n  copyAsPng: \"Copiar al quichapapi\\xE8rs coma PNG\",\n  copyAsSvg: \"Copiar al quichapapi\\xE8rs coma SVG\",\n  copyText: \"Copiar al quichapapi\\xE8rs coma t\\xE8xt\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"En avant\",\n  sendToBack: \"En arri\\xE8r\",\n  bringToFront: \"A l\\u2019endavant\",\n  sendBackward: \"Endarri\\xE8r\",\n  delete: \"Suprimir\",\n  copyStyles: \"Copiar los estiles\",\n  pasteStyles: \"Pegar los estils\",\n  stroke: \"Contorn\",\n  background: \"R\\xE8ireplan\",\n  fill: \"Empliment\",\n  strokeWidth: \"Largor de contorn\",\n  strokeStyle: \"Estil de contorn\",\n  strokeStyle_solid: \"Solide\",\n  strokeStyle_dashed: \"Tiret\",\n  strokeStyle_dotted: \"Puntilhat\",\n  sloppiness: \"Estil de tra\\xE7a\",\n  opacity: \"Opacitat\",\n  textAlign: \"Alinhament t\\xE8xt\",\n  edges: \"Angles\",\n  sharp: \"Ponchut\",\n  round: \"Arredonit\",\n  arrowheads: \"Cap de la fl\\xE8cha\",\n  arrowhead_none: \"Cap\",\n  arrowhead_arrow: \"Sageta\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Triangle\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Talha polissa\",\n  fontFamily: \"Familha de polissa\",\n  addWatermark: \"Apondre \\xAB\\u202FFabricat amb Excalidraw\\u202F\\xBB\",\n  handDrawn: \"A la man levada\",\n  normal: \"Normala\",\n  code: \"C\\xF2di\",\n  small: \"Pichona\",\n  medium: \"Mejana\",\n  large: \"Granda\",\n  veryLarge: \"Gradassa\",\n  solid: \"Solide\",\n  hachure: \"Raia\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Raia crosada\",\n  thin: \"Fin\",\n  bold: \"Esp\\xE9s\",\n  left: \"Esqu\\xE8rra\",\n  center: \"Centre\",\n  right: \"Drecha\",\n  extraBold: \"Espes\\xE0s\",\n  architect: \"Arquit\\xE8cte\",\n  artist: \"Artista\",\n  cartoonist: \"Dessenhaire\",\n  fileTitle: \"Nom del fichi\\xE8r\",\n  colorPicker: \"Selector de color\",\n  canvasColors: \"Utilizada suls canabassses\",\n  canvasBackground: \"R\\xE8ireplan del canab\\xE0s\",\n  drawingCanvas: \"Z\\xF2na de dessenh\",\n  layers: \"Calques\",\n  actions: \"Accions\",\n  language: \"Lenga\",\n  liveCollaboration: \"Collaboracion en dir\\xE8ct...\",\n  duplicateSelection: \"Duplicar\",\n  untitled: \"Sens t\\xEDtol\",\n  name: \"Nom\",\n  yourName: \"V\\xF2stre nom\",\n  madeWithExcalidraw: \"Fabricat amb Excalidraw\",\n  group: \"Gropar la seleccion\",\n  ungroup: \"Desunir la seleccion\",\n  collaborators: \"Collaborators\",\n  showGrid: \"Afichar la gresilha\",\n  addToLibrary: \"Apondre a la bibliot\\xE8ca\",\n  removeFromLibrary: \"Suprimir de la bibliot\\xE8ca\",\n  libraryLoadingMessage: \"Cargament de la bibliot\\xE8ca\\u2026\",\n  libraries: \"Perc\\xF3rrer las bibliot\\xE8cas\",\n  loadingScene: \"Cargament de la sc\\xE8na\\u2026\",\n  align: \"Alinhament\",\n  alignTop: \"Alinhar ennaut\",\n  alignBottom: \"Alinhar enb\\xE0s\",\n  alignLeft: \"Alinhar a esqu\\xE8rra\",\n  alignRight: \"Alinhar a drecha\",\n  centerVertically: \"Centrar verticalament\",\n  centerHorizontally: \"Centrar orizontalament\",\n  distributeHorizontally: \"Distribuir orizontalament\",\n  distributeVertically: \"Distribuir verticalament\",\n  flipHorizontal: \"Virar orizontalament\",\n  flipVertical: \"Virar verticalament\",\n  viewMode: \"M\\xF2de de vista\",\n  share: \"Partejar\",\n  showStroke: \"Mostrar lo selector de color de contorn\",\n  showBackground: \"Mostrar lo selector de color de fons\",\n  toggleTheme: \"Alternar t\\xE8ma\",\n  personalLib: \"Bibliot\\xE8ca personala\",\n  excalidrawLib: \"Bibliot\\xE8ca Excalidraw\",\n  decreaseFontSize: \"Reduire talha polissa\",\n  increaseFontSize: \"Aumentar talha polissa\",\n  unbindText: \"Dessociar lo t\\xE8xte\",\n  bindText: \"Ligar lo t\\xE8xt al contenidor\",\n  createContainerFromText: \"Envelopar lo t\\xE8xte dins un contenedor\",\n  link: {\n    edit: \"Modificar lo ligam\",\n    editEmbed: \"Modificar lo ligam e l\\u2019integracion\",\n    create: \"Crear un ligam\",\n    createEmbed: \"Crear un ligam e son integracion\",\n    label: \"Ligam\",\n    labelEmbed: \"Ligam e integracion\",\n    empty: \"Cap de ligam pas definit\"\n  },\n  lineEditor: {\n    edit: \"Modificar la linha\",\n    exit: \"Sortir de l\\u2019editor de linha\"\n  },\n  elementLock: {\n    lock: \"Verrolhar\",\n    unlock: \"Desverrolhar\",\n    lockAll: \"Tot verrolhar\",\n    unlockAll: \"Tot desverrolhar\"\n  },\n  statusPublished: \"Publicat\",\n  sidebarLock: \"Gardar la barra laterala dob\\xE8rta\",\n  selectAllElementsInFrame: \"Seleccionar totes los elements del quadre\",\n  removeAllElementsFromFrame: \"Tirar totes los elements d\\u2019al quadre\",\n  eyeDropper: \"Prendre la color a partir d\\u2019un canab\\xE0s\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Cap d\\u2019element pas encara apondut...\",\n  hint_emptyLibrary: \"Seleccionatz un element d\\u2019apondre aqu\\xED, o installatz una bibliot\\xE8ca del depaus public, \\xE7ai-jos.\",\n  hint_emptyPrivateLibrary: \"Seleccionatz un element d\\u2019apondre aqu\\xED pel canab\\xE0s.\"\n};\nvar buttons = {\n  clearReset: \"Re\\xEFnicializar lo canab\\xE0s\",\n  exportJSON: \"Exportar en fichi\\xE8r\",\n  exportImage: \"Exportar imatges...\",\n  export: \"Enregistrar dins...\",\n  copyToClipboard: \"Copiar al quichapapi\\xE8rs\",\n  save: \"Salvar al fichi\\xE8r actual\",\n  saveAs: \"Enregistrar jos\",\n  load: \"Dobrir\",\n  getShareableLink: \"Obt\\xE9ner lo ligam de partatge\",\n  close: \"Tampar\",\n  selectLanguage: \"Causir una lenga\",\n  scrollBackToContent: \"Tornar al contengut\",\n  zoomIn: \"Zoom avant\",\n  zoomOut: \"Zoom arri\\xE8r\",\n  resetZoom: \"Tirar lo zoom\",\n  menu: \"Men\\xFA\",\n  done: \"Acabat\",\n  edit: \"Modificar\",\n  undo: \"Anullar\",\n  redo: \"Restablir\",\n  resetLibrary: \"Re\\xEFnicializar la bibliot\\xE8ca\",\n  createNewRoom: \"Crear sala nov\\xE8la\",\n  fullScreen: \"Ecran compl\\xE8t\",\n  darkMode: \"M\\xF2de escur\",\n  lightMode: \"M\\xF2de clar\",\n  zenMode: \"M\\xF2de escur\",\n  objectsSnapMode: \"Ancorar als obj\\xE8ctes\",\n  exitZenMode: \"Sortir del m\\xF2de zen\",\n  cancel: \"Anullar\",\n  clear: \"Escafar\",\n  remove: \"Tirar\",\n  embed: \"Alternar l\\u2019integracion\",\n  publishLibrary: \"Publicar\",\n  submit: \"Enviar\",\n  confirm: \"Confirmar\",\n  embeddableInteractionButton: \"Clicar per interagir\"\n};\nvar alerts = {\n  clearReset: \"Aqu\\xF2 suprimir\\xE0 lo canab\\xE0s compl\\xE8t. O vol\\xE8tz vertadi\\xE8rament\\u202F?\",\n  couldNotCreateShareableLink: \"Creacion impossibla del ligam de partatge.\",\n  couldNotCreateShareableLinkTooBig: \"Creacion impossibla del ligam de partatge\\u202F: la sc\\xE8na es tr\\xF2p granda\",\n  couldNotLoadInvalidFile: \"Cargament impossible d\\u2019un fichi\\xE8r invalid\",\n  importBackendFailed: \"Importacion fracassada.\",\n  cannotExportEmptyCanvas: \"Impossible d\\u2019exportar los canabasses voids.\",\n  couldNotCopyToClipboard: \"C\\xF2pia impossibla al quichapapi\\xE8rs.\",\n  decryptFailed: \"Deschiframent impossible de las donadas.\",\n  uploadedSecurly: \"Lo telecargament es estat securizat amb un chiframent del cap a la fin, significa que los servidors d\\u2019Excalidraw o que quina t\\xE8r\\xE7a part que si\\xE1 p\\xF2don pas legir lo contengut.\",\n  loadSceneOverridePrompt: \"Cargar un dessenh ext\\xE8rn rempla\\xE7ar\\xE0 v\\xF2stre contengut existent. Vol\\xE8tz contunhar\\u202F?\",\n  collabStopOverridePrompt: \"Arrestar la session rempla\\xE7ar\\xE0 v\\xF2stre precedent dessenh gardat localament. O vol\\xE8tz vertadi\\xE8rament\\u202F?\\n\\n(Se vol\\xE8tz gardar v\\xF2stre dessenh local, tampatz simplament l\\u2019onglet del navegador a la pla\\xE7a)\",\n  errorAddingToLibrary: \"Apondon impossible de l\\u2019element a la bibliot\\xE8ca\",\n  errorRemovingFromLibrary: \"Supression impossibla de l\\u2019element a la bibliot\\xE8ca\",\n  confirmAddLibrary: \"Apondr\\xE0 {{numShapes}} forma(s) a v\\xF2stra bibliot\\xE8ca. Confirmatz\\u202F?\",\n  imageDoesNotContainScene: \"Aqueste imatge sembla pas cont\\xE9ner cap de donadas de sc\\xE8na. Av\\xE8tz activat l\\u2019integracion de sc\\xE8na pendent l\\u2019exportacion\\u202F?\",\n  cannotRestoreFromImage: \"Restauracion impossibla de la sc\\xE8na a partir del fichi\\xE8r imatge\",\n  invalidSceneUrl: \"Importacion impossibla de la sc\\xE8na a partir de l\\u2019URL provesida. Es si\\xE1 mal formatada o si\\xE1 conten pas cap de donada JSON Excalidraw valida.\",\n  resetLibrary: \"Aqu\\xF2 suprimir\\xE0 v\\xF2stra bibliot\\xE8ca. O vol\\xE8tz vertadi\\xE8rament\\u202F?\",\n  removeItemsFromsLibrary: \"Suprimir {{count}} element(s) de la bibliot\\xE8ca\\u202F?\",\n  invalidEncryptionKey: \"La clau de chiframent deu cont\\xE9ner 22 caract\\xE8rs. La collaboracion en dir\\xE8ct es desactivada.\",\n  collabOfflineWarning: \"Cap de connexion pas disponibla.\\nV\\xF2stras modificacions ser\\xE0n pas salvadas !\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipe de fichi\\xE8r pas pres en carga.\",\n  imageInsertError: \"Insercion d\\u2019imatge impossibla. Tornatz ensajar mai tard...\",\n  fileTooBig: \"Fichi\\xE8r tr\\xF2p pesuc. La talha maximala autorizada es {{maxSize}}.\",\n  svgImageInsertError: \"Insercion d\\u2019imatge SVG impossibla. Las balisas SVG semblan invalidas.\",\n  failedToFetchImage: \"Frac\\xE0s de la recuperacion de l\\u2019imatge.\",\n  invalidSVGString: \"SVG invalid.\",\n  cannotResolveCollabServer: \"Connexion impossibla al servidor collab. Merc\\xE9s de recargar la pagina e tornar ensajar.\",\n  importLibraryError: \"Impossible de cargar la bibliot\\xE8ca\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"Empegatge impossible (lectura impossibla a partir del quichapapi\\xE8rs).\",\n  asyncPasteFailedOnParse: \"Empegatge impossible.\",\n  copyToSystemClipboardFailed: \"C\\xF2pia impossibla al quichapapi\\xE8rs.\"\n};\nvar toolBar = {\n  selection: \"Seleccion\",\n  image: \"Inserir imatge\",\n  rectangle: \"Rectangle\",\n  diamond: \"Lausange\",\n  ellipse: \"Ellipsa\",\n  arrow: \"Sageta\",\n  line: \"Linha\",\n  freedraw: \"Dessenhar\",\n  text: \"T\\xE8xt\",\n  library: \"Bibliot\\xE8ca\",\n  lock: \"Mantenir activa l\\u2019aisina apr\\xE8p dessenhar\",\n  penMode: \"M\\xF2de estilo - empachar lo contact\",\n  link: \"Apondre/Actualizar lo ligam per una f\\xF2rma seleccionada\",\n  eraser: \"Goma\",\n  frame: \"Esplech quadre\",\n  magicframe: \"\",\n  embeddable: \"Integracion Web\",\n  laser: \"Puntador laser\",\n  hand: \"Man (aisina de despla\\xE7ament de la vista)\",\n  extraTools: \"Mai d\\u2019aisinas\",\n  mermaidToExcalidraw: \"De Mermaid cap a Excalidraw\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Accions del canab\\xE0s\",\n  selectedShapeActions: \"Accions per la forma seleccionada\",\n  shapes: \"Formas\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"Clicatz per comen\\xE7ar mantun punt, lisatz per una sola linha\",\n  freeDraw: \"Clicatz e lisatz, relargatz un c\\xF2p acabat\",\n  text: \"Ast\\xFAcia\\u202F: pod\\xE8tz tanben apondre de t\\xE8xt en doble clicant ont que si\\xE1 amb l\\u2019aisina de seleccion\",\n  embeddable: \"\",\n  text_selected: \"Clicatz dos c\\xF2ps o quichatz ENTRADA per modificar lo t\\xE8xt\",\n  text_editing: \"Quichatz ESCAPAR o CtrlOrCmd+ENTRADA per acabar la modificacion\",\n  linearElementMulti: \"Clicatz sul darri\\xE8r punt o quichatz Ecap o Entrada per acabar\",\n  lockAngle: \"Pod\\xE8tz restr\\xE9nger l\\u2019angle en mantenent MAJ\",\n  resize: \"Pod\\xE8tz servar las proporcions en mantenent la t\\xF2ca MAJ pendent lo redimensionament,\\nmanten\\xE8tz la t\\xF2ca ALT per redimensionar a partir del centre\",\n  resizeImage: \"Pod\\xE8tz retalhar liurament en quichant CTRL,\\nquichatz ALT per retalhar a partir del centre\",\n  rotate: \"Pod\\xE8tz restr\\xE9nger los angles en mantenent MAJ pendent la rotacion\",\n  lineEditor_info: \"Ten\\xE8tz quichat Ctrl o Cmd e doble clic o quichatz Ctrl o Cmd + Entrada per modificar los ponches\",\n  lineEditor_pointSelected: \"Quichar Suprimir per tirar lo(s) punt(s),\\nCtrlOCmd+D per duplicar, o lisatz per despla\\xE7ar\",\n  lineEditor_nothingSelected: \"Seleccionar un punt d\\u2019editar (mant\\xE9ner Maj. per ne seleccionar mantun),\\no mant\\xE9ner Alt e clicar per n\\u2019apondre de nov\\xE8ls\",\n  placeImage: \"Clicatz per pla\\xE7ar l\\u2019imatge, o clicatz e lisatz per definir sa talha manualament\",\n  publishLibrary: \"Publicar v\\xF2stra pr\\xF2pria bibliot\\xE8ca\",\n  bindTextToElement: \"Quichatz Entrada per apondre de t\\xE8xte\",\n  deepBoxSelect: \"Gardar CtrlOCmd per una seleccion gropada e empachar lo despla\\xE7ament\",\n  eraserRevert: \"Ten\\xE8tz quichat Alt per anullar los elements marcats per supression\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Afichatge impossible de l\\u2019apercebut\",\n  canvasTooBig: \"Lo canab\\xE0s p\\xF2t \\xE8sser tr\\xF2p grand.\",\n  canvasTooBigTip: \"Ast\\xFAcia\\u202F: ensajatz de sarrar los elements mai alonhats.\"\n};\nvar errorSplash = {\n  headingMain: \"Una error s\\u2019es producha. Ensajatz <button>recargament de la pagina.</button>\",\n  clearCanvasMessage: \"Se recargar fonciona pas, ensajatz <button>d\\u2019escafar los canabasses.</button>\",\n  clearCanvasCaveat: \" Menar\\xE0 a una p\\xE8rda del trabalh \",\n  trackedToSentry: \"Error amb l\\u2019identificant {{eventId}} es estada enregistrada sus n\\xF2stre sist\\xE8ma.\",\n  openIssueMessage: \"\\xC8rem plan prudents per inclure pas d\\u2019informacions de la sc\\xE8na v\\xF2stra sus l\\u2019error. Se v\\xF2stra sc\\xE8na es pas privada, volgatz considerar de perseguir sus n\\xF2stre <button>tra\\xE7adors d\\u2019avarias.</button> Volgatz inclure las informacions \\xE7ai-jos en las copiant e pegant a l\\u2019issue GitHub.\",\n  sceneContent: \"Contengut de la sc\\xE8na\\u202F:\"\n};\nvar roomDialog = {\n  desc_intro: \"Pod\\xE8tz convidar lo monde a v\\xF2stra sc\\xE8na actuala per participar amb vos.\",\n  desc_privacy: \"Vos en fagatz pas, la session utiliza lo chiframent del cap a la fin, \\xE7\\xF2 que dessenetz demorar\\xE0 privat. Mai n\\xF2stres servidors poir\\xE0n pas veire v\\xF2stra creacion.\",\n  button_startSession: \"Comen\\xE7ar la session\",\n  button_stopSession: \"Arrestar la session\",\n  desc_inProgressIntro: \"La session de collaboracion es ara en cors.\",\n  desc_shareLink: \"Partejatz aqueste ligam amb lo monde amb qui vol\\xE8tz collaborar\\u202F:\",\n  desc_exitSession: \"Arrestar la session vos desconnectar\\xE0 de la sala, mas poiretz contunhar de trabalhar a la sc\\xE8na, en local. Notatz qu\\u2019aqu\\xF2 afectar\\xE0 pas los autres, e poir\\xE0n collaborar a lor version.\",\n  shareTitle: \"Rejonh\\xE8tz una session collaborativa sus Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Error\"\n};\nvar exportDialog = {\n  disk_title: \"Salvar al disc\",\n  disk_details: \"Exportar las donadas de la sc\\xE8na cap a un fichi\\xE8r que pod\\xE8tz importar mai tard.\",\n  disk_button: \"Salvar al fichi\\xE8r\",\n  link_title: \"Ligam de partejar\",\n  link_details: \"Exportar coma un ligam de lectura sola.\",\n  link_button: \"Exportar en ligam\",\n  excalidrawplus_description: \"Enregistrar la sc\\xE8na dins v\\xF2stre espaci de trabalh Excalidraw+.\",\n  excalidrawplus_button: \"Exportar\",\n  excalidrawplus_exportError: \"Export impossibla cap a Excalidraw+ pel moment...\"\n};\nvar helpDialog = {\n  blog: \"Legir n\\xF2stre blog\",\n  click: \"clic\",\n  deepSelect: \"Seleccion prigonda\",\n  deepBoxSelect: \"Seleccionar demest un grop e empacha lo despla\\xE7ament\",\n  curvedArrow: \"Sageta corba\",\n  curvedLine: \"Linha corba\",\n  documentation: \"Documentacion\",\n  doubleClick: \"doble clic\",\n  drag: \"lisar\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"\",\n  editText: \"Modificar lo t\\xE8xte / apondre etiqueta\",\n  github: \"Probl\\xE8ma trobat\\u202F? Senhalatz-lo\",\n  howto: \"Seguiss\\xE8tz n\\xF2stras guidas\",\n  or: \"o\",\n  preventBinding: \"Empachar la fixacion de sagetas\",\n  tools: \"Aisinas\",\n  shortcuts: \"Acorchis clavi\\xE8r\",\n  textFinish: \"Terminar l\\u2019edicion (editor de t\\xE8xt)\",\n  textNewLine: \"Apondre linha nov\\xE8l (editor de t\\xE8xt)\",\n  title: \"Ajuda\",\n  view: \"Vista\",\n  zoomToFit: \"Zoomar per veire totes los elements\",\n  zoomToSelection: \"Zoomar la seleccion\",\n  toggleElementLock: \"Verrolhar/Desverrolhar la seleccion\",\n  movePageUpDown: \"Despla\\xE7ar la pagina ennaut/enb\\xE0s\",\n  movePageLeftRight: \"Despla\\xE7ar la pagina a esqu\\xE8rra/drecha\"\n};\nvar clearCanvasDialog = {\n  title: \"Escafar canab\\xE0s\"\n};\nvar publishDialog = {\n  title: \"Publicar la bibliot\\xE8ca\",\n  itemName: \"Nom de l\\u2019element\",\n  authorName: \"Nom de l\\u2019autor\",\n  githubUsername: \"Nom d\\u2019utilizaire GitHub\",\n  twitterUsername: \"Nom d\\u2019utilizaire Twitter\",\n  libraryName: \"Nom de la bibliot\\xE8ca\",\n  libraryDesc: \"Descripcion de la bibliot\\xE8ca\",\n  website: \"Site web\",\n  placeholder: {\n    authorName: \"V\\xF2stre nom o nom d\\u2019utilizaire\",\n    libraryName: \"Nom de v\\xF2stra bibliot\\xE8ca\",\n    libraryDesc: \"Descripcion de v\\xF2stra bibliot\\xE8ca per ajudar lo monde a comprendre son utilizacion\",\n    githubHandle: \"GitHub handle(opcional), per poder modificar la bibliot\\xE8ca un c\\xF2p enviada per repassa\",\n    twitterHandle: \"Nom d\\u2019utilizaire Twitter (opcional), per saber qual mercejar quand ne parlam sus Twitter\",\n    website: \"Ligam cap a v\\xF2stre site web personal o endac\\xF2m mai (opcional)\"\n  },\n  errors: {\n    required: \"Requerit\",\n    website: \"Picatz una URL valida\"\n  },\n  noteDescription: \"Enviatz v\\xF2stra bibliot\\xE8ca per \\xE8sser compresa al <link>repert\\xF2ri public de bibliot\\xE8ca</link>per que los autres l\\u2019utilizen dins lor dessenhs.\",\n  noteGuidelines: \"Qualqu\\u2019un deu aprovar la bibliot\\xE8ca manualament per comen\\xE7ar. Volgatz legir las <link>linhas directrises</link> abans de sometre. Vos far\\xE0 mesti\\xE8r un compte GitHub per comunicar e realizar de modificacions se demandadas, mas es pas compl\\xE8tament obligat\\xF2ri.\",\n  noteLicense: \"En sometent, acceptatz que la bibliot\\xE8ca si\\xE1 publicada sota la <link>Lic\\xE9ncia MIT, </link>que significa en br\\xE8u que qual que si\\xE1 p\\xF2t l\\u2019utilizar sens cap de restriccion.\",\n  noteItems: \"Cada element de bibliot\\xE8ca deu aver un nom pr\\xF2pri per \\xE8sser filtrable. Los elements de bibliot\\xE8ca seguentas ser\\xE0n incluses\\u202F:\",\n  atleastOneLibItem: \"Volgatz seleccionar almens un element de bibliot\\xE8ca per comen\\xE7ar\",\n  republishWarning: \"N\\xF2ta\\u202F: d\\u2019unes elements seleccionats son marcats ja coma publicats/enviats. Deuriatz sonque tornar enviar los elements pendent l\\u2019actualizacion d\\u2019una bibliot\\xE8ca existenta o un mandad\\xEDs.\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliot\\xE8ca somesa\",\n  content: \"Merc\\xE9s {{authorName}}. V\\xF2stre bibliot\\xE8ca es estada somesa per repassa. Pod\\xE8tz seguir l\\u2019avan\\xE7ament<link>aqu\\xED</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Re\\xEFnicializar la bibliot\\xE8ca\",\n  removeItemsFromLib: \"Tirar los elements seleccionats de la bibliot\\xE8ca\"\n};\nvar imageExportDialog = {\n  header: \"Exportar imatges\",\n  label: {\n    withBackground: \"R\\xE8ireplan\",\n    onlySelected: \"Seleccion sonque\",\n    darkMode: \"M\\xF2de escur\",\n    embedScene: \"Embarcar la sc\\xE8na\",\n    scale: \"Escala\",\n    padding: \"Espa\\xE7ament\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"Exportar en PNG\",\n    exportToSvg: \"Exportar en SVG\",\n    copyPngToClipboard: \"Copiar PNG al quichapapi\\xE8rs\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copiar al quichapapi\\xE8rs\"\n  }\n};\nvar encrypted = {\n  tooltip: \"V\\xF2stres dessenhs son chifrats del cap a la fin en consequ\\xE9ncia los servidors d\\u2019Excalidraw los veir\\xE0n pas jamai.\",\n  link: \"Article de bl\\xF2g sul chiframent del cap a la fin dins Excalidraw\"\n};\nvar stats = {\n  angle: \"Angle\",\n  element: \"Element\",\n  elements: \"Elements\",\n  height: \"Nautor\",\n  scene: \"Sc\\xE8na\",\n  selected: \"Seleccionat\",\n  storage: \"Emmagazinatge\",\n  title: \"Estatisticas pels nerds\",\n  total: \"Total\",\n  version: \"Version\",\n  versionCopy: \"Clicar per copiar\",\n  versionNotAvailable: \"Version pas disponibla\",\n  width: \"Largor\"\n};\nvar toast = {\n  addedToLibrary: \"Apondut a la bibliot\\xE8ca\",\n  copyStyles: \"Estiles copiats.\",\n  copyToClipboard: \"Copiats al quichapapi\\xE8rs.\",\n  copyToClipboardAsPng: \"{{exportSelection}} copiat coma PNG ({{exportColorScheme}})\",\n  fileSaved: \"Fichi\\xE8r enregistrat.\",\n  fileSavedToFilename: \"Enregistrat jos {filename}\",\n  canvas: \"canab\\xE0s\",\n  selection: \"seleccion\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transpar\\xE9ncia\",\n  black: \"Negre\",\n  white: \"Blanc\",\n  red: \"Roge\",\n  pink: \"R\\xF2se\",\n  grape: \"Bord\\xE8u\",\n  violet: \"Violet\",\n  gray: \"Gris\",\n  blue: \"Blau\",\n  cyan: \"Cian\",\n  teal: \"Sarc\\xE8la\",\n  green: \"Verd\",\n  yellow: \"Jaune\",\n  orange: \"Irange\",\n  bronze: \"Bronze\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Totas las donadas son enregistradas dins v\\xF2stre navegador.\",\n    center_heading_plus: \"Voliatz pusl\\xE8u utilizar Excalidraw+ a la pla\\xE7a\\u202F?\",\n    menuHint: \"Exportar, prefer\\xE9ncias, lengas, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportar, prefer\\xE9ncias, e mai...\",\n    center_heading: \"Diagram. Tot. Simplament.\",\n    toolbarHint: \"Pren\\xE8tz un esplech e comen\\xE7atz de dessenhar\\u202F!\",\n    helpHint: \"Acorchis e ajuda\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Colors personalizadas mai utilizadas\",\n  colors: \"Colors\",\n  shades: \"Nuan\\xE7as\",\n  hexCode: \"C\\xF2di exadecimal\",\n  noShades: \"Cap de nuan\\xE7a pas disponibla per aquesta color\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportar coma imatge\",\n      button: \"Exportar coma imatge\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"Salvar al disc\",\n      button: \"Salvar al disc\",\n      description: \"Exportar las donadas de la sc\\xE8na cap a un fichi\\xE8r que pod\\xE8tz importar mai tard.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exportar dins Excalidraw+\",\n      description: \"Enregistrar la sc\\xE8na dins v\\xF2stre espaci de trabalh Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Cargar d\\u2019un fichi\\xE8r\",\n      button: \"Cargar d\\u2019un fichi\\xE8r\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"Cargar d\\u2019un ligam\",\n      button: \"Rempla\\xE7ar mon contengut\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"De Mermaid cap a Excalidraw\",\n  button: \"Inserir\",\n  description: \"\",\n  syntax: \"Sintaxi Mermaid\",\n  preview: \"Apercebut\"\n};\nvar oc_FR_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=oc-FR-ATFBDMF6.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/oc-FR-ATFBDMF6.js\n"));

/***/ })

}]);