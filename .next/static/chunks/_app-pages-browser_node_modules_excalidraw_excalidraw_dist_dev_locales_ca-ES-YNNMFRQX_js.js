"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_ca-ES-YNNMFRQX_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/ca-ES-YNNMFRQX.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/ca-ES-YNNMFRQX.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ ca_ES_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/ca-ES.json\nvar labels = {\n  paste: \"Enganxa\",\n  pasteAsPlaintext: \"Enganxar com a text pla\",\n  pasteCharts: \"Enganxa els diagrames\",\n  selectAll: \"Selecciona-ho tot\",\n  multiSelect: \"Afegeix un element a la selecci\\xF3\",\n  moveCanvas: \"Mou el llen\\xE7\",\n  cut: \"Retalla\",\n  copy: \"Copia\",\n  copyAsPng: \"Copia al porta-retalls com a PNG\",\n  copyAsSvg: \"Copia al porta-retalls com a SVG\",\n  copyText: \"Copia al porta-retalls com a text\",\n  copySource: \"Copia l'origen al porta-retalls\",\n  convertToCode: \"\",\n  bringForward: \"Porta endavant\",\n  sendToBack: \"Envia enrere\",\n  bringToFront: \"Porta al davant\",\n  sendBackward: \"Envia al fons\",\n  delete: \"Elimina\",\n  copyStyles: \"Copia els estils\",\n  pasteStyles: \"Enganxa els estils\",\n  stroke: \"Color del tra\\xE7\",\n  background: \"Color del fons\",\n  fill: \"Estil del fons\",\n  strokeWidth: \"Amplada del tra\\xE7\",\n  strokeStyle: \"Estil del tra\\xE7\",\n  strokeStyle_solid: \"S\\xF2lid\",\n  strokeStyle_dashed: \"Guions\",\n  strokeStyle_dotted: \"Punts\",\n  sloppiness: \"Estil del tra\\xE7\",\n  opacity: \"Opacitat\",\n  textAlign: \"Alineaci\\xF3 del text\",\n  edges: \"Vores\",\n  sharp: \"Agut\",\n  round: \"Arrodonit\",\n  arrowheads: \"Puntes de fletxa\",\n  arrowhead_none: \"Cap\",\n  arrowhead_arrow: \"Fletxa\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Triangle\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Mida de lletra\",\n  fontFamily: \"Tipus de lletra\",\n  addWatermark: \"Afegeix-hi \\xABFet amb Excalidraw\\xBB\",\n  handDrawn: \"Dibuixat a m\\xE0\",\n  normal: \"Normal\",\n  code: \"Codi\",\n  small: \"Petit\",\n  medium: \"Mitj\\xE0\",\n  large: \"Gran\",\n  veryLarge: \"Molt gran\",\n  solid: \"S\\xF2lid\",\n  hachure: \"Ratlletes\",\n  zigzag: \"\",\n  crossHatch: \"Ratlletes creuades\",\n  thin: \"Fi\",\n  bold: \"Negreta\",\n  left: \"Esquerra\",\n  center: \"Centre\",\n  right: \"Dreta\",\n  extraBold: \"Extra negreta\",\n  architect: \"Arquitecte\",\n  artist: \"Artista\",\n  cartoonist: \"Dibuixant\",\n  fileTitle: \"Nom del fitxer\",\n  colorPicker: \"Selector de colors\",\n  canvasColors: \"Usat al llen\\xE7\",\n  canvasBackground: \"Fons del llen\\xE7\",\n  drawingCanvas: \"Llen\\xE7 de dibuix\",\n  layers: \"Capes\",\n  actions: \"Accions\",\n  language: \"Llengua\",\n  liveCollaboration: \"Col\\xB7laboraci\\xF3 en directe...\",\n  duplicateSelection: \"Duplica\",\n  untitled: \"Sense t\\xEDtol\",\n  name: \"Nom\",\n  yourName: \"El vostre nom\",\n  madeWithExcalidraw: \"Fet amb Excalidraw\",\n  group: \"Agrupa la selecci\\xF3\",\n  ungroup: \"Desagrupa la selecci\\xF3\",\n  collaborators: \"Col\\xB7laboradors\",\n  showGrid: \"Mostra la graella\",\n  addToLibrary: \"Afegir a la biblioteca\",\n  removeFromLibrary: \"Eliminar de la biblioteca\",\n  libraryLoadingMessage: \"S'est\\xE0 carregant la biblioteca\\u2026\",\n  libraries: \"Explora les biblioteques\",\n  loadingScene: \"S'est\\xE0 carregant l'escena\\u2026\",\n  align: \"Alinea\",\n  alignTop: \"Alinea a la part superior\",\n  alignBottom: \"Alinea a la part inferior\",\n  alignLeft: \"Alinea a l\\u2019esquerra\",\n  alignRight: \"Alinea a la dreta\",\n  centerVertically: \"Centra verticalment\",\n  centerHorizontally: \"Centra horitzontalment\",\n  distributeHorizontally: \"Distribueix horitzontalment\",\n  distributeVertically: \"Distribueix verticalment\",\n  flipHorizontal: \"Capgira horitzontalment\",\n  flipVertical: \"Capgira verticalment\",\n  viewMode: \"Mode de visualitzaci\\xF3\",\n  share: \"Comparteix\",\n  showStroke: \"Mostra el selector de color del tra\\xE7\",\n  showBackground: \"Mostra el selector de color de fons\",\n  toggleTheme: \"Activa o desactiva el tema\",\n  personalLib: \"Biblioteca personal\",\n  excalidrawLib: \"Biblioteca d'Excalidraw\",\n  decreaseFontSize: \"Redueix la mida de la lletra\",\n  increaseFontSize: \"Augmenta la mida de la lletra\",\n  unbindText: \"Desvincular el text\",\n  bindText: \"Ajusta el text al contenidor\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"Edita l'enlla\\xE7\",\n    editEmbed: \"Edita l'enlla\\xE7 i incrusta-ho\",\n    create: \"Crea un enlla\\xE7\",\n    createEmbed: \"\",\n    label: \"Enlla\\xE7\",\n    labelEmbed: \"\",\n    empty: \"No s'ha definit cap enlla\\xE7\"\n  },\n  lineEditor: {\n    edit: \"Editar l\\xEDnia\",\n    exit: \"Sortir de l'editor de l\\xEDnia\"\n  },\n  elementLock: {\n    lock: \"Bloca\",\n    unlock: \"Desbloca\",\n    lockAll: \"Bloca-ho tot\",\n    unlockAll: \"Desbloca-ho tot\"\n  },\n  statusPublished: \"Publicat\",\n  sidebarLock: \"Mant\\xE9 la barra lateral oberta\",\n  selectAllElementsInFrame: \"Selecciona tots els elements del marc\",\n  removeAllElementsFromFrame: \"Eliminat tots els elements del marc\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Encara no s'hi han afegit elements...\",\n  hint_emptyLibrary: \"Trieu un element o un llen\\xE7 per a afegir-lo aqu\\xED, o instal\\xB7leu una biblioteca del repositori p\\xFAblic, m\\xE9s avall.\",\n  hint_emptyPrivateLibrary: \"Trieu un element o un llen\\xE7 per a afegir-lo aqu\\xED.\"\n};\nvar buttons = {\n  clearReset: \"Neteja el llen\\xE7\",\n  exportJSON: \"Exporta a un fitxer\",\n  exportImage: \"Exporta la imatge...\",\n  export: \"Guardar a...\",\n  copyToClipboard: \"Copia al porta-retalls\",\n  save: \"Desa al fitxer actual\",\n  saveAs: \"Anomena i desa\",\n  load: \"Obrir\",\n  getShareableLink: \"Obt\\xE9 l'enlla\\xE7 per a compartir\",\n  close: \"Tanca\",\n  selectLanguage: \"Trieu la llengua\",\n  scrollBackToContent: \"Torna al contingut\",\n  zoomIn: \"Apropa't\",\n  zoomOut: \"Allunya't\",\n  resetZoom: \"Restableix el zoom\",\n  menu: \"Men\\xFA\",\n  done: \"Fet\",\n  edit: \"Edita\",\n  undo: \"Desf\\xE9s\",\n  redo: \"Ref\\xE9s\",\n  resetLibrary: \"Restableix la biblioteca\",\n  createNewRoom: \"Crea una sala nova\",\n  fullScreen: \"Pantalla completa\",\n  darkMode: \"Mode fosc\",\n  lightMode: \"Mode clar\",\n  zenMode: \"Mode zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Surt de mode zen\",\n  cancel: \"Cancel\\xB7la\",\n  clear: \"Neteja\",\n  remove: \"Suprimeix\",\n  embed: \"\",\n  publishLibrary: \"Publica\",\n  submit: \"Envia\",\n  confirm: \"Confirma\",\n  embeddableInteractionButton: \"Feu clic per interactuar\"\n};\nvar alerts = {\n  clearReset: \"S'esborrar\\xE0 tot el llen\\xE7. N'esteu segur?\",\n  couldNotCreateShareableLink: \"No s'ha pogut crear un enlla\\xE7 per a compartir.\",\n  couldNotCreateShareableLinkTooBig: \"No s\\u2019ha pogut crear un enlla\\xE7 per a compartir: l\\u2019escena \\xE9s massa gran\",\n  couldNotLoadInvalidFile: \"No s'ha pogut carregar un fitxer no v\\xE0lid\",\n  importBackendFailed: \"Importaci\\xF3 fallida.\",\n  cannotExportEmptyCanvas: \"No es pot exportar un llen\\xE7 buit.\",\n  couldNotCopyToClipboard: \"No s'ha pogut copiar al porta-retalls.\",\n  decryptFailed: \"No s'ha pogut desencriptar.\",\n  uploadedSecurly: \"La c\\xE0rrega s'ha assegurat amb xifratge punta a punta, cosa que significa que el servidor Excalidraw i tercers no poden llegir el contingut.\",\n  loadSceneOverridePrompt: \"Si carregas aquest dibuix extern, substituir\\xE1 el que tens. Vols continuar?\",\n  collabStopOverridePrompt: \"Aturar la sessi\\xF3 provocar\\xE0 la sobreescriptura del dibuix previ, que hi ha desat en l'emmagatzematge local. N'esteu segur?\\n\\n(Si voleu conservar el dibuix local, tanqueu la pentanya del navegador en comptes d'aturar la sessi\\xF3).\",\n  errorAddingToLibrary: \"No s'ha pogut afegir l'element a la biblioteca\",\n  errorRemovingFromLibrary: \"No s'ha pogut eliminar l'element de la biblioteca\",\n  confirmAddLibrary: \"Aix\\xF2 afegir\\xE0 {{numShapes}} forma(es) a la vostra biblioteca. Est\\xE0s segur?\",\n  imageDoesNotContainScene: \"Aquesta imatge no sembla contenir cap dada d'escena. Heu activat l'incrustaci\\xF3 de l'escena durant l'exportaci\\xF3?\",\n  cannotRestoreFromImage: \"L\\u2019escena no s\\u2019ha pogut restaurar des d\\u2019aquest fitxer d\\u2019imatge\",\n  invalidSceneUrl: \"No s'ha pogut importar l'escena des de l'adre\\xE7a URL proporcionada. Est\\xE0 malformada o no cont\\xE9 dades Excalidraw JSON v\\xE0lides.\",\n  resetLibrary: \"Aix\\xF2 buidar\\xE0 la biblioteca. N'esteu segur?\",\n  removeItemsFromsLibrary: \"Suprimir {{count}} element(s) de la biblioteca?\",\n  invalidEncryptionKey: \"La clau d'encriptaci\\xF3 ha de tenir 22 car\\xE0cters. La col\\xB7laboraci\\xF3 en directe est\\xE0 desactivada.\",\n  collabOfflineWarning: \"Sense connexi\\xF3 a internet disponible.\\nEls vostres canvis no seran guardats!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipus de fitxer no suportat.\",\n  imageInsertError: \"No s'ha pogut insertar la imatge, torneu-ho a provar m\\xE9s tard...\",\n  fileTooBig: \"El fitxer \\xE9s massa gros. La mida m\\xE0xima permesa \\xE9s {{maxSize}}.\",\n  svgImageInsertError: \"No ha estat possible inserir la imatge SVG. Les marques SVG semblen inv\\xE0lides.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG no v\\xE0lid.\",\n  cannotResolveCollabServer: \"No ha estat possible connectar amb el servidor collab. Si us plau recarregueu la p\\xE0gina i torneu a provar.\",\n  importLibraryError: \"No s'ha pogut carregar la biblioteca\",\n  collabSaveFailed: \"No s'ha pogut desar a la base de dades de fons. Si els problemes persisteixen, haur\\xEDeu de desar el fitxer localment per assegurar-vos que no perdeu el vostre treball.\",\n  collabSaveFailed_sizeExceeded: \"No s'ha pogut desar a la base de dades de fons, sembla que el llen\\xE7 \\xE9s massa gran. Haur\\xEDeu de desar el fitxer localment per assegurar-vos que no perdeu el vostre treball.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"No s'ha pogut enganxar.\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Selecci\\xF3\",\n  image: \"Insereix imatge\",\n  rectangle: \"Rectangle\",\n  diamond: \"Rombe\",\n  ellipse: \"El\\xB7lipse\",\n  arrow: \"Fletxa\",\n  line: \"L\\xEDnia\",\n  freedraw: \"Dibuix\",\n  text: \"Text\",\n  library: \"Biblioteca\",\n  lock: \"Mantenir activa l'eina seleccionada despr\\xE8s de dibuixar\",\n  penMode: \"Mode de llapis - evita tocar\",\n  link: \"Afegeix / actualitza l'enlla\\xE7 per a la forma seleccionada\",\n  eraser: \"Esborrador\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"M\\xE0 (eina de despla\\xE7ament)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"De Mermaid a Excalidraw\",\n  magicSettings: \"Prefer\\xE8ncies d'IA\"\n};\nvar headings = {\n  canvasActions: \"Accions del llen\\xE7\",\n  selectedShapeActions: \"Accions per a les formes seleccionades\",\n  shapes: \"Formes\"\n};\nvar hints = {\n  canvasPanning: \"Per moure el llen\\xE7, manteniu premuda la roda del ratol\\xED o la barra espaiadora mentre arrossegueu o utilitzeu l'eina manual\",\n  linearElement: \"Feu clic per a dibuixar m\\xFAltiples punts; arrossegueu per a una sola l\\xEDnia\",\n  freeDraw: \"Feu clic i arrossegueu, deixeu anar per a finalitzar\",\n  text: \"Consell: tamb\\xE9 podeu afegir text fent doble clic en qualsevol lloc amb l'eina de selecci\\xF3\",\n  embeddable: \"\",\n  text_selected: \"Feu doble clic o premeu Retorn per a editar el text\",\n  text_editing: \"Premeu Escapada o Ctrl+Retorn (o Ordre+Retorn) per a finalitzar l'edici\\xF3\",\n  linearElementMulti: \"Feu clic a l'ultim punt, o pitgeu Esc o Retorn per a finalitzar\",\n  lockAngle: \"Per restringir els angles, mantenir premut el maj\\xFAscul (SHIFT)\",\n  resize: \"Per restringir les proporcions mentres es canvia la mida, mantenir premut el maj\\xFAscul (SHIFT); per canviar la mida des del centre, mantenir premut ALT\",\n  resizeImage: \"Podeu redimensionar lliurement prement MAJ\\xDASCULA;\\nper a redimensionar des del centre, premeu ALT\",\n  rotate: \"Per restringir els angles mentre gira, mantenir premut el maj\\xFAscul (SHIFT)\",\n  lineEditor_info: \"Mantingueu premut Ctrl o Cmd i feu doble clic o premeu Ctrl o Cmd + Retorn per editar els punts\",\n  lineEditor_pointSelected: \"Premeu Suprimir per a eliminar el(s) punt(s), CtrlOrCmd+D per a duplicar-lo, o arrossegueu-lo per a moure'l\",\n  lineEditor_nothingSelected: \"Seleccioneu un punt per a editar-lo (premeu SHIFT si voleu\\nselecci\\xF3 m\\xFAltiple), o manteniu Alt i feu clic per a afegir m\\xE9s punts\",\n  placeImage: \"Feu clic per a col\\xB7locar la imatge o clic i arrossegar per a establir-ne la mida manualment\",\n  publishLibrary: \"Publiqueu la vostra pr\\xF2pia llibreria\",\n  bindTextToElement: \"Premeu enter per a afegir-hi text\",\n  deepBoxSelect: \"Manteniu CtrlOrCmd per a selecci\\xF3 profunda, i per a evitar l'arrossegament\",\n  eraserRevert: \"Mantingueu premuda Alt per a revertir els elements seleccionats per a esborrar\",\n  firefox_clipboard_write: '\\xC9s probable que aquesta funci\\xF3 es pugui activar posant la marca \"dom.events.asyncClipboard.clipboardItem\" a \"true\". Per canviar les marques del navegador al Firefox, visiteu la p\\xE0gina \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"No es pot mostrar la previsualitzaci\\xF3\",\n  canvasTooBig: \"Pot ser que el llen\\xE7 sigui massa gran.\",\n  canvasTooBigTip: \"Consell: proveu d\\u2019acostar una mica els elements m\\xE9s allunyats.\"\n};\nvar errorSplash = {\n  headingMain: \"S'ha produ\\xEFt un error. Proveu <button>recarregar la p\\xE0gina.</button>\",\n  clearCanvasMessage: \"Si la rec\\xE0rrega no funciona, proveu <button>esborrar el llen\\xE7.</button>\",\n  clearCanvasCaveat: \" Aix\\xF2 resultar\\xE0 en la p\\xE8rdua de feina \",\n  trackedToSentry: \"L'error amb l'identificador {{eventId}} s'ha rastrejat en el nostre sistema.\",\n  openIssueMessage: \"An\\xE0vem amb molta cura de no incloure la informaci\\xF3 de la vostra escena en l'error. Si l'escena no \\xE9s privada, podeu fer-ne el seguiment al nostre <button>rastrejador d'errors.</button> Incloeu la informaci\\xF3 a continuaci\\xF3 copiant i enganxant a GitHub Issues.\",\n  sceneContent: \"Contingut de l'escena:\"\n};\nvar roomDialog = {\n  desc_intro: \"Podeu convidar persones a la vostra escena actual a col\\xB7laborar amb v\\xF3s.\",\n  desc_privacy: \"No us preocupeu, la sessi\\xF3 utilitza el xifratge de punta a punta, de manera que qualsevol cosa que dibuixeu romandr\\xE0 privada. Ni tan sols el nostre servidor podr\\xE0 veure qu\\xE8 feu.\",\n  button_startSession: \"Inicia la sessi\\xF3\",\n  button_stopSession: \"Atura la sessi\\xF3\",\n  desc_inProgressIntro: \"La sessi\\xF3 de col\\xB7laboraci\\xF3 en directe est\\xE0 en marxa.\",\n  desc_shareLink: \"Comparteix aquest enlla\\xE7 amb qualsevol persona amb qui vulgueu col\\xB7laborar:\",\n  desc_exitSession: \"Si atureu la sessi\\xF3, us desconectareu de la sala, per\\xF2 podreu continuar treballant amb el dibuix localment. Tingues en compte que aix\\xF2 no afectar\\xE0 a altres persones, i encara podran col\\xB7laborar en la seva versi\\xF3.\",\n  shareTitle: \"Uniu-vos a una sessi\\xF3 de col\\xB7laboraci\\xF3 en directe a Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Error\"\n};\nvar exportDialog = {\n  disk_title: \"Desa al disc\",\n  disk_details: \"Exporta les dades de l'escena a un fitxer que despr\\xE9s podreu importar.\",\n  disk_button: \"Desa en un fitxer\",\n  link_title: \"Enlla\\xE7 per a compartir\",\n  link_details: \"Exporta com a un enlla\\xE7 de nom\\xE9s lectura.\",\n  link_button: \"Exporta a un enlla\\xE7\",\n  excalidrawplus_description: \"Desa l'escena en el vostre espai de treball Excalidraw+.\",\n  excalidrawplus_button: \"Exporta\",\n  excalidrawplus_exportError: \"No \\xE9s possible exportar a Excalidraw+ ara mateix...\"\n};\nvar helpDialog = {\n  blog: \"Llegiu el nostre blog\",\n  click: \"clic\",\n  deepSelect: \"Selecci\\xF3 profunda\",\n  deepBoxSelect: \"Seleccioneu profundament dins del quadre i eviteu arrossegar\",\n  curvedArrow: \"Fletxa corba\",\n  curvedLine: \"L\\xEDnia corba\",\n  documentation: \"Documentaci\\xF3\",\n  doubleClick: \"doble clic\",\n  drag: \"arrossega\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"Hi heu trobat un problema? Informeu-ne\",\n  howto: \"Seguiu les nostres guies\",\n  or: \"o\",\n  preventBinding: \"Prevenir vinculaci\\xF3 de la fletxa\",\n  tools: \"Eines\",\n  shortcuts: \"Dreceres de teclat\",\n  textFinish: \"Finalitza l'edici\\xF3 (editor de text)\",\n  textNewLine: \"Afegeix una l\\xEDnia nova (editor de text)\",\n  title: \"Ajuda\",\n  view: \"Visualitzaci\\xF3\",\n  zoomToFit: \"Zoom per veure tots els elements\",\n  zoomToSelection: \"Zoom per veure la selecci\\xF3\",\n  toggleElementLock: \"Blocar/desblocar la selecci\\xF3\",\n  movePageUpDown: \"Mou la p\\xE0gina cap amunt/a baix\",\n  movePageLeftRight: \"Mou la p\\xE0gina cap a l'esquerra/dreta\"\n};\nvar clearCanvasDialog = {\n  title: \"Neteja el llen\\xE7\"\n};\nvar publishDialog = {\n  title: \"Publica la biblioteca\",\n  itemName: \"Nom de l'element\",\n  authorName: \"Nom de l'autor/a\",\n  githubUsername: \"Nom d'usuari de GitHub\",\n  twitterUsername: \"Nom d'usuari de Twitter\",\n  libraryName: \"Nom de la biblioteca\",\n  libraryDesc: \"Descripci\\xF3 de la biblioteca\",\n  website: \"Lloc web\",\n  placeholder: {\n    authorName: \"Nom o usuari\",\n    libraryName: \"Nom de la vostra biblioteca\",\n    libraryDesc: \"Descripci\\xF3 de la biblioteca per a ajudar a la gent a entendre'n el funcionament\",\n    githubHandle: \"Identificador de GitHub (opcional), per tal que pugueu editar la biblioteca una vegada enviada per a ser revisada\",\n    twitterHandle: \"Usuari de twitter (opcional), per tal que puguem donar-vos cr\\xE8dit quan fem la promoci\\xF3 a Twitter\",\n    website: \"Enlla\\xE7 al vostre lloc web personal o a qualsevol altre (opcional)\"\n  },\n  errors: {\n    required: \"Requerit\",\n    website: \"Introdu\\xEFu una URL v\\xE0lida\"\n  },\n  noteDescription: \"Envieu la vostra biblioteca perqu\\xE8 sigui inclosa al <link>repositori p\\xFAblic</link>per tal que altres persones puguin fer-ne \\xFAs en els seus dibuixos.\",\n  noteGuidelines: \"La biblioteca ha de ser aprovada manualment. Si us plau, llegiu les <link>directrius</link> abans d'enviar-hi res. Necessitareu un compte de GitHub per a comunicar i fer-hi canvis si cal, per\\xF2 no \\xE9s requisit imprescindible.\",\n  noteLicense: \"Quan l'envieu, accepteu que la biblioteca sigui publicada sota la <link>llic\\xE8ncia MIT, </link>que, en resum, vol dir que qualsevol persona pot fer-ne \\xFAs sense restriccions.\",\n  noteItems: \"Cada element de la biblioteca ha de tenir el seu propi nom per tal que sigui filtrable. S'hi inclouran els elements seg\\xFCents:\",\n  atleastOneLibItem: \"Si us plau, seleccioneu si m\\xE9s no un element de la biblioteca per a comen\\xE7ar\",\n  republishWarning: \"Nota: alguns dels elements seleccionats s'han marcat com a publicats/enviats. Nom\\xE9s haur\\xEDeu de reenviar elements quan actualitzeu una biblioteca existent.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteca enviada\",\n  content: \"Gr\\xE0cies, {{authorName}}. La vostra biblioteca ha estat enviada per a ser revisada. Podeu comprovar-ne l'estat<link>aqu\\xED</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Restableix la biblioteca\",\n  removeItemsFromLib: \"Suprimeix els elements seleccionats de la llibreria\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"Nom\\xE9s els seleccionats\",\n    darkMode: \"Mode fosc\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"Exporta a PNG\",\n    exportToSvg: \"Exporta a SVG\",\n    copyPngToClipboard: \"Copia el PNG al porta-retalls\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Els vostres dibuixos estan xifrats de punta a punta de manera que els servidors d\\u2019Excalidraw no els veuran mai.\",\n  link: \"Article del blog sobre encriptaci\\xF3 d'extrem a extrem a Excalidraw\"\n};\nvar stats = {\n  angle: \"Angle\",\n  element: \"Element\",\n  elements: \"Elements\",\n  height: \"Altura\",\n  scene: \"Escena\",\n  selected: \"Seleccionat\",\n  storage: \"Emmagatzematge\",\n  title: \"Estad\\xEDstiques per nerds\",\n  total: \"Total\",\n  version: \"Versi\\xF3\",\n  versionCopy: \"Feu clic per a copiar\",\n  versionNotAvailable: \"Versi\\xF3 no disponible\",\n  width: \"Amplada\"\n};\nvar toast = {\n  addedToLibrary: \"Afegit a la biblioteca\",\n  copyStyles: \"S'han copiat els estils.\",\n  copyToClipboard: \"S'ha copiat al porta-retalls.\",\n  copyToClipboardAsPng: \"S'ha copiat {{exportSelection}} al porta-retalls en format PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"S'ha desat el fitxer.\",\n  fileSavedToFilename: \"S'ha desat a {filename}\",\n  canvas: \"el llen\\xE7\",\n  selection: \"la selecci\\xF3\",\n  pasteAsSingleElement: \"Fer servir {{shortcut}} per enganxar com un sol element,\\no enganxeu-lo en un editor de text existent\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transparent\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"Verd\",\n  yellow: \"Groc\",\n  orange: \"Taronja\",\n  bronze: \"Bronze\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Totes les vostres dades es guarden localment al vostre navegador.\",\n    center_heading_plus: \"Vols anar a Excalidraw+ en comptes?\",\n    menuHint: \"Exportar, prefer\\xE8ncies, llenguatges...\"\n  },\n  defaults: {\n    menuHint: \"Exportar, prefer\\xE8ncies i m\\xE9s...\",\n    center_heading: \"Diagrames. Fer. Simple.\",\n    toolbarHint: \"Selecciona una eina i comen\\xE7a a dibuixar!\",\n    helpHint: \"Dreceres i ajuda\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exporta com a imatge\",\n      button: \"Exporta com a imatge\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"Desa al disc\",\n      button: \"Desa al disc\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Carrega des d'un fitxer\",\n      button: \"Carrega des d'un fitxer\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"Carrega des d'un enlla\\xE7\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"De Mermaid a Excalidraw\",\n  button: \"Insereix\",\n  description: \"\",\n  syntax: \"Sintaxi de Mermaid\",\n  preview: \"Previsualitzaci\\xF3\"\n};\nvar ca_ES_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=ca-ES-YNNMFRQX.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvY2EtRVMtWU5OTUZSUVguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFlBQVk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsUUFBUTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkUsU0FBUztBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOERBQThEO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkdBQTZHO0FBQzdHLG9FQUFvRTtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsVUFBVTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixZQUFZO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLGtCQUFrQixtQ0FBbUMsbUJBQW1CO0FBQy9HO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0M7QUFDQTtBQUNBLHNDQUFzQyxXQUFXO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBOEJFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvQGV4Y2FsaWRyYXcvZXhjYWxpZHJhdy9kaXN0L2Rldi9sb2NhbGVzL2NhLUVTLVlOTk1GUlFYLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4uL2NodW5rLVhERkNVVVQ2LmpzXCI7XG5cbi8vIGxvY2FsZXMvY2EtRVMuanNvblxudmFyIGxhYmVscyA9IHtcbiAgcGFzdGU6IFwiRW5nYW54YVwiLFxuICBwYXN0ZUFzUGxhaW50ZXh0OiBcIkVuZ2FueGFyIGNvbSBhIHRleHQgcGxhXCIsXG4gIHBhc3RlQ2hhcnRzOiBcIkVuZ2FueGEgZWxzIGRpYWdyYW1lc1wiLFxuICBzZWxlY3RBbGw6IFwiU2VsZWNjaW9uYS1obyB0b3RcIixcbiAgbXVsdGlTZWxlY3Q6IFwiQWZlZ2VpeCB1biBlbGVtZW50IGEgbGEgc2VsZWNjaVxceEYzXCIsXG4gIG1vdmVDYW52YXM6IFwiTW91IGVsIGxsZW5cXHhFN1wiLFxuICBjdXQ6IFwiUmV0YWxsYVwiLFxuICBjb3B5OiBcIkNvcGlhXCIsXG4gIGNvcHlBc1BuZzogXCJDb3BpYSBhbCBwb3J0YS1yZXRhbGxzIGNvbSBhIFBOR1wiLFxuICBjb3B5QXNTdmc6IFwiQ29waWEgYWwgcG9ydGEtcmV0YWxscyBjb20gYSBTVkdcIixcbiAgY29weVRleHQ6IFwiQ29waWEgYWwgcG9ydGEtcmV0YWxscyBjb20gYSB0ZXh0XCIsXG4gIGNvcHlTb3VyY2U6IFwiQ29waWEgbCdvcmlnZW4gYWwgcG9ydGEtcmV0YWxsc1wiLFxuICBjb252ZXJ0VG9Db2RlOiBcIlwiLFxuICBicmluZ0ZvcndhcmQ6IFwiUG9ydGEgZW5kYXZhbnRcIixcbiAgc2VuZFRvQmFjazogXCJFbnZpYSBlbnJlcmVcIixcbiAgYnJpbmdUb0Zyb250OiBcIlBvcnRhIGFsIGRhdmFudFwiLFxuICBzZW5kQmFja3dhcmQ6IFwiRW52aWEgYWwgZm9uc1wiLFxuICBkZWxldGU6IFwiRWxpbWluYVwiLFxuICBjb3B5U3R5bGVzOiBcIkNvcGlhIGVscyBlc3RpbHNcIixcbiAgcGFzdGVTdHlsZXM6IFwiRW5nYW54YSBlbHMgZXN0aWxzXCIsXG4gIHN0cm9rZTogXCJDb2xvciBkZWwgdHJhXFx4RTdcIixcbiAgYmFja2dyb3VuZDogXCJDb2xvciBkZWwgZm9uc1wiLFxuICBmaWxsOiBcIkVzdGlsIGRlbCBmb25zXCIsXG4gIHN0cm9rZVdpZHRoOiBcIkFtcGxhZGEgZGVsIHRyYVxceEU3XCIsXG4gIHN0cm9rZVN0eWxlOiBcIkVzdGlsIGRlbCB0cmFcXHhFN1wiLFxuICBzdHJva2VTdHlsZV9zb2xpZDogXCJTXFx4RjJsaWRcIixcbiAgc3Ryb2tlU3R5bGVfZGFzaGVkOiBcIkd1aW9uc1wiLFxuICBzdHJva2VTdHlsZV9kb3R0ZWQ6IFwiUHVudHNcIixcbiAgc2xvcHBpbmVzczogXCJFc3RpbCBkZWwgdHJhXFx4RTdcIixcbiAgb3BhY2l0eTogXCJPcGFjaXRhdFwiLFxuICB0ZXh0QWxpZ246IFwiQWxpbmVhY2lcXHhGMyBkZWwgdGV4dFwiLFxuICBlZGdlczogXCJWb3Jlc1wiLFxuICBzaGFycDogXCJBZ3V0XCIsXG4gIHJvdW5kOiBcIkFycm9kb25pdFwiLFxuICBhcnJvd2hlYWRzOiBcIlB1bnRlcyBkZSBmbGV0eGFcIixcbiAgYXJyb3doZWFkX25vbmU6IFwiQ2FwXCIsXG4gIGFycm93aGVhZF9hcnJvdzogXCJGbGV0eGFcIixcbiAgYXJyb3doZWFkX2JhcjogXCJCYXJyYVwiLFxuICBhcnJvd2hlYWRfY2lyY2xlOiBcIlwiLFxuICBhcnJvd2hlYWRfY2lyY2xlX291dGxpbmU6IFwiXCIsXG4gIGFycm93aGVhZF90cmlhbmdsZTogXCJUcmlhbmdsZVwiLFxuICBhcnJvd2hlYWRfdHJpYW5nbGVfb3V0bGluZTogXCJcIixcbiAgYXJyb3doZWFkX2RpYW1vbmQ6IFwiXCIsXG4gIGFycm93aGVhZF9kaWFtb25kX291dGxpbmU6IFwiXCIsXG4gIGZvbnRTaXplOiBcIk1pZGEgZGUgbGxldHJhXCIsXG4gIGZvbnRGYW1pbHk6IFwiVGlwdXMgZGUgbGxldHJhXCIsXG4gIGFkZFdhdGVybWFyazogXCJBZmVnZWl4LWhpIFxceEFCRmV0IGFtYiBFeGNhbGlkcmF3XFx4QkJcIixcbiAgaGFuZERyYXduOiBcIkRpYnVpeGF0IGEgbVxceEUwXCIsXG4gIG5vcm1hbDogXCJOb3JtYWxcIixcbiAgY29kZTogXCJDb2RpXCIsXG4gIHNtYWxsOiBcIlBldGl0XCIsXG4gIG1lZGl1bTogXCJNaXRqXFx4RTBcIixcbiAgbGFyZ2U6IFwiR3JhblwiLFxuICB2ZXJ5TGFyZ2U6IFwiTW9sdCBncmFuXCIsXG4gIHNvbGlkOiBcIlNcXHhGMmxpZFwiLFxuICBoYWNodXJlOiBcIlJhdGxsZXRlc1wiLFxuICB6aWd6YWc6IFwiXCIsXG4gIGNyb3NzSGF0Y2g6IFwiUmF0bGxldGVzIGNyZXVhZGVzXCIsXG4gIHRoaW46IFwiRmlcIixcbiAgYm9sZDogXCJOZWdyZXRhXCIsXG4gIGxlZnQ6IFwiRXNxdWVycmFcIixcbiAgY2VudGVyOiBcIkNlbnRyZVwiLFxuICByaWdodDogXCJEcmV0YVwiLFxuICBleHRyYUJvbGQ6IFwiRXh0cmEgbmVncmV0YVwiLFxuICBhcmNoaXRlY3Q6IFwiQXJxdWl0ZWN0ZVwiLFxuICBhcnRpc3Q6IFwiQXJ0aXN0YVwiLFxuICBjYXJ0b29uaXN0OiBcIkRpYnVpeGFudFwiLFxuICBmaWxlVGl0bGU6IFwiTm9tIGRlbCBmaXR4ZXJcIixcbiAgY29sb3JQaWNrZXI6IFwiU2VsZWN0b3IgZGUgY29sb3JzXCIsXG4gIGNhbnZhc0NvbG9yczogXCJVc2F0IGFsIGxsZW5cXHhFN1wiLFxuICBjYW52YXNCYWNrZ3JvdW5kOiBcIkZvbnMgZGVsIGxsZW5cXHhFN1wiLFxuICBkcmF3aW5nQ2FudmFzOiBcIkxsZW5cXHhFNyBkZSBkaWJ1aXhcIixcbiAgbGF5ZXJzOiBcIkNhcGVzXCIsXG4gIGFjdGlvbnM6IFwiQWNjaW9uc1wiLFxuICBsYW5ndWFnZTogXCJMbGVuZ3VhXCIsXG4gIGxpdmVDb2xsYWJvcmF0aW9uOiBcIkNvbFxceEI3bGFib3JhY2lcXHhGMyBlbiBkaXJlY3RlLi4uXCIsXG4gIGR1cGxpY2F0ZVNlbGVjdGlvbjogXCJEdXBsaWNhXCIsXG4gIHVudGl0bGVkOiBcIlNlbnNlIHRcXHhFRHRvbFwiLFxuICBuYW1lOiBcIk5vbVwiLFxuICB5b3VyTmFtZTogXCJFbCB2b3N0cmUgbm9tXCIsXG4gIG1hZGVXaXRoRXhjYWxpZHJhdzogXCJGZXQgYW1iIEV4Y2FsaWRyYXdcIixcbiAgZ3JvdXA6IFwiQWdydXBhIGxhIHNlbGVjY2lcXHhGM1wiLFxuICB1bmdyb3VwOiBcIkRlc2FncnVwYSBsYSBzZWxlY2NpXFx4RjNcIixcbiAgY29sbGFib3JhdG9yczogXCJDb2xcXHhCN2xhYm9yYWRvcnNcIixcbiAgc2hvd0dyaWQ6IFwiTW9zdHJhIGxhIGdyYWVsbGFcIixcbiAgYWRkVG9MaWJyYXJ5OiBcIkFmZWdpciBhIGxhIGJpYmxpb3RlY2FcIixcbiAgcmVtb3ZlRnJvbUxpYnJhcnk6IFwiRWxpbWluYXIgZGUgbGEgYmlibGlvdGVjYVwiLFxuICBsaWJyYXJ5TG9hZGluZ01lc3NhZ2U6IFwiUydlc3RcXHhFMCBjYXJyZWdhbnQgbGEgYmlibGlvdGVjYVxcdTIwMjZcIixcbiAgbGlicmFyaWVzOiBcIkV4cGxvcmEgbGVzIGJpYmxpb3RlcXVlc1wiLFxuICBsb2FkaW5nU2NlbmU6IFwiUydlc3RcXHhFMCBjYXJyZWdhbnQgbCdlc2NlbmFcXHUyMDI2XCIsXG4gIGFsaWduOiBcIkFsaW5lYVwiLFxuICBhbGlnblRvcDogXCJBbGluZWEgYSBsYSBwYXJ0IHN1cGVyaW9yXCIsXG4gIGFsaWduQm90dG9tOiBcIkFsaW5lYSBhIGxhIHBhcnQgaW5mZXJpb3JcIixcbiAgYWxpZ25MZWZ0OiBcIkFsaW5lYSBhIGxcXHUyMDE5ZXNxdWVycmFcIixcbiAgYWxpZ25SaWdodDogXCJBbGluZWEgYSBsYSBkcmV0YVwiLFxuICBjZW50ZXJWZXJ0aWNhbGx5OiBcIkNlbnRyYSB2ZXJ0aWNhbG1lbnRcIixcbiAgY2VudGVySG9yaXpvbnRhbGx5OiBcIkNlbnRyYSBob3JpdHpvbnRhbG1lbnRcIixcbiAgZGlzdHJpYnV0ZUhvcml6b250YWxseTogXCJEaXN0cmlidWVpeCBob3JpdHpvbnRhbG1lbnRcIixcbiAgZGlzdHJpYnV0ZVZlcnRpY2FsbHk6IFwiRGlzdHJpYnVlaXggdmVydGljYWxtZW50XCIsXG4gIGZsaXBIb3Jpem9udGFsOiBcIkNhcGdpcmEgaG9yaXR6b250YWxtZW50XCIsXG4gIGZsaXBWZXJ0aWNhbDogXCJDYXBnaXJhIHZlcnRpY2FsbWVudFwiLFxuICB2aWV3TW9kZTogXCJNb2RlIGRlIHZpc3VhbGl0emFjaVxceEYzXCIsXG4gIHNoYXJlOiBcIkNvbXBhcnRlaXhcIixcbiAgc2hvd1N0cm9rZTogXCJNb3N0cmEgZWwgc2VsZWN0b3IgZGUgY29sb3IgZGVsIHRyYVxceEU3XCIsXG4gIHNob3dCYWNrZ3JvdW5kOiBcIk1vc3RyYSBlbCBzZWxlY3RvciBkZSBjb2xvciBkZSBmb25zXCIsXG4gIHRvZ2dsZVRoZW1lOiBcIkFjdGl2YSBvIGRlc2FjdGl2YSBlbCB0ZW1hXCIsXG4gIHBlcnNvbmFsTGliOiBcIkJpYmxpb3RlY2EgcGVyc29uYWxcIixcbiAgZXhjYWxpZHJhd0xpYjogXCJCaWJsaW90ZWNhIGQnRXhjYWxpZHJhd1wiLFxuICBkZWNyZWFzZUZvbnRTaXplOiBcIlJlZHVlaXggbGEgbWlkYSBkZSBsYSBsbGV0cmFcIixcbiAgaW5jcmVhc2VGb250U2l6ZTogXCJBdWdtZW50YSBsYSBtaWRhIGRlIGxhIGxsZXRyYVwiLFxuICB1bmJpbmRUZXh0OiBcIkRlc3ZpbmN1bGFyIGVsIHRleHRcIixcbiAgYmluZFRleHQ6IFwiQWp1c3RhIGVsIHRleHQgYWwgY29udGVuaWRvclwiLFxuICBjcmVhdGVDb250YWluZXJGcm9tVGV4dDogXCJcIixcbiAgbGluazoge1xuICAgIGVkaXQ6IFwiRWRpdGEgbCdlbmxsYVxceEU3XCIsXG4gICAgZWRpdEVtYmVkOiBcIkVkaXRhIGwnZW5sbGFcXHhFNyBpIGluY3J1c3RhLWhvXCIsXG4gICAgY3JlYXRlOiBcIkNyZWEgdW4gZW5sbGFcXHhFN1wiLFxuICAgIGNyZWF0ZUVtYmVkOiBcIlwiLFxuICAgIGxhYmVsOiBcIkVubGxhXFx4RTdcIixcbiAgICBsYWJlbEVtYmVkOiBcIlwiLFxuICAgIGVtcHR5OiBcIk5vIHMnaGEgZGVmaW5pdCBjYXAgZW5sbGFcXHhFN1wiXG4gIH0sXG4gIGxpbmVFZGl0b3I6IHtcbiAgICBlZGl0OiBcIkVkaXRhciBsXFx4RURuaWFcIixcbiAgICBleGl0OiBcIlNvcnRpciBkZSBsJ2VkaXRvciBkZSBsXFx4RURuaWFcIlxuICB9LFxuICBlbGVtZW50TG9jazoge1xuICAgIGxvY2s6IFwiQmxvY2FcIixcbiAgICB1bmxvY2s6IFwiRGVzYmxvY2FcIixcbiAgICBsb2NrQWxsOiBcIkJsb2NhLWhvIHRvdFwiLFxuICAgIHVubG9ja0FsbDogXCJEZXNibG9jYS1obyB0b3RcIlxuICB9LFxuICBzdGF0dXNQdWJsaXNoZWQ6IFwiUHVibGljYXRcIixcbiAgc2lkZWJhckxvY2s6IFwiTWFudFxceEU5IGxhIGJhcnJhIGxhdGVyYWwgb2JlcnRhXCIsXG4gIHNlbGVjdEFsbEVsZW1lbnRzSW5GcmFtZTogXCJTZWxlY2Npb25hIHRvdHMgZWxzIGVsZW1lbnRzIGRlbCBtYXJjXCIsXG4gIHJlbW92ZUFsbEVsZW1lbnRzRnJvbUZyYW1lOiBcIkVsaW1pbmF0IHRvdHMgZWxzIGVsZW1lbnRzIGRlbCBtYXJjXCIsXG4gIGV5ZURyb3BwZXI6IFwiXCIsXG4gIHRleHRUb0RpYWdyYW06IFwiXCIsXG4gIHByb21wdDogXCJcIlxufTtcbnZhciBsaWJyYXJ5ID0ge1xuICBub0l0ZW1zOiBcIkVuY2FyYSBubyBzJ2hpIGhhbiBhZmVnaXQgZWxlbWVudHMuLi5cIixcbiAgaGludF9lbXB0eUxpYnJhcnk6IFwiVHJpZXUgdW4gZWxlbWVudCBvIHVuIGxsZW5cXHhFNyBwZXIgYSBhZmVnaXItbG8gYXF1XFx4RUQsIG8gaW5zdGFsXFx4QjdsZXUgdW5hIGJpYmxpb3RlY2EgZGVsIHJlcG9zaXRvcmkgcFxceEZBYmxpYywgbVxceEU5cyBhdmFsbC5cIixcbiAgaGludF9lbXB0eVByaXZhdGVMaWJyYXJ5OiBcIlRyaWV1IHVuIGVsZW1lbnQgbyB1biBsbGVuXFx4RTcgcGVyIGEgYWZlZ2lyLWxvIGFxdVxceEVELlwiXG59O1xudmFyIGJ1dHRvbnMgPSB7XG4gIGNsZWFyUmVzZXQ6IFwiTmV0ZWphIGVsIGxsZW5cXHhFN1wiLFxuICBleHBvcnRKU09OOiBcIkV4cG9ydGEgYSB1biBmaXR4ZXJcIixcbiAgZXhwb3J0SW1hZ2U6IFwiRXhwb3J0YSBsYSBpbWF0Z2UuLi5cIixcbiAgZXhwb3J0OiBcIkd1YXJkYXIgYS4uLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiQ29waWEgYWwgcG9ydGEtcmV0YWxsc1wiLFxuICBzYXZlOiBcIkRlc2EgYWwgZml0eGVyIGFjdHVhbFwiLFxuICBzYXZlQXM6IFwiQW5vbWVuYSBpIGRlc2FcIixcbiAgbG9hZDogXCJPYnJpclwiLFxuICBnZXRTaGFyZWFibGVMaW5rOiBcIk9idFxceEU5IGwnZW5sbGFcXHhFNyBwZXIgYSBjb21wYXJ0aXJcIixcbiAgY2xvc2U6IFwiVGFuY2FcIixcbiAgc2VsZWN0TGFuZ3VhZ2U6IFwiVHJpZXUgbGEgbGxlbmd1YVwiLFxuICBzY3JvbGxCYWNrVG9Db250ZW50OiBcIlRvcm5hIGFsIGNvbnRpbmd1dFwiLFxuICB6b29tSW46IFwiQXByb3BhJ3RcIixcbiAgem9vbU91dDogXCJBbGx1bnlhJ3RcIixcbiAgcmVzZXRab29tOiBcIlJlc3RhYmxlaXggZWwgem9vbVwiLFxuICBtZW51OiBcIk1lblxceEZBXCIsXG4gIGRvbmU6IFwiRmV0XCIsXG4gIGVkaXQ6IFwiRWRpdGFcIixcbiAgdW5kbzogXCJEZXNmXFx4RTlzXCIsXG4gIHJlZG86IFwiUmVmXFx4RTlzXCIsXG4gIHJlc2V0TGlicmFyeTogXCJSZXN0YWJsZWl4IGxhIGJpYmxpb3RlY2FcIixcbiAgY3JlYXRlTmV3Um9vbTogXCJDcmVhIHVuYSBzYWxhIG5vdmFcIixcbiAgZnVsbFNjcmVlbjogXCJQYW50YWxsYSBjb21wbGV0YVwiLFxuICBkYXJrTW9kZTogXCJNb2RlIGZvc2NcIixcbiAgbGlnaHRNb2RlOiBcIk1vZGUgY2xhclwiLFxuICB6ZW5Nb2RlOiBcIk1vZGUgemVuXCIsXG4gIG9iamVjdHNTbmFwTW9kZTogXCJcIixcbiAgZXhpdFplbk1vZGU6IFwiU3VydCBkZSBtb2RlIHplblwiLFxuICBjYW5jZWw6IFwiQ2FuY2VsXFx4QjdsYVwiLFxuICBjbGVhcjogXCJOZXRlamFcIixcbiAgcmVtb3ZlOiBcIlN1cHJpbWVpeFwiLFxuICBlbWJlZDogXCJcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiUHVibGljYVwiLFxuICBzdWJtaXQ6IFwiRW52aWFcIixcbiAgY29uZmlybTogXCJDb25maXJtYVwiLFxuICBlbWJlZGRhYmxlSW50ZXJhY3Rpb25CdXR0b246IFwiRmV1IGNsaWMgcGVyIGludGVyYWN0dWFyXCJcbn07XG52YXIgYWxlcnRzID0ge1xuICBjbGVhclJlc2V0OiBcIlMnZXNib3JyYXJcXHhFMCB0b3QgZWwgbGxlblxceEU3LiBOJ2VzdGV1IHNlZ3VyP1wiLFxuICBjb3VsZE5vdENyZWF0ZVNoYXJlYWJsZUxpbms6IFwiTm8gcydoYSBwb2d1dCBjcmVhciB1biBlbmxsYVxceEU3IHBlciBhIGNvbXBhcnRpci5cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rVG9vQmlnOiBcIk5vIHNcXHUyMDE5aGEgcG9ndXQgY3JlYXIgdW4gZW5sbGFcXHhFNyBwZXIgYSBjb21wYXJ0aXI6IGxcXHUyMDE5ZXNjZW5hIFxceEU5cyBtYXNzYSBncmFuXCIsXG4gIGNvdWxkTm90TG9hZEludmFsaWRGaWxlOiBcIk5vIHMnaGEgcG9ndXQgY2FycmVnYXIgdW4gZml0eGVyIG5vIHZcXHhFMGxpZFwiLFxuICBpbXBvcnRCYWNrZW5kRmFpbGVkOiBcIkltcG9ydGFjaVxceEYzIGZhbGxpZGEuXCIsXG4gIGNhbm5vdEV4cG9ydEVtcHR5Q2FudmFzOiBcIk5vIGVzIHBvdCBleHBvcnRhciB1biBsbGVuXFx4RTcgYnVpdC5cIixcbiAgY291bGROb3RDb3B5VG9DbGlwYm9hcmQ6IFwiTm8gcydoYSBwb2d1dCBjb3BpYXIgYWwgcG9ydGEtcmV0YWxscy5cIixcbiAgZGVjcnlwdEZhaWxlZDogXCJObyBzJ2hhIHBvZ3V0IGRlc2VuY3JpcHRhci5cIixcbiAgdXBsb2FkZWRTZWN1cmx5OiBcIkxhIGNcXHhFMHJyZWdhIHMnaGEgYXNzZWd1cmF0IGFtYiB4aWZyYXRnZSBwdW50YSBhIHB1bnRhLCBjb3NhIHF1ZSBzaWduaWZpY2EgcXVlIGVsIHNlcnZpZG9yIEV4Y2FsaWRyYXcgaSB0ZXJjZXJzIG5vIHBvZGVuIGxsZWdpciBlbCBjb250aW5ndXQuXCIsXG4gIGxvYWRTY2VuZU92ZXJyaWRlUHJvbXB0OiBcIlNpIGNhcnJlZ2FzIGFxdWVzdCBkaWJ1aXggZXh0ZXJuLCBzdWJzdGl0dWlyXFx4RTEgZWwgcXVlIHRlbnMuIFZvbHMgY29udGludWFyP1wiLFxuICBjb2xsYWJTdG9wT3ZlcnJpZGVQcm9tcHQ6IFwiQXR1cmFyIGxhIHNlc3NpXFx4RjMgcHJvdm9jYXJcXHhFMCBsYSBzb2JyZWVzY3JpcHR1cmEgZGVsIGRpYnVpeCBwcmV2aSwgcXVlIGhpIGhhIGRlc2F0IGVuIGwnZW1tYWdhdHplbWF0Z2UgbG9jYWwuIE4nZXN0ZXUgc2VndXI/XFxuXFxuKFNpIHZvbGV1IGNvbnNlcnZhciBlbCBkaWJ1aXggbG9jYWwsIHRhbnF1ZXUgbGEgcGVudGFueWEgZGVsIG5hdmVnYWRvciBlbiBjb21wdGVzIGQnYXR1cmFyIGxhIHNlc3NpXFx4RjMpLlwiLFxuICBlcnJvckFkZGluZ1RvTGlicmFyeTogXCJObyBzJ2hhIHBvZ3V0IGFmZWdpciBsJ2VsZW1lbnQgYSBsYSBiaWJsaW90ZWNhXCIsXG4gIGVycm9yUmVtb3ZpbmdGcm9tTGlicmFyeTogXCJObyBzJ2hhIHBvZ3V0IGVsaW1pbmFyIGwnZWxlbWVudCBkZSBsYSBiaWJsaW90ZWNhXCIsXG4gIGNvbmZpcm1BZGRMaWJyYXJ5OiBcIkFpeFxceEYyIGFmZWdpclxceEUwIHt7bnVtU2hhcGVzfX0gZm9ybWEoZXMpIGEgbGEgdm9zdHJhIGJpYmxpb3RlY2EuIEVzdFxceEUwcyBzZWd1cj9cIixcbiAgaW1hZ2VEb2VzTm90Q29udGFpblNjZW5lOiBcIkFxdWVzdGEgaW1hdGdlIG5vIHNlbWJsYSBjb250ZW5pciBjYXAgZGFkYSBkJ2VzY2VuYS4gSGV1IGFjdGl2YXQgbCdpbmNydXN0YWNpXFx4RjMgZGUgbCdlc2NlbmEgZHVyYW50IGwnZXhwb3J0YWNpXFx4RjM/XCIsXG4gIGNhbm5vdFJlc3RvcmVGcm9tSW1hZ2U6IFwiTFxcdTIwMTllc2NlbmEgbm8gc1xcdTIwMTloYSBwb2d1dCByZXN0YXVyYXIgZGVzIGRcXHUyMDE5YXF1ZXN0IGZpdHhlciBkXFx1MjAxOWltYXRnZVwiLFxuICBpbnZhbGlkU2NlbmVVcmw6IFwiTm8gcydoYSBwb2d1dCBpbXBvcnRhciBsJ2VzY2VuYSBkZXMgZGUgbCdhZHJlXFx4RTdhIFVSTCBwcm9wb3JjaW9uYWRhLiBFc3RcXHhFMCBtYWxmb3JtYWRhIG8gbm8gY29udFxceEU5IGRhZGVzIEV4Y2FsaWRyYXcgSlNPTiB2XFx4RTBsaWRlcy5cIixcbiAgcmVzZXRMaWJyYXJ5OiBcIkFpeFxceEYyIGJ1aWRhclxceEUwIGxhIGJpYmxpb3RlY2EuIE4nZXN0ZXUgc2VndXI/XCIsXG4gIHJlbW92ZUl0ZW1zRnJvbXNMaWJyYXJ5OiBcIlN1cHJpbWlyIHt7Y291bnR9fSBlbGVtZW50KHMpIGRlIGxhIGJpYmxpb3RlY2E/XCIsXG4gIGludmFsaWRFbmNyeXB0aW9uS2V5OiBcIkxhIGNsYXUgZCdlbmNyaXB0YWNpXFx4RjMgaGEgZGUgdGVuaXIgMjIgY2FyXFx4RTBjdGVycy4gTGEgY29sXFx4QjdsYWJvcmFjaVxceEYzIGVuIGRpcmVjdGUgZXN0XFx4RTAgZGVzYWN0aXZhZGEuXCIsXG4gIGNvbGxhYk9mZmxpbmVXYXJuaW5nOiBcIlNlbnNlIGNvbm5leGlcXHhGMyBhIGludGVybmV0IGRpc3BvbmlibGUuXFxuRWxzIHZvc3RyZXMgY2FudmlzIG5vIHNlcmFuIGd1YXJkYXRzIVwiXG59O1xudmFyIGVycm9ycyA9IHtcbiAgdW5zdXBwb3J0ZWRGaWxlVHlwZTogXCJUaXB1cyBkZSBmaXR4ZXIgbm8gc3Vwb3J0YXQuXCIsXG4gIGltYWdlSW5zZXJ0RXJyb3I6IFwiTm8gcydoYSBwb2d1dCBpbnNlcnRhciBsYSBpbWF0Z2UsIHRvcm5ldS1obyBhIHByb3ZhciBtXFx4RTlzIHRhcmQuLi5cIixcbiAgZmlsZVRvb0JpZzogXCJFbCBmaXR4ZXIgXFx4RTlzIG1hc3NhIGdyb3MuIExhIG1pZGEgbVxceEUweGltYSBwZXJtZXNhIFxceEU5cyB7e21heFNpemV9fS5cIixcbiAgc3ZnSW1hZ2VJbnNlcnRFcnJvcjogXCJObyBoYSBlc3RhdCBwb3NzaWJsZSBpbnNlcmlyIGxhIGltYXRnZSBTVkcuIExlcyBtYXJxdWVzIFNWRyBzZW1ibGVuIGludlxceEUwbGlkZXMuXCIsXG4gIGZhaWxlZFRvRmV0Y2hJbWFnZTogXCJcIixcbiAgaW52YWxpZFNWR1N0cmluZzogXCJTVkcgbm8gdlxceEUwbGlkLlwiLFxuICBjYW5ub3RSZXNvbHZlQ29sbGFiU2VydmVyOiBcIk5vIGhhIGVzdGF0IHBvc3NpYmxlIGNvbm5lY3RhciBhbWIgZWwgc2Vydmlkb3IgY29sbGFiLiBTaSB1cyBwbGF1IHJlY2FycmVndWV1IGxhIHBcXHhFMGdpbmEgaSB0b3JuZXUgYSBwcm92YXIuXCIsXG4gIGltcG9ydExpYnJhcnlFcnJvcjogXCJObyBzJ2hhIHBvZ3V0IGNhcnJlZ2FyIGxhIGJpYmxpb3RlY2FcIixcbiAgY29sbGFiU2F2ZUZhaWxlZDogXCJObyBzJ2hhIHBvZ3V0IGRlc2FyIGEgbGEgYmFzZSBkZSBkYWRlcyBkZSBmb25zLiBTaSBlbHMgcHJvYmxlbWVzIHBlcnNpc3RlaXhlbiwgaGF1clxceEVEZXUgZGUgZGVzYXIgZWwgZml0eGVyIGxvY2FsbWVudCBwZXIgYXNzZWd1cmFyLXZvcyBxdWUgbm8gcGVyZGV1IGVsIHZvc3RyZSB0cmViYWxsLlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkX3NpemVFeGNlZWRlZDogXCJObyBzJ2hhIHBvZ3V0IGRlc2FyIGEgbGEgYmFzZSBkZSBkYWRlcyBkZSBmb25zLCBzZW1ibGEgcXVlIGVsIGxsZW5cXHhFNyBcXHhFOXMgbWFzc2EgZ3Jhbi4gSGF1clxceEVEZXUgZGUgZGVzYXIgZWwgZml0eGVyIGxvY2FsbWVudCBwZXIgYXNzZWd1cmFyLXZvcyBxdWUgbm8gcGVyZGV1IGVsIHZvc3RyZSB0cmViYWxsLlwiLFxuICBpbWFnZVRvb2xOb3RTdXBwb3J0ZWQ6IFwiXCIsXG4gIGJyYXZlX21lYXN1cmVfdGV4dF9lcnJvcjoge1xuICAgIGxpbmUxOiBcIlwiLFxuICAgIGxpbmUyOiBcIlwiLFxuICAgIGxpbmUzOiBcIlwiLFxuICAgIGxpbmU0OiBcIlwiXG4gIH0sXG4gIGxpYnJhcnlFbGVtZW50VHlwZUVycm9yOiB7XG4gICAgZW1iZWRkYWJsZTogXCJcIixcbiAgICBpZnJhbWU6IFwiXCIsXG4gICAgaW1hZ2U6IFwiXCJcbiAgfSxcbiAgYXN5bmNQYXN0ZUZhaWxlZE9uUmVhZDogXCJcIixcbiAgYXN5bmNQYXN0ZUZhaWxlZE9uUGFyc2U6IFwiTm8gcydoYSBwb2d1dCBlbmdhbnhhci5cIixcbiAgY29weVRvU3lzdGVtQ2xpcGJvYXJkRmFpbGVkOiBcIlwiXG59O1xudmFyIHRvb2xCYXIgPSB7XG4gIHNlbGVjdGlvbjogXCJTZWxlY2NpXFx4RjNcIixcbiAgaW1hZ2U6IFwiSW5zZXJlaXggaW1hdGdlXCIsXG4gIHJlY3RhbmdsZTogXCJSZWN0YW5nbGVcIixcbiAgZGlhbW9uZDogXCJSb21iZVwiLFxuICBlbGxpcHNlOiBcIkVsXFx4QjdsaXBzZVwiLFxuICBhcnJvdzogXCJGbGV0eGFcIixcbiAgbGluZTogXCJMXFx4RURuaWFcIixcbiAgZnJlZWRyYXc6IFwiRGlidWl4XCIsXG4gIHRleHQ6IFwiVGV4dFwiLFxuICBsaWJyYXJ5OiBcIkJpYmxpb3RlY2FcIixcbiAgbG9jazogXCJNYW50ZW5pciBhY3RpdmEgbCdlaW5hIHNlbGVjY2lvbmFkYSBkZXNwclxceEU4cyBkZSBkaWJ1aXhhclwiLFxuICBwZW5Nb2RlOiBcIk1vZGUgZGUgbGxhcGlzIC0gZXZpdGEgdG9jYXJcIixcbiAgbGluazogXCJBZmVnZWl4IC8gYWN0dWFsaXR6YSBsJ2VubGxhXFx4RTcgcGVyIGEgbGEgZm9ybWEgc2VsZWNjaW9uYWRhXCIsXG4gIGVyYXNlcjogXCJFc2JvcnJhZG9yXCIsXG4gIGZyYW1lOiBcIlwiLFxuICBtYWdpY2ZyYW1lOiBcIlwiLFxuICBlbWJlZGRhYmxlOiBcIlwiLFxuICBsYXNlcjogXCJcIixcbiAgaGFuZDogXCJNXFx4RTAgKGVpbmEgZGUgZGVzcGxhXFx4RTdhbWVudClcIixcbiAgZXh0cmFUb29sczogXCJcIixcbiAgbWVybWFpZFRvRXhjYWxpZHJhdzogXCJEZSBNZXJtYWlkIGEgRXhjYWxpZHJhd1wiLFxuICBtYWdpY1NldHRpbmdzOiBcIlByZWZlclxceEU4bmNpZXMgZCdJQVwiXG59O1xudmFyIGhlYWRpbmdzID0ge1xuICBjYW52YXNBY3Rpb25zOiBcIkFjY2lvbnMgZGVsIGxsZW5cXHhFN1wiLFxuICBzZWxlY3RlZFNoYXBlQWN0aW9uczogXCJBY2Npb25zIHBlciBhIGxlcyBmb3JtZXMgc2VsZWNjaW9uYWRlc1wiLFxuICBzaGFwZXM6IFwiRm9ybWVzXCJcbn07XG52YXIgaGludHMgPSB7XG4gIGNhbnZhc1Bhbm5pbmc6IFwiUGVyIG1vdXJlIGVsIGxsZW5cXHhFNywgbWFudGVuaXUgcHJlbXVkYSBsYSByb2RhIGRlbCByYXRvbFxceEVEIG8gbGEgYmFycmEgZXNwYWlhZG9yYSBtZW50cmUgYXJyb3NzZWd1ZXUgbyB1dGlsaXR6ZXUgbCdlaW5hIG1hbnVhbFwiLFxuICBsaW5lYXJFbGVtZW50OiBcIkZldSBjbGljIHBlciBhIGRpYnVpeGFyIG1cXHhGQWx0aXBsZXMgcHVudHM7IGFycm9zc2VndWV1IHBlciBhIHVuYSBzb2xhIGxcXHhFRG5pYVwiLFxuICBmcmVlRHJhdzogXCJGZXUgY2xpYyBpIGFycm9zc2VndWV1LCBkZWl4ZXUgYW5hciBwZXIgYSBmaW5hbGl0emFyXCIsXG4gIHRleHQ6IFwiQ29uc2VsbDogdGFtYlxceEU5IHBvZGV1IGFmZWdpciB0ZXh0IGZlbnQgZG9ibGUgY2xpYyBlbiBxdWFsc2V2b2wgbGxvYyBhbWIgbCdlaW5hIGRlIHNlbGVjY2lcXHhGM1wiLFxuICBlbWJlZGRhYmxlOiBcIlwiLFxuICB0ZXh0X3NlbGVjdGVkOiBcIkZldSBkb2JsZSBjbGljIG8gcHJlbWV1IFJldG9ybiBwZXIgYSBlZGl0YXIgZWwgdGV4dFwiLFxuICB0ZXh0X2VkaXRpbmc6IFwiUHJlbWV1IEVzY2FwYWRhIG8gQ3RybCtSZXRvcm4gKG8gT3JkcmUrUmV0b3JuKSBwZXIgYSBmaW5hbGl0emFyIGwnZWRpY2lcXHhGM1wiLFxuICBsaW5lYXJFbGVtZW50TXVsdGk6IFwiRmV1IGNsaWMgYSBsJ3VsdGltIHB1bnQsIG8gcGl0Z2V1IEVzYyBvIFJldG9ybiBwZXIgYSBmaW5hbGl0emFyXCIsXG4gIGxvY2tBbmdsZTogXCJQZXIgcmVzdHJpbmdpciBlbHMgYW5nbGVzLCBtYW50ZW5pciBwcmVtdXQgZWwgbWFqXFx4RkFzY3VsIChTSElGVClcIixcbiAgcmVzaXplOiBcIlBlciByZXN0cmluZ2lyIGxlcyBwcm9wb3JjaW9ucyBtZW50cmVzIGVzIGNhbnZpYSBsYSBtaWRhLCBtYW50ZW5pciBwcmVtdXQgZWwgbWFqXFx4RkFzY3VsIChTSElGVCk7IHBlciBjYW52aWFyIGxhIG1pZGEgZGVzIGRlbCBjZW50cmUsIG1hbnRlbmlyIHByZW11dCBBTFRcIixcbiAgcmVzaXplSW1hZ2U6IFwiUG9kZXUgcmVkaW1lbnNpb25hciBsbGl1cmVtZW50IHByZW1lbnQgTUFKXFx4REFTQ1VMQTtcXG5wZXIgYSByZWRpbWVuc2lvbmFyIGRlcyBkZWwgY2VudHJlLCBwcmVtZXUgQUxUXCIsXG4gIHJvdGF0ZTogXCJQZXIgcmVzdHJpbmdpciBlbHMgYW5nbGVzIG1lbnRyZSBnaXJhLCBtYW50ZW5pciBwcmVtdXQgZWwgbWFqXFx4RkFzY3VsIChTSElGVClcIixcbiAgbGluZUVkaXRvcl9pbmZvOiBcIk1hbnRpbmd1ZXUgcHJlbXV0IEN0cmwgbyBDbWQgaSBmZXUgZG9ibGUgY2xpYyBvIHByZW1ldSBDdHJsIG8gQ21kICsgUmV0b3JuIHBlciBlZGl0YXIgZWxzIHB1bnRzXCIsXG4gIGxpbmVFZGl0b3JfcG9pbnRTZWxlY3RlZDogXCJQcmVtZXUgU3VwcmltaXIgcGVyIGEgZWxpbWluYXIgZWwocykgcHVudChzKSwgQ3RybE9yQ21kK0QgcGVyIGEgZHVwbGljYXItbG8sIG8gYXJyb3NzZWd1ZXUtbG8gcGVyIGEgbW91cmUnbFwiLFxuICBsaW5lRWRpdG9yX25vdGhpbmdTZWxlY3RlZDogXCJTZWxlY2Npb25ldSB1biBwdW50IHBlciBhIGVkaXRhci1sbyAocHJlbWV1IFNISUZUIHNpIHZvbGV1XFxuc2VsZWNjaVxceEYzIG1cXHhGQWx0aXBsZSksIG8gbWFudGVuaXUgQWx0IGkgZmV1IGNsaWMgcGVyIGEgYWZlZ2lyIG1cXHhFOXMgcHVudHNcIixcbiAgcGxhY2VJbWFnZTogXCJGZXUgY2xpYyBwZXIgYSBjb2xcXHhCN2xvY2FyIGxhIGltYXRnZSBvIGNsaWMgaSBhcnJvc3NlZ2FyIHBlciBhIGVzdGFibGlyLW5lIGxhIG1pZGEgbWFudWFsbWVudFwiLFxuICBwdWJsaXNoTGlicmFyeTogXCJQdWJsaXF1ZXUgbGEgdm9zdHJhIHByXFx4RjJwaWEgbGxpYnJlcmlhXCIsXG4gIGJpbmRUZXh0VG9FbGVtZW50OiBcIlByZW1ldSBlbnRlciBwZXIgYSBhZmVnaXItaGkgdGV4dFwiLFxuICBkZWVwQm94U2VsZWN0OiBcIk1hbnRlbml1IEN0cmxPckNtZCBwZXIgYSBzZWxlY2NpXFx4RjMgcHJvZnVuZGEsIGkgcGVyIGEgZXZpdGFyIGwnYXJyb3NzZWdhbWVudFwiLFxuICBlcmFzZXJSZXZlcnQ6IFwiTWFudGluZ3VldSBwcmVtdWRhIEFsdCBwZXIgYSByZXZlcnRpciBlbHMgZWxlbWVudHMgc2VsZWNjaW9uYXRzIHBlciBhIGVzYm9ycmFyXCIsXG4gIGZpcmVmb3hfY2xpcGJvYXJkX3dyaXRlOiAnXFx4QzlzIHByb2JhYmxlIHF1ZSBhcXVlc3RhIGZ1bmNpXFx4RjMgZXMgcHVndWkgYWN0aXZhciBwb3NhbnQgbGEgbWFyY2EgXCJkb20uZXZlbnRzLmFzeW5jQ2xpcGJvYXJkLmNsaXBib2FyZEl0ZW1cIiBhIFwidHJ1ZVwiLiBQZXIgY2FudmlhciBsZXMgbWFycXVlcyBkZWwgbmF2ZWdhZG9yIGFsIEZpcmVmb3gsIHZpc2l0ZXUgbGEgcFxceEUwZ2luYSBcImFib3V0OmNvbmZpZ1wiLicsXG4gIGRpc2FibGVTbmFwcGluZzogXCJcIlxufTtcbnZhciBjYW52YXNFcnJvciA9IHtcbiAgY2Fubm90U2hvd1ByZXZpZXc6IFwiTm8gZXMgcG90IG1vc3RyYXIgbGEgcHJldmlzdWFsaXR6YWNpXFx4RjNcIixcbiAgY2FudmFzVG9vQmlnOiBcIlBvdCBzZXIgcXVlIGVsIGxsZW5cXHhFNyBzaWd1aSBtYXNzYSBncmFuLlwiLFxuICBjYW52YXNUb29CaWdUaXA6IFwiQ29uc2VsbDogcHJvdmV1IGRcXHUyMDE5YWNvc3RhciB1bmEgbWljYSBlbHMgZWxlbWVudHMgbVxceEU5cyBhbGx1bnlhdHMuXCJcbn07XG52YXIgZXJyb3JTcGxhc2ggPSB7XG4gIGhlYWRpbmdNYWluOiBcIlMnaGEgcHJvZHVcXHhFRnQgdW4gZXJyb3IuIFByb3ZldSA8YnV0dG9uPnJlY2FycmVnYXIgbGEgcFxceEUwZ2luYS48L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNNZXNzYWdlOiBcIlNpIGxhIHJlY1xceEUwcnJlZ2Egbm8gZnVuY2lvbmEsIHByb3ZldSA8YnV0dG9uPmVzYm9ycmFyIGVsIGxsZW5cXHhFNy48L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNDYXZlYXQ6IFwiIEFpeFxceEYyIHJlc3VsdGFyXFx4RTAgZW4gbGEgcFxceEU4cmR1YSBkZSBmZWluYSBcIixcbiAgdHJhY2tlZFRvU2VudHJ5OiBcIkwnZXJyb3IgYW1iIGwnaWRlbnRpZmljYWRvciB7e2V2ZW50SWR9fSBzJ2hhIHJhc3RyZWphdCBlbiBlbCBub3N0cmUgc2lzdGVtYS5cIixcbiAgb3Blbklzc3VlTWVzc2FnZTogXCJBblxceEUwdmVtIGFtYiBtb2x0YSBjdXJhIGRlIG5vIGluY2xvdXJlIGxhIGluZm9ybWFjaVxceEYzIGRlIGxhIHZvc3RyYSBlc2NlbmEgZW4gbCdlcnJvci4gU2kgbCdlc2NlbmEgbm8gXFx4RTlzIHByaXZhZGEsIHBvZGV1IGZlci1uZSBlbCBzZWd1aW1lbnQgYWwgbm9zdHJlIDxidXR0b24+cmFzdHJlamFkb3IgZCdlcnJvcnMuPC9idXR0b24+IEluY2xvZXUgbGEgaW5mb3JtYWNpXFx4RjMgYSBjb250aW51YWNpXFx4RjMgY29waWFudCBpIGVuZ2FueGFudCBhIEdpdEh1YiBJc3N1ZXMuXCIsXG4gIHNjZW5lQ29udGVudDogXCJDb250aW5ndXQgZGUgbCdlc2NlbmE6XCJcbn07XG52YXIgcm9vbURpYWxvZyA9IHtcbiAgZGVzY19pbnRybzogXCJQb2RldSBjb252aWRhciBwZXJzb25lcyBhIGxhIHZvc3RyYSBlc2NlbmEgYWN0dWFsIGEgY29sXFx4QjdsYWJvcmFyIGFtYiB2XFx4RjNzLlwiLFxuICBkZXNjX3ByaXZhY3k6IFwiTm8gdXMgcHJlb2N1cGV1LCBsYSBzZXNzaVxceEYzIHV0aWxpdHphIGVsIHhpZnJhdGdlIGRlIHB1bnRhIGEgcHVudGEsIGRlIG1hbmVyYSBxdWUgcXVhbHNldm9sIGNvc2EgcXVlIGRpYnVpeGV1IHJvbWFuZHJcXHhFMCBwcml2YWRhLiBOaSB0YW4gc29scyBlbCBub3N0cmUgc2Vydmlkb3IgcG9kclxceEUwIHZldXJlIHF1XFx4RTggZmV1LlwiLFxuICBidXR0b25fc3RhcnRTZXNzaW9uOiBcIkluaWNpYSBsYSBzZXNzaVxceEYzXCIsXG4gIGJ1dHRvbl9zdG9wU2Vzc2lvbjogXCJBdHVyYSBsYSBzZXNzaVxceEYzXCIsXG4gIGRlc2NfaW5Qcm9ncmVzc0ludHJvOiBcIkxhIHNlc3NpXFx4RjMgZGUgY29sXFx4QjdsYWJvcmFjaVxceEYzIGVuIGRpcmVjdGUgZXN0XFx4RTAgZW4gbWFyeGEuXCIsXG4gIGRlc2Nfc2hhcmVMaW5rOiBcIkNvbXBhcnRlaXggYXF1ZXN0IGVubGxhXFx4RTcgYW1iIHF1YWxzZXZvbCBwZXJzb25hIGFtYiBxdWkgdnVsZ3VldSBjb2xcXHhCN2xhYm9yYXI6XCIsXG4gIGRlc2NfZXhpdFNlc3Npb246IFwiU2kgYXR1cmV1IGxhIHNlc3NpXFx4RjMsIHVzIGRlc2NvbmVjdGFyZXUgZGUgbGEgc2FsYSwgcGVyXFx4RjIgcG9kcmV1IGNvbnRpbnVhciB0cmViYWxsYW50IGFtYiBlbCBkaWJ1aXggbG9jYWxtZW50LiBUaW5ndWVzIGVuIGNvbXB0ZSBxdWUgYWl4XFx4RjIgbm8gYWZlY3RhclxceEUwIGEgYWx0cmVzIHBlcnNvbmVzLCBpIGVuY2FyYSBwb2RyYW4gY29sXFx4QjdsYWJvcmFyIGVuIGxhIHNldmEgdmVyc2lcXHhGMy5cIixcbiAgc2hhcmVUaXRsZTogXCJVbml1LXZvcyBhIHVuYSBzZXNzaVxceEYzIGRlIGNvbFxceEI3bGFib3JhY2lcXHhGMyBlbiBkaXJlY3RlIGEgRXhjYWxpZHJhd1wiXG59O1xudmFyIGVycm9yRGlhbG9nID0ge1xuICB0aXRsZTogXCJFcnJvclwiXG59O1xudmFyIGV4cG9ydERpYWxvZyA9IHtcbiAgZGlza190aXRsZTogXCJEZXNhIGFsIGRpc2NcIixcbiAgZGlza19kZXRhaWxzOiBcIkV4cG9ydGEgbGVzIGRhZGVzIGRlIGwnZXNjZW5hIGEgdW4gZml0eGVyIHF1ZSBkZXNwclxceEU5cyBwb2RyZXUgaW1wb3J0YXIuXCIsXG4gIGRpc2tfYnV0dG9uOiBcIkRlc2EgZW4gdW4gZml0eGVyXCIsXG4gIGxpbmtfdGl0bGU6IFwiRW5sbGFcXHhFNyBwZXIgYSBjb21wYXJ0aXJcIixcbiAgbGlua19kZXRhaWxzOiBcIkV4cG9ydGEgY29tIGEgdW4gZW5sbGFcXHhFNyBkZSBub21cXHhFOXMgbGVjdHVyYS5cIixcbiAgbGlua19idXR0b246IFwiRXhwb3J0YSBhIHVuIGVubGxhXFx4RTdcIixcbiAgZXhjYWxpZHJhd3BsdXNfZGVzY3JpcHRpb246IFwiRGVzYSBsJ2VzY2VuYSBlbiBlbCB2b3N0cmUgZXNwYWkgZGUgdHJlYmFsbCBFeGNhbGlkcmF3Ky5cIixcbiAgZXhjYWxpZHJhd3BsdXNfYnV0dG9uOiBcIkV4cG9ydGFcIixcbiAgZXhjYWxpZHJhd3BsdXNfZXhwb3J0RXJyb3I6IFwiTm8gXFx4RTlzIHBvc3NpYmxlIGV4cG9ydGFyIGEgRXhjYWxpZHJhdysgYXJhIG1hdGVpeC4uLlwiXG59O1xudmFyIGhlbHBEaWFsb2cgPSB7XG4gIGJsb2c6IFwiTGxlZ2l1IGVsIG5vc3RyZSBibG9nXCIsXG4gIGNsaWNrOiBcImNsaWNcIixcbiAgZGVlcFNlbGVjdDogXCJTZWxlY2NpXFx4RjMgcHJvZnVuZGFcIixcbiAgZGVlcEJveFNlbGVjdDogXCJTZWxlY2Npb25ldSBwcm9mdW5kYW1lbnQgZGlucyBkZWwgcXVhZHJlIGkgZXZpdGV1IGFycm9zc2VnYXJcIixcbiAgY3VydmVkQXJyb3c6IFwiRmxldHhhIGNvcmJhXCIsXG4gIGN1cnZlZExpbmU6IFwiTFxceEVEbmlhIGNvcmJhXCIsXG4gIGRvY3VtZW50YXRpb246IFwiRG9jdW1lbnRhY2lcXHhGM1wiLFxuICBkb3VibGVDbGljazogXCJkb2JsZSBjbGljXCIsXG4gIGRyYWc6IFwiYXJyb3NzZWdhXCIsXG4gIGVkaXRvcjogXCJFZGl0b3JcIixcbiAgZWRpdExpbmVBcnJvd1BvaW50czogXCJcIixcbiAgZWRpdFRleHQ6IFwiXCIsXG4gIGdpdGh1YjogXCJIaSBoZXUgdHJvYmF0IHVuIHByb2JsZW1hPyBJbmZvcm1ldS1uZVwiLFxuICBob3d0bzogXCJTZWd1aXUgbGVzIG5vc3RyZXMgZ3VpZXNcIixcbiAgb3I6IFwib1wiLFxuICBwcmV2ZW50QmluZGluZzogXCJQcmV2ZW5pciB2aW5jdWxhY2lcXHhGMyBkZSBsYSBmbGV0eGFcIixcbiAgdG9vbHM6IFwiRWluZXNcIixcbiAgc2hvcnRjdXRzOiBcIkRyZWNlcmVzIGRlIHRlY2xhdFwiLFxuICB0ZXh0RmluaXNoOiBcIkZpbmFsaXR6YSBsJ2VkaWNpXFx4RjMgKGVkaXRvciBkZSB0ZXh0KVwiLFxuICB0ZXh0TmV3TGluZTogXCJBZmVnZWl4IHVuYSBsXFx4RURuaWEgbm92YSAoZWRpdG9yIGRlIHRleHQpXCIsXG4gIHRpdGxlOiBcIkFqdWRhXCIsXG4gIHZpZXc6IFwiVmlzdWFsaXR6YWNpXFx4RjNcIixcbiAgem9vbVRvRml0OiBcIlpvb20gcGVyIHZldXJlIHRvdHMgZWxzIGVsZW1lbnRzXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJab29tIHBlciB2ZXVyZSBsYSBzZWxlY2NpXFx4RjNcIixcbiAgdG9nZ2xlRWxlbWVudExvY2s6IFwiQmxvY2FyL2Rlc2Jsb2NhciBsYSBzZWxlY2NpXFx4RjNcIixcbiAgbW92ZVBhZ2VVcERvd246IFwiTW91IGxhIHBcXHhFMGdpbmEgY2FwIGFtdW50L2EgYmFpeFwiLFxuICBtb3ZlUGFnZUxlZnRSaWdodDogXCJNb3UgbGEgcFxceEUwZ2luYSBjYXAgYSBsJ2VzcXVlcnJhL2RyZXRhXCJcbn07XG52YXIgY2xlYXJDYW52YXNEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIk5ldGVqYSBlbCBsbGVuXFx4RTdcIlxufTtcbnZhciBwdWJsaXNoRGlhbG9nID0ge1xuICB0aXRsZTogXCJQdWJsaWNhIGxhIGJpYmxpb3RlY2FcIixcbiAgaXRlbU5hbWU6IFwiTm9tIGRlIGwnZWxlbWVudFwiLFxuICBhdXRob3JOYW1lOiBcIk5vbSBkZSBsJ2F1dG9yL2FcIixcbiAgZ2l0aHViVXNlcm5hbWU6IFwiTm9tIGQndXN1YXJpIGRlIEdpdEh1YlwiLFxuICB0d2l0dGVyVXNlcm5hbWU6IFwiTm9tIGQndXN1YXJpIGRlIFR3aXR0ZXJcIixcbiAgbGlicmFyeU5hbWU6IFwiTm9tIGRlIGxhIGJpYmxpb3RlY2FcIixcbiAgbGlicmFyeURlc2M6IFwiRGVzY3JpcGNpXFx4RjMgZGUgbGEgYmlibGlvdGVjYVwiLFxuICB3ZWJzaXRlOiBcIkxsb2Mgd2ViXCIsXG4gIHBsYWNlaG9sZGVyOiB7XG4gICAgYXV0aG9yTmFtZTogXCJOb20gbyB1c3VhcmlcIixcbiAgICBsaWJyYXJ5TmFtZTogXCJOb20gZGUgbGEgdm9zdHJhIGJpYmxpb3RlY2FcIixcbiAgICBsaWJyYXJ5RGVzYzogXCJEZXNjcmlwY2lcXHhGMyBkZSBsYSBiaWJsaW90ZWNhIHBlciBhIGFqdWRhciBhIGxhIGdlbnQgYSBlbnRlbmRyZSduIGVsIGZ1bmNpb25hbWVudFwiLFxuICAgIGdpdGh1YkhhbmRsZTogXCJJZGVudGlmaWNhZG9yIGRlIEdpdEh1YiAob3BjaW9uYWwpLCBwZXIgdGFsIHF1ZSBwdWd1ZXUgZWRpdGFyIGxhIGJpYmxpb3RlY2EgdW5hIHZlZ2FkYSBlbnZpYWRhIHBlciBhIHNlciByZXZpc2FkYVwiLFxuICAgIHR3aXR0ZXJIYW5kbGU6IFwiVXN1YXJpIGRlIHR3aXR0ZXIgKG9wY2lvbmFsKSwgcGVyIHRhbCBxdWUgcHVndWVtIGRvbmFyLXZvcyBjclxceEU4ZGl0IHF1YW4gZmVtIGxhIHByb21vY2lcXHhGMyBhIFR3aXR0ZXJcIixcbiAgICB3ZWJzaXRlOiBcIkVubGxhXFx4RTcgYWwgdm9zdHJlIGxsb2Mgd2ViIHBlcnNvbmFsIG8gYSBxdWFsc2V2b2wgYWx0cmUgKG9wY2lvbmFsKVwiXG4gIH0sXG4gIGVycm9yczoge1xuICAgIHJlcXVpcmVkOiBcIlJlcXVlcml0XCIsXG4gICAgd2Vic2l0ZTogXCJJbnRyb2R1XFx4RUZ1IHVuYSBVUkwgdlxceEUwbGlkYVwiXG4gIH0sXG4gIG5vdGVEZXNjcmlwdGlvbjogXCJFbnZpZXUgbGEgdm9zdHJhIGJpYmxpb3RlY2EgcGVycXVcXHhFOCBzaWd1aSBpbmNsb3NhIGFsIDxsaW5rPnJlcG9zaXRvcmkgcFxceEZBYmxpYzwvbGluaz5wZXIgdGFsIHF1ZSBhbHRyZXMgcGVyc29uZXMgcHVndWluIGZlci1uZSBcXHhGQXMgZW4gZWxzIHNldXMgZGlidWl4b3MuXCIsXG4gIG5vdGVHdWlkZWxpbmVzOiBcIkxhIGJpYmxpb3RlY2EgaGEgZGUgc2VyIGFwcm92YWRhIG1hbnVhbG1lbnQuIFNpIHVzIHBsYXUsIGxsZWdpdSBsZXMgPGxpbms+ZGlyZWN0cml1czwvbGluaz4gYWJhbnMgZCdlbnZpYXItaGkgcmVzLiBOZWNlc3NpdGFyZXUgdW4gY29tcHRlIGRlIEdpdEh1YiBwZXIgYSBjb211bmljYXIgaSBmZXItaGkgY2FudmlzIHNpIGNhbCwgcGVyXFx4RjIgbm8gXFx4RTlzIHJlcXVpc2l0IGltcHJlc2NpbmRpYmxlLlwiLFxuICBub3RlTGljZW5zZTogXCJRdWFuIGwnZW52aWV1LCBhY2NlcHRldSBxdWUgbGEgYmlibGlvdGVjYSBzaWd1aSBwdWJsaWNhZGEgc290YSBsYSA8bGluaz5sbGljXFx4RThuY2lhIE1JVCwgPC9saW5rPnF1ZSwgZW4gcmVzdW0sIHZvbCBkaXIgcXVlIHF1YWxzZXZvbCBwZXJzb25hIHBvdCBmZXItbmUgXFx4RkFzIHNlbnNlIHJlc3RyaWNjaW9ucy5cIixcbiAgbm90ZUl0ZW1zOiBcIkNhZGEgZWxlbWVudCBkZSBsYSBiaWJsaW90ZWNhIGhhIGRlIHRlbmlyIGVsIHNldSBwcm9waSBub20gcGVyIHRhbCBxdWUgc2lndWkgZmlsdHJhYmxlLiBTJ2hpIGluY2xvdXJhbiBlbHMgZWxlbWVudHMgc2VnXFx4RkNlbnRzOlwiLFxuICBhdGxlYXN0T25lTGliSXRlbTogXCJTaSB1cyBwbGF1LCBzZWxlY2Npb25ldSBzaSBtXFx4RTlzIG5vIHVuIGVsZW1lbnQgZGUgbGEgYmlibGlvdGVjYSBwZXIgYSBjb21lblxceEU3YXJcIixcbiAgcmVwdWJsaXNoV2FybmluZzogXCJOb3RhOiBhbGd1bnMgZGVscyBlbGVtZW50cyBzZWxlY2Npb25hdHMgcydoYW4gbWFyY2F0IGNvbSBhIHB1YmxpY2F0cy9lbnZpYXRzLiBOb21cXHhFOXMgaGF1clxceEVEZXUgZGUgcmVlbnZpYXIgZWxlbWVudHMgcXVhbiBhY3R1YWxpdHpldSB1bmEgYmlibGlvdGVjYSBleGlzdGVudC5cIlxufTtcbnZhciBwdWJsaXNoU3VjY2Vzc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiQmlibGlvdGVjYSBlbnZpYWRhXCIsXG4gIGNvbnRlbnQ6IFwiR3JcXHhFMGNpZXMsIHt7YXV0aG9yTmFtZX19LiBMYSB2b3N0cmEgYmlibGlvdGVjYSBoYSBlc3RhdCBlbnZpYWRhIHBlciBhIHNlciByZXZpc2FkYS4gUG9kZXUgY29tcHJvdmFyLW5lIGwnZXN0YXQ8bGluaz5hcXVcXHhFRDwvbGluaz5cIlxufTtcbnZhciBjb25maXJtRGlhbG9nID0ge1xuICByZXNldExpYnJhcnk6IFwiUmVzdGFibGVpeCBsYSBiaWJsaW90ZWNhXCIsXG4gIHJlbW92ZUl0ZW1zRnJvbUxpYjogXCJTdXByaW1laXggZWxzIGVsZW1lbnRzIHNlbGVjY2lvbmF0cyBkZSBsYSBsbGlicmVyaWFcIlxufTtcbnZhciBpbWFnZUV4cG9ydERpYWxvZyA9IHtcbiAgaGVhZGVyOiBcIlwiLFxuICBsYWJlbDoge1xuICAgIHdpdGhCYWNrZ3JvdW5kOiBcIlwiLFxuICAgIG9ubHlTZWxlY3RlZDogXCJOb21cXHhFOXMgZWxzIHNlbGVjY2lvbmF0c1wiLFxuICAgIGRhcmtNb2RlOiBcIk1vZGUgZm9zY1wiLFxuICAgIGVtYmVkU2NlbmU6IFwiXCIsXG4gICAgc2NhbGU6IFwiXCIsXG4gICAgcGFkZGluZzogXCJcIlxuICB9LFxuICB0b29sdGlwOiB7XG4gICAgZW1iZWRTY2VuZTogXCJcIlxuICB9LFxuICB0aXRsZToge1xuICAgIGV4cG9ydFRvUG5nOiBcIkV4cG9ydGEgYSBQTkdcIixcbiAgICBleHBvcnRUb1N2ZzogXCJFeHBvcnRhIGEgU1ZHXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIkNvcGlhIGVsIFBORyBhbCBwb3J0YS1yZXRhbGxzXCJcbiAgfSxcbiAgYnV0dG9uOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiUE5HXCIsXG4gICAgZXhwb3J0VG9Tdmc6IFwiXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIlwiXG4gIH1cbn07XG52YXIgZW5jcnlwdGVkID0ge1xuICB0b29sdGlwOiBcIkVscyB2b3N0cmVzIGRpYnVpeG9zIGVzdGFuIHhpZnJhdHMgZGUgcHVudGEgYSBwdW50YSBkZSBtYW5lcmEgcXVlIGVscyBzZXJ2aWRvcnMgZFxcdTIwMTlFeGNhbGlkcmF3IG5vIGVscyB2ZXVyYW4gbWFpLlwiLFxuICBsaW5rOiBcIkFydGljbGUgZGVsIGJsb2cgc29icmUgZW5jcmlwdGFjaVxceEYzIGQnZXh0cmVtIGEgZXh0cmVtIGEgRXhjYWxpZHJhd1wiXG59O1xudmFyIHN0YXRzID0ge1xuICBhbmdsZTogXCJBbmdsZVwiLFxuICBlbGVtZW50OiBcIkVsZW1lbnRcIixcbiAgZWxlbWVudHM6IFwiRWxlbWVudHNcIixcbiAgaGVpZ2h0OiBcIkFsdHVyYVwiLFxuICBzY2VuZTogXCJFc2NlbmFcIixcbiAgc2VsZWN0ZWQ6IFwiU2VsZWNjaW9uYXRcIixcbiAgc3RvcmFnZTogXCJFbW1hZ2F0emVtYXRnZVwiLFxuICB0aXRsZTogXCJFc3RhZFxceEVEc3RpcXVlcyBwZXIgbmVyZHNcIixcbiAgdG90YWw6IFwiVG90YWxcIixcbiAgdmVyc2lvbjogXCJWZXJzaVxceEYzXCIsXG4gIHZlcnNpb25Db3B5OiBcIkZldSBjbGljIHBlciBhIGNvcGlhclwiLFxuICB2ZXJzaW9uTm90QXZhaWxhYmxlOiBcIlZlcnNpXFx4RjMgbm8gZGlzcG9uaWJsZVwiLFxuICB3aWR0aDogXCJBbXBsYWRhXCJcbn07XG52YXIgdG9hc3QgPSB7XG4gIGFkZGVkVG9MaWJyYXJ5OiBcIkFmZWdpdCBhIGxhIGJpYmxpb3RlY2FcIixcbiAgY29weVN0eWxlczogXCJTJ2hhbiBjb3BpYXQgZWxzIGVzdGlscy5cIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIlMnaGEgY29waWF0IGFsIHBvcnRhLXJldGFsbHMuXCIsXG4gIGNvcHlUb0NsaXBib2FyZEFzUG5nOiBcIlMnaGEgY29waWF0IHt7ZXhwb3J0U2VsZWN0aW9ufX0gYWwgcG9ydGEtcmV0YWxscyBlbiBmb3JtYXQgUE5HXFxuKHt7ZXhwb3J0Q29sb3JTY2hlbWV9fSlcIixcbiAgZmlsZVNhdmVkOiBcIlMnaGEgZGVzYXQgZWwgZml0eGVyLlwiLFxuICBmaWxlU2F2ZWRUb0ZpbGVuYW1lOiBcIlMnaGEgZGVzYXQgYSB7ZmlsZW5hbWV9XCIsXG4gIGNhbnZhczogXCJlbCBsbGVuXFx4RTdcIixcbiAgc2VsZWN0aW9uOiBcImxhIHNlbGVjY2lcXHhGM1wiLFxuICBwYXN0ZUFzU2luZ2xlRWxlbWVudDogXCJGZXIgc2VydmlyIHt7c2hvcnRjdXR9fSBwZXIgZW5nYW54YXIgY29tIHVuIHNvbCBlbGVtZW50LFxcbm8gZW5nYW54ZXUtbG8gZW4gdW4gZWRpdG9yIGRlIHRleHQgZXhpc3RlbnRcIixcbiAgdW5hYmxlVG9FbWJlZDogXCJcIixcbiAgdW5yZWNvZ25pemVkTGlua0Zvcm1hdDogXCJcIlxufTtcbnZhciBjb2xvcnMgPSB7XG4gIHRyYW5zcGFyZW50OiBcIlRyYW5zcGFyZW50XCIsXG4gIGJsYWNrOiBcIlwiLFxuICB3aGl0ZTogXCJcIixcbiAgcmVkOiBcIlwiLFxuICBwaW5rOiBcIlwiLFxuICBncmFwZTogXCJcIixcbiAgdmlvbGV0OiBcIlwiLFxuICBncmF5OiBcIlwiLFxuICBibHVlOiBcIlwiLFxuICBjeWFuOiBcIlwiLFxuICB0ZWFsOiBcIlwiLFxuICBncmVlbjogXCJWZXJkXCIsXG4gIHllbGxvdzogXCJHcm9jXCIsXG4gIG9yYW5nZTogXCJUYXJvbmphXCIsXG4gIGJyb256ZTogXCJCcm9uemVcIlxufTtcbnZhciB3ZWxjb21lU2NyZWVuID0ge1xuICBhcHA6IHtcbiAgICBjZW50ZXJfaGVhZGluZzogXCJUb3RlcyBsZXMgdm9zdHJlcyBkYWRlcyBlcyBndWFyZGVuIGxvY2FsbWVudCBhbCB2b3N0cmUgbmF2ZWdhZG9yLlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nX3BsdXM6IFwiVm9scyBhbmFyIGEgRXhjYWxpZHJhdysgZW4gY29tcHRlcz9cIixcbiAgICBtZW51SGludDogXCJFeHBvcnRhciwgcHJlZmVyXFx4RThuY2llcywgbGxlbmd1YXRnZXMuLi5cIlxuICB9LFxuICBkZWZhdWx0czoge1xuICAgIG1lbnVIaW50OiBcIkV4cG9ydGFyLCBwcmVmZXJcXHhFOG5jaWVzIGkgbVxceEU5cy4uLlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nOiBcIkRpYWdyYW1lcy4gRmVyLiBTaW1wbGUuXCIsXG4gICAgdG9vbGJhckhpbnQ6IFwiU2VsZWNjaW9uYSB1bmEgZWluYSBpIGNvbWVuXFx4RTdhIGEgZGlidWl4YXIhXCIsXG4gICAgaGVscEhpbnQ6IFwiRHJlY2VyZXMgaSBhanVkYVwiXG4gIH1cbn07XG52YXIgY29sb3JQaWNrZXIgPSB7XG4gIG1vc3RVc2VkQ3VzdG9tQ29sb3JzOiBcIlwiLFxuICBjb2xvcnM6IFwiXCIsXG4gIHNoYWRlczogXCJcIixcbiAgaGV4Q29kZTogXCJcIixcbiAgbm9TaGFkZXM6IFwiXCJcbn07XG52YXIgb3ZlcndyaXRlQ29uZmlybSA9IHtcbiAgYWN0aW9uOiB7XG4gICAgZXhwb3J0VG9JbWFnZToge1xuICAgICAgdGl0bGU6IFwiRXhwb3J0YSBjb20gYSBpbWF0Z2VcIixcbiAgICAgIGJ1dHRvbjogXCJFeHBvcnRhIGNvbSBhIGltYXRnZVwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9LFxuICAgIHNhdmVUb0Rpc2s6IHtcbiAgICAgIHRpdGxlOiBcIkRlc2EgYWwgZGlzY1wiLFxuICAgICAgYnV0dG9uOiBcIkRlc2EgYWwgZGlzY1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9LFxuICAgIGV4Y2FsaWRyYXdQbHVzOiB7XG4gICAgICB0aXRsZTogXCJcIixcbiAgICAgIGJ1dHRvbjogXCJcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfVxuICB9LFxuICBtb2RhbDoge1xuICAgIGxvYWRGcm9tRmlsZToge1xuICAgICAgdGl0bGU6IFwiQ2FycmVnYSBkZXMgZCd1biBmaXR4ZXJcIixcbiAgICAgIGJ1dHRvbjogXCJDYXJyZWdhIGRlcyBkJ3VuIGZpdHhlclwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9LFxuICAgIHNoYXJlYWJsZUxpbms6IHtcbiAgICAgIHRpdGxlOiBcIkNhcnJlZ2EgZGVzIGQndW4gZW5sbGFcXHhFN1wiLFxuICAgICAgYnV0dG9uOiBcIlwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9XG4gIH1cbn07XG52YXIgbWVybWFpZCA9IHtcbiAgdGl0bGU6IFwiRGUgTWVybWFpZCBhIEV4Y2FsaWRyYXdcIixcbiAgYnV0dG9uOiBcIkluc2VyZWl4XCIsXG4gIGRlc2NyaXB0aW9uOiBcIlwiLFxuICBzeW50YXg6IFwiU2ludGF4aSBkZSBNZXJtYWlkXCIsXG4gIHByZXZpZXc6IFwiUHJldmlzdWFsaXR6YWNpXFx4RjNcIlxufTtcbnZhciBjYV9FU19kZWZhdWx0ID0ge1xuICBsYWJlbHMsXG4gIGxpYnJhcnksXG4gIGJ1dHRvbnMsXG4gIGFsZXJ0cyxcbiAgZXJyb3JzLFxuICB0b29sQmFyLFxuICBoZWFkaW5ncyxcbiAgaGludHMsXG4gIGNhbnZhc0Vycm9yLFxuICBlcnJvclNwbGFzaCxcbiAgcm9vbURpYWxvZyxcbiAgZXJyb3JEaWFsb2csXG4gIGV4cG9ydERpYWxvZyxcbiAgaGVscERpYWxvZyxcbiAgY2xlYXJDYW52YXNEaWFsb2csXG4gIHB1Ymxpc2hEaWFsb2csXG4gIHB1Ymxpc2hTdWNjZXNzRGlhbG9nLFxuICBjb25maXJtRGlhbG9nLFxuICBpbWFnZUV4cG9ydERpYWxvZyxcbiAgZW5jcnlwdGVkLFxuICBzdGF0cyxcbiAgdG9hc3QsXG4gIGNvbG9ycyxcbiAgd2VsY29tZVNjcmVlbixcbiAgY29sb3JQaWNrZXIsXG4gIG92ZXJ3cml0ZUNvbmZpcm0sXG4gIG1lcm1haWRcbn07XG5leHBvcnQge1xuICBhbGVydHMsXG4gIGJ1dHRvbnMsXG4gIGNhbnZhc0Vycm9yLFxuICBjbGVhckNhbnZhc0RpYWxvZyxcbiAgY29sb3JQaWNrZXIsXG4gIGNvbG9ycyxcbiAgY29uZmlybURpYWxvZyxcbiAgY2FfRVNfZGVmYXVsdCBhcyBkZWZhdWx0LFxuICBlbmNyeXB0ZWQsXG4gIGVycm9yRGlhbG9nLFxuICBlcnJvclNwbGFzaCxcbiAgZXJyb3JzLFxuICBleHBvcnREaWFsb2csXG4gIGhlYWRpbmdzLFxuICBoZWxwRGlhbG9nLFxuICBoaW50cyxcbiAgaW1hZ2VFeHBvcnREaWFsb2csXG4gIGxhYmVscyxcbiAgbGlicmFyeSxcbiAgbWVybWFpZCxcbiAgb3ZlcndyaXRlQ29uZmlybSxcbiAgcHVibGlzaERpYWxvZyxcbiAgcHVibGlzaFN1Y2Nlc3NEaWFsb2csXG4gIHJvb21EaWFsb2csXG4gIHN0YXRzLFxuICB0b2FzdCxcbiAgdG9vbEJhcixcbiAgd2VsY29tZVNjcmVlblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNhLUVTLVlOTk1GUlFYLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/ca-ES-YNNMFRQX.js\n"));

/***/ })

}]);