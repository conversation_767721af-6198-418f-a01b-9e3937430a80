"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_c4Diagram-3d4e48cf_js"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/c4Diagram-3d4e48cf.js":
/*!*********************************************************!*\
  !*** ./node_modules/mermaid/dist/c4Diagram-3d4e48cf.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var _svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./svgDrawCommon-08f97a94.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [1, 24], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 63], $V6 = [1, 64], $V7 = [1, 65], $V8 = [1, 66], $V9 = [1, 67], $Va = [1, 68], $Vb = [1, 69], $Vc = [1, 29], $Vd = [1, 30], $Ve = [1, 31], $Vf = [1, 32], $Vg = [1, 33], $Vh = [1, 34], $Vi = [1, 35], $Vj = [1, 36], $Vk = [1, 37], $Vl = [1, 38], $Vm = [1, 39], $Vn = [1, 40], $Vo = [1, 41], $Vp = [1, 42], $Vq = [1, 43], $Vr = [1, 44], $Vs = [1, 45], $Vt = [1, 46], $Vu = [1, 47], $Vv = [1, 48], $Vw = [1, 50], $Vx = [1, 51], $Vy = [1, 52], $Vz = [1, 53], $VA = [1, 54], $VB = [1, 55], $VC = [1, 56], $VD = [1, 57], $VE = [1, 58], $VF = [1, 59], $VG = [1, 60], $VH = [14, 42], $VI = [14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VJ = [12, 14, 34, 36, 37, 38, 39, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], $VK = [1, 82], $VL = [1, 83], $VM = [1, 84], $VN = [1, 85], $VO = [12, 14, 42], $VP = [12, 14, 33, 42], $VQ = [12, 14, 33, 42, 76, 77, 79, 80], $VR = [12, 33], $VS = [34, 36, 37, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"mermaidDoc\": 4, \"direction\": 5, \"direction_tb\": 6, \"direction_bt\": 7, \"direction_rl\": 8, \"direction_lr\": 9, \"graphConfig\": 10, \"C4_CONTEXT\": 11, \"NEWLINE\": 12, \"statements\": 13, \"EOF\": 14, \"C4_CONTAINER\": 15, \"C4_COMPONENT\": 16, \"C4_DYNAMIC\": 17, \"C4_DEPLOYMENT\": 18, \"otherStatements\": 19, \"diagramStatements\": 20, \"otherStatement\": 21, \"title\": 22, \"accDescription\": 23, \"acc_title\": 24, \"acc_title_value\": 25, \"acc_descr\": 26, \"acc_descr_value\": 27, \"acc_descr_multiline_value\": 28, \"boundaryStatement\": 29, \"boundaryStartStatement\": 30, \"boundaryStopStatement\": 31, \"boundaryStart\": 32, \"LBRACE\": 33, \"ENTERPRISE_BOUNDARY\": 34, \"attributes\": 35, \"SYSTEM_BOUNDARY\": 36, \"BOUNDARY\": 37, \"CONTAINER_BOUNDARY\": 38, \"NODE\": 39, \"NODE_L\": 40, \"NODE_R\": 41, \"RBRACE\": 42, \"diagramStatement\": 43, \"PERSON\": 44, \"PERSON_EXT\": 45, \"SYSTEM\": 46, \"SYSTEM_DB\": 47, \"SYSTEM_QUEUE\": 48, \"SYSTEM_EXT\": 49, \"SYSTEM_EXT_DB\": 50, \"SYSTEM_EXT_QUEUE\": 51, \"CONTAINER\": 52, \"CONTAINER_DB\": 53, \"CONTAINER_QUEUE\": 54, \"CONTAINER_EXT\": 55, \"CONTAINER_EXT_DB\": 56, \"CONTAINER_EXT_QUEUE\": 57, \"COMPONENT\": 58, \"COMPONENT_DB\": 59, \"COMPONENT_QUEUE\": 60, \"COMPONENT_EXT\": 61, \"COMPONENT_EXT_DB\": 62, \"COMPONENT_EXT_QUEUE\": 63, \"REL\": 64, \"BIREL\": 65, \"REL_U\": 66, \"REL_D\": 67, \"REL_L\": 68, \"REL_R\": 69, \"REL_B\": 70, \"REL_INDEX\": 71, \"UPDATE_EL_STYLE\": 72, \"UPDATE_REL_STYLE\": 73, \"UPDATE_LAYOUT_CONFIG\": 74, \"attribute\": 75, \"STR\": 76, \"STR_KEY\": 77, \"STR_VALUE\": 78, \"ATTRIBUTE\": 79, \"ATTRIBUTE_EMPTY\": 80, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 6: \"direction_tb\", 7: \"direction_bt\", 8: \"direction_rl\", 9: \"direction_lr\", 11: \"C4_CONTEXT\", 12: \"NEWLINE\", 14: \"EOF\", 15: \"C4_CONTAINER\", 16: \"C4_COMPONENT\", 17: \"C4_DYNAMIC\", 18: \"C4_DEPLOYMENT\", 22: \"title\", 23: \"accDescription\", 24: \"acc_title\", 25: \"acc_title_value\", 26: \"acc_descr\", 27: \"acc_descr_value\", 28: \"acc_descr_multiline_value\", 33: \"LBRACE\", 34: \"ENTERPRISE_BOUNDARY\", 36: \"SYSTEM_BOUNDARY\", 37: \"BOUNDARY\", 38: \"CONTAINER_BOUNDARY\", 39: \"NODE\", 40: \"NODE_L\", 41: \"NODE_R\", 42: \"RBRACE\", 44: \"PERSON\", 45: \"PERSON_EXT\", 46: \"SYSTEM\", 47: \"SYSTEM_DB\", 48: \"SYSTEM_QUEUE\", 49: \"SYSTEM_EXT\", 50: \"SYSTEM_EXT_DB\", 51: \"SYSTEM_EXT_QUEUE\", 52: \"CONTAINER\", 53: \"CONTAINER_DB\", 54: \"CONTAINER_QUEUE\", 55: \"CONTAINER_EXT\", 56: \"CONTAINER_EXT_DB\", 57: \"CONTAINER_EXT_QUEUE\", 58: \"COMPONENT\", 59: \"COMPONENT_DB\", 60: \"COMPONENT_QUEUE\", 61: \"COMPONENT_EXT\", 62: \"COMPONENT_EXT_DB\", 63: \"COMPONENT_EXT_QUEUE\", 64: \"REL\", 65: \"BIREL\", 66: \"REL_U\", 67: \"REL_D\", 68: \"REL_L\", 69: \"REL_R\", 70: \"REL_B\", 71: \"REL_INDEX\", 72: \"UPDATE_EL_STYLE\", 73: \"UPDATE_REL_STYLE\", 74: \"UPDATE_LAYOUT_CONFIG\", 76: \"STR\", 77: \"STR_KEY\", 78: \"STR_VALUE\", 79: \"ATTRIBUTE\", 80: \"ATTRIBUTE_EMPTY\" },\n    productions_: [0, [3, 1], [3, 1], [5, 1], [5, 1], [5, 1], [5, 1], [4, 1], [10, 4], [10, 4], [10, 4], [10, 4], [10, 4], [13, 1], [13, 1], [13, 2], [19, 1], [19, 2], [19, 3], [21, 1], [21, 1], [21, 2], [21, 2], [21, 1], [29, 3], [30, 3], [30, 3], [30, 4], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [32, 2], [31, 1], [20, 1], [20, 2], [20, 3], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 1], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [43, 2], [35, 1], [35, 2], [75, 1], [75, 2], [75, 1], [75, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setDirection(\"TB\");\n          break;\n        case 4:\n          yy.setDirection(\"BT\");\n          break;\n        case 5:\n          yy.setDirection(\"RL\");\n          break;\n        case 6:\n          yy.setDirection(\"LR\");\n          break;\n        case 8:\n        case 9:\n        case 10:\n        case 11:\n        case 12:\n          yy.setC4Type($$[$0 - 3]);\n          break;\n        case 19:\n          yy.setTitle($$[$0].substring(6));\n          this.$ = $$[$0].substring(6);\n          break;\n        case 20:\n          yy.setAccDescription($$[$0].substring(15));\n          this.$ = $$[$0].substring(15);\n          break;\n        case 21:\n          this.$ = $$[$0].trim();\n          yy.setTitle(this.$);\n          break;\n        case 22:\n        case 23:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n        case 29:\n          $$[$0].splice(2, 0, \"ENTERPRISE\");\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 30:\n          yy.addPersonOrSystemBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 31:\n          $$[$0].splice(2, 0, \"CONTAINER\");\n          yy.addContainerBoundary(...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 32:\n          yy.addDeploymentNode(\"node\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 33:\n          yy.addDeploymentNode(\"nodeL\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 34:\n          yy.addDeploymentNode(\"nodeR\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 35:\n          yy.popBoundaryParseStack();\n          break;\n        case 39:\n          yy.addPersonOrSystem(\"person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 40:\n          yy.addPersonOrSystem(\"external_person\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 41:\n          yy.addPersonOrSystem(\"system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 42:\n          yy.addPersonOrSystem(\"system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 43:\n          yy.addPersonOrSystem(\"system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 44:\n          yy.addPersonOrSystem(\"external_system\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 45:\n          yy.addPersonOrSystem(\"external_system_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 46:\n          yy.addPersonOrSystem(\"external_system_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 47:\n          yy.addContainer(\"container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 48:\n          yy.addContainer(\"container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 49:\n          yy.addContainer(\"container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 50:\n          yy.addContainer(\"external_container\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 51:\n          yy.addContainer(\"external_container_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 52:\n          yy.addContainer(\"external_container_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 53:\n          yy.addComponent(\"component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 54:\n          yy.addComponent(\"component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 55:\n          yy.addComponent(\"component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 56:\n          yy.addComponent(\"external_component\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 57:\n          yy.addComponent(\"external_component_db\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 58:\n          yy.addComponent(\"external_component_queue\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 60:\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 61:\n          yy.addRel(\"birel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 62:\n          yy.addRel(\"rel_u\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 63:\n          yy.addRel(\"rel_d\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 64:\n          yy.addRel(\"rel_l\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 65:\n          yy.addRel(\"rel_r\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 66:\n          yy.addRel(\"rel_b\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 67:\n          $$[$0].splice(0, 1);\n          yy.addRel(\"rel\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 68:\n          yy.updateElStyle(\"update_el_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 69:\n          yy.updateRelStyle(\"update_rel_style\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 70:\n          yy.updateLayoutConfig(\"update_layout_config\", ...$$[$0]);\n          this.$ = $$[$0];\n          break;\n        case 71:\n          this.$ = [$$[$0]];\n          break;\n        case 72:\n          $$[$0].unshift($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 73:\n        case 75:\n          this.$ = $$[$0].trim();\n          break;\n        case 74:\n          let kv = {};\n          kv[$$[$0 - 1].trim()] = $$[$0].trim();\n          this.$ = kv;\n          break;\n        case 76:\n          this.$ = \"\";\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: 2, 5: 3, 6: [1, 5], 7: [1, 6], 8: [1, 7], 9: [1, 8], 10: 4, 11: [1, 9], 15: [1, 10], 16: [1, 11], 17: [1, 12], 18: [1, 13] }, { 1: [3] }, { 1: [2, 1] }, { 1: [2, 2] }, { 1: [2, 7] }, { 1: [2, 3] }, { 1: [2, 4] }, { 1: [2, 5] }, { 1: [2, 6] }, { 12: [1, 14] }, { 12: [1, 15] }, { 12: [1, 16] }, { 12: [1, 17] }, { 12: [1, 18] }, { 13: 19, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 70, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 71, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 72, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 13: 73, 19: 20, 20: 21, 21: 22, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 14: [1, 74] }, o($VH, [2, 13], { 43: 23, 29: 49, 30: 61, 32: 62, 20: 75, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VH, [2, 14]), o($VI, [2, 16], { 12: [1, 76] }), o($VH, [2, 36], { 12: [1, 77] }), o($VJ, [2, 19]), o($VJ, [2, 20]), { 25: [1, 78] }, { 27: [1, 79] }, o($VJ, [2, 23]), { 35: 80, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 86, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 87, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 88, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 89, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 90, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 91, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 92, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 93, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 94, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 95, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 96, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 97, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 98, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 99, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 100, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 101, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 102, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 103, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 104, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, o($VO, [2, 59]), { 35: 105, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 106, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 107, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 108, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 109, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 110, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 111, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 112, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 113, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 114, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 115, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 20: 116, 29: 49, 30: 61, 32: 62, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 43: 23, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }, { 12: [1, 118], 33: [1, 117] }, { 35: 119, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 120, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 121, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 122, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 123, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 124, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 35: 125, 75: 81, 76: $VK, 77: $VL, 79: $VM, 80: $VN }, { 14: [1, 126] }, { 14: [1, 127] }, { 14: [1, 128] }, { 14: [1, 129] }, { 1: [2, 8] }, o($VH, [2, 15]), o($VI, [2, 17], { 21: 22, 19: 130, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4 }), o($VH, [2, 37], { 19: 20, 20: 21, 21: 22, 43: 23, 29: 49, 30: 61, 32: 62, 13: 131, 22: $V0, 23: $V1, 24: $V2, 26: $V3, 28: $V4, 34: $V5, 36: $V6, 37: $V7, 38: $V8, 39: $V9, 40: $Va, 41: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi, 51: $Vj, 52: $Vk, 53: $Vl, 54: $Vm, 55: $Vn, 56: $Vo, 57: $Vp, 58: $Vq, 59: $Vr, 60: $Vs, 61: $Vt, 62: $Vu, 63: $Vv, 64: $Vw, 65: $Vx, 66: $Vy, 67: $Vz, 68: $VA, 69: $VB, 70: $VC, 71: $VD, 72: $VE, 73: $VF, 74: $VG }), o($VJ, [2, 21]), o($VJ, [2, 22]), o($VO, [2, 39]), o($VP, [2, 71], { 75: 81, 35: 132, 76: $VK, 77: $VL, 79: $VM, 80: $VN }), o($VQ, [2, 73]), { 78: [1, 133] }, o($VQ, [2, 75]), o($VQ, [2, 76]), o($VO, [2, 40]), o($VO, [2, 41]), o($VO, [2, 42]), o($VO, [2, 43]), o($VO, [2, 44]), o($VO, [2, 45]), o($VO, [2, 46]), o($VO, [2, 47]), o($VO, [2, 48]), o($VO, [2, 49]), o($VO, [2, 50]), o($VO, [2, 51]), o($VO, [2, 52]), o($VO, [2, 53]), o($VO, [2, 54]), o($VO, [2, 55]), o($VO, [2, 56]), o($VO, [2, 57]), o($VO, [2, 58]), o($VO, [2, 60]), o($VO, [2, 61]), o($VO, [2, 62]), o($VO, [2, 63]), o($VO, [2, 64]), o($VO, [2, 65]), o($VO, [2, 66]), o($VO, [2, 67]), o($VO, [2, 68]), o($VO, [2, 69]), o($VO, [2, 70]), { 31: 134, 42: [1, 135] }, { 12: [1, 136] }, { 33: [1, 137] }, o($VR, [2, 28]), o($VR, [2, 29]), o($VR, [2, 30]), o($VR, [2, 31]), o($VR, [2, 32]), o($VR, [2, 33]), o($VR, [2, 34]), { 1: [2, 9] }, { 1: [2, 10] }, { 1: [2, 11] }, { 1: [2, 12] }, o($VI, [2, 18]), o($VH, [2, 38]), o($VP, [2, 72]), o($VQ, [2, 74]), o($VO, [2, 24]), o($VO, [2, 35]), o($VS, [2, 25]), o($VS, [2, 26], { 12: [1, 138] }), o($VS, [2, 27])],\n    defaultActions: { 2: [2, 1], 3: [2, 2], 4: [2, 7], 5: [2, 3], 6: [2, 4], 7: [2, 5], 8: [2, 6], 74: [2, 8], 126: [2, 9], 127: [2, 10], 128: [2, 11], 129: [2, 12] },\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c2 = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c2 + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: {},\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            return 6;\n          case 1:\n            return 7;\n          case 2:\n            return 8;\n          case 3:\n            return 9;\n          case 4:\n            return 22;\n          case 5:\n            return 23;\n          case 6:\n            this.begin(\"acc_title\");\n            return 24;\n          case 7:\n            this.popState();\n            return \"acc_title_value\";\n          case 8:\n            this.begin(\"acc_descr\");\n            return 26;\n          case 9:\n            this.popState();\n            return \"acc_descr_value\";\n          case 10:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 11:\n            this.popState();\n            break;\n          case 12:\n            return \"acc_descr_multiline_value\";\n          case 13:\n            break;\n          case 14:\n            c;\n            break;\n          case 15:\n            return 12;\n          case 16:\n            break;\n          case 17:\n            return 11;\n          case 18:\n            return 15;\n          case 19:\n            return 16;\n          case 20:\n            return 17;\n          case 21:\n            return 18;\n          case 22:\n            this.begin(\"person_ext\");\n            return 45;\n          case 23:\n            this.begin(\"person\");\n            return 44;\n          case 24:\n            this.begin(\"system_ext_queue\");\n            return 51;\n          case 25:\n            this.begin(\"system_ext_db\");\n            return 50;\n          case 26:\n            this.begin(\"system_ext\");\n            return 49;\n          case 27:\n            this.begin(\"system_queue\");\n            return 48;\n          case 28:\n            this.begin(\"system_db\");\n            return 47;\n          case 29:\n            this.begin(\"system\");\n            return 46;\n          case 30:\n            this.begin(\"boundary\");\n            return 37;\n          case 31:\n            this.begin(\"enterprise_boundary\");\n            return 34;\n          case 32:\n            this.begin(\"system_boundary\");\n            return 36;\n          case 33:\n            this.begin(\"container_ext_queue\");\n            return 57;\n          case 34:\n            this.begin(\"container_ext_db\");\n            return 56;\n          case 35:\n            this.begin(\"container_ext\");\n            return 55;\n          case 36:\n            this.begin(\"container_queue\");\n            return 54;\n          case 37:\n            this.begin(\"container_db\");\n            return 53;\n          case 38:\n            this.begin(\"container\");\n            return 52;\n          case 39:\n            this.begin(\"container_boundary\");\n            return 38;\n          case 40:\n            this.begin(\"component_ext_queue\");\n            return 63;\n          case 41:\n            this.begin(\"component_ext_db\");\n            return 62;\n          case 42:\n            this.begin(\"component_ext\");\n            return 61;\n          case 43:\n            this.begin(\"component_queue\");\n            return 60;\n          case 44:\n            this.begin(\"component_db\");\n            return 59;\n          case 45:\n            this.begin(\"component\");\n            return 58;\n          case 46:\n            this.begin(\"node\");\n            return 39;\n          case 47:\n            this.begin(\"node\");\n            return 39;\n          case 48:\n            this.begin(\"node_l\");\n            return 40;\n          case 49:\n            this.begin(\"node_r\");\n            return 41;\n          case 50:\n            this.begin(\"rel\");\n            return 64;\n          case 51:\n            this.begin(\"birel\");\n            return 65;\n          case 52:\n            this.begin(\"rel_u\");\n            return 66;\n          case 53:\n            this.begin(\"rel_u\");\n            return 66;\n          case 54:\n            this.begin(\"rel_d\");\n            return 67;\n          case 55:\n            this.begin(\"rel_d\");\n            return 67;\n          case 56:\n            this.begin(\"rel_l\");\n            return 68;\n          case 57:\n            this.begin(\"rel_l\");\n            return 68;\n          case 58:\n            this.begin(\"rel_r\");\n            return 69;\n          case 59:\n            this.begin(\"rel_r\");\n            return 69;\n          case 60:\n            this.begin(\"rel_b\");\n            return 70;\n          case 61:\n            this.begin(\"rel_index\");\n            return 71;\n          case 62:\n            this.begin(\"update_el_style\");\n            return 72;\n          case 63:\n            this.begin(\"update_rel_style\");\n            return 73;\n          case 64:\n            this.begin(\"update_layout_config\");\n            return 74;\n          case 65:\n            return \"EOF_IN_STRUCT\";\n          case 66:\n            this.begin(\"attribute\");\n            return \"ATTRIBUTE_EMPTY\";\n          case 67:\n            this.begin(\"attribute\");\n            break;\n          case 68:\n            this.popState();\n            this.popState();\n            break;\n          case 69:\n            return 80;\n          case 70:\n            break;\n          case 71:\n            return 80;\n          case 72:\n            this.begin(\"string\");\n            break;\n          case 73:\n            this.popState();\n            break;\n          case 74:\n            return \"STR\";\n          case 75:\n            this.begin(\"string_kv\");\n            break;\n          case 76:\n            this.begin(\"string_kv_key\");\n            return \"STR_KEY\";\n          case 77:\n            this.popState();\n            this.begin(\"string_kv_value\");\n            break;\n          case 78:\n            return \"STR_VALUE\";\n          case 79:\n            this.popState();\n            this.popState();\n            break;\n          case 80:\n            return \"STR\";\n          case 81:\n            return \"LBRACE\";\n          case 82:\n            return \"RBRACE\";\n          case 83:\n            return \"SPACE\";\n          case 84:\n            return \"EOL\";\n          case 85:\n            return 14;\n        }\n      },\n      rules: [/^(?:.*direction\\s+TB[^\\n]*)/, /^(?:.*direction\\s+BT[^\\n]*)/, /^(?:.*direction\\s+RL[^\\n]*)/, /^(?:.*direction\\s+LR[^\\n]*)/, /^(?:title\\s[^#\\n;]+)/, /^(?:accDescription\\s[^#\\n;]+)/, /^(?:accTitle\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*:\\s*)/, /^(?:(?!\\n||)*[^\\n]*)/, /^(?:accDescr\\s*\\{\\s*)/, /^(?:[\\}])/, /^(?:[^\\}]*)/, /^(?:%%(?!\\{)*[^\\n]*(\\r?\\n?)+)/, /^(?:%%[^\\n]*(\\r?\\n)*)/, /^(?:\\s*(\\r?\\n)+)/, /^(?:\\s+)/, /^(?:C4Context\\b)/, /^(?:C4Container\\b)/, /^(?:C4Component\\b)/, /^(?:C4Dynamic\\b)/, /^(?:C4Deployment\\b)/, /^(?:Person_Ext\\b)/, /^(?:Person\\b)/, /^(?:SystemQueue_Ext\\b)/, /^(?:SystemDb_Ext\\b)/, /^(?:System_Ext\\b)/, /^(?:SystemQueue\\b)/, /^(?:SystemDb\\b)/, /^(?:System\\b)/, /^(?:Boundary\\b)/, /^(?:Enterprise_Boundary\\b)/, /^(?:System_Boundary\\b)/, /^(?:ContainerQueue_Ext\\b)/, /^(?:ContainerDb_Ext\\b)/, /^(?:Container_Ext\\b)/, /^(?:ContainerQueue\\b)/, /^(?:ContainerDb\\b)/, /^(?:Container\\b)/, /^(?:Container_Boundary\\b)/, /^(?:ComponentQueue_Ext\\b)/, /^(?:ComponentDb_Ext\\b)/, /^(?:Component_Ext\\b)/, /^(?:ComponentQueue\\b)/, /^(?:ComponentDb\\b)/, /^(?:Component\\b)/, /^(?:Deployment_Node\\b)/, /^(?:Node\\b)/, /^(?:Node_L\\b)/, /^(?:Node_R\\b)/, /^(?:Rel\\b)/, /^(?:BiRel\\b)/, /^(?:Rel_Up\\b)/, /^(?:Rel_U\\b)/, /^(?:Rel_Down\\b)/, /^(?:Rel_D\\b)/, /^(?:Rel_Left\\b)/, /^(?:Rel_L\\b)/, /^(?:Rel_Right\\b)/, /^(?:Rel_R\\b)/, /^(?:Rel_Back\\b)/, /^(?:RelIndex\\b)/, /^(?:UpdateElementStyle\\b)/, /^(?:UpdateRelStyle\\b)/, /^(?:UpdateLayoutConfig\\b)/, /^(?:$)/, /^(?:[(][ ]*[,])/, /^(?:[(])/, /^(?:[)])/, /^(?:,,)/, /^(?:,)/, /^(?:[ ]*[\"][\"])/, /^(?:[ ]*[\"])/, /^(?:[\"])/, /^(?:[^\"]*)/, /^(?:[ ]*[\\$])/, /^(?:[^=]*)/, /^(?:[=][ ]*[\"])/, /^(?:[^\"]+)/, /^(?:[\"])/, /^(?:[^,]+)/, /^(?:\\{)/, /^(?:\\})/, /^(?:[\\s]+)/, /^(?:[\\n\\r]+)/, /^(?:$)/],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [11, 12], \"inclusive\": false }, \"acc_descr\": { \"rules\": [9], \"inclusive\": false }, \"acc_title\": { \"rules\": [7], \"inclusive\": false }, \"string_kv_value\": { \"rules\": [78, 79], \"inclusive\": false }, \"string_kv_key\": { \"rules\": [77], \"inclusive\": false }, \"string_kv\": { \"rules\": [76], \"inclusive\": false }, \"string\": { \"rules\": [73, 74], \"inclusive\": false }, \"attribute\": { \"rules\": [68, 69, 70, 71, 72, 75, 80], \"inclusive\": false }, \"update_layout_config\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_rel_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"update_el_style\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_b\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_d\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_u\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"rel_bi\": { \"rules\": [], \"inclusive\": false }, \"rel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_r\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node_l\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"node\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"index\": { \"rules\": [], \"inclusive\": false }, \"rel_index\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext_queue\": { \"rules\": [], \"inclusive\": false }, \"component_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"component\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"container\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"birel\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"enterprise_boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"boundary\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_queue\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system_db\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"system\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person_ext\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"person\": { \"rules\": [65, 66, 67, 68], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 8, 10, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 81, 82, 83, 84, 85], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nlet c4ShapeArray = [];\nlet boundaryParseStack = [\"\"];\nlet currentBoundaryParse = \"global\";\nlet parentBoundaryParse = \"\";\nlet boundaries = [\n  {\n    alias: \"global\",\n    label: { text: \"global\" },\n    type: { text: \"global\" },\n    tags: null,\n    link: null,\n    parentBoundary: \"\"\n  }\n];\nlet rels = [];\nlet title = \"\";\nlet wrapEnabled = false;\nlet c4ShapeInRow$1 = 4;\nlet c4BoundaryInRow$1 = 2;\nvar c4Type;\nconst getC4Type = function() {\n  return c4Type;\n};\nconst setC4Type = function(c4TypeParam) {\n  let sanitizedText = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.d)(c4TypeParam, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  c4Type = sanitizedText;\n};\nconst addRel = function(type, from, to, label, techn, descr, sprite, tags, link) {\n  if (type === void 0 || type === null || from === void 0 || from === null || to === void 0 || to === null || label === void 0 || label === null) {\n    return;\n  }\n  let rel = {};\n  const old = rels.find((rel2) => rel2.from === from && rel2.to === to);\n  if (old) {\n    rel = old;\n  } else {\n    rels.push(rel);\n  }\n  rel.type = type;\n  rel.from = from;\n  rel.to = to;\n  rel.label = { text: label };\n  if (techn === void 0 || techn === null) {\n    rel.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    rel.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      rel[key] = { text: value };\n    } else {\n      rel.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    rel[key] = value;\n  } else {\n    rel.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    rel[key] = value;\n  } else {\n    rel.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    rel[key] = value;\n  } else {\n    rel.link = link;\n  }\n  rel.wrap = autoWrap();\n};\nconst addPersonOrSystem = function(typeC4Shape, alias, label, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let personOrSystem = {};\n  const old = c4ShapeArray.find((personOrSystem2) => personOrSystem2.alias === alias);\n  if (old && alias === old.alias) {\n    personOrSystem = old;\n  } else {\n    personOrSystem.alias = alias;\n    c4ShapeArray.push(personOrSystem);\n  }\n  if (label === void 0 || label === null) {\n    personOrSystem.label = { text: \"\" };\n  } else {\n    personOrSystem.label = { text: label };\n  }\n  if (descr === void 0 || descr === null) {\n    personOrSystem.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      personOrSystem[key] = { text: value };\n    } else {\n      personOrSystem.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    personOrSystem[key] = value;\n  } else {\n    personOrSystem.link = link;\n  }\n  personOrSystem.typeC4Shape = { text: typeC4Shape };\n  personOrSystem.parentBoundary = currentBoundaryParse;\n  personOrSystem.wrap = autoWrap();\n};\nconst addContainer = function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let container = {};\n  const old = c4ShapeArray.find((container2) => container2.alias === alias);\n  if (old && alias === old.alias) {\n    container = old;\n  } else {\n    container.alias = alias;\n    c4ShapeArray.push(container);\n  }\n  if (label === void 0 || label === null) {\n    container.label = { text: \"\" };\n  } else {\n    container.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    container.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      container[key] = { text: value };\n    } else {\n      container.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    container.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      container[key] = { text: value };\n    } else {\n      container.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    container[key] = value;\n  } else {\n    container.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    container[key] = value;\n  } else {\n    container.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    container[key] = value;\n  } else {\n    container.link = link;\n  }\n  container.wrap = autoWrap();\n  container.typeC4Shape = { text: typeC4Shape };\n  container.parentBoundary = currentBoundaryParse;\n};\nconst addComponent = function(typeC4Shape, alias, label, techn, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let component = {};\n  const old = c4ShapeArray.find((component2) => component2.alias === alias);\n  if (old && alias === old.alias) {\n    component = old;\n  } else {\n    component.alias = alias;\n    c4ShapeArray.push(component);\n  }\n  if (label === void 0 || label === null) {\n    component.label = { text: \"\" };\n  } else {\n    component.label = { text: label };\n  }\n  if (techn === void 0 || techn === null) {\n    component.techn = { text: \"\" };\n  } else {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      component[key] = { text: value };\n    } else {\n      component.techn = { text: techn };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    component.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      component[key] = { text: value };\n    } else {\n      component.descr = { text: descr };\n    }\n  }\n  if (typeof sprite === \"object\") {\n    let [key, value] = Object.entries(sprite)[0];\n    component[key] = value;\n  } else {\n    component.sprite = sprite;\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    component[key] = value;\n  } else {\n    component.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    component[key] = value;\n  } else {\n    component.link = link;\n  }\n  component.wrap = autoWrap();\n  component.typeC4Shape = { text: typeC4Shape };\n  component.parentBoundary = currentBoundaryParse;\n};\nconst addPersonOrSystemBoundary = function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"system\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst addContainerBoundary = function(alias, label, type, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"container\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst addDeploymentNode = function(nodeType, alias, label, type, descr, sprite, tags, link) {\n  if (alias === null || label === null) {\n    return;\n  }\n  let boundary = {};\n  const old = boundaries.find((boundary2) => boundary2.alias === alias);\n  if (old && alias === old.alias) {\n    boundary = old;\n  } else {\n    boundary.alias = alias;\n    boundaries.push(boundary);\n  }\n  if (label === void 0 || label === null) {\n    boundary.label = { text: \"\" };\n  } else {\n    boundary.label = { text: label };\n  }\n  if (type === void 0 || type === null) {\n    boundary.type = { text: \"node\" };\n  } else {\n    if (typeof type === \"object\") {\n      let [key, value] = Object.entries(type)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.type = { text: type };\n    }\n  }\n  if (descr === void 0 || descr === null) {\n    boundary.descr = { text: \"\" };\n  } else {\n    if (typeof descr === \"object\") {\n      let [key, value] = Object.entries(descr)[0];\n      boundary[key] = { text: value };\n    } else {\n      boundary.descr = { text: descr };\n    }\n  }\n  if (typeof tags === \"object\") {\n    let [key, value] = Object.entries(tags)[0];\n    boundary[key] = value;\n  } else {\n    boundary.tags = tags;\n  }\n  if (typeof link === \"object\") {\n    let [key, value] = Object.entries(link)[0];\n    boundary[key] = value;\n  } else {\n    boundary.link = link;\n  }\n  boundary.nodeType = nodeType;\n  boundary.parentBoundary = currentBoundaryParse;\n  boundary.wrap = autoWrap();\n  parentBoundaryParse = currentBoundaryParse;\n  currentBoundaryParse = alias;\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst popBoundaryParseStack = function() {\n  currentBoundaryParse = parentBoundaryParse;\n  boundaryParseStack.pop();\n  parentBoundaryParse = boundaryParseStack.pop();\n  boundaryParseStack.push(parentBoundaryParse);\n};\nconst updateElStyle = function(typeC4Shape, elementName, bgColor, fontColor, borderColor, shadowing, shape, sprite, techn, legendText, legendSprite) {\n  let old = c4ShapeArray.find((element) => element.alias === elementName);\n  if (old === void 0) {\n    old = boundaries.find((element) => element.alias === elementName);\n    if (old === void 0) {\n      return;\n    }\n  }\n  if (bgColor !== void 0 && bgColor !== null) {\n    if (typeof bgColor === \"object\") {\n      let [key, value] = Object.entries(bgColor)[0];\n      old[key] = value;\n    } else {\n      old.bgColor = bgColor;\n    }\n  }\n  if (fontColor !== void 0 && fontColor !== null) {\n    if (typeof fontColor === \"object\") {\n      let [key, value] = Object.entries(fontColor)[0];\n      old[key] = value;\n    } else {\n      old.fontColor = fontColor;\n    }\n  }\n  if (borderColor !== void 0 && borderColor !== null) {\n    if (typeof borderColor === \"object\") {\n      let [key, value] = Object.entries(borderColor)[0];\n      old[key] = value;\n    } else {\n      old.borderColor = borderColor;\n    }\n  }\n  if (shadowing !== void 0 && shadowing !== null) {\n    if (typeof shadowing === \"object\") {\n      let [key, value] = Object.entries(shadowing)[0];\n      old[key] = value;\n    } else {\n      old.shadowing = shadowing;\n    }\n  }\n  if (shape !== void 0 && shape !== null) {\n    if (typeof shape === \"object\") {\n      let [key, value] = Object.entries(shape)[0];\n      old[key] = value;\n    } else {\n      old.shape = shape;\n    }\n  }\n  if (sprite !== void 0 && sprite !== null) {\n    if (typeof sprite === \"object\") {\n      let [key, value] = Object.entries(sprite)[0];\n      old[key] = value;\n    } else {\n      old.sprite = sprite;\n    }\n  }\n  if (techn !== void 0 && techn !== null) {\n    if (typeof techn === \"object\") {\n      let [key, value] = Object.entries(techn)[0];\n      old[key] = value;\n    } else {\n      old.techn = techn;\n    }\n  }\n  if (legendText !== void 0 && legendText !== null) {\n    if (typeof legendText === \"object\") {\n      let [key, value] = Object.entries(legendText)[0];\n      old[key] = value;\n    } else {\n      old.legendText = legendText;\n    }\n  }\n  if (legendSprite !== void 0 && legendSprite !== null) {\n    if (typeof legendSprite === \"object\") {\n      let [key, value] = Object.entries(legendSprite)[0];\n      old[key] = value;\n    } else {\n      old.legendSprite = legendSprite;\n    }\n  }\n};\nconst updateRelStyle = function(typeC4Shape, from, to, textColor, lineColor, offsetX, offsetY) {\n  const old = rels.find((rel) => rel.from === from && rel.to === to);\n  if (old === void 0) {\n    return;\n  }\n  if (textColor !== void 0 && textColor !== null) {\n    if (typeof textColor === \"object\") {\n      let [key, value] = Object.entries(textColor)[0];\n      old[key] = value;\n    } else {\n      old.textColor = textColor;\n    }\n  }\n  if (lineColor !== void 0 && lineColor !== null) {\n    if (typeof lineColor === \"object\") {\n      let [key, value] = Object.entries(lineColor)[0];\n      old[key] = value;\n    } else {\n      old.lineColor = lineColor;\n    }\n  }\n  if (offsetX !== void 0 && offsetX !== null) {\n    if (typeof offsetX === \"object\") {\n      let [key, value] = Object.entries(offsetX)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetX = parseInt(offsetX);\n    }\n  }\n  if (offsetY !== void 0 && offsetY !== null) {\n    if (typeof offsetY === \"object\") {\n      let [key, value] = Object.entries(offsetY)[0];\n      old[key] = parseInt(value);\n    } else {\n      old.offsetY = parseInt(offsetY);\n    }\n  }\n};\nconst updateLayoutConfig = function(typeC4Shape, c4ShapeInRowParam, c4BoundaryInRowParam) {\n  let c4ShapeInRowValue = c4ShapeInRow$1;\n  let c4BoundaryInRowValue = c4BoundaryInRow$1;\n  if (typeof c4ShapeInRowParam === \"object\") {\n    const value = Object.values(c4ShapeInRowParam)[0];\n    c4ShapeInRowValue = parseInt(value);\n  } else {\n    c4ShapeInRowValue = parseInt(c4ShapeInRowParam);\n  }\n  if (typeof c4BoundaryInRowParam === \"object\") {\n    const value = Object.values(c4BoundaryInRowParam)[0];\n    c4BoundaryInRowValue = parseInt(value);\n  } else {\n    c4BoundaryInRowValue = parseInt(c4BoundaryInRowParam);\n  }\n  if (c4ShapeInRowValue >= 1) {\n    c4ShapeInRow$1 = c4ShapeInRowValue;\n  }\n  if (c4BoundaryInRowValue >= 1) {\n    c4BoundaryInRow$1 = c4BoundaryInRowValue;\n  }\n};\nconst getC4ShapeInRow = function() {\n  return c4ShapeInRow$1;\n};\nconst getC4BoundaryInRow = function() {\n  return c4BoundaryInRow$1;\n};\nconst getCurrentBoundaryParse = function() {\n  return currentBoundaryParse;\n};\nconst getParentBoundaryParse = function() {\n  return parentBoundaryParse;\n};\nconst getC4ShapeArray = function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return c4ShapeArray;\n  } else {\n    return c4ShapeArray.filter((personOrSystem) => {\n      return personOrSystem.parentBoundary === parentBoundary;\n    });\n  }\n};\nconst getC4Shape = function(alias) {\n  return c4ShapeArray.find((personOrSystem) => personOrSystem.alias === alias);\n};\nconst getC4ShapeKeys = function(parentBoundary) {\n  return Object.keys(getC4ShapeArray(parentBoundary));\n};\nconst getBoundaries = function(parentBoundary) {\n  if (parentBoundary === void 0 || parentBoundary === null) {\n    return boundaries;\n  } else {\n    return boundaries.filter((boundary) => boundary.parentBoundary === parentBoundary);\n  }\n};\nconst getBoundarys = getBoundaries;\nconst getRels = function() {\n  return rels;\n};\nconst getTitle = function() {\n  return title;\n};\nconst setWrap = function(wrapSetting) {\n  wrapEnabled = wrapSetting;\n};\nconst autoWrap = function() {\n  return wrapEnabled;\n};\nconst clear = function() {\n  c4ShapeArray = [];\n  boundaries = [\n    {\n      alias: \"global\",\n      label: { text: \"global\" },\n      type: { text: \"global\" },\n      tags: null,\n      link: null,\n      parentBoundary: \"\"\n    }\n  ];\n  parentBoundaryParse = \"\";\n  currentBoundaryParse = \"global\";\n  boundaryParseStack = [\"\"];\n  rels = [];\n  boundaryParseStack = [\"\"];\n  title = \"\";\n  wrapEnabled = false;\n  c4ShapeInRow$1 = 4;\n  c4BoundaryInRow$1 = 2;\n};\nconst LINETYPE = {\n  SOLID: 0,\n  DOTTED: 1,\n  NOTE: 2,\n  SOLID_CROSS: 3,\n  DOTTED_CROSS: 4,\n  SOLID_OPEN: 5,\n  DOTTED_OPEN: 6,\n  LOOP_START: 10,\n  LOOP_END: 11,\n  ALT_START: 12,\n  ALT_ELSE: 13,\n  ALT_END: 14,\n  OPT_START: 15,\n  OPT_END: 16,\n  ACTIVE_START: 17,\n  ACTIVE_END: 18,\n  PAR_START: 19,\n  PAR_AND: 20,\n  PAR_END: 21,\n  RECT_START: 22,\n  RECT_END: 23,\n  SOLID_POINT: 24,\n  DOTTED_POINT: 25\n};\nconst ARROWTYPE = {\n  FILLED: 0,\n  OPEN: 1\n};\nconst PLACEMENT = {\n  LEFTOF: 0,\n  RIGHTOF: 1,\n  OVER: 2\n};\nconst setTitle = function(txt) {\n  let sanitizedText = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.d)(txt, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  title = sanitizedText;\n};\nconst db = {\n  addPersonOrSystem,\n  addPersonOrSystemBoundary,\n  addContainer,\n  addContainerBoundary,\n  addComponent,\n  addDeploymentNode,\n  popBoundaryParseStack,\n  addRel,\n  updateElStyle,\n  updateRelStyle,\n  updateLayoutConfig,\n  autoWrap,\n  setWrap,\n  getC4ShapeArray,\n  getC4Shape,\n  getC4ShapeKeys,\n  getBoundaries,\n  getBoundarys,\n  getCurrentBoundaryParse,\n  getParentBoundaryParse,\n  getRels,\n  getTitle,\n  getC4Type,\n  getC4ShapeInRow,\n  getC4BoundaryInRow,\n  setAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.s,\n  getAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.g,\n  getAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  setAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.b,\n  getConfig: () => (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().c4,\n  clear,\n  LINETYPE,\n  ARROWTYPE,\n  PLACEMENT,\n  setTitle,\n  setC4Type\n  // apply,\n};\nconst drawRect = function(elem, rectData) {\n  return (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.d)(elem, rectData);\n};\nconst drawImage = function(elem, width, height, x, y, link) {\n  const imageElem = elem.append(\"image\");\n  imageElem.attr(\"width\", width);\n  imageElem.attr(\"height\", height);\n  imageElem.attr(\"x\", x);\n  imageElem.attr(\"y\", y);\n  let sanitizedLink = link.startsWith(\"data:image/png;base64\") ? link : (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_1__.sanitizeUrl)(link);\n  imageElem.attr(\"xlink:href\", sanitizedLink);\n};\nconst drawRels$1 = (elem, rels2, conf2) => {\n  const relsElem = elem.append(\"g\");\n  let i = 0;\n  for (let rel of rels2) {\n    let textColor = rel.textColor ? rel.textColor : \"#444444\";\n    let strokeColor = rel.lineColor ? rel.lineColor : \"#444444\";\n    let offsetX = rel.offsetX ? parseInt(rel.offsetX) : 0;\n    let offsetY = rel.offsetY ? parseInt(rel.offsetY) : 0;\n    let url = \"\";\n    if (i === 0) {\n      let line = relsElem.append(\"line\");\n      line.attr(\"x1\", rel.startPoint.x);\n      line.attr(\"y1\", rel.startPoint.y);\n      line.attr(\"x2\", rel.endPoint.x);\n      line.attr(\"y2\", rel.endPoint.y);\n      line.attr(\"stroke-width\", \"1\");\n      line.attr(\"stroke\", strokeColor);\n      line.style(\"fill\", \"none\");\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n      i = -1;\n    } else {\n      let line = relsElem.append(\"path\");\n      line.attr(\"fill\", \"none\").attr(\"stroke-width\", \"1\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,starty Qcontrolx,controly stopx,stopy \".replaceAll(\"startx\", rel.startPoint.x).replaceAll(\"starty\", rel.startPoint.y).replaceAll(\n          \"controlx\",\n          rel.startPoint.x + (rel.endPoint.x - rel.startPoint.x) / 2 - (rel.endPoint.x - rel.startPoint.x) / 4\n        ).replaceAll(\"controly\", rel.startPoint.y + (rel.endPoint.y - rel.startPoint.y) / 2).replaceAll(\"stopx\", rel.endPoint.x).replaceAll(\"stopy\", rel.endPoint.y)\n      );\n      if (rel.type !== \"rel_b\") {\n        line.attr(\"marker-end\", \"url(\" + url + \"#arrowhead)\");\n      }\n      if (rel.type === \"birel\" || rel.type === \"rel_b\") {\n        line.attr(\"marker-start\", \"url(\" + url + \"#arrowend)\");\n      }\n    }\n    let messageConf = conf2.messageFont();\n    _drawTextCandidateFunc(conf2)(\n      rel.label.text,\n      relsElem,\n      Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n      Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + offsetY,\n      rel.label.width,\n      rel.label.height,\n      { fill: textColor },\n      messageConf\n    );\n    if (rel.techn && rel.techn.text !== \"\") {\n      messageConf = conf2.messageFont();\n      _drawTextCandidateFunc(conf2)(\n        \"[\" + rel.techn.text + \"]\",\n        relsElem,\n        Math.min(rel.startPoint.x, rel.endPoint.x) + Math.abs(rel.endPoint.x - rel.startPoint.x) / 2 + offsetX,\n        Math.min(rel.startPoint.y, rel.endPoint.y) + Math.abs(rel.endPoint.y - rel.startPoint.y) / 2 + conf2.messageFontSize + 5 + offsetY,\n        Math.max(rel.label.width, rel.techn.width),\n        rel.techn.height,\n        { fill: textColor, \"font-style\": \"italic\" },\n        messageConf\n      );\n    }\n  }\n};\nconst drawBoundary$1 = function(elem, boundary, conf2) {\n  const boundaryElem = elem.append(\"g\");\n  let fillColor = boundary.bgColor ? boundary.bgColor : \"none\";\n  let strokeColor = boundary.borderColor ? boundary.borderColor : \"#444444\";\n  let fontColor = boundary.fontColor ? boundary.fontColor : \"black\";\n  let attrsValue = { \"stroke-width\": 1, \"stroke-dasharray\": \"7.0,7.0\" };\n  if (boundary.nodeType) {\n    attrsValue = { \"stroke-width\": 1 };\n  }\n  let rectData = {\n    x: boundary.x,\n    y: boundary.y,\n    fill: fillColor,\n    stroke: strokeColor,\n    width: boundary.width,\n    height: boundary.height,\n    rx: 2.5,\n    ry: 2.5,\n    attrs: attrsValue\n  };\n  drawRect(boundaryElem, rectData);\n  let boundaryConf = conf2.boundaryFont();\n  boundaryConf.fontWeight = \"bold\";\n  boundaryConf.fontSize = boundaryConf.fontSize + 2;\n  boundaryConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    boundary.label.text,\n    boundaryElem,\n    boundary.x,\n    boundary.y + boundary.label.Y,\n    boundary.width,\n    boundary.height,\n    { fill: \"#444444\" },\n    boundaryConf\n  );\n  if (boundary.type && boundary.type.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.type.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.type.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n  if (boundary.descr && boundary.descr.text !== \"\") {\n    boundaryConf = conf2.boundaryFont();\n    boundaryConf.fontSize = boundaryConf.fontSize - 2;\n    boundaryConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      boundary.descr.text,\n      boundaryElem,\n      boundary.x,\n      boundary.y + boundary.descr.Y,\n      boundary.width,\n      boundary.height,\n      { fill: \"#444444\" },\n      boundaryConf\n    );\n  }\n};\nconst drawC4Shape = function(elem, c4Shape, conf2) {\n  var _a;\n  let fillColor = c4Shape.bgColor ? c4Shape.bgColor : conf2[c4Shape.typeC4Shape.text + \"_bg_color\"];\n  let strokeColor = c4Shape.borderColor ? c4Shape.borderColor : conf2[c4Shape.typeC4Shape.text + \"_border_color\"];\n  let fontColor = c4Shape.fontColor ? c4Shape.fontColor : \"#FFFFFF\";\n  let personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=\";\n      break;\n    case \"external_person\":\n      personImg = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=\";\n      break;\n  }\n  const c4ShapeElem = elem.append(\"g\");\n  c4ShapeElem.attr(\"class\", \"person-man\");\n  const rect = (0,_svgDrawCommon_08f97a94_js__WEBPACK_IMPORTED_MODULE_6__.g)();\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n    case \"system\":\n    case \"external_system\":\n    case \"container\":\n    case \"external_container\":\n    case \"component\":\n    case \"external_component\":\n      rect.x = c4Shape.x;\n      rect.y = c4Shape.y;\n      rect.fill = fillColor;\n      rect.width = c4Shape.width;\n      rect.height = c4Shape.height;\n      rect.stroke = strokeColor;\n      rect.rx = 2.5;\n      rect.ry = 2.5;\n      rect.attrs = { \"stroke-width\": 0.5 };\n      drawRect(c4ShapeElem, rect);\n      break;\n    case \"system_db\":\n    case \"external_system_db\":\n    case \"container_db\":\n    case \"external_container_db\":\n    case \"component_db\":\n    case \"external_component_db\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2).replaceAll(\"height\", c4Shape.height)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.width / 2)\n      );\n      break;\n    case \"system_queue\":\n    case \"external_system_queue\":\n    case \"container_queue\":\n    case \"external_container_queue\":\n    case \"component_queue\":\n    case \"external_component_queue\":\n      c4ShapeElem.append(\"path\").attr(\"fill\", fillColor).attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half\".replaceAll(\"startx\", c4Shape.x).replaceAll(\"starty\", c4Shape.y).replaceAll(\"width\", c4Shape.width).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      c4ShapeElem.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke-width\", \"0.5\").attr(\"stroke\", strokeColor).attr(\n        \"d\",\n        \"Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half\".replaceAll(\"startx\", c4Shape.x + c4Shape.width).replaceAll(\"starty\", c4Shape.y).replaceAll(\"half\", c4Shape.height / 2)\n      );\n      break;\n  }\n  let c4ShapeFontConf = getC4ShapeFont(conf2, c4Shape.typeC4Shape.text);\n  c4ShapeElem.append(\"text\").attr(\"fill\", fontColor).attr(\"font-family\", c4ShapeFontConf.fontFamily).attr(\"font-size\", c4ShapeFontConf.fontSize - 2).attr(\"font-style\", \"italic\").attr(\"lengthAdjust\", \"spacing\").attr(\"textLength\", c4Shape.typeC4Shape.width).attr(\"x\", c4Shape.x + c4Shape.width / 2 - c4Shape.typeC4Shape.width / 2).attr(\"y\", c4Shape.y + c4Shape.typeC4Shape.Y).text(\"<<\" + c4Shape.typeC4Shape.text + \">>\");\n  switch (c4Shape.typeC4Shape.text) {\n    case \"person\":\n    case \"external_person\":\n      drawImage(\n        c4ShapeElem,\n        48,\n        48,\n        c4Shape.x + c4Shape.width / 2 - 24,\n        c4Shape.y + c4Shape.image.Y,\n        personImg\n      );\n      break;\n  }\n  let textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontWeight = \"bold\";\n  textFontConf.fontSize = textFontConf.fontSize + 2;\n  textFontConf.fontColor = fontColor;\n  _drawTextCandidateFunc(conf2)(\n    c4Shape.label.text,\n    c4ShapeElem,\n    c4Shape.x,\n    c4Shape.y + c4Shape.label.Y,\n    c4Shape.width,\n    c4Shape.height,\n    { fill: fontColor },\n    textFontConf\n  );\n  textFontConf = conf2[c4Shape.typeC4Shape.text + \"Font\"]();\n  textFontConf.fontColor = fontColor;\n  if (c4Shape.techn && ((_a = c4Shape.techn) == null ? void 0 : _a.text) !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.techn.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.techn.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  } else if (c4Shape.type && c4Shape.type.text !== \"\") {\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.type.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.type.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor, \"font-style\": \"italic\" },\n      textFontConf\n    );\n  }\n  if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n    textFontConf = conf2.personFont();\n    textFontConf.fontColor = fontColor;\n    _drawTextCandidateFunc(conf2)(\n      c4Shape.descr.text,\n      c4ShapeElem,\n      c4Shape.x,\n      c4Shape.y + c4Shape.descr.Y,\n      c4Shape.width,\n      c4Shape.height,\n      { fill: fontColor },\n      textFontConf\n    );\n  }\n  return c4Shape.height;\n};\nconst insertDatabaseIcon = function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"database\").attr(\"fill-rule\", \"evenodd\").attr(\"clip-rule\", \"evenodd\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z\"\n  );\n};\nconst insertComputerIcon = function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"computer\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z\"\n  );\n};\nconst insertClockIcon = function(elem) {\n  elem.append(\"defs\").append(\"symbol\").attr(\"id\", \"clock\").attr(\"width\", \"24\").attr(\"height\", \"24\").append(\"path\").attr(\"transform\", \"scale(.5)\").attr(\n    \"d\",\n    \"M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z\"\n  );\n};\nconst insertArrowHead = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 9).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0 0 L 10 5 L 0 10 z\");\n};\nconst insertArrowEnd = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowend\").attr(\"refX\", 1).attr(\"refY\", 5).attr(\"markerUnits\", \"userSpaceOnUse\").attr(\"markerWidth\", 12).attr(\"markerHeight\", 12).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 10 0 L 0 5 L 10 10 z\");\n};\nconst insertArrowFilledHead = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"filled-head\").attr(\"refX\", 18).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 18,7 L9,13 L14,7 L9,1 Z\");\n};\nconst insertDynamicNumber = function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"sequencenumber\").attr(\"refX\", 15).attr(\"refY\", 15).attr(\"markerWidth\", 60).attr(\"markerHeight\", 40).attr(\"orient\", \"auto\").append(\"circle\").attr(\"cx\", 15).attr(\"cy\", 15).attr(\"r\", 6);\n};\nconst insertArrowCrossHead = function(elem) {\n  const defs = elem.append(\"defs\");\n  const marker = defs.append(\"marker\").attr(\"id\", \"crosshead\").attr(\"markerWidth\", 15).attr(\"markerHeight\", 8).attr(\"orient\", \"auto\").attr(\"refX\", 16).attr(\"refY\", 4);\n  marker.append(\"path\").attr(\"fill\", \"black\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 9,2 V 6 L16,4 Z\");\n  marker.append(\"path\").attr(\"fill\", \"none\").attr(\"stroke\", \"#000000\").style(\"stroke-dasharray\", \"0, 0\").attr(\"stroke-width\", \"1px\").attr(\"d\", \"M 0,1 L 6,7 M 6,1 L 0,7\");\n};\nconst getC4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n};\nconst _drawTextCandidateFunc = function() {\n  function byText(content, g, x, y, width, height, textAttrs) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  function byTspan(content, g, x, y, width, height, textAttrs, conf2) {\n    const { fontSize, fontFamily, fontWeight } = conf2;\n    const lines = content.split(_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.lineBreakRegex);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * fontSize - fontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).style(\"text-anchor\", \"middle\").attr(\"dominant-baseline\", \"middle\").style(\"font-size\", fontSize).style(\"font-weight\", fontWeight).style(\"font-family\", fontFamily);\n      text.append(\"tspan\").attr(\"dy\", dy).text(lines[i]).attr(\"alignment-baseline\", \"mathematical\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  function byFo(content, g, x, y, width, height, textAttrs, conf2) {\n    const s = g.append(\"switch\");\n    const f = s.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height);\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, s, x, y, width, height, textAttrs, conf2);\n    _setTextAttrs(text, textAttrs);\n  }\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (fromTextAttrsDict.hasOwnProperty(key)) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  return function(conf2) {\n    return conf2.textPlacement === \"fo\" ? byFo : conf2.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nconst svgDraw = {\n  drawRect,\n  drawBoundary: drawBoundary$1,\n  drawC4Shape,\n  drawRels: drawRels$1,\n  drawImage,\n  insertArrowHead,\n  insertArrowEnd,\n  insertArrowFilledHead,\n  insertDynamicNumber,\n  insertArrowCrossHead,\n  insertDatabaseIcon,\n  insertComputerIcon,\n  insertClockIcon\n};\nlet globalBoundaryMaxX = 0, globalBoundaryMaxY = 0;\nlet c4ShapeInRow = 4;\nlet c4BoundaryInRow = 2;\nparser.yy = db;\nlet conf = {};\nclass Bounds {\n  constructor(diagObj) {\n    this.name = \"\";\n    this.data = {};\n    this.data.startx = void 0;\n    this.data.stopx = void 0;\n    this.data.starty = void 0;\n    this.data.stopy = void 0;\n    this.data.widthLimit = void 0;\n    this.nextData = {};\n    this.nextData.startx = void 0;\n    this.nextData.stopx = void 0;\n    this.nextData.starty = void 0;\n    this.nextData.stopy = void 0;\n    this.nextData.cnt = 0;\n    setConf(diagObj.db.getConfig());\n  }\n  setData(startx, stopx, starty, stopy) {\n    this.nextData.startx = this.data.startx = startx;\n    this.nextData.stopx = this.data.stopx = stopx;\n    this.nextData.starty = this.data.starty = starty;\n    this.nextData.stopy = this.data.stopy = stopy;\n  }\n  updateVal(obj, key, val, fun) {\n    if (obj[key] === void 0) {\n      obj[key] = val;\n    } else {\n      obj[key] = fun(val, obj[key]);\n    }\n  }\n  insert(c4Shape) {\n    this.nextData.cnt = this.nextData.cnt + 1;\n    let _startx = this.nextData.startx === this.nextData.stopx ? this.nextData.stopx + c4Shape.margin : this.nextData.stopx + c4Shape.margin * 2;\n    let _stopx = _startx + c4Shape.width;\n    let _starty = this.nextData.starty + c4Shape.margin * 2;\n    let _stopy = _starty + c4Shape.height;\n    if (_startx >= this.data.widthLimit || _stopx >= this.data.widthLimit || this.nextData.cnt > c4ShapeInRow) {\n      _startx = this.nextData.startx + c4Shape.margin + conf.nextLinePaddingX;\n      _starty = this.nextData.stopy + c4Shape.margin * 2;\n      this.nextData.stopx = _stopx = _startx + c4Shape.width;\n      this.nextData.starty = this.nextData.stopy;\n      this.nextData.stopy = _stopy = _starty + c4Shape.height;\n      this.nextData.cnt = 1;\n    }\n    c4Shape.x = _startx;\n    c4Shape.y = _starty;\n    this.updateVal(this.data, \"startx\", _startx, Math.min);\n    this.updateVal(this.data, \"starty\", _starty, Math.min);\n    this.updateVal(this.data, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.data, \"stopy\", _stopy, Math.max);\n    this.updateVal(this.nextData, \"startx\", _startx, Math.min);\n    this.updateVal(this.nextData, \"starty\", _starty, Math.min);\n    this.updateVal(this.nextData, \"stopx\", _stopx, Math.max);\n    this.updateVal(this.nextData, \"stopy\", _stopy, Math.max);\n  }\n  init(diagObj) {\n    this.name = \"\";\n    this.data = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      widthLimit: void 0\n    };\n    this.nextData = {\n      startx: void 0,\n      stopx: void 0,\n      starty: void 0,\n      stopy: void 0,\n      cnt: 0\n    };\n    setConf(diagObj.db.getConfig());\n  }\n  bumpLastMargin(margin) {\n    this.data.stopx += margin;\n    this.data.stopy += margin;\n  }\n}\nconst setConf = function(cnf) {\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.f)(conf, cnf);\n  if (cnf.fontFamily) {\n    conf.personFontFamily = conf.systemFontFamily = conf.messageFontFamily = cnf.fontFamily;\n  }\n  if (cnf.fontSize) {\n    conf.personFontSize = conf.systemFontSize = conf.messageFontSize = cnf.fontSize;\n  }\n  if (cnf.fontWeight) {\n    conf.personFontWeight = conf.systemFontWeight = conf.messageFontWeight = cnf.fontWeight;\n  }\n};\nconst c4ShapeFont = (cnf, typeC4Shape) => {\n  return {\n    fontFamily: cnf[typeC4Shape + \"FontFamily\"],\n    fontSize: cnf[typeC4Shape + \"FontSize\"],\n    fontWeight: cnf[typeC4Shape + \"FontWeight\"]\n  };\n};\nconst boundaryFont = (cnf) => {\n  return {\n    fontFamily: cnf.boundaryFontFamily,\n    fontSize: cnf.boundaryFontSize,\n    fontWeight: cnf.boundaryFontWeight\n  };\n};\nconst messageFont = (cnf) => {\n  return {\n    fontFamily: cnf.messageFontFamily,\n    fontSize: cnf.messageFontSize,\n    fontWeight: cnf.messageFontWeight\n  };\n};\nfunction calcC4ShapeTextWH(textType, c4Shape, c4ShapeTextWrap, textConf, textLimitWidth) {\n  if (!c4Shape[textType].width) {\n    if (c4ShapeTextWrap) {\n      c4Shape[textType].text = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.w)(c4Shape[textType].text, textLimitWidth, textConf);\n      c4Shape[textType].textLines = c4Shape[textType].text.split(_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.lineBreakRegex).length;\n      c4Shape[textType].width = textLimitWidth;\n      c4Shape[textType].height = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.j)(c4Shape[textType].text, textConf);\n    } else {\n      let lines = c4Shape[textType].text.split(_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.lineBreakRegex);\n      c4Shape[textType].textLines = lines.length;\n      let lineHeight = 0;\n      c4Shape[textType].height = 0;\n      c4Shape[textType].width = 0;\n      for (const line of lines) {\n        c4Shape[textType].width = Math.max(\n          (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.h)(line, textConf),\n          c4Shape[textType].width\n        );\n        lineHeight = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.j)(line, textConf);\n        c4Shape[textType].height = c4Shape[textType].height + lineHeight;\n      }\n    }\n  }\n}\nconst drawBoundary = function(diagram2, boundary, bounds) {\n  boundary.x = bounds.data.startx;\n  boundary.y = bounds.data.starty;\n  boundary.width = bounds.data.stopx - bounds.data.startx;\n  boundary.height = bounds.data.stopy - bounds.data.starty;\n  boundary.label.y = conf.c4ShapeMargin - 35;\n  let boundaryTextWrap = boundary.wrap && conf.wrap;\n  let boundaryLabelConf = boundaryFont(conf);\n  boundaryLabelConf.fontSize = boundaryLabelConf.fontSize + 2;\n  boundaryLabelConf.fontWeight = \"bold\";\n  let textLimitWidth = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.h)(boundary.label.text, boundaryLabelConf);\n  calcC4ShapeTextWH(\"label\", boundary, boundaryTextWrap, boundaryLabelConf, textLimitWidth);\n  svgDraw.drawBoundary(diagram2, boundary, conf);\n};\nconst drawC4ShapeArray = function(currentBounds, diagram2, c4ShapeArray2, c4ShapeKeys) {\n  let Y = 0;\n  for (const c4ShapeKey of c4ShapeKeys) {\n    Y = 0;\n    const c4Shape = c4ShapeArray2[c4ShapeKey];\n    let c4ShapeTypeConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeTypeConf.fontSize = c4ShapeTypeConf.fontSize - 2;\n    c4Shape.typeC4Shape.width = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.h)(\n      \"«\" + c4Shape.typeC4Shape.text + \"»\",\n      c4ShapeTypeConf\n    );\n    c4Shape.typeC4Shape.height = c4ShapeTypeConf.fontSize + 2;\n    c4Shape.typeC4Shape.Y = conf.c4ShapePadding;\n    Y = c4Shape.typeC4Shape.Y + c4Shape.typeC4Shape.height - 4;\n    c4Shape.image = { width: 0, height: 0, Y: 0 };\n    switch (c4Shape.typeC4Shape.text) {\n      case \"person\":\n      case \"external_person\":\n        c4Shape.image.width = 48;\n        c4Shape.image.height = 48;\n        c4Shape.image.Y = Y;\n        Y = c4Shape.image.Y + c4Shape.image.height;\n        break;\n    }\n    if (c4Shape.sprite) {\n      c4Shape.image.width = 48;\n      c4Shape.image.height = 48;\n      c4Shape.image.Y = Y;\n      Y = c4Shape.image.Y + c4Shape.image.height;\n    }\n    let c4ShapeTextWrap = c4Shape.wrap && conf.wrap;\n    let textLimitWidth = conf.width - conf.c4ShapePadding * 2;\n    let c4ShapeLabelConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n    c4ShapeLabelConf.fontSize = c4ShapeLabelConf.fontSize + 2;\n    c4ShapeLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\"label\", c4Shape, c4ShapeTextWrap, c4ShapeLabelConf, textLimitWidth);\n    c4Shape[\"label\"].Y = Y + 8;\n    Y = c4Shape[\"label\"].Y + c4Shape[\"label\"].height;\n    if (c4Shape.type && c4Shape.type.text !== \"\") {\n      c4Shape.type.text = \"[\" + c4Shape.type.text + \"]\";\n      let c4ShapeTypeConf2 = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"type\", c4Shape, c4ShapeTextWrap, c4ShapeTypeConf2, textLimitWidth);\n      c4Shape[\"type\"].Y = Y + 5;\n      Y = c4Shape[\"type\"].Y + c4Shape[\"type\"].height;\n    } else if (c4Shape.techn && c4Shape.techn.text !== \"\") {\n      c4Shape.techn.text = \"[\" + c4Shape.techn.text + \"]\";\n      let c4ShapeTechnConf = c4ShapeFont(conf, c4Shape.techn.text);\n      calcC4ShapeTextWH(\"techn\", c4Shape, c4ShapeTextWrap, c4ShapeTechnConf, textLimitWidth);\n      c4Shape[\"techn\"].Y = Y + 5;\n      Y = c4Shape[\"techn\"].Y + c4Shape[\"techn\"].height;\n    }\n    let rectHeight = Y;\n    let rectWidth = c4Shape.label.width;\n    if (c4Shape.descr && c4Shape.descr.text !== \"\") {\n      let c4ShapeDescrConf = c4ShapeFont(conf, c4Shape.typeC4Shape.text);\n      calcC4ShapeTextWH(\"descr\", c4Shape, c4ShapeTextWrap, c4ShapeDescrConf, textLimitWidth);\n      c4Shape[\"descr\"].Y = Y + 20;\n      Y = c4Shape[\"descr\"].Y + c4Shape[\"descr\"].height;\n      rectWidth = Math.max(c4Shape.label.width, c4Shape.descr.width);\n      rectHeight = Y - c4Shape[\"descr\"].textLines * 5;\n    }\n    rectWidth = rectWidth + conf.c4ShapePadding;\n    c4Shape.width = Math.max(c4Shape.width || conf.width, rectWidth, conf.width);\n    c4Shape.height = Math.max(c4Shape.height || conf.height, rectHeight, conf.height);\n    c4Shape.margin = c4Shape.margin || conf.c4ShapeMargin;\n    currentBounds.insert(c4Shape);\n    svgDraw.drawC4Shape(diagram2, c4Shape, conf);\n  }\n  currentBounds.bumpLastMargin(conf.c4ShapeMargin);\n};\nclass Point {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n}\nlet getIntersectPoint = function(fromNode, endPoint) {\n  let x1 = fromNode.x;\n  let y1 = fromNode.y;\n  let x2 = endPoint.x;\n  let y2 = endPoint.y;\n  let fromCenterX = x1 + fromNode.width / 2;\n  let fromCenterY = y1 + fromNode.height / 2;\n  let dx = Math.abs(x1 - x2);\n  let dy = Math.abs(y1 - y2);\n  let tanDYX = dy / dx;\n  let fromDYX = fromNode.height / fromNode.width;\n  let returnPoint = null;\n  if (y1 == y2 && x1 < x2) {\n    returnPoint = new Point(x1 + fromNode.width, fromCenterY);\n  } else if (y1 == y2 && x1 > x2) {\n    returnPoint = new Point(x1, fromCenterY);\n  } else if (x1 == x2 && y1 < y2) {\n    returnPoint = new Point(fromCenterX, y1 + fromNode.height);\n  } else if (x1 == x2 && y1 > y2) {\n    returnPoint = new Point(fromCenterX, y1);\n  }\n  if (x1 > x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX - dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 < y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY + tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(\n        fromCenterX + dx / dy * fromNode.height / 2,\n        y1 + fromNode.height\n      );\n    }\n  } else if (x1 < x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1 + fromNode.width, fromCenterY - tanDYX * fromNode.width / 2);\n    } else {\n      returnPoint = new Point(fromCenterX + fromNode.height / 2 * dx / dy, y1);\n    }\n  } else if (x1 > x2 && y1 > y2) {\n    if (fromDYX >= tanDYX) {\n      returnPoint = new Point(x1, fromCenterY - fromNode.width / 2 * tanDYX);\n    } else {\n      returnPoint = new Point(fromCenterX - fromNode.height / 2 * dx / dy, y1);\n    }\n  }\n  return returnPoint;\n};\nlet getIntersectPoints = function(fromNode, endNode) {\n  let endIntersectPoint = { x: 0, y: 0 };\n  endIntersectPoint.x = endNode.x + endNode.width / 2;\n  endIntersectPoint.y = endNode.y + endNode.height / 2;\n  let startPoint = getIntersectPoint(fromNode, endIntersectPoint);\n  endIntersectPoint.x = fromNode.x + fromNode.width / 2;\n  endIntersectPoint.y = fromNode.y + fromNode.height / 2;\n  let endPoint = getIntersectPoint(endNode, endIntersectPoint);\n  return { startPoint, endPoint };\n};\nconst drawRels = function(diagram2, rels2, getC4ShapeObj, diagObj) {\n  let i = 0;\n  for (let rel of rels2) {\n    i = i + 1;\n    let relTextWrap = rel.wrap && conf.wrap;\n    let relConf = messageFont(conf);\n    let diagramType = diagObj.db.getC4Type();\n    if (diagramType === \"C4Dynamic\") {\n      rel.label.text = i + \": \" + rel.label.text;\n    }\n    let textLimitWidth = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.h)(rel.label.text, relConf);\n    calcC4ShapeTextWH(\"label\", rel, relTextWrap, relConf, textLimitWidth);\n    if (rel.techn && rel.techn.text !== \"\") {\n      textLimitWidth = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.h)(rel.techn.text, relConf);\n      calcC4ShapeTextWH(\"techn\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    if (rel.descr && rel.descr.text !== \"\") {\n      textLimitWidth = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.h)(rel.descr.text, relConf);\n      calcC4ShapeTextWH(\"descr\", rel, relTextWrap, relConf, textLimitWidth);\n    }\n    let fromNode = getC4ShapeObj(rel.from);\n    let endNode = getC4ShapeObj(rel.to);\n    let points = getIntersectPoints(fromNode, endNode);\n    rel.startPoint = points.startPoint;\n    rel.endPoint = points.endPoint;\n  }\n  svgDraw.drawRels(diagram2, rels2, conf);\n};\nfunction drawInsideBoundary(diagram2, parentBoundaryAlias, parentBounds, currentBoundaries, diagObj) {\n  let currentBounds = new Bounds(diagObj);\n  currentBounds.data.widthLimit = parentBounds.data.widthLimit / Math.min(c4BoundaryInRow, currentBoundaries.length);\n  for (let [i, currentBoundary] of currentBoundaries.entries()) {\n    let Y = 0;\n    currentBoundary.image = { width: 0, height: 0, Y: 0 };\n    if (currentBoundary.sprite) {\n      currentBoundary.image.width = 48;\n      currentBoundary.image.height = 48;\n      currentBoundary.image.Y = Y;\n      Y = currentBoundary.image.Y + currentBoundary.image.height;\n    }\n    let currentBoundaryTextWrap = currentBoundary.wrap && conf.wrap;\n    let currentBoundaryLabelConf = boundaryFont(conf);\n    currentBoundaryLabelConf.fontSize = currentBoundaryLabelConf.fontSize + 2;\n    currentBoundaryLabelConf.fontWeight = \"bold\";\n    calcC4ShapeTextWH(\n      \"label\",\n      currentBoundary,\n      currentBoundaryTextWrap,\n      currentBoundaryLabelConf,\n      currentBounds.data.widthLimit\n    );\n    currentBoundary[\"label\"].Y = Y + 8;\n    Y = currentBoundary[\"label\"].Y + currentBoundary[\"label\"].height;\n    if (currentBoundary.type && currentBoundary.type.text !== \"\") {\n      currentBoundary.type.text = \"[\" + currentBoundary.type.text + \"]\";\n      let currentBoundaryTypeConf = boundaryFont(conf);\n      calcC4ShapeTextWH(\n        \"type\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryTypeConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary[\"type\"].Y = Y + 5;\n      Y = currentBoundary[\"type\"].Y + currentBoundary[\"type\"].height;\n    }\n    if (currentBoundary.descr && currentBoundary.descr.text !== \"\") {\n      let currentBoundaryDescrConf = boundaryFont(conf);\n      currentBoundaryDescrConf.fontSize = currentBoundaryDescrConf.fontSize - 2;\n      calcC4ShapeTextWH(\n        \"descr\",\n        currentBoundary,\n        currentBoundaryTextWrap,\n        currentBoundaryDescrConf,\n        currentBounds.data.widthLimit\n      );\n      currentBoundary[\"descr\"].Y = Y + 20;\n      Y = currentBoundary[\"descr\"].Y + currentBoundary[\"descr\"].height;\n    }\n    if (i == 0 || i % c4BoundaryInRow === 0) {\n      let _x = parentBounds.data.startx + conf.diagramMarginX;\n      let _y = parentBounds.data.stopy + conf.diagramMarginY + Y;\n      currentBounds.setData(_x, _x, _y, _y);\n    } else {\n      let _x = currentBounds.data.stopx !== currentBounds.data.startx ? currentBounds.data.stopx + conf.diagramMarginX : currentBounds.data.startx;\n      let _y = currentBounds.data.starty;\n      currentBounds.setData(_x, _x, _y, _y);\n    }\n    currentBounds.name = currentBoundary.alias;\n    let currentPersonOrSystemArray = diagObj.db.getC4ShapeArray(currentBoundary.alias);\n    let currentPersonOrSystemKeys = diagObj.db.getC4ShapeKeys(currentBoundary.alias);\n    if (currentPersonOrSystemKeys.length > 0) {\n      drawC4ShapeArray(\n        currentBounds,\n        diagram2,\n        currentPersonOrSystemArray,\n        currentPersonOrSystemKeys\n      );\n    }\n    parentBoundaryAlias = currentBoundary.alias;\n    let nextCurrentBoundaries = diagObj.db.getBoundarys(parentBoundaryAlias);\n    if (nextCurrentBoundaries.length > 0) {\n      drawInsideBoundary(\n        diagram2,\n        parentBoundaryAlias,\n        currentBounds,\n        nextCurrentBoundaries,\n        diagObj\n      );\n    }\n    if (currentBoundary.alias !== \"global\") {\n      drawBoundary(diagram2, currentBoundary, currentBounds);\n    }\n    parentBounds.data.stopy = Math.max(\n      currentBounds.data.stopy + conf.c4ShapeMargin,\n      parentBounds.data.stopy\n    );\n    parentBounds.data.stopx = Math.max(\n      currentBounds.data.stopx + conf.c4ShapeMargin,\n      parentBounds.data.stopx\n    );\n    globalBoundaryMaxX = Math.max(globalBoundaryMaxX, parentBounds.data.stopx);\n    globalBoundaryMaxY = Math.max(globalBoundaryMaxY, parentBounds.data.stopy);\n  }\n}\nconst draw = function(_text, id, _version, diagObj) {\n  conf = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().c4;\n  const securityLevel = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"body\");\n  let db2 = diagObj.db;\n  diagObj.db.setWrap(conf.wrap);\n  c4ShapeInRow = db2.getC4ShapeInRow();\n  c4BoundaryInRow = db2.getC4BoundaryInRow();\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(`C:${JSON.stringify(conf, null, 2)}`);\n  const diagram2 = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(`[id=\"${id}\"]`);\n  svgDraw.insertComputerIcon(diagram2);\n  svgDraw.insertDatabaseIcon(diagram2);\n  svgDraw.insertClockIcon(diagram2);\n  let screenBounds = new Bounds(diagObj);\n  screenBounds.setData(\n    conf.diagramMarginX,\n    conf.diagramMarginX,\n    conf.diagramMarginY,\n    conf.diagramMarginY\n  );\n  screenBounds.data.widthLimit = screen.availWidth;\n  globalBoundaryMaxX = conf.diagramMarginX;\n  globalBoundaryMaxY = conf.diagramMarginY;\n  const title2 = diagObj.db.getTitle();\n  let currentBoundaries = diagObj.db.getBoundarys(\"\");\n  drawInsideBoundary(diagram2, \"\", screenBounds, currentBoundaries, diagObj);\n  svgDraw.insertArrowHead(diagram2);\n  svgDraw.insertArrowEnd(diagram2);\n  svgDraw.insertArrowCrossHead(diagram2);\n  svgDraw.insertArrowFilledHead(diagram2);\n  drawRels(diagram2, diagObj.db.getRels(), diagObj.db.getC4Shape, diagObj);\n  screenBounds.data.stopx = globalBoundaryMaxX;\n  screenBounds.data.stopy = globalBoundaryMaxY;\n  const box = screenBounds.data;\n  let boxHeight = box.stopy - box.starty;\n  let height = boxHeight + 2 * conf.diagramMarginY;\n  let boxWidth = box.stopx - box.startx;\n  const width = boxWidth + 2 * conf.diagramMarginX;\n  if (title2) {\n    diagram2.append(\"text\").text(title2).attr(\"x\", (box.stopx - box.startx) / 2 - 4 * conf.diagramMarginX).attr(\"y\", box.starty + conf.diagramMarginY);\n  }\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.i)(diagram2, height, width, conf.useMaxWidth);\n  const extraVertForTitle = title2 ? 60 : 0;\n  diagram2.attr(\n    \"viewBox\",\n    box.startx - conf.diagramMarginX + \" -\" + (conf.diagramMarginY + extraVertForTitle) + \" \" + width + \" \" + (height + extraVertForTitle)\n  );\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(`models:`, box);\n};\nconst renderer = {\n  drawPersonOrSystemArray: drawC4ShapeArray,\n  drawBoundary,\n  setConf,\n  draw\n};\nconst getStyles = (options) => `.person {\n    stroke: ${options.personBorder};\n    fill: ${options.personBkg};\n  }\n`;\nconst styles = getStyles;\nconst diagram = {\n  parser: parser$1,\n  db,\n  renderer,\n  styles,\n  init: ({ c4, wrap }) => {\n    renderer.setConf(c4);\n    db.setWrap(wrap);\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvYzREaWFncmFtLTNkNGU0OGNmLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXFTO0FBQ3pRO0FBQ29EO0FBQzFCO0FBQ25DO0FBQ0o7QUFDSTtBQUNIO0FBQ2M7QUFDRjtBQUNaO0FBQ2M7QUFDOUI7QUFDQTtBQUNBLHNCQUFzQixnQkFBZ0IsS0FBSztBQUMzQztBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsVUFBVTtBQUNWLGdCQUFnQiw4L0NBQTgvQztBQUM5Z0Qsa0JBQWtCLHlxQ0FBeXFDO0FBQzNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLGNBQWMscUlBQXFJLElBQUksUUFBUSxJQUFJLFdBQVcsSUFBSSxXQUFXLElBQUksV0FBVyxJQUFJLFdBQVcsSUFBSSxXQUFXLElBQUksV0FBVyxJQUFJLFdBQVcsSUFBSSxhQUFhLElBQUksYUFBYSxJQUFJLGFBQWEsSUFBSSxhQUFhLElBQUksYUFBYSxJQUFJLG1jQUFtYyxJQUFJLG1jQUFtYyxJQUFJLG1jQUFtYyxJQUFJLG1jQUFtYyxJQUFJLG1jQUFtYyxJQUFJLGFBQWEsb0JBQW9CLDhYQUE4WCxzQ0FBc0MsYUFBYSxxQkFBcUIsYUFBYSx1Q0FBdUMsYUFBYSxJQUFJLGFBQWEscUJBQXFCLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLG9EQUFvRCxJQUFJLHFEQUFxRCxJQUFJLHFEQUFxRCxJQUFJLHFEQUFxRCxJQUFJLHFEQUFxRCxJQUFJLHFEQUFxRCxxQkFBcUIscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUksK1hBQStYLElBQUksNEJBQTRCLElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUkscURBQXFELElBQUksY0FBYyxJQUFJLGNBQWMsSUFBSSxjQUFjLElBQUksY0FBYyxJQUFJLFdBQVcscUNBQXFDLDhEQUE4RCxxQkFBcUIsb2NBQW9jLHdFQUF3RSxxREFBcUQsc0JBQXNCLGNBQWMsb2lCQUFvaUIsdUJBQXVCLElBQUksY0FBYyxJQUFJLGNBQWMsMkhBQTJILFdBQVcsSUFBSSxZQUFZLElBQUksWUFBWSxJQUFJLFlBQVksMklBQTJJLGNBQWM7QUFDMzRPLHNCQUFzQixnSkFBZ0o7QUFDdEs7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEM7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixrQkFBa0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLE9BQU87QUFDUCw0REFBNEQ7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsNEpBQTRKLGlDQUFpQyx3SEFBd0gsZUFBZSxjQUFjLGtCQUFrQixnMENBQWcwQyxXQUFXO0FBQy9xRCxvQkFBb0IseUJBQXlCLHVDQUF1QyxpQkFBaUIsa0NBQWtDLGlCQUFpQixrQ0FBa0MsdUJBQXVCLHVDQUF1QyxxQkFBcUIsbUNBQW1DLGlCQUFpQixtQ0FBbUMsY0FBYyx1Q0FBdUMsaUJBQWlCLDJEQUEyRCw0QkFBNEIsK0NBQStDLHdCQUF3QiwrQ0FBK0MsdUJBQXVCLCtDQUErQyxhQUFhLCtDQUErQyxhQUFhLCtDQUErQyxhQUFhLCtDQUErQyxhQUFhLCtDQUErQyxhQUFhLCtDQUErQyxjQUFjLGlDQUFpQyxXQUFXLCtDQUErQyxjQUFjLCtDQUErQyxjQUFjLCtDQUErQyxZQUFZLCtDQUErQyxhQUFhLGlDQUFpQyxpQkFBaUIsK0NBQStDLDJCQUEyQixpQ0FBaUMsd0JBQXdCLCtDQUErQyxxQkFBcUIsK0NBQStDLHVCQUF1QiwrQ0FBK0Msb0JBQW9CLCtDQUErQyxpQkFBaUIsK0NBQStDLDBCQUEwQiwrQ0FBK0MsMkJBQTJCLCtDQUErQyx3QkFBd0IsK0NBQStDLHFCQUFxQiwrQ0FBK0MsdUJBQXVCLCtDQUErQyxvQkFBb0IsK0NBQStDLGlCQUFpQiwrQ0FBK0MsYUFBYSwrQ0FBK0MsdUJBQXVCLCtDQUErQywyQkFBMkIsK0NBQStDLGdCQUFnQiwrQ0FBK0Msd0JBQXdCLCtDQUErQyxxQkFBcUIsK0NBQStDLGtCQUFrQiwrQ0FBK0Msb0JBQW9CLCtDQUErQyxpQkFBaUIsK0NBQStDLGNBQWMsK0NBQStDLGtCQUFrQiwrQ0FBK0MsY0FBYywrQ0FBK0MsZUFBZTtBQUNoaEc7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGdCQUFnQjtBQUM3QixZQUFZLGdCQUFnQjtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsdURBQVksY0FBYyx1REFBUztBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0Esa0JBQWtCO0FBQ2xCLElBQUk7QUFDSjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CLE1BQU07QUFDTixvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCLElBQUk7QUFDSjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CLE1BQU07QUFDTixvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCLElBQUk7QUFDSiw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QixJQUFJO0FBQ0o7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QixNQUFNO0FBQ04sK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QixJQUFJO0FBQ0osd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsSUFBSTtBQUNKO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIsTUFBTTtBQUNOLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsSUFBSTtBQUNKO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIsTUFBTTtBQUNOLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsSUFBSTtBQUNKLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCLElBQUk7QUFDSjtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLE1BQU07QUFDTiwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCLElBQUk7QUFDSjtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLE1BQU07QUFDTiwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCLElBQUk7QUFDSix1QkFBdUI7QUFDdkI7QUFDQTtBQUNBLHNCQUFzQjtBQUN0QixJQUFJO0FBQ0o7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QixNQUFNO0FBQ04sd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QixJQUFJO0FBQ0osdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEIsSUFBSTtBQUNKO0FBQ0E7QUFDQSx3QkFBd0I7QUFDeEIsTUFBTTtBQUNOLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkIsSUFBSTtBQUNKLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCLElBQUk7QUFDSjtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCLE1BQU07QUFDTix3QkFBd0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCLElBQUk7QUFDSjtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCLE1BQU07QUFDTix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxnQkFBZ0I7QUFDL0IsY0FBYyxnQkFBZ0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix1REFBWSxNQUFNLHVEQUFTO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLGFBQWE7QUFDYixtQkFBbUI7QUFDbkIsbUJBQW1CO0FBQ25CLG1CQUFtQix1REFBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDZEQUFVO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELGtCQUFrQixvRUFBVztBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUJBQWlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHlDQUF5QztBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLGlCQUFpQjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlCQUFpQjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpQkFBaUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsNkRBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxpQkFBaUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5Q0FBeUM7QUFDakQ7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEseUNBQXlDO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxpQkFBaUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxtQ0FBbUM7QUFDL0MsZ0NBQWdDLG1EQUFNO0FBQ3RDLG9CQUFvQixrQkFBa0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSx1REFBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix1REFBUztBQUN4QyxpRUFBaUUsbURBQU07QUFDdkU7QUFDQSxpQ0FBaUMsdURBQW1CO0FBQ3BELE1BQU07QUFDTiwrQ0FBK0MsbURBQU07QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx1REFBa0I7QUFDNUI7QUFDQTtBQUNBLHFCQUFxQix1REFBbUI7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHVEQUFrQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyx1REFBa0I7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHVEQUFrQjtBQUMzQztBQUNBO0FBQ0EsdUJBQXVCLHVEQUFrQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsdURBQWtCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLHVEQUFTO0FBQ2xCLHdCQUF3Qix1REFBUztBQUNqQztBQUNBO0FBQ0EscUJBQXFCLDBDQUFNO0FBQzNCO0FBQ0EsNkNBQTZDLDBDQUFNLG1EQUFtRCwwQ0FBTTtBQUM1RztBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsbURBQUcsWUFBWSw4QkFBOEI7QUFDL0MscUVBQXFFLEdBQUcsT0FBTywwQ0FBTSxTQUFTLEdBQUc7QUFDakc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLHVEQUFnQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxtREFBRztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2QsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFHRSIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9tZXJtYWlkL2Rpc3QvYzREaWFncmFtLTNkNGU0OGNmLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHMgYXMgc2V0QWNjVGl0bGUsIGcgYXMgZ2V0QWNjVGl0bGUsIGEgYXMgZ2V0QWNjRGVzY3JpcHRpb24sIGIgYXMgc2V0QWNjRGVzY3JpcHRpb24sIGMgYXMgZ2V0Q29uZmlnLCBkIGFzIHNhbml0aXplVGV4dCwgZSBhcyBjb21tb24sIGYgYXMgYXNzaWduV2l0aERlcHRoLCBoIGFzIGNhbGN1bGF0ZVRleHRXaWR0aCwgbCBhcyBsb2csIGkgYXMgY29uZmlndXJlU3ZnU2l6ZSwgdyBhcyB3cmFwTGFiZWwsIGogYXMgY2FsY3VsYXRlVGV4dEhlaWdodCB9IGZyb20gXCIuL21lcm1haWQtYjU4NjBiNTQuanNcIjtcbmltcG9ydCB7IHNlbGVjdCB9IGZyb20gXCJkM1wiO1xuaW1wb3J0IHsgZCBhcyBkcmF3UmVjdCQxLCBnIGFzIGdldE5vdGVSZWN0IH0gZnJvbSBcIi4vc3ZnRHJhd0NvbW1vbi0wOGY5N2E5NC5qc1wiO1xuaW1wb3J0IHsgc2FuaXRpemVVcmwgfSBmcm9tIFwiQGJyYWludHJlZS9zYW5pdGl6ZS11cmxcIjtcbmltcG9ydCBcInRzLWRlZGVudFwiO1xuaW1wb3J0IFwiZGF5anNcIjtcbmltcG9ydCBcImRvbXB1cmlmeVwiO1xuaW1wb3J0IFwia2hyb21hXCI7XG5pbXBvcnQgXCJsb2Rhc2gtZXMvbWVtb2l6ZS5qc1wiO1xuaW1wb3J0IFwibG9kYXNoLWVzL21lcmdlLmpzXCI7XG5pbXBvcnQgXCJzdHlsaXNcIjtcbmltcG9ydCBcImxvZGFzaC1lcy9pc0VtcHR5LmpzXCI7XG52YXIgcGFyc2VyID0gZnVuY3Rpb24oKSB7XG4gIHZhciBvID0gZnVuY3Rpb24oaywgdiwgbzIsIGwpIHtcbiAgICBmb3IgKG8yID0gbzIgfHwge30sIGwgPSBrLmxlbmd0aDsgbC0tOyBvMltrW2xdXSA9IHYpXG4gICAgICA7XG4gICAgcmV0dXJuIG8yO1xuICB9LCAkVjAgPSBbMSwgMjRdLCAkVjEgPSBbMSwgMjVdLCAkVjIgPSBbMSwgMjZdLCAkVjMgPSBbMSwgMjddLCAkVjQgPSBbMSwgMjhdLCAkVjUgPSBbMSwgNjNdLCAkVjYgPSBbMSwgNjRdLCAkVjcgPSBbMSwgNjVdLCAkVjggPSBbMSwgNjZdLCAkVjkgPSBbMSwgNjddLCAkVmEgPSBbMSwgNjhdLCAkVmIgPSBbMSwgNjldLCAkVmMgPSBbMSwgMjldLCAkVmQgPSBbMSwgMzBdLCAkVmUgPSBbMSwgMzFdLCAkVmYgPSBbMSwgMzJdLCAkVmcgPSBbMSwgMzNdLCAkVmggPSBbMSwgMzRdLCAkVmkgPSBbMSwgMzVdLCAkVmogPSBbMSwgMzZdLCAkVmsgPSBbMSwgMzddLCAkVmwgPSBbMSwgMzhdLCAkVm0gPSBbMSwgMzldLCAkVm4gPSBbMSwgNDBdLCAkVm8gPSBbMSwgNDFdLCAkVnAgPSBbMSwgNDJdLCAkVnEgPSBbMSwgNDNdLCAkVnIgPSBbMSwgNDRdLCAkVnMgPSBbMSwgNDVdLCAkVnQgPSBbMSwgNDZdLCAkVnUgPSBbMSwgNDddLCAkVnYgPSBbMSwgNDhdLCAkVncgPSBbMSwgNTBdLCAkVnggPSBbMSwgNTFdLCAkVnkgPSBbMSwgNTJdLCAkVnogPSBbMSwgNTNdLCAkVkEgPSBbMSwgNTRdLCAkVkIgPSBbMSwgNTVdLCAkVkMgPSBbMSwgNTZdLCAkVkQgPSBbMSwgNTddLCAkVkUgPSBbMSwgNThdLCAkVkYgPSBbMSwgNTldLCAkVkcgPSBbMSwgNjBdLCAkVkggPSBbMTQsIDQyXSwgJFZJID0gWzE0LCAzNCwgMzYsIDM3LCAzOCwgMzksIDQwLCA0MSwgNDIsIDQ0LCA0NSwgNDYsIDQ3LCA0OCwgNDksIDUwLCA1MSwgNTIsIDUzLCA1NCwgNTUsIDU2LCA1NywgNTgsIDU5LCA2MCwgNjEsIDYyLCA2MywgNjQsIDY1LCA2NiwgNjcsIDY4LCA2OSwgNzAsIDcxLCA3MiwgNzMsIDc0XSwgJFZKID0gWzEyLCAxNCwgMzQsIDM2LCAzNywgMzgsIDM5LCA0MCwgNDEsIDQyLCA0NCwgNDUsIDQ2LCA0NywgNDgsIDQ5LCA1MCwgNTEsIDUyLCA1MywgNTQsIDU1LCA1NiwgNTcsIDU4LCA1OSwgNjAsIDYxLCA2MiwgNjMsIDY0LCA2NSwgNjYsIDY3LCA2OCwgNjksIDcwLCA3MSwgNzIsIDczLCA3NF0sICRWSyA9IFsxLCA4Ml0sICRWTCA9IFsxLCA4M10sICRWTSA9IFsxLCA4NF0sICRWTiA9IFsxLCA4NV0sICRWTyA9IFsxMiwgMTQsIDQyXSwgJFZQID0gWzEyLCAxNCwgMzMsIDQyXSwgJFZRID0gWzEyLCAxNCwgMzMsIDQyLCA3NiwgNzcsIDc5LCA4MF0sICRWUiA9IFsxMiwgMzNdLCAkVlMgPSBbMzQsIDM2LCAzNywgMzgsIDM5LCA0MCwgNDEsIDQ0LCA0NSwgNDYsIDQ3LCA0OCwgNDksIDUwLCA1MSwgNTIsIDUzLCA1NCwgNTUsIDU2LCA1NywgNTgsIDU5LCA2MCwgNjEsIDYyLCA2MywgNjQsIDY1LCA2NiwgNjcsIDY4LCA2OSwgNzAsIDcxLCA3MiwgNzMsIDc0XTtcbiAgdmFyIHBhcnNlcjIgPSB7XG4gICAgdHJhY2U6IGZ1bmN0aW9uIHRyYWNlKCkge1xuICAgIH0sXG4gICAgeXk6IHt9LFxuICAgIHN5bWJvbHNfOiB7IFwiZXJyb3JcIjogMiwgXCJzdGFydFwiOiAzLCBcIm1lcm1haWREb2NcIjogNCwgXCJkaXJlY3Rpb25cIjogNSwgXCJkaXJlY3Rpb25fdGJcIjogNiwgXCJkaXJlY3Rpb25fYnRcIjogNywgXCJkaXJlY3Rpb25fcmxcIjogOCwgXCJkaXJlY3Rpb25fbHJcIjogOSwgXCJncmFwaENvbmZpZ1wiOiAxMCwgXCJDNF9DT05URVhUXCI6IDExLCBcIk5FV0xJTkVcIjogMTIsIFwic3RhdGVtZW50c1wiOiAxMywgXCJFT0ZcIjogMTQsIFwiQzRfQ09OVEFJTkVSXCI6IDE1LCBcIkM0X0NPTVBPTkVOVFwiOiAxNiwgXCJDNF9EWU5BTUlDXCI6IDE3LCBcIkM0X0RFUExPWU1FTlRcIjogMTgsIFwib3RoZXJTdGF0ZW1lbnRzXCI6IDE5LCBcImRpYWdyYW1TdGF0ZW1lbnRzXCI6IDIwLCBcIm90aGVyU3RhdGVtZW50XCI6IDIxLCBcInRpdGxlXCI6IDIyLCBcImFjY0Rlc2NyaXB0aW9uXCI6IDIzLCBcImFjY190aXRsZVwiOiAyNCwgXCJhY2NfdGl0bGVfdmFsdWVcIjogMjUsIFwiYWNjX2Rlc2NyXCI6IDI2LCBcImFjY19kZXNjcl92YWx1ZVwiOiAyNywgXCJhY2NfZGVzY3JfbXVsdGlsaW5lX3ZhbHVlXCI6IDI4LCBcImJvdW5kYXJ5U3RhdGVtZW50XCI6IDI5LCBcImJvdW5kYXJ5U3RhcnRTdGF0ZW1lbnRcIjogMzAsIFwiYm91bmRhcnlTdG9wU3RhdGVtZW50XCI6IDMxLCBcImJvdW5kYXJ5U3RhcnRcIjogMzIsIFwiTEJSQUNFXCI6IDMzLCBcIkVOVEVSUFJJU0VfQk9VTkRBUllcIjogMzQsIFwiYXR0cmlidXRlc1wiOiAzNSwgXCJTWVNURU1fQk9VTkRBUllcIjogMzYsIFwiQk9VTkRBUllcIjogMzcsIFwiQ09OVEFJTkVSX0JPVU5EQVJZXCI6IDM4LCBcIk5PREVcIjogMzksIFwiTk9ERV9MXCI6IDQwLCBcIk5PREVfUlwiOiA0MSwgXCJSQlJBQ0VcIjogNDIsIFwiZGlhZ3JhbVN0YXRlbWVudFwiOiA0MywgXCJQRVJTT05cIjogNDQsIFwiUEVSU09OX0VYVFwiOiA0NSwgXCJTWVNURU1cIjogNDYsIFwiU1lTVEVNX0RCXCI6IDQ3LCBcIlNZU1RFTV9RVUVVRVwiOiA0OCwgXCJTWVNURU1fRVhUXCI6IDQ5LCBcIlNZU1RFTV9FWFRfREJcIjogNTAsIFwiU1lTVEVNX0VYVF9RVUVVRVwiOiA1MSwgXCJDT05UQUlORVJcIjogNTIsIFwiQ09OVEFJTkVSX0RCXCI6IDUzLCBcIkNPTlRBSU5FUl9RVUVVRVwiOiA1NCwgXCJDT05UQUlORVJfRVhUXCI6IDU1LCBcIkNPTlRBSU5FUl9FWFRfREJcIjogNTYsIFwiQ09OVEFJTkVSX0VYVF9RVUVVRVwiOiA1NywgXCJDT01QT05FTlRcIjogNTgsIFwiQ09NUE9ORU5UX0RCXCI6IDU5LCBcIkNPTVBPTkVOVF9RVUVVRVwiOiA2MCwgXCJDT01QT05FTlRfRVhUXCI6IDYxLCBcIkNPTVBPTkVOVF9FWFRfREJcIjogNjIsIFwiQ09NUE9ORU5UX0VYVF9RVUVVRVwiOiA2MywgXCJSRUxcIjogNjQsIFwiQklSRUxcIjogNjUsIFwiUkVMX1VcIjogNjYsIFwiUkVMX0RcIjogNjcsIFwiUkVMX0xcIjogNjgsIFwiUkVMX1JcIjogNjksIFwiUkVMX0JcIjogNzAsIFwiUkVMX0lOREVYXCI6IDcxLCBcIlVQREFURV9FTF9TVFlMRVwiOiA3MiwgXCJVUERBVEVfUkVMX1NUWUxFXCI6IDczLCBcIlVQREFURV9MQVlPVVRfQ09ORklHXCI6IDc0LCBcImF0dHJpYnV0ZVwiOiA3NSwgXCJTVFJcIjogNzYsIFwiU1RSX0tFWVwiOiA3NywgXCJTVFJfVkFMVUVcIjogNzgsIFwiQVRUUklCVVRFXCI6IDc5LCBcIkFUVFJJQlVURV9FTVBUWVwiOiA4MCwgXCIkYWNjZXB0XCI6IDAsIFwiJGVuZFwiOiAxIH0sXG4gICAgdGVybWluYWxzXzogeyAyOiBcImVycm9yXCIsIDY6IFwiZGlyZWN0aW9uX3RiXCIsIDc6IFwiZGlyZWN0aW9uX2J0XCIsIDg6IFwiZGlyZWN0aW9uX3JsXCIsIDk6IFwiZGlyZWN0aW9uX2xyXCIsIDExOiBcIkM0X0NPTlRFWFRcIiwgMTI6IFwiTkVXTElORVwiLCAxNDogXCJFT0ZcIiwgMTU6IFwiQzRfQ09OVEFJTkVSXCIsIDE2OiBcIkM0X0NPTVBPTkVOVFwiLCAxNzogXCJDNF9EWU5BTUlDXCIsIDE4OiBcIkM0X0RFUExPWU1FTlRcIiwgMjI6IFwidGl0bGVcIiwgMjM6IFwiYWNjRGVzY3JpcHRpb25cIiwgMjQ6IFwiYWNjX3RpdGxlXCIsIDI1OiBcImFjY190aXRsZV92YWx1ZVwiLCAyNjogXCJhY2NfZGVzY3JcIiwgMjc6IFwiYWNjX2Rlc2NyX3ZhbHVlXCIsIDI4OiBcImFjY19kZXNjcl9tdWx0aWxpbmVfdmFsdWVcIiwgMzM6IFwiTEJSQUNFXCIsIDM0OiBcIkVOVEVSUFJJU0VfQk9VTkRBUllcIiwgMzY6IFwiU1lTVEVNX0JPVU5EQVJZXCIsIDM3OiBcIkJPVU5EQVJZXCIsIDM4OiBcIkNPTlRBSU5FUl9CT1VOREFSWVwiLCAzOTogXCJOT0RFXCIsIDQwOiBcIk5PREVfTFwiLCA0MTogXCJOT0RFX1JcIiwgNDI6IFwiUkJSQUNFXCIsIDQ0OiBcIlBFUlNPTlwiLCA0NTogXCJQRVJTT05fRVhUXCIsIDQ2OiBcIlNZU1RFTVwiLCA0NzogXCJTWVNURU1fREJcIiwgNDg6IFwiU1lTVEVNX1FVRVVFXCIsIDQ5OiBcIlNZU1RFTV9FWFRcIiwgNTA6IFwiU1lTVEVNX0VYVF9EQlwiLCA1MTogXCJTWVNURU1fRVhUX1FVRVVFXCIsIDUyOiBcIkNPTlRBSU5FUlwiLCA1MzogXCJDT05UQUlORVJfREJcIiwgNTQ6IFwiQ09OVEFJTkVSX1FVRVVFXCIsIDU1OiBcIkNPTlRBSU5FUl9FWFRcIiwgNTY6IFwiQ09OVEFJTkVSX0VYVF9EQlwiLCA1NzogXCJDT05UQUlORVJfRVhUX1FVRVVFXCIsIDU4OiBcIkNPTVBPTkVOVFwiLCA1OTogXCJDT01QT05FTlRfREJcIiwgNjA6IFwiQ09NUE9ORU5UX1FVRVVFXCIsIDYxOiBcIkNPTVBPTkVOVF9FWFRcIiwgNjI6IFwiQ09NUE9ORU5UX0VYVF9EQlwiLCA2MzogXCJDT01QT05FTlRfRVhUX1FVRVVFXCIsIDY0OiBcIlJFTFwiLCA2NTogXCJCSVJFTFwiLCA2NjogXCJSRUxfVVwiLCA2NzogXCJSRUxfRFwiLCA2ODogXCJSRUxfTFwiLCA2OTogXCJSRUxfUlwiLCA3MDogXCJSRUxfQlwiLCA3MTogXCJSRUxfSU5ERVhcIiwgNzI6IFwiVVBEQVRFX0VMX1NUWUxFXCIsIDczOiBcIlVQREFURV9SRUxfU1RZTEVcIiwgNzQ6IFwiVVBEQVRFX0xBWU9VVF9DT05GSUdcIiwgNzY6IFwiU1RSXCIsIDc3OiBcIlNUUl9LRVlcIiwgNzg6IFwiU1RSX1ZBTFVFXCIsIDc5OiBcIkFUVFJJQlVURVwiLCA4MDogXCJBVFRSSUJVVEVfRU1QVFlcIiB9LFxuICAgIHByb2R1Y3Rpb25zXzogWzAsIFszLCAxXSwgWzMsIDFdLCBbNSwgMV0sIFs1LCAxXSwgWzUsIDFdLCBbNSwgMV0sIFs0LCAxXSwgWzEwLCA0XSwgWzEwLCA0XSwgWzEwLCA0XSwgWzEwLCA0XSwgWzEwLCA0XSwgWzEzLCAxXSwgWzEzLCAxXSwgWzEzLCAyXSwgWzE5LCAxXSwgWzE5LCAyXSwgWzE5LCAzXSwgWzIxLCAxXSwgWzIxLCAxXSwgWzIxLCAyXSwgWzIxLCAyXSwgWzIxLCAxXSwgWzI5LCAzXSwgWzMwLCAzXSwgWzMwLCAzXSwgWzMwLCA0XSwgWzMyLCAyXSwgWzMyLCAyXSwgWzMyLCAyXSwgWzMyLCAyXSwgWzMyLCAyXSwgWzMyLCAyXSwgWzMyLCAyXSwgWzMxLCAxXSwgWzIwLCAxXSwgWzIwLCAyXSwgWzIwLCAzXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAxXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzQzLCAyXSwgWzM1LCAxXSwgWzM1LCAyXSwgWzc1LCAxXSwgWzc1LCAyXSwgWzc1LCAxXSwgWzc1LCAxXV0sXG4gICAgcGVyZm9ybUFjdGlvbjogZnVuY3Rpb24gYW5vbnltb3VzKHl5dGV4dCwgeXlsZW5nLCB5eWxpbmVubywgeXksIHl5c3RhdGUsICQkLCBfJCkge1xuICAgICAgdmFyICQwID0gJCQubGVuZ3RoIC0gMTtcbiAgICAgIHN3aXRjaCAoeXlzdGF0ZSkge1xuICAgICAgICBjYXNlIDM6XG4gICAgICAgICAgeXkuc2V0RGlyZWN0aW9uKFwiVEJcIik7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDpcbiAgICAgICAgICB5eS5zZXREaXJlY3Rpb24oXCJCVFwiKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA1OlxuICAgICAgICAgIHl5LnNldERpcmVjdGlvbihcIlJMXCIpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDY6XG4gICAgICAgICAgeXkuc2V0RGlyZWN0aW9uKFwiTFJcIik7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgODpcbiAgICAgICAgY2FzZSA5OlxuICAgICAgICBjYXNlIDEwOlxuICAgICAgICBjYXNlIDExOlxuICAgICAgICBjYXNlIDEyOlxuICAgICAgICAgIHl5LnNldEM0VHlwZSgkJFskMCAtIDNdKTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAxOTpcbiAgICAgICAgICB5eS5zZXRUaXRsZSgkJFskMF0uc3Vic3RyaW5nKDYpKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF0uc3Vic3RyaW5nKDYpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDIwOlxuICAgICAgICAgIHl5LnNldEFjY0Rlc2NyaXB0aW9uKCQkWyQwXS5zdWJzdHJpbmcoMTUpKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF0uc3Vic3RyaW5nKDE1KTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAyMTpcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF0udHJpbSgpO1xuICAgICAgICAgIHl5LnNldFRpdGxlKHRoaXMuJCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgMjI6XG4gICAgICAgIGNhc2UgMjM6XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdLnRyaW0oKTtcbiAgICAgICAgICB5eS5zZXRBY2NEZXNjcmlwdGlvbih0aGlzLiQpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDI4OlxuICAgICAgICBjYXNlIDI5OlxuICAgICAgICAgICQkWyQwXS5zcGxpY2UoMiwgMCwgXCJFTlRFUlBSSVNFXCIpO1xuICAgICAgICAgIHl5LmFkZFBlcnNvbk9yU3lzdGVtQm91bmRhcnkoLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgMzA6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW1Cb3VuZGFyeSguLi4kJFskMF0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAzMTpcbiAgICAgICAgICAkJFskMF0uc3BsaWNlKDIsIDAsIFwiQ09OVEFJTkVSXCIpO1xuICAgICAgICAgIHl5LmFkZENvbnRhaW5lckJvdW5kYXJ5KC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDMyOlxuICAgICAgICAgIHl5LmFkZERlcGxveW1lbnROb2RlKFwibm9kZVwiLCAuLi4kJFskMF0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSAzMzpcbiAgICAgICAgICB5eS5hZGREZXBsb3ltZW50Tm9kZShcIm5vZGVMXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDM0OlxuICAgICAgICAgIHl5LmFkZERlcGxveW1lbnROb2RlKFwibm9kZVJcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgMzU6XG4gICAgICAgICAgeXkucG9wQm91bmRhcnlQYXJzZVN0YWNrKCk7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgMzk6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJwZXJzb25cIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDA6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJleHRlcm5hbF9wZXJzb25cIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDE6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJzeXN0ZW1cIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDI6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJzeXN0ZW1fZGJcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDM6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJzeXN0ZW1fcXVldWVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDQ6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJleHRlcm5hbF9zeXN0ZW1cIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDU6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJleHRlcm5hbF9zeXN0ZW1fZGJcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDY6XG4gICAgICAgICAgeXkuYWRkUGVyc29uT3JTeXN0ZW0oXCJleHRlcm5hbF9zeXN0ZW1fcXVldWVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNDc6XG4gICAgICAgICAgeXkuYWRkQ29udGFpbmVyKFwiY29udGFpbmVyXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDQ4OlxuICAgICAgICAgIHl5LmFkZENvbnRhaW5lcihcImNvbnRhaW5lcl9kYlwiLCAuLi4kJFskMF0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA0OTpcbiAgICAgICAgICB5eS5hZGRDb250YWluZXIoXCJjb250YWluZXJfcXVldWVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNTA6XG4gICAgICAgICAgeXkuYWRkQ29udGFpbmVyKFwiZXh0ZXJuYWxfY29udGFpbmVyXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDUxOlxuICAgICAgICAgIHl5LmFkZENvbnRhaW5lcihcImV4dGVybmFsX2NvbnRhaW5lcl9kYlwiLCAuLi4kJFskMF0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA1MjpcbiAgICAgICAgICB5eS5hZGRDb250YWluZXIoXCJleHRlcm5hbF9jb250YWluZXJfcXVldWVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNTM6XG4gICAgICAgICAgeXkuYWRkQ29tcG9uZW50KFwiY29tcG9uZW50XCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDU0OlxuICAgICAgICAgIHl5LmFkZENvbXBvbmVudChcImNvbXBvbmVudF9kYlwiLCAuLi4kJFskMF0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA1NTpcbiAgICAgICAgICB5eS5hZGRDb21wb25lbnQoXCJjb21wb25lbnRfcXVldWVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNTY6XG4gICAgICAgICAgeXkuYWRkQ29tcG9uZW50KFwiZXh0ZXJuYWxfY29tcG9uZW50XCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDU3OlxuICAgICAgICAgIHl5LmFkZENvbXBvbmVudChcImV4dGVybmFsX2NvbXBvbmVudF9kYlwiLCAuLi4kJFskMF0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA1ODpcbiAgICAgICAgICB5eS5hZGRDb21wb25lbnQoXCJleHRlcm5hbF9jb21wb25lbnRfcXVldWVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNjA6XG4gICAgICAgICAgeXkuYWRkUmVsKFwicmVsXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDYxOlxuICAgICAgICAgIHl5LmFkZFJlbChcImJpcmVsXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDYyOlxuICAgICAgICAgIHl5LmFkZFJlbChcInJlbF91XCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDYzOlxuICAgICAgICAgIHl5LmFkZFJlbChcInJlbF9kXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDY0OlxuICAgICAgICAgIHl5LmFkZFJlbChcInJlbF9sXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDY1OlxuICAgICAgICAgIHl5LmFkZFJlbChcInJlbF9yXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDY2OlxuICAgICAgICAgIHl5LmFkZFJlbChcInJlbF9iXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDY3OlxuICAgICAgICAgICQkWyQwXS5zcGxpY2UoMCwgMSk7XG4gICAgICAgICAgeXkuYWRkUmVsKFwicmVsXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDY4OlxuICAgICAgICAgIHl5LnVwZGF0ZUVsU3R5bGUoXCJ1cGRhdGVfZWxfc3R5bGVcIiwgLi4uJCRbJDBdKTtcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF07XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNjk6XG4gICAgICAgICAgeXkudXBkYXRlUmVsU3R5bGUoXCJ1cGRhdGVfcmVsX3N0eWxlXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDcwOlxuICAgICAgICAgIHl5LnVwZGF0ZUxheW91dENvbmZpZyhcInVwZGF0ZV9sYXlvdXRfY29uZmlnXCIsIC4uLiQkWyQwXSk7XG4gICAgICAgICAgdGhpcy4kID0gJCRbJDBdO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDcxOlxuICAgICAgICAgIHRoaXMuJCA9IFskJFskMF1dO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDcyOlxuICAgICAgICAgICQkWyQwXS51bnNoaWZ0KCQkWyQwIC0gMV0pO1xuICAgICAgICAgIHRoaXMuJCA9ICQkWyQwXTtcbiAgICAgICAgICBicmVhaztcbiAgICAgICAgY2FzZSA3MzpcbiAgICAgICAgY2FzZSA3NTpcbiAgICAgICAgICB0aGlzLiQgPSAkJFskMF0udHJpbSgpO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgICBjYXNlIDc0OlxuICAgICAgICAgIGxldCBrdiA9IHt9O1xuICAgICAgICAgIGt2WyQkWyQwIC0gMV0udHJpbSgpXSA9ICQkWyQwXS50cmltKCk7XG4gICAgICAgICAgdGhpcy4kID0ga3Y7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgNzY6XG4gICAgICAgICAgdGhpcy4kID0gXCJcIjtcbiAgICAgICAgICBicmVhaztcbiAgICAgIH1cbiAgICB9LFxuICAgIHRhYmxlOiBbeyAzOiAxLCA0OiAyLCA1OiAzLCA2OiBbMSwgNV0sIDc6IFsxLCA2XSwgODogWzEsIDddLCA5OiBbMSwgOF0sIDEwOiA0LCAxMTogWzEsIDldLCAxNTogWzEsIDEwXSwgMTY6IFsxLCAxMV0sIDE3OiBbMSwgMTJdLCAxODogWzEsIDEzXSB9LCB7IDE6IFszXSB9LCB7IDE6IFsyLCAxXSB9LCB7IDE6IFsyLCAyXSB9LCB7IDE6IFsyLCA3XSB9LCB7IDE6IFsyLCAzXSB9LCB7IDE6IFsyLCA0XSB9LCB7IDE6IFsyLCA1XSB9LCB7IDE6IFsyLCA2XSB9LCB7IDEyOiBbMSwgMTRdIH0sIHsgMTI6IFsxLCAxNV0gfSwgeyAxMjogWzEsIDE2XSB9LCB7IDEyOiBbMSwgMTddIH0sIHsgMTI6IFsxLCAxOF0gfSwgeyAxMzogMTksIDE5OiAyMCwgMjA6IDIxLCAyMTogMjIsIDIyOiAkVjAsIDIzOiAkVjEsIDI0OiAkVjIsIDI2OiAkVjMsIDI4OiAkVjQsIDI5OiA0OSwgMzA6IDYxLCAzMjogNjIsIDM0OiAkVjUsIDM2OiAkVjYsIDM3OiAkVjcsIDM4OiAkVjgsIDM5OiAkVjksIDQwOiAkVmEsIDQxOiAkVmIsIDQzOiAyMywgNDQ6ICRWYywgNDU6ICRWZCwgNDY6ICRWZSwgNDc6ICRWZiwgNDg6ICRWZywgNDk6ICRWaCwgNTA6ICRWaSwgNTE6ICRWaiwgNTI6ICRWaywgNTM6ICRWbCwgNTQ6ICRWbSwgNTU6ICRWbiwgNTY6ICRWbywgNTc6ICRWcCwgNTg6ICRWcSwgNTk6ICRWciwgNjA6ICRWcywgNjE6ICRWdCwgNjI6ICRWdSwgNjM6ICRWdiwgNjQ6ICRWdywgNjU6ICRWeCwgNjY6ICRWeSwgNjc6ICRWeiwgNjg6ICRWQSwgNjk6ICRWQiwgNzA6ICRWQywgNzE6ICRWRCwgNzI6ICRWRSwgNzM6ICRWRiwgNzQ6ICRWRyB9LCB7IDEzOiA3MCwgMTk6IDIwLCAyMDogMjEsIDIxOiAyMiwgMjI6ICRWMCwgMjM6ICRWMSwgMjQ6ICRWMiwgMjY6ICRWMywgMjg6ICRWNCwgMjk6IDQ5LCAzMDogNjEsIDMyOiA2MiwgMzQ6ICRWNSwgMzY6ICRWNiwgMzc6ICRWNywgMzg6ICRWOCwgMzk6ICRWOSwgNDA6ICRWYSwgNDE6ICRWYiwgNDM6IDIzLCA0NDogJFZjLCA0NTogJFZkLCA0NjogJFZlLCA0NzogJFZmLCA0ODogJFZnLCA0OTogJFZoLCA1MDogJFZpLCA1MTogJFZqLCA1MjogJFZrLCA1MzogJFZsLCA1NDogJFZtLCA1NTogJFZuLCA1NjogJFZvLCA1NzogJFZwLCA1ODogJFZxLCA1OTogJFZyLCA2MDogJFZzLCA2MTogJFZ0LCA2MjogJFZ1LCA2MzogJFZ2LCA2NDogJFZ3LCA2NTogJFZ4LCA2NjogJFZ5LCA2NzogJFZ6LCA2ODogJFZBLCA2OTogJFZCLCA3MDogJFZDLCA3MTogJFZELCA3MjogJFZFLCA3MzogJFZGLCA3NDogJFZHIH0sIHsgMTM6IDcxLCAxOTogMjAsIDIwOiAyMSwgMjE6IDIyLCAyMjogJFYwLCAyMzogJFYxLCAyNDogJFYyLCAyNjogJFYzLCAyODogJFY0LCAyOTogNDksIDMwOiA2MSwgMzI6IDYyLCAzNDogJFY1LCAzNjogJFY2LCAzNzogJFY3LCAzODogJFY4LCAzOTogJFY5LCA0MDogJFZhLCA0MTogJFZiLCA0MzogMjMsIDQ0OiAkVmMsIDQ1OiAkVmQsIDQ2OiAkVmUsIDQ3OiAkVmYsIDQ4OiAkVmcsIDQ5OiAkVmgsIDUwOiAkVmksIDUxOiAkVmosIDUyOiAkVmssIDUzOiAkVmwsIDU0OiAkVm0sIDU1OiAkVm4sIDU2OiAkVm8sIDU3OiAkVnAsIDU4OiAkVnEsIDU5OiAkVnIsIDYwOiAkVnMsIDYxOiAkVnQsIDYyOiAkVnUsIDYzOiAkVnYsIDY0OiAkVncsIDY1OiAkVngsIDY2OiAkVnksIDY3OiAkVnosIDY4OiAkVkEsIDY5OiAkVkIsIDcwOiAkVkMsIDcxOiAkVkQsIDcyOiAkVkUsIDczOiAkVkYsIDc0OiAkVkcgfSwgeyAxMzogNzIsIDE5OiAyMCwgMjA6IDIxLCAyMTogMjIsIDIyOiAkVjAsIDIzOiAkVjEsIDI0OiAkVjIsIDI2OiAkVjMsIDI4OiAkVjQsIDI5OiA0OSwgMzA6IDYxLCAzMjogNjIsIDM0OiAkVjUsIDM2OiAkVjYsIDM3OiAkVjcsIDM4OiAkVjgsIDM5OiAkVjksIDQwOiAkVmEsIDQxOiAkVmIsIDQzOiAyMywgNDQ6ICRWYywgNDU6ICRWZCwgNDY6ICRWZSwgNDc6ICRWZiwgNDg6ICRWZywgNDk6ICRWaCwgNTA6ICRWaSwgNTE6ICRWaiwgNTI6ICRWaywgNTM6ICRWbCwgNTQ6ICRWbSwgNTU6ICRWbiwgNTY6ICRWbywgNTc6ICRWcCwgNTg6ICRWcSwgNTk6ICRWciwgNjA6ICRWcywgNjE6ICRWdCwgNjI6ICRWdSwgNjM6ICRWdiwgNjQ6ICRWdywgNjU6ICRWeCwgNjY6ICRWeSwgNjc6ICRWeiwgNjg6ICRWQSwgNjk6ICRWQiwgNzA6ICRWQywgNzE6ICRWRCwgNzI6ICRWRSwgNzM6ICRWRiwgNzQ6ICRWRyB9LCB7IDEzOiA3MywgMTk6IDIwLCAyMDogMjEsIDIxOiAyMiwgMjI6ICRWMCwgMjM6ICRWMSwgMjQ6ICRWMiwgMjY6ICRWMywgMjg6ICRWNCwgMjk6IDQ5LCAzMDogNjEsIDMyOiA2MiwgMzQ6ICRWNSwgMzY6ICRWNiwgMzc6ICRWNywgMzg6ICRWOCwgMzk6ICRWOSwgNDA6ICRWYSwgNDE6ICRWYiwgNDM6IDIzLCA0NDogJFZjLCA0NTogJFZkLCA0NjogJFZlLCA0NzogJFZmLCA0ODogJFZnLCA0OTogJFZoLCA1MDogJFZpLCA1MTogJFZqLCA1MjogJFZrLCA1MzogJFZsLCA1NDogJFZtLCA1NTogJFZuLCA1NjogJFZvLCA1NzogJFZwLCA1ODogJFZxLCA1OTogJFZyLCA2MDogJFZzLCA2MTogJFZ0LCA2MjogJFZ1LCA2MzogJFZ2LCA2NDogJFZ3LCA2NTogJFZ4LCA2NjogJFZ5LCA2NzogJFZ6LCA2ODogJFZBLCA2OTogJFZCLCA3MDogJFZDLCA3MTogJFZELCA3MjogJFZFLCA3MzogJFZGLCA3NDogJFZHIH0sIHsgMTQ6IFsxLCA3NF0gfSwgbygkVkgsIFsyLCAxM10sIHsgNDM6IDIzLCAyOTogNDksIDMwOiA2MSwgMzI6IDYyLCAyMDogNzUsIDM0OiAkVjUsIDM2OiAkVjYsIDM3OiAkVjcsIDM4OiAkVjgsIDM5OiAkVjksIDQwOiAkVmEsIDQxOiAkVmIsIDQ0OiAkVmMsIDQ1OiAkVmQsIDQ2OiAkVmUsIDQ3OiAkVmYsIDQ4OiAkVmcsIDQ5OiAkVmgsIDUwOiAkVmksIDUxOiAkVmosIDUyOiAkVmssIDUzOiAkVmwsIDU0OiAkVm0sIDU1OiAkVm4sIDU2OiAkVm8sIDU3OiAkVnAsIDU4OiAkVnEsIDU5OiAkVnIsIDYwOiAkVnMsIDYxOiAkVnQsIDYyOiAkVnUsIDYzOiAkVnYsIDY0OiAkVncsIDY1OiAkVngsIDY2OiAkVnksIDY3OiAkVnosIDY4OiAkVkEsIDY5OiAkVkIsIDcwOiAkVkMsIDcxOiAkVkQsIDcyOiAkVkUsIDczOiAkVkYsIDc0OiAkVkcgfSksIG8oJFZILCBbMiwgMTRdKSwgbygkVkksIFsyLCAxNl0sIHsgMTI6IFsxLCA3Nl0gfSksIG8oJFZILCBbMiwgMzZdLCB7IDEyOiBbMSwgNzddIH0pLCBvKCRWSiwgWzIsIDE5XSksIG8oJFZKLCBbMiwgMjBdKSwgeyAyNTogWzEsIDc4XSB9LCB7IDI3OiBbMSwgNzldIH0sIG8oJFZKLCBbMiwgMjNdKSwgeyAzNTogODAsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiA4NiwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDg3LCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogODgsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiA4OSwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDkwLCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogOTEsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiA5MiwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDkzLCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogOTQsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiA5NSwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDk2LCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogOTcsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiA5OCwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDk5LCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogMTAwLCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogMTAxLCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogMTAyLCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogMTAzLCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgeyAzNTogMTA0LCA3NTogODEsIDc2OiAkVkssIDc3OiAkVkwsIDc5OiAkVk0sIDgwOiAkVk4gfSwgbygkVk8sIFsyLCA1OV0pLCB7IDM1OiAxMDUsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMDYsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMDcsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMDgsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMDksIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMTAsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMTEsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMTIsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMTMsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMTQsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDM1OiAxMTUsIDc1OiA4MSwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9LCB7IDIwOiAxMTYsIDI5OiA0OSwgMzA6IDYxLCAzMjogNjIsIDM0OiAkVjUsIDM2OiAkVjYsIDM3OiAkVjcsIDM4OiAkVjgsIDM5OiAkVjksIDQwOiAkVmEsIDQxOiAkVmIsIDQzOiAyMywgNDQ6ICRWYywgNDU6ICRWZCwgNDY6ICRWZSwgNDc6ICRWZiwgNDg6ICRWZywgNDk6ICRWaCwgNTA6ICRWaSwgNTE6ICRWaiwgNTI6ICRWaywgNTM6ICRWbCwgNTQ6ICRWbSwgNTU6ICRWbiwgNTY6ICRWbywgNTc6ICRWcCwgNTg6ICRWcSwgNTk6ICRWciwgNjA6ICRWcywgNjE6ICRWdCwgNjI6ICRWdSwgNjM6ICRWdiwgNjQ6ICRWdywgNjU6ICRWeCwgNjY6ICRWeSwgNjc6ICRWeiwgNjg6ICRWQSwgNjk6ICRWQiwgNzA6ICRWQywgNzE6ICRWRCwgNzI6ICRWRSwgNzM6ICRWRiwgNzQ6ICRWRyB9LCB7IDEyOiBbMSwgMTE4XSwgMzM6IFsxLCAxMTddIH0sIHsgMzU6IDExOSwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDEyMCwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDEyMSwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDEyMiwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDEyMywgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDEyNCwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMzU6IDEyNSwgNzU6IDgxLCA3NjogJFZLLCA3NzogJFZMLCA3OTogJFZNLCA4MDogJFZOIH0sIHsgMTQ6IFsxLCAxMjZdIH0sIHsgMTQ6IFsxLCAxMjddIH0sIHsgMTQ6IFsxLCAxMjhdIH0sIHsgMTQ6IFsxLCAxMjldIH0sIHsgMTogWzIsIDhdIH0sIG8oJFZILCBbMiwgMTVdKSwgbygkVkksIFsyLCAxN10sIHsgMjE6IDIyLCAxOTogMTMwLCAyMjogJFYwLCAyMzogJFYxLCAyNDogJFYyLCAyNjogJFYzLCAyODogJFY0IH0pLCBvKCRWSCwgWzIsIDM3XSwgeyAxOTogMjAsIDIwOiAyMSwgMjE6IDIyLCA0MzogMjMsIDI5OiA0OSwgMzA6IDYxLCAzMjogNjIsIDEzOiAxMzEsIDIyOiAkVjAsIDIzOiAkVjEsIDI0OiAkVjIsIDI2OiAkVjMsIDI4OiAkVjQsIDM0OiAkVjUsIDM2OiAkVjYsIDM3OiAkVjcsIDM4OiAkVjgsIDM5OiAkVjksIDQwOiAkVmEsIDQxOiAkVmIsIDQ0OiAkVmMsIDQ1OiAkVmQsIDQ2OiAkVmUsIDQ3OiAkVmYsIDQ4OiAkVmcsIDQ5OiAkVmgsIDUwOiAkVmksIDUxOiAkVmosIDUyOiAkVmssIDUzOiAkVmwsIDU0OiAkVm0sIDU1OiAkVm4sIDU2OiAkVm8sIDU3OiAkVnAsIDU4OiAkVnEsIDU5OiAkVnIsIDYwOiAkVnMsIDYxOiAkVnQsIDYyOiAkVnUsIDYzOiAkVnYsIDY0OiAkVncsIDY1OiAkVngsIDY2OiAkVnksIDY3OiAkVnosIDY4OiAkVkEsIDY5OiAkVkIsIDcwOiAkVkMsIDcxOiAkVkQsIDcyOiAkVkUsIDczOiAkVkYsIDc0OiAkVkcgfSksIG8oJFZKLCBbMiwgMjFdKSwgbygkVkosIFsyLCAyMl0pLCBvKCRWTywgWzIsIDM5XSksIG8oJFZQLCBbMiwgNzFdLCB7IDc1OiA4MSwgMzU6IDEzMiwgNzY6ICRWSywgNzc6ICRWTCwgNzk6ICRWTSwgODA6ICRWTiB9KSwgbygkVlEsIFsyLCA3M10pLCB7IDc4OiBbMSwgMTMzXSB9LCBvKCRWUSwgWzIsIDc1XSksIG8oJFZRLCBbMiwgNzZdKSwgbygkVk8sIFsyLCA0MF0pLCBvKCRWTywgWzIsIDQxXSksIG8oJFZPLCBbMiwgNDJdKSwgbygkVk8sIFsyLCA0M10pLCBvKCRWTywgWzIsIDQ0XSksIG8oJFZPLCBbMiwgNDVdKSwgbygkVk8sIFsyLCA0Nl0pLCBvKCRWTywgWzIsIDQ3XSksIG8oJFZPLCBbMiwgNDhdKSwgbygkVk8sIFsyLCA0OV0pLCBvKCRWTywgWzIsIDUwXSksIG8oJFZPLCBbMiwgNTFdKSwgbygkVk8sIFsyLCA1Ml0pLCBvKCRWTywgWzIsIDUzXSksIG8oJFZPLCBbMiwgNTRdKSwgbygkVk8sIFsyLCA1NV0pLCBvKCRWTywgWzIsIDU2XSksIG8oJFZPLCBbMiwgNTddKSwgbygkVk8sIFsyLCA1OF0pLCBvKCRWTywgWzIsIDYwXSksIG8oJFZPLCBbMiwgNjFdKSwgbygkVk8sIFsyLCA2Ml0pLCBvKCRWTywgWzIsIDYzXSksIG8oJFZPLCBbMiwgNjRdKSwgbygkVk8sIFsyLCA2NV0pLCBvKCRWTywgWzIsIDY2XSksIG8oJFZPLCBbMiwgNjddKSwgbygkVk8sIFsyLCA2OF0pLCBvKCRWTywgWzIsIDY5XSksIG8oJFZPLCBbMiwgNzBdKSwgeyAzMTogMTM0LCA0MjogWzEsIDEzNV0gfSwgeyAxMjogWzEsIDEzNl0gfSwgeyAzMzogWzEsIDEzN10gfSwgbygkVlIsIFsyLCAyOF0pLCBvKCRWUiwgWzIsIDI5XSksIG8oJFZSLCBbMiwgMzBdKSwgbygkVlIsIFsyLCAzMV0pLCBvKCRWUiwgWzIsIDMyXSksIG8oJFZSLCBbMiwgMzNdKSwgbygkVlIsIFsyLCAzNF0pLCB7IDE6IFsyLCA5XSB9LCB7IDE6IFsyLCAxMF0gfSwgeyAxOiBbMiwgMTFdIH0sIHsgMTogWzIsIDEyXSB9LCBvKCRWSSwgWzIsIDE4XSksIG8oJFZILCBbMiwgMzhdKSwgbygkVlAsIFsyLCA3Ml0pLCBvKCRWUSwgWzIsIDc0XSksIG8oJFZPLCBbMiwgMjRdKSwgbygkVk8sIFsyLCAzNV0pLCBvKCRWUywgWzIsIDI1XSksIG8oJFZTLCBbMiwgMjZdLCB7IDEyOiBbMSwgMTM4XSB9KSwgbygkVlMsIFsyLCAyN10pXSxcbiAgICBkZWZhdWx0QWN0aW9uczogeyAyOiBbMiwgMV0sIDM6IFsyLCAyXSwgNDogWzIsIDddLCA1OiBbMiwgM10sIDY6IFsyLCA0XSwgNzogWzIsIDVdLCA4OiBbMiwgNl0sIDc0OiBbMiwgOF0sIDEyNjogWzIsIDldLCAxMjc6IFsyLCAxMF0sIDEyODogWzIsIDExXSwgMTI5OiBbMiwgMTJdIH0sXG4gICAgcGFyc2VFcnJvcjogZnVuY3Rpb24gcGFyc2VFcnJvcihzdHIsIGhhc2gpIHtcbiAgICAgIGlmIChoYXNoLnJlY292ZXJhYmxlKSB7XG4gICAgICAgIHRoaXMudHJhY2Uoc3RyKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHZhciBlcnJvciA9IG5ldyBFcnJvcihzdHIpO1xuICAgICAgICBlcnJvci5oYXNoID0gaGFzaDtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG4gICAgfSxcbiAgICBwYXJzZTogZnVuY3Rpb24gcGFyc2UoaW5wdXQpIHtcbiAgICAgIHZhciBzZWxmID0gdGhpcywgc3RhY2sgPSBbMF0sIHRzdGFjayA9IFtdLCB2c3RhY2sgPSBbbnVsbF0sIGxzdGFjayA9IFtdLCB0YWJsZSA9IHRoaXMudGFibGUsIHl5dGV4dCA9IFwiXCIsIHl5bGluZW5vID0gMCwgeXlsZW5nID0gMCwgVEVSUk9SID0gMiwgRU9GID0gMTtcbiAgICAgIHZhciBhcmdzID0gbHN0YWNrLnNsaWNlLmNhbGwoYXJndW1lbnRzLCAxKTtcbiAgICAgIHZhciBsZXhlcjIgPSBPYmplY3QuY3JlYXRlKHRoaXMubGV4ZXIpO1xuICAgICAgdmFyIHNoYXJlZFN0YXRlID0geyB5eToge30gfTtcbiAgICAgIGZvciAodmFyIGsgaW4gdGhpcy55eSkge1xuICAgICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHRoaXMueXksIGspKSB7XG4gICAgICAgICAgc2hhcmVkU3RhdGUueXlba10gPSB0aGlzLnl5W2tdO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBsZXhlcjIuc2V0SW5wdXQoaW5wdXQsIHNoYXJlZFN0YXRlLnl5KTtcbiAgICAgIHNoYXJlZFN0YXRlLnl5LmxleGVyID0gbGV4ZXIyO1xuICAgICAgc2hhcmVkU3RhdGUueXkucGFyc2VyID0gdGhpcztcbiAgICAgIGlmICh0eXBlb2YgbGV4ZXIyLnl5bGxvYyA9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIGxleGVyMi55eWxsb2MgPSB7fTtcbiAgICAgIH1cbiAgICAgIHZhciB5eWxvYyA9IGxleGVyMi55eWxsb2M7XG4gICAgICBsc3RhY2sucHVzaCh5eWxvYyk7XG4gICAgICB2YXIgcmFuZ2VzID0gbGV4ZXIyLm9wdGlvbnMgJiYgbGV4ZXIyLm9wdGlvbnMucmFuZ2VzO1xuICAgICAgaWYgKHR5cGVvZiBzaGFyZWRTdGF0ZS55eS5wYXJzZUVycm9yID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgdGhpcy5wYXJzZUVycm9yID0gc2hhcmVkU3RhdGUueXkucGFyc2VFcnJvcjtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMucGFyc2VFcnJvciA9IE9iamVjdC5nZXRQcm90b3R5cGVPZih0aGlzKS5wYXJzZUVycm9yO1xuICAgICAgfVxuICAgICAgZnVuY3Rpb24gbGV4KCkge1xuICAgICAgICB2YXIgdG9rZW47XG4gICAgICAgIHRva2VuID0gdHN0YWNrLnBvcCgpIHx8IGxleGVyMi5sZXgoKSB8fCBFT0Y7XG4gICAgICAgIGlmICh0eXBlb2YgdG9rZW4gIT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICBpZiAodG9rZW4gaW5zdGFuY2VvZiBBcnJheSkge1xuICAgICAgICAgICAgdHN0YWNrID0gdG9rZW47XG4gICAgICAgICAgICB0b2tlbiA9IHRzdGFjay5wb3AoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgdG9rZW4gPSBzZWxmLnN5bWJvbHNfW3Rva2VuXSB8fCB0b2tlbjtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdG9rZW47XG4gICAgICB9XG4gICAgICB2YXIgc3ltYm9sLCBzdGF0ZSwgYWN0aW9uLCByLCB5eXZhbCA9IHt9LCBwLCBsZW4sIG5ld1N0YXRlLCBleHBlY3RlZDtcbiAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgIHN0YXRlID0gc3RhY2tbc3RhY2subGVuZ3RoIC0gMV07XG4gICAgICAgIGlmICh0aGlzLmRlZmF1bHRBY3Rpb25zW3N0YXRlXSkge1xuICAgICAgICAgIGFjdGlvbiA9IHRoaXMuZGVmYXVsdEFjdGlvbnNbc3RhdGVdO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGlmIChzeW1ib2wgPT09IG51bGwgfHwgdHlwZW9mIHN5bWJvbCA9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICBzeW1ib2wgPSBsZXgoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgYWN0aW9uID0gdGFibGVbc3RhdGVdICYmIHRhYmxlW3N0YXRlXVtzeW1ib2xdO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgYWN0aW9uID09PSBcInVuZGVmaW5lZFwiIHx8ICFhY3Rpb24ubGVuZ3RoIHx8ICFhY3Rpb25bMF0pIHtcbiAgICAgICAgICB2YXIgZXJyU3RyID0gXCJcIjtcbiAgICAgICAgICBleHBlY3RlZCA9IFtdO1xuICAgICAgICAgIGZvciAocCBpbiB0YWJsZVtzdGF0ZV0pIHtcbiAgICAgICAgICAgIGlmICh0aGlzLnRlcm1pbmFsc19bcF0gJiYgcCA+IFRFUlJPUikge1xuICAgICAgICAgICAgICBleHBlY3RlZC5wdXNoKFwiJ1wiICsgdGhpcy50ZXJtaW5hbHNfW3BdICsgXCInXCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAobGV4ZXIyLnNob3dQb3NpdGlvbikge1xuICAgICAgICAgICAgZXJyU3RyID0gXCJQYXJzZSBlcnJvciBvbiBsaW5lIFwiICsgKHl5bGluZW5vICsgMSkgKyBcIjpcXG5cIiArIGxleGVyMi5zaG93UG9zaXRpb24oKSArIFwiXFxuRXhwZWN0aW5nIFwiICsgZXhwZWN0ZWQuam9pbihcIiwgXCIpICsgXCIsIGdvdCAnXCIgKyAodGhpcy50ZXJtaW5hbHNfW3N5bWJvbF0gfHwgc3ltYm9sKSArIFwiJ1wiO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBlcnJTdHIgPSBcIlBhcnNlIGVycm9yIG9uIGxpbmUgXCIgKyAoeXlsaW5lbm8gKyAxKSArIFwiOiBVbmV4cGVjdGVkIFwiICsgKHN5bWJvbCA9PSBFT0YgPyBcImVuZCBvZiBpbnB1dFwiIDogXCInXCIgKyAodGhpcy50ZXJtaW5hbHNfW3N5bWJvbF0gfHwgc3ltYm9sKSArIFwiJ1wiKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgdGhpcy5wYXJzZUVycm9yKGVyclN0ciwge1xuICAgICAgICAgICAgdGV4dDogbGV4ZXIyLm1hdGNoLFxuICAgICAgICAgICAgdG9rZW46IHRoaXMudGVybWluYWxzX1tzeW1ib2xdIHx8IHN5bWJvbCxcbiAgICAgICAgICAgIGxpbmU6IGxleGVyMi55eWxpbmVubyxcbiAgICAgICAgICAgIGxvYzogeXlsb2MsXG4gICAgICAgICAgICBleHBlY3RlZFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGlmIChhY3Rpb25bMF0gaW5zdGFuY2VvZiBBcnJheSAmJiBhY3Rpb24ubGVuZ3RoID4gMSkge1xuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlBhcnNlIEVycm9yOiBtdWx0aXBsZSBhY3Rpb25zIHBvc3NpYmxlIGF0IHN0YXRlOiBcIiArIHN0YXRlICsgXCIsIHRva2VuOiBcIiArIHN5bWJvbCk7XG4gICAgICAgIH1cbiAgICAgICAgc3dpdGNoIChhY3Rpb25bMF0pIHtcbiAgICAgICAgICBjYXNlIDE6XG4gICAgICAgICAgICBzdGFjay5wdXNoKHN5bWJvbCk7XG4gICAgICAgICAgICB2c3RhY2sucHVzaChsZXhlcjIueXl0ZXh0KTtcbiAgICAgICAgICAgIGxzdGFjay5wdXNoKGxleGVyMi55eWxsb2MpO1xuICAgICAgICAgICAgc3RhY2sucHVzaChhY3Rpb25bMV0pO1xuICAgICAgICAgICAgc3ltYm9sID0gbnVsbDtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgeXlsZW5nID0gbGV4ZXIyLnl5bGVuZztcbiAgICAgICAgICAgICAgeXl0ZXh0ID0gbGV4ZXIyLnl5dGV4dDtcbiAgICAgICAgICAgICAgeXlsaW5lbm8gPSBsZXhlcjIueXlsaW5lbm87XG4gICAgICAgICAgICAgIHl5bG9jID0gbGV4ZXIyLnl5bGxvYztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgIGxlbiA9IHRoaXMucHJvZHVjdGlvbnNfW2FjdGlvblsxXV1bMV07XG4gICAgICAgICAgICB5eXZhbC4kID0gdnN0YWNrW3ZzdGFjay5sZW5ndGggLSBsZW5dO1xuICAgICAgICAgICAgeXl2YWwuXyQgPSB7XG4gICAgICAgICAgICAgIGZpcnN0X2xpbmU6IGxzdGFja1tsc3RhY2subGVuZ3RoIC0gKGxlbiB8fCAxKV0uZmlyc3RfbGluZSxcbiAgICAgICAgICAgICAgbGFzdF9saW5lOiBsc3RhY2tbbHN0YWNrLmxlbmd0aCAtIDFdLmxhc3RfbGluZSxcbiAgICAgICAgICAgICAgZmlyc3RfY29sdW1uOiBsc3RhY2tbbHN0YWNrLmxlbmd0aCAtIChsZW4gfHwgMSldLmZpcnN0X2NvbHVtbixcbiAgICAgICAgICAgICAgbGFzdF9jb2x1bW46IGxzdGFja1tsc3RhY2subGVuZ3RoIC0gMV0ubGFzdF9jb2x1bW5cbiAgICAgICAgICAgIH07XG4gICAgICAgICAgICBpZiAocmFuZ2VzKSB7XG4gICAgICAgICAgICAgIHl5dmFsLl8kLnJhbmdlID0gW1xuICAgICAgICAgICAgICAgIGxzdGFja1tsc3RhY2subGVuZ3RoIC0gKGxlbiB8fCAxKV0ucmFuZ2VbMF0sXG4gICAgICAgICAgICAgICAgbHN0YWNrW2xzdGFjay5sZW5ndGggLSAxXS5yYW5nZVsxXVxuICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgciA9IHRoaXMucGVyZm9ybUFjdGlvbi5hcHBseSh5eXZhbCwgW1xuICAgICAgICAgICAgICB5eXRleHQsXG4gICAgICAgICAgICAgIHl5bGVuZyxcbiAgICAgICAgICAgICAgeXlsaW5lbm8sXG4gICAgICAgICAgICAgIHNoYXJlZFN0YXRlLnl5LFxuICAgICAgICAgICAgICBhY3Rpb25bMV0sXG4gICAgICAgICAgICAgIHZzdGFjayxcbiAgICAgICAgICAgICAgbHN0YWNrXG4gICAgICAgICAgICBdLmNvbmNhdChhcmdzKSk7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHIgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIHI7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobGVuKSB7XG4gICAgICAgICAgICAgIHN0YWNrID0gc3RhY2suc2xpY2UoMCwgLTEgKiBsZW4gKiAyKTtcbiAgICAgICAgICAgICAgdnN0YWNrID0gdnN0YWNrLnNsaWNlKDAsIC0xICogbGVuKTtcbiAgICAgICAgICAgICAgbHN0YWNrID0gbHN0YWNrLnNsaWNlKDAsIC0xICogbGVuKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHN0YWNrLnB1c2godGhpcy5wcm9kdWN0aW9uc19bYWN0aW9uWzFdXVswXSk7XG4gICAgICAgICAgICB2c3RhY2sucHVzaCh5eXZhbC4kKTtcbiAgICAgICAgICAgIGxzdGFjay5wdXNoKHl5dmFsLl8kKTtcbiAgICAgICAgICAgIG5ld1N0YXRlID0gdGFibGVbc3RhY2tbc3RhY2subGVuZ3RoIC0gMl1dW3N0YWNrW3N0YWNrLmxlbmd0aCAtIDFdXTtcbiAgICAgICAgICAgIHN0YWNrLnB1c2gobmV3U3RhdGUpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfTtcbiAgdmFyIGxleGVyID0gZnVuY3Rpb24oKSB7XG4gICAgdmFyIGxleGVyMiA9IHtcbiAgICAgIEVPRjogMSxcbiAgICAgIHBhcnNlRXJyb3I6IGZ1bmN0aW9uIHBhcnNlRXJyb3Ioc3RyLCBoYXNoKSB7XG4gICAgICAgIGlmICh0aGlzLnl5LnBhcnNlcikge1xuICAgICAgICAgIHRoaXMueXkucGFyc2VyLnBhcnNlRXJyb3Ioc3RyLCBoYXNoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3Ioc3RyKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIC8vIHJlc2V0cyB0aGUgbGV4ZXIsIHNldHMgbmV3IGlucHV0XG4gICAgICBzZXRJbnB1dDogZnVuY3Rpb24oaW5wdXQsIHl5KSB7XG4gICAgICAgIHRoaXMueXkgPSB5eSB8fCB0aGlzLnl5IHx8IHt9O1xuICAgICAgICB0aGlzLl9pbnB1dCA9IGlucHV0O1xuICAgICAgICB0aGlzLl9tb3JlID0gdGhpcy5fYmFja3RyYWNrID0gdGhpcy5kb25lID0gZmFsc2U7XG4gICAgICAgIHRoaXMueXlsaW5lbm8gPSB0aGlzLnl5bGVuZyA9IDA7XG4gICAgICAgIHRoaXMueXl0ZXh0ID0gdGhpcy5tYXRjaGVkID0gdGhpcy5tYXRjaCA9IFwiXCI7XG4gICAgICAgIHRoaXMuY29uZGl0aW9uU3RhY2sgPSBbXCJJTklUSUFMXCJdO1xuICAgICAgICB0aGlzLnl5bGxvYyA9IHtcbiAgICAgICAgICBmaXJzdF9saW5lOiAxLFxuICAgICAgICAgIGZpcnN0X2NvbHVtbjogMCxcbiAgICAgICAgICBsYXN0X2xpbmU6IDEsXG4gICAgICAgICAgbGFzdF9jb2x1bW46IDBcbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5yYW5nZXMpIHtcbiAgICAgICAgICB0aGlzLnl5bGxvYy5yYW5nZSA9IFswLCAwXTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLm9mZnNldCA9IDA7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgfSxcbiAgICAgIC8vIGNvbnN1bWVzIGFuZCByZXR1cm5zIG9uZSBjaGFyIGZyb20gdGhlIGlucHV0XG4gICAgICBpbnB1dDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHZhciBjaCA9IHRoaXMuX2lucHV0WzBdO1xuICAgICAgICB0aGlzLnl5dGV4dCArPSBjaDtcbiAgICAgICAgdGhpcy55eWxlbmcrKztcbiAgICAgICAgdGhpcy5vZmZzZXQrKztcbiAgICAgICAgdGhpcy5tYXRjaCArPSBjaDtcbiAgICAgICAgdGhpcy5tYXRjaGVkICs9IGNoO1xuICAgICAgICB2YXIgbGluZXMgPSBjaC5tYXRjaCgvKD86XFxyXFxuP3xcXG4pLiovZyk7XG4gICAgICAgIGlmIChsaW5lcykge1xuICAgICAgICAgIHRoaXMueXlsaW5lbm8rKztcbiAgICAgICAgICB0aGlzLnl5bGxvYy5sYXN0X2xpbmUrKztcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0aGlzLnl5bGxvYy5sYXN0X2NvbHVtbisrO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMucmFuZ2VzKSB7XG4gICAgICAgICAgdGhpcy55eWxsb2MucmFuZ2VbMV0rKztcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9pbnB1dCA9IHRoaXMuX2lucHV0LnNsaWNlKDEpO1xuICAgICAgICByZXR1cm4gY2g7XG4gICAgICB9LFxuICAgICAgLy8gdW5zaGlmdHMgb25lIGNoYXIgKG9yIGEgc3RyaW5nKSBpbnRvIHRoZSBpbnB1dFxuICAgICAgdW5wdXQ6IGZ1bmN0aW9uKGNoKSB7XG4gICAgICAgIHZhciBsZW4gPSBjaC5sZW5ndGg7XG4gICAgICAgIHZhciBsaW5lcyA9IGNoLnNwbGl0KC8oPzpcXHJcXG4/fFxcbikvZyk7XG4gICAgICAgIHRoaXMuX2lucHV0ID0gY2ggKyB0aGlzLl9pbnB1dDtcbiAgICAgICAgdGhpcy55eXRleHQgPSB0aGlzLnl5dGV4dC5zdWJzdHIoMCwgdGhpcy55eXRleHQubGVuZ3RoIC0gbGVuKTtcbiAgICAgICAgdGhpcy5vZmZzZXQgLT0gbGVuO1xuICAgICAgICB2YXIgb2xkTGluZXMgPSB0aGlzLm1hdGNoLnNwbGl0KC8oPzpcXHJcXG4/fFxcbikvZyk7XG4gICAgICAgIHRoaXMubWF0Y2ggPSB0aGlzLm1hdGNoLnN1YnN0cigwLCB0aGlzLm1hdGNoLmxlbmd0aCAtIDEpO1xuICAgICAgICB0aGlzLm1hdGNoZWQgPSB0aGlzLm1hdGNoZWQuc3Vic3RyKDAsIHRoaXMubWF0Y2hlZC5sZW5ndGggLSAxKTtcbiAgICAgICAgaWYgKGxpbmVzLmxlbmd0aCAtIDEpIHtcbiAgICAgICAgICB0aGlzLnl5bGluZW5vIC09IGxpbmVzLmxlbmd0aCAtIDE7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIHIgPSB0aGlzLnl5bGxvYy5yYW5nZTtcbiAgICAgICAgdGhpcy55eWxsb2MgPSB7XG4gICAgICAgICAgZmlyc3RfbGluZTogdGhpcy55eWxsb2MuZmlyc3RfbGluZSxcbiAgICAgICAgICBsYXN0X2xpbmU6IHRoaXMueXlsaW5lbm8gKyAxLFxuICAgICAgICAgIGZpcnN0X2NvbHVtbjogdGhpcy55eWxsb2MuZmlyc3RfY29sdW1uLFxuICAgICAgICAgIGxhc3RfY29sdW1uOiBsaW5lcyA/IChsaW5lcy5sZW5ndGggPT09IG9sZExpbmVzLmxlbmd0aCA/IHRoaXMueXlsbG9jLmZpcnN0X2NvbHVtbiA6IDApICsgb2xkTGluZXNbb2xkTGluZXMubGVuZ3RoIC0gbGluZXMubGVuZ3RoXS5sZW5ndGggLSBsaW5lc1swXS5sZW5ndGggOiB0aGlzLnl5bGxvYy5maXJzdF9jb2x1bW4gLSBsZW5cbiAgICAgICAgfTtcbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5yYW5nZXMpIHtcbiAgICAgICAgICB0aGlzLnl5bGxvYy5yYW5nZSA9IFtyWzBdLCByWzBdICsgdGhpcy55eWxlbmcgLSBsZW5dO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMueXlsZW5nID0gdGhpcy55eXRleHQubGVuZ3RoO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH0sXG4gICAgICAvLyBXaGVuIGNhbGxlZCBmcm9tIGFjdGlvbiwgY2FjaGVzIG1hdGNoZWQgdGV4dCBhbmQgYXBwZW5kcyBpdCBvbiBuZXh0IGFjdGlvblxuICAgICAgbW9yZTogZnVuY3Rpb24oKSB7XG4gICAgICAgIHRoaXMuX21vcmUgPSB0cnVlO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH0sXG4gICAgICAvLyBXaGVuIGNhbGxlZCBmcm9tIGFjdGlvbiwgc2lnbmFscyB0aGUgbGV4ZXIgdGhhdCB0aGlzIHJ1bGUgZmFpbHMgdG8gbWF0Y2ggdGhlIGlucHV0LCBzbyB0aGUgbmV4dCBtYXRjaGluZyBydWxlIChyZWdleCkgc2hvdWxkIGJlIHRlc3RlZCBpbnN0ZWFkLlxuICAgICAgcmVqZWN0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5iYWNrdHJhY2tfbGV4ZXIpIHtcbiAgICAgICAgICB0aGlzLl9iYWNrdHJhY2sgPSB0cnVlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiB0aGlzLnBhcnNlRXJyb3IoXCJMZXhpY2FsIGVycm9yIG9uIGxpbmUgXCIgKyAodGhpcy55eWxpbmVubyArIDEpICsgXCIuIFlvdSBjYW4gb25seSBpbnZva2UgcmVqZWN0KCkgaW4gdGhlIGxleGVyIHdoZW4gdGhlIGxleGVyIGlzIG9mIHRoZSBiYWNrdHJhY2tpbmcgcGVyc3Vhc2lvbiAob3B0aW9ucy5iYWNrdHJhY2tfbGV4ZXIgPSB0cnVlKS5cXG5cIiArIHRoaXMuc2hvd1Bvc2l0aW9uKCksIHtcbiAgICAgICAgICAgIHRleHQ6IFwiXCIsXG4gICAgICAgICAgICB0b2tlbjogbnVsbCxcbiAgICAgICAgICAgIGxpbmU6IHRoaXMueXlsaW5lbm9cbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcztcbiAgICAgIH0sXG4gICAgICAvLyByZXRhaW4gZmlyc3QgbiBjaGFyYWN0ZXJzIG9mIHRoZSBtYXRjaFxuICAgICAgbGVzczogZnVuY3Rpb24obikge1xuICAgICAgICB0aGlzLnVucHV0KHRoaXMubWF0Y2guc2xpY2UobikpO1xuICAgICAgfSxcbiAgICAgIC8vIGRpc3BsYXlzIGFscmVhZHkgbWF0Y2hlZCBpbnB1dCwgaS5lLiBmb3IgZXJyb3IgbWVzc2FnZXNcbiAgICAgIHBhc3RJbnB1dDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHZhciBwYXN0ID0gdGhpcy5tYXRjaGVkLnN1YnN0cigwLCB0aGlzLm1hdGNoZWQubGVuZ3RoIC0gdGhpcy5tYXRjaC5sZW5ndGgpO1xuICAgICAgICByZXR1cm4gKHBhc3QubGVuZ3RoID4gMjAgPyBcIi4uLlwiIDogXCJcIikgKyBwYXN0LnN1YnN0cigtMjApLnJlcGxhY2UoL1xcbi9nLCBcIlwiKTtcbiAgICAgIH0sXG4gICAgICAvLyBkaXNwbGF5cyB1cGNvbWluZyBpbnB1dCwgaS5lLiBmb3IgZXJyb3IgbWVzc2FnZXNcbiAgICAgIHVwY29taW5nSW5wdXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgbmV4dCA9IHRoaXMubWF0Y2g7XG4gICAgICAgIGlmIChuZXh0Lmxlbmd0aCA8IDIwKSB7XG4gICAgICAgICAgbmV4dCArPSB0aGlzLl9pbnB1dC5zdWJzdHIoMCwgMjAgLSBuZXh0Lmxlbmd0aCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIChuZXh0LnN1YnN0cigwLCAyMCkgKyAobmV4dC5sZW5ndGggPiAyMCA/IFwiLi4uXCIgOiBcIlwiKSkucmVwbGFjZSgvXFxuL2csIFwiXCIpO1xuICAgICAgfSxcbiAgICAgIC8vIGRpc3BsYXlzIHRoZSBjaGFyYWN0ZXIgcG9zaXRpb24gd2hlcmUgdGhlIGxleGluZyBlcnJvciBvY2N1cnJlZCwgaS5lLiBmb3IgZXJyb3IgbWVzc2FnZXNcbiAgICAgIHNob3dQb3NpdGlvbjogZnVuY3Rpb24oKSB7XG4gICAgICAgIHZhciBwcmUgPSB0aGlzLnBhc3RJbnB1dCgpO1xuICAgICAgICB2YXIgYzIgPSBuZXcgQXJyYXkocHJlLmxlbmd0aCArIDEpLmpvaW4oXCItXCIpO1xuICAgICAgICByZXR1cm4gcHJlICsgdGhpcy51cGNvbWluZ0lucHV0KCkgKyBcIlxcblwiICsgYzIgKyBcIl5cIjtcbiAgICAgIH0sXG4gICAgICAvLyB0ZXN0IHRoZSBsZXhlZCB0b2tlbjogcmV0dXJuIEZBTFNFIHdoZW4gbm90IGEgbWF0Y2gsIG90aGVyd2lzZSByZXR1cm4gdG9rZW5cbiAgICAgIHRlc3RfbWF0Y2g6IGZ1bmN0aW9uKG1hdGNoLCBpbmRleGVkX3J1bGUpIHtcbiAgICAgICAgdmFyIHRva2VuLCBsaW5lcywgYmFja3VwO1xuICAgICAgICBpZiAodGhpcy5vcHRpb25zLmJhY2t0cmFja19sZXhlcikge1xuICAgICAgICAgIGJhY2t1cCA9IHtcbiAgICAgICAgICAgIHl5bGluZW5vOiB0aGlzLnl5bGluZW5vLFxuICAgICAgICAgICAgeXlsbG9jOiB7XG4gICAgICAgICAgICAgIGZpcnN0X2xpbmU6IHRoaXMueXlsbG9jLmZpcnN0X2xpbmUsXG4gICAgICAgICAgICAgIGxhc3RfbGluZTogdGhpcy5sYXN0X2xpbmUsXG4gICAgICAgICAgICAgIGZpcnN0X2NvbHVtbjogdGhpcy55eWxsb2MuZmlyc3RfY29sdW1uLFxuICAgICAgICAgICAgICBsYXN0X2NvbHVtbjogdGhpcy55eWxsb2MubGFzdF9jb2x1bW5cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB5eXRleHQ6IHRoaXMueXl0ZXh0LFxuICAgICAgICAgICAgbWF0Y2g6IHRoaXMubWF0Y2gsXG4gICAgICAgICAgICBtYXRjaGVzOiB0aGlzLm1hdGNoZXMsXG4gICAgICAgICAgICBtYXRjaGVkOiB0aGlzLm1hdGNoZWQsXG4gICAgICAgICAgICB5eWxlbmc6IHRoaXMueXlsZW5nLFxuICAgICAgICAgICAgb2Zmc2V0OiB0aGlzLm9mZnNldCxcbiAgICAgICAgICAgIF9tb3JlOiB0aGlzLl9tb3JlLFxuICAgICAgICAgICAgX2lucHV0OiB0aGlzLl9pbnB1dCxcbiAgICAgICAgICAgIHl5OiB0aGlzLnl5LFxuICAgICAgICAgICAgY29uZGl0aW9uU3RhY2s6IHRoaXMuY29uZGl0aW9uU3RhY2suc2xpY2UoMCksXG4gICAgICAgICAgICBkb25lOiB0aGlzLmRvbmVcbiAgICAgICAgICB9O1xuICAgICAgICAgIGlmICh0aGlzLm9wdGlvbnMucmFuZ2VzKSB7XG4gICAgICAgICAgICBiYWNrdXAueXlsbG9jLnJhbmdlID0gdGhpcy55eWxsb2MucmFuZ2Uuc2xpY2UoMCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGxpbmVzID0gbWF0Y2hbMF0ubWF0Y2goLyg/Olxcclxcbj98XFxuKS4qL2cpO1xuICAgICAgICBpZiAobGluZXMpIHtcbiAgICAgICAgICB0aGlzLnl5bGluZW5vICs9IGxpbmVzLmxlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnl5bGxvYyA9IHtcbiAgICAgICAgICBmaXJzdF9saW5lOiB0aGlzLnl5bGxvYy5sYXN0X2xpbmUsXG4gICAgICAgICAgbGFzdF9saW5lOiB0aGlzLnl5bGluZW5vICsgMSxcbiAgICAgICAgICBmaXJzdF9jb2x1bW46IHRoaXMueXlsbG9jLmxhc3RfY29sdW1uLFxuICAgICAgICAgIGxhc3RfY29sdW1uOiBsaW5lcyA/IGxpbmVzW2xpbmVzLmxlbmd0aCAtIDFdLmxlbmd0aCAtIGxpbmVzW2xpbmVzLmxlbmd0aCAtIDFdLm1hdGNoKC9cXHI/XFxuPy8pWzBdLmxlbmd0aCA6IHRoaXMueXlsbG9jLmxhc3RfY29sdW1uICsgbWF0Y2hbMF0ubGVuZ3RoXG4gICAgICAgIH07XG4gICAgICAgIHRoaXMueXl0ZXh0ICs9IG1hdGNoWzBdO1xuICAgICAgICB0aGlzLm1hdGNoICs9IG1hdGNoWzBdO1xuICAgICAgICB0aGlzLm1hdGNoZXMgPSBtYXRjaDtcbiAgICAgICAgdGhpcy55eWxlbmcgPSB0aGlzLnl5dGV4dC5sZW5ndGg7XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMucmFuZ2VzKSB7XG4gICAgICAgICAgdGhpcy55eWxsb2MucmFuZ2UgPSBbdGhpcy5vZmZzZXQsIHRoaXMub2Zmc2V0ICs9IHRoaXMueXlsZW5nXTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9tb3JlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuX2JhY2t0cmFjayA9IGZhbHNlO1xuICAgICAgICB0aGlzLl9pbnB1dCA9IHRoaXMuX2lucHV0LnNsaWNlKG1hdGNoWzBdLmxlbmd0aCk7XG4gICAgICAgIHRoaXMubWF0Y2hlZCArPSBtYXRjaFswXTtcbiAgICAgICAgdG9rZW4gPSB0aGlzLnBlcmZvcm1BY3Rpb24uY2FsbCh0aGlzLCB0aGlzLnl5LCB0aGlzLCBpbmRleGVkX3J1bGUsIHRoaXMuY29uZGl0aW9uU3RhY2tbdGhpcy5jb25kaXRpb25TdGFjay5sZW5ndGggLSAxXSk7XG4gICAgICAgIGlmICh0aGlzLmRvbmUgJiYgdGhpcy5faW5wdXQpIHtcbiAgICAgICAgICB0aGlzLmRvbmUgPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodG9rZW4pIHtcbiAgICAgICAgICByZXR1cm4gdG9rZW47XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5fYmFja3RyYWNrKSB7XG4gICAgICAgICAgZm9yICh2YXIgayBpbiBiYWNrdXApIHtcbiAgICAgICAgICAgIHRoaXNba10gPSBiYWNrdXBba107XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9LFxuICAgICAgLy8gcmV0dXJuIG5leHQgbWF0Y2ggaW4gaW5wdXRcbiAgICAgIG5leHQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICBpZiAodGhpcy5kb25lKSB7XG4gICAgICAgICAgcmV0dXJuIHRoaXMuRU9GO1xuICAgICAgICB9XG4gICAgICAgIGlmICghdGhpcy5faW5wdXQpIHtcbiAgICAgICAgICB0aGlzLmRvbmUgPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciB0b2tlbiwgbWF0Y2gsIHRlbXBNYXRjaCwgaW5kZXg7XG4gICAgICAgIGlmICghdGhpcy5fbW9yZSkge1xuICAgICAgICAgIHRoaXMueXl0ZXh0ID0gXCJcIjtcbiAgICAgICAgICB0aGlzLm1hdGNoID0gXCJcIjtcbiAgICAgICAgfVxuICAgICAgICB2YXIgcnVsZXMgPSB0aGlzLl9jdXJyZW50UnVsZXMoKTtcbiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBydWxlcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgIHRlbXBNYXRjaCA9IHRoaXMuX2lucHV0Lm1hdGNoKHRoaXMucnVsZXNbcnVsZXNbaV1dKTtcbiAgICAgICAgICBpZiAodGVtcE1hdGNoICYmICghbWF0Y2ggfHwgdGVtcE1hdGNoWzBdLmxlbmd0aCA+IG1hdGNoWzBdLmxlbmd0aCkpIHtcbiAgICAgICAgICAgIG1hdGNoID0gdGVtcE1hdGNoO1xuICAgICAgICAgICAgaW5kZXggPSBpO1xuICAgICAgICAgICAgaWYgKHRoaXMub3B0aW9ucy5iYWNrdHJhY2tfbGV4ZXIpIHtcbiAgICAgICAgICAgICAgdG9rZW4gPSB0aGlzLnRlc3RfbWF0Y2godGVtcE1hdGNoLCBydWxlc1tpXSk7XG4gICAgICAgICAgICAgIGlmICh0b2tlbiAhPT0gZmFsc2UpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gdG9rZW47XG4gICAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5fYmFja3RyYWNrKSB7XG4gICAgICAgICAgICAgICAgbWF0Y2ggPSBmYWxzZTtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIXRoaXMub3B0aW9ucy5mbGV4KSB7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAobWF0Y2gpIHtcbiAgICAgICAgICB0b2tlbiA9IHRoaXMudGVzdF9tYXRjaChtYXRjaCwgcnVsZXNbaW5kZXhdKTtcbiAgICAgICAgICBpZiAodG9rZW4gIT09IGZhbHNlKSB7XG4gICAgICAgICAgICByZXR1cm4gdG9rZW47XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5faW5wdXQgPT09IFwiXCIpIHtcbiAgICAgICAgICByZXR1cm4gdGhpcy5FT0Y7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VFcnJvcihcIkxleGljYWwgZXJyb3Igb24gbGluZSBcIiArICh0aGlzLnl5bGluZW5vICsgMSkgKyBcIi4gVW5yZWNvZ25pemVkIHRleHQuXFxuXCIgKyB0aGlzLnNob3dQb3NpdGlvbigpLCB7XG4gICAgICAgICAgICB0ZXh0OiBcIlwiLFxuICAgICAgICAgICAgdG9rZW46IG51bGwsXG4gICAgICAgICAgICBsaW5lOiB0aGlzLnl5bGluZW5vXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAvLyByZXR1cm4gbmV4dCBtYXRjaCB0aGF0IGhhcyBhIHRva2VuXG4gICAgICBsZXg6IGZ1bmN0aW9uIGxleCgpIHtcbiAgICAgICAgdmFyIHIgPSB0aGlzLm5leHQoKTtcbiAgICAgICAgaWYgKHIpIHtcbiAgICAgICAgICByZXR1cm4gcjtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gdGhpcy5sZXgoKTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIC8vIGFjdGl2YXRlcyBhIG5ldyBsZXhlciBjb25kaXRpb24gc3RhdGUgKHB1c2hlcyB0aGUgbmV3IGxleGVyIGNvbmRpdGlvbiBzdGF0ZSBvbnRvIHRoZSBjb25kaXRpb24gc3RhY2spXG4gICAgICBiZWdpbjogZnVuY3Rpb24gYmVnaW4oY29uZGl0aW9uKSB7XG4gICAgICAgIHRoaXMuY29uZGl0aW9uU3RhY2sucHVzaChjb25kaXRpb24pO1xuICAgICAgfSxcbiAgICAgIC8vIHBvcCB0aGUgcHJldmlvdXNseSBhY3RpdmUgbGV4ZXIgY29uZGl0aW9uIHN0YXRlIG9mZiB0aGUgY29uZGl0aW9uIHN0YWNrXG4gICAgICBwb3BTdGF0ZTogZnVuY3Rpb24gcG9wU3RhdGUoKSB7XG4gICAgICAgIHZhciBuID0gdGhpcy5jb25kaXRpb25TdGFjay5sZW5ndGggLSAxO1xuICAgICAgICBpZiAobiA+IDApIHtcbiAgICAgICAgICByZXR1cm4gdGhpcy5jb25kaXRpb25TdGFjay5wb3AoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gdGhpcy5jb25kaXRpb25TdGFja1swXTtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIC8vIHByb2R1Y2UgdGhlIGxleGVyIHJ1bGUgc2V0IHdoaWNoIGlzIGFjdGl2ZSBmb3IgdGhlIGN1cnJlbnRseSBhY3RpdmUgbGV4ZXIgY29uZGl0aW9uIHN0YXRlXG4gICAgICBfY3VycmVudFJ1bGVzOiBmdW5jdGlvbiBfY3VycmVudFJ1bGVzKCkge1xuICAgICAgICBpZiAodGhpcy5jb25kaXRpb25TdGFjay5sZW5ndGggJiYgdGhpcy5jb25kaXRpb25TdGFja1t0aGlzLmNvbmRpdGlvblN0YWNrLmxlbmd0aCAtIDFdKSB7XG4gICAgICAgICAgcmV0dXJuIHRoaXMuY29uZGl0aW9uc1t0aGlzLmNvbmRpdGlvblN0YWNrW3RoaXMuY29uZGl0aW9uU3RhY2subGVuZ3RoIC0gMV1dLnJ1bGVzO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiB0aGlzLmNvbmRpdGlvbnNbXCJJTklUSUFMXCJdLnJ1bGVzO1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgLy8gcmV0dXJuIHRoZSBjdXJyZW50bHkgYWN0aXZlIGxleGVyIGNvbmRpdGlvbiBzdGF0ZTsgd2hlbiBhbiBpbmRleCBhcmd1bWVudCBpcyBwcm92aWRlZCBpdCBwcm9kdWNlcyB0aGUgTi10aCBwcmV2aW91cyBjb25kaXRpb24gc3RhdGUsIGlmIGF2YWlsYWJsZVxuICAgICAgdG9wU3RhdGU6IGZ1bmN0aW9uIHRvcFN0YXRlKG4pIHtcbiAgICAgICAgbiA9IHRoaXMuY29uZGl0aW9uU3RhY2subGVuZ3RoIC0gMSAtIE1hdGguYWJzKG4gfHwgMCk7XG4gICAgICAgIGlmIChuID49IDApIHtcbiAgICAgICAgICByZXR1cm4gdGhpcy5jb25kaXRpb25TdGFja1tuXTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gXCJJTklUSUFMXCI7XG4gICAgICAgIH1cbiAgICAgIH0sXG4gICAgICAvLyBhbGlhcyBmb3IgYmVnaW4oY29uZGl0aW9uKVxuICAgICAgcHVzaFN0YXRlOiBmdW5jdGlvbiBwdXNoU3RhdGUoY29uZGl0aW9uKSB7XG4gICAgICAgIHRoaXMuYmVnaW4oY29uZGl0aW9uKTtcbiAgICAgIH0sXG4gICAgICAvLyByZXR1cm4gdGhlIG51bWJlciBvZiBzdGF0ZXMgY3VycmVudGx5IG9uIHRoZSBzdGFja1xuICAgICAgc3RhdGVTdGFja1NpemU6IGZ1bmN0aW9uIHN0YXRlU3RhY2tTaXplKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5jb25kaXRpb25TdGFjay5sZW5ndGg7XG4gICAgICB9LFxuICAgICAgb3B0aW9uczoge30sXG4gICAgICBwZXJmb3JtQWN0aW9uOiBmdW5jdGlvbiBhbm9ueW1vdXMoeXksIHl5XywgJGF2b2lkaW5nX25hbWVfY29sbGlzaW9ucywgWVlfU1RBUlQpIHtcbiAgICAgICAgc3dpdGNoICgkYXZvaWRpbmdfbmFtZV9jb2xsaXNpb25zKSB7XG4gICAgICAgICAgY2FzZSAwOlxuICAgICAgICAgICAgcmV0dXJuIDY7XG4gICAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgcmV0dXJuIDc7XG4gICAgICAgICAgY2FzZSAyOlxuICAgICAgICAgICAgcmV0dXJuIDg7XG4gICAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgcmV0dXJuIDk7XG4gICAgICAgICAgY2FzZSA0OlxuICAgICAgICAgICAgcmV0dXJuIDIyO1xuICAgICAgICAgIGNhc2UgNTpcbiAgICAgICAgICAgIHJldHVybiAyMztcbiAgICAgICAgICBjYXNlIDY6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwiYWNjX3RpdGxlXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDI0O1xuICAgICAgICAgIGNhc2UgNzpcbiAgICAgICAgICAgIHRoaXMucG9wU3RhdGUoKTtcbiAgICAgICAgICAgIHJldHVybiBcImFjY190aXRsZV92YWx1ZVwiO1xuICAgICAgICAgIGNhc2UgODpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJhY2NfZGVzY3JcIik7XG4gICAgICAgICAgICByZXR1cm4gMjY7XG4gICAgICAgICAgY2FzZSA5OlxuICAgICAgICAgICAgdGhpcy5wb3BTdGF0ZSgpO1xuICAgICAgICAgICAgcmV0dXJuIFwiYWNjX2Rlc2NyX3ZhbHVlXCI7XG4gICAgICAgICAgY2FzZSAxMDpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJhY2NfZGVzY3JfbXVsdGlsaW5lXCIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAxMTpcbiAgICAgICAgICAgIHRoaXMucG9wU3RhdGUoKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgMTI6XG4gICAgICAgICAgICByZXR1cm4gXCJhY2NfZGVzY3JfbXVsdGlsaW5lX3ZhbHVlXCI7XG4gICAgICAgICAgY2FzZSAxMzpcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgMTQ6XG4gICAgICAgICAgICBjO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAxNTpcbiAgICAgICAgICAgIHJldHVybiAxMjtcbiAgICAgICAgICBjYXNlIDE2OlxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSAxNzpcbiAgICAgICAgICAgIHJldHVybiAxMTtcbiAgICAgICAgICBjYXNlIDE4OlxuICAgICAgICAgICAgcmV0dXJuIDE1O1xuICAgICAgICAgIGNhc2UgMTk6XG4gICAgICAgICAgICByZXR1cm4gMTY7XG4gICAgICAgICAgY2FzZSAyMDpcbiAgICAgICAgICAgIHJldHVybiAxNztcbiAgICAgICAgICBjYXNlIDIxOlxuICAgICAgICAgICAgcmV0dXJuIDE4O1xuICAgICAgICAgIGNhc2UgMjI6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwicGVyc29uX2V4dFwiKTtcbiAgICAgICAgICAgIHJldHVybiA0NTtcbiAgICAgICAgICBjYXNlIDIzOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInBlcnNvblwiKTtcbiAgICAgICAgICAgIHJldHVybiA0NDtcbiAgICAgICAgICBjYXNlIDI0OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInN5c3RlbV9leHRfcXVldWVcIik7XG4gICAgICAgICAgICByZXR1cm4gNTE7XG4gICAgICAgICAgY2FzZSAyNTpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJzeXN0ZW1fZXh0X2RiXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDUwO1xuICAgICAgICAgIGNhc2UgMjY6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwic3lzdGVtX2V4dFwiKTtcbiAgICAgICAgICAgIHJldHVybiA0OTtcbiAgICAgICAgICBjYXNlIDI3OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInN5c3RlbV9xdWV1ZVwiKTtcbiAgICAgICAgICAgIHJldHVybiA0ODtcbiAgICAgICAgICBjYXNlIDI4OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInN5c3RlbV9kYlwiKTtcbiAgICAgICAgICAgIHJldHVybiA0NztcbiAgICAgICAgICBjYXNlIDI5OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInN5c3RlbVwiKTtcbiAgICAgICAgICAgIHJldHVybiA0NjtcbiAgICAgICAgICBjYXNlIDMwOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImJvdW5kYXJ5XCIpO1xuICAgICAgICAgICAgcmV0dXJuIDM3O1xuICAgICAgICAgIGNhc2UgMzE6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwiZW50ZXJwcmlzZV9ib3VuZGFyeVwiKTtcbiAgICAgICAgICAgIHJldHVybiAzNDtcbiAgICAgICAgICBjYXNlIDMyOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInN5c3RlbV9ib3VuZGFyeVwiKTtcbiAgICAgICAgICAgIHJldHVybiAzNjtcbiAgICAgICAgICBjYXNlIDMzOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbnRhaW5lcl9leHRfcXVldWVcIik7XG4gICAgICAgICAgICByZXR1cm4gNTc7XG4gICAgICAgICAgY2FzZSAzNDpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJjb250YWluZXJfZXh0X2RiXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDU2O1xuICAgICAgICAgIGNhc2UgMzU6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwiY29udGFpbmVyX2V4dFwiKTtcbiAgICAgICAgICAgIHJldHVybiA1NTtcbiAgICAgICAgICBjYXNlIDM2OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbnRhaW5lcl9xdWV1ZVwiKTtcbiAgICAgICAgICAgIHJldHVybiA1NDtcbiAgICAgICAgICBjYXNlIDM3OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbnRhaW5lcl9kYlwiKTtcbiAgICAgICAgICAgIHJldHVybiA1MztcbiAgICAgICAgICBjYXNlIDM4OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbnRhaW5lclwiKTtcbiAgICAgICAgICAgIHJldHVybiA1MjtcbiAgICAgICAgICBjYXNlIDM5OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbnRhaW5lcl9ib3VuZGFyeVwiKTtcbiAgICAgICAgICAgIHJldHVybiAzODtcbiAgICAgICAgICBjYXNlIDQwOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbXBvbmVudF9leHRfcXVldWVcIik7XG4gICAgICAgICAgICByZXR1cm4gNjM7XG4gICAgICAgICAgY2FzZSA0MTpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJjb21wb25lbnRfZXh0X2RiXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDYyO1xuICAgICAgICAgIGNhc2UgNDI6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwiY29tcG9uZW50X2V4dFwiKTtcbiAgICAgICAgICAgIHJldHVybiA2MTtcbiAgICAgICAgICBjYXNlIDQzOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbXBvbmVudF9xdWV1ZVwiKTtcbiAgICAgICAgICAgIHJldHVybiA2MDtcbiAgICAgICAgICBjYXNlIDQ0OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbXBvbmVudF9kYlwiKTtcbiAgICAgICAgICAgIHJldHVybiA1OTtcbiAgICAgICAgICBjYXNlIDQ1OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImNvbXBvbmVudFwiKTtcbiAgICAgICAgICAgIHJldHVybiA1ODtcbiAgICAgICAgICBjYXNlIDQ2OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcIm5vZGVcIik7XG4gICAgICAgICAgICByZXR1cm4gMzk7XG4gICAgICAgICAgY2FzZSA0NzpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJub2RlXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDM5O1xuICAgICAgICAgIGNhc2UgNDg6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwibm9kZV9sXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDQwO1xuICAgICAgICAgIGNhc2UgNDk6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwibm9kZV9yXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDQxO1xuICAgICAgICAgIGNhc2UgNTA6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwicmVsXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDY0O1xuICAgICAgICAgIGNhc2UgNTE6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwiYmlyZWxcIik7XG4gICAgICAgICAgICByZXR1cm4gNjU7XG4gICAgICAgICAgY2FzZSA1MjpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJyZWxfdVwiKTtcbiAgICAgICAgICAgIHJldHVybiA2NjtcbiAgICAgICAgICBjYXNlIDUzOlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInJlbF91XCIpO1xuICAgICAgICAgICAgcmV0dXJuIDY2O1xuICAgICAgICAgIGNhc2UgNTQ6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwicmVsX2RcIik7XG4gICAgICAgICAgICByZXR1cm4gNjc7XG4gICAgICAgICAgY2FzZSA1NTpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJyZWxfZFwiKTtcbiAgICAgICAgICAgIHJldHVybiA2NztcbiAgICAgICAgICBjYXNlIDU2OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInJlbF9sXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDY4O1xuICAgICAgICAgIGNhc2UgNTc6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwicmVsX2xcIik7XG4gICAgICAgICAgICByZXR1cm4gNjg7XG4gICAgICAgICAgY2FzZSA1ODpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJyZWxfclwiKTtcbiAgICAgICAgICAgIHJldHVybiA2OTtcbiAgICAgICAgICBjYXNlIDU5OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcInJlbF9yXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDY5O1xuICAgICAgICAgIGNhc2UgNjA6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwicmVsX2JcIik7XG4gICAgICAgICAgICByZXR1cm4gNzA7XG4gICAgICAgICAgY2FzZSA2MTpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJyZWxfaW5kZXhcIik7XG4gICAgICAgICAgICByZXR1cm4gNzE7XG4gICAgICAgICAgY2FzZSA2MjpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJ1cGRhdGVfZWxfc3R5bGVcIik7XG4gICAgICAgICAgICByZXR1cm4gNzI7XG4gICAgICAgICAgY2FzZSA2MzpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJ1cGRhdGVfcmVsX3N0eWxlXCIpO1xuICAgICAgICAgICAgcmV0dXJuIDczO1xuICAgICAgICAgIGNhc2UgNjQ6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwidXBkYXRlX2xheW91dF9jb25maWdcIik7XG4gICAgICAgICAgICByZXR1cm4gNzQ7XG4gICAgICAgICAgY2FzZSA2NTpcbiAgICAgICAgICAgIHJldHVybiBcIkVPRl9JTl9TVFJVQ1RcIjtcbiAgICAgICAgICBjYXNlIDY2OlxuICAgICAgICAgICAgdGhpcy5iZWdpbihcImF0dHJpYnV0ZVwiKTtcbiAgICAgICAgICAgIHJldHVybiBcIkFUVFJJQlVURV9FTVBUWVwiO1xuICAgICAgICAgIGNhc2UgNjc6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwiYXR0cmlidXRlXCIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSA2ODpcbiAgICAgICAgICAgIHRoaXMucG9wU3RhdGUoKTtcbiAgICAgICAgICAgIHRoaXMucG9wU3RhdGUoKTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgNjk6XG4gICAgICAgICAgICByZXR1cm4gODA7XG4gICAgICAgICAgY2FzZSA3MDpcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIGNhc2UgNzE6XG4gICAgICAgICAgICByZXR1cm4gODA7XG4gICAgICAgICAgY2FzZSA3MjpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJzdHJpbmdcIik7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIDczOlxuICAgICAgICAgICAgdGhpcy5wb3BTdGF0ZSgpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSA3NDpcbiAgICAgICAgICAgIHJldHVybiBcIlNUUlwiO1xuICAgICAgICAgIGNhc2UgNzU6XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwic3RyaW5nX2t2XCIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSA3NjpcbiAgICAgICAgICAgIHRoaXMuYmVnaW4oXCJzdHJpbmdfa3Zfa2V5XCIpO1xuICAgICAgICAgICAgcmV0dXJuIFwiU1RSX0tFWVwiO1xuICAgICAgICAgIGNhc2UgNzc6XG4gICAgICAgICAgICB0aGlzLnBvcFN0YXRlKCk7XG4gICAgICAgICAgICB0aGlzLmJlZ2luKFwic3RyaW5nX2t2X3ZhbHVlXCIpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgY2FzZSA3ODpcbiAgICAgICAgICAgIHJldHVybiBcIlNUUl9WQUxVRVwiO1xuICAgICAgICAgIGNhc2UgNzk6XG4gICAgICAgICAgICB0aGlzLnBvcFN0YXRlKCk7XG4gICAgICAgICAgICB0aGlzLnBvcFN0YXRlKCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICBjYXNlIDgwOlxuICAgICAgICAgICAgcmV0dXJuIFwiU1RSXCI7XG4gICAgICAgICAgY2FzZSA4MTpcbiAgICAgICAgICAgIHJldHVybiBcIkxCUkFDRVwiO1xuICAgICAgICAgIGNhc2UgODI6XG4gICAgICAgICAgICByZXR1cm4gXCJSQlJBQ0VcIjtcbiAgICAgICAgICBjYXNlIDgzOlxuICAgICAgICAgICAgcmV0dXJuIFwiU1BBQ0VcIjtcbiAgICAgICAgICBjYXNlIDg0OlxuICAgICAgICAgICAgcmV0dXJuIFwiRU9MXCI7XG4gICAgICAgICAgY2FzZSA4NTpcbiAgICAgICAgICAgIHJldHVybiAxNDtcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHJ1bGVzOiBbL14oPzouKmRpcmVjdGlvblxccytUQlteXFxuXSopLywgL14oPzouKmRpcmVjdGlvblxccytCVFteXFxuXSopLywgL14oPzouKmRpcmVjdGlvblxccytSTFteXFxuXSopLywgL14oPzouKmRpcmVjdGlvblxccytMUlteXFxuXSopLywgL14oPzp0aXRsZVxcc1teI1xcbjtdKykvLCAvXig/OmFjY0Rlc2NyaXB0aW9uXFxzW14jXFxuO10rKS8sIC9eKD86YWNjVGl0bGVcXHMqOlxccyopLywgL14oPzooPyFcXG58fCkqW15cXG5dKikvLCAvXig/OmFjY0Rlc2NyXFxzKjpcXHMqKS8sIC9eKD86KD8hXFxufHwpKlteXFxuXSopLywgL14oPzphY2NEZXNjclxccypcXHtcXHMqKS8sIC9eKD86W1xcfV0pLywgL14oPzpbXlxcfV0qKS8sIC9eKD86JSUoPyFcXHspKlteXFxuXSooXFxyP1xcbj8pKykvLCAvXig/OiUlW15cXG5dKihcXHI/XFxuKSopLywgL14oPzpcXHMqKFxccj9cXG4pKykvLCAvXig/OlxccyspLywgL14oPzpDNENvbnRleHRcXGIpLywgL14oPzpDNENvbnRhaW5lclxcYikvLCAvXig/OkM0Q29tcG9uZW50XFxiKS8sIC9eKD86QzREeW5hbWljXFxiKS8sIC9eKD86QzREZXBsb3ltZW50XFxiKS8sIC9eKD86UGVyc29uX0V4dFxcYikvLCAvXig/OlBlcnNvblxcYikvLCAvXig/OlN5c3RlbVF1ZXVlX0V4dFxcYikvLCAvXig/OlN5c3RlbURiX0V4dFxcYikvLCAvXig/OlN5c3RlbV9FeHRcXGIpLywgL14oPzpTeXN0ZW1RdWV1ZVxcYikvLCAvXig/OlN5c3RlbURiXFxiKS8sIC9eKD86U3lzdGVtXFxiKS8sIC9eKD86Qm91bmRhcnlcXGIpLywgL14oPzpFbnRlcnByaXNlX0JvdW5kYXJ5XFxiKS8sIC9eKD86U3lzdGVtX0JvdW5kYXJ5XFxiKS8sIC9eKD86Q29udGFpbmVyUXVldWVfRXh0XFxiKS8sIC9eKD86Q29udGFpbmVyRGJfRXh0XFxiKS8sIC9eKD86Q29udGFpbmVyX0V4dFxcYikvLCAvXig/OkNvbnRhaW5lclF1ZXVlXFxiKS8sIC9eKD86Q29udGFpbmVyRGJcXGIpLywgL14oPzpDb250YWluZXJcXGIpLywgL14oPzpDb250YWluZXJfQm91bmRhcnlcXGIpLywgL14oPzpDb21wb25lbnRRdWV1ZV9FeHRcXGIpLywgL14oPzpDb21wb25lbnREYl9FeHRcXGIpLywgL14oPzpDb21wb25lbnRfRXh0XFxiKS8sIC9eKD86Q29tcG9uZW50UXVldWVcXGIpLywgL14oPzpDb21wb25lbnREYlxcYikvLCAvXig/OkNvbXBvbmVudFxcYikvLCAvXig/OkRlcGxveW1lbnRfTm9kZVxcYikvLCAvXig/Ok5vZGVcXGIpLywgL14oPzpOb2RlX0xcXGIpLywgL14oPzpOb2RlX1JcXGIpLywgL14oPzpSZWxcXGIpLywgL14oPzpCaVJlbFxcYikvLCAvXig/OlJlbF9VcFxcYikvLCAvXig/OlJlbF9VXFxiKS8sIC9eKD86UmVsX0Rvd25cXGIpLywgL14oPzpSZWxfRFxcYikvLCAvXig/OlJlbF9MZWZ0XFxiKS8sIC9eKD86UmVsX0xcXGIpLywgL14oPzpSZWxfUmlnaHRcXGIpLywgL14oPzpSZWxfUlxcYikvLCAvXig/OlJlbF9CYWNrXFxiKS8sIC9eKD86UmVsSW5kZXhcXGIpLywgL14oPzpVcGRhdGVFbGVtZW50U3R5bGVcXGIpLywgL14oPzpVcGRhdGVSZWxTdHlsZVxcYikvLCAvXig/OlVwZGF0ZUxheW91dENvbmZpZ1xcYikvLCAvXig/OiQpLywgL14oPzpbKF1bIF0qWyxdKS8sIC9eKD86WyhdKS8sIC9eKD86WyldKS8sIC9eKD86LCwpLywgL14oPzosKS8sIC9eKD86WyBdKltcIl1bXCJdKS8sIC9eKD86WyBdKltcIl0pLywgL14oPzpbXCJdKS8sIC9eKD86W15cIl0qKS8sIC9eKD86WyBdKltcXCRdKS8sIC9eKD86W149XSopLywgL14oPzpbPV1bIF0qW1wiXSkvLCAvXig/OlteXCJdKykvLCAvXig/OltcIl0pLywgL14oPzpbXixdKykvLCAvXig/OlxceykvLCAvXig/OlxcfSkvLCAvXig/OltcXHNdKykvLCAvXig/OltcXG5cXHJdKykvLCAvXig/OiQpL10sXG4gICAgICBjb25kaXRpb25zOiB7IFwiYWNjX2Rlc2NyX211bHRpbGluZVwiOiB7IFwicnVsZXNcIjogWzExLCAxMl0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiYWNjX2Rlc2NyXCI6IHsgXCJydWxlc1wiOiBbOV0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiYWNjX3RpdGxlXCI6IHsgXCJydWxlc1wiOiBbN10sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwic3RyaW5nX2t2X3ZhbHVlXCI6IHsgXCJydWxlc1wiOiBbNzgsIDc5XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJzdHJpbmdfa3Zfa2V5XCI6IHsgXCJydWxlc1wiOiBbNzddLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInN0cmluZ19rdlwiOiB7IFwicnVsZXNcIjogWzc2XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJzdHJpbmdcIjogeyBcInJ1bGVzXCI6IFs3MywgNzRdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImF0dHJpYnV0ZVwiOiB7IFwicnVsZXNcIjogWzY4LCA2OSwgNzAsIDcxLCA3MiwgNzUsIDgwXSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJ1cGRhdGVfbGF5b3V0X2NvbmZpZ1wiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJ1cGRhdGVfcmVsX3N0eWxlXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInVwZGF0ZV9lbF9zdHlsZVwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJyZWxfYlwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJyZWxfclwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJyZWxfbFwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJyZWxfZFwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJyZWxfdVwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJyZWxfYmlcIjogeyBcInJ1bGVzXCI6IFtdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInJlbFwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJub2RlX3JcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwibm9kZV9sXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcIm5vZGVcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiaW5kZXhcIjogeyBcInJ1bGVzXCI6IFtdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInJlbF9pbmRleFwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJjb21wb25lbnRfZXh0X3F1ZXVlXCI6IHsgXCJydWxlc1wiOiBbXSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJjb21wb25lbnRfZXh0X2RiXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImNvbXBvbmVudF9leHRcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiY29tcG9uZW50X3F1ZXVlXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImNvbXBvbmVudF9kYlwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJjb21wb25lbnRcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiY29udGFpbmVyX2JvdW5kYXJ5XCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImNvbnRhaW5lcl9leHRfcXVldWVcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiY29udGFpbmVyX2V4dF9kYlwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJjb250YWluZXJfZXh0XCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImNvbnRhaW5lcl9xdWV1ZVwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJjb250YWluZXJfZGJcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwiY29udGFpbmVyXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImJpcmVsXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInN5c3RlbV9ib3VuZGFyeVwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJlbnRlcnByaXNlX2JvdW5kYXJ5XCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcImJvdW5kYXJ5XCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInN5c3RlbV9leHRfcXVldWVcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwic3lzdGVtX2V4dF9kYlwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJzeXN0ZW1fZXh0XCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInN5c3RlbV9xdWV1ZVwiOiB7IFwicnVsZXNcIjogWzY1LCA2NiwgNjcsIDY4XSwgXCJpbmNsdXNpdmVcIjogZmFsc2UgfSwgXCJzeXN0ZW1fZGJcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwic3lzdGVtXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcInBlcnNvbl9leHRcIjogeyBcInJ1bGVzXCI6IFs2NSwgNjYsIDY3LCA2OF0sIFwiaW5jbHVzaXZlXCI6IGZhbHNlIH0sIFwicGVyc29uXCI6IHsgXCJydWxlc1wiOiBbNjUsIDY2LCA2NywgNjhdLCBcImluY2x1c2l2ZVwiOiBmYWxzZSB9LCBcIklOSVRJQUxcIjogeyBcInJ1bGVzXCI6IFswLCAxLCAyLCAzLCA0LCA1LCA2LCA4LCAxMCwgMTMsIDE0LCAxNSwgMTYsIDE3LCAxOCwgMTksIDIwLCAyMSwgMjIsIDIzLCAyNCwgMjUsIDI2LCAyNywgMjgsIDI5LCAzMCwgMzEsIDMyLCAzMywgMzQsIDM1LCAzNiwgMzcsIDM4LCAzOSwgNDAsIDQxLCA0MiwgNDMsIDQ0LCA0NSwgNDYsIDQ3LCA0OCwgNDksIDUwLCA1MSwgNTIsIDUzLCA1NCwgNTUsIDU2LCA1NywgNTgsIDU5LCA2MCwgNjEsIDYyLCA2MywgNjQsIDgxLCA4MiwgODMsIDg0LCA4NV0sIFwiaW5jbHVzaXZlXCI6IHRydWUgfSB9XG4gICAgfTtcbiAgICByZXR1cm4gbGV4ZXIyO1xuICB9KCk7XG4gIHBhcnNlcjIubGV4ZXIgPSBsZXhlcjtcbiAgZnVuY3Rpb24gUGFyc2VyKCkge1xuICAgIHRoaXMueXkgPSB7fTtcbiAgfVxuICBQYXJzZXIucHJvdG90eXBlID0gcGFyc2VyMjtcbiAgcGFyc2VyMi5QYXJzZXIgPSBQYXJzZXI7XG4gIHJldHVybiBuZXcgUGFyc2VyKCk7XG59KCk7XG5wYXJzZXIucGFyc2VyID0gcGFyc2VyO1xuY29uc3QgcGFyc2VyJDEgPSBwYXJzZXI7XG5sZXQgYzRTaGFwZUFycmF5ID0gW107XG5sZXQgYm91bmRhcnlQYXJzZVN0YWNrID0gW1wiXCJdO1xubGV0IGN1cnJlbnRCb3VuZGFyeVBhcnNlID0gXCJnbG9iYWxcIjtcbmxldCBwYXJlbnRCb3VuZGFyeVBhcnNlID0gXCJcIjtcbmxldCBib3VuZGFyaWVzID0gW1xuICB7XG4gICAgYWxpYXM6IFwiZ2xvYmFsXCIsXG4gICAgbGFiZWw6IHsgdGV4dDogXCJnbG9iYWxcIiB9LFxuICAgIHR5cGU6IHsgdGV4dDogXCJnbG9iYWxcIiB9LFxuICAgIHRhZ3M6IG51bGwsXG4gICAgbGluazogbnVsbCxcbiAgICBwYXJlbnRCb3VuZGFyeTogXCJcIlxuICB9XG5dO1xubGV0IHJlbHMgPSBbXTtcbmxldCB0aXRsZSA9IFwiXCI7XG5sZXQgd3JhcEVuYWJsZWQgPSBmYWxzZTtcbmxldCBjNFNoYXBlSW5Sb3ckMSA9IDQ7XG5sZXQgYzRCb3VuZGFyeUluUm93JDEgPSAyO1xudmFyIGM0VHlwZTtcbmNvbnN0IGdldEM0VHlwZSA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gYzRUeXBlO1xufTtcbmNvbnN0IHNldEM0VHlwZSA9IGZ1bmN0aW9uKGM0VHlwZVBhcmFtKSB7XG4gIGxldCBzYW5pdGl6ZWRUZXh0ID0gc2FuaXRpemVUZXh0KGM0VHlwZVBhcmFtLCBnZXRDb25maWcoKSk7XG4gIGM0VHlwZSA9IHNhbml0aXplZFRleHQ7XG59O1xuY29uc3QgYWRkUmVsID0gZnVuY3Rpb24odHlwZSwgZnJvbSwgdG8sIGxhYmVsLCB0ZWNobiwgZGVzY3IsIHNwcml0ZSwgdGFncywgbGluaykge1xuICBpZiAodHlwZSA9PT0gdm9pZCAwIHx8IHR5cGUgPT09IG51bGwgfHwgZnJvbSA9PT0gdm9pZCAwIHx8IGZyb20gPT09IG51bGwgfHwgdG8gPT09IHZvaWQgMCB8fCB0byA9PT0gbnVsbCB8fCBsYWJlbCA9PT0gdm9pZCAwIHx8IGxhYmVsID09PSBudWxsKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGxldCByZWwgPSB7fTtcbiAgY29uc3Qgb2xkID0gcmVscy5maW5kKChyZWwyKSA9PiByZWwyLmZyb20gPT09IGZyb20gJiYgcmVsMi50byA9PT0gdG8pO1xuICBpZiAob2xkKSB7XG4gICAgcmVsID0gb2xkO1xuICB9IGVsc2Uge1xuICAgIHJlbHMucHVzaChyZWwpO1xuICB9XG4gIHJlbC50eXBlID0gdHlwZTtcbiAgcmVsLmZyb20gPSBmcm9tO1xuICByZWwudG8gPSB0bztcbiAgcmVsLmxhYmVsID0geyB0ZXh0OiBsYWJlbCB9O1xuICBpZiAodGVjaG4gPT09IHZvaWQgMCB8fCB0ZWNobiA9PT0gbnVsbCkge1xuICAgIHJlbC50ZWNobiA9IHsgdGV4dDogXCJcIiB9O1xuICB9IGVsc2Uge1xuICAgIGlmICh0eXBlb2YgdGVjaG4gPT09IFwib2JqZWN0XCIpIHtcbiAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyh0ZWNobilbMF07XG4gICAgICByZWxba2V5XSA9IHsgdGV4dDogdmFsdWUgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmVsLnRlY2huID0geyB0ZXh0OiB0ZWNobiB9O1xuICAgIH1cbiAgfVxuICBpZiAoZGVzY3IgPT09IHZvaWQgMCB8fCBkZXNjciA9PT0gbnVsbCkge1xuICAgIHJlbC5kZXNjciA9IHsgdGV4dDogXCJcIiB9O1xuICB9IGVsc2Uge1xuICAgIGlmICh0eXBlb2YgZGVzY3IgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhkZXNjcilbMF07XG4gICAgICByZWxba2V5XSA9IHsgdGV4dDogdmFsdWUgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmVsLmRlc2NyID0geyB0ZXh0OiBkZXNjciB9O1xuICAgIH1cbiAgfVxuICBpZiAodHlwZW9mIHNwcml0ZSA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhzcHJpdGUpWzBdO1xuICAgIHJlbFtrZXldID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgcmVsLnNwcml0ZSA9IHNwcml0ZTtcbiAgfVxuICBpZiAodHlwZW9mIHRhZ3MgPT09IFwib2JqZWN0XCIpIHtcbiAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXModGFncylbMF07XG4gICAgcmVsW2tleV0gPSB2YWx1ZTtcbiAgfSBlbHNlIHtcbiAgICByZWwudGFncyA9IHRhZ3M7XG4gIH1cbiAgaWYgKHR5cGVvZiBsaW5rID09PSBcIm9iamVjdFwiKSB7XG4gICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKGxpbmspWzBdO1xuICAgIHJlbFtrZXldID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgcmVsLmxpbmsgPSBsaW5rO1xuICB9XG4gIHJlbC53cmFwID0gYXV0b1dyYXAoKTtcbn07XG5jb25zdCBhZGRQZXJzb25PclN5c3RlbSA9IGZ1bmN0aW9uKHR5cGVDNFNoYXBlLCBhbGlhcywgbGFiZWwsIGRlc2NyLCBzcHJpdGUsIHRhZ3MsIGxpbmspIHtcbiAgaWYgKGFsaWFzID09PSBudWxsIHx8IGxhYmVsID09PSBudWxsKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGxldCBwZXJzb25PclN5c3RlbSA9IHt9O1xuICBjb25zdCBvbGQgPSBjNFNoYXBlQXJyYXkuZmluZCgocGVyc29uT3JTeXN0ZW0yKSA9PiBwZXJzb25PclN5c3RlbTIuYWxpYXMgPT09IGFsaWFzKTtcbiAgaWYgKG9sZCAmJiBhbGlhcyA9PT0gb2xkLmFsaWFzKSB7XG4gICAgcGVyc29uT3JTeXN0ZW0gPSBvbGQ7XG4gIH0gZWxzZSB7XG4gICAgcGVyc29uT3JTeXN0ZW0uYWxpYXMgPSBhbGlhcztcbiAgICBjNFNoYXBlQXJyYXkucHVzaChwZXJzb25PclN5c3RlbSk7XG4gIH1cbiAgaWYgKGxhYmVsID09PSB2b2lkIDAgfHwgbGFiZWwgPT09IG51bGwpIHtcbiAgICBwZXJzb25PclN5c3RlbS5sYWJlbCA9IHsgdGV4dDogXCJcIiB9O1xuICB9IGVsc2Uge1xuICAgIHBlcnNvbk9yU3lzdGVtLmxhYmVsID0geyB0ZXh0OiBsYWJlbCB9O1xuICB9XG4gIGlmIChkZXNjciA9PT0gdm9pZCAwIHx8IGRlc2NyID09PSBudWxsKSB7XG4gICAgcGVyc29uT3JTeXN0ZW0uZGVzY3IgPSB7IHRleHQ6IFwiXCIgfTtcbiAgfSBlbHNlIHtcbiAgICBpZiAodHlwZW9mIGRlc2NyID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMoZGVzY3IpWzBdO1xuICAgICAgcGVyc29uT3JTeXN0ZW1ba2V5XSA9IHsgdGV4dDogdmFsdWUgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgcGVyc29uT3JTeXN0ZW0uZGVzY3IgPSB7IHRleHQ6IGRlc2NyIH07XG4gICAgfVxuICB9XG4gIGlmICh0eXBlb2Ygc3ByaXRlID09PSBcIm9iamVjdFwiKSB7XG4gICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHNwcml0ZSlbMF07XG4gICAgcGVyc29uT3JTeXN0ZW1ba2V5XSA9IHZhbHVlO1xuICB9IGVsc2Uge1xuICAgIHBlcnNvbk9yU3lzdGVtLnNwcml0ZSA9IHNwcml0ZTtcbiAgfVxuICBpZiAodHlwZW9mIHRhZ3MgPT09IFwib2JqZWN0XCIpIHtcbiAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXModGFncylbMF07XG4gICAgcGVyc29uT3JTeXN0ZW1ba2V5XSA9IHZhbHVlO1xuICB9IGVsc2Uge1xuICAgIHBlcnNvbk9yU3lzdGVtLnRhZ3MgPSB0YWdzO1xuICB9XG4gIGlmICh0eXBlb2YgbGluayA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhsaW5rKVswXTtcbiAgICBwZXJzb25PclN5c3RlbVtrZXldID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgcGVyc29uT3JTeXN0ZW0ubGluayA9IGxpbms7XG4gIH1cbiAgcGVyc29uT3JTeXN0ZW0udHlwZUM0U2hhcGUgPSB7IHRleHQ6IHR5cGVDNFNoYXBlIH07XG4gIHBlcnNvbk9yU3lzdGVtLnBhcmVudEJvdW5kYXJ5ID0gY3VycmVudEJvdW5kYXJ5UGFyc2U7XG4gIHBlcnNvbk9yU3lzdGVtLndyYXAgPSBhdXRvV3JhcCgpO1xufTtcbmNvbnN0IGFkZENvbnRhaW5lciA9IGZ1bmN0aW9uKHR5cGVDNFNoYXBlLCBhbGlhcywgbGFiZWwsIHRlY2huLCBkZXNjciwgc3ByaXRlLCB0YWdzLCBsaW5rKSB7XG4gIGlmIChhbGlhcyA9PT0gbnVsbCB8fCBsYWJlbCA9PT0gbnVsbCkge1xuICAgIHJldHVybjtcbiAgfVxuICBsZXQgY29udGFpbmVyID0ge307XG4gIGNvbnN0IG9sZCA9IGM0U2hhcGVBcnJheS5maW5kKChjb250YWluZXIyKSA9PiBjb250YWluZXIyLmFsaWFzID09PSBhbGlhcyk7XG4gIGlmIChvbGQgJiYgYWxpYXMgPT09IG9sZC5hbGlhcykge1xuICAgIGNvbnRhaW5lciA9IG9sZDtcbiAgfSBlbHNlIHtcbiAgICBjb250YWluZXIuYWxpYXMgPSBhbGlhcztcbiAgICBjNFNoYXBlQXJyYXkucHVzaChjb250YWluZXIpO1xuICB9XG4gIGlmIChsYWJlbCA9PT0gdm9pZCAwIHx8IGxhYmVsID09PSBudWxsKSB7XG4gICAgY29udGFpbmVyLmxhYmVsID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgY29udGFpbmVyLmxhYmVsID0geyB0ZXh0OiBsYWJlbCB9O1xuICB9XG4gIGlmICh0ZWNobiA9PT0gdm9pZCAwIHx8IHRlY2huID09PSBudWxsKSB7XG4gICAgY29udGFpbmVyLnRlY2huID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgaWYgKHR5cGVvZiB0ZWNobiA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHRlY2huKVswXTtcbiAgICAgIGNvbnRhaW5lcltrZXldID0geyB0ZXh0OiB2YWx1ZSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb250YWluZXIudGVjaG4gPSB7IHRleHQ6IHRlY2huIH07XG4gICAgfVxuICB9XG4gIGlmIChkZXNjciA9PT0gdm9pZCAwIHx8IGRlc2NyID09PSBudWxsKSB7XG4gICAgY29udGFpbmVyLmRlc2NyID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgaWYgKHR5cGVvZiBkZXNjciA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKGRlc2NyKVswXTtcbiAgICAgIGNvbnRhaW5lcltrZXldID0geyB0ZXh0OiB2YWx1ZSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb250YWluZXIuZGVzY3IgPSB7IHRleHQ6IGRlc2NyIH07XG4gICAgfVxuICB9XG4gIGlmICh0eXBlb2Ygc3ByaXRlID09PSBcIm9iamVjdFwiKSB7XG4gICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHNwcml0ZSlbMF07XG4gICAgY29udGFpbmVyW2tleV0gPSB2YWx1ZTtcbiAgfSBlbHNlIHtcbiAgICBjb250YWluZXIuc3ByaXRlID0gc3ByaXRlO1xuICB9XG4gIGlmICh0eXBlb2YgdGFncyA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyh0YWdzKVswXTtcbiAgICBjb250YWluZXJba2V5XSA9IHZhbHVlO1xuICB9IGVsc2Uge1xuICAgIGNvbnRhaW5lci50YWdzID0gdGFncztcbiAgfVxuICBpZiAodHlwZW9mIGxpbmsgPT09IFwib2JqZWN0XCIpIHtcbiAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMobGluaylbMF07XG4gICAgY29udGFpbmVyW2tleV0gPSB2YWx1ZTtcbiAgfSBlbHNlIHtcbiAgICBjb250YWluZXIubGluayA9IGxpbms7XG4gIH1cbiAgY29udGFpbmVyLndyYXAgPSBhdXRvV3JhcCgpO1xuICBjb250YWluZXIudHlwZUM0U2hhcGUgPSB7IHRleHQ6IHR5cGVDNFNoYXBlIH07XG4gIGNvbnRhaW5lci5wYXJlbnRCb3VuZGFyeSA9IGN1cnJlbnRCb3VuZGFyeVBhcnNlO1xufTtcbmNvbnN0IGFkZENvbXBvbmVudCA9IGZ1bmN0aW9uKHR5cGVDNFNoYXBlLCBhbGlhcywgbGFiZWwsIHRlY2huLCBkZXNjciwgc3ByaXRlLCB0YWdzLCBsaW5rKSB7XG4gIGlmIChhbGlhcyA9PT0gbnVsbCB8fCBsYWJlbCA9PT0gbnVsbCkge1xuICAgIHJldHVybjtcbiAgfVxuICBsZXQgY29tcG9uZW50ID0ge307XG4gIGNvbnN0IG9sZCA9IGM0U2hhcGVBcnJheS5maW5kKChjb21wb25lbnQyKSA9PiBjb21wb25lbnQyLmFsaWFzID09PSBhbGlhcyk7XG4gIGlmIChvbGQgJiYgYWxpYXMgPT09IG9sZC5hbGlhcykge1xuICAgIGNvbXBvbmVudCA9IG9sZDtcbiAgfSBlbHNlIHtcbiAgICBjb21wb25lbnQuYWxpYXMgPSBhbGlhcztcbiAgICBjNFNoYXBlQXJyYXkucHVzaChjb21wb25lbnQpO1xuICB9XG4gIGlmIChsYWJlbCA9PT0gdm9pZCAwIHx8IGxhYmVsID09PSBudWxsKSB7XG4gICAgY29tcG9uZW50LmxhYmVsID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgY29tcG9uZW50LmxhYmVsID0geyB0ZXh0OiBsYWJlbCB9O1xuICB9XG4gIGlmICh0ZWNobiA9PT0gdm9pZCAwIHx8IHRlY2huID09PSBudWxsKSB7XG4gICAgY29tcG9uZW50LnRlY2huID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgaWYgKHR5cGVvZiB0ZWNobiA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHRlY2huKVswXTtcbiAgICAgIGNvbXBvbmVudFtrZXldID0geyB0ZXh0OiB2YWx1ZSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb21wb25lbnQudGVjaG4gPSB7IHRleHQ6IHRlY2huIH07XG4gICAgfVxuICB9XG4gIGlmIChkZXNjciA9PT0gdm9pZCAwIHx8IGRlc2NyID09PSBudWxsKSB7XG4gICAgY29tcG9uZW50LmRlc2NyID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgaWYgKHR5cGVvZiBkZXNjciA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKGRlc2NyKVswXTtcbiAgICAgIGNvbXBvbmVudFtrZXldID0geyB0ZXh0OiB2YWx1ZSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb21wb25lbnQuZGVzY3IgPSB7IHRleHQ6IGRlc2NyIH07XG4gICAgfVxuICB9XG4gIGlmICh0eXBlb2Ygc3ByaXRlID09PSBcIm9iamVjdFwiKSB7XG4gICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHNwcml0ZSlbMF07XG4gICAgY29tcG9uZW50W2tleV0gPSB2YWx1ZTtcbiAgfSBlbHNlIHtcbiAgICBjb21wb25lbnQuc3ByaXRlID0gc3ByaXRlO1xuICB9XG4gIGlmICh0eXBlb2YgdGFncyA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyh0YWdzKVswXTtcbiAgICBjb21wb25lbnRba2V5XSA9IHZhbHVlO1xuICB9IGVsc2Uge1xuICAgIGNvbXBvbmVudC50YWdzID0gdGFncztcbiAgfVxuICBpZiAodHlwZW9mIGxpbmsgPT09IFwib2JqZWN0XCIpIHtcbiAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMobGluaylbMF07XG4gICAgY29tcG9uZW50W2tleV0gPSB2YWx1ZTtcbiAgfSBlbHNlIHtcbiAgICBjb21wb25lbnQubGluayA9IGxpbms7XG4gIH1cbiAgY29tcG9uZW50LndyYXAgPSBhdXRvV3JhcCgpO1xuICBjb21wb25lbnQudHlwZUM0U2hhcGUgPSB7IHRleHQ6IHR5cGVDNFNoYXBlIH07XG4gIGNvbXBvbmVudC5wYXJlbnRCb3VuZGFyeSA9IGN1cnJlbnRCb3VuZGFyeVBhcnNlO1xufTtcbmNvbnN0IGFkZFBlcnNvbk9yU3lzdGVtQm91bmRhcnkgPSBmdW5jdGlvbihhbGlhcywgbGFiZWwsIHR5cGUsIHRhZ3MsIGxpbmspIHtcbiAgaWYgKGFsaWFzID09PSBudWxsIHx8IGxhYmVsID09PSBudWxsKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGxldCBib3VuZGFyeSA9IHt9O1xuICBjb25zdCBvbGQgPSBib3VuZGFyaWVzLmZpbmQoKGJvdW5kYXJ5MikgPT4gYm91bmRhcnkyLmFsaWFzID09PSBhbGlhcyk7XG4gIGlmIChvbGQgJiYgYWxpYXMgPT09IG9sZC5hbGlhcykge1xuICAgIGJvdW5kYXJ5ID0gb2xkO1xuICB9IGVsc2Uge1xuICAgIGJvdW5kYXJ5LmFsaWFzID0gYWxpYXM7XG4gICAgYm91bmRhcmllcy5wdXNoKGJvdW5kYXJ5KTtcbiAgfVxuICBpZiAobGFiZWwgPT09IHZvaWQgMCB8fCBsYWJlbCA9PT0gbnVsbCkge1xuICAgIGJvdW5kYXJ5LmxhYmVsID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgYm91bmRhcnkubGFiZWwgPSB7IHRleHQ6IGxhYmVsIH07XG4gIH1cbiAgaWYgKHR5cGUgPT09IHZvaWQgMCB8fCB0eXBlID09PSBudWxsKSB7XG4gICAgYm91bmRhcnkudHlwZSA9IHsgdGV4dDogXCJzeXN0ZW1cIiB9O1xuICB9IGVsc2Uge1xuICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHR5cGUpWzBdO1xuICAgICAgYm91bmRhcnlba2V5XSA9IHsgdGV4dDogdmFsdWUgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgYm91bmRhcnkudHlwZSA9IHsgdGV4dDogdHlwZSB9O1xuICAgIH1cbiAgfVxuICBpZiAodHlwZW9mIHRhZ3MgPT09IFwib2JqZWN0XCIpIHtcbiAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXModGFncylbMF07XG4gICAgYm91bmRhcnlba2V5XSA9IHZhbHVlO1xuICB9IGVsc2Uge1xuICAgIGJvdW5kYXJ5LnRhZ3MgPSB0YWdzO1xuICB9XG4gIGlmICh0eXBlb2YgbGluayA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhsaW5rKVswXTtcbiAgICBib3VuZGFyeVtrZXldID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgYm91bmRhcnkubGluayA9IGxpbms7XG4gIH1cbiAgYm91bmRhcnkucGFyZW50Qm91bmRhcnkgPSBjdXJyZW50Qm91bmRhcnlQYXJzZTtcbiAgYm91bmRhcnkud3JhcCA9IGF1dG9XcmFwKCk7XG4gIHBhcmVudEJvdW5kYXJ5UGFyc2UgPSBjdXJyZW50Qm91bmRhcnlQYXJzZTtcbiAgY3VycmVudEJvdW5kYXJ5UGFyc2UgPSBhbGlhcztcbiAgYm91bmRhcnlQYXJzZVN0YWNrLnB1c2gocGFyZW50Qm91bmRhcnlQYXJzZSk7XG59O1xuY29uc3QgYWRkQ29udGFpbmVyQm91bmRhcnkgPSBmdW5jdGlvbihhbGlhcywgbGFiZWwsIHR5cGUsIHRhZ3MsIGxpbmspIHtcbiAgaWYgKGFsaWFzID09PSBudWxsIHx8IGxhYmVsID09PSBudWxsKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGxldCBib3VuZGFyeSA9IHt9O1xuICBjb25zdCBvbGQgPSBib3VuZGFyaWVzLmZpbmQoKGJvdW5kYXJ5MikgPT4gYm91bmRhcnkyLmFsaWFzID09PSBhbGlhcyk7XG4gIGlmIChvbGQgJiYgYWxpYXMgPT09IG9sZC5hbGlhcykge1xuICAgIGJvdW5kYXJ5ID0gb2xkO1xuICB9IGVsc2Uge1xuICAgIGJvdW5kYXJ5LmFsaWFzID0gYWxpYXM7XG4gICAgYm91bmRhcmllcy5wdXNoKGJvdW5kYXJ5KTtcbiAgfVxuICBpZiAobGFiZWwgPT09IHZvaWQgMCB8fCBsYWJlbCA9PT0gbnVsbCkge1xuICAgIGJvdW5kYXJ5LmxhYmVsID0geyB0ZXh0OiBcIlwiIH07XG4gIH0gZWxzZSB7XG4gICAgYm91bmRhcnkubGFiZWwgPSB7IHRleHQ6IGxhYmVsIH07XG4gIH1cbiAgaWYgKHR5cGUgPT09IHZvaWQgMCB8fCB0eXBlID09PSBudWxsKSB7XG4gICAgYm91bmRhcnkudHlwZSA9IHsgdGV4dDogXCJjb250YWluZXJcIiB9O1xuICB9IGVsc2Uge1xuICAgIGlmICh0eXBlb2YgdHlwZSA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHR5cGUpWzBdO1xuICAgICAgYm91bmRhcnlba2V5XSA9IHsgdGV4dDogdmFsdWUgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgYm91bmRhcnkudHlwZSA9IHsgdGV4dDogdHlwZSB9O1xuICAgIH1cbiAgfVxuICBpZiAodHlwZW9mIHRhZ3MgPT09IFwib2JqZWN0XCIpIHtcbiAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXModGFncylbMF07XG4gICAgYm91bmRhcnlba2V5XSA9IHZhbHVlO1xuICB9IGVsc2Uge1xuICAgIGJvdW5kYXJ5LnRhZ3MgPSB0YWdzO1xuICB9XG4gIGlmICh0eXBlb2YgbGluayA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhsaW5rKVswXTtcbiAgICBib3VuZGFyeVtrZXldID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgYm91bmRhcnkubGluayA9IGxpbms7XG4gIH1cbiAgYm91bmRhcnkucGFyZW50Qm91bmRhcnkgPSBjdXJyZW50Qm91bmRhcnlQYXJzZTtcbiAgYm91bmRhcnkud3JhcCA9IGF1dG9XcmFwKCk7XG4gIHBhcmVudEJvdW5kYXJ5UGFyc2UgPSBjdXJyZW50Qm91bmRhcnlQYXJzZTtcbiAgY3VycmVudEJvdW5kYXJ5UGFyc2UgPSBhbGlhcztcbiAgYm91bmRhcnlQYXJzZVN0YWNrLnB1c2gocGFyZW50Qm91bmRhcnlQYXJzZSk7XG59O1xuY29uc3QgYWRkRGVwbG95bWVudE5vZGUgPSBmdW5jdGlvbihub2RlVHlwZSwgYWxpYXMsIGxhYmVsLCB0eXBlLCBkZXNjciwgc3ByaXRlLCB0YWdzLCBsaW5rKSB7XG4gIGlmIChhbGlhcyA9PT0gbnVsbCB8fCBsYWJlbCA9PT0gbnVsbCkge1xuICAgIHJldHVybjtcbiAgfVxuICBsZXQgYm91bmRhcnkgPSB7fTtcbiAgY29uc3Qgb2xkID0gYm91bmRhcmllcy5maW5kKChib3VuZGFyeTIpID0+IGJvdW5kYXJ5Mi5hbGlhcyA9PT0gYWxpYXMpO1xuICBpZiAob2xkICYmIGFsaWFzID09PSBvbGQuYWxpYXMpIHtcbiAgICBib3VuZGFyeSA9IG9sZDtcbiAgfSBlbHNlIHtcbiAgICBib3VuZGFyeS5hbGlhcyA9IGFsaWFzO1xuICAgIGJvdW5kYXJpZXMucHVzaChib3VuZGFyeSk7XG4gIH1cbiAgaWYgKGxhYmVsID09PSB2b2lkIDAgfHwgbGFiZWwgPT09IG51bGwpIHtcbiAgICBib3VuZGFyeS5sYWJlbCA9IHsgdGV4dDogXCJcIiB9O1xuICB9IGVsc2Uge1xuICAgIGJvdW5kYXJ5LmxhYmVsID0geyB0ZXh0OiBsYWJlbCB9O1xuICB9XG4gIGlmICh0eXBlID09PSB2b2lkIDAgfHwgdHlwZSA9PT0gbnVsbCkge1xuICAgIGJvdW5kYXJ5LnR5cGUgPSB7IHRleHQ6IFwibm9kZVwiIH07XG4gIH0gZWxzZSB7XG4gICAgaWYgKHR5cGVvZiB0eXBlID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXModHlwZSlbMF07XG4gICAgICBib3VuZGFyeVtrZXldID0geyB0ZXh0OiB2YWx1ZSB9O1xuICAgIH0gZWxzZSB7XG4gICAgICBib3VuZGFyeS50eXBlID0geyB0ZXh0OiB0eXBlIH07XG4gICAgfVxuICB9XG4gIGlmIChkZXNjciA9PT0gdm9pZCAwIHx8IGRlc2NyID09PSBudWxsKSB7XG4gICAgYm91bmRhcnkuZGVzY3IgPSB7IHRleHQ6IFwiXCIgfTtcbiAgfSBlbHNlIHtcbiAgICBpZiAodHlwZW9mIGRlc2NyID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMoZGVzY3IpWzBdO1xuICAgICAgYm91bmRhcnlba2V5XSA9IHsgdGV4dDogdmFsdWUgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgYm91bmRhcnkuZGVzY3IgPSB7IHRleHQ6IGRlc2NyIH07XG4gICAgfVxuICB9XG4gIGlmICh0eXBlb2YgdGFncyA9PT0gXCJvYmplY3RcIikge1xuICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyh0YWdzKVswXTtcbiAgICBib3VuZGFyeVtrZXldID0gdmFsdWU7XG4gIH0gZWxzZSB7XG4gICAgYm91bmRhcnkudGFncyA9IHRhZ3M7XG4gIH1cbiAgaWYgKHR5cGVvZiBsaW5rID09PSBcIm9iamVjdFwiKSB7XG4gICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKGxpbmspWzBdO1xuICAgIGJvdW5kYXJ5W2tleV0gPSB2YWx1ZTtcbiAgfSBlbHNlIHtcbiAgICBib3VuZGFyeS5saW5rID0gbGluaztcbiAgfVxuICBib3VuZGFyeS5ub2RlVHlwZSA9IG5vZGVUeXBlO1xuICBib3VuZGFyeS5wYXJlbnRCb3VuZGFyeSA9IGN1cnJlbnRCb3VuZGFyeVBhcnNlO1xuICBib3VuZGFyeS53cmFwID0gYXV0b1dyYXAoKTtcbiAgcGFyZW50Qm91bmRhcnlQYXJzZSA9IGN1cnJlbnRCb3VuZGFyeVBhcnNlO1xuICBjdXJyZW50Qm91bmRhcnlQYXJzZSA9IGFsaWFzO1xuICBib3VuZGFyeVBhcnNlU3RhY2sucHVzaChwYXJlbnRCb3VuZGFyeVBhcnNlKTtcbn07XG5jb25zdCBwb3BCb3VuZGFyeVBhcnNlU3RhY2sgPSBmdW5jdGlvbigpIHtcbiAgY3VycmVudEJvdW5kYXJ5UGFyc2UgPSBwYXJlbnRCb3VuZGFyeVBhcnNlO1xuICBib3VuZGFyeVBhcnNlU3RhY2sucG9wKCk7XG4gIHBhcmVudEJvdW5kYXJ5UGFyc2UgPSBib3VuZGFyeVBhcnNlU3RhY2sucG9wKCk7XG4gIGJvdW5kYXJ5UGFyc2VTdGFjay5wdXNoKHBhcmVudEJvdW5kYXJ5UGFyc2UpO1xufTtcbmNvbnN0IHVwZGF0ZUVsU3R5bGUgPSBmdW5jdGlvbih0eXBlQzRTaGFwZSwgZWxlbWVudE5hbWUsIGJnQ29sb3IsIGZvbnRDb2xvciwgYm9yZGVyQ29sb3IsIHNoYWRvd2luZywgc2hhcGUsIHNwcml0ZSwgdGVjaG4sIGxlZ2VuZFRleHQsIGxlZ2VuZFNwcml0ZSkge1xuICBsZXQgb2xkID0gYzRTaGFwZUFycmF5LmZpbmQoKGVsZW1lbnQpID0+IGVsZW1lbnQuYWxpYXMgPT09IGVsZW1lbnROYW1lKTtcbiAgaWYgKG9sZCA9PT0gdm9pZCAwKSB7XG4gICAgb2xkID0gYm91bmRhcmllcy5maW5kKChlbGVtZW50KSA9PiBlbGVtZW50LmFsaWFzID09PSBlbGVtZW50TmFtZSk7XG4gICAgaWYgKG9sZCA9PT0gdm9pZCAwKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICB9XG4gIGlmIChiZ0NvbG9yICE9PSB2b2lkIDAgJiYgYmdDb2xvciAhPT0gbnVsbCkge1xuICAgIGlmICh0eXBlb2YgYmdDb2xvciA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKGJnQ29sb3IpWzBdO1xuICAgICAgb2xkW2tleV0gPSB2YWx1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2xkLmJnQ29sb3IgPSBiZ0NvbG9yO1xuICAgIH1cbiAgfVxuICBpZiAoZm9udENvbG9yICE9PSB2b2lkIDAgJiYgZm9udENvbG9yICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGVvZiBmb250Q29sb3IgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhmb250Q29sb3IpWzBdO1xuICAgICAgb2xkW2tleV0gPSB2YWx1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2xkLmZvbnRDb2xvciA9IGZvbnRDb2xvcjtcbiAgICB9XG4gIH1cbiAgaWYgKGJvcmRlckNvbG9yICE9PSB2b2lkIDAgJiYgYm9yZGVyQ29sb3IgIT09IG51bGwpIHtcbiAgICBpZiAodHlwZW9mIGJvcmRlckNvbG9yID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMoYm9yZGVyQ29sb3IpWzBdO1xuICAgICAgb2xkW2tleV0gPSB2YWx1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2xkLmJvcmRlckNvbG9yID0gYm9yZGVyQ29sb3I7XG4gICAgfVxuICB9XG4gIGlmIChzaGFkb3dpbmcgIT09IHZvaWQgMCAmJiBzaGFkb3dpbmcgIT09IG51bGwpIHtcbiAgICBpZiAodHlwZW9mIHNoYWRvd2luZyA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHNoYWRvd2luZylbMF07XG4gICAgICBvbGRba2V5XSA9IHZhbHVlO1xuICAgIH0gZWxzZSB7XG4gICAgICBvbGQuc2hhZG93aW5nID0gc2hhZG93aW5nO1xuICAgIH1cbiAgfVxuICBpZiAoc2hhcGUgIT09IHZvaWQgMCAmJiBzaGFwZSAhPT0gbnVsbCkge1xuICAgIGlmICh0eXBlb2Ygc2hhcGUgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhzaGFwZSlbMF07XG4gICAgICBvbGRba2V5XSA9IHZhbHVlO1xuICAgIH0gZWxzZSB7XG4gICAgICBvbGQuc2hhcGUgPSBzaGFwZTtcbiAgICB9XG4gIH1cbiAgaWYgKHNwcml0ZSAhPT0gdm9pZCAwICYmIHNwcml0ZSAhPT0gbnVsbCkge1xuICAgIGlmICh0eXBlb2Ygc3ByaXRlID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMoc3ByaXRlKVswXTtcbiAgICAgIG9sZFtrZXldID0gdmFsdWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9sZC5zcHJpdGUgPSBzcHJpdGU7XG4gICAgfVxuICB9XG4gIGlmICh0ZWNobiAhPT0gdm9pZCAwICYmIHRlY2huICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGVvZiB0ZWNobiA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHRlY2huKVswXTtcbiAgICAgIG9sZFtrZXldID0gdmFsdWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9sZC50ZWNobiA9IHRlY2huO1xuICAgIH1cbiAgfVxuICBpZiAobGVnZW5kVGV4dCAhPT0gdm9pZCAwICYmIGxlZ2VuZFRleHQgIT09IG51bGwpIHtcbiAgICBpZiAodHlwZW9mIGxlZ2VuZFRleHQgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhsZWdlbmRUZXh0KVswXTtcbiAgICAgIG9sZFtrZXldID0gdmFsdWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9sZC5sZWdlbmRUZXh0ID0gbGVnZW5kVGV4dDtcbiAgICB9XG4gIH1cbiAgaWYgKGxlZ2VuZFNwcml0ZSAhPT0gdm9pZCAwICYmIGxlZ2VuZFNwcml0ZSAhPT0gbnVsbCkge1xuICAgIGlmICh0eXBlb2YgbGVnZW5kU3ByaXRlID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMobGVnZW5kU3ByaXRlKVswXTtcbiAgICAgIG9sZFtrZXldID0gdmFsdWU7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9sZC5sZWdlbmRTcHJpdGUgPSBsZWdlbmRTcHJpdGU7XG4gICAgfVxuICB9XG59O1xuY29uc3QgdXBkYXRlUmVsU3R5bGUgPSBmdW5jdGlvbih0eXBlQzRTaGFwZSwgZnJvbSwgdG8sIHRleHRDb2xvciwgbGluZUNvbG9yLCBvZmZzZXRYLCBvZmZzZXRZKSB7XG4gIGNvbnN0IG9sZCA9IHJlbHMuZmluZCgocmVsKSA9PiByZWwuZnJvbSA9PT0gZnJvbSAmJiByZWwudG8gPT09IHRvKTtcbiAgaWYgKG9sZCA9PT0gdm9pZCAwKSB7XG4gICAgcmV0dXJuO1xuICB9XG4gIGlmICh0ZXh0Q29sb3IgIT09IHZvaWQgMCAmJiB0ZXh0Q29sb3IgIT09IG51bGwpIHtcbiAgICBpZiAodHlwZW9mIHRleHRDb2xvciA9PT0gXCJvYmplY3RcIikge1xuICAgICAgbGV0IFtrZXksIHZhbHVlXSA9IE9iamVjdC5lbnRyaWVzKHRleHRDb2xvcilbMF07XG4gICAgICBvbGRba2V5XSA9IHZhbHVlO1xuICAgIH0gZWxzZSB7XG4gICAgICBvbGQudGV4dENvbG9yID0gdGV4dENvbG9yO1xuICAgIH1cbiAgfVxuICBpZiAobGluZUNvbG9yICE9PSB2b2lkIDAgJiYgbGluZUNvbG9yICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGVvZiBsaW5lQ29sb3IgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgIGxldCBba2V5LCB2YWx1ZV0gPSBPYmplY3QuZW50cmllcyhsaW5lQ29sb3IpWzBdO1xuICAgICAgb2xkW2tleV0gPSB2YWx1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2xkLmxpbmVDb2xvciA9IGxpbmVDb2xvcjtcbiAgICB9XG4gIH1cbiAgaWYgKG9mZnNldFggIT09IHZvaWQgMCAmJiBvZmZzZXRYICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGVvZiBvZmZzZXRYID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMob2Zmc2V0WClbMF07XG4gICAgICBvbGRba2V5XSA9IHBhcnNlSW50KHZhbHVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2xkLm9mZnNldFggPSBwYXJzZUludChvZmZzZXRYKTtcbiAgICB9XG4gIH1cbiAgaWYgKG9mZnNldFkgIT09IHZvaWQgMCAmJiBvZmZzZXRZICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGVvZiBvZmZzZXRZID09PSBcIm9iamVjdFwiKSB7XG4gICAgICBsZXQgW2tleSwgdmFsdWVdID0gT2JqZWN0LmVudHJpZXMob2Zmc2V0WSlbMF07XG4gICAgICBvbGRba2V5XSA9IHBhcnNlSW50KHZhbHVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2xkLm9mZnNldFkgPSBwYXJzZUludChvZmZzZXRZKTtcbiAgICB9XG4gIH1cbn07XG5jb25zdCB1cGRhdGVMYXlvdXRDb25maWcgPSBmdW5jdGlvbih0eXBlQzRTaGFwZSwgYzRTaGFwZUluUm93UGFyYW0sIGM0Qm91bmRhcnlJblJvd1BhcmFtKSB7XG4gIGxldCBjNFNoYXBlSW5Sb3dWYWx1ZSA9IGM0U2hhcGVJblJvdyQxO1xuICBsZXQgYzRCb3VuZGFyeUluUm93VmFsdWUgPSBjNEJvdW5kYXJ5SW5Sb3ckMTtcbiAgaWYgKHR5cGVvZiBjNFNoYXBlSW5Sb3dQYXJhbSA9PT0gXCJvYmplY3RcIikge1xuICAgIGNvbnN0IHZhbHVlID0gT2JqZWN0LnZhbHVlcyhjNFNoYXBlSW5Sb3dQYXJhbSlbMF07XG4gICAgYzRTaGFwZUluUm93VmFsdWUgPSBwYXJzZUludCh2YWx1ZSk7XG4gIH0gZWxzZSB7XG4gICAgYzRTaGFwZUluUm93VmFsdWUgPSBwYXJzZUludChjNFNoYXBlSW5Sb3dQYXJhbSk7XG4gIH1cbiAgaWYgKHR5cGVvZiBjNEJvdW5kYXJ5SW5Sb3dQYXJhbSA9PT0gXCJvYmplY3RcIikge1xuICAgIGNvbnN0IHZhbHVlID0gT2JqZWN0LnZhbHVlcyhjNEJvdW5kYXJ5SW5Sb3dQYXJhbSlbMF07XG4gICAgYzRCb3VuZGFyeUluUm93VmFsdWUgPSBwYXJzZUludCh2YWx1ZSk7XG4gIH0gZWxzZSB7XG4gICAgYzRCb3VuZGFyeUluUm93VmFsdWUgPSBwYXJzZUludChjNEJvdW5kYXJ5SW5Sb3dQYXJhbSk7XG4gIH1cbiAgaWYgKGM0U2hhcGVJblJvd1ZhbHVlID49IDEpIHtcbiAgICBjNFNoYXBlSW5Sb3ckMSA9IGM0U2hhcGVJblJvd1ZhbHVlO1xuICB9XG4gIGlmIChjNEJvdW5kYXJ5SW5Sb3dWYWx1ZSA+PSAxKSB7XG4gICAgYzRCb3VuZGFyeUluUm93JDEgPSBjNEJvdW5kYXJ5SW5Sb3dWYWx1ZTtcbiAgfVxufTtcbmNvbnN0IGdldEM0U2hhcGVJblJvdyA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gYzRTaGFwZUluUm93JDE7XG59O1xuY29uc3QgZ2V0QzRCb3VuZGFyeUluUm93ID0gZnVuY3Rpb24oKSB7XG4gIHJldHVybiBjNEJvdW5kYXJ5SW5Sb3ckMTtcbn07XG5jb25zdCBnZXRDdXJyZW50Qm91bmRhcnlQYXJzZSA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gY3VycmVudEJvdW5kYXJ5UGFyc2U7XG59O1xuY29uc3QgZ2V0UGFyZW50Qm91bmRhcnlQYXJzZSA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gcGFyZW50Qm91bmRhcnlQYXJzZTtcbn07XG5jb25zdCBnZXRDNFNoYXBlQXJyYXkgPSBmdW5jdGlvbihwYXJlbnRCb3VuZGFyeSkge1xuICBpZiAocGFyZW50Qm91bmRhcnkgPT09IHZvaWQgMCB8fCBwYXJlbnRCb3VuZGFyeSA9PT0gbnVsbCkge1xuICAgIHJldHVybiBjNFNoYXBlQXJyYXk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGM0U2hhcGVBcnJheS5maWx0ZXIoKHBlcnNvbk9yU3lzdGVtKSA9PiB7XG4gICAgICByZXR1cm4gcGVyc29uT3JTeXN0ZW0ucGFyZW50Qm91bmRhcnkgPT09IHBhcmVudEJvdW5kYXJ5O1xuICAgIH0pO1xuICB9XG59O1xuY29uc3QgZ2V0QzRTaGFwZSA9IGZ1bmN0aW9uKGFsaWFzKSB7XG4gIHJldHVybiBjNFNoYXBlQXJyYXkuZmluZCgocGVyc29uT3JTeXN0ZW0pID0+IHBlcnNvbk9yU3lzdGVtLmFsaWFzID09PSBhbGlhcyk7XG59O1xuY29uc3QgZ2V0QzRTaGFwZUtleXMgPSBmdW5jdGlvbihwYXJlbnRCb3VuZGFyeSkge1xuICByZXR1cm4gT2JqZWN0LmtleXMoZ2V0QzRTaGFwZUFycmF5KHBhcmVudEJvdW5kYXJ5KSk7XG59O1xuY29uc3QgZ2V0Qm91bmRhcmllcyA9IGZ1bmN0aW9uKHBhcmVudEJvdW5kYXJ5KSB7XG4gIGlmIChwYXJlbnRCb3VuZGFyeSA9PT0gdm9pZCAwIHx8IHBhcmVudEJvdW5kYXJ5ID09PSBudWxsKSB7XG4gICAgcmV0dXJuIGJvdW5kYXJpZXM7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIGJvdW5kYXJpZXMuZmlsdGVyKChib3VuZGFyeSkgPT4gYm91bmRhcnkucGFyZW50Qm91bmRhcnkgPT09IHBhcmVudEJvdW5kYXJ5KTtcbiAgfVxufTtcbmNvbnN0IGdldEJvdW5kYXJ5cyA9IGdldEJvdW5kYXJpZXM7XG5jb25zdCBnZXRSZWxzID0gZnVuY3Rpb24oKSB7XG4gIHJldHVybiByZWxzO1xufTtcbmNvbnN0IGdldFRpdGxlID0gZnVuY3Rpb24oKSB7XG4gIHJldHVybiB0aXRsZTtcbn07XG5jb25zdCBzZXRXcmFwID0gZnVuY3Rpb24od3JhcFNldHRpbmcpIHtcbiAgd3JhcEVuYWJsZWQgPSB3cmFwU2V0dGluZztcbn07XG5jb25zdCBhdXRvV3JhcCA9IGZ1bmN0aW9uKCkge1xuICByZXR1cm4gd3JhcEVuYWJsZWQ7XG59O1xuY29uc3QgY2xlYXIgPSBmdW5jdGlvbigpIHtcbiAgYzRTaGFwZUFycmF5ID0gW107XG4gIGJvdW5kYXJpZXMgPSBbXG4gICAge1xuICAgICAgYWxpYXM6IFwiZ2xvYmFsXCIsXG4gICAgICBsYWJlbDogeyB0ZXh0OiBcImdsb2JhbFwiIH0sXG4gICAgICB0eXBlOiB7IHRleHQ6IFwiZ2xvYmFsXCIgfSxcbiAgICAgIHRhZ3M6IG51bGwsXG4gICAgICBsaW5rOiBudWxsLFxuICAgICAgcGFyZW50Qm91bmRhcnk6IFwiXCJcbiAgICB9XG4gIF07XG4gIHBhcmVudEJvdW5kYXJ5UGFyc2UgPSBcIlwiO1xuICBjdXJyZW50Qm91bmRhcnlQYXJzZSA9IFwiZ2xvYmFsXCI7XG4gIGJvdW5kYXJ5UGFyc2VTdGFjayA9IFtcIlwiXTtcbiAgcmVscyA9IFtdO1xuICBib3VuZGFyeVBhcnNlU3RhY2sgPSBbXCJcIl07XG4gIHRpdGxlID0gXCJcIjtcbiAgd3JhcEVuYWJsZWQgPSBmYWxzZTtcbiAgYzRTaGFwZUluUm93JDEgPSA0O1xuICBjNEJvdW5kYXJ5SW5Sb3ckMSA9IDI7XG59O1xuY29uc3QgTElORVRZUEUgPSB7XG4gIFNPTElEOiAwLFxuICBET1RURUQ6IDEsXG4gIE5PVEU6IDIsXG4gIFNPTElEX0NST1NTOiAzLFxuICBET1RURURfQ1JPU1M6IDQsXG4gIFNPTElEX09QRU46IDUsXG4gIERPVFRFRF9PUEVOOiA2LFxuICBMT09QX1NUQVJUOiAxMCxcbiAgTE9PUF9FTkQ6IDExLFxuICBBTFRfU1RBUlQ6IDEyLFxuICBBTFRfRUxTRTogMTMsXG4gIEFMVF9FTkQ6IDE0LFxuICBPUFRfU1RBUlQ6IDE1LFxuICBPUFRfRU5EOiAxNixcbiAgQUNUSVZFX1NUQVJUOiAxNyxcbiAgQUNUSVZFX0VORDogMTgsXG4gIFBBUl9TVEFSVDogMTksXG4gIFBBUl9BTkQ6IDIwLFxuICBQQVJfRU5EOiAyMSxcbiAgUkVDVF9TVEFSVDogMjIsXG4gIFJFQ1RfRU5EOiAyMyxcbiAgU09MSURfUE9JTlQ6IDI0LFxuICBET1RURURfUE9JTlQ6IDI1XG59O1xuY29uc3QgQVJST1dUWVBFID0ge1xuICBGSUxMRUQ6IDAsXG4gIE9QRU46IDFcbn07XG5jb25zdCBQTEFDRU1FTlQgPSB7XG4gIExFRlRPRjogMCxcbiAgUklHSFRPRjogMSxcbiAgT1ZFUjogMlxufTtcbmNvbnN0IHNldFRpdGxlID0gZnVuY3Rpb24odHh0KSB7XG4gIGxldCBzYW5pdGl6ZWRUZXh0ID0gc2FuaXRpemVUZXh0KHR4dCwgZ2V0Q29uZmlnKCkpO1xuICB0aXRsZSA9IHNhbml0aXplZFRleHQ7XG59O1xuY29uc3QgZGIgPSB7XG4gIGFkZFBlcnNvbk9yU3lzdGVtLFxuICBhZGRQZXJzb25PclN5c3RlbUJvdW5kYXJ5LFxuICBhZGRDb250YWluZXIsXG4gIGFkZENvbnRhaW5lckJvdW5kYXJ5LFxuICBhZGRDb21wb25lbnQsXG4gIGFkZERlcGxveW1lbnROb2RlLFxuICBwb3BCb3VuZGFyeVBhcnNlU3RhY2ssXG4gIGFkZFJlbCxcbiAgdXBkYXRlRWxTdHlsZSxcbiAgdXBkYXRlUmVsU3R5bGUsXG4gIHVwZGF0ZUxheW91dENvbmZpZyxcbiAgYXV0b1dyYXAsXG4gIHNldFdyYXAsXG4gIGdldEM0U2hhcGVBcnJheSxcbiAgZ2V0QzRTaGFwZSxcbiAgZ2V0QzRTaGFwZUtleXMsXG4gIGdldEJvdW5kYXJpZXMsXG4gIGdldEJvdW5kYXJ5cyxcbiAgZ2V0Q3VycmVudEJvdW5kYXJ5UGFyc2UsXG4gIGdldFBhcmVudEJvdW5kYXJ5UGFyc2UsXG4gIGdldFJlbHMsXG4gIGdldFRpdGxlLFxuICBnZXRDNFR5cGUsXG4gIGdldEM0U2hhcGVJblJvdyxcbiAgZ2V0QzRCb3VuZGFyeUluUm93LFxuICBzZXRBY2NUaXRsZSxcbiAgZ2V0QWNjVGl0bGUsXG4gIGdldEFjY0Rlc2NyaXB0aW9uLFxuICBzZXRBY2NEZXNjcmlwdGlvbixcbiAgZ2V0Q29uZmlnOiAoKSA9PiBnZXRDb25maWcoKS5jNCxcbiAgY2xlYXIsXG4gIExJTkVUWVBFLFxuICBBUlJPV1RZUEUsXG4gIFBMQUNFTUVOVCxcbiAgc2V0VGl0bGUsXG4gIHNldEM0VHlwZVxuICAvLyBhcHBseSxcbn07XG5jb25zdCBkcmF3UmVjdCA9IGZ1bmN0aW9uKGVsZW0sIHJlY3REYXRhKSB7XG4gIHJldHVybiBkcmF3UmVjdCQxKGVsZW0sIHJlY3REYXRhKTtcbn07XG5jb25zdCBkcmF3SW1hZ2UgPSBmdW5jdGlvbihlbGVtLCB3aWR0aCwgaGVpZ2h0LCB4LCB5LCBsaW5rKSB7XG4gIGNvbnN0IGltYWdlRWxlbSA9IGVsZW0uYXBwZW5kKFwiaW1hZ2VcIik7XG4gIGltYWdlRWxlbS5hdHRyKFwid2lkdGhcIiwgd2lkdGgpO1xuICBpbWFnZUVsZW0uYXR0cihcImhlaWdodFwiLCBoZWlnaHQpO1xuICBpbWFnZUVsZW0uYXR0cihcInhcIiwgeCk7XG4gIGltYWdlRWxlbS5hdHRyKFwieVwiLCB5KTtcbiAgbGV0IHNhbml0aXplZExpbmsgPSBsaW5rLnN0YXJ0c1dpdGgoXCJkYXRhOmltYWdlL3BuZztiYXNlNjRcIikgPyBsaW5rIDogc2FuaXRpemVVcmwobGluayk7XG4gIGltYWdlRWxlbS5hdHRyKFwieGxpbms6aHJlZlwiLCBzYW5pdGl6ZWRMaW5rKTtcbn07XG5jb25zdCBkcmF3UmVscyQxID0gKGVsZW0sIHJlbHMyLCBjb25mMikgPT4ge1xuICBjb25zdCByZWxzRWxlbSA9IGVsZW0uYXBwZW5kKFwiZ1wiKTtcbiAgbGV0IGkgPSAwO1xuICBmb3IgKGxldCByZWwgb2YgcmVsczIpIHtcbiAgICBsZXQgdGV4dENvbG9yID0gcmVsLnRleHRDb2xvciA/IHJlbC50ZXh0Q29sb3IgOiBcIiM0NDQ0NDRcIjtcbiAgICBsZXQgc3Ryb2tlQ29sb3IgPSByZWwubGluZUNvbG9yID8gcmVsLmxpbmVDb2xvciA6IFwiIzQ0NDQ0NFwiO1xuICAgIGxldCBvZmZzZXRYID0gcmVsLm9mZnNldFggPyBwYXJzZUludChyZWwub2Zmc2V0WCkgOiAwO1xuICAgIGxldCBvZmZzZXRZID0gcmVsLm9mZnNldFkgPyBwYXJzZUludChyZWwub2Zmc2V0WSkgOiAwO1xuICAgIGxldCB1cmwgPSBcIlwiO1xuICAgIGlmIChpID09PSAwKSB7XG4gICAgICBsZXQgbGluZSA9IHJlbHNFbGVtLmFwcGVuZChcImxpbmVcIik7XG4gICAgICBsaW5lLmF0dHIoXCJ4MVwiLCByZWwuc3RhcnRQb2ludC54KTtcbiAgICAgIGxpbmUuYXR0cihcInkxXCIsIHJlbC5zdGFydFBvaW50LnkpO1xuICAgICAgbGluZS5hdHRyKFwieDJcIiwgcmVsLmVuZFBvaW50LngpO1xuICAgICAgbGluZS5hdHRyKFwieTJcIiwgcmVsLmVuZFBvaW50LnkpO1xuICAgICAgbGluZS5hdHRyKFwic3Ryb2tlLXdpZHRoXCIsIFwiMVwiKTtcbiAgICAgIGxpbmUuYXR0cihcInN0cm9rZVwiLCBzdHJva2VDb2xvcik7XG4gICAgICBsaW5lLnN0eWxlKFwiZmlsbFwiLCBcIm5vbmVcIik7XG4gICAgICBpZiAocmVsLnR5cGUgIT09IFwicmVsX2JcIikge1xuICAgICAgICBsaW5lLmF0dHIoXCJtYXJrZXItZW5kXCIsIFwidXJsKFwiICsgdXJsICsgXCIjYXJyb3doZWFkKVwiKTtcbiAgICAgIH1cbiAgICAgIGlmIChyZWwudHlwZSA9PT0gXCJiaXJlbFwiIHx8IHJlbC50eXBlID09PSBcInJlbF9iXCIpIHtcbiAgICAgICAgbGluZS5hdHRyKFwibWFya2VyLXN0YXJ0XCIsIFwidXJsKFwiICsgdXJsICsgXCIjYXJyb3dlbmQpXCIpO1xuICAgICAgfVxuICAgICAgaSA9IC0xO1xuICAgIH0gZWxzZSB7XG4gICAgICBsZXQgbGluZSA9IHJlbHNFbGVtLmFwcGVuZChcInBhdGhcIik7XG4gICAgICBsaW5lLmF0dHIoXCJmaWxsXCIsIFwibm9uZVwiKS5hdHRyKFwic3Ryb2tlLXdpZHRoXCIsIFwiMVwiKS5hdHRyKFwic3Ryb2tlXCIsIHN0cm9rZUNvbG9yKS5hdHRyKFxuICAgICAgICBcImRcIixcbiAgICAgICAgXCJNc3RhcnR4LHN0YXJ0eSBRY29udHJvbHgsY29udHJvbHkgc3RvcHgsc3RvcHkgXCIucmVwbGFjZUFsbChcInN0YXJ0eFwiLCByZWwuc3RhcnRQb2ludC54KS5yZXBsYWNlQWxsKFwic3RhcnR5XCIsIHJlbC5zdGFydFBvaW50LnkpLnJlcGxhY2VBbGwoXG4gICAgICAgICAgXCJjb250cm9seFwiLFxuICAgICAgICAgIHJlbC5zdGFydFBvaW50LnggKyAocmVsLmVuZFBvaW50LnggLSByZWwuc3RhcnRQb2ludC54KSAvIDIgLSAocmVsLmVuZFBvaW50LnggLSByZWwuc3RhcnRQb2ludC54KSAvIDRcbiAgICAgICAgKS5yZXBsYWNlQWxsKFwiY29udHJvbHlcIiwgcmVsLnN0YXJ0UG9pbnQueSArIChyZWwuZW5kUG9pbnQueSAtIHJlbC5zdGFydFBvaW50LnkpIC8gMikucmVwbGFjZUFsbChcInN0b3B4XCIsIHJlbC5lbmRQb2ludC54KS5yZXBsYWNlQWxsKFwic3RvcHlcIiwgcmVsLmVuZFBvaW50LnkpXG4gICAgICApO1xuICAgICAgaWYgKHJlbC50eXBlICE9PSBcInJlbF9iXCIpIHtcbiAgICAgICAgbGluZS5hdHRyKFwibWFya2VyLWVuZFwiLCBcInVybChcIiArIHVybCArIFwiI2Fycm93aGVhZClcIik7XG4gICAgICB9XG4gICAgICBpZiAocmVsLnR5cGUgPT09IFwiYmlyZWxcIiB8fCByZWwudHlwZSA9PT0gXCJyZWxfYlwiKSB7XG4gICAgICAgIGxpbmUuYXR0cihcIm1hcmtlci1zdGFydFwiLCBcInVybChcIiArIHVybCArIFwiI2Fycm93ZW5kKVwiKTtcbiAgICAgIH1cbiAgICB9XG4gICAgbGV0IG1lc3NhZ2VDb25mID0gY29uZjIubWVzc2FnZUZvbnQoKTtcbiAgICBfZHJhd1RleHRDYW5kaWRhdGVGdW5jKGNvbmYyKShcbiAgICAgIHJlbC5sYWJlbC50ZXh0LFxuICAgICAgcmVsc0VsZW0sXG4gICAgICBNYXRoLm1pbihyZWwuc3RhcnRQb2ludC54LCByZWwuZW5kUG9pbnQueCkgKyBNYXRoLmFicyhyZWwuZW5kUG9pbnQueCAtIHJlbC5zdGFydFBvaW50LngpIC8gMiArIG9mZnNldFgsXG4gICAgICBNYXRoLm1pbihyZWwuc3RhcnRQb2ludC55LCByZWwuZW5kUG9pbnQueSkgKyBNYXRoLmFicyhyZWwuZW5kUG9pbnQueSAtIHJlbC5zdGFydFBvaW50LnkpIC8gMiArIG9mZnNldFksXG4gICAgICByZWwubGFiZWwud2lkdGgsXG4gICAgICByZWwubGFiZWwuaGVpZ2h0LFxuICAgICAgeyBmaWxsOiB0ZXh0Q29sb3IgfSxcbiAgICAgIG1lc3NhZ2VDb25mXG4gICAgKTtcbiAgICBpZiAocmVsLnRlY2huICYmIHJlbC50ZWNobi50ZXh0ICE9PSBcIlwiKSB7XG4gICAgICBtZXNzYWdlQ29uZiA9IGNvbmYyLm1lc3NhZ2VGb250KCk7XG4gICAgICBfZHJhd1RleHRDYW5kaWRhdGVGdW5jKGNvbmYyKShcbiAgICAgICAgXCJbXCIgKyByZWwudGVjaG4udGV4dCArIFwiXVwiLFxuICAgICAgICByZWxzRWxlbSxcbiAgICAgICAgTWF0aC5taW4ocmVsLnN0YXJ0UG9pbnQueCwgcmVsLmVuZFBvaW50LngpICsgTWF0aC5hYnMocmVsLmVuZFBvaW50LnggLSByZWwuc3RhcnRQb2ludC54KSAvIDIgKyBvZmZzZXRYLFxuICAgICAgICBNYXRoLm1pbihyZWwuc3RhcnRQb2ludC55LCByZWwuZW5kUG9pbnQueSkgKyBNYXRoLmFicyhyZWwuZW5kUG9pbnQueSAtIHJlbC5zdGFydFBvaW50LnkpIC8gMiArIGNvbmYyLm1lc3NhZ2VGb250U2l6ZSArIDUgKyBvZmZzZXRZLFxuICAgICAgICBNYXRoLm1heChyZWwubGFiZWwud2lkdGgsIHJlbC50ZWNobi53aWR0aCksXG4gICAgICAgIHJlbC50ZWNobi5oZWlnaHQsXG4gICAgICAgIHsgZmlsbDogdGV4dENvbG9yLCBcImZvbnQtc3R5bGVcIjogXCJpdGFsaWNcIiB9LFxuICAgICAgICBtZXNzYWdlQ29uZlxuICAgICAgKTtcbiAgICB9XG4gIH1cbn07XG5jb25zdCBkcmF3Qm91bmRhcnkkMSA9IGZ1bmN0aW9uKGVsZW0sIGJvdW5kYXJ5LCBjb25mMikge1xuICBjb25zdCBib3VuZGFyeUVsZW0gPSBlbGVtLmFwcGVuZChcImdcIik7XG4gIGxldCBmaWxsQ29sb3IgPSBib3VuZGFyeS5iZ0NvbG9yID8gYm91bmRhcnkuYmdDb2xvciA6IFwibm9uZVwiO1xuICBsZXQgc3Ryb2tlQ29sb3IgPSBib3VuZGFyeS5ib3JkZXJDb2xvciA/IGJvdW5kYXJ5LmJvcmRlckNvbG9yIDogXCIjNDQ0NDQ0XCI7XG4gIGxldCBmb250Q29sb3IgPSBib3VuZGFyeS5mb250Q29sb3IgPyBib3VuZGFyeS5mb250Q29sb3IgOiBcImJsYWNrXCI7XG4gIGxldCBhdHRyc1ZhbHVlID0geyBcInN0cm9rZS13aWR0aFwiOiAxLCBcInN0cm9rZS1kYXNoYXJyYXlcIjogXCI3LjAsNy4wXCIgfTtcbiAgaWYgKGJvdW5kYXJ5Lm5vZGVUeXBlKSB7XG4gICAgYXR0cnNWYWx1ZSA9IHsgXCJzdHJva2Utd2lkdGhcIjogMSB9O1xuICB9XG4gIGxldCByZWN0RGF0YSA9IHtcbiAgICB4OiBib3VuZGFyeS54LFxuICAgIHk6IGJvdW5kYXJ5LnksXG4gICAgZmlsbDogZmlsbENvbG9yLFxuICAgIHN0cm9rZTogc3Ryb2tlQ29sb3IsXG4gICAgd2lkdGg6IGJvdW5kYXJ5LndpZHRoLFxuICAgIGhlaWdodDogYm91bmRhcnkuaGVpZ2h0LFxuICAgIHJ4OiAyLjUsXG4gICAgcnk6IDIuNSxcbiAgICBhdHRyczogYXR0cnNWYWx1ZVxuICB9O1xuICBkcmF3UmVjdChib3VuZGFyeUVsZW0sIHJlY3REYXRhKTtcbiAgbGV0IGJvdW5kYXJ5Q29uZiA9IGNvbmYyLmJvdW5kYXJ5Rm9udCgpO1xuICBib3VuZGFyeUNvbmYuZm9udFdlaWdodCA9IFwiYm9sZFwiO1xuICBib3VuZGFyeUNvbmYuZm9udFNpemUgPSBib3VuZGFyeUNvbmYuZm9udFNpemUgKyAyO1xuICBib3VuZGFyeUNvbmYuZm9udENvbG9yID0gZm9udENvbG9yO1xuICBfZHJhd1RleHRDYW5kaWRhdGVGdW5jKGNvbmYyKShcbiAgICBib3VuZGFyeS5sYWJlbC50ZXh0LFxuICAgIGJvdW5kYXJ5RWxlbSxcbiAgICBib3VuZGFyeS54LFxuICAgIGJvdW5kYXJ5LnkgKyBib3VuZGFyeS5sYWJlbC5ZLFxuICAgIGJvdW5kYXJ5LndpZHRoLFxuICAgIGJvdW5kYXJ5LmhlaWdodCxcbiAgICB7IGZpbGw6IFwiIzQ0NDQ0NFwiIH0sXG4gICAgYm91bmRhcnlDb25mXG4gICk7XG4gIGlmIChib3VuZGFyeS50eXBlICYmIGJvdW5kYXJ5LnR5cGUudGV4dCAhPT0gXCJcIikge1xuICAgIGJvdW5kYXJ5Q29uZiA9IGNvbmYyLmJvdW5kYXJ5Rm9udCgpO1xuICAgIGJvdW5kYXJ5Q29uZi5mb250Q29sb3IgPSBmb250Q29sb3I7XG4gICAgX2RyYXdUZXh0Q2FuZGlkYXRlRnVuYyhjb25mMikoXG4gICAgICBib3VuZGFyeS50eXBlLnRleHQsXG4gICAgICBib3VuZGFyeUVsZW0sXG4gICAgICBib3VuZGFyeS54LFxuICAgICAgYm91bmRhcnkueSArIGJvdW5kYXJ5LnR5cGUuWSxcbiAgICAgIGJvdW5kYXJ5LndpZHRoLFxuICAgICAgYm91bmRhcnkuaGVpZ2h0LFxuICAgICAgeyBmaWxsOiBcIiM0NDQ0NDRcIiB9LFxuICAgICAgYm91bmRhcnlDb25mXG4gICAgKTtcbiAgfVxuICBpZiAoYm91bmRhcnkuZGVzY3IgJiYgYm91bmRhcnkuZGVzY3IudGV4dCAhPT0gXCJcIikge1xuICAgIGJvdW5kYXJ5Q29uZiA9IGNvbmYyLmJvdW5kYXJ5Rm9udCgpO1xuICAgIGJvdW5kYXJ5Q29uZi5mb250U2l6ZSA9IGJvdW5kYXJ5Q29uZi5mb250U2l6ZSAtIDI7XG4gICAgYm91bmRhcnlDb25mLmZvbnRDb2xvciA9IGZvbnRDb2xvcjtcbiAgICBfZHJhd1RleHRDYW5kaWRhdGVGdW5jKGNvbmYyKShcbiAgICAgIGJvdW5kYXJ5LmRlc2NyLnRleHQsXG4gICAgICBib3VuZGFyeUVsZW0sXG4gICAgICBib3VuZGFyeS54LFxuICAgICAgYm91bmRhcnkueSArIGJvdW5kYXJ5LmRlc2NyLlksXG4gICAgICBib3VuZGFyeS53aWR0aCxcbiAgICAgIGJvdW5kYXJ5LmhlaWdodCxcbiAgICAgIHsgZmlsbDogXCIjNDQ0NDQ0XCIgfSxcbiAgICAgIGJvdW5kYXJ5Q29uZlxuICAgICk7XG4gIH1cbn07XG5jb25zdCBkcmF3QzRTaGFwZSA9IGZ1bmN0aW9uKGVsZW0sIGM0U2hhcGUsIGNvbmYyKSB7XG4gIHZhciBfYTtcbiAgbGV0IGZpbGxDb2xvciA9IGM0U2hhcGUuYmdDb2xvciA/IGM0U2hhcGUuYmdDb2xvciA6IGNvbmYyW2M0U2hhcGUudHlwZUM0U2hhcGUudGV4dCArIFwiX2JnX2NvbG9yXCJdO1xuICBsZXQgc3Ryb2tlQ29sb3IgPSBjNFNoYXBlLmJvcmRlckNvbG9yID8gYzRTaGFwZS5ib3JkZXJDb2xvciA6IGNvbmYyW2M0U2hhcGUudHlwZUM0U2hhcGUudGV4dCArIFwiX2JvcmRlcl9jb2xvclwiXTtcbiAgbGV0IGZvbnRDb2xvciA9IGM0U2hhcGUuZm9udENvbG9yID8gYzRTaGFwZS5mb250Q29sb3IgOiBcIiNGRkZGRkZcIjtcbiAgbGV0IHBlcnNvbkltZyA9IFwiZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFEQUFBQUF3Q0FJQUFBRFlZRzdRQUFBQ0QwbEVRVlI0WHUyWW9VNEVNUkNHVCs0ajhBaThBaGFINFFIZ0FValF1Rk1FQ1VnTUlVZ3dKQWdNaGdRc0FZVWlKQ2lRSUJCWStFSVRzamZUZG1lNlYyNHY0Yzh2eUdiYitaak90TjBiTmN2alFYbWtIODNXdllCV3RvNlBMbTZ2N3A3dUgxL3cyZlhEK1BCeWNYMVB2MmwzSWREbS92bjd4K2RYUWlBdWJSem9VUmE3Z1JaV2QwaUdSSWlKYk9uaG5mWUJRWk5Kak5idXlZMmVKRzhma0RFM2JiRzRlcDZNSFVBc2dZeG1FM25WczZWc0JXSlNHY2NzT2xGUG1MSVZpTXpMT0I3cENWTzJBdEhKTW9oSDdGaDZ6cWl0UUs3bTBySnZBVllnR2NFcGUvL1BMZER6NjVzTTRwRjlON0lDY1hES0lCNU52Nmo3dEQwTm9TZE0yUXJVOUdnMGV3RTFMcUJoSFIzQkJkdmoydmFwbmlkakh4RC9xNnZkN1B2aHIzMUF3Y1k4ZVhNVFhBS0VDWlpKRlh1RXEyN2FMZ1FLNXVMTW9oQ2VuR0d1R2V3T3hTakJ2WUJxZUc2QitOcWlibGdnZGpuYytaWER5K0ZORnBGenc3Nk8zVUJBUk91WGg2Rm9pQWNmNWc5ZVR2VWd6eTBuV2c2SThjWEhSVXBnNWJPVkJDbytLRHBGYWpPZjIzR2dQbWU3UlNRK2xhY0lFTlVnSjZnZzFrNkhqZ09scW5McWlwNHRFdWh2MGhORU1YVUQwY2x5WEUzcDZwWkEwUzJubnZUbFh3TEpFWldsYjdjVFFIMStVU2dUTjRWaEFlbm0vd2VhMU9DQU9tcW82ZkUxV0NiOVdTS0JhaCtyYlVXUFdBbUUyUnZrMEFwaUI0NWVPeU5BelU4eGNUdmo4S3ZrS0VvT2FJWWVITkEzWnV5Z0F2Rk1VTzBBQUFBQVNVVk9SSzVDWUlJPVwiO1xuICBzd2l0Y2ggKGM0U2hhcGUudHlwZUM0U2hhcGUudGV4dCkge1xuICAgIGNhc2UgXCJwZXJzb25cIjpcbiAgICAgIHBlcnNvbkltZyA9IFwiZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFEQUFBQUF3Q0FJQUFBRFlZRzdRQUFBQ0QwbEVRVlI0WHUyWW9VNEVNUkNHVCs0ajhBaThBaGFINFFIZ0FValF1Rk1FQ1VnTUlVZ3dKQWdNaGdRc0FZVWlKQ2lRSUJCWStFSVRzamZUZG1lNlYyNHY0Yzh2eUdiYitaak90TjBiTmN2alFYbWtIODNXdllCV3RvNlBMbTZ2N3A3dUgxL3cyZlhEK1BCeWNYMVB2MmwzSWREbS92bjd4K2RYUWlBdWJSem9VUmE3Z1JaV2QwaUdSSWlKYk9uaG5mWUJRWk5Kak5idXlZMmVKRzhma0RFM2JiRzRlcDZNSFVBc2dZeG1FM25WczZWc0JXSlNHY2NzT2xGUG1MSVZpTXpMT0I3cENWTzJBdEhKTW9oSDdGaDZ6cWl0UUs3bTBySnZBVllnR2NFcGUvL1BMZER6NjVzTTRwRjlON0lDY1hES0lCNU52Nmo3dEQwTm9TZE0yUXJVOUdnMGV3RTFMcUJoSFIzQkJkdmoydmFwbmlkakh4RC9xNnZkN1B2aHIzMUF3Y1k4ZVhNVFhBS0VDWlpKRlh1RXEyN2FMZ1FLNXVMTW9oQ2VuR0d1R2V3T3hTakJ2WUJxZUc2QitOcWlibGdnZGpuYytaWER5K0ZORnBGenc3Nk8zVUJBUk91WGg2Rm9pQWNmNWc5ZVR2VWd6eTBuV2c2SThjWEhSVXBnNWJPVkJDbytLRHBGYWpPZjIzR2dQbWU3UlNRK2xhY0lFTlVnSjZnZzFrNkhqZ09scW5McWlwNHRFdWh2MGhORU1YVUQwY2x5WEUzcDZwWkEwUzJubnZUbFh3TEpFWldsYjdjVFFIMStVU2dUTjRWaEFlbm0vd2VhMU9DQU9tcW82ZkUxV0NiOVdTS0JhaCtyYlVXUFdBbUUyUnZrMEFwaUI0NWVPeU5BelU4eGNUdmo4S3ZrS0VvT2FJWWVITkEzWnV5Z0F2Rk1VTzBBQUFBQVNVVk9SSzVDWUlJPVwiO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSBcImV4dGVybmFsX3BlcnNvblwiOlxuICAgICAgcGVyc29uSW1nID0gXCJkYXRhOmltYWdlL3BuZztiYXNlNjQsaVZCT1J3MEtHZ29BQUFBTlNVaEVVZ0FBQURBQUFBQXdDQUlBQUFEWVlHN1FBQUFCNkVsRVFWUjRYdTJZTFkrRU1CQ0c5K2RXcjBhajBXZzBHbzFHbzArajhYZHYydVRDdnYxZ3B0MGViSEtQdWhEYWVXNDYwNVo5bUp2eDRBZFhVeVRVZGQwOHordTZmbG1XWlJuSHNXa2FmazlEcHRBd0RQdStmMGVBWXR1MlBFYUdXdWo1ZkNJWnJCQUMyZUxCQW5SQ3NFa2t4bWVhSnA3aURKMlFNRGRIc0xnOFN4S0ZFSmFBbzhsQVhubXVPRkloVE1weHhLQVRlYm80VWlGa251Tm80T25pU0lYUXlSeEVBM1lzbmpHQ1ZFalZYRDd5TFVBcXhCR1V5UHYvWTRXMmJlTWdHdVM3a1ZRSUJ5Y0gwZkQrb2k1cGV6UUVUeGRIS21RS0drMWVRRVlsZEsranc1R3hQZlo5ejdNazBRbmhmMVcxbTN3Ly9FVW41QkRtU1pzYlI0NFFRTEJFcXJCSHFPcm1TS2FRQXhkbkxBckNyeFpjTTdBN1pLczRpb1JxOExGQytOcEMzV0NCSnN2cFZ3NWVkbTlpRVhGdXlOZnhYQWdTd2ZyRlExYzBpTmRhOEFkZWp2VWdua3RPdEpRUXhtY2ZGekdnbGM1V1ZDajdvRGdGcVUxOGJvZUZTczUyQ1VoOExFOEJJVlFEVDFBQnJCMEh0Z1NFWWxYNWRvSm5Dd3Y5VFhvY0tDYUtibndoZERLUHE0bGYzU3dVM0hMcTRWLytXWWhIVk1hLzNiNElsZnlpa0FkdUNrY0JjN21RMy96L1FxL2NUdWlraGt6QjEyQWUvbWNKQzlVK1ZvOEVqMWdXQXRnYmVHZ0ZzQU1IcjUwQklXT0xDYmV6dmhwQkZVZFk2RUp1Si9RRFcwWG9NWDYwelowQUFBQUFTVVZPUks1Q1lJST1cIjtcbiAgICAgIGJyZWFrO1xuICB9XG4gIGNvbnN0IGM0U2hhcGVFbGVtID0gZWxlbS5hcHBlbmQoXCJnXCIpO1xuICBjNFNoYXBlRWxlbS5hdHRyKFwiY2xhc3NcIiwgXCJwZXJzb24tbWFuXCIpO1xuICBjb25zdCByZWN0ID0gZ2V0Tm90ZVJlY3QoKTtcbiAgc3dpdGNoIChjNFNoYXBlLnR5cGVDNFNoYXBlLnRleHQpIHtcbiAgICBjYXNlIFwicGVyc29uXCI6XG4gICAgY2FzZSBcImV4dGVybmFsX3BlcnNvblwiOlxuICAgIGNhc2UgXCJzeXN0ZW1cIjpcbiAgICBjYXNlIFwiZXh0ZXJuYWxfc3lzdGVtXCI6XG4gICAgY2FzZSBcImNvbnRhaW5lclwiOlxuICAgIGNhc2UgXCJleHRlcm5hbF9jb250YWluZXJcIjpcbiAgICBjYXNlIFwiY29tcG9uZW50XCI6XG4gICAgY2FzZSBcImV4dGVybmFsX2NvbXBvbmVudFwiOlxuICAgICAgcmVjdC54ID0gYzRTaGFwZS54O1xuICAgICAgcmVjdC55ID0gYzRTaGFwZS55O1xuICAgICAgcmVjdC5maWxsID0gZmlsbENvbG9yO1xuICAgICAgcmVjdC53aWR0aCA9IGM0U2hhcGUud2lkdGg7XG4gICAgICByZWN0LmhlaWdodCA9IGM0U2hhcGUuaGVpZ2h0O1xuICAgICAgcmVjdC5zdHJva2UgPSBzdHJva2VDb2xvcjtcbiAgICAgIHJlY3QucnggPSAyLjU7XG4gICAgICByZWN0LnJ5ID0gMi41O1xuICAgICAgcmVjdC5hdHRycyA9IHsgXCJzdHJva2Utd2lkdGhcIjogMC41IH07XG4gICAgICBkcmF3UmVjdChjNFNoYXBlRWxlbSwgcmVjdCk7XG4gICAgICBicmVhaztcbiAgICBjYXNlIFwic3lzdGVtX2RiXCI6XG4gICAgY2FzZSBcImV4dGVybmFsX3N5c3RlbV9kYlwiOlxuICAgIGNhc2UgXCJjb250YWluZXJfZGJcIjpcbiAgICBjYXNlIFwiZXh0ZXJuYWxfY29udGFpbmVyX2RiXCI6XG4gICAgY2FzZSBcImNvbXBvbmVudF9kYlwiOlxuICAgIGNhc2UgXCJleHRlcm5hbF9jb21wb25lbnRfZGJcIjpcbiAgICAgIGM0U2hhcGVFbGVtLmFwcGVuZChcInBhdGhcIikuYXR0cihcImZpbGxcIiwgZmlsbENvbG9yKS5hdHRyKFwic3Ryb2tlLXdpZHRoXCIsIFwiMC41XCIpLmF0dHIoXCJzdHJva2VcIiwgc3Ryb2tlQ29sb3IpLmF0dHIoXG4gICAgICAgIFwiZFwiLFxuICAgICAgICBcIk1zdGFydHgsc3RhcnR5YzAsLTEwIGhhbGYsLTEwIGhhbGYsLTEwYzAsMCBoYWxmLDAgaGFsZiwxMGwwLGhlaWdodGMwLDEwIC1oYWxmLDEwIC1oYWxmLDEwYzAsMCAtaGFsZiwwIC1oYWxmLC0xMGwwLC1oZWlnaHRcIi5yZXBsYWNlQWxsKFwic3RhcnR4XCIsIGM0U2hhcGUueCkucmVwbGFjZUFsbChcInN0YXJ0eVwiLCBjNFNoYXBlLnkpLnJlcGxhY2VBbGwoXCJoYWxmXCIsIGM0U2hhcGUud2lkdGggLyAyKS5yZXBsYWNlQWxsKFwiaGVpZ2h0XCIsIGM0U2hhcGUuaGVpZ2h0KVxuICAgICAgKTtcbiAgICAgIGM0U2hhcGVFbGVtLmFwcGVuZChcInBhdGhcIikuYXR0cihcImZpbGxcIiwgXCJub25lXCIpLmF0dHIoXCJzdHJva2Utd2lkdGhcIiwgXCIwLjVcIikuYXR0cihcInN0cm9rZVwiLCBzdHJva2VDb2xvcikuYXR0cihcbiAgICAgICAgXCJkXCIsXG4gICAgICAgIFwiTXN0YXJ0eCxzdGFydHljMCwxMCBoYWxmLDEwIGhhbGYsMTBjMCwwIGhhbGYsMCBoYWxmLC0xMFwiLnJlcGxhY2VBbGwoXCJzdGFydHhcIiwgYzRTaGFwZS54KS5yZXBsYWNlQWxsKFwic3RhcnR5XCIsIGM0U2hhcGUueSkucmVwbGFjZUFsbChcImhhbGZcIiwgYzRTaGFwZS53aWR0aCAvIDIpXG4gICAgICApO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSBcInN5c3RlbV9xdWV1ZVwiOlxuICAgIGNhc2UgXCJleHRlcm5hbF9zeXN0ZW1fcXVldWVcIjpcbiAgICBjYXNlIFwiY29udGFpbmVyX3F1ZXVlXCI6XG4gICAgY2FzZSBcImV4dGVybmFsX2NvbnRhaW5lcl9xdWV1ZVwiOlxuICAgIGNhc2UgXCJjb21wb25lbnRfcXVldWVcIjpcbiAgICBjYXNlIFwiZXh0ZXJuYWxfY29tcG9uZW50X3F1ZXVlXCI6XG4gICAgICBjNFNoYXBlRWxlbS5hcHBlbmQoXCJwYXRoXCIpLmF0dHIoXCJmaWxsXCIsIGZpbGxDb2xvcikuYXR0cihcInN0cm9rZS13aWR0aFwiLCBcIjAuNVwiKS5hdHRyKFwic3Ryb2tlXCIsIHN0cm9rZUNvbG9yKS5hdHRyKFxuICAgICAgICBcImRcIixcbiAgICAgICAgXCJNc3RhcnR4LHN0YXJ0eWx3aWR0aCwwYzUsMCA1LGhhbGYgNSxoYWxmYzAsMCAwLGhhbGYgLTUsaGFsZmwtd2lkdGgsMGMtNSwwIC01LC1oYWxmIC01LC1oYWxmYzAsMCAwLC1oYWxmIDUsLWhhbGZcIi5yZXBsYWNlQWxsKFwic3RhcnR4XCIsIGM0U2hhcGUueCkucmVwbGFjZUFsbChcInN0YXJ0eVwiLCBjNFNoYXBlLnkpLnJlcGxhY2VBbGwoXCJ3aWR0aFwiLCBjNFNoYXBlLndpZHRoKS5yZXBsYWNlQWxsKFwiaGFsZlwiLCBjNFNoYXBlLmhlaWdodCAvIDIpXG4gICAgICApO1xuICAgICAgYzRTaGFwZUVsZW0uYXBwZW5kKFwicGF0aFwiKS5hdHRyKFwiZmlsbFwiLCBcIm5vbmVcIikuYXR0cihcInN0cm9rZS13aWR0aFwiLCBcIjAuNVwiKS5hdHRyKFwic3Ryb2tlXCIsIHN0cm9rZUNvbG9yKS5hdHRyKFxuICAgICAgICBcImRcIixcbiAgICAgICAgXCJNc3RhcnR4LHN0YXJ0eWMtNSwwIC01LGhhbGYgLTUsaGFsZmMwLGhhbGYgNSxoYWxmIDUsaGFsZlwiLnJlcGxhY2VBbGwoXCJzdGFydHhcIiwgYzRTaGFwZS54ICsgYzRTaGFwZS53aWR0aCkucmVwbGFjZUFsbChcInN0YXJ0eVwiLCBjNFNoYXBlLnkpLnJlcGxhY2VBbGwoXCJoYWxmXCIsIGM0U2hhcGUuaGVpZ2h0IC8gMilcbiAgICAgICk7XG4gICAgICBicmVhaztcbiAgfVxuICBsZXQgYzRTaGFwZUZvbnRDb25mID0gZ2V0QzRTaGFwZUZvbnQoY29uZjIsIGM0U2hhcGUudHlwZUM0U2hhcGUudGV4dCk7XG4gIGM0U2hhcGVFbGVtLmFwcGVuZChcInRleHRcIikuYXR0cihcImZpbGxcIiwgZm9udENvbG9yKS5hdHRyKFwiZm9udC1mYW1pbHlcIiwgYzRTaGFwZUZvbnRDb25mLmZvbnRGYW1pbHkpLmF0dHIoXCJmb250LXNpemVcIiwgYzRTaGFwZUZvbnRDb25mLmZvbnRTaXplIC0gMikuYXR0cihcImZvbnQtc3R5bGVcIiwgXCJpdGFsaWNcIikuYXR0cihcImxlbmd0aEFkanVzdFwiLCBcInNwYWNpbmdcIikuYXR0cihcInRleHRMZW5ndGhcIiwgYzRTaGFwZS50eXBlQzRTaGFwZS53aWR0aCkuYXR0cihcInhcIiwgYzRTaGFwZS54ICsgYzRTaGFwZS53aWR0aCAvIDIgLSBjNFNoYXBlLnR5cGVDNFNoYXBlLndpZHRoIC8gMikuYXR0cihcInlcIiwgYzRTaGFwZS55ICsgYzRTaGFwZS50eXBlQzRTaGFwZS5ZKS50ZXh0KFwiPDxcIiArIGM0U2hhcGUudHlwZUM0U2hhcGUudGV4dCArIFwiPj5cIik7XG4gIHN3aXRjaCAoYzRTaGFwZS50eXBlQzRTaGFwZS50ZXh0KSB7XG4gICAgY2FzZSBcInBlcnNvblwiOlxuICAgIGNhc2UgXCJleHRlcm5hbF9wZXJzb25cIjpcbiAgICAgIGRyYXdJbWFnZShcbiAgICAgICAgYzRTaGFwZUVsZW0sXG4gICAgICAgIDQ4LFxuICAgICAgICA0OCxcbiAgICAgICAgYzRTaGFwZS54ICsgYzRTaGFwZS53aWR0aCAvIDIgLSAyNCxcbiAgICAgICAgYzRTaGFwZS55ICsgYzRTaGFwZS5pbWFnZS5ZLFxuICAgICAgICBwZXJzb25JbWdcbiAgICAgICk7XG4gICAgICBicmVhaztcbiAgfVxuICBsZXQgdGV4dEZvbnRDb25mID0gY29uZjJbYzRTaGFwZS50eXBlQzRTaGFwZS50ZXh0ICsgXCJGb250XCJdKCk7XG4gIHRleHRGb250Q29uZi5mb250V2VpZ2h0ID0gXCJib2xkXCI7XG4gIHRleHRGb250Q29uZi5mb250U2l6ZSA9IHRleHRGb250Q29uZi5mb250U2l6ZSArIDI7XG4gIHRleHRGb250Q29uZi5mb250Q29sb3IgPSBmb250Q29sb3I7XG4gIF9kcmF3VGV4dENhbmRpZGF0ZUZ1bmMoY29uZjIpKFxuICAgIGM0U2hhcGUubGFiZWwudGV4dCxcbiAgICBjNFNoYXBlRWxlbSxcbiAgICBjNFNoYXBlLngsXG4gICAgYzRTaGFwZS55ICsgYzRTaGFwZS5sYWJlbC5ZLFxuICAgIGM0U2hhcGUud2lkdGgsXG4gICAgYzRTaGFwZS5oZWlnaHQsXG4gICAgeyBmaWxsOiBmb250Q29sb3IgfSxcbiAgICB0ZXh0Rm9udENvbmZcbiAgKTtcbiAgdGV4dEZvbnRDb25mID0gY29uZjJbYzRTaGFwZS50eXBlQzRTaGFwZS50ZXh0ICsgXCJGb250XCJdKCk7XG4gIHRleHRGb250Q29uZi5mb250Q29sb3IgPSBmb250Q29sb3I7XG4gIGlmIChjNFNoYXBlLnRlY2huICYmICgoX2EgPSBjNFNoYXBlLnRlY2huKSA9PSBudWxsID8gdm9pZCAwIDogX2EudGV4dCkgIT09IFwiXCIpIHtcbiAgICBfZHJhd1RleHRDYW5kaWRhdGVGdW5jKGNvbmYyKShcbiAgICAgIGM0U2hhcGUudGVjaG4udGV4dCxcbiAgICAgIGM0U2hhcGVFbGVtLFxuICAgICAgYzRTaGFwZS54LFxuICAgICAgYzRTaGFwZS55ICsgYzRTaGFwZS50ZWNobi5ZLFxuICAgICAgYzRTaGFwZS53aWR0aCxcbiAgICAgIGM0U2hhcGUuaGVpZ2h0LFxuICAgICAgeyBmaWxsOiBmb250Q29sb3IsIFwiZm9udC1zdHlsZVwiOiBcIml0YWxpY1wiIH0sXG4gICAgICB0ZXh0Rm9udENvbmZcbiAgICApO1xuICB9IGVsc2UgaWYgKGM0U2hhcGUudHlwZSAmJiBjNFNoYXBlLnR5cGUudGV4dCAhPT0gXCJcIikge1xuICAgIF9kcmF3VGV4dENhbmRpZGF0ZUZ1bmMoY29uZjIpKFxuICAgICAgYzRTaGFwZS50eXBlLnRleHQsXG4gICAgICBjNFNoYXBlRWxlbSxcbiAgICAgIGM0U2hhcGUueCxcbiAgICAgIGM0U2hhcGUueSArIGM0U2hhcGUudHlwZS5ZLFxuICAgICAgYzRTaGFwZS53aWR0aCxcbiAgICAgIGM0U2hhcGUuaGVpZ2h0LFxuICAgICAgeyBmaWxsOiBmb250Q29sb3IsIFwiZm9udC1zdHlsZVwiOiBcIml0YWxpY1wiIH0sXG4gICAgICB0ZXh0Rm9udENvbmZcbiAgICApO1xuICB9XG4gIGlmIChjNFNoYXBlLmRlc2NyICYmIGM0U2hhcGUuZGVzY3IudGV4dCAhPT0gXCJcIikge1xuICAgIHRleHRGb250Q29uZiA9IGNvbmYyLnBlcnNvbkZvbnQoKTtcbiAgICB0ZXh0Rm9udENvbmYuZm9udENvbG9yID0gZm9udENvbG9yO1xuICAgIF9kcmF3VGV4dENhbmRpZGF0ZUZ1bmMoY29uZjIpKFxuICAgICAgYzRTaGFwZS5kZXNjci50ZXh0LFxuICAgICAgYzRTaGFwZUVsZW0sXG4gICAgICBjNFNoYXBlLngsXG4gICAgICBjNFNoYXBlLnkgKyBjNFNoYXBlLmRlc2NyLlksXG4gICAgICBjNFNoYXBlLndpZHRoLFxuICAgICAgYzRTaGFwZS5oZWlnaHQsXG4gICAgICB7IGZpbGw6IGZvbnRDb2xvciB9LFxuICAgICAgdGV4dEZvbnRDb25mXG4gICAgKTtcbiAgfVxuICByZXR1cm4gYzRTaGFwZS5oZWlnaHQ7XG59O1xuY29uc3QgaW5zZXJ0RGF0YWJhc2VJY29uID0gZnVuY3Rpb24oZWxlbSkge1xuICBlbGVtLmFwcGVuZChcImRlZnNcIikuYXBwZW5kKFwic3ltYm9sXCIpLmF0dHIoXCJpZFwiLCBcImRhdGFiYXNlXCIpLmF0dHIoXCJmaWxsLXJ1bGVcIiwgXCJldmVub2RkXCIpLmF0dHIoXCJjbGlwLXJ1bGVcIiwgXCJldmVub2RkXCIpLmFwcGVuZChcInBhdGhcIikuYXR0cihcInRyYW5zZm9ybVwiLCBcInNjYWxlKC41KVwiKS5hdHRyKFxuICAgIFwiZFwiLFxuICAgIFwiTTEyLjI1OC4wMDFsLjI1Ni4wMDQuMjU1LjAwNS4yNTMuMDA4LjI1MS4wMS4yNDkuMDEyLjI0Ny4wMTUuMjQ2LjAxNi4yNDIuMDE5LjI0MS4wMi4yMzkuMDIzLjIzNi4wMjQuMjMzLjAyNy4yMzEuMDI4LjIyOS4wMzEuMjI1LjAzMi4yMjMuMDM0LjIyLjAzNi4yMTcuMDM4LjIxNC4wNC4yMTEuMDQxLjIwOC4wNDMuMjA1LjA0NS4yMDEuMDQ2LjE5OC4wNDguMTk0LjA1LjE5MS4wNTEuMTg3LjA1My4xODMuMDU0LjE4LjA1Ni4xNzUuMDU3LjE3Mi4wNTkuMTY4LjA2LjE2My4wNjEuMTYuMDYzLjE1NS4wNjQuMTUuMDY2LjA3NC4wMzMuMDczLjAzMy4wNzEuMDM0LjA3LjAzNC4wNjkuMDM1LjA2OC4wMzUuMDY3LjAzNS4wNjYuMDM1LjA2NC4wMzYuMDY0LjAzNi4wNjIuMDM2LjA2LjAzNi4wNi4wMzcuMDU4LjAzNy4wNTguMDM3LjA1NS4wMzguMDU1LjAzOC4wNTMuMDM4LjA1Mi4wMzguMDUxLjAzOS4wNS4wMzkuMDQ4LjAzOS4wNDcuMDM5LjA0NS4wNC4wNDQuMDQuMDQzLjA0LjA0MS4wNC4wNC4wNDEuMDM5LjA0MS4wMzcuMDQxLjAzNi4wNDEuMDM0LjA0MS4wMzMuMDQyLjAzMi4wNDIuMDMuMDQyLjAyOS4wNDIuMDI3LjA0Mi4wMjYuMDQzLjAyNC4wNDMuMDIzLjA0My4wMjEuMDQzLjAyLjA0My4wMTguMDQ0LjAxNy4wNDMuMDE1LjA0NC4wMTMuMDQ0LjAxMi4wNDQuMDExLjA0NS4wMDkuMDQ0LjAwNy4wNDUuMDA2LjA0NS4wMDQuMDQ1LjAwMi4wNDUuMDAxLjA0NXYxN2wtLjAwMS4wNDUtLjAwMi4wNDUtLjAwNC4wNDUtLjAwNi4wNDUtLjAwNy4wNDUtLjAwOS4wNDQtLjAxMS4wNDUtLjAxMi4wNDQtLjAxMy4wNDQtLjAxNS4wNDQtLjAxNy4wNDMtLjAxOC4wNDQtLjAyLjA0My0uMDIxLjA0My0uMDIzLjA0My0uMDI0LjA0My0uMDI2LjA0My0uMDI3LjA0Mi0uMDI5LjA0Mi0uMDMuMDQyLS4wMzIuMDQyLS4wMzMuMDQyLS4wMzQuMDQxLS4wMzYuMDQxLS4wMzcuMDQxLS4wMzkuMDQxLS4wNC4wNDEtLjA0MS4wNC0uMDQzLjA0LS4wNDQuMDQtLjA0NS4wNC0uMDQ3LjAzOS0uMDQ4LjAzOS0uMDUuMDM5LS4wNTEuMDM5LS4wNTIuMDM4LS4wNTMuMDM4LS4wNTUuMDM4LS4wNTUuMDM4LS4wNTguMDM3LS4wNTguMDM3LS4wNi4wMzctLjA2LjAzNi0uMDYyLjAzNi0uMDY0LjAzNi0uMDY0LjAzNi0uMDY2LjAzNS0uMDY3LjAzNS0uMDY4LjAzNS0uMDY5LjAzNS0uMDcuMDM0LS4wNzEuMDM0LS4wNzMuMDMzLS4wNzQuMDMzLS4xNS4wNjYtLjE1NS4wNjQtLjE2LjA2My0uMTYzLjA2MS0uMTY4LjA2LS4xNzIuMDU5LS4xNzUuMDU3LS4xOC4wNTYtLjE4My4wNTQtLjE4Ny4wNTMtLjE5MS4wNTEtLjE5NC4wNS0uMTk4LjA0OC0uMjAxLjA0Ni0uMjA1LjA0NS0uMjA4LjA0My0uMjExLjA0MS0uMjE0LjA0LS4yMTcuMDM4LS4yMi4wMzYtLjIyMy4wMzQtLjIyNS4wMzItLjIyOS4wMzEtLjIzMS4wMjgtLjIzMy4wMjctLjIzNi4wMjQtLjIzOS4wMjMtLjI0MS4wMi0uMjQyLjAxOS0uMjQ2LjAxNi0uMjQ3LjAxNS0uMjQ5LjAxMi0uMjUxLjAxLS4yNTMuMDA4LS4yNTUuMDA1LS4yNTYuMDA0LS4yNTguMDAxLS4yNTgtLjAwMS0uMjU2LS4wMDQtLjI1NS0uMDA1LS4yNTMtLjAwOC0uMjUxLS4wMS0uMjQ5LS4wMTItLjI0Ny0uMDE1LS4yNDUtLjAxNi0uMjQzLS4wMTktLjI0MS0uMDItLjIzOC0uMDIzLS4yMzYtLjAyNC0uMjM0LS4wMjctLjIzMS0uMDI4LS4yMjgtLjAzMS0uMjI2LS4wMzItLjIyMy0uMDM0LS4yMi0uMDM2LS4yMTctLjAzOC0uMjE0LS4wNC0uMjExLS4wNDEtLjIwOC0uMDQzLS4yMDQtLjA0NS0uMjAxLS4wNDYtLjE5OC0uMDQ4LS4xOTUtLjA1LS4xOS0uMDUxLS4xODctLjA1My0uMTg0LS4wNTQtLjE3OS0uMDU2LS4xNzYtLjA1Ny0uMTcyLS4wNTktLjE2Ny0uMDYtLjE2NC0uMDYxLS4xNTktLjA2My0uMTU1LS4wNjQtLjE1MS0uMDY2LS4wNzQtLjAzMy0uMDcyLS4wMzMtLjA3Mi0uMDM0LS4wNy0uMDM0LS4wNjktLjAzNS0uMDY4LS4wMzUtLjA2Ny0uMDM1LS4wNjYtLjAzNS0uMDY0LS4wMzYtLjA2My0uMDM2LS4wNjItLjAzNi0uMDYxLS4wMzYtLjA2LS4wMzctLjA1OC0uMDM3LS4wNTctLjAzNy0uMDU2LS4wMzgtLjA1NS0uMDM4LS4wNTMtLjAzOC0uMDUyLS4wMzgtLjA1MS0uMDM5LS4wNDktLjAzOS0uMDQ5LS4wMzktLjA0Ni0uMDM5LS4wNDYtLjA0LS4wNDQtLjA0LS4wNDMtLjA0LS4wNDEtLjA0LS4wNC0uMDQxLS4wMzktLjA0MS0uMDM3LS4wNDEtLjAzNi0uMDQxLS4wMzQtLjA0MS0uMDMzLS4wNDItLjAzMi0uMDQyLS4wMy0uMDQyLS4wMjktLjA0Mi0uMDI3LS4wNDItLjAyNi0uMDQzLS4wMjQtLjA0My0uMDIzLS4wNDMtLjAyMS0uMDQzLS4wMi0uMDQzLS4wMTgtLjA0NC0uMDE3LS4wNDMtLjAxNS0uMDQ0LS4wMTMtLjA0NC0uMDEyLS4wNDQtLjAxMS0uMDQ1LS4wMDktLjA0NC0uMDA3LS4wNDUtLjAwNi0uMDQ1LS4wMDQtLjA0NS0uMDAyLS4wNDUtLjAwMS0uMDQ1di0xN2wuMDAxLS4wNDUuMDAyLS4wNDUuMDA0LS4wNDUuMDA2LS4wNDUuMDA3LS4wNDUuMDA5LS4wNDQuMDExLS4wNDUuMDEyLS4wNDQuMDEzLS4wNDQuMDE1LS4wNDQuMDE3LS4wNDMuMDE4LS4wNDQuMDItLjA0My4wMjEtLjA0My4wMjMtLjA0My4wMjQtLjA0My4wMjYtLjA0My4wMjctLjA0Mi4wMjktLjA0Mi4wMy0uMDQyLjAzMi0uMDQyLjAzMy0uMDQyLjAzNC0uMDQxLjAzNi0uMDQxLjAzNy0uMDQxLjAzOS0uMDQxLjA0LS4wNDEuMDQxLS4wNC4wNDMtLjA0LjA0NC0uMDQuMDQ2LS4wNC4wNDYtLjAzOS4wNDktLjAzOS4wNDktLjAzOS4wNTEtLjAzOS4wNTItLjAzOC4wNTMtLjAzOC4wNTUtLjAzOC4wNTYtLjAzOC4wNTctLjAzNy4wNTgtLjAzNy4wNi0uMDM3LjA2MS0uMDM2LjA2Mi0uMDM2LjA2My0uMDM2LjA2NC0uMDM2LjA2Ni0uMDM1LjA2Ny0uMDM1LjA2OC0uMDM1LjA2OS0uMDM1LjA3LS4wMzQuMDcyLS4wMzQuMDcyLS4wMzMuMDc0LS4wMzMuMTUxLS4wNjYuMTU1LS4wNjQuMTU5LS4wNjMuMTY0LS4wNjEuMTY3LS4wNi4xNzItLjA1OS4xNzYtLjA1Ny4xNzktLjA1Ni4xODQtLjA1NC4xODctLjA1My4xOS0uMDUxLjE5NS0uMDUuMTk4LS4wNDguMjAxLS4wNDYuMjA0LS4wNDUuMjA4LS4wNDMuMjExLS4wNDEuMjE0LS4wNC4yMTctLjAzOC4yMi0uMDM2LjIyMy0uMDM0LjIyNi0uMDMyLjIyOC0uMDMxLjIzMS0uMDI4LjIzNC0uMDI3LjIzNi0uMDI0LjIzOC0uMDIzLjI0MS0uMDIuMjQzLS4wMTkuMjQ1LS4wMTYuMjQ3LS4wMTUuMjQ5LS4wMTIuMjUxLS4wMS4yNTMtLjAwOC4yNTUtLjAwNS4yNTYtLjAwNC4yNTgtLjAwMS4yNTguMDAxem0tOS4yNTggMjAuNDk5di4wMWwuMDAxLjAyMS4wMDMuMDIxLjAwNC4wMjIuMDA1LjAyMS4wMDYuMDIyLjAwNy4wMjIuMDA5LjAyMy4wMS4wMjIuMDExLjAyMy4wMTIuMDIzLjAxMy4wMjMuMDE1LjAyMy4wMTYuMDI0LjAxNy4wMjMuMDE4LjAyNC4wMTkuMDI0LjAyMS4wMjQuMDIyLjAyNS4wMjMuMDI0LjAyNC4wMjUuMDUyLjA0OS4wNTYuMDUuMDYxLjA1MS4wNjYuMDUxLjA3LjA1MS4wNzUuMDUxLjA3OS4wNTIuMDg0LjA1Mi4wODguMDUyLjA5Mi4wNTIuMDk3LjA1Mi4xMDIuMDUxLjEwNS4wNTIuMTEuMDUyLjExNC4wNTEuMTE5LjA1MS4xMjMuMDUxLjEyNy4wNS4xMzEuMDUuMTM1LjA1LjEzOS4wNDguMTQ0LjA0OS4xNDcuMDQ3LjE1Mi4wNDcuMTU1LjA0Ny4xNi4wNDUuMTYzLjA0NS4xNjcuMDQzLjE3MS4wNDMuMTc2LjA0MS4xNzguMDQxLjE4My4wMzkuMTg3LjAzOS4xOS4wMzcuMTk0LjAzNS4xOTcuMDM1LjIwMi4wMzMuMjA0LjAzMS4yMDkuMDMuMjEyLjAyOS4yMTYuMDI3LjIxOS4wMjUuMjIyLjAyNC4yMjYuMDIxLjIzLjAyLjIzMy4wMTguMjM2LjAxNi4yNC4wMTUuMjQzLjAxMi4yNDYuMDEuMjQ5LjAwOC4yNTMuMDA1LjI1Ni4wMDQuMjU5LjAwMS4yNi0uMDAxLjI1Ny0uMDA0LjI1NC0uMDA1LjI1LS4wMDguMjQ3LS4wMTEuMjQ0LS4wMTIuMjQxLS4wMTQuMjM3LS4wMTYuMjMzLS4wMTguMjMxLS4wMjEuMjI2LS4wMjEuMjI0LS4wMjQuMjItLjAyNi4yMTYtLjAyNy4yMTItLjAyOC4yMS0uMDMxLjIwNS0uMDMxLjIwMi0uMDM0LjE5OC0uMDM0LjE5NC0uMDM2LjE5MS0uMDM3LjE4Ny0uMDM5LjE4My0uMDQuMTc5LS4wNC4xNzUtLjA0Mi4xNzItLjA0My4xNjgtLjA0NC4xNjMtLjA0NS4xNi0uMDQ2LjE1NS0uMDQ2LjE1Mi0uMDQ3LjE0OC0uMDQ4LjE0My0uMDQ5LjEzOS0uMDQ5LjEzNi0uMDUuMTMxLS4wNS4xMjYtLjA1LjEyMy0uMDUxLjExOC0uMDUyLjExNC0uMDUxLjExLS4wNTIuMTA2LS4wNTIuMTAxLS4wNTIuMDk2LS4wNTIuMDkyLS4wNTIuMDg4LS4wNTMuMDgzLS4wNTEuMDc5LS4wNTIuMDc0LS4wNTIuMDctLjA1MS4wNjUtLjA1MS4wNi0uMDUxLjA1Ni0uMDUuMDUxLS4wNS4wMjMtLjAyNC4wMjMtLjAyNS4wMjEtLjAyNC4wMi0uMDI0LjAxOS0uMDI0LjAxOC0uMDI0LjAxNy0uMDI0LjAxNS0uMDIzLjAxNC0uMDI0LjAxMy0uMDIzLjAxMi0uMDIzLjAxLS4wMjMuMDEtLjAyMi4wMDgtLjAyMi4wMDYtLjAyMi4wMDYtLjAyMi4wMDQtLjAyMi4wMDQtLjAyMS4wMDEtLjAyMS4wMDEtLjAyMXYtNC4xMjdsLS4wNzcuMDU1LS4wOC4wNTMtLjA4My4wNTQtLjA4NS4wNTMtLjA4Ny4wNTItLjA5LjA1Mi0uMDkzLjA1MS0uMDk1LjA1LS4wOTcuMDUtLjEuMDQ5LS4xMDIuMDQ5LS4xMDUuMDQ4LS4xMDYuMDQ3LS4xMDkuMDQ3LS4xMTEuMDQ2LS4xMTQuMDQ1LS4xMTUuMDQ1LS4xMTguMDQ0LS4xMi4wNDMtLjEyMi4wNDItLjEyNC4wNDItLjEyNi4wNDEtLjEyOC4wNC0uMTMuMDQtLjEzMi4wMzgtLjEzNC4wMzgtLjEzNS4wMzctLjEzOC4wMzctLjEzOS4wMzUtLjE0Mi4wMzUtLjE0My4wMzQtLjE0NC4wMzMtLjE0Ny4wMzItLjE0OC4wMzEtLjE1LjAzLS4xNTEuMDMtLjE1My4wMjktLjE1NC4wMjctLjE1Ni4wMjctLjE1OC4wMjYtLjE1OS4wMjUtLjE2MS4wMjQtLjE2Mi4wMjMtLjE2My4wMjItLjE2NS4wMjEtLjE2Ni4wMi0uMTY3LjAxOS0uMTY5LjAxOC0uMTY5LjAxNy0uMTcxLjAxNi0uMTczLjAxNS0uMTczLjAxNC0uMTc1LjAxMy0uMTc1LjAxMi0uMTc3LjAxMS0uMTc4LjAxLS4xNzkuMDA4LS4xNzkuMDA4LS4xODEuMDA2LS4xODIuMDA1LS4xODIuMDA0LS4xODQuMDAzLS4xODQuMDAyaC0uMzdsLS4xODQtLjAwMi0uMTg0LS4wMDMtLjE4Mi0uMDA0LS4xODItLjAwNS0uMTgxLS4wMDYtLjE3OS0uMDA4LS4xNzktLjAwOC0uMTc4LS4wMS0uMTc2LS4wMTEtLjE3Ni0uMDEyLS4xNzUtLjAxMy0uMTczLS4wMTQtLjE3Mi0uMDE1LS4xNzEtLjAxNi0uMTctLjAxNy0uMTY5LS4wMTgtLjE2Ny0uMDE5LS4xNjYtLjAyLS4xNjUtLjAyMS0uMTYzLS4wMjItLjE2Mi0uMDIzLS4xNjEtLjAyNC0uMTU5LS4wMjUtLjE1Ny0uMDI2LS4xNTYtLjAyNy0uMTU1LS4wMjctLjE1My0uMDI5LS4xNTEtLjAzLS4xNS0uMDMtLjE0OC0uMDMxLS4xNDYtLjAzMi0uMTQ1LS4wMzMtLjE0My0uMDM0LS4xNDEtLjAzNS0uMTQtLjAzNS0uMTM3LS4wMzctLjEzNi0uMDM3LS4xMzQtLjAzOC0uMTMyLS4wMzgtLjEzLS4wNC0uMTI4LS4wNC0uMTI2LS4wNDEtLjEyNC0uMDQyLS4xMjItLjA0Mi0uMTItLjA0NC0uMTE3LS4wNDMtLjExNi0uMDQ1LS4xMTMtLjA0NS0uMTEyLS4wNDYtLjEwOS0uMDQ3LS4xMDYtLjA0Ny0uMTA1LS4wNDgtLjEwMi0uMDQ5LS4xLS4wNDktLjA5Ny0uMDUtLjA5NS0uMDUtLjA5My0uMDUyLS4wOS0uMDUxLS4wODctLjA1Mi0uMDg1LS4wNTMtLjA4My0uMDU0LS4wOC0uMDU0LS4wNzctLjA1NHY0LjEyN3ptMC01LjY1NHYuMDExbC4wMDEuMDIxLjAwMy4wMjEuMDA0LjAyMS4wMDUuMDIyLjAwNi4wMjIuMDA3LjAyMi4wMDkuMDIyLjAxLjAyMi4wMTEuMDIzLjAxMi4wMjMuMDEzLjAyMy4wMTUuMDI0LjAxNi4wMjMuMDE3LjAyNC4wMTguMDI0LjAxOS4wMjQuMDIxLjAyNC4wMjIuMDI0LjAyMy4wMjUuMDI0LjAyNC4wNTIuMDUuMDU2LjA1LjA2MS4wNS4wNjYuMDUxLjA3LjA1MS4wNzUuMDUyLjA3OS4wNTEuMDg0LjA1Mi4wODguMDUyLjA5Mi4wNTIuMDk3LjA1Mi4xMDIuMDUyLjEwNS4wNTIuMTEuMDUxLjExNC4wNTEuMTE5LjA1Mi4xMjMuMDUuMTI3LjA1MS4xMzEuMDUuMTM1LjA0OS4xMzkuMDQ5LjE0NC4wNDguMTQ3LjA0OC4xNTIuMDQ3LjE1NS4wNDYuMTYuMDQ1LjE2My4wNDUuMTY3LjA0NC4xNzEuMDQyLjE3Ni4wNDIuMTc4LjA0LjE4My4wNC4xODcuMDM4LjE5LjAzNy4xOTQuMDM2LjE5Ny4wMzQuMjAyLjAzMy4yMDQuMDMyLjIwOS4wMy4yMTIuMDI4LjIxNi4wMjcuMjE5LjAyNS4yMjIuMDI0LjIyNi4wMjIuMjMuMDIuMjMzLjAxOC4yMzYuMDE2LjI0LjAxNC4yNDMuMDEyLjI0Ni4wMS4yNDkuMDA4LjI1My4wMDYuMjU2LjAwMy4yNTkuMDAxLjI2LS4wMDEuMjU3LS4wMDMuMjU0LS4wMDYuMjUtLjAwOC4yNDctLjAxLjI0NC0uMDEyLjI0MS0uMDE1LjIzNy0uMDE2LjIzMy0uMDE4LjIzMS0uMDIuMjI2LS4wMjIuMjI0LS4wMjQuMjItLjAyNS4yMTYtLjAyNy4yMTItLjAyOS4yMS0uMDMuMjA1LS4wMzIuMjAyLS4wMzMuMTk4LS4wMzUuMTk0LS4wMzYuMTkxLS4wMzcuMTg3LS4wMzkuMTgzLS4wMzkuMTc5LS4wNDEuMTc1LS4wNDIuMTcyLS4wNDMuMTY4LS4wNDQuMTYzLS4wNDUuMTYtLjA0NS4xNTUtLjA0Ny4xNTItLjA0Ny4xNDgtLjA0OC4xNDMtLjA0OC4xMzktLjA1LjEzNi0uMDQ5LjEzMS0uMDUuMTI2LS4wNTEuMTIzLS4wNTEuMTE4LS4wNTEuMTE0LS4wNTIuMTEtLjA1Mi4xMDYtLjA1Mi4xMDEtLjA1Mi4wOTYtLjA1Mi4wOTItLjA1Mi4wODgtLjA1Mi4wODMtLjA1Mi4wNzktLjA1Mi4wNzQtLjA1MS4wNy0uMDUyLjA2NS0uMDUxLjA2LS4wNS4wNTYtLjA1MS4wNTEtLjA0OS4wMjMtLjAyNS4wMjMtLjAyNC4wMjEtLjAyNS4wMi0uMDI0LjAxOS0uMDI0LjAxOC0uMDI0LjAxNy0uMDI0LjAxNS0uMDIzLjAxNC0uMDIzLjAxMy0uMDI0LjAxMi0uMDIyLjAxLS4wMjMuMDEtLjAyMy4wMDgtLjAyMi4wMDYtLjAyMi4wMDYtLjAyMi4wMDQtLjAyMS4wMDQtLjAyMi4wMDEtLjAyMS4wMDEtLjAyMXYtNC4xMzlsLS4wNzcuMDU0LS4wOC4wNTQtLjA4My4wNTQtLjA4NS4wNTItLjA4Ny4wNTMtLjA5LjA1MS0uMDkzLjA1MS0uMDk1LjA1MS0uMDk3LjA1LS4xLjA0OS0uMTAyLjA0OS0uMTA1LjA0OC0uMTA2LjA0Ny0uMTA5LjA0Ny0uMTExLjA0Ni0uMTE0LjA0NS0uMTE1LjA0NC0uMTE4LjA0NC0uMTIuMDQ0LS4xMjIuMDQyLS4xMjQuMDQyLS4xMjYuMDQxLS4xMjguMDQtLjEzLjAzOS0uMTMyLjAzOS0uMTM0LjAzOC0uMTM1LjAzNy0uMTM4LjAzNi0uMTM5LjAzNi0uMTQyLjAzNS0uMTQzLjAzMy0uMTQ0LjAzMy0uMTQ3LjAzMy0uMTQ4LjAzMS0uMTUuMDMtLjE1MS4wMy0uMTUzLjAyOC0uMTU0LjAyOC0uMTU2LjAyNy0uMTU4LjAyNi0uMTU5LjAyNS0uMTYxLjAyNC0uMTYyLjAyMy0uMTYzLjAyMi0uMTY1LjAyMS0uMTY2LjAyLS4xNjcuMDE5LS4xNjkuMDE4LS4xNjkuMDE3LS4xNzEuMDE2LS4xNzMuMDE1LS4xNzMuMDE0LS4xNzUuMDEzLS4xNzUuMDEyLS4xNzcuMDExLS4xNzguMDA5LS4xNzkuMDA5LS4xNzkuMDA3LS4xODEuMDA3LS4xODIuMDA1LS4xODIuMDA0LS4xODQuMDAzLS4xODQuMDAyaC0uMzdsLS4xODQtLjAwMi0uMTg0LS4wMDMtLjE4Mi0uMDA0LS4xODItLjAwNS0uMTgxLS4wMDctLjE3OS0uMDA3LS4xNzktLjAwOS0uMTc4LS4wMDktLjE3Ni0uMDExLS4xNzYtLjAxMi0uMTc1LS4wMTMtLjE3My0uMDE0LS4xNzItLjAxNS0uMTcxLS4wMTYtLjE3LS4wMTctLjE2OS0uMDE4LS4xNjctLjAxOS0uMTY2LS4wMi0uMTY1LS4wMjEtLjE2My0uMDIyLS4xNjItLjAyMy0uMTYxLS4wMjQtLjE1OS0uMDI1LS4xNTctLjAyNi0uMTU2LS4wMjctLjE1NS0uMDI4LS4xNTMtLjAyOC0uMTUxLS4wMy0uMTUtLjAzLS4xNDgtLjAzMS0uMTQ2LS4wMzMtLjE0NS0uMDMzLS4xNDMtLjAzMy0uMTQxLS4wMzUtLjE0LS4wMzYtLjEzNy0uMDM2LS4xMzYtLjAzNy0uMTM0LS4wMzgtLjEzMi0uMDM5LS4xMy0uMDM5LS4xMjgtLjA0LS4xMjYtLjA0MS0uMTI0LS4wNDItLjEyMi0uMDQzLS4xMi0uMDQzLS4xMTctLjA0NC0uMTE2LS4wNDQtLjExMy0uMDQ2LS4xMTItLjA0Ni0uMTA5LS4wNDYtLjEwNi0uMDQ3LS4xMDUtLjA0OC0uMTAyLS4wNDktLjEtLjA0OS0uMDk3LS4wNS0uMDk1LS4wNTEtLjA5My0uMDUxLS4wOS0uMDUxLS4wODctLjA1My0uMDg1LS4wNTItLjA4My0uMDU0LS4wOC0uMDU0LS4wNzctLjA1NHY0LjEzOXptMC01LjY2NnYuMDExbC4wMDEuMDIuMDAzLjAyMi4wMDQuMDIxLjAwNS4wMjIuMDA2LjAyMS4wMDcuMDIyLjAwOS4wMjMuMDEuMDIyLjAxMS4wMjMuMDEyLjAyMy4wMTMuMDIzLjAxNS4wMjMuMDE2LjAyNC4wMTcuMDI0LjAxOC4wMjMuMDE5LjAyNC4wMjEuMDI1LjAyMi4wMjQuMDIzLjAyNC4wMjQuMDI1LjA1Mi4wNS4wNTYuMDUuMDYxLjA1LjA2Ni4wNTEuMDcuMDUxLjA3NS4wNTIuMDc5LjA1MS4wODQuMDUyLjA4OC4wNTIuMDkyLjA1Mi4wOTcuMDUyLjEwMi4wNTIuMTA1LjA1MS4xMS4wNTIuMTE0LjA1MS4xMTkuMDUxLjEyMy4wNTEuMTI3LjA1LjEzMS4wNS4xMzUuMDUuMTM5LjA0OS4xNDQuMDQ4LjE0Ny4wNDguMTUyLjA0Ny4xNTUuMDQ2LjE2LjA0NS4xNjMuMDQ1LjE2Ny4wNDMuMTcxLjA0My4xNzYuMDQyLjE3OC4wNC4xODMuMDQuMTg3LjAzOC4xOS4wMzcuMTk0LjAzNi4xOTcuMDM0LjIwMi4wMzMuMjA0LjAzMi4yMDkuMDMuMjEyLjAyOC4yMTYuMDI3LjIxOS4wMjUuMjIyLjAyNC4yMjYuMDIxLjIzLjAyLjIzMy4wMTguMjM2LjAxNy4yNC4wMTQuMjQzLjAxMi4yNDYuMDEuMjQ5LjAwOC4yNTMuMDA2LjI1Ni4wMDMuMjU5LjAwMS4yNi0uMDAxLjI1Ny0uMDAzLjI1NC0uMDA2LjI1LS4wMDguMjQ3LS4wMS4yNDQtLjAxMy4yNDEtLjAxNC4yMzctLjAxNi4yMzMtLjAxOC4yMzEtLjAyLjIyNi0uMDIyLjIyNC0uMDI0LjIyLS4wMjUuMjE2LS4wMjcuMjEyLS4wMjkuMjEtLjAzLjIwNS0uMDMyLjIwMi0uMDMzLjE5OC0uMDM1LjE5NC0uMDM2LjE5MS0uMDM3LjE4Ny0uMDM5LjE4My0uMDM5LjE3OS0uMDQxLjE3NS0uMDQyLjE3Mi0uMDQzLjE2OC0uMDQ0LjE2My0uMDQ1LjE2LS4wNDUuMTU1LS4wNDcuMTUyLS4wNDcuMTQ4LS4wNDguMTQzLS4wNDkuMTM5LS4wNDkuMTM2LS4wNDkuMTMxLS4wNTEuMTI2LS4wNS4xMjMtLjA1MS4xMTgtLjA1Mi4xMTQtLjA1MS4xMS0uMDUyLjEwNi0uMDUyLjEwMS0uMDUyLjA5Ni0uMDUyLjA5Mi0uMDUyLjA4OC0uMDUyLjA4My0uMDUyLjA3OS0uMDUyLjA3NC0uMDUyLjA3LS4wNTEuMDY1LS4wNTEuMDYtLjA1MS4wNTYtLjA1LjA1MS0uMDQ5LjAyMy0uMDI1LjAyMy0uMDI1LjAyMS0uMDI0LjAyLS4wMjQuMDE5LS4wMjQuMDE4LS4wMjQuMDE3LS4wMjQuMDE1LS4wMjMuMDE0LS4wMjQuMDEzLS4wMjMuMDEyLS4wMjMuMDEtLjAyMi4wMS0uMDIzLjAwOC0uMDIyLjAwNi0uMDIyLjAwNi0uMDIyLjAwNC0uMDIyLjAwNC0uMDIxLjAwMS0uMDIxLjAwMS0uMDIxdi00LjE1M2wtLjA3Ny4wNTQtLjA4LjA1NC0uMDgzLjA1My0uMDg1LjA1My0uMDg3LjA1My0uMDkuMDUxLS4wOTMuMDUxLS4wOTUuMDUxLS4wOTcuMDUtLjEuMDQ5LS4xMDIuMDQ4LS4xMDUuMDQ4LS4xMDYuMDQ4LS4xMDkuMDQ2LS4xMTEuMDQ2LS4xMTQuMDQ2LS4xMTUuMDQ0LS4xMTguMDQ0LS4xMi4wNDMtLjEyMi4wNDMtLjEyNC4wNDItLjEyNi4wNDEtLjEyOC4wNC0uMTMuMDM5LS4xMzIuMDM5LS4xMzQuMDM4LS4xMzUuMDM3LS4xMzguMDM2LS4xMzkuMDM2LS4xNDIuMDM0LS4xNDMuMDM0LS4xNDQuMDMzLS4xNDcuMDMyLS4xNDguMDMyLS4xNS4wMy0uMTUxLjAzLS4xNTMuMDI4LS4xNTQuMDI4LS4xNTYuMDI3LS4xNTguMDI2LS4xNTkuMDI0LS4xNjEuMDI0LS4xNjIuMDIzLS4xNjMuMDIzLS4xNjUuMDIxLS4xNjYuMDItLjE2Ny4wMTktLjE2OS4wMTgtLjE2OS4wMTctLjE3MS4wMTYtLjE3My4wMTUtLjE3My4wMTQtLjE3NS4wMTMtLjE3NS4wMTItLjE3Ny4wMS0uMTc4LjAxLS4xNzkuMDA5LS4xNzkuMDA3LS4xODEuMDA2LS4xODIuMDA2LS4xODIuMDA0LS4xODQuMDAzLS4xODQuMDAxLS4xODUuMDAxLS4xODUtLjAwMS0uMTg0LS4wMDEtLjE4NC0uMDAzLS4xODItLjAwNC0uMTgyLS4wMDYtLjE4MS0uMDA2LS4xNzktLjAwNy0uMTc5LS4wMDktLjE3OC0uMDEtLjE3Ni0uMDEtLjE3Ni0uMDEyLS4xNzUtLjAxMy0uMTczLS4wMTQtLjE3Mi0uMDE1LS4xNzEtLjAxNi0uMTctLjAxNy0uMTY5LS4wMTgtLjE2Ny0uMDE5LS4xNjYtLjAyLS4xNjUtLjAyMS0uMTYzLS4wMjMtLjE2Mi0uMDIzLS4xNjEtLjAyNC0uMTU5LS4wMjQtLjE1Ny0uMDI2LS4xNTYtLjAyNy0uMTU1LS4wMjgtLjE1My0uMDI4LS4xNTEtLjAzLS4xNS0uMDMtLjE0OC0uMDMyLS4xNDYtLjAzMi0uMTQ1LS4wMzMtLjE0My0uMDM0LS4xNDEtLjAzNC0uMTQtLjAzNi0uMTM3LS4wMzYtLjEzNi0uMDM3LS4xMzQtLjAzOC0uMTMyLS4wMzktLjEzLS4wMzktLjEyOC0uMDQxLS4xMjYtLjA0MS0uMTI0LS4wNDEtLjEyMi0uMDQzLS4xMi0uMDQzLS4xMTctLjA0NC0uMTE2LS4wNDQtLjExMy0uMDQ2LS4xMTItLjA0Ni0uMTA5LS4wNDYtLjEwNi0uMDQ4LS4xMDUtLjA0OC0uMTAyLS4wNDgtLjEtLjA1LS4wOTctLjA0OS0uMDk1LS4wNTEtLjA5My0uMDUxLS4wOS0uMDUyLS4wODctLjA1Mi0uMDg1LS4wNTMtLjA4My0uMDUzLS4wOC0uMDU0LS4wNzctLjA1NHY0LjE1M3ptOC43NC04LjE3OWwtLjI1Ny4wMDQtLjI1NC4wMDUtLjI1LjAwOC0uMjQ3LjAxMS0uMjQ0LjAxMi0uMjQxLjAxNC0uMjM3LjAxNi0uMjMzLjAxOC0uMjMxLjAyMS0uMjI2LjAyMi0uMjI0LjAyMy0uMjIuMDI2LS4yMTYuMDI3LS4yMTIuMDI4LS4yMS4wMzEtLjIwNS4wMzItLjIwMi4wMzMtLjE5OC4wMzQtLjE5NC4wMzYtLjE5MS4wMzgtLjE4Ny4wMzgtLjE4My4wNC0uMTc5LjA0MS0uMTc1LjA0Mi0uMTcyLjA0My0uMTY4LjA0My0uMTYzLjA0NS0uMTYuMDQ2LS4xNTUuMDQ2LS4xNTIuMDQ4LS4xNDguMDQ4LS4xNDMuMDQ4LS4xMzkuMDQ5LS4xMzYuMDUtLjEzMS4wNS0uMTI2LjA1MS0uMTIzLjA1MS0uMTE4LjA1MS0uMTE0LjA1Mi0uMTEuMDUyLS4xMDYuMDUyLS4xMDEuMDUyLS4wOTYuMDUyLS4wOTIuMDUyLS4wODguMDUyLS4wODMuMDUyLS4wNzkuMDUyLS4wNzQuMDUxLS4wNy4wNTItLjA2NS4wNTEtLjA2LjA1LS4wNTYuMDUtLjA1MS4wNS0uMDIzLjAyNS0uMDIzLjAyNC0uMDIxLjAyNC0uMDIuMDI1LS4wMTkuMDI0LS4wMTguMDI0LS4wMTcuMDIzLS4wMTUuMDI0LS4wMTQuMDIzLS4wMTMuMDIzLS4wMTIuMDIzLS4wMS4wMjMtLjAxLjAyMi0uMDA4LjAyMi0uMDA2LjAyMy0uMDA2LjAyMS0uMDA0LjAyMi0uMDA0LjAyMS0uMDAxLjAyMS0uMDAxLjAyMS4wMDEuMDIxLjAwMS4wMjEuMDA0LjAyMS4wMDQuMDIyLjAwNi4wMjEuMDA2LjAyMy4wMDguMDIyLjAxLjAyMi4wMS4wMjMuMDEyLjAyMy4wMTMuMDIzLjAxNC4wMjMuMDE1LjAyNC4wMTcuMDIzLjAxOC4wMjQuMDE5LjAyNC4wMi4wMjUuMDIxLjAyNC4wMjMuMDI0LjAyMy4wMjUuMDUxLjA1LjA1Ni4wNS4wNi4wNS4wNjUuMDUxLjA3LjA1Mi4wNzQuMDUxLjA3OS4wNTIuMDgzLjA1Mi4wODguMDUyLjA5Mi4wNTIuMDk2LjA1Mi4xMDEuMDUyLjEwNi4wNTIuMTEuMDUyLjExNC4wNTIuMTE4LjA1MS4xMjMuMDUxLjEyNi4wNTEuMTMxLjA1LjEzNi4wNS4xMzkuMDQ5LjE0My4wNDguMTQ4LjA0OC4xNTIuMDQ4LjE1NS4wNDYuMTYuMDQ2LjE2My4wNDUuMTY4LjA0My4xNzIuMDQzLjE3NS4wNDIuMTc5LjA0MS4xODMuMDQuMTg3LjAzOC4xOTEuMDM4LjE5NC4wMzYuMTk4LjAzNC4yMDIuMDMzLjIwNS4wMzIuMjEuMDMxLjIxMi4wMjguMjE2LjAyNy4yMi4wMjYuMjI0LjAyMy4yMjYuMDIyLjIzMS4wMjEuMjMzLjAxOC4yMzcuMDE2LjI0MS4wMTQuMjQ0LjAxMi4yNDcuMDExLjI1LjAwOC4yNTQuMDA1LjI1Ny4wMDQuMjYuMDAxLjI2LS4wMDEuMjU3LS4wMDQuMjU0LS4wMDUuMjUtLjAwOC4yNDctLjAxMS4yNDQtLjAxMi4yNDEtLjAxNC4yMzctLjAxNi4yMzMtLjAxOC4yMzEtLjAyMS4yMjYtLjAyMi4yMjQtLjAyMy4yMi0uMDI2LjIxNi0uMDI3LjIxMi0uMDI4LjIxLS4wMzEuMjA1LS4wMzIuMjAyLS4wMzMuMTk4LS4wMzQuMTk0LS4wMzYuMTkxLS4wMzguMTg3LS4wMzguMTgzLS4wNC4xNzktLjA0MS4xNzUtLjA0Mi4xNzItLjA0My4xNjgtLjA0My4xNjMtLjA0NS4xNi0uMDQ2LjE1NS0uMDQ2LjE1Mi0uMDQ4LjE0OC0uMDQ4LjE0My0uMDQ4LjEzOS0uMDQ5LjEzNi0uMDUuMTMxLS4wNS4xMjYtLjA1MS4xMjMtLjA1MS4xMTgtLjA1MS4xMTQtLjA1Mi4xMS0uMDUyLjEwNi0uMDUyLjEwMS0uMDUyLjA5Ni0uMDUyLjA5Mi0uMDUyLjA4OC0uMDUyLjA4My0uMDUyLjA3OS0uMDUyLjA3NC0uMDUxLjA3LS4wNTIuMDY1LS4wNTEuMDYtLjA1LjA1Ni0uMDUuMDUxLS4wNS4wMjMtLjAyNS4wMjMtLjAyNC4wMjEtLjAyNC4wMi0uMDI1LjAxOS0uMDI0LjAxOC0uMDI0LjAxNy0uMDIzLjAxNS0uMDI0LjAxNC0uMDIzLjAxMy0uMDIzLjAxMi0uMDIzLjAxLS4wMjMuMDEtLjAyMi4wMDgtLjAyMi4wMDYtLjAyMy4wMDYtLjAyMS4wMDQtLjAyMi4wMDQtLjAyMS4wMDEtLjAyMS4wMDEtLjAyMS0uMDAxLS4wMjEtLjAwMS0uMDIxLS4wMDQtLjAyMS0uMDA0LS4wMjItLjAwNi0uMDIxLS4wMDYtLjAyMy0uMDA4LS4wMjItLjAxLS4wMjItLjAxLS4wMjMtLjAxMi0uMDIzLS4wMTMtLjAyMy0uMDE0LS4wMjMtLjAxNS0uMDI0LS4wMTctLjAyMy0uMDE4LS4wMjQtLjAxOS0uMDI0LS4wMi0uMDI1LS4wMjEtLjAyNC0uMDIzLS4wMjQtLjAyMy0uMDI1LS4wNTEtLjA1LS4wNTYtLjA1LS4wNi0uMDUtLjA2NS0uMDUxLS4wNy0uMDUyLS4wNzQtLjA1MS0uMDc5LS4wNTItLjA4My0uMDUyLS4wODgtLjA1Mi0uMDkyLS4wNTItLjA5Ni0uMDUyLS4xMDEtLjA1Mi0uMTA2LS4wNTItLjExLS4wNTItLjExNC0uMDUyLS4xMTgtLjA1MS0uMTIzLS4wNTEtLjEyNi0uMDUxLS4xMzEtLjA1LS4xMzYtLjA1LS4xMzktLjA0OS0uMTQzLS4wNDgtLjE0OC0uMDQ4LS4xNTItLjA0OC0uMTU1LS4wNDYtLjE2LS4wNDYtLjE2My0uMDQ1LS4xNjgtLjA0My0uMTcyLS4wNDMtLjE3NS0uMDQyLS4xNzktLjA0MS0uMTgzLS4wNC0uMTg3LS4wMzgtLjE5MS0uMDM4LS4xOTQtLjAzNi0uMTk4LS4wMzQtLjIwMi0uMDMzLS4yMDUtLjAzMi0uMjEtLjAzMS0uMjEyLS4wMjgtLjIxNi0uMDI3LS4yMi0uMDI2LS4yMjQtLjAyMy0uMjI2LS4wMjItLjIzMS0uMDIxLS4yMzMtLjAxOC0uMjM3LS4wMTYtLjI0MS0uMDE0LS4yNDQtLjAxMi0uMjQ3LS4wMTEtLjI1LS4wMDgtLjI1NC0uMDA1LS4yNTctLjAwNC0uMjYtLjAwMS0uMjYuMDAxelwiXG4gICk7XG59O1xuY29uc3QgaW5zZXJ0Q29tcHV0ZXJJY29uID0gZnVuY3Rpb24oZWxlbSkge1xuICBlbGVtLmFwcGVuZChcImRlZnNcIikuYXBwZW5kKFwic3ltYm9sXCIpLmF0dHIoXCJpZFwiLCBcImNvbXB1dGVyXCIpLmF0dHIoXCJ3aWR0aFwiLCBcIjI0XCIpLmF0dHIoXCJoZWlnaHRcIiwgXCIyNFwiKS5hcHBlbmQoXCJwYXRoXCIpLmF0dHIoXCJ0cmFuc2Zvcm1cIiwgXCJzY2FsZSguNSlcIikuYXR0cihcbiAgICBcImRcIixcbiAgICBcIk0yIDJ2MTNoMjB2LTEzaC0yMHptMTggMTFoLTE2di05aDE2djl6bS0xMC4yMjggNmwuNDY2LTFoMy41MjRsLjQ2NyAxaC00LjQ1N3ptMTQuMjI4IDNoLTI0bDItNmgyLjEwNGwtMS4zMyA0aDE4LjQ1bC0xLjI5Ny00aDIuMDczbDIgNnptLTUtMTBoLTE0di03aDE0djd6XCJcbiAgKTtcbn07XG5jb25zdCBpbnNlcnRDbG9ja0ljb24gPSBmdW5jdGlvbihlbGVtKSB7XG4gIGVsZW0uYXBwZW5kKFwiZGVmc1wiKS5hcHBlbmQoXCJzeW1ib2xcIikuYXR0cihcImlkXCIsIFwiY2xvY2tcIikuYXR0cihcIndpZHRoXCIsIFwiMjRcIikuYXR0cihcImhlaWdodFwiLCBcIjI0XCIpLmFwcGVuZChcInBhdGhcIikuYXR0cihcInRyYW5zZm9ybVwiLCBcInNjYWxlKC41KVwiKS5hdHRyKFxuICAgIFwiZFwiLFxuICAgIFwiTTEyIDJjNS41MTQgMCAxMCA0LjQ4NiAxMCAxMHMtNC40ODYgMTAtMTAgMTAtMTAtNC40ODYtMTAtMTAgNC40ODYtMTAgMTAtMTB6bTAtMmMtNi42MjcgMC0xMiA1LjM3My0xMiAxMnM1LjM3MyAxMiAxMiAxMiAxMi01LjM3MyAxMi0xMi01LjM3My0xMi0xMi0xMnptNS44NDggMTIuNDU5Yy4yMDIuMDM4LjIwMi4zMzMuMDAxLjM3Mi0xLjkwNy4zNjEtNi4wNDUgMS4xMTEtNi41NDcgMS4xMTEtLjcxOSAwLTEuMzAxLS41ODItMS4zMDEtMS4zMDEgMC0uNTEyLjc3LTUuNDQ3IDEuMTI1LTcuNDQ1LjAzNC0uMTkyLjMxMi0uMTgxLjM0My4wMTRsLjk4NSA2LjIzOCA1LjM5NCAxLjAxMXpcIlxuICApO1xufTtcbmNvbnN0IGluc2VydEFycm93SGVhZCA9IGZ1bmN0aW9uKGVsZW0pIHtcbiAgZWxlbS5hcHBlbmQoXCJkZWZzXCIpLmFwcGVuZChcIm1hcmtlclwiKS5hdHRyKFwiaWRcIiwgXCJhcnJvd2hlYWRcIikuYXR0cihcInJlZlhcIiwgOSkuYXR0cihcInJlZllcIiwgNSkuYXR0cihcIm1hcmtlclVuaXRzXCIsIFwidXNlclNwYWNlT25Vc2VcIikuYXR0cihcIm1hcmtlcldpZHRoXCIsIDEyKS5hdHRyKFwibWFya2VySGVpZ2h0XCIsIDEyKS5hdHRyKFwib3JpZW50XCIsIFwiYXV0b1wiKS5hcHBlbmQoXCJwYXRoXCIpLmF0dHIoXCJkXCIsIFwiTSAwIDAgTCAxMCA1IEwgMCAxMCB6XCIpO1xufTtcbmNvbnN0IGluc2VydEFycm93RW5kID0gZnVuY3Rpb24oZWxlbSkge1xuICBlbGVtLmFwcGVuZChcImRlZnNcIikuYXBwZW5kKFwibWFya2VyXCIpLmF0dHIoXCJpZFwiLCBcImFycm93ZW5kXCIpLmF0dHIoXCJyZWZYXCIsIDEpLmF0dHIoXCJyZWZZXCIsIDUpLmF0dHIoXCJtYXJrZXJVbml0c1wiLCBcInVzZXJTcGFjZU9uVXNlXCIpLmF0dHIoXCJtYXJrZXJXaWR0aFwiLCAxMikuYXR0cihcIm1hcmtlckhlaWdodFwiLCAxMikuYXR0cihcIm9yaWVudFwiLCBcImF1dG9cIikuYXBwZW5kKFwicGF0aFwiKS5hdHRyKFwiZFwiLCBcIk0gMTAgMCBMIDAgNSBMIDEwIDEwIHpcIik7XG59O1xuY29uc3QgaW5zZXJ0QXJyb3dGaWxsZWRIZWFkID0gZnVuY3Rpb24oZWxlbSkge1xuICBlbGVtLmFwcGVuZChcImRlZnNcIikuYXBwZW5kKFwibWFya2VyXCIpLmF0dHIoXCJpZFwiLCBcImZpbGxlZC1oZWFkXCIpLmF0dHIoXCJyZWZYXCIsIDE4KS5hdHRyKFwicmVmWVwiLCA3KS5hdHRyKFwibWFya2VyV2lkdGhcIiwgMjApLmF0dHIoXCJtYXJrZXJIZWlnaHRcIiwgMjgpLmF0dHIoXCJvcmllbnRcIiwgXCJhdXRvXCIpLmFwcGVuZChcInBhdGhcIikuYXR0cihcImRcIiwgXCJNIDE4LDcgTDksMTMgTDE0LDcgTDksMSBaXCIpO1xufTtcbmNvbnN0IGluc2VydER5bmFtaWNOdW1iZXIgPSBmdW5jdGlvbihlbGVtKSB7XG4gIGVsZW0uYXBwZW5kKFwiZGVmc1wiKS5hcHBlbmQoXCJtYXJrZXJcIikuYXR0cihcImlkXCIsIFwic2VxdWVuY2VudW1iZXJcIikuYXR0cihcInJlZlhcIiwgMTUpLmF0dHIoXCJyZWZZXCIsIDE1KS5hdHRyKFwibWFya2VyV2lkdGhcIiwgNjApLmF0dHIoXCJtYXJrZXJIZWlnaHRcIiwgNDApLmF0dHIoXCJvcmllbnRcIiwgXCJhdXRvXCIpLmFwcGVuZChcImNpcmNsZVwiKS5hdHRyKFwiY3hcIiwgMTUpLmF0dHIoXCJjeVwiLCAxNSkuYXR0cihcInJcIiwgNik7XG59O1xuY29uc3QgaW5zZXJ0QXJyb3dDcm9zc0hlYWQgPSBmdW5jdGlvbihlbGVtKSB7XG4gIGNvbnN0IGRlZnMgPSBlbGVtLmFwcGVuZChcImRlZnNcIik7XG4gIGNvbnN0IG1hcmtlciA9IGRlZnMuYXBwZW5kKFwibWFya2VyXCIpLmF0dHIoXCJpZFwiLCBcImNyb3NzaGVhZFwiKS5hdHRyKFwibWFya2VyV2lkdGhcIiwgMTUpLmF0dHIoXCJtYXJrZXJIZWlnaHRcIiwgOCkuYXR0cihcIm9yaWVudFwiLCBcImF1dG9cIikuYXR0cihcInJlZlhcIiwgMTYpLmF0dHIoXCJyZWZZXCIsIDQpO1xuICBtYXJrZXIuYXBwZW5kKFwicGF0aFwiKS5hdHRyKFwiZmlsbFwiLCBcImJsYWNrXCIpLmF0dHIoXCJzdHJva2VcIiwgXCIjMDAwMDAwXCIpLnN0eWxlKFwic3Ryb2tlLWRhc2hhcnJheVwiLCBcIjAsIDBcIikuYXR0cihcInN0cm9rZS13aWR0aFwiLCBcIjFweFwiKS5hdHRyKFwiZFwiLCBcIk0gOSwyIFYgNiBMMTYsNCBaXCIpO1xuICBtYXJrZXIuYXBwZW5kKFwicGF0aFwiKS5hdHRyKFwiZmlsbFwiLCBcIm5vbmVcIikuYXR0cihcInN0cm9rZVwiLCBcIiMwMDAwMDBcIikuc3R5bGUoXCJzdHJva2UtZGFzaGFycmF5XCIsIFwiMCwgMFwiKS5hdHRyKFwic3Ryb2tlLXdpZHRoXCIsIFwiMXB4XCIpLmF0dHIoXCJkXCIsIFwiTSAwLDEgTCA2LDcgTSA2LDEgTCAwLDdcIik7XG59O1xuY29uc3QgZ2V0QzRTaGFwZUZvbnQgPSAoY25mLCB0eXBlQzRTaGFwZSkgPT4ge1xuICByZXR1cm4ge1xuICAgIGZvbnRGYW1pbHk6IGNuZlt0eXBlQzRTaGFwZSArIFwiRm9udEZhbWlseVwiXSxcbiAgICBmb250U2l6ZTogY25mW3R5cGVDNFNoYXBlICsgXCJGb250U2l6ZVwiXSxcbiAgICBmb250V2VpZ2h0OiBjbmZbdHlwZUM0U2hhcGUgKyBcIkZvbnRXZWlnaHRcIl1cbiAgfTtcbn07XG5jb25zdCBfZHJhd1RleHRDYW5kaWRhdGVGdW5jID0gZnVuY3Rpb24oKSB7XG4gIGZ1bmN0aW9uIGJ5VGV4dChjb250ZW50LCBnLCB4LCB5LCB3aWR0aCwgaGVpZ2h0LCB0ZXh0QXR0cnMpIHtcbiAgICBjb25zdCB0ZXh0ID0gZy5hcHBlbmQoXCJ0ZXh0XCIpLmF0dHIoXCJ4XCIsIHggKyB3aWR0aCAvIDIpLmF0dHIoXCJ5XCIsIHkgKyBoZWlnaHQgLyAyICsgNSkuc3R5bGUoXCJ0ZXh0LWFuY2hvclwiLCBcIm1pZGRsZVwiKS50ZXh0KGNvbnRlbnQpO1xuICAgIF9zZXRUZXh0QXR0cnModGV4dCwgdGV4dEF0dHJzKTtcbiAgfVxuICBmdW5jdGlvbiBieVRzcGFuKGNvbnRlbnQsIGcsIHgsIHksIHdpZHRoLCBoZWlnaHQsIHRleHRBdHRycywgY29uZjIpIHtcbiAgICBjb25zdCB7IGZvbnRTaXplLCBmb250RmFtaWx5LCBmb250V2VpZ2h0IH0gPSBjb25mMjtcbiAgICBjb25zdCBsaW5lcyA9IGNvbnRlbnQuc3BsaXQoY29tbW9uLmxpbmVCcmVha1JlZ2V4KTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxpbmVzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBkeSA9IGkgKiBmb250U2l6ZSAtIGZvbnRTaXplICogKGxpbmVzLmxlbmd0aCAtIDEpIC8gMjtcbiAgICAgIGNvbnN0IHRleHQgPSBnLmFwcGVuZChcInRleHRcIikuYXR0cihcInhcIiwgeCArIHdpZHRoIC8gMikuYXR0cihcInlcIiwgeSkuc3R5bGUoXCJ0ZXh0LWFuY2hvclwiLCBcIm1pZGRsZVwiKS5hdHRyKFwiZG9taW5hbnQtYmFzZWxpbmVcIiwgXCJtaWRkbGVcIikuc3R5bGUoXCJmb250LXNpemVcIiwgZm9udFNpemUpLnN0eWxlKFwiZm9udC13ZWlnaHRcIiwgZm9udFdlaWdodCkuc3R5bGUoXCJmb250LWZhbWlseVwiLCBmb250RmFtaWx5KTtcbiAgICAgIHRleHQuYXBwZW5kKFwidHNwYW5cIikuYXR0cihcImR5XCIsIGR5KS50ZXh0KGxpbmVzW2ldKS5hdHRyKFwiYWxpZ25tZW50LWJhc2VsaW5lXCIsIFwibWF0aGVtYXRpY2FsXCIpO1xuICAgICAgX3NldFRleHRBdHRycyh0ZXh0LCB0ZXh0QXR0cnMpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBieUZvKGNvbnRlbnQsIGcsIHgsIHksIHdpZHRoLCBoZWlnaHQsIHRleHRBdHRycywgY29uZjIpIHtcbiAgICBjb25zdCBzID0gZy5hcHBlbmQoXCJzd2l0Y2hcIik7XG4gICAgY29uc3QgZiA9IHMuYXBwZW5kKFwiZm9yZWlnbk9iamVjdFwiKS5hdHRyKFwieFwiLCB4KS5hdHRyKFwieVwiLCB5KS5hdHRyKFwid2lkdGhcIiwgd2lkdGgpLmF0dHIoXCJoZWlnaHRcIiwgaGVpZ2h0KTtcbiAgICBjb25zdCB0ZXh0ID0gZi5hcHBlbmQoXCJ4aHRtbDpkaXZcIikuc3R5bGUoXCJkaXNwbGF5XCIsIFwidGFibGVcIikuc3R5bGUoXCJoZWlnaHRcIiwgXCIxMDAlXCIpLnN0eWxlKFwid2lkdGhcIiwgXCIxMDAlXCIpO1xuICAgIHRleHQuYXBwZW5kKFwiZGl2XCIpLnN0eWxlKFwiZGlzcGxheVwiLCBcInRhYmxlLWNlbGxcIikuc3R5bGUoXCJ0ZXh0LWFsaWduXCIsIFwiY2VudGVyXCIpLnN0eWxlKFwidmVydGljYWwtYWxpZ25cIiwgXCJtaWRkbGVcIikudGV4dChjb250ZW50KTtcbiAgICBieVRzcGFuKGNvbnRlbnQsIHMsIHgsIHksIHdpZHRoLCBoZWlnaHQsIHRleHRBdHRycywgY29uZjIpO1xuICAgIF9zZXRUZXh0QXR0cnModGV4dCwgdGV4dEF0dHJzKTtcbiAgfVxuICBmdW5jdGlvbiBfc2V0VGV4dEF0dHJzKHRvVGV4dCwgZnJvbVRleHRBdHRyc0RpY3QpIHtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBmcm9tVGV4dEF0dHJzRGljdCkge1xuICAgICAgaWYgKGZyb21UZXh0QXR0cnNEaWN0Lmhhc093blByb3BlcnR5KGtleSkpIHtcbiAgICAgICAgdG9UZXh0LmF0dHIoa2V5LCBmcm9tVGV4dEF0dHJzRGljdFtrZXldKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgcmV0dXJuIGZ1bmN0aW9uKGNvbmYyKSB7XG4gICAgcmV0dXJuIGNvbmYyLnRleHRQbGFjZW1lbnQgPT09IFwiZm9cIiA/IGJ5Rm8gOiBjb25mMi50ZXh0UGxhY2VtZW50ID09PSBcIm9sZFwiID8gYnlUZXh0IDogYnlUc3BhbjtcbiAgfTtcbn0oKTtcbmNvbnN0IHN2Z0RyYXcgPSB7XG4gIGRyYXdSZWN0LFxuICBkcmF3Qm91bmRhcnk6IGRyYXdCb3VuZGFyeSQxLFxuICBkcmF3QzRTaGFwZSxcbiAgZHJhd1JlbHM6IGRyYXdSZWxzJDEsXG4gIGRyYXdJbWFnZSxcbiAgaW5zZXJ0QXJyb3dIZWFkLFxuICBpbnNlcnRBcnJvd0VuZCxcbiAgaW5zZXJ0QXJyb3dGaWxsZWRIZWFkLFxuICBpbnNlcnREeW5hbWljTnVtYmVyLFxuICBpbnNlcnRBcnJvd0Nyb3NzSGVhZCxcbiAgaW5zZXJ0RGF0YWJhc2VJY29uLFxuICBpbnNlcnRDb21wdXRlckljb24sXG4gIGluc2VydENsb2NrSWNvblxufTtcbmxldCBnbG9iYWxCb3VuZGFyeU1heFggPSAwLCBnbG9iYWxCb3VuZGFyeU1heFkgPSAwO1xubGV0IGM0U2hhcGVJblJvdyA9IDQ7XG5sZXQgYzRCb3VuZGFyeUluUm93ID0gMjtcbnBhcnNlci55eSA9IGRiO1xubGV0IGNvbmYgPSB7fTtcbmNsYXNzIEJvdW5kcyB7XG4gIGNvbnN0cnVjdG9yKGRpYWdPYmopIHtcbiAgICB0aGlzLm5hbWUgPSBcIlwiO1xuICAgIHRoaXMuZGF0YSA9IHt9O1xuICAgIHRoaXMuZGF0YS5zdGFydHggPSB2b2lkIDA7XG4gICAgdGhpcy5kYXRhLnN0b3B4ID0gdm9pZCAwO1xuICAgIHRoaXMuZGF0YS5zdGFydHkgPSB2b2lkIDA7XG4gICAgdGhpcy5kYXRhLnN0b3B5ID0gdm9pZCAwO1xuICAgIHRoaXMuZGF0YS53aWR0aExpbWl0ID0gdm9pZCAwO1xuICAgIHRoaXMubmV4dERhdGEgPSB7fTtcbiAgICB0aGlzLm5leHREYXRhLnN0YXJ0eCA9IHZvaWQgMDtcbiAgICB0aGlzLm5leHREYXRhLnN0b3B4ID0gdm9pZCAwO1xuICAgIHRoaXMubmV4dERhdGEuc3RhcnR5ID0gdm9pZCAwO1xuICAgIHRoaXMubmV4dERhdGEuc3RvcHkgPSB2b2lkIDA7XG4gICAgdGhpcy5uZXh0RGF0YS5jbnQgPSAwO1xuICAgIHNldENvbmYoZGlhZ09iai5kYi5nZXRDb25maWcoKSk7XG4gIH1cbiAgc2V0RGF0YShzdGFydHgsIHN0b3B4LCBzdGFydHksIHN0b3B5KSB7XG4gICAgdGhpcy5uZXh0RGF0YS5zdGFydHggPSB0aGlzLmRhdGEuc3RhcnR4ID0gc3RhcnR4O1xuICAgIHRoaXMubmV4dERhdGEuc3RvcHggPSB0aGlzLmRhdGEuc3RvcHggPSBzdG9weDtcbiAgICB0aGlzLm5leHREYXRhLnN0YXJ0eSA9IHRoaXMuZGF0YS5zdGFydHkgPSBzdGFydHk7XG4gICAgdGhpcy5uZXh0RGF0YS5zdG9weSA9IHRoaXMuZGF0YS5zdG9weSA9IHN0b3B5O1xuICB9XG4gIHVwZGF0ZVZhbChvYmosIGtleSwgdmFsLCBmdW4pIHtcbiAgICBpZiAob2JqW2tleV0gPT09IHZvaWQgMCkge1xuICAgICAgb2JqW2tleV0gPSB2YWw7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9ialtrZXldID0gZnVuKHZhbCwgb2JqW2tleV0pO1xuICAgIH1cbiAgfVxuICBpbnNlcnQoYzRTaGFwZSkge1xuICAgIHRoaXMubmV4dERhdGEuY250ID0gdGhpcy5uZXh0RGF0YS5jbnQgKyAxO1xuICAgIGxldCBfc3RhcnR4ID0gdGhpcy5uZXh0RGF0YS5zdGFydHggPT09IHRoaXMubmV4dERhdGEuc3RvcHggPyB0aGlzLm5leHREYXRhLnN0b3B4ICsgYzRTaGFwZS5tYXJnaW4gOiB0aGlzLm5leHREYXRhLnN0b3B4ICsgYzRTaGFwZS5tYXJnaW4gKiAyO1xuICAgIGxldCBfc3RvcHggPSBfc3RhcnR4ICsgYzRTaGFwZS53aWR0aDtcbiAgICBsZXQgX3N0YXJ0eSA9IHRoaXMubmV4dERhdGEuc3RhcnR5ICsgYzRTaGFwZS5tYXJnaW4gKiAyO1xuICAgIGxldCBfc3RvcHkgPSBfc3RhcnR5ICsgYzRTaGFwZS5oZWlnaHQ7XG4gICAgaWYgKF9zdGFydHggPj0gdGhpcy5kYXRhLndpZHRoTGltaXQgfHwgX3N0b3B4ID49IHRoaXMuZGF0YS53aWR0aExpbWl0IHx8IHRoaXMubmV4dERhdGEuY250ID4gYzRTaGFwZUluUm93KSB7XG4gICAgICBfc3RhcnR4ID0gdGhpcy5uZXh0RGF0YS5zdGFydHggKyBjNFNoYXBlLm1hcmdpbiArIGNvbmYubmV4dExpbmVQYWRkaW5nWDtcbiAgICAgIF9zdGFydHkgPSB0aGlzLm5leHREYXRhLnN0b3B5ICsgYzRTaGFwZS5tYXJnaW4gKiAyO1xuICAgICAgdGhpcy5uZXh0RGF0YS5zdG9weCA9IF9zdG9weCA9IF9zdGFydHggKyBjNFNoYXBlLndpZHRoO1xuICAgICAgdGhpcy5uZXh0RGF0YS5zdGFydHkgPSB0aGlzLm5leHREYXRhLnN0b3B5O1xuICAgICAgdGhpcy5uZXh0RGF0YS5zdG9weSA9IF9zdG9weSA9IF9zdGFydHkgKyBjNFNoYXBlLmhlaWdodDtcbiAgICAgIHRoaXMubmV4dERhdGEuY250ID0gMTtcbiAgICB9XG4gICAgYzRTaGFwZS54ID0gX3N0YXJ0eDtcbiAgICBjNFNoYXBlLnkgPSBfc3RhcnR5O1xuICAgIHRoaXMudXBkYXRlVmFsKHRoaXMuZGF0YSwgXCJzdGFydHhcIiwgX3N0YXJ0eCwgTWF0aC5taW4pO1xuICAgIHRoaXMudXBkYXRlVmFsKHRoaXMuZGF0YSwgXCJzdGFydHlcIiwgX3N0YXJ0eSwgTWF0aC5taW4pO1xuICAgIHRoaXMudXBkYXRlVmFsKHRoaXMuZGF0YSwgXCJzdG9weFwiLCBfc3RvcHgsIE1hdGgubWF4KTtcbiAgICB0aGlzLnVwZGF0ZVZhbCh0aGlzLmRhdGEsIFwic3RvcHlcIiwgX3N0b3B5LCBNYXRoLm1heCk7XG4gICAgdGhpcy51cGRhdGVWYWwodGhpcy5uZXh0RGF0YSwgXCJzdGFydHhcIiwgX3N0YXJ0eCwgTWF0aC5taW4pO1xuICAgIHRoaXMudXBkYXRlVmFsKHRoaXMubmV4dERhdGEsIFwic3RhcnR5XCIsIF9zdGFydHksIE1hdGgubWluKTtcbiAgICB0aGlzLnVwZGF0ZVZhbCh0aGlzLm5leHREYXRhLCBcInN0b3B4XCIsIF9zdG9weCwgTWF0aC5tYXgpO1xuICAgIHRoaXMudXBkYXRlVmFsKHRoaXMubmV4dERhdGEsIFwic3RvcHlcIiwgX3N0b3B5LCBNYXRoLm1heCk7XG4gIH1cbiAgaW5pdChkaWFnT2JqKSB7XG4gICAgdGhpcy5uYW1lID0gXCJcIjtcbiAgICB0aGlzLmRhdGEgPSB7XG4gICAgICBzdGFydHg6IHZvaWQgMCxcbiAgICAgIHN0b3B4OiB2b2lkIDAsXG4gICAgICBzdGFydHk6IHZvaWQgMCxcbiAgICAgIHN0b3B5OiB2b2lkIDAsXG4gICAgICB3aWR0aExpbWl0OiB2b2lkIDBcbiAgICB9O1xuICAgIHRoaXMubmV4dERhdGEgPSB7XG4gICAgICBzdGFydHg6IHZvaWQgMCxcbiAgICAgIHN0b3B4OiB2b2lkIDAsXG4gICAgICBzdGFydHk6IHZvaWQgMCxcbiAgICAgIHN0b3B5OiB2b2lkIDAsXG4gICAgICBjbnQ6IDBcbiAgICB9O1xuICAgIHNldENvbmYoZGlhZ09iai5kYi5nZXRDb25maWcoKSk7XG4gIH1cbiAgYnVtcExhc3RNYXJnaW4obWFyZ2luKSB7XG4gICAgdGhpcy5kYXRhLnN0b3B4ICs9IG1hcmdpbjtcbiAgICB0aGlzLmRhdGEuc3RvcHkgKz0gbWFyZ2luO1xuICB9XG59XG5jb25zdCBzZXRDb25mID0gZnVuY3Rpb24oY25mKSB7XG4gIGFzc2lnbldpdGhEZXB0aChjb25mLCBjbmYpO1xuICBpZiAoY25mLmZvbnRGYW1pbHkpIHtcbiAgICBjb25mLnBlcnNvbkZvbnRGYW1pbHkgPSBjb25mLnN5c3RlbUZvbnRGYW1pbHkgPSBjb25mLm1lc3NhZ2VGb250RmFtaWx5ID0gY25mLmZvbnRGYW1pbHk7XG4gIH1cbiAgaWYgKGNuZi5mb250U2l6ZSkge1xuICAgIGNvbmYucGVyc29uRm9udFNpemUgPSBjb25mLnN5c3RlbUZvbnRTaXplID0gY29uZi5tZXNzYWdlRm9udFNpemUgPSBjbmYuZm9udFNpemU7XG4gIH1cbiAgaWYgKGNuZi5mb250V2VpZ2h0KSB7XG4gICAgY29uZi5wZXJzb25Gb250V2VpZ2h0ID0gY29uZi5zeXN0ZW1Gb250V2VpZ2h0ID0gY29uZi5tZXNzYWdlRm9udFdlaWdodCA9IGNuZi5mb250V2VpZ2h0O1xuICB9XG59O1xuY29uc3QgYzRTaGFwZUZvbnQgPSAoY25mLCB0eXBlQzRTaGFwZSkgPT4ge1xuICByZXR1cm4ge1xuICAgIGZvbnRGYW1pbHk6IGNuZlt0eXBlQzRTaGFwZSArIFwiRm9udEZhbWlseVwiXSxcbiAgICBmb250U2l6ZTogY25mW3R5cGVDNFNoYXBlICsgXCJGb250U2l6ZVwiXSxcbiAgICBmb250V2VpZ2h0OiBjbmZbdHlwZUM0U2hhcGUgKyBcIkZvbnRXZWlnaHRcIl1cbiAgfTtcbn07XG5jb25zdCBib3VuZGFyeUZvbnQgPSAoY25mKSA9PiB7XG4gIHJldHVybiB7XG4gICAgZm9udEZhbWlseTogY25mLmJvdW5kYXJ5Rm9udEZhbWlseSxcbiAgICBmb250U2l6ZTogY25mLmJvdW5kYXJ5Rm9udFNpemUsXG4gICAgZm9udFdlaWdodDogY25mLmJvdW5kYXJ5Rm9udFdlaWdodFxuICB9O1xufTtcbmNvbnN0IG1lc3NhZ2VGb250ID0gKGNuZikgPT4ge1xuICByZXR1cm4ge1xuICAgIGZvbnRGYW1pbHk6IGNuZi5tZXNzYWdlRm9udEZhbWlseSxcbiAgICBmb250U2l6ZTogY25mLm1lc3NhZ2VGb250U2l6ZSxcbiAgICBmb250V2VpZ2h0OiBjbmYubWVzc2FnZUZvbnRXZWlnaHRcbiAgfTtcbn07XG5mdW5jdGlvbiBjYWxjQzRTaGFwZVRleHRXSCh0ZXh0VHlwZSwgYzRTaGFwZSwgYzRTaGFwZVRleHRXcmFwLCB0ZXh0Q29uZiwgdGV4dExpbWl0V2lkdGgpIHtcbiAgaWYgKCFjNFNoYXBlW3RleHRUeXBlXS53aWR0aCkge1xuICAgIGlmIChjNFNoYXBlVGV4dFdyYXApIHtcbiAgICAgIGM0U2hhcGVbdGV4dFR5cGVdLnRleHQgPSB3cmFwTGFiZWwoYzRTaGFwZVt0ZXh0VHlwZV0udGV4dCwgdGV4dExpbWl0V2lkdGgsIHRleHRDb25mKTtcbiAgICAgIGM0U2hhcGVbdGV4dFR5cGVdLnRleHRMaW5lcyA9IGM0U2hhcGVbdGV4dFR5cGVdLnRleHQuc3BsaXQoY29tbW9uLmxpbmVCcmVha1JlZ2V4KS5sZW5ndGg7XG4gICAgICBjNFNoYXBlW3RleHRUeXBlXS53aWR0aCA9IHRleHRMaW1pdFdpZHRoO1xuICAgICAgYzRTaGFwZVt0ZXh0VHlwZV0uaGVpZ2h0ID0gY2FsY3VsYXRlVGV4dEhlaWdodChjNFNoYXBlW3RleHRUeXBlXS50ZXh0LCB0ZXh0Q29uZik7XG4gICAgfSBlbHNlIHtcbiAgICAgIGxldCBsaW5lcyA9IGM0U2hhcGVbdGV4dFR5cGVdLnRleHQuc3BsaXQoY29tbW9uLmxpbmVCcmVha1JlZ2V4KTtcbiAgICAgIGM0U2hhcGVbdGV4dFR5cGVdLnRleHRMaW5lcyA9IGxpbmVzLmxlbmd0aDtcbiAgICAgIGxldCBsaW5lSGVpZ2h0ID0gMDtcbiAgICAgIGM0U2hhcGVbdGV4dFR5cGVdLmhlaWdodCA9IDA7XG4gICAgICBjNFNoYXBlW3RleHRUeXBlXS53aWR0aCA9IDA7XG4gICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHtcbiAgICAgICAgYzRTaGFwZVt0ZXh0VHlwZV0ud2lkdGggPSBNYXRoLm1heChcbiAgICAgICAgICBjYWxjdWxhdGVUZXh0V2lkdGgobGluZSwgdGV4dENvbmYpLFxuICAgICAgICAgIGM0U2hhcGVbdGV4dFR5cGVdLndpZHRoXG4gICAgICAgICk7XG4gICAgICAgIGxpbmVIZWlnaHQgPSBjYWxjdWxhdGVUZXh0SGVpZ2h0KGxpbmUsIHRleHRDb25mKTtcbiAgICAgICAgYzRTaGFwZVt0ZXh0VHlwZV0uaGVpZ2h0ID0gYzRTaGFwZVt0ZXh0VHlwZV0uaGVpZ2h0ICsgbGluZUhlaWdodDtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbmNvbnN0IGRyYXdCb3VuZGFyeSA9IGZ1bmN0aW9uKGRpYWdyYW0yLCBib3VuZGFyeSwgYm91bmRzKSB7XG4gIGJvdW5kYXJ5LnggPSBib3VuZHMuZGF0YS5zdGFydHg7XG4gIGJvdW5kYXJ5LnkgPSBib3VuZHMuZGF0YS5zdGFydHk7XG4gIGJvdW5kYXJ5LndpZHRoID0gYm91bmRzLmRhdGEuc3RvcHggLSBib3VuZHMuZGF0YS5zdGFydHg7XG4gIGJvdW5kYXJ5LmhlaWdodCA9IGJvdW5kcy5kYXRhLnN0b3B5IC0gYm91bmRzLmRhdGEuc3RhcnR5O1xuICBib3VuZGFyeS5sYWJlbC55ID0gY29uZi5jNFNoYXBlTWFyZ2luIC0gMzU7XG4gIGxldCBib3VuZGFyeVRleHRXcmFwID0gYm91bmRhcnkud3JhcCAmJiBjb25mLndyYXA7XG4gIGxldCBib3VuZGFyeUxhYmVsQ29uZiA9IGJvdW5kYXJ5Rm9udChjb25mKTtcbiAgYm91bmRhcnlMYWJlbENvbmYuZm9udFNpemUgPSBib3VuZGFyeUxhYmVsQ29uZi5mb250U2l6ZSArIDI7XG4gIGJvdW5kYXJ5TGFiZWxDb25mLmZvbnRXZWlnaHQgPSBcImJvbGRcIjtcbiAgbGV0IHRleHRMaW1pdFdpZHRoID0gY2FsY3VsYXRlVGV4dFdpZHRoKGJvdW5kYXJ5LmxhYmVsLnRleHQsIGJvdW5kYXJ5TGFiZWxDb25mKTtcbiAgY2FsY0M0U2hhcGVUZXh0V0goXCJsYWJlbFwiLCBib3VuZGFyeSwgYm91bmRhcnlUZXh0V3JhcCwgYm91bmRhcnlMYWJlbENvbmYsIHRleHRMaW1pdFdpZHRoKTtcbiAgc3ZnRHJhdy5kcmF3Qm91bmRhcnkoZGlhZ3JhbTIsIGJvdW5kYXJ5LCBjb25mKTtcbn07XG5jb25zdCBkcmF3QzRTaGFwZUFycmF5ID0gZnVuY3Rpb24oY3VycmVudEJvdW5kcywgZGlhZ3JhbTIsIGM0U2hhcGVBcnJheTIsIGM0U2hhcGVLZXlzKSB7XG4gIGxldCBZID0gMDtcbiAgZm9yIChjb25zdCBjNFNoYXBlS2V5IG9mIGM0U2hhcGVLZXlzKSB7XG4gICAgWSA9IDA7XG4gICAgY29uc3QgYzRTaGFwZSA9IGM0U2hhcGVBcnJheTJbYzRTaGFwZUtleV07XG4gICAgbGV0IGM0U2hhcGVUeXBlQ29uZiA9IGM0U2hhcGVGb250KGNvbmYsIGM0U2hhcGUudHlwZUM0U2hhcGUudGV4dCk7XG4gICAgYzRTaGFwZVR5cGVDb25mLmZvbnRTaXplID0gYzRTaGFwZVR5cGVDb25mLmZvbnRTaXplIC0gMjtcbiAgICBjNFNoYXBlLnR5cGVDNFNoYXBlLndpZHRoID0gY2FsY3VsYXRlVGV4dFdpZHRoKFxuICAgICAgXCLCq1wiICsgYzRTaGFwZS50eXBlQzRTaGFwZS50ZXh0ICsgXCLCu1wiLFxuICAgICAgYzRTaGFwZVR5cGVDb25mXG4gICAgKTtcbiAgICBjNFNoYXBlLnR5cGVDNFNoYXBlLmhlaWdodCA9IGM0U2hhcGVUeXBlQ29uZi5mb250U2l6ZSArIDI7XG4gICAgYzRTaGFwZS50eXBlQzRTaGFwZS5ZID0gY29uZi5jNFNoYXBlUGFkZGluZztcbiAgICBZID0gYzRTaGFwZS50eXBlQzRTaGFwZS5ZICsgYzRTaGFwZS50eXBlQzRTaGFwZS5oZWlnaHQgLSA0O1xuICAgIGM0U2hhcGUuaW1hZ2UgPSB7IHdpZHRoOiAwLCBoZWlnaHQ6IDAsIFk6IDAgfTtcbiAgICBzd2l0Y2ggKGM0U2hhcGUudHlwZUM0U2hhcGUudGV4dCkge1xuICAgICAgY2FzZSBcInBlcnNvblwiOlxuICAgICAgY2FzZSBcImV4dGVybmFsX3BlcnNvblwiOlxuICAgICAgICBjNFNoYXBlLmltYWdlLndpZHRoID0gNDg7XG4gICAgICAgIGM0U2hhcGUuaW1hZ2UuaGVpZ2h0ID0gNDg7XG4gICAgICAgIGM0U2hhcGUuaW1hZ2UuWSA9IFk7XG4gICAgICAgIFkgPSBjNFNoYXBlLmltYWdlLlkgKyBjNFNoYXBlLmltYWdlLmhlaWdodDtcbiAgICAgICAgYnJlYWs7XG4gICAgfVxuICAgIGlmIChjNFNoYXBlLnNwcml0ZSkge1xuICAgICAgYzRTaGFwZS5pbWFnZS53aWR0aCA9IDQ4O1xuICAgICAgYzRTaGFwZS5pbWFnZS5oZWlnaHQgPSA0ODtcbiAgICAgIGM0U2hhcGUuaW1hZ2UuWSA9IFk7XG4gICAgICBZID0gYzRTaGFwZS5pbWFnZS5ZICsgYzRTaGFwZS5pbWFnZS5oZWlnaHQ7XG4gICAgfVxuICAgIGxldCBjNFNoYXBlVGV4dFdyYXAgPSBjNFNoYXBlLndyYXAgJiYgY29uZi53cmFwO1xuICAgIGxldCB0ZXh0TGltaXRXaWR0aCA9IGNvbmYud2lkdGggLSBjb25mLmM0U2hhcGVQYWRkaW5nICogMjtcbiAgICBsZXQgYzRTaGFwZUxhYmVsQ29uZiA9IGM0U2hhcGVGb250KGNvbmYsIGM0U2hhcGUudHlwZUM0U2hhcGUudGV4dCk7XG4gICAgYzRTaGFwZUxhYmVsQ29uZi5mb250U2l6ZSA9IGM0U2hhcGVMYWJlbENvbmYuZm9udFNpemUgKyAyO1xuICAgIGM0U2hhcGVMYWJlbENvbmYuZm9udFdlaWdodCA9IFwiYm9sZFwiO1xuICAgIGNhbGNDNFNoYXBlVGV4dFdIKFwibGFiZWxcIiwgYzRTaGFwZSwgYzRTaGFwZVRleHRXcmFwLCBjNFNoYXBlTGFiZWxDb25mLCB0ZXh0TGltaXRXaWR0aCk7XG4gICAgYzRTaGFwZVtcImxhYmVsXCJdLlkgPSBZICsgODtcbiAgICBZID0gYzRTaGFwZVtcImxhYmVsXCJdLlkgKyBjNFNoYXBlW1wibGFiZWxcIl0uaGVpZ2h0O1xuICAgIGlmIChjNFNoYXBlLnR5cGUgJiYgYzRTaGFwZS50eXBlLnRleHQgIT09IFwiXCIpIHtcbiAgICAgIGM0U2hhcGUudHlwZS50ZXh0ID0gXCJbXCIgKyBjNFNoYXBlLnR5cGUudGV4dCArIFwiXVwiO1xuICAgICAgbGV0IGM0U2hhcGVUeXBlQ29uZjIgPSBjNFNoYXBlRm9udChjb25mLCBjNFNoYXBlLnR5cGVDNFNoYXBlLnRleHQpO1xuICAgICAgY2FsY0M0U2hhcGVUZXh0V0goXCJ0eXBlXCIsIGM0U2hhcGUsIGM0U2hhcGVUZXh0V3JhcCwgYzRTaGFwZVR5cGVDb25mMiwgdGV4dExpbWl0V2lkdGgpO1xuICAgICAgYzRTaGFwZVtcInR5cGVcIl0uWSA9IFkgKyA1O1xuICAgICAgWSA9IGM0U2hhcGVbXCJ0eXBlXCJdLlkgKyBjNFNoYXBlW1widHlwZVwiXS5oZWlnaHQ7XG4gICAgfSBlbHNlIGlmIChjNFNoYXBlLnRlY2huICYmIGM0U2hhcGUudGVjaG4udGV4dCAhPT0gXCJcIikge1xuICAgICAgYzRTaGFwZS50ZWNobi50ZXh0ID0gXCJbXCIgKyBjNFNoYXBlLnRlY2huLnRleHQgKyBcIl1cIjtcbiAgICAgIGxldCBjNFNoYXBlVGVjaG5Db25mID0gYzRTaGFwZUZvbnQoY29uZiwgYzRTaGFwZS50ZWNobi50ZXh0KTtcbiAgICAgIGNhbGNDNFNoYXBlVGV4dFdIKFwidGVjaG5cIiwgYzRTaGFwZSwgYzRTaGFwZVRleHRXcmFwLCBjNFNoYXBlVGVjaG5Db25mLCB0ZXh0TGltaXRXaWR0aCk7XG4gICAgICBjNFNoYXBlW1widGVjaG5cIl0uWSA9IFkgKyA1O1xuICAgICAgWSA9IGM0U2hhcGVbXCJ0ZWNoblwiXS5ZICsgYzRTaGFwZVtcInRlY2huXCJdLmhlaWdodDtcbiAgICB9XG4gICAgbGV0IHJlY3RIZWlnaHQgPSBZO1xuICAgIGxldCByZWN0V2lkdGggPSBjNFNoYXBlLmxhYmVsLndpZHRoO1xuICAgIGlmIChjNFNoYXBlLmRlc2NyICYmIGM0U2hhcGUuZGVzY3IudGV4dCAhPT0gXCJcIikge1xuICAgICAgbGV0IGM0U2hhcGVEZXNjckNvbmYgPSBjNFNoYXBlRm9udChjb25mLCBjNFNoYXBlLnR5cGVDNFNoYXBlLnRleHQpO1xuICAgICAgY2FsY0M0U2hhcGVUZXh0V0goXCJkZXNjclwiLCBjNFNoYXBlLCBjNFNoYXBlVGV4dFdyYXAsIGM0U2hhcGVEZXNjckNvbmYsIHRleHRMaW1pdFdpZHRoKTtcbiAgICAgIGM0U2hhcGVbXCJkZXNjclwiXS5ZID0gWSArIDIwO1xuICAgICAgWSA9IGM0U2hhcGVbXCJkZXNjclwiXS5ZICsgYzRTaGFwZVtcImRlc2NyXCJdLmhlaWdodDtcbiAgICAgIHJlY3RXaWR0aCA9IE1hdGgubWF4KGM0U2hhcGUubGFiZWwud2lkdGgsIGM0U2hhcGUuZGVzY3Iud2lkdGgpO1xuICAgICAgcmVjdEhlaWdodCA9IFkgLSBjNFNoYXBlW1wiZGVzY3JcIl0udGV4dExpbmVzICogNTtcbiAgICB9XG4gICAgcmVjdFdpZHRoID0gcmVjdFdpZHRoICsgY29uZi5jNFNoYXBlUGFkZGluZztcbiAgICBjNFNoYXBlLndpZHRoID0gTWF0aC5tYXgoYzRTaGFwZS53aWR0aCB8fCBjb25mLndpZHRoLCByZWN0V2lkdGgsIGNvbmYud2lkdGgpO1xuICAgIGM0U2hhcGUuaGVpZ2h0ID0gTWF0aC5tYXgoYzRTaGFwZS5oZWlnaHQgfHwgY29uZi5oZWlnaHQsIHJlY3RIZWlnaHQsIGNvbmYuaGVpZ2h0KTtcbiAgICBjNFNoYXBlLm1hcmdpbiA9IGM0U2hhcGUubWFyZ2luIHx8IGNvbmYuYzRTaGFwZU1hcmdpbjtcbiAgICBjdXJyZW50Qm91bmRzLmluc2VydChjNFNoYXBlKTtcbiAgICBzdmdEcmF3LmRyYXdDNFNoYXBlKGRpYWdyYW0yLCBjNFNoYXBlLCBjb25mKTtcbiAgfVxuICBjdXJyZW50Qm91bmRzLmJ1bXBMYXN0TWFyZ2luKGNvbmYuYzRTaGFwZU1hcmdpbik7XG59O1xuY2xhc3MgUG9pbnQge1xuICBjb25zdHJ1Y3Rvcih4LCB5KSB7XG4gICAgdGhpcy54ID0geDtcbiAgICB0aGlzLnkgPSB5O1xuICB9XG59XG5sZXQgZ2V0SW50ZXJzZWN0UG9pbnQgPSBmdW5jdGlvbihmcm9tTm9kZSwgZW5kUG9pbnQpIHtcbiAgbGV0IHgxID0gZnJvbU5vZGUueDtcbiAgbGV0IHkxID0gZnJvbU5vZGUueTtcbiAgbGV0IHgyID0gZW5kUG9pbnQueDtcbiAgbGV0IHkyID0gZW5kUG9pbnQueTtcbiAgbGV0IGZyb21DZW50ZXJYID0geDEgKyBmcm9tTm9kZS53aWR0aCAvIDI7XG4gIGxldCBmcm9tQ2VudGVyWSA9IHkxICsgZnJvbU5vZGUuaGVpZ2h0IC8gMjtcbiAgbGV0IGR4ID0gTWF0aC5hYnMoeDEgLSB4Mik7XG4gIGxldCBkeSA9IE1hdGguYWJzKHkxIC0geTIpO1xuICBsZXQgdGFuRFlYID0gZHkgLyBkeDtcbiAgbGV0IGZyb21EWVggPSBmcm9tTm9kZS5oZWlnaHQgLyBmcm9tTm9kZS53aWR0aDtcbiAgbGV0IHJldHVyblBvaW50ID0gbnVsbDtcbiAgaWYgKHkxID09IHkyICYmIHgxIDwgeDIpIHtcbiAgICByZXR1cm5Qb2ludCA9IG5ldyBQb2ludCh4MSArIGZyb21Ob2RlLndpZHRoLCBmcm9tQ2VudGVyWSk7XG4gIH0gZWxzZSBpZiAoeTEgPT0geTIgJiYgeDEgPiB4Mikge1xuICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KHgxLCBmcm9tQ2VudGVyWSk7XG4gIH0gZWxzZSBpZiAoeDEgPT0geDIgJiYgeTEgPCB5Mikge1xuICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KGZyb21DZW50ZXJYLCB5MSArIGZyb21Ob2RlLmhlaWdodCk7XG4gIH0gZWxzZSBpZiAoeDEgPT0geDIgJiYgeTEgPiB5Mikge1xuICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KGZyb21DZW50ZXJYLCB5MSk7XG4gIH1cbiAgaWYgKHgxID4geDIgJiYgeTEgPCB5Mikge1xuICAgIGlmIChmcm9tRFlYID49IHRhbkRZWCkge1xuICAgICAgcmV0dXJuUG9pbnQgPSBuZXcgUG9pbnQoeDEsIGZyb21DZW50ZXJZICsgdGFuRFlYICogZnJvbU5vZGUud2lkdGggLyAyKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuUG9pbnQgPSBuZXcgUG9pbnQoXG4gICAgICAgIGZyb21DZW50ZXJYIC0gZHggLyBkeSAqIGZyb21Ob2RlLmhlaWdodCAvIDIsXG4gICAgICAgIHkxICsgZnJvbU5vZGUuaGVpZ2h0XG4gICAgICApO1xuICAgIH1cbiAgfSBlbHNlIGlmICh4MSA8IHgyICYmIHkxIDwgeTIpIHtcbiAgICBpZiAoZnJvbURZWCA+PSB0YW5EWVgpIHtcbiAgICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KHgxICsgZnJvbU5vZGUud2lkdGgsIGZyb21DZW50ZXJZICsgdGFuRFlYICogZnJvbU5vZGUud2lkdGggLyAyKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuUG9pbnQgPSBuZXcgUG9pbnQoXG4gICAgICAgIGZyb21DZW50ZXJYICsgZHggLyBkeSAqIGZyb21Ob2RlLmhlaWdodCAvIDIsXG4gICAgICAgIHkxICsgZnJvbU5vZGUuaGVpZ2h0XG4gICAgICApO1xuICAgIH1cbiAgfSBlbHNlIGlmICh4MSA8IHgyICYmIHkxID4geTIpIHtcbiAgICBpZiAoZnJvbURZWCA+PSB0YW5EWVgpIHtcbiAgICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KHgxICsgZnJvbU5vZGUud2lkdGgsIGZyb21DZW50ZXJZIC0gdGFuRFlYICogZnJvbU5vZGUud2lkdGggLyAyKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuUG9pbnQgPSBuZXcgUG9pbnQoZnJvbUNlbnRlclggKyBmcm9tTm9kZS5oZWlnaHQgLyAyICogZHggLyBkeSwgeTEpO1xuICAgIH1cbiAgfSBlbHNlIGlmICh4MSA+IHgyICYmIHkxID4geTIpIHtcbiAgICBpZiAoZnJvbURZWCA+PSB0YW5EWVgpIHtcbiAgICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KHgxLCBmcm9tQ2VudGVyWSAtIGZyb21Ob2RlLndpZHRoIC8gMiAqIHRhbkRZWCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVyblBvaW50ID0gbmV3IFBvaW50KGZyb21DZW50ZXJYIC0gZnJvbU5vZGUuaGVpZ2h0IC8gMiAqIGR4IC8gZHksIHkxKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHJldHVyblBvaW50O1xufTtcbmxldCBnZXRJbnRlcnNlY3RQb2ludHMgPSBmdW5jdGlvbihmcm9tTm9kZSwgZW5kTm9kZSkge1xuICBsZXQgZW5kSW50ZXJzZWN0UG9pbnQgPSB7IHg6IDAsIHk6IDAgfTtcbiAgZW5kSW50ZXJzZWN0UG9pbnQueCA9IGVuZE5vZGUueCArIGVuZE5vZGUud2lkdGggLyAyO1xuICBlbmRJbnRlcnNlY3RQb2ludC55ID0gZW5kTm9kZS55ICsgZW5kTm9kZS5oZWlnaHQgLyAyO1xuICBsZXQgc3RhcnRQb2ludCA9IGdldEludGVyc2VjdFBvaW50KGZyb21Ob2RlLCBlbmRJbnRlcnNlY3RQb2ludCk7XG4gIGVuZEludGVyc2VjdFBvaW50LnggPSBmcm9tTm9kZS54ICsgZnJvbU5vZGUud2lkdGggLyAyO1xuICBlbmRJbnRlcnNlY3RQb2ludC55ID0gZnJvbU5vZGUueSArIGZyb21Ob2RlLmhlaWdodCAvIDI7XG4gIGxldCBlbmRQb2ludCA9IGdldEludGVyc2VjdFBvaW50KGVuZE5vZGUsIGVuZEludGVyc2VjdFBvaW50KTtcbiAgcmV0dXJuIHsgc3RhcnRQb2ludCwgZW5kUG9pbnQgfTtcbn07XG5jb25zdCBkcmF3UmVscyA9IGZ1bmN0aW9uKGRpYWdyYW0yLCByZWxzMiwgZ2V0QzRTaGFwZU9iaiwgZGlhZ09iaikge1xuICBsZXQgaSA9IDA7XG4gIGZvciAobGV0IHJlbCBvZiByZWxzMikge1xuICAgIGkgPSBpICsgMTtcbiAgICBsZXQgcmVsVGV4dFdyYXAgPSByZWwud3JhcCAmJiBjb25mLndyYXA7XG4gICAgbGV0IHJlbENvbmYgPSBtZXNzYWdlRm9udChjb25mKTtcbiAgICBsZXQgZGlhZ3JhbVR5cGUgPSBkaWFnT2JqLmRiLmdldEM0VHlwZSgpO1xuICAgIGlmIChkaWFncmFtVHlwZSA9PT0gXCJDNER5bmFtaWNcIikge1xuICAgICAgcmVsLmxhYmVsLnRleHQgPSBpICsgXCI6IFwiICsgcmVsLmxhYmVsLnRleHQ7XG4gICAgfVxuICAgIGxldCB0ZXh0TGltaXRXaWR0aCA9IGNhbGN1bGF0ZVRleHRXaWR0aChyZWwubGFiZWwudGV4dCwgcmVsQ29uZik7XG4gICAgY2FsY0M0U2hhcGVUZXh0V0goXCJsYWJlbFwiLCByZWwsIHJlbFRleHRXcmFwLCByZWxDb25mLCB0ZXh0TGltaXRXaWR0aCk7XG4gICAgaWYgKHJlbC50ZWNobiAmJiByZWwudGVjaG4udGV4dCAhPT0gXCJcIikge1xuICAgICAgdGV4dExpbWl0V2lkdGggPSBjYWxjdWxhdGVUZXh0V2lkdGgocmVsLnRlY2huLnRleHQsIHJlbENvbmYpO1xuICAgICAgY2FsY0M0U2hhcGVUZXh0V0goXCJ0ZWNoblwiLCByZWwsIHJlbFRleHRXcmFwLCByZWxDb25mLCB0ZXh0TGltaXRXaWR0aCk7XG4gICAgfVxuICAgIGlmIChyZWwuZGVzY3IgJiYgcmVsLmRlc2NyLnRleHQgIT09IFwiXCIpIHtcbiAgICAgIHRleHRMaW1pdFdpZHRoID0gY2FsY3VsYXRlVGV4dFdpZHRoKHJlbC5kZXNjci50ZXh0LCByZWxDb25mKTtcbiAgICAgIGNhbGNDNFNoYXBlVGV4dFdIKFwiZGVzY3JcIiwgcmVsLCByZWxUZXh0V3JhcCwgcmVsQ29uZiwgdGV4dExpbWl0V2lkdGgpO1xuICAgIH1cbiAgICBsZXQgZnJvbU5vZGUgPSBnZXRDNFNoYXBlT2JqKHJlbC5mcm9tKTtcbiAgICBsZXQgZW5kTm9kZSA9IGdldEM0U2hhcGVPYmoocmVsLnRvKTtcbiAgICBsZXQgcG9pbnRzID0gZ2V0SW50ZXJzZWN0UG9pbnRzKGZyb21Ob2RlLCBlbmROb2RlKTtcbiAgICByZWwuc3RhcnRQb2ludCA9IHBvaW50cy5zdGFydFBvaW50O1xuICAgIHJlbC5lbmRQb2ludCA9IHBvaW50cy5lbmRQb2ludDtcbiAgfVxuICBzdmdEcmF3LmRyYXdSZWxzKGRpYWdyYW0yLCByZWxzMiwgY29uZik7XG59O1xuZnVuY3Rpb24gZHJhd0luc2lkZUJvdW5kYXJ5KGRpYWdyYW0yLCBwYXJlbnRCb3VuZGFyeUFsaWFzLCBwYXJlbnRCb3VuZHMsIGN1cnJlbnRCb3VuZGFyaWVzLCBkaWFnT2JqKSB7XG4gIGxldCBjdXJyZW50Qm91bmRzID0gbmV3IEJvdW5kcyhkaWFnT2JqKTtcbiAgY3VycmVudEJvdW5kcy5kYXRhLndpZHRoTGltaXQgPSBwYXJlbnRCb3VuZHMuZGF0YS53aWR0aExpbWl0IC8gTWF0aC5taW4oYzRCb3VuZGFyeUluUm93LCBjdXJyZW50Qm91bmRhcmllcy5sZW5ndGgpO1xuICBmb3IgKGxldCBbaSwgY3VycmVudEJvdW5kYXJ5XSBvZiBjdXJyZW50Qm91bmRhcmllcy5lbnRyaWVzKCkpIHtcbiAgICBsZXQgWSA9IDA7XG4gICAgY3VycmVudEJvdW5kYXJ5LmltYWdlID0geyB3aWR0aDogMCwgaGVpZ2h0OiAwLCBZOiAwIH07XG4gICAgaWYgKGN1cnJlbnRCb3VuZGFyeS5zcHJpdGUpIHtcbiAgICAgIGN1cnJlbnRCb3VuZGFyeS5pbWFnZS53aWR0aCA9IDQ4O1xuICAgICAgY3VycmVudEJvdW5kYXJ5LmltYWdlLmhlaWdodCA9IDQ4O1xuICAgICAgY3VycmVudEJvdW5kYXJ5LmltYWdlLlkgPSBZO1xuICAgICAgWSA9IGN1cnJlbnRCb3VuZGFyeS5pbWFnZS5ZICsgY3VycmVudEJvdW5kYXJ5LmltYWdlLmhlaWdodDtcbiAgICB9XG4gICAgbGV0IGN1cnJlbnRCb3VuZGFyeVRleHRXcmFwID0gY3VycmVudEJvdW5kYXJ5LndyYXAgJiYgY29uZi53cmFwO1xuICAgIGxldCBjdXJyZW50Qm91bmRhcnlMYWJlbENvbmYgPSBib3VuZGFyeUZvbnQoY29uZik7XG4gICAgY3VycmVudEJvdW5kYXJ5TGFiZWxDb25mLmZvbnRTaXplID0gY3VycmVudEJvdW5kYXJ5TGFiZWxDb25mLmZvbnRTaXplICsgMjtcbiAgICBjdXJyZW50Qm91bmRhcnlMYWJlbENvbmYuZm9udFdlaWdodCA9IFwiYm9sZFwiO1xuICAgIGNhbGNDNFNoYXBlVGV4dFdIKFxuICAgICAgXCJsYWJlbFwiLFxuICAgICAgY3VycmVudEJvdW5kYXJ5LFxuICAgICAgY3VycmVudEJvdW5kYXJ5VGV4dFdyYXAsXG4gICAgICBjdXJyZW50Qm91bmRhcnlMYWJlbENvbmYsXG4gICAgICBjdXJyZW50Qm91bmRzLmRhdGEud2lkdGhMaW1pdFxuICAgICk7XG4gICAgY3VycmVudEJvdW5kYXJ5W1wibGFiZWxcIl0uWSA9IFkgKyA4O1xuICAgIFkgPSBjdXJyZW50Qm91bmRhcnlbXCJsYWJlbFwiXS5ZICsgY3VycmVudEJvdW5kYXJ5W1wibGFiZWxcIl0uaGVpZ2h0O1xuICAgIGlmIChjdXJyZW50Qm91bmRhcnkudHlwZSAmJiBjdXJyZW50Qm91bmRhcnkudHlwZS50ZXh0ICE9PSBcIlwiKSB7XG4gICAgICBjdXJyZW50Qm91bmRhcnkudHlwZS50ZXh0ID0gXCJbXCIgKyBjdXJyZW50Qm91bmRhcnkudHlwZS50ZXh0ICsgXCJdXCI7XG4gICAgICBsZXQgY3VycmVudEJvdW5kYXJ5VHlwZUNvbmYgPSBib3VuZGFyeUZvbnQoY29uZik7XG4gICAgICBjYWxjQzRTaGFwZVRleHRXSChcbiAgICAgICAgXCJ0eXBlXCIsXG4gICAgICAgIGN1cnJlbnRCb3VuZGFyeSxcbiAgICAgICAgY3VycmVudEJvdW5kYXJ5VGV4dFdyYXAsXG4gICAgICAgIGN1cnJlbnRCb3VuZGFyeVR5cGVDb25mLFxuICAgICAgICBjdXJyZW50Qm91bmRzLmRhdGEud2lkdGhMaW1pdFxuICAgICAgKTtcbiAgICAgIGN1cnJlbnRCb3VuZGFyeVtcInR5cGVcIl0uWSA9IFkgKyA1O1xuICAgICAgWSA9IGN1cnJlbnRCb3VuZGFyeVtcInR5cGVcIl0uWSArIGN1cnJlbnRCb3VuZGFyeVtcInR5cGVcIl0uaGVpZ2h0O1xuICAgIH1cbiAgICBpZiAoY3VycmVudEJvdW5kYXJ5LmRlc2NyICYmIGN1cnJlbnRCb3VuZGFyeS5kZXNjci50ZXh0ICE9PSBcIlwiKSB7XG4gICAgICBsZXQgY3VycmVudEJvdW5kYXJ5RGVzY3JDb25mID0gYm91bmRhcnlGb250KGNvbmYpO1xuICAgICAgY3VycmVudEJvdW5kYXJ5RGVzY3JDb25mLmZvbnRTaXplID0gY3VycmVudEJvdW5kYXJ5RGVzY3JDb25mLmZvbnRTaXplIC0gMjtcbiAgICAgIGNhbGNDNFNoYXBlVGV4dFdIKFxuICAgICAgICBcImRlc2NyXCIsXG4gICAgICAgIGN1cnJlbnRCb3VuZGFyeSxcbiAgICAgICAgY3VycmVudEJvdW5kYXJ5VGV4dFdyYXAsXG4gICAgICAgIGN1cnJlbnRCb3VuZGFyeURlc2NyQ29uZixcbiAgICAgICAgY3VycmVudEJvdW5kcy5kYXRhLndpZHRoTGltaXRcbiAgICAgICk7XG4gICAgICBjdXJyZW50Qm91bmRhcnlbXCJkZXNjclwiXS5ZID0gWSArIDIwO1xuICAgICAgWSA9IGN1cnJlbnRCb3VuZGFyeVtcImRlc2NyXCJdLlkgKyBjdXJyZW50Qm91bmRhcnlbXCJkZXNjclwiXS5oZWlnaHQ7XG4gICAgfVxuICAgIGlmIChpID09IDAgfHwgaSAlIGM0Qm91bmRhcnlJblJvdyA9PT0gMCkge1xuICAgICAgbGV0IF94ID0gcGFyZW50Qm91bmRzLmRhdGEuc3RhcnR4ICsgY29uZi5kaWFncmFtTWFyZ2luWDtcbiAgICAgIGxldCBfeSA9IHBhcmVudEJvdW5kcy5kYXRhLnN0b3B5ICsgY29uZi5kaWFncmFtTWFyZ2luWSArIFk7XG4gICAgICBjdXJyZW50Qm91bmRzLnNldERhdGEoX3gsIF94LCBfeSwgX3kpO1xuICAgIH0gZWxzZSB7XG4gICAgICBsZXQgX3ggPSBjdXJyZW50Qm91bmRzLmRhdGEuc3RvcHggIT09IGN1cnJlbnRCb3VuZHMuZGF0YS5zdGFydHggPyBjdXJyZW50Qm91bmRzLmRhdGEuc3RvcHggKyBjb25mLmRpYWdyYW1NYXJnaW5YIDogY3VycmVudEJvdW5kcy5kYXRhLnN0YXJ0eDtcbiAgICAgIGxldCBfeSA9IGN1cnJlbnRCb3VuZHMuZGF0YS5zdGFydHk7XG4gICAgICBjdXJyZW50Qm91bmRzLnNldERhdGEoX3gsIF94LCBfeSwgX3kpO1xuICAgIH1cbiAgICBjdXJyZW50Qm91bmRzLm5hbWUgPSBjdXJyZW50Qm91bmRhcnkuYWxpYXM7XG4gICAgbGV0IGN1cnJlbnRQZXJzb25PclN5c3RlbUFycmF5ID0gZGlhZ09iai5kYi5nZXRDNFNoYXBlQXJyYXkoY3VycmVudEJvdW5kYXJ5LmFsaWFzKTtcbiAgICBsZXQgY3VycmVudFBlcnNvbk9yU3lzdGVtS2V5cyA9IGRpYWdPYmouZGIuZ2V0QzRTaGFwZUtleXMoY3VycmVudEJvdW5kYXJ5LmFsaWFzKTtcbiAgICBpZiAoY3VycmVudFBlcnNvbk9yU3lzdGVtS2V5cy5sZW5ndGggPiAwKSB7XG4gICAgICBkcmF3QzRTaGFwZUFycmF5KFxuICAgICAgICBjdXJyZW50Qm91bmRzLFxuICAgICAgICBkaWFncmFtMixcbiAgICAgICAgY3VycmVudFBlcnNvbk9yU3lzdGVtQXJyYXksXG4gICAgICAgIGN1cnJlbnRQZXJzb25PclN5c3RlbUtleXNcbiAgICAgICk7XG4gICAgfVxuICAgIHBhcmVudEJvdW5kYXJ5QWxpYXMgPSBjdXJyZW50Qm91bmRhcnkuYWxpYXM7XG4gICAgbGV0IG5leHRDdXJyZW50Qm91bmRhcmllcyA9IGRpYWdPYmouZGIuZ2V0Qm91bmRhcnlzKHBhcmVudEJvdW5kYXJ5QWxpYXMpO1xuICAgIGlmIChuZXh0Q3VycmVudEJvdW5kYXJpZXMubGVuZ3RoID4gMCkge1xuICAgICAgZHJhd0luc2lkZUJvdW5kYXJ5KFxuICAgICAgICBkaWFncmFtMixcbiAgICAgICAgcGFyZW50Qm91bmRhcnlBbGlhcyxcbiAgICAgICAgY3VycmVudEJvdW5kcyxcbiAgICAgICAgbmV4dEN1cnJlbnRCb3VuZGFyaWVzLFxuICAgICAgICBkaWFnT2JqXG4gICAgICApO1xuICAgIH1cbiAgICBpZiAoY3VycmVudEJvdW5kYXJ5LmFsaWFzICE9PSBcImdsb2JhbFwiKSB7XG4gICAgICBkcmF3Qm91bmRhcnkoZGlhZ3JhbTIsIGN1cnJlbnRCb3VuZGFyeSwgY3VycmVudEJvdW5kcyk7XG4gICAgfVxuICAgIHBhcmVudEJvdW5kcy5kYXRhLnN0b3B5ID0gTWF0aC5tYXgoXG4gICAgICBjdXJyZW50Qm91bmRzLmRhdGEuc3RvcHkgKyBjb25mLmM0U2hhcGVNYXJnaW4sXG4gICAgICBwYXJlbnRCb3VuZHMuZGF0YS5zdG9weVxuICAgICk7XG4gICAgcGFyZW50Qm91bmRzLmRhdGEuc3RvcHggPSBNYXRoLm1heChcbiAgICAgIGN1cnJlbnRCb3VuZHMuZGF0YS5zdG9weCArIGNvbmYuYzRTaGFwZU1hcmdpbixcbiAgICAgIHBhcmVudEJvdW5kcy5kYXRhLnN0b3B4XG4gICAgKTtcbiAgICBnbG9iYWxCb3VuZGFyeU1heFggPSBNYXRoLm1heChnbG9iYWxCb3VuZGFyeU1heFgsIHBhcmVudEJvdW5kcy5kYXRhLnN0b3B4KTtcbiAgICBnbG9iYWxCb3VuZGFyeU1heFkgPSBNYXRoLm1heChnbG9iYWxCb3VuZGFyeU1heFksIHBhcmVudEJvdW5kcy5kYXRhLnN0b3B5KTtcbiAgfVxufVxuY29uc3QgZHJhdyA9IGZ1bmN0aW9uKF90ZXh0LCBpZCwgX3ZlcnNpb24sIGRpYWdPYmopIHtcbiAgY29uZiA9IGdldENvbmZpZygpLmM0O1xuICBjb25zdCBzZWN1cml0eUxldmVsID0gZ2V0Q29uZmlnKCkuc2VjdXJpdHlMZXZlbDtcbiAgbGV0IHNhbmRib3hFbGVtZW50O1xuICBpZiAoc2VjdXJpdHlMZXZlbCA9PT0gXCJzYW5kYm94XCIpIHtcbiAgICBzYW5kYm94RWxlbWVudCA9IHNlbGVjdChcIiNpXCIgKyBpZCk7XG4gIH1cbiAgY29uc3Qgcm9vdCA9IHNlY3VyaXR5TGV2ZWwgPT09IFwic2FuZGJveFwiID8gc2VsZWN0KHNhbmRib3hFbGVtZW50Lm5vZGVzKClbMF0uY29udGVudERvY3VtZW50LmJvZHkpIDogc2VsZWN0KFwiYm9keVwiKTtcbiAgbGV0IGRiMiA9IGRpYWdPYmouZGI7XG4gIGRpYWdPYmouZGIuc2V0V3JhcChjb25mLndyYXApO1xuICBjNFNoYXBlSW5Sb3cgPSBkYjIuZ2V0QzRTaGFwZUluUm93KCk7XG4gIGM0Qm91bmRhcnlJblJvdyA9IGRiMi5nZXRDNEJvdW5kYXJ5SW5Sb3coKTtcbiAgbG9nLmRlYnVnKGBDOiR7SlNPTi5zdHJpbmdpZnkoY29uZiwgbnVsbCwgMil9YCk7XG4gIGNvbnN0IGRpYWdyYW0yID0gc2VjdXJpdHlMZXZlbCA9PT0gXCJzYW5kYm94XCIgPyByb290LnNlbGVjdChgW2lkPVwiJHtpZH1cIl1gKSA6IHNlbGVjdChgW2lkPVwiJHtpZH1cIl1gKTtcbiAgc3ZnRHJhdy5pbnNlcnRDb21wdXRlckljb24oZGlhZ3JhbTIpO1xuICBzdmdEcmF3Lmluc2VydERhdGFiYXNlSWNvbihkaWFncmFtMik7XG4gIHN2Z0RyYXcuaW5zZXJ0Q2xvY2tJY29uKGRpYWdyYW0yKTtcbiAgbGV0IHNjcmVlbkJvdW5kcyA9IG5ldyBCb3VuZHMoZGlhZ09iaik7XG4gIHNjcmVlbkJvdW5kcy5zZXREYXRhKFxuICAgIGNvbmYuZGlhZ3JhbU1hcmdpblgsXG4gICAgY29uZi5kaWFncmFtTWFyZ2luWCxcbiAgICBjb25mLmRpYWdyYW1NYXJnaW5ZLFxuICAgIGNvbmYuZGlhZ3JhbU1hcmdpbllcbiAgKTtcbiAgc2NyZWVuQm91bmRzLmRhdGEud2lkdGhMaW1pdCA9IHNjcmVlbi5hdmFpbFdpZHRoO1xuICBnbG9iYWxCb3VuZGFyeU1heFggPSBjb25mLmRpYWdyYW1NYXJnaW5YO1xuICBnbG9iYWxCb3VuZGFyeU1heFkgPSBjb25mLmRpYWdyYW1NYXJnaW5ZO1xuICBjb25zdCB0aXRsZTIgPSBkaWFnT2JqLmRiLmdldFRpdGxlKCk7XG4gIGxldCBjdXJyZW50Qm91bmRhcmllcyA9IGRpYWdPYmouZGIuZ2V0Qm91bmRhcnlzKFwiXCIpO1xuICBkcmF3SW5zaWRlQm91bmRhcnkoZGlhZ3JhbTIsIFwiXCIsIHNjcmVlbkJvdW5kcywgY3VycmVudEJvdW5kYXJpZXMsIGRpYWdPYmopO1xuICBzdmdEcmF3Lmluc2VydEFycm93SGVhZChkaWFncmFtMik7XG4gIHN2Z0RyYXcuaW5zZXJ0QXJyb3dFbmQoZGlhZ3JhbTIpO1xuICBzdmdEcmF3Lmluc2VydEFycm93Q3Jvc3NIZWFkKGRpYWdyYW0yKTtcbiAgc3ZnRHJhdy5pbnNlcnRBcnJvd0ZpbGxlZEhlYWQoZGlhZ3JhbTIpO1xuICBkcmF3UmVscyhkaWFncmFtMiwgZGlhZ09iai5kYi5nZXRSZWxzKCksIGRpYWdPYmouZGIuZ2V0QzRTaGFwZSwgZGlhZ09iaik7XG4gIHNjcmVlbkJvdW5kcy5kYXRhLnN0b3B4ID0gZ2xvYmFsQm91bmRhcnlNYXhYO1xuICBzY3JlZW5Cb3VuZHMuZGF0YS5zdG9weSA9IGdsb2JhbEJvdW5kYXJ5TWF4WTtcbiAgY29uc3QgYm94ID0gc2NyZWVuQm91bmRzLmRhdGE7XG4gIGxldCBib3hIZWlnaHQgPSBib3guc3RvcHkgLSBib3guc3RhcnR5O1xuICBsZXQgaGVpZ2h0ID0gYm94SGVpZ2h0ICsgMiAqIGNvbmYuZGlhZ3JhbU1hcmdpblk7XG4gIGxldCBib3hXaWR0aCA9IGJveC5zdG9weCAtIGJveC5zdGFydHg7XG4gIGNvbnN0IHdpZHRoID0gYm94V2lkdGggKyAyICogY29uZi5kaWFncmFtTWFyZ2luWDtcbiAgaWYgKHRpdGxlMikge1xuICAgIGRpYWdyYW0yLmFwcGVuZChcInRleHRcIikudGV4dCh0aXRsZTIpLmF0dHIoXCJ4XCIsIChib3guc3RvcHggLSBib3guc3RhcnR4KSAvIDIgLSA0ICogY29uZi5kaWFncmFtTWFyZ2luWCkuYXR0cihcInlcIiwgYm94LnN0YXJ0eSArIGNvbmYuZGlhZ3JhbU1hcmdpblkpO1xuICB9XG4gIGNvbmZpZ3VyZVN2Z1NpemUoZGlhZ3JhbTIsIGhlaWdodCwgd2lkdGgsIGNvbmYudXNlTWF4V2lkdGgpO1xuICBjb25zdCBleHRyYVZlcnRGb3JUaXRsZSA9IHRpdGxlMiA/IDYwIDogMDtcbiAgZGlhZ3JhbTIuYXR0cihcbiAgICBcInZpZXdCb3hcIixcbiAgICBib3guc3RhcnR4IC0gY29uZi5kaWFncmFtTWFyZ2luWCArIFwiIC1cIiArIChjb25mLmRpYWdyYW1NYXJnaW5ZICsgZXh0cmFWZXJ0Rm9yVGl0bGUpICsgXCIgXCIgKyB3aWR0aCArIFwiIFwiICsgKGhlaWdodCArIGV4dHJhVmVydEZvclRpdGxlKVxuICApO1xuICBsb2cuZGVidWcoYG1vZGVsczpgLCBib3gpO1xufTtcbmNvbnN0IHJlbmRlcmVyID0ge1xuICBkcmF3UGVyc29uT3JTeXN0ZW1BcnJheTogZHJhd0M0U2hhcGVBcnJheSxcbiAgZHJhd0JvdW5kYXJ5LFxuICBzZXRDb25mLFxuICBkcmF3XG59O1xuY29uc3QgZ2V0U3R5bGVzID0gKG9wdGlvbnMpID0+IGAucGVyc29uIHtcbiAgICBzdHJva2U6ICR7b3B0aW9ucy5wZXJzb25Cb3JkZXJ9O1xuICAgIGZpbGw6ICR7b3B0aW9ucy5wZXJzb25Ca2d9O1xuICB9XG5gO1xuY29uc3Qgc3R5bGVzID0gZ2V0U3R5bGVzO1xuY29uc3QgZGlhZ3JhbSA9IHtcbiAgcGFyc2VyOiBwYXJzZXIkMSxcbiAgZGIsXG4gIHJlbmRlcmVyLFxuICBzdHlsZXMsXG4gIGluaXQ6ICh7IGM0LCB3cmFwIH0pID0+IHtcbiAgICByZW5kZXJlci5zZXRDb25mKGM0KTtcbiAgICBkYi5zZXRXcmFwKHdyYXApO1xuICB9XG59O1xuZXhwb3J0IHtcbiAgZGlhZ3JhbVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/c4Diagram-3d4e48cf.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js":
/*!*************************************************************!*\
  !*** ./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   a: () => (/* binding */ drawBackgroundRect),\n/* harmony export */   b: () => (/* binding */ drawEmbeddedImage),\n/* harmony export */   c: () => (/* binding */ drawImage),\n/* harmony export */   d: () => (/* binding */ drawRect),\n/* harmony export */   e: () => (/* binding */ getTextObj),\n/* harmony export */   f: () => (/* binding */ drawText),\n/* harmony export */   g: () => (/* binding */ getNoteRect)\n/* harmony export */ });\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n\n\nconst drawRect = (element, rectData) => {\n  const rectElement = element.append(\"rect\");\n  rectElement.attr(\"x\", rectData.x);\n  rectElement.attr(\"y\", rectData.y);\n  rectElement.attr(\"fill\", rectData.fill);\n  rectElement.attr(\"stroke\", rectData.stroke);\n  rectElement.attr(\"width\", rectData.width);\n  rectElement.attr(\"height\", rectData.height);\n  if (rectData.name) {\n    rectElement.attr(\"name\", rectData.name);\n  }\n  rectData.rx !== void 0 && rectElement.attr(\"rx\", rectData.rx);\n  rectData.ry !== void 0 && rectElement.attr(\"ry\", rectData.ry);\n  if (rectData.attrs !== void 0) {\n    for (const attrKey in rectData.attrs) {\n      rectElement.attr(attrKey, rectData.attrs[attrKey]);\n    }\n  }\n  rectData.class !== void 0 && rectElement.attr(\"class\", rectData.class);\n  return rectElement;\n};\nconst drawBackgroundRect = (element, bounds) => {\n  const rectData = {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    stroke: bounds.stroke,\n    class: \"rect\"\n  };\n  const rectElement = drawRect(element, rectData);\n  rectElement.lower();\n};\nconst drawText = (element, textData) => {\n  const nText = textData.text.replace(_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_1__.J, \" \");\n  const textElem = element.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  textData.class !== void 0 && textElem.attr(\"class\", textData.class);\n  const tspan = textElem.append(\"tspan\");\n  tspan.attr(\"x\", textData.x + textData.textMargin * 2);\n  tspan.text(nText);\n  return textElem;\n};\nconst drawImage = (elem, x, y, link) => {\n  const imageElement = elem.append(\"image\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", sanitizedLink);\n};\nconst drawEmbeddedImage = (element, x, y, link) => {\n  const imageElement = element.append(\"use\");\n  imageElement.attr(\"x\", x);\n  imageElement.attr(\"y\", y);\n  const sanitizedLink = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__.sanitizeUrl)(link);\n  imageElement.attr(\"xlink:href\", `#${sanitizedLink}`);\n};\nconst getNoteRect = () => {\n  const noteRectData = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    fill: \"#EDF2AE\",\n    stroke: \"#666\",\n    anchor: \"start\",\n    rx: 0,\n    ry: 0\n  };\n  return noteRectData;\n};\nconst getTextObj = () => {\n  const testObject = {\n    x: 0,\n    y: 0,\n    width: 100,\n    height: 100,\n    \"text-anchor\": \"start\",\n    style: \"#666\",\n    textMargin: 0,\n    rx: 0,\n    ry: 0,\n    tspan: true\n  };\n  return testObject;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/svgDrawCommon-08f97a94.js\n"));

/***/ })

}]);