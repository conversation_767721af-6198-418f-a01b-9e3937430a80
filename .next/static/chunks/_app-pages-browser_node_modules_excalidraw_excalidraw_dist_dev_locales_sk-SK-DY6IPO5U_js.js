"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_sk-SK-DY6IPO5U_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/sk-SK-DY6IPO5U.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/sk-SK-DY6IPO5U.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ sk_SK_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/sk-SK.json\nvar labels = {\n  paste: \"Vlo\\u017Ei\\u0165\",\n  pasteAsPlaintext: \"Vlo\\u017Ei\\u0165 ako oby\\u010Dajn\\xFD text\",\n  pasteCharts: \"Vlo\\u017Ei\\u0165 grafy\",\n  selectAll: \"Vybra\\u0165 v\\u0161etko\",\n  multiSelect: \"Prida\\u0165 prvok do v\\xFDberu\",\n  moveCanvas: \"Pohyb pl\\xE1tna\",\n  cut: \"Vystrihn\\xFA\\u0165\",\n  copy: \"Kop\\xEDrova\\u0165\",\n  copyAsPng: \"Kop\\xEDrova\\u0165 do schr\\xE1nky ako PNG\",\n  copyAsSvg: \"Kop\\xEDrova\\u0165 do schr\\xE1nky ako SVG\",\n  copyText: \"Kop\\xEDrova\\u0165 do schr\\xE1nky ako text\",\n  copySource: \"Kop\\xEDrova\\u0165 k\\xF3d do schr\\xE1nky\",\n  convertToCode: \"Konvertova\\u0165 na k\\xF3d\",\n  bringForward: \"Presun\\xFA\\u0165 o \\xFArove\\u0148 dopredu\",\n  sendToBack: \"Presun\\xFA\\u0165 dozadu\",\n  bringToFront: \"Presun\\xFA\\u0165 dopredu\",\n  sendBackward: \"Presun\\xFA\\u0165 o \\xFArove\\u0148 dozadu\",\n  delete: \"Vymaza\\u0165\",\n  copyStyles: \"Kop\\xEDrova\\u0165 \\u0161t\\xFDly\",\n  pasteStyles: \"Vlo\\u017Ei\\u0165 \\u0161t\\xFDly\",\n  stroke: \"Obrys\",\n  background: \"Pozadie\",\n  fill: \"V\\xFDpl\\u0148\",\n  strokeWidth: \"Hr\\xFAbka obrysu\",\n  strokeStyle: \"\\u0160t\\xFDl obrysu\",\n  strokeStyle_solid: \"Pln\\xFD\",\n  strokeStyle_dashed: \"\\u010Ciarkovan\\xFD\",\n  strokeStyle_dotted: \"Bodkovan\\xFD\",\n  sloppiness: \"\\u0160tyliz\\xE1cia\",\n  opacity: \"Prieh\\u013Eadnos\\u0165\",\n  textAlign: \"Zarovnanie textu\",\n  edges: \"Okraje\",\n  sharp: \"Ostr\\xE9\",\n  round: \"Zaokr\\xFAhlen\\xE9\",\n  arrowheads: \"Zakon\\u010Denie \\u0161\\xEDpky\",\n  arrowhead_none: \"\\u017Diadne\",\n  arrowhead_arrow: \"\\u0160\\xEDpka\",\n  arrowhead_bar: \"\\u010Ciara\",\n  arrowhead_circle: \"Kruh\",\n  arrowhead_circle_outline: \"Kruh (obrys)\",\n  arrowhead_triangle: \"Trojuholn\\xEDk\",\n  arrowhead_triangle_outline: \"Trojuholn\\xEDk (obrys)\",\n  arrowhead_diamond: \"Diamant\",\n  arrowhead_diamond_outline: \"Diamant (obrys)\",\n  fontSize: \"Ve\\u013Ekos\\u0165 p\\xEDsma\",\n  fontFamily: \"P\\xEDsmo\",\n  addWatermark: 'Prida\\u0165 \"Vytvoren\\xE9 s Excalidraw\"',\n  handDrawn: \"Ru\\u010Dne p\\xEDsan\\xE9\",\n  normal: \"Norm\\xE1lne\",\n  code: \"K\\xF3d\",\n  small: \"Mal\\xE9\",\n  medium: \"Stredn\\xE9\",\n  large: \"Ve\\u013Ek\\xE9\",\n  veryLarge: \"Ve\\u013Emi ve\\u013Ek\\xE9\",\n  solid: \"Pln\\xE1\",\n  hachure: \"\\u0160rafovan\\xE1\",\n  zigzag: \"Cik-cak\",\n  crossHatch: \"Mrie\\u017Ekovan\\xE1\",\n  thin: \"Tenk\\xE1\",\n  bold: \"Hrub\\xE1\",\n  left: \"Do\\u013Eava\",\n  center: \"Na stred\",\n  right: \"Doprava\",\n  extraBold: \"Ve\\u013Emi hrub\\xE1\",\n  architect: \"Architekt\",\n  artist: \"Umelec\",\n  cartoonist: \"Ilustr\\xE1tor\",\n  fileTitle: \"N\\xE1zov s\\xFAboru\",\n  colorPicker: \"V\\xFDber farby\",\n  canvasColors: \"Pou\\u017Eit\\xE9 na pl\\xE1tne\",\n  canvasBackground: \"Pozadie pl\\xE1tna\",\n  drawingCanvas: \"Kresliace pl\\xE1tno\",\n  layers: \"Vrstvy\",\n  actions: \"Akcie\",\n  language: \"Jazyk\",\n  liveCollaboration: \"\\u017Div\\xE1 spolupr\\xE1ca...\",\n  duplicateSelection: \"Duplikova\\u0165\",\n  untitled: \"Bez n\\xE1zvu\",\n  name: \"Meno\",\n  yourName: \"Va\\u0161e meno\",\n  madeWithExcalidraw: \"Vytvoren\\xE9 s Excalidraw\",\n  group: \"Zoskupi\\u0165\",\n  ungroup: \"Zru\\u0161i\\u0165 zoskupenie\",\n  collaborators: \"Spolupracovn\\xEDci\",\n  showGrid: \"Zobrazi\\u0165 mrie\\u017Eku\",\n  addToLibrary: \"Prida\\u0165 do kni\\u017Enice\",\n  removeFromLibrary: \"Odstr\\xE1ni\\u0165 z kni\\u017Enice\",\n  libraryLoadingMessage: \"Na\\u010D\\xEDtavanie kni\\u017Enice\\u2026\",\n  libraries: \"Prehliada\\u0165 kni\\u017Enice\",\n  loadingScene: \"Na\\u010D\\xEDtavanie sc\\xE9ny\\u2026\",\n  align: \"Zarovnanie\",\n  alignTop: \"Zarovna\\u0165 nahor\",\n  alignBottom: \"Zarovna\\u0165 nadol\",\n  alignLeft: \"Zarovna\\u0165 do\\u013Eava\",\n  alignRight: \"Zarovna\\u0165 doprava\",\n  centerVertically: \"Zarovna\\u0165 zvislo na stred\",\n  centerHorizontally: \"Zarovna\\u0165 vodorovne na stred\",\n  distributeHorizontally: \"Rozmiestni\\u0165 vodorovne\",\n  distributeVertically: \"Rozmiestni\\u0165 zvisle\",\n  flipHorizontal: \"Prevr\\xE1ti\\u0165 vodorovne\",\n  flipVertical: \"Prevr\\xE1ti\\u0165 zvislo\",\n  viewMode: \"Re\\u017Eim zobrazenia\",\n  share: \"Zdie\\u013Ea\\u0165\",\n  showStroke: \"Zobrazi\\u0165 v\\xFDber farby pre obrys\",\n  showBackground: \"Zobrazi\\u0165 v\\xFDber farby pre pozadie\",\n  toggleTheme: \"Prepn\\xFA\\u0165 t\\xE9mu\",\n  personalLib: \"Moja kni\\u017Enica\",\n  excalidrawLib: \"Excalidraw kni\\u017Enica\",\n  decreaseFontSize: \"Zmen\\u0161i\\u0165 ve\\u013Ekos\\u0165 p\\xEDsma\",\n  increaseFontSize: \"Zv\\xE4\\u010D\\u0161i\\u0165 ve\\u013Ekos\\u0165 p\\xEDsma\",\n  unbindText: \"Zru\\u0161i\\u0165 previazanie textu\",\n  bindText: \"Previaza\\u0165 text s kontajnerom\",\n  createContainerFromText: \"Zabali\\u0165 text do kontajneru\",\n  link: {\n    edit: \"Upravi\\u0165 odkaz\",\n    editEmbed: \"Editova\\u0165 a zapusti\\u0165 odkaz\",\n    create: \"Vytvori\\u0165 odkaz\",\n    createEmbed: \"Vytvori\\u0165 a zapusti\\u0165 odkaz\",\n    label: \"Odkaz\",\n    labelEmbed: \"Zapusti\\u0165 odkaz\",\n    empty: \"Nie je nastaven\\xFD \\u017Eiaden odkaz\"\n  },\n  lineEditor: {\n    edit: \"Upravi\\u0165 \\u010Diaru\",\n    exit: \"Ukon\\u010Di\\u0165 editovanie \\u010Diary\"\n  },\n  elementLock: {\n    lock: \"Zamkn\\xFA\\u0165\",\n    unlock: \"Odomkn\\xFA\\u0165\",\n    lockAll: \"Zamkn\\xFA\\u0165 v\\u0161etko\",\n    unlockAll: \"Odomkn\\xFA\\u0165 v\\u0161etko\"\n  },\n  statusPublished: \"Zverejnen\\xE9\",\n  sidebarLock: \"Necha\\u0165 bo\\u010Dn\\xFD panel otvoren\\xFD\",\n  selectAllElementsInFrame: \"Vybra\\u0165 v\\u0161etky prvky v r\\xE1me\",\n  removeAllElementsFromFrame: \"Odstr\\xE1ni\\u0165 v\\u0161etky prvky z r\\xE1mu\",\n  eyeDropper: \"Vybra\\u0165 farbu z pl\\xE1tna\",\n  textToDiagram: \"Text na diagram\",\n  prompt: \"In\\u0161trukcia\"\n};\nvar library = {\n  noItems: \"Zatia\\u013E neboli pridan\\xE9 \\u017Eiadne polo\\u017Eky...\",\n  hint_emptyLibrary: \"Vyberte polo\\u017Eku z pl\\xE1tna pre jej pridanie do kni\\u017Enice alebo pou\\u017Eite kni\\u017Enicu z verejn\\xE9ho zoznamu kni\\u017En\\xEDc ni\\u017E\\u0161ie.\",\n  hint_emptyPrivateLibrary: \"Vyberte polo\\u017Eku z pl\\xE1tna pre jej pridanie do kni\\u017Enice.\"\n};\nvar buttons = {\n  clearReset: \"Obnovi\\u0165 pl\\xE1tno\",\n  exportJSON: \"Exportova\\u0165 do s\\xFAboru\",\n  exportImage: \"Exportova\\u0165 obr\\xE1zok...\",\n  export: \"Ulo\\u017Ei\\u0165 do...\",\n  copyToClipboard: \"Kop\\xEDrova\\u0165 do schr\\xE1nky\",\n  save: \"Ulo\\u017Ei\\u0165 do aktu\\xE1lneho s\\xFAboru\",\n  saveAs: \"Ulo\\u017Ei\\u0165 ako\",\n  load: \"Otvori\\u0165\",\n  getShareableLink: \"Z\\xEDska\\u0165 odkaz na zdie\\u013Eanie\",\n  close: \"Zavrie\\u0165\",\n  selectLanguage: \"Zvoli\\u0165 jazyk\",\n  scrollBackToContent: \"Vr\\xE1ti\\u0165 sa sp\\xE4\\u0165 na obsah\",\n  zoomIn: \"Pribl\\xED\\u017Ei\\u0165\",\n  zoomOut: \"Oddiali\\u0165\",\n  resetZoom: \"Obnovi\\u0165 pribl\\xED\\u017Eenie\",\n  menu: \"Ponuka\",\n  done: \"Hotovo\",\n  edit: \"Upravi\\u0165\",\n  undo: \"Sp\\xE4\\u0165\",\n  redo: \"Znova\",\n  resetLibrary: \"Obnovi\\u0165 kni\\u017Enicu\",\n  createNewRoom: \"Vytvori\\u0165 nov\\xFA miestnos\\u0165\",\n  fullScreen: \"Cel\\xE1 obrazovka\",\n  darkMode: \"Tmav\\xFD re\\u017Eim\",\n  lightMode: \"Svetl\\xFD re\\u017Eim\",\n  zenMode: \"Re\\u017Eim zen\",\n  objectsSnapMode: \"Prichyti\\u0165 k objektom\",\n  exitZenMode: \"Zru\\u0161i\\u0165 re\\u017Eim zen\",\n  cancel: \"Zru\\u0161i\\u0165\",\n  clear: \"Vymaza\\u0165\",\n  remove: \"Odstr\\xE1ni\\u0165\",\n  embed: \"Prepn\\xFA\\u0165 zapustenie\",\n  publishLibrary: \"Uverejni\\u0165\",\n  submit: \"Potvrdi\\u0165\",\n  confirm: \"Potvrdi\\u0165\",\n  embeddableInteractionButton: \"Kliknite pre interakciu\"\n};\nvar alerts = {\n  clearReset: \"T\\xFDmto sa vy\\u010Dist\\xED cel\\xE9 pl\\xE1tno. Ste si ist\\xED?\",\n  couldNotCreateShareableLink: \"Nepodarilo sa vytvori\\u0165 odkaz na zdie\\u013Eanie.\",\n  couldNotCreateShareableLinkTooBig: \"Nepodarilo sa vytvori\\u0165 odkaz na zdie\\u013Eanie: sc\\xE9na je pr\\xEDli\\u0161 ve\\u013Ek\\xE1\",\n  couldNotLoadInvalidFile: \"Nepodarilo sa na\\u010D\\xEDta\\u0165 nevalidn\\xFD s\\xFAbor\",\n  importBackendFailed: \"Nepdarilo sa importovanie zo serveru.\",\n  cannotExportEmptyCanvas: \"Nie je mo\\u017En\\xE9 exportova\\u0165 pr\\xE1zdne pl\\xE1tno.\",\n  couldNotCopyToClipboard: \"Kop\\xEDrovanie do schr\\xE1nky sa nepodarilo.\",\n  decryptFailed: \"Nepodarilo sa roz\\u0161ifrova\\u0165 \\xFAdaje.\",\n  uploadedSecurly: \"Nahratie je zabezpe\\u010Den\\xE9 end-to-end \\u0161ifrovan\\xEDm, tak\\u017Ee Excalidraw server a tretie strany nedok\\xE1\\u017Eu pre\\u010D\\xEDta\\u0165 jeho obsah.\",\n  loadSceneOverridePrompt: \"Nahratie externej kresby nahrad\\xED existuj\\xFAci obsah. Prajete si pokra\\u010Dova\\u0165?\",\n  collabStopOverridePrompt: \"Ukon\\u010Denie sch\\xF4dze nahrad\\xED va\\u0161u predch\\xE1dzaj\\xFAcu lok\\xE1lne ulo\\u017Een\\xFA sc\\xE9nu. Ste si ist\\xFD?\\n\\n(Ak si chcete ponecha\\u0165 lok\\xE1lnu sc\\xE9nu, jednoducho iba zavrite kartu prehliada\\u010Da.)\",\n  errorAddingToLibrary: \"Nepodarilo sa prida\\u0165 polo\\u017Eku do kni\\u017Enice\",\n  errorRemovingFromLibrary: \"Nepodarilo sa odstr\\xE1ni\\u0165 polo\\u017Eku z kni\\u017Enice\",\n  confirmAddLibrary: \"T\\xFDmto sa prid\\xE1 {{numShapes}} tvar(ov) do va\\u0161ej kni\\u017Enice. Ste si ist\\xED?\",\n  imageDoesNotContainScene: \"Tento obr\\xE1zok neobsahuje \\u017Eiadne \\xFAdaje sc\\xE9ny. Zvolili ste mo\\u017Enos\\u0165 zahrn\\xFA\\u0165 sc\\xE9nu po\\u010Das exportu?\",\n  cannotRestoreFromImage: \"Nepodarilo sa obnovi\\u0165 sc\\xE9nu z tohto obr\\xE1zkov\\xE9ho s\\xFAboru\",\n  invalidSceneUrl: \"Nepodarilo sa na\\u010D\\xEDta\\u0165 sc\\xE9nu z poskytnutej URL. Je nevalidn\\xE1 alebo neobsahuje \\u017Eiadne validn\\xE9 Excalidraw JSON d\\xE1ta.\",\n  resetLibrary: \"T\\xFDmto vypr\\xE1zdnite va\\u0161u kni\\u017Enicu. Ste si ist\\xFD?\",\n  removeItemsFromsLibrary: \"Odstr\\xE1ni\\u0165 {{count}} polo\\u017Eiek z kni\\u017Enice?\",\n  invalidEncryptionKey: \"\\u0160ifrovac\\xED k\\u013E\\xFA\\u010D mus\\xED ma\\u0165 22 znakov. \\u017Div\\xE1 spolupr\\xE1ca je vypnut\\xE1.\",\n  collabOfflineWarning: \"Internetov\\xE9 pripojenie nie je dostupn\\xE9.\\nVa\\u0161e zmeny nebud\\xFA ulo\\u017Een\\xE9!\"\n};\nvar errors = {\n  unsupportedFileType: \"Nepodporovan\\xFD typ s\\xFAboru.\",\n  imageInsertError: \"Nepodarilo sa vlo\\u017Ei\\u0165 obr\\xE1zok. Sk\\xFAste to znova nesk\\xF4r...\",\n  fileTooBig: \"S\\xFAbor je pr\\xEDli\\u0161 ve\\u013Ek\\xFD. Maxim\\xE1lna povolen\\xE1 ve\\u013Ekos\\u0165 je {{maxSize}}.\",\n  svgImageInsertError: \"Nepodarilo sa vlo\\u017Ei\\u0165 SVG obr\\xE1zok. SVG form\\xE1t je pravdepodobne nevalidn\\xFD.\",\n  failedToFetchImage: \"Na\\u010D\\xEDtanie obr\\xE1zka zlyhalo.\",\n  invalidSVGString: \"Nevalidn\\xE9 SVG.\",\n  cannotResolveCollabServer: \"Nepodarilo sa pripoji\\u0165 ku kolabora\\u010Dn\\xE9mu serveru. Pros\\xEDm obnovte str\\xE1nku a sk\\xFAste to znovu.\",\n  importLibraryError: \"Nepodarilo sa na\\u010D\\xEDta\\u0165 kni\\u017Enicu\",\n  collabSaveFailed: \"Ulo\\u017Eenie do datab\\xE1zy sa nepodarilo. Ak tento probl\\xE9m pretrv\\xE1va ulo\\u017Ete si v\\xE1\\u0161 s\\xFAbor lok\\xE1lne aby ste nestratili va\\u0161u pr\\xE1cu.\",\n  collabSaveFailed_sizeExceeded: \"Ulo\\u017Eenie do datab\\xE1zy sa nepodarilo, preto\\u017Ee ve\\u013Ekos\\u0165 pl\\xE1tna je pr\\xEDli\\u0161 ve\\u013Ek\\xE1. Ulo\\u017Ete si v\\xE1\\u0161 s\\xFAbor lok\\xE1lne aby ste nestratili va\\u0161u pr\\xE1cu.\",\n  imageToolNotSupported: \"Obr\\xE1zky s\\xFA vypnut\\xE9.\",\n  brave_measure_text_error: {\n    line1: \"Vyzer\\xE1 to, \\u017Ee pou\\u017E\\xEDvate prehliada\\u010D Brave so zapnut\\xFDm nastaven\\xEDm pre <bold>agres\\xEDvne blokovanie</bold>.\",\n    line2: \"To m\\xF4\\u017Ee sp\\xF4sobi\\u0165 nespr\\xE1vne zobrazenie <bold>textov\\xFDch prvkov</bold> vo va\\u0161ej kresbe.\",\n    line3: \"D\\xF4razne odpor\\xFA\\u010Dame vypnutie toho nastavenia. M\\xF4\\u017Eete tak spravi\\u0165 vykonan\\xEDm <link>t\\xFDchto krokov</link>.\",\n    line4: \"Ak vypnutie toho nastavenia nevyrie\\u0161i probl\\xE9m so zobrazen\\xEDm textov\\xFDch prvkov, pros\\xEDm ohl\\xE1ste <issueLink>probl\\xE9m</issueLink> na na\\u0161om GitHub-e alebo n\\xE1m nap\\xED\\u0161te na n\\xE1\\u0161 <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Zapusten\\xE9 prvky nie je mo\\u017En\\xE9 prida\\u0165 do kni\\u017Enice.\",\n    iframe: \"Vlo\\u017Een\\xE9 r\\xE1mce IFrame nie je mo\\u017En\\xE9 prida\\u0165 do kni\\u017Enice.\",\n    image: \"Podpora pre prid\\xE1vanie obr\\xE1zkov do kni\\u017Enice bude dostupn\\xE1 u\\u017E \\u010Doskoro!\"\n  },\n  asyncPasteFailedOnRead: \"Vlo\\u017Eenie sa nepodarilo (nebolo mo\\u017En\\xE9 pre\\u010D\\xEDta\\u0165 obsah schr\\xE1nky).\",\n  asyncPasteFailedOnParse: \"Vlo\\u017Eenie sa nepodarilo.\",\n  copyToSystemClipboardFailed: \"Kop\\xEDrovanie do schr\\xE1nky sa nepodarilo.\"\n};\nvar toolBar = {\n  selection: \"V\\xFDber\",\n  image: \"Vlo\\u017Ei\\u0165 obr\\xE1zok\",\n  rectangle: \"Obd\\u013A\\u017Enik\",\n  diamond: \"Diamant\",\n  ellipse: \"Elipsa\",\n  arrow: \"\\u0160\\xEDpka\",\n  line: \"\\u010Ciara\",\n  freedraw: \"Kresli\\u0165\",\n  text: \"Text\",\n  library: \"Kni\\u017Enica\",\n  lock: \"Necha\\u0165 zvolen\\xFD n\\xE1stroj akt\\xEDvny po skon\\u010Den\\xED kreslenia\",\n  penMode: \"Re\\u017Eim pera \\u2013 zabr\\xE1ni\\u0165 dotyku\",\n  link: \"Prida\\u0165/ Upravi\\u0165 odkaz pre vybran\\xFD tvar\",\n  eraser: \"Guma\",\n  frame: \"N\\xE1stroj r\\xE1m\",\n  magicframe: \"Dr\\xF4ten\\xFD model na k\\xF3d\",\n  embeddable: \"Web Embed\",\n  laser: \"Laserov\\xFD ukazovate\\u013E\",\n  hand: \"Ruka (n\\xE1stroj pre pohyb pl\\xE1tna)\",\n  extraTools: \"\\u010Eal\\u0161ie n\\xE1stroje\",\n  mermaidToExcalidraw: \"Mermaid do Excalidraw\",\n  magicSettings: \"AI nastavenia\"\n};\nvar headings = {\n  canvasActions: \"Akcie pl\\xE1tna\",\n  selectedShapeActions: \"Akcie tvarov z v\\xFDberu\",\n  shapes: \"Tvary\"\n};\nvar hints = {\n  canvasPanning: \"Pre pohyb pl\\xE1tna podr\\u017Ete koliesko my\\u0161i alebo medzern\\xEDk po\\u010Das \\u0165ahania, alebo pou\\u017Eite n\\xE1stroj ruka\",\n  linearElement: \"Kliknite na vlo\\u017Eenie viacer\\xFDch bodov, potiahnite na vytvorenie jednej priamky\",\n  freeDraw: \"Kliknite a \\u0165ahajte, pustite na ukon\\u010Denie\",\n  text: \"Tip: text m\\xF4\\u017Eete prida\\u0165 aj dvojklikom kdeko\\u013Evek, ak je zvolen\\xFD n\\xE1stroj v\\xFDber\",\n  embeddable: \"Kliknite a \\u0165ahajte pre zapustenie webovej str\\xE1nky\",\n  text_selected: \"Pou\\u017Eite dvojklik alebo stla\\u010Dte Enter na edit\\xE1ciu textu\",\n  text_editing: \"Stla\\u010Dte Escape alebo CtrlOrCmd+ENTER na ukon\\u010Denie editovania\",\n  linearElementMulti: \"Kliknite na po\\u010Diato\\u010Dn\\xFD bod alebo stla\\u010Dte Escape alebo Enter na ukon\\u010Denie\",\n  lockAngle: \"Po\\u010Das rot\\xE1cie obmedz\\xEDte uhol podr\\u017Ean\\xEDm SHIFT\",\n  resize: \"Po\\u010Das zmeny ve\\u013Ekosti zachov\\xE1te proporcie podr\\u017Ean\\xEDm SHIFT,\\\\npodr\\u017Ean\\xEDm ALT men\\xEDte ve\\u013Ekos\\u0165 so zachovan\\xEDm stredu\",\n  resizeImage: \"Podr\\u017Ete SHIFT pre vo\\u013En\\xFA zmenu ve\\u013Ekosti, podr\\u017Ete ALT pre zmenu ve\\u013Ekosti od stredu\",\n  rotate: \"Po\\u010Das rot\\xE1cie obmedz\\xEDte uhol podr\\u017Ean\\xEDm SHIFT\",\n  lineEditor_info: \"Podr\\u017Ete CtrlOrCmd a kliknite dva kr\\xE1t alebo stla\\u010Dte CtrlOrCmd + Enter pre edit\\xE1ciu bodov\",\n  lineEditor_pointSelected: \"Sta\\u010Dte Delete na vymazanie bodu (bodov), CtrlOrCmd+D na duplikovanie, alebo potiahnite na presunutie\",\n  lineEditor_nothingSelected: \"Zvo\\u013Ete bod na upravovanie (podr\\u017Ete SHIFT pre zvolenie viacer\\xFDch bodov) alebo podr\\u017Ete Alt a kliknite na pridanie nov\\xE9ho bodu\",\n  placeImage: \"Kliknite pre umiestnenie obr\\xE1zka alebo kliknite a \\u0165ahajte pre zmenu jeho ve\\u013Ekosti\",\n  publishLibrary: \"Uverejni\\u0165 va\\u0161u kni\\u017Enicu\",\n  bindTextToElement: \"Stla\\u010Dte enter na pridanie textu\",\n  deepBoxSelect: \"Podr\\u017Ete CtrlOrCmd na v\\xFDber v skupine alebo zamedzeniu po\\u0165iahnutia\",\n  eraserRevert: \"Podr\\u017Ete Alt pre prehodenie polo\\u017Eiek ur\\u010Den\\xFDch na vymazanie\",\n  firefox_clipboard_write: 'T\\xE1to sa funkcionalita sa d\\xE1 zapn\\xFA\\u0165 nastaven\\xEDm \"dom.events.asyncClipboard.clipboardItem\" na \"true\". Pre zmenu nastaven\\xED vo Firefox-e otvorte str\\xE1nku \"about:config\".',\n  disableSnapping: \"Podr\\u017Ete CtrlOrCmd pre vypnutie prichyt\\xE1vania\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Nie je mo\\u017En\\xE9 zobrazi\\u0165 n\\xE1h\\u013Ead pl\\xE1tna\",\n  canvasTooBig: \"Pl\\xE1tno je mo\\u017Eno pr\\xEDli\\u0161 ve\\u013Ek\\xE9.\",\n  canvasTooBigTip: \"Tip: sk\\xFAste presun\\xFA\\u0165 najvzdialenej\\u0161ie prvky bli\\u017E\\u0161ie k sebe.\"\n};\nvar errorSplash = {\n  headingMain: \"Nastala chyba. Vysk\\xFA\\u0161ajte <button>obnovi\\u0165 str\\xE1nku.</button>\",\n  clearCanvasMessage: \"Ak obnovenie str\\xE1nky nepom\\xE1ha, vysk\\xFA\\u0161ajte <button>vy\\u010Disti\\u0165 pl\\xE1tno.</button>\",\n  clearCanvasCaveat: \" To bude ma\\u0165 za n\\xE1sledok stratu pr\\xE1ce \",\n  trackedToSentry: \"Chyba s identifik\\xE1torom {{eventId}} bola zaznamenan\\xE1 v na\\u0161om syst\\xE9me.\",\n  openIssueMessage: \"Boli sme ve\\u013Emi opatrn\\xED, aby inform\\xE1cie va\\u0161ej sc\\xE9ny neboli v chybe zaznamenan\\xE9. Ak va\\u0161a sc\\xE9na nie je s\\xFAkromn\\xE1, pros\\xEDm zv\\xE1\\u017Ete pokra\\u010Dovanie na na\\u0161e <button>hl\\xE1senie ch\\xFDb.</button> Pros\\xEDm zahr\\u0148te inform\\xE1cie ni\\u017E\\u0161ie pomocou kop\\xEDrovania a prilepenia do GitHub issue.\",\n  sceneContent: \"Obsah sc\\xE9ny:\"\n};\nvar roomDialog = {\n  desc_intro: \"Pozvite niekoho do svojej aktu\\xE1lnej sc\\xE9ny a pracujte spolo\\u010Dne.\",\n  desc_privacy: \"Nemajte obavy, sch\\xF4dza pou\\u017E\\xEDva end-to-end \\u0161ifrovanie, tak\\u017Ee v\\u0161etko \\u010Do nakresl\\xEDte je s\\xFAkromn\\xE9. Dokonca, ani n\\xE1\\u0161 server dedok\\xE1\\u017Ee pre\\u010D\\xEDta\\u0165, \\u010Do ste vytvorili.\",\n  button_startSession: \"Za\\u010Da\\u0165 sch\\xF4dzu\",\n  button_stopSession: \"Ukon\\u010Di\\u0165 sch\\xF4dzu\",\n  desc_inProgressIntro: \"Pr\\xE1ve prebieha \\u017Eiv\\xE1 sch\\xF4dza.\",\n  desc_shareLink: \"Zdie\\u013Eajte tento odkaz s osobou, s ktorou chcete spolupracova\\u0165:\",\n  desc_exitSession: \"Ukon\\u010Denie sch\\xF4dze v\\xE1s odpoj\\xED z miestnosti, av\\u0161ak na\\u010Falej budete m\\xF4c\\u0165 pokra\\u010Dova\\u0165 v pr\\xE1ci na sc\\xE9ne lok\\xE1lne. Toto neovplyvn\\xED ostatn\\xFDch spolupracovn\\xEDkov a st\\xE1le bud\\xFA m\\xF4c\\u0165 spolupracova\\u0165 na ich verzii.\",\n  shareTitle: \"Pripoji\\u0165 sa k \\u017Eivej sch\\xF4dzi na Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Chyba\"\n};\nvar exportDialog = {\n  disk_title: \"Ulo\\u017Ei\\u0165 na disk\",\n  disk_details: \"Exportova\\u0165 \\xFAdaje sc\\xE9ny do s\\xFAboru, z ktor\\xE9ho m\\xF4\\u017Eu by\\u0165 nesk\\xF4r importovan\\xE9.\",\n  disk_button: \"Ulo\\u017Ei\\u0165 do s\\xFAboru\",\n  link_title: \"Odkaz na zdie\\u013Eanie\",\n  link_details: \"Exportova\\u0165 ako odkaz iba na \\u010D\\xEDtanie.\",\n  link_button: \"Exportova\\u0165 ako odkaz\",\n  excalidrawplus_description: \"Ulo\\u017Ei\\u0165 sc\\xE9nu do v\\xE1\\u0161ho Excalidraw+ pracovn\\xE9ho priestoru.\",\n  excalidrawplus_button: \"Exportova\\u0165\",\n  excalidrawplus_exportError: \"Nepodarilo sa vykona\\u0165 export do Excalidraw+...\"\n};\nvar helpDialog = {\n  blog: \"Pre\\u010D\\xEDtajte si n\\xE1\\u0161 blog\",\n  click: \"kliknutie\",\n  deepSelect: \"V\\xFDber v skupine\",\n  deepBoxSelect: \"V\\xFDber v skupine alebo zamedzenie po\\u0165iahnutia\",\n  curvedArrow: \"Zakriven\\xE1 \\u0161\\xEDpka\",\n  curvedLine: \"Zakriven\\xE1 \\u010Diara\",\n  documentation: \"Dokument\\xE1cia\",\n  doubleClick: \"dvojklik\",\n  drag: \"potiahnutie\",\n  editor: \"Editovanie\",\n  editLineArrowPoints: \"Edit\\xE1cia bodov \\u010Diary/\\u0161\\xEDpky\",\n  editText: \"Edit\\xE1cia textu / pridanie \\u0161t\\xEDtku\",\n  github: \"Objavili ste probl\\xE9m? Nahl\\xE1ste ho\",\n  howto: \"Postupujte pod\\u013Ea na\\u0161\\xEDch n\\xE1vodov\",\n  or: \"alebo\",\n  preventBinding: \"Zak\\xE1za\\u0165 prip\\xE1janie \\u0161\\xEDpky\",\n  tools: \"N\\xE1stroje\",\n  shortcuts: \"Kl\\xE1vesov\\xE9 skratky\",\n  textFinish: \"Ukon\\u010Denie editovania (text editor)\",\n  textNewLine: \"Vlo\\u017Ei\\u0165 nov\\xFD riadok (text editor)\",\n  title: \"Pomocn\\xEDk\",\n  view: \"Zobrazenie\",\n  zoomToFit: \"Pribl\\xED\\u017Ei\\u0165 aby boli zahrnut\\xE9 v\\u0161etky prvky\",\n  zoomToSelection: \"Pribl\\xED\\u017Ei\\u0165 na v\\xFDber\",\n  toggleElementLock: \"Zamkn\\xFA\\u0165/odomkn\\xFA\\u0165 vybran\\xE9\",\n  movePageUpDown: \"Posun\\xFA\\u0165 stranu hore/dole\",\n  movePageLeftRight: \"Posun\\xFA\\u0165 stranu do\\u013Eava/doprava\"\n};\nvar clearCanvasDialog = {\n  title: \"Vy\\u010Disti\\u0165 pl\\xE1tno\"\n};\nvar publishDialog = {\n  title: \"Uverejni\\u0165 kni\\u017Enicu\",\n  itemName: \"N\\xE1zov polo\\u017Eky\",\n  authorName: \"Meno autora\",\n  githubUsername: \"Github u\\u017E\\xEDvate\\u013Esk\\xE9 meno\",\n  twitterUsername: \"Twitter u\\u017E\\xEDvate\\u013Esk\\xE9 meno\",\n  libraryName: \"N\\xE1zov kni\\u017Enice\",\n  libraryDesc: \"Popis kni\\u017Enice\",\n  website: \"Webov\\xE1 str\\xE1nka\",\n  placeholder: {\n    authorName: \"Va\\u0161e meno alebo u\\u017E\\xEDvate\\u013Esk\\xE9 meno\",\n    libraryName: \"N\\xE1zov va\\u0161ej kni\\u017Enice\",\n    libraryDesc: \"Popis va\\u0161ej kni\\u017Enice, ktor\\xFD ostatn\\xFDm pom\\xF4\\u017Ee porozumie\\u0165 jej vhodn\\xE9mu pou\\u017Eitiu\",\n    githubHandle: \"GitHub u\\u017E\\xEDvate\\u013Esk\\xE9 meno (nepovinn\\xE9), aby ste mohli robi\\u0165 \\xFApravy po tom, \\u010Do bude kni\\u017Enica uverejnen\\xE1 na schv\\xE1lenie\",\n    twitterHandle: \"Twitter u\\u017E\\xEDvate\\u013Esk\\xE9 meno (nepovinn\\xE9), aby sme vedeli komu prip\\xEDsa\\u0165 z\\xE1sluhu pri propagovan\\xED cez Twitter\",\n    website: \"Odkaz na va\\u0161u osobn\\xFA webov\\xFA str\\xE1nku alebo niekam inam (nepovinn\\xE9)\"\n  },\n  errors: {\n    required: \"Povinn\\xE9\",\n    website: \"Zadajte platn\\xFA adresu URL\"\n  },\n  noteDescription: \"Uverejnite va\\u0161u kni\\u017Enicu vo <link>verejnom zozname kni\\u017En\\xEDc</link>aby ju aj ostatn\\xED mohli pou\\u017Ei\\u0165 v ich n\\xE1\\u010Drtoch.\",\n  noteGuidelines: \"Kni\\u017Enica mus\\xED by\\u0165 najprv manu\\xE1lne schv\\xE1len\\xE1. Pros\\xEDm pre\\u010D\\xEDtajte si <link>pokyny</link> pred uverejnen\\xEDm. Budete potrebova\\u0165 Github \\xFA\\u010Det na komunik\\xE1ciu a vykonanie zmien, ak bud\\xFA potrebn\\xE9, av\\u0161ak nie je to \\xFAplne povinn\\xE9.\",\n  noteLicense: \"Potvrden\\xEDm s\\xFAhlas\\xEDte, \\u017Ee kni\\u017Enica bude zverejnen\\xE1 s <link>MIT licenciou, </link>\\u010Do v skratke znamen\\xE1, \\u017Ee ju m\\xF4\\u017Ee pou\\u017Ei\\u0165 hocikto bez obmedzen\\xED.\",\n  noteItems: \"Ka\\u017Ed\\xE1 polo\\u017Eka v kni\\u017Enici mus\\xED ma\\u0165 svoje vlastn\\xE9 meno, aby sa dala vyh\\u013Eada\\u0165. S\\xFA\\u010Das\\u0165ou kni\\u017Enice bud\\xFA nasleduj\\xFAce polo\\u017Eky:\",\n  atleastOneLibItem: \"Za\\u010Dnite pros\\xEDm zvolen\\xEDm aspo\\u0148 jednej polo\\u017Eky z kni\\u017Enice\",\n  republishWarning: \"Pozn\\xE1mka: Niektor\\xE9 z vybran\\xFDch polo\\u017Eiek s\\xFA u\\u017E ozna\\u010Den\\xE9 ako zverejnen\\xE9. Ich znovu uverejnenie by ste mali vykova\\u0165 iba vtedy ak aktualizujete u\\u017E existuj\\xFAcu kni\\u017Enicu alebo po\\u017Eiadavku na uverejnenie.\"\n};\nvar publishSuccessDialog = {\n  title: \"Kni\\u017Enica uverejnen\\xE1\",\n  content: \"\\u010Eakujeme v\\xE1m {{authorName}}. Va\\u0161a kni\\u017Enica bola uverejnen\\xE1 na pos\\xFAdenie. Stav m\\xF4\\u017Eete skontrolova\\u0165<link>tu</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Obnovi\\u0165 kni\\u017Enicu\",\n  removeItemsFromLib: \"Odstr\\xE1ni\\u0165 zvolen\\xE9 polo\\u017Eky z kni\\u017Enice\"\n};\nvar imageExportDialog = {\n  header: \"Exportova\\u0165 obr\\xE1zok\",\n  label: {\n    withBackground: \"Pozadie\",\n    onlySelected: \"Iba vybran\\xE9\",\n    darkMode: \"Tmav\\xFD re\\u017Eim\",\n    embedScene: \"Zahrn\\xFA\\u0165 sc\\xE9nu\",\n    scale: \"Mierka\",\n    padding: \"Odsadenie\"\n  },\n  tooltip: {\n    embedScene: \"\\xDAdaje sc\\xE9ny bud\\xFA ulo\\u017Een\\xE9 do exportovan\\xE9ho PNG/SVG s\\xFAboru, tak\\u017Ee sc\\xE9na z neho m\\xF4\\u017Ee by\\u0165 op\\xE4\\u0165 obnoven\\xE1.\\nBude to ma\\u0165 za n\\xE1sledok zv\\xFD\\u0161enie ve\\u013Ekosti s\\xFAboru.\"\n  },\n  title: {\n    exportToPng: \"Exportova\\u0165 do PNG\",\n    exportToSvg: \"Exportova\\u0165 do SVG\",\n    copyPngToClipboard: \"Kop\\xEDrova\\u0165 PNG do schr\\xE1nky\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Kop\\xEDrova\\u0165 do schr\\xE1nky\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Va\\u0161e kresby pou\\u017E\\xEDvaj\\xFA end-to-end \\u0161ifrovanie, tak\\u017Ee ich Excalidraw server nedok\\xE1\\u017Ee pre\\u010D\\xEDta\\u0165.\",\n  link: \"Blog o end-to-end \\u0161ifrovan\\xED v Excalidraw\"\n};\nvar stats = {\n  angle: \"Uhol\",\n  element: \"Prvok\",\n  elements: \"Prvky\",\n  height: \"V\\xFD\\u0161ka\",\n  scene: \"Sc\\xE9na\",\n  selected: \"Vybran\\xE9\",\n  storage: \"\\xDAlo\\u017Eisko\",\n  title: \"\\u0160tatistiky\",\n  total: \"Celkom\",\n  version: \"Verzia\",\n  versionCopy: \"Kliknut\\xEDm skop\\xEDrujete\",\n  versionNotAvailable: \"Verzia nie je k dispoz\\xEDcii\",\n  width: \"\\u0160\\xEDrka\"\n};\nvar toast = {\n  addedToLibrary: \"Pridan\\xE9 do kni\\u017Enice\",\n  copyStyles: \"\\u0160t\\xFDly skop\\xEDrovan\\xE9.\",\n  copyToClipboard: \"Skop\\xEDrovan\\xE9 do schr\\xE1nky.\",\n  copyToClipboardAsPng: \"Kop\\xEDrovanie {{exportSelection}} do schr\\xE1nky ako PNG prebehlo \\xFAspe\\u0161ne\\n({{exportColorScheme}})\",\n  fileSaved: \"S\\xFAbor ulo\\u017Een\\xFD.\",\n  fileSavedToFilename: \"Ulo\\u017Een\\xFD ako {filename}\",\n  canvas: \"pl\\xE1tna\",\n  selection: \"v\\xFDberu\",\n  pasteAsSingleElement: \"Pou\\u017Eit\\xEDm {{shortcut}} vlo\\u017Ete ako samostatn\\xFD prvok alebo vlo\\u017Ete do existuj\\xFAceho editovan\\xE9ho textu\",\n  unableToEmbed: \"Zapustenie tejto URL nie je povolen\\xE9. Vytvorte issue na GitHub-e a po\\u017Eiadajte povolenie tejto URL\",\n  unrecognizedLinkFormat: \"Odkaz, ktor\\xFD sa sna\\u017E\\xEDte zapusti\\u0165 nie je v o\\u010Dak\\xE1vanom form\\xE1te. Pros\\xEDm sk\\xFAste vlo\\u017Ei\\u0165 'odkaz na zdie\\u013Eanie' poskytnut\\xFD zdrojovou webovou str\\xE1nkou\"\n};\nvar colors = {\n  transparent: \"Prieh\\u013Eadn\\xE1\",\n  black: \"\\u010Cierna\",\n  white: \"Biela\",\n  red: \"\\u010Cerven\\xE1\",\n  pink: \"Ru\\u017Eov\\xE1\",\n  grape: \"Hroznov\\xE1 fialov\\xE1\",\n  violet: \"Fialov\\xE1\",\n  gray: \"Siv\\xE1\",\n  blue: \"Modr\\xE1\",\n  cyan: \"Az\\xFArov\\xE1\",\n  teal: \"Modrozelen\\xE1\",\n  green: \"Zelen\\xE1\",\n  yellow: \"\\u017Dlt\\xE1\",\n  orange: \"Oran\\u017Eov\\xE1\",\n  bronze: \"Bronzov\\xE1\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"V\\u0161etky va\\u0161e d\\xE1ta s\\xFA ulo\\u017Een\\xE9 lok\\xE1lne vo va\\u0161om prehliada\\u010Di.\",\n    center_heading_plus: \"Chceli ste namiesto toho prejs\\u0165 do Excalidraw+?\",\n    menuHint: \"Exportovanie, nastavenia, jazyky, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportovanie, nastavenia a \\u010Fal\\u0161ie...\",\n    center_heading: \"Diagramy. Jednoducho.\",\n    toolbarHint: \"Zvo\\u013Ete n\\xE1stroj a za\\u010Dnite kresli\\u0165!\",\n    helpHint: \"Kl\\xE1vesov\\xE9 skratky a pomocn\\xEDk\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Najpou\\u017E\\xEDvanej\\u0161ie vlastn\\xE9 farby\",\n  colors: \"Farby\",\n  shades: \"Odtiene\",\n  hexCode: \"Hex k\\xF3d\",\n  noShades: \"Pre t\\xFAto farbu nie s\\xFA dostupn\\xE9 \\u017Eiadne odtiene\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportova\\u0165 ako obr\\xE1zok\",\n      button: \"Exportova\\u0165 ako obr\\xE1zok\",\n      description: \"Exportova\\u0165 \\xFAdaje sc\\xE9ny ako obr\\xE1zok, z ktor\\xE9ho m\\xF4\\u017Eu by\\u0165 nesk\\xF4r importovan\\xE9.\"\n    },\n    saveToDisk: {\n      title: \"Ulo\\u017Ei\\u0165 na disk\",\n      button: \"Ulo\\u017Ei\\u0165 na disk\",\n      description: \"Exportova\\u0165 \\xFAdaje sc\\xE9ny do s\\xFAboru, z ktor\\xE9ho m\\xF4\\u017Eu by\\u0165 nesk\\xF4r importovan\\xE9.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exportova\\u0165 ako Excalidraw+\",\n      description: \"Ulo\\u017Ei\\u0165 sc\\xE9nu do v\\xE1\\u0161ho Excalidraw+ pracovn\\xE9ho priestoru.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Na\\u010D\\xEDta\\u0165 zo s\\xFAboru\",\n      button: \"Na\\u010D\\xEDta\\u0165 zo s\\xFAboru\",\n      description: \"Na\\u010D\\xEDtanie zo s\\xFAboru <bold>nahrad\\xED v\\xE1\\u0161 existuj\\xFAci obsah</bold>.<br></br>Va\\u0161u kresbu m\\xF4\\u017Eete z\\xE1lohova\\u0165 jednou z ni\\u017E\\u0161ie uveden\\xFDch mo\\u017Enost\\xED.\"\n    },\n    shareableLink: {\n      title: \"Na\\u010D\\xEDta\\u0165 z odkazu\",\n      button: \"Nahradi\\u0165 m\\xF4j obsah\",\n      description: \"Na\\u010D\\xEDtanie externej kresby <bold>nahrad\\xED v\\xE1\\u0161 existuj\\xFAci obsah</bold>.<br></br>Va\\u0161u kresbu m\\xF4\\u017Eete z\\xE1lohova\\u0165 jednou z ni\\u017E\\u0161ie uveden\\xFDch mo\\u017Enost\\xED.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid do Excalidraw\",\n  button: \"Vlo\\u017Ei\\u0165\",\n  description: \"Aktu\\xE1lne s\\xFA podporovan\\xE9 iba <flowchartLink>v\\xFDvojov\\xE9 diagramy</flowchartLink>, <sequenceLink>sekven\\u010Dn\\xE9 diagramy</sequenceLink> a <classLink>diagramy tried</classLink>. Ostatn\\xE9 typy bud\\xFA v Excalidraw vykreslen\\xE9 ako obr\\xE1zky.\",\n  syntax: \"Mermaid syntax\",\n  preview: \"Uk\\xE1\\u017Eka\"\n};\nvar sk_SK_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=sk-SK-DY6IPO5U.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/sk-SK-DY6IPO5U.js\n"));

/***/ })

}]);