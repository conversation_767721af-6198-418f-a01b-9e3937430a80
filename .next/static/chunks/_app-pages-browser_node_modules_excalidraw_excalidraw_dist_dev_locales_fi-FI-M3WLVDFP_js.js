"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_fi-FI-M3WLVDFP_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/fi-FI-M3WLVDFP.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/fi-FI-M3WLVDFP.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ fi_FI_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/fi-FI.json\nvar labels = {\n  paste: \"Liit\\xE4\",\n  pasteAsPlaintext: \"Liit\\xE4 pelkk\\xE4n\\xE4 tekstin\\xE4\",\n  pasteCharts: \"Liit\\xE4 kaaviot\",\n  selectAll: \"Valitse kaikki\",\n  multiSelect: \"Lis\\xE4\\xE4 kohde valintaan\",\n  moveCanvas: \"Siirr\\xE4 piirtoaluetta\",\n  cut: \"Leikkaa\",\n  copy: \"Kopioi\",\n  copyAsPng: \"Kopioi leikep\\xF6yd\\xE4lle PNG-tiedostona\",\n  copyAsSvg: \"Kopioi leikep\\xF6yd\\xE4lle SVG-tiedostona\",\n  copyText: \"Kopioi tekstin\\xE4\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Tuo eteenp\\xE4in\",\n  sendToBack: \"Vie taakse\",\n  bringToFront: \"Tuo eteen\",\n  sendBackward: \"Vie taaksep\\xE4in\",\n  delete: \"Poista\",\n  copyStyles: \"Kopioi tyyli\",\n  pasteStyles: \"Liit\\xE4 tyyli\",\n  stroke: \"Piirto\",\n  background: \"Tausta\",\n  fill: \"T\\xE4ytt\\xF6\",\n  strokeWidth: \"Viivan leveys\",\n  strokeStyle: \"Viivan tyyli\",\n  strokeStyle_solid: \"Yhten\\xE4inen\",\n  strokeStyle_dashed: \"Katkoviiva\",\n  strokeStyle_dotted: \"Pisteviiva\",\n  sloppiness: \"Viivan tarkkuus\",\n  opacity: \"Peitt\\xE4vyys\",\n  textAlign: \"Tekstin tasaus\",\n  edges: \"Reunat\",\n  sharp: \"Ter\\xE4v\\xE4\",\n  round: \"Py\\xF6ristetty\",\n  arrowheads: \"Nuolenk\\xE4rjet\",\n  arrowhead_none: \"Ei mit\\xE4\\xE4n\",\n  arrowhead_arrow: \"Nuoli\",\n  arrowhead_bar: \"Tasap\\xE4\\xE4\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Kolmio\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Kirjasinkoko\",\n  fontFamily: \"Kirjasintyyppi\",\n  addWatermark: 'Lis\\xE4\\xE4 \"Tehty Excalidrawilla\"',\n  handDrawn: \"K\\xE4sinpiirretty\",\n  normal: \"Tavallinen\",\n  code: \"Koodi\",\n  small: \"Pieni\",\n  medium: \"Keskikoko\",\n  large: \"Suuri\",\n  veryLarge: \"Eritt\\xE4in suuri\",\n  solid: \"Yhten\\xE4inen\",\n  hachure: \"Vinoviivoitus\",\n  zigzag: \"\",\n  crossHatch: \"Ristiviivoitus\",\n  thin: \"Ohut\",\n  bold: \"Lihavoitu\",\n  left: \"Vasen\",\n  center: \"Keskit\\xE4\",\n  right: \"Oikea\",\n  extraBold: \"Eritt\\xE4in lihavoitu\",\n  architect: \"Arkkitehti\",\n  artist: \"Taiteilija\",\n  cartoonist: \"Sarjakuva\",\n  fileTitle: \"Tiedostonimi\",\n  colorPicker: \"V\\xE4rin valinta\",\n  canvasColors: \"K\\xE4yt\\xF6ss\\xE4 piirtoalueella\",\n  canvasBackground: \"Piirtoalueen tausta\",\n  drawingCanvas: \"Piirtoalue\",\n  layers: \"Tasot\",\n  actions: \"Toiminnot\",\n  language: \"Kieli\",\n  liveCollaboration: \"Live Yhteisty\\xF6...\",\n  duplicateSelection: \"Monista\",\n  untitled: \"Nimet\\xF6n\",\n  name: \"Nimi\",\n  yourName: \"Nimesi\",\n  madeWithExcalidraw: \"Tehty Excalidrawilla\",\n  group: \"Ryhmit\\xE4 valinta\",\n  ungroup: \"Pura valittu ryhm\\xE4\",\n  collaborators: \"Yhteisty\\xF6kumppanit\",\n  showGrid: \"N\\xE4yt\\xE4 ruudukko\",\n  addToLibrary: \"Lis\\xE4\\xE4 kirjastoon\",\n  removeFromLibrary: \"Poista kirjastosta\",\n  libraryLoadingMessage: \"Ladataan kirjastoa\\u2026\",\n  libraries: \"Selaa kirjastoja\",\n  loadingScene: \"Ladataan ty\\xF6t\\xE4\\u2026\",\n  align: \"Tasaa\",\n  alignTop: \"Tasaa yl\\xF6s\",\n  alignBottom: \"Tasaa alas\",\n  alignLeft: \"Tasaa vasemmalle\",\n  alignRight: \"Tasaa oikealle\",\n  centerVertically: \"Keskit\\xE4 pystysuunnassa\",\n  centerHorizontally: \"Keskit\\xE4 vaakasuunnassa\",\n  distributeHorizontally: \"Jaa vaakasuunnassa\",\n  distributeVertically: \"Jaa pystysuunnassa\",\n  flipHorizontal: \"K\\xE4\\xE4nn\\xE4 vaakasuunnassa\",\n  flipVertical: \"K\\xE4\\xE4nn\\xE4 pystysuunnassa\",\n  viewMode: \"Katselutila\",\n  share: \"Jaa\",\n  showStroke: \"N\\xE4yt\\xE4 viivan v\\xE4rin valitsin\",\n  showBackground: \"N\\xE4yt\\xE4 taustav\\xE4rin valitsin\",\n  toggleTheme: \"Vaihda teema\",\n  personalLib: \"Oma kirjasto\",\n  excalidrawLib: \"Excalidraw kirjasto\",\n  decreaseFontSize: \"Pienenn\\xE4 kirjasinkokoa\",\n  increaseFontSize: \"Kasvata kirjasinkokoa\",\n  unbindText: \"Irroita teksti\",\n  bindText: \"Kiinnit\\xE4 teksti s\\xE4ili\\xF6\\xF6n\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"Muokkaa linkki\\xE4\",\n    editEmbed: \"\",\n    create: \"Luo linkki\",\n    createEmbed: \"\",\n    label: \"Linkki\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Muokkaa rivi\\xE4\",\n    exit: \"Poistu rivieditorista\"\n  },\n  elementLock: {\n    lock: \"Lukitse\",\n    unlock: \"Poista lukitus\",\n    lockAll: \"Lukitse kaikki\",\n    unlockAll: \"Poista lukitus kaikista\"\n  },\n  statusPublished: \"Julkaistu\",\n  sidebarLock: \"Pid\\xE4 sivupalkki avoinna\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Kirjastossa ei ole viel\\xE4 yht\\xE4\\xE4n kohdetta...\",\n  hint_emptyLibrary: \"Valitse lis\\xE4tt\\xE4v\\xE4 kohde piirtoalueelta, tai asenna alta julkinen kirjasto.\",\n  hint_emptyPrivateLibrary: \"Valitse lis\\xE4tt\\xE4v\\xE4 kohde piirtoalueelta.\"\n};\nvar buttons = {\n  clearReset: \"Tyhjenn\\xE4 piirtoalue\",\n  exportJSON: \"Vie tiedostoon\",\n  exportImage: \"Vie kuva...\",\n  export: \"Tallenna nimell\\xE4...\",\n  copyToClipboard: \"Kopioi leikep\\xF6yd\\xE4lle\",\n  save: \"Tallenna nykyiseen tiedostoon\",\n  saveAs: \"Tallenna nimell\\xE4\",\n  load: \"Avaa\",\n  getShareableLink: \"Hae jaettava linkki\",\n  close: \"Sulje\",\n  selectLanguage: \"Valitse kieli\",\n  scrollBackToContent: \"N\\xE4yt\\xE4 sis\\xE4lt\\xF6\",\n  zoomIn: \"L\\xE4henn\\xE4\",\n  zoomOut: \"Loitonna\",\n  resetZoom: \"Nollaa suurennuksen taso\",\n  menu: \"Valikko\",\n  done: \"Valmis\",\n  edit: \"Muokkaa\",\n  undo: \"Kumoa\",\n  redo: \"Tee uudelleen\",\n  resetLibrary: \"Tyhjenn\\xE4 kirjasto\",\n  createNewRoom: \"Luo huone\",\n  fullScreen: \"Koko n\\xE4ytt\\xF6\",\n  darkMode: \"Tumma tila\",\n  lightMode: \"Vaalea tila\",\n  zenMode: \"Zen-tila\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Poistu zen-tilasta\",\n  cancel: \"Peruuta\",\n  clear: \"Pyyhi\",\n  remove: \"Poista\",\n  embed: \"\",\n  publishLibrary: \"Julkaise\",\n  submit: \"L\\xE4het\\xE4\",\n  confirm: \"Vahvista\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"T\\xE4m\\xE4 tyhjent\\xE4\\xE4 koko piirtoalueen. Jatketaanko?\",\n  couldNotCreateShareableLink: \"Jaettavan linkin luominen ep\\xE4onnistui.\",\n  couldNotCreateShareableLinkTooBig: \"Jaettavaa linkki\\xE4 ei voitu luoda: teos on liian suuri\",\n  couldNotLoadInvalidFile: \"Virheellist\\xE4 tiedostoa ei voitu avata\",\n  importBackendFailed: \"Palvelimelta tuonti ep\\xE4onnistui.\",\n  cannotExportEmptyCanvas: \"Tyhj\\xE4\\xE4 piirtoaluetta ei voi vied\\xE4.\",\n  couldNotCopyToClipboard: \"Leikep\\xF6yd\\xE4lle vieminen ep\\xE4onnistui.\",\n  decryptFailed: \"Salauksen purkaminen ep\\xE4onnistui.\",\n  uploadedSecurly: \"L\\xE4hetys on turvattu p\\xE4\\xE4st\\xE4-p\\xE4\\xE4h\\xE4n-salauksella. Excalidrawin palvelin ja kolmannet osapuolet eiv\\xE4t voi lukea sis\\xE4lt\\xF6\\xE4.\",\n  loadSceneOverridePrompt: \"Ulkopuolisen piirroksen lataaminen korvaa nykyisen sis\\xE4lt\\xF6si. Jatketaanko?\",\n  collabStopOverridePrompt: \"Istunnon lopettaminen korvaa aiemman, paikallisesti tallennetun piirustuksen. Jatketaanko?\\n\\n(Jos haluat s\\xE4ilytt\\xE4\\xE4 paikallisesti tallennetun piirustuksen, sulje selaimen v\\xE4lilehti lopettamisen sijaan.)\",\n  errorAddingToLibrary: \"Kohdetta ei voitu lis\\xE4t\\xE4 kirjastoon\",\n  errorRemovingFromLibrary: \"Kohdetta ei voitu poistaa kirjastosta\",\n  confirmAddLibrary: \"T\\xE4m\\xE4 lis\\xE4\\xE4 {{numShapes}} muotoa kirjastoosi. Jatketaanko?\",\n  imageDoesNotContainScene: \"T\\xE4m\\xE4 kuva ei n\\xE4yt\\xE4 sis\\xE4lt\\xE4v\\xE4n piirrostietoja. Oletko ottanut k\\xE4ytt\\xF6\\xF6n piirroksen tallennuksen viennin aikana?\",\n  cannotRestoreFromImage: \"Teosta ei voitu palauttaa t\\xE4st\\xE4 kuvatiedostosta\",\n  invalidSceneUrl: \"Teosta ei voitu tuoda annetusta URL-osoitteesta. Tallenne on vioittunut, tai osoitteessa ei ole Excalidraw JSON-dataa.\",\n  resetLibrary: \"T\\xE4m\\xE4 tyhjent\\xE4\\xE4 kirjastosi. Jatketaanko?\",\n  removeItemsFromsLibrary: \"Poista {{count}} kohdetta kirjastosta?\",\n  invalidEncryptionKey: \"Salausavaimen on oltava 22 merkki\\xE4 pitk\\xE4. Live-yhteisty\\xF6 ei ole k\\xE4yt\\xF6ss\\xE4.\",\n  collabOfflineWarning: \"Internet-yhteytt\\xE4 ei ole saatavilla.\\nMuutoksiasi ei tallenneta!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tiedostotyyppi\\xE4 ei tueta.\",\n  imageInsertError: \"Kuvan lis\\xE4\\xE4minen ep\\xE4onnistui. Yrit\\xE4 my\\xF6hemmin uudelleen...\",\n  fileTooBig: \"Tiedosto on liian suuri. Suurin sallittu koko on {{maxSize}}.\",\n  svgImageInsertError: \"SVG- kuvaa ei voitu lis\\xE4t\\xE4. Tiedoston SVG-sis\\xE4lt\\xF6 n\\xE4ytt\\xE4\\xE4 virheelliselt\\xE4.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Virheellinen SVG.\",\n  cannotResolveCollabServer: \"Yhteyden muodostaminen collab-palvelimeen ep\\xE4onnistui. Virkist\\xE4 sivu ja yrit\\xE4 uudelleen.\",\n  importLibraryError: \"Kokoelman lataaminen ep\\xE4onnistui\",\n  collabSaveFailed: \"Ei voitu tallentaan palvelimen tietokantaan. Jos ongelmia esiintyy, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, ett\\xE4 et menet\\xE4 ty\\xF6t\\xE4si.\",\n  collabSaveFailed_sizeExceeded: \"Ei voitu tallentaan palvelimen tietokantaan. Jos ongelmia esiintyy, sinun kannatta tallentaa tallentaa tiedosto paikallisesti varmistaaksesi, ett\\xE4 et menet\\xE4 ty\\xF6t\\xE4si.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Valinta\",\n  image: \"Lis\\xE4\\xE4 kuva\",\n  rectangle: \"Suorakulmio\",\n  diamond: \"Vinoneli\\xF6\",\n  ellipse: \"Soikio\",\n  arrow: \"Nuoli\",\n  line: \"Viiva\",\n  freedraw: \"Piirr\\xE4\",\n  text: \"Teksti\",\n  library: \"Kirjasto\",\n  lock: \"Pid\\xE4 valittu ty\\xF6kalu aktiivisena piirron j\\xE4lkeen\",\n  penMode: \"Kyn\\xE4tila - est\\xE4 kosketus\",\n  link: \"Lis\\xE4\\xE4/p\\xE4ivit\\xE4 linkki valitulle muodolle\",\n  eraser: \"Poistoty\\xF6kalu\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"K\\xE4si (panning-ty\\xF6kalu)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Piirtoalueen toiminnot\",\n  selectedShapeActions: \"Valitun muodon toiminnot\",\n  shapes: \"Muodot\"\n};\nvar hints = {\n  canvasPanning: \"Piirtoalueen liikuttamiseksi pid\\xE4 hiiren py\\xF6r\\xE4\\xE4 tai v\\xE4lily\\xF6nti\\xE4 pohjassa tai k\\xE4yt\\xE4 k\\xE4sity\\xF6kalua\",\n  linearElement: \"Klikkaa piirt\\xE4\\xE4ksesi useampi piste, raahaa piirt\\xE4\\xE4ksesi yksitt\\xE4inen viiva\",\n  freeDraw: \"Paina ja raahaa, p\\xE4\\xE4st\\xE4 irti kun olet valmis\",\n  text: \"Vinkki: voit my\\xF6s lis\\xE4t\\xE4 teksti\\xE4 kaksoisnapsauttamalla mihin tahansa valintaty\\xF6kalulla\",\n  embeddable: \"\",\n  text_selected: \"Kaksoisnapsauta tai paina ENTER muokataksesi teksti\\xE4\",\n  text_editing: \"Paina Escape tai CtrlOrCmd+ENTER lopettaaksesi muokkaamisen\",\n  linearElementMulti: \"Lopeta klikkaamalla viimeist\\xE4 pistett\\xE4, painamalla Escape- tai Enter-n\\xE4pp\\xE4int\\xE4\",\n  lockAngle: \"Voit rajoittaa kulmaa pit\\xE4m\\xE4ll\\xE4 SHIFT-n\\xE4pp\\xE4int\\xE4 alaspainettuna\",\n  resize: \"Voit rajoittaa mittasuhteet pit\\xE4m\\xE4ll\\xE4 SHIFT-n\\xE4pp\\xE4int\\xE4 alaspainettuna kun muutat kokoa, pid\\xE4 ALT-n\\xE4pp\\xE4int\\xE4 alaspainettuna muuttaaksesi kokoa keskipisteen suhteen\",\n  resizeImage: \"Voit muuttaa kokoa vapaasti pit\\xE4m\\xE4ll\\xE4 SHIFTi\\xE4 pohjassa, pid\\xE4 ALT pohjassa muuttaaksesi kokoa keskipisteen ymp\\xE4ri\",\n  rotate: \"Voit rajoittaa kulman pit\\xE4m\\xE4ll\\xE4 SHIFT pohjassa py\\xF6ritt\\xE4ess\\xE4si\",\n  lineEditor_info: \"Pid\\xE4 CtrlOrCmd pohjassa ja kaksoisnapsauta tai paina CtrlOrCmd + Enter muokataksesi pisteit\\xE4\",\n  lineEditor_pointSelected: \"Poista piste(et) painamalla delete, monista painamalla CtrlOrCmd+D, tai liikuta raahaamalla\",\n  lineEditor_nothingSelected: \"Valitse muokattava piste (monivalinta pit\\xE4m\\xE4ll\\xE4 SHIFT pohjassa), tai paina Alt ja klikkaa lis\\xE4t\\xE4ksesi uusia pisteit\\xE4\",\n  placeImage: \"Klikkaa asettaaksesi kuvan, tai klikkaa ja raahaa asettaaksesi sen koon manuaalisesti\",\n  publishLibrary: \"Julkaise oma kirjasto\",\n  bindTextToElement: \"Lis\\xE4\\xE4 teksti\\xE4 painamalla enter\",\n  deepBoxSelect: \"K\\xE4yt\\xE4 syv\\xE4valintaa ja est\\xE4 raahaus painamalla CtrlOrCmd\",\n  eraserRevert: \"Pid\\xE4 Alt alaspainettuna, kumotaksesi merkittyjen elementtien poistamisen\",\n  firefox_clipboard_write: 'T\\xE4m\\xE4 ominaisuus voidaan todenn\\xE4k\\xF6isesti ottaa k\\xE4ytt\\xF6\\xF6n asettamalla \"dom.events.asyncClipboard.clipboardItem\" kohta \"true\":ksi. Vaihtaaksesi selaimen kohdan Firefoxissa, k\\xE4y \"about:config\" sivulla.',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Esikatselua ei voitu n\\xE4ytt\\xE4\\xE4\",\n  canvasTooBig: \"Piirtoalue saattaa olla liian suuri.\",\n  canvasTooBigTip: \"Vinkki: yrit\\xE4 siirt\\xE4\\xE4 kaukaisimpia elementtej\\xE4 hieman l\\xE4hemm\\xE4s toisiaan.\"\n};\nvar errorSplash = {\n  headingMain: \"Tapahtui virhe. Yrit\\xE4 <button>sivun lataamista uudelleen.</button>\",\n  clearCanvasMessage: \"Mik\\xE4li sivun lataaminen uudelleen ei auta, yrit\\xE4 <button>tyhjent\\xE4\\xE4 piirtoalue.</button>\",\n  clearCanvasCaveat: \" T\\xE4m\\xE4 johtaa ty\\xF6n menetykseen \",\n  trackedToSentry: \"Virhe tunnisteella {{eventId}} tallennettiin j\\xE4rjestelm\\xE4\\xE4mme.\",\n  openIssueMessage: \"Olimme varovaisia emmek\\xE4 sis\\xE4llytt\\xE4neet tietoa piirroksestasi virheeseen. Mik\\xE4li piirroksesi ei ole yksityinen, harkitsethan kertovasi meille <button>virheenseurantaj\\xE4rjestelm\\xE4ss\\xE4mme.</button> Sis\\xE4llyt\\xE4 alla olevat tiedot kopioimalla ne GitHub-ongelmaan.\",\n  sceneContent: \"Piirroksen tiedot:\"\n};\nvar roomDialog = {\n  desc_intro: \"Voit kutsua ihmisi\\xE4 piirrokseesi tekem\\xE4\\xE4n yhteisty\\xF6t\\xE4 kanssasi.\",\n  desc_privacy: \"\\xC4l\\xE4 huoli, istunto k\\xE4ytt\\xE4\\xE4 p\\xE4\\xE4st\\xE4-p\\xE4\\xE4h\\xE4n-salausta, joten mit\\xE4 tahansa piirr\\xE4tkin, se pysyy salassa. Edes palvelimemme eiv\\xE4t n\\xE4e mit\\xE4 keksit.\",\n  button_startSession: \"Aloita istunto\",\n  button_stopSession: \"Lopeta istunto\",\n  desc_inProgressIntro: \"Jaettu istunto on nyt k\\xE4ynniss\\xE4.\",\n  desc_shareLink: \"Jaa t\\xE4m\\xE4 linkki kenelle tahansa, jonka kanssa haluat tehd\\xE4 yhteisty\\xF6t\\xE4:\",\n  desc_exitSession: \"Istunnon pys\\xE4ytt\\xE4minen katkaisee yhteyden huoneeseen, mutta voit viel\\xE4 jatkaa ty\\xF6skentely\\xE4 paikallisesti. Huomaa, ett\\xE4 t\\xE4m\\xE4 ei vaikuta muihin k\\xE4ytt\\xE4jiin ja he voivat jatkaa oman versionsa parissa ty\\xF6skentely\\xE4.\",\n  shareTitle: \"Liity Excalidraw live-yhteisty\\xF6istuntoon\"\n};\nvar errorDialog = {\n  title: \"Virhe\"\n};\nvar exportDialog = {\n  disk_title: \"Tallenna levylle\",\n  disk_details: \"Vie ty\\xF6n tiedot tiedostoon, josta sen voi tuoda my\\xF6hemmin.\",\n  disk_button: \"Tallenna tiedostoon\",\n  link_title: \"Jaettava linkki\",\n  link_details: \"Vie vain luku -linkkin\\xE4.\",\n  link_button: \"Vie linkkin\\xE4\",\n  excalidrawplus_description: \"Tallenna teos Excalidraw+ tilaan.\",\n  excalidrawplus_button: \"Vie\",\n  excalidrawplus_exportError: \"Ei voitu vied\\xE4 Excalidraw+-palveluun t\\xE4ll\\xE4 hetkell\\xE4...\"\n};\nvar helpDialog = {\n  blog: \"Lue blogiamme\",\n  click: \"klikkaa\",\n  deepSelect: \"Syv\\xE4valinta\",\n  deepBoxSelect: \"K\\xE4yt\\xE4 syv\\xE4valintaa ja est\\xE4 raahaus\",\n  curvedArrow: \"Kaareva nuoli\",\n  curvedLine: \"Kaareva viiva\",\n  documentation: \"K\\xE4ytt\\xF6ohjeet\",\n  doubleClick: \"kaksoisnapsautus\",\n  drag: \"ved\\xE4\",\n  editor: \"Muokkausohjelma\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"L\\xF6ysitk\\xF6 ongelman? Kerro meille\",\n  howto: \"Seuraa oppaitamme\",\n  or: \"tai\",\n  preventBinding: \"Est\\xE4 nuolten kiinnitys\",\n  tools: \"Ty\\xF6kalut\",\n  shortcuts: \"Pikan\\xE4pp\\xE4imet\",\n  textFinish: \"Lopeta muokkaus (tekstieditori)\",\n  textNewLine: \"Lis\\xE4\\xE4 uusi rivi (tekstieditori)\",\n  title: \"Ohjeet\",\n  view: \"N\\xE4kym\\xE4\",\n  zoomToFit: \"N\\xE4yt\\xE4 kaikki elementit\",\n  zoomToSelection: \"N\\xE4yt\\xE4 valinta\",\n  toggleElementLock: \"Lukitse / poista lukitus valinta\",\n  movePageUpDown: \"Siirr\\xE4 sivua yl\\xF6s/alas\",\n  movePageLeftRight: \"Siirr\\xE4 sivua vasemmalle/oikealle\"\n};\nvar clearCanvasDialog = {\n  title: \"Pyyhi piirtoalue\"\n};\nvar publishDialog = {\n  title: \"Julkaise kirjasto\",\n  itemName: \"Kohteen nimi\",\n  authorName: \"Tekij\\xE4n nimi\",\n  githubUsername: \"GitHub-k\\xE4ytt\\xE4j\\xE4tunnus\",\n  twitterUsername: \"Twitter-k\\xE4ytt\\xE4j\\xE4tunnus\",\n  libraryName: \"Kirjaston nimi\",\n  libraryDesc: \"Kirjaston kuvaus\",\n  website: \"Verkkosivu\",\n  placeholder: {\n    authorName: \"Nimesi tai k\\xE4ytt\\xE4j\\xE4nimesi\",\n    libraryName: \"Kirjastosi nimi\",\n    libraryDesc: \"Kirjaston kuvaus, joka auttaa ihmisi\\xE4 ymm\\xE4rt\\xE4m\\xE4\\xE4n sen k\\xE4ytt\\xF6tarkoitukset\",\n    githubHandle: \"GitHub-tunnuksesi (valinnainen), jotta voit muokata kirjastoa sen j\\xE4lkeen kun se on l\\xE4hetetty tarkastettavaksi\",\n    twitterHandle: \"Twitter-tunnus (valinnainen), jotta tied\\xE4mme ket\\xE4 kiitt\\xE4\\xE4 kun viestimme Twitteriss\\xE4\",\n    website: \"Linkki henkil\\xF6kohtaiselle verkkosivustollesi tai muualle (valinnainen)\"\n  },\n  errors: {\n    required: \"Pakollinen\",\n    website: \"Sy\\xF6t\\xE4 oikeamuotoinen URL-osoite\"\n  },\n  noteDescription: \"L\\xE4het\\xE4 kirjastosi, jotta se voidaan sis\\xE4llytt\\xE4\\xE4 <link>julkisessa kirjastolistauksessa</link>muiden k\\xE4ytt\\xF6\\xF6n omissa piirrustuksissaan.\",\n  noteGuidelines: \"Kirjasto on ensin hyv\\xE4ksytt\\xE4v\\xE4 manuaalisesti. Ole hyv\\xE4 ja lue <link>ohjeet</link> ennen l\\xE4hett\\xE4mist\\xE4. Tarvitset GitHub-tilin, jotta voit viesti\\xE4 ja tehd\\xE4 muutoksia pyydett\\xE4ess\\xE4, mutta se ei ole ehdottoman v\\xE4ltt\\xE4m\\xE4t\\xF6nt\\xE4.\",\n  noteLicense: \"L\\xE4hett\\xE4m\\xE4ll\\xE4 hyv\\xE4ksyt ett\\xE4 kirjasto julkaistaan <link>MIT-lisenssin </link>alla, mik\\xE4 lyhyesti antaa muiden k\\xE4ytt\\xE4\\xE4 sit\\xE4 ilman rajoituksia.\",\n  noteItems: \"Jokaisella kirjaston kohteella on oltava oma nimens\\xE4 suodatusta varten. Seuraavat kirjaston kohteet sis\\xE4ltyv\\xE4t:\",\n  atleastOneLibItem: \"Valitse v\\xE4hint\\xE4\\xE4n yksi kirjaston kohde aloittaaksesi\",\n  republishWarning: \"Huom! Osa valituista kohteista on merkitty jo julkaistu/l\\xE4hetetyiksi. L\\xE4het\\xE4 kohteita uudelleen vain p\\xE4ivitett\\xE4ess\\xE4 olemassa olevaa kirjastoa tai ehdotusta.\"\n};\nvar publishSuccessDialog = {\n  title: \"Kirjasto l\\xE4hetetty\",\n  content: \"Kiitos {{authorName}}. Kirjastosi on l\\xE4hetetty tarkistettavaksi. Voit seurata sen tilaa<link>t\\xE4\\xE4ll\\xE4</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Tyhjenn\\xE4 kirjasto\",\n  removeItemsFromLib: \"Poista valitut kohteet kirjastosta\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Piirroksesi ovat p\\xE4\\xE4st\\xE4-p\\xE4\\xE4h\\xE4n-salattuja, joten Excalidrawin palvelimet eiv\\xE4t koskaan n\\xE4e niit\\xE4.\",\n  link: \"Blogiartikkeli p\\xE4\\xE4st\\xE4 p\\xE4\\xE4h\\xE4n -salauksesta Excalidraw:ssa\"\n};\nvar stats = {\n  angle: \"Kulma\",\n  element: \"Elementti\",\n  elements: \"Elementit\",\n  height: \"Korkeus\",\n  scene: \"Teos\",\n  selected: \"Valitut\",\n  storage: \"Tallennustila\",\n  title: \"Tilastoja n\\xF6rteille\",\n  total: \"Yhteens\\xE4\",\n  version: \"Versio\",\n  versionCopy: \"Klikkaa kopioidaksesi\",\n  versionNotAvailable: \"Versio ei saatavilla\",\n  width: \"Leveys\"\n};\nvar toast = {\n  addedToLibrary: \"Lis\\xE4tty kirjastoon\",\n  copyStyles: \"Tyylit kopioitiin.\",\n  copyToClipboard: \"Kopioitiin leikep\\xF6yd\\xE4lle.\",\n  copyToClipboardAsPng: \"Kopioitiin {{exportSelection}} leikep\\xF6yd\\xE4lle PNG:n\\xE4\\n({{exportColorScheme}})\",\n  fileSaved: \"Tiedosto tallennettu.\",\n  fileSavedToFilename: \"Tallennettiin kohteeseen {filename}\",\n  canvas: \"piirtoalue\",\n  selection: \"valinta\",\n  pasteAsSingleElement: \"K\\xE4yt\\xE4 {{shortcut}} liitt\\xE4\\xE4ksesi yhten\\xE4 elementtin\\xE4,\\ntai liitt\\xE4\\xE4ksesi olemassa olevaan tekstieditoriin\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"L\\xE4pin\\xE4kyv\\xE4\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Kaikki tietosi on tallennettu paikallisesti selaimellesi.\",\n    center_heading_plus: \"Haluatko sen sijaan menn\\xE4 Excalidraw+:aan?\",\n    menuHint: \"Vie, asetukset, kielet, ...\"\n  },\n  defaults: {\n    menuHint: \"Vie, asetukset ja lis\\xE4\\xE4...\",\n    center_heading: \"Kaaviot. Tehty. Yksinkertaiseksi.\",\n    toolbarHint: \"Valitse ty\\xF6kalu ja aloita piirt\\xE4minen!\",\n    helpHint: \"Pikan\\xE4pp\\xE4imet & ohje\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar fi_FI_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=fi-FI-M3WLVDFP.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/fi-FI-M3WLVDFP.js\n"));

/***/ })

}]);