"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_image-blob-reduce_dist_image-blob-reduce_esm_mjs"],{

/***/ "(app-pages-browser)/./node_modules/image-blob-reduce/dist/image-blob-reduce.esm.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/image-blob-reduce/dist/image-blob-reduce.esm.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\n/*! image-blob-reduce 3.0.1 https://github.com/nodeca/image-blob-reduce @license MIT */\nvar assign$1 = function assign(to) {\n  var from;\n\n  for (var s = 1; s < arguments.length; s++) {\n    from = Object(arguments[s]);\n\n    for (var key in from) {\n      if (Object.prototype.hasOwnProperty.call(from, key)) to[key] = from[key];\n    }\n  }\n\n  return to;\n};\n\n\nfunction pick(from, props) {\n  var to = {};\n\n  props.forEach(function (key) {\n    if (Object.prototype.hasOwnProperty.call(from, key)) to[key] = from[key];\n  });\n\n  return to;\n}\n\n\nfunction pick_pica_resize_options(from) {\n  return pick(from, [\n    'alpha',\n    'unsharpAmount',\n    'unsharpRadius',\n    'unsharpThreshold',\n    'cancelToken'\n  ]);\n}\n\n\nvar pick_1 = pick;\nvar pick_pica_resize_options_1 = pick_pica_resize_options;\n\nvar utils = {\n\tassign: assign$1,\n\tpick: pick_1,\n\tpick_pica_resize_options: pick_pica_resize_options_1\n};\n\nfunction createCommonjsModule(fn) {\n  var module = { exports: {} };\n\treturn fn(module, module.exports), module.exports;\n}\n\nfunction commonjsRequire (target) {\n\tthrow new Error('Could not dynamically require \"' + target + '\". Please configure the dynamicRequireTargets option of @rollup/plugin-commonjs appropriately for this require call to behave properly.');\n}\n\n/*!\n\npica\nhttps://github.com/nodeca/pica\n\n*/\n\nvar pica = createCommonjsModule(function (module, exports) {\n(function(f){{module.exports=f();}})(function(){return (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c=\"function\"==typeof commonjsRequire&&commonjsRequire;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error(\"Cannot find module '\"+i+\"'\");throw a.code=\"MODULE_NOT_FOUND\",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t);}return n[i].exports}for(var u=\"function\"==typeof commonjsRequire&&commonjsRequire,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(_dereq_,module,exports){\n\nvar inherits = _dereq_('inherits');\n\nvar Multimath = _dereq_('multimath');\n\nvar mm_unsharp_mask = _dereq_('./mm_unsharp_mask');\n\nvar mm_resize = _dereq_('./mm_resize');\n\nfunction MathLib(requested_features) {\n  var __requested_features = requested_features || [];\n\n  var features = {\n    js: __requested_features.indexOf('js') >= 0,\n    wasm: __requested_features.indexOf('wasm') >= 0\n  };\n  Multimath.call(this, features);\n  this.features = {\n    js: features.js,\n    wasm: features.wasm && this.has_wasm()\n  };\n  this.use(mm_unsharp_mask);\n  this.use(mm_resize);\n}\n\ninherits(MathLib, Multimath);\n\nMathLib.prototype.resizeAndUnsharp = function resizeAndUnsharp(options, cache) {\n  var result = this.resize(options, cache);\n\n  if (options.unsharpAmount) {\n    this.unsharp_mask(result, options.toWidth, options.toHeight, options.unsharpAmount, options.unsharpRadius, options.unsharpThreshold);\n  }\n\n  return result;\n};\n\nmodule.exports = MathLib;\n\n},{\"./mm_resize\":4,\"./mm_unsharp_mask\":9,\"inherits\":19,\"multimath\":20}],2:[function(_dereq_,module,exports){\n//var FIXED_FRAC_BITS = 14;\n\nfunction clampTo8(i) {\n  return i < 0 ? 0 : i > 255 ? 255 : i;\n} // Convolve image in horizontal directions and transpose output. In theory,\n// transpose allow:\n//\n// - use the same convolver for both passes (this fails due different\n//   types of input array and temporary buffer)\n// - making vertical pass by horisonltal lines inprove CPU cache use.\n//\n// But in real life this doesn't work :)\n//\n\n\nfunction convolveHorizontally(src, dest, srcW, srcH, destW, filters) {\n  var r, g, b, a;\n  var filterPtr, filterShift, filterSize;\n  var srcPtr, srcY, destX, filterVal;\n  var srcOffset = 0,\n      destOffset = 0; // For each row\n\n  for (srcY = 0; srcY < srcH; srcY++) {\n    filterPtr = 0; // Apply precomputed filters to each destination row point\n\n    for (destX = 0; destX < destW; destX++) {\n      // Get the filter that determines the current output pixel.\n      filterShift = filters[filterPtr++];\n      filterSize = filters[filterPtr++];\n      srcPtr = srcOffset + filterShift * 4 | 0;\n      r = g = b = a = 0; // Apply the filter to the row to get the destination pixel r, g, b, a\n\n      for (; filterSize > 0; filterSize--) {\n        filterVal = filters[filterPtr++]; // Use reverse order to workaround deopts in old v8 (node v.10)\n        // Big thanks to @mraleph (Vyacheslav Egorov) for the tip.\n\n        a = a + filterVal * src[srcPtr + 3] | 0;\n        b = b + filterVal * src[srcPtr + 2] | 0;\n        g = g + filterVal * src[srcPtr + 1] | 0;\n        r = r + filterVal * src[srcPtr] | 0;\n        srcPtr = srcPtr + 4 | 0;\n      } // Bring this value back in range. All of the filter scaling factors\n      // are in fixed point with FIXED_FRAC_BITS bits of fractional part.\n      //\n      // (!) Add 1/2 of value before clamping to get proper rounding. In other\n      // case brightness loss will be noticeable if you resize image with white\n      // border and place it on white background.\n      //\n\n\n      dest[destOffset + 3] = clampTo8(a + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      dest[destOffset + 2] = clampTo8(b + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      dest[destOffset + 1] = clampTo8(g + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      dest[destOffset] = clampTo8(r + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      destOffset = destOffset + srcH * 4 | 0;\n    }\n\n    destOffset = (srcY + 1) * 4 | 0;\n    srcOffset = (srcY + 1) * srcW * 4 | 0;\n  }\n} // Technically, convolvers are the same. But input array and temporary\n// buffer can be of different type (especially, in old browsers). So,\n// keep code in separate functions to avoid deoptimizations & speed loss.\n\n\nfunction convolveVertically(src, dest, srcW, srcH, destW, filters) {\n  var r, g, b, a;\n  var filterPtr, filterShift, filterSize;\n  var srcPtr, srcY, destX, filterVal;\n  var srcOffset = 0,\n      destOffset = 0; // For each row\n\n  for (srcY = 0; srcY < srcH; srcY++) {\n    filterPtr = 0; // Apply precomputed filters to each destination row point\n\n    for (destX = 0; destX < destW; destX++) {\n      // Get the filter that determines the current output pixel.\n      filterShift = filters[filterPtr++];\n      filterSize = filters[filterPtr++];\n      srcPtr = srcOffset + filterShift * 4 | 0;\n      r = g = b = a = 0; // Apply the filter to the row to get the destination pixel r, g, b, a\n\n      for (; filterSize > 0; filterSize--) {\n        filterVal = filters[filterPtr++]; // Use reverse order to workaround deopts in old v8 (node v.10)\n        // Big thanks to @mraleph (Vyacheslav Egorov) for the tip.\n\n        a = a + filterVal * src[srcPtr + 3] | 0;\n        b = b + filterVal * src[srcPtr + 2] | 0;\n        g = g + filterVal * src[srcPtr + 1] | 0;\n        r = r + filterVal * src[srcPtr] | 0;\n        srcPtr = srcPtr + 4 | 0;\n      } // Bring this value back in range. All of the filter scaling factors\n      // are in fixed point with FIXED_FRAC_BITS bits of fractional part.\n      //\n      // (!) Add 1/2 of value before clamping to get proper rounding. In other\n      // case brightness loss will be noticeable if you resize image with white\n      // border and place it on white background.\n      //\n\n\n      dest[destOffset + 3] = clampTo8(a + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      dest[destOffset + 2] = clampTo8(b + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      dest[destOffset + 1] = clampTo8(g + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      dest[destOffset] = clampTo8(r + (1 << 13) >> 14\n      /*FIXED_FRAC_BITS*/\n      );\n      destOffset = destOffset + srcH * 4 | 0;\n    }\n\n    destOffset = (srcY + 1) * 4 | 0;\n    srcOffset = (srcY + 1) * srcW * 4 | 0;\n  }\n}\n\nmodule.exports = {\n  convolveHorizontally: convolveHorizontally,\n  convolveVertically: convolveVertically\n};\n\n},{}],3:[function(_dereq_,module,exports){\n/* eslint-disable max-len */\n\nmodule.exports = 'AGFzbQEAAAAADAZkeWxpbmsAAAAAAAEXA2AAAGAGf39/f39/AGAHf39/f39/fwACDwEDZW52Bm1lbW9yeQIAAAMEAwABAgYGAX8AQQALB1cFEV9fd2FzbV9jYWxsX2N0b3JzAAAIY29udm9sdmUAAQpjb252b2x2ZUhWAAIMX19kc29faGFuZGxlAwAYX193YXNtX2FwcGx5X2RhdGFfcmVsb2NzAAAK7AMDAwABC8YDAQ9/AkAgA0UNACAERQ0AA0AgDCENQQAhE0EAIQcDQCAHQQJqIQYCfyAHQQF0IAVqIgcuAQIiFEUEQEGAwAAhCEGAwAAhCUGAwAAhCkGAwAAhCyAGDAELIBIgBy4BAGohCEEAIQsgFCEHQQAhDiAGIQlBACEPQQAhEANAIAUgCUEBdGouAQAiESAAIAhBAnRqKAIAIgpBGHZsIBBqIRAgCkH/AXEgEWwgC2ohCyAKQRB2Qf8BcSARbCAPaiEPIApBCHZB/wFxIBFsIA5qIQ4gCEEBaiEIIAlBAWohCSAHQQFrIgcNAAsgC0GAQGshCCAOQYBAayEJIA9BgEBrIQogEEGAQGshCyAGIBRqCyEHIAEgDUECdGogCUEOdSIGQf8BIAZB/wFIGyIGQQAgBkEAShtBCHRBgP4DcSAKQQ51IgZB/wEgBkH/AUgbIgZBACAGQQBKG0EQdEGAgPwHcSALQQ51IgZB/wEgBkH/AUgbIgZBACAGQQBKG0EYdHJyIAhBDnUiBkH/ASAGQf8BSBsiBkEAIAZBAEobcjYCACADIA1qIQ0gE0EBaiITIARHDQALIAxBAWoiDCACbCESIAMgDEcNAAsLCx4AQQAgAiADIAQgBSAAEAEgAkEAIAQgBSAGIAEQAQs=';\n\n},{}],4:[function(_dereq_,module,exports){\n\nmodule.exports = {\n  name: 'resize',\n  fn: _dereq_('./resize'),\n  wasm_fn: _dereq_('./resize_wasm'),\n  wasm_src: _dereq_('./convolve_wasm_base64')\n};\n\n},{\"./convolve_wasm_base64\":3,\"./resize\":5,\"./resize_wasm\":8}],5:[function(_dereq_,module,exports){\n\nvar createFilters = _dereq_('./resize_filter_gen');\n\nvar convolveHorizontally = _dereq_('./convolve').convolveHorizontally;\n\nvar convolveVertically = _dereq_('./convolve').convolveVertically;\n\nfunction resetAlpha(dst, width, height) {\n  var ptr = 3,\n      len = width * height * 4 | 0;\n\n  while (ptr < len) {\n    dst[ptr] = 0xFF;\n    ptr = ptr + 4 | 0;\n  }\n}\n\nmodule.exports = function resize(options) {\n  var src = options.src;\n  var srcW = options.width;\n  var srcH = options.height;\n  var destW = options.toWidth;\n  var destH = options.toHeight;\n  var scaleX = options.scaleX || options.toWidth / options.width;\n  var scaleY = options.scaleY || options.toHeight / options.height;\n  var offsetX = options.offsetX || 0;\n  var offsetY = options.offsetY || 0;\n  var dest = options.dest || new Uint8Array(destW * destH * 4);\n  var quality = typeof options.quality === 'undefined' ? 3 : options.quality;\n  var alpha = options.alpha || false;\n  var filtersX = createFilters(quality, srcW, destW, scaleX, offsetX),\n      filtersY = createFilters(quality, srcH, destH, scaleY, offsetY);\n  var tmp = new Uint8Array(destW * srcH * 4); // To use single function we need src & tmp of the same type.\n  // But src can be CanvasPixelArray, and tmp - Uint8Array. So, keep\n  // vertical and horizontal passes separately to avoid deoptimization.\n\n  convolveHorizontally(src, tmp, srcW, srcH, destW, filtersX);\n  convolveVertically(tmp, dest, srcH, destW, destH, filtersY); // That's faster than doing checks in convolver.\n  // !!! Note, canvas data is not premultipled. We don't need other\n  // alpha corrections.\n\n  if (!alpha) resetAlpha(dest, destW, destH);\n  return dest;\n};\n\n},{\"./convolve\":2,\"./resize_filter_gen\":6}],6:[function(_dereq_,module,exports){\n\nvar FILTER_INFO = _dereq_('./resize_filter_info'); // Precision of fixed FP values\n\n\nvar FIXED_FRAC_BITS = 14;\n\nfunction toFixedPoint(num) {\n  return Math.round(num * ((1 << FIXED_FRAC_BITS) - 1));\n}\n\nmodule.exports = function resizeFilterGen(quality, srcSize, destSize, scale, offset) {\n  var filterFunction = FILTER_INFO[quality].filter;\n  var scaleInverted = 1.0 / scale;\n  var scaleClamped = Math.min(1.0, scale); // For upscale\n  // Filter window (averaging interval), scaled to src image\n\n  var srcWindow = FILTER_INFO[quality].win / scaleClamped;\n  var destPixel, srcPixel, srcFirst, srcLast, filterElementSize, floatFilter, fxpFilter, total, pxl, idx, floatVal, filterTotal, filterVal;\n  var leftNotEmpty, rightNotEmpty, filterShift, filterSize;\n  var maxFilterElementSize = Math.floor((srcWindow + 1) * 2);\n  var packedFilter = new Int16Array((maxFilterElementSize + 2) * destSize);\n  var packedFilterPtr = 0;\n  var slowCopy = !packedFilter.subarray || !packedFilter.set; // For each destination pixel calculate source range and built filter values\n\n  for (destPixel = 0; destPixel < destSize; destPixel++) {\n    // Scaling should be done relative to central pixel point\n    srcPixel = (destPixel + 0.5) * scaleInverted + offset;\n    srcFirst = Math.max(0, Math.floor(srcPixel - srcWindow));\n    srcLast = Math.min(srcSize - 1, Math.ceil(srcPixel + srcWindow));\n    filterElementSize = srcLast - srcFirst + 1;\n    floatFilter = new Float32Array(filterElementSize);\n    fxpFilter = new Int16Array(filterElementSize);\n    total = 0.0; // Fill filter values for calculated range\n\n    for (pxl = srcFirst, idx = 0; pxl <= srcLast; pxl++, idx++) {\n      floatVal = filterFunction((pxl + 0.5 - srcPixel) * scaleClamped);\n      total += floatVal;\n      floatFilter[idx] = floatVal;\n    } // Normalize filter, convert to fixed point and accumulate conversion error\n\n\n    filterTotal = 0;\n\n    for (idx = 0; idx < floatFilter.length; idx++) {\n      filterVal = floatFilter[idx] / total;\n      filterTotal += filterVal;\n      fxpFilter[idx] = toFixedPoint(filterVal);\n    } // Compensate normalization error, to minimize brightness drift\n\n\n    fxpFilter[destSize >> 1] += toFixedPoint(1.0 - filterTotal); //\n    // Now pack filter to useable form\n    //\n    // 1. Trim heading and tailing zero values, and compensate shitf/length\n    // 2. Put all to single array in this format:\n    //\n    //    [ pos shift, data length, value1, value2, value3, ... ]\n    //\n\n    leftNotEmpty = 0;\n\n    while (leftNotEmpty < fxpFilter.length && fxpFilter[leftNotEmpty] === 0) {\n      leftNotEmpty++;\n    }\n\n    if (leftNotEmpty < fxpFilter.length) {\n      rightNotEmpty = fxpFilter.length - 1;\n\n      while (rightNotEmpty > 0 && fxpFilter[rightNotEmpty] === 0) {\n        rightNotEmpty--;\n      }\n\n      filterShift = srcFirst + leftNotEmpty;\n      filterSize = rightNotEmpty - leftNotEmpty + 1;\n      packedFilter[packedFilterPtr++] = filterShift; // shift\n\n      packedFilter[packedFilterPtr++] = filterSize; // size\n\n      if (!slowCopy) {\n        packedFilter.set(fxpFilter.subarray(leftNotEmpty, rightNotEmpty + 1), packedFilterPtr);\n        packedFilterPtr += filterSize;\n      } else {\n        // fallback for old IE < 11, without subarray/set methods\n        for (idx = leftNotEmpty; idx <= rightNotEmpty; idx++) {\n          packedFilter[packedFilterPtr++] = fxpFilter[idx];\n        }\n      }\n    } else {\n      // zero data, write header only\n      packedFilter[packedFilterPtr++] = 0; // shift\n\n      packedFilter[packedFilterPtr++] = 0; // size\n    }\n  }\n\n  return packedFilter;\n};\n\n},{\"./resize_filter_info\":7}],7:[function(_dereq_,module,exports){\n\nmodule.exports = [{\n  // Nearest neibor (Box)\n  win: 0.5,\n  filter: function filter(x) {\n    return x >= -0.5 && x < 0.5 ? 1.0 : 0.0;\n  }\n}, {\n  // Hamming\n  win: 1.0,\n  filter: function filter(x) {\n    if (x <= -1.0 || x >= 1.0) {\n      return 0.0;\n    }\n\n    if (x > -1.19209290E-07 && x < 1.19209290E-07) {\n      return 1.0;\n    }\n\n    var xpi = x * Math.PI;\n    return Math.sin(xpi) / xpi * (0.54 + 0.46 * Math.cos(xpi / 1.0));\n  }\n}, {\n  // Lanczos, win = 2\n  win: 2.0,\n  filter: function filter(x) {\n    if (x <= -2.0 || x >= 2.0) {\n      return 0.0;\n    }\n\n    if (x > -1.19209290E-07 && x < 1.19209290E-07) {\n      return 1.0;\n    }\n\n    var xpi = x * Math.PI;\n    return Math.sin(xpi) / xpi * Math.sin(xpi / 2.0) / (xpi / 2.0);\n  }\n}, {\n  // Lanczos, win = 3\n  win: 3.0,\n  filter: function filter(x) {\n    if (x <= -3.0 || x >= 3.0) {\n      return 0.0;\n    }\n\n    if (x > -1.19209290E-07 && x < 1.19209290E-07) {\n      return 1.0;\n    }\n\n    var xpi = x * Math.PI;\n    return Math.sin(xpi) / xpi * Math.sin(xpi / 3.0) / (xpi / 3.0);\n  }\n}];\n\n},{}],8:[function(_dereq_,module,exports){\n\nvar createFilters = _dereq_('./resize_filter_gen');\n\nfunction resetAlpha(dst, width, height) {\n  var ptr = 3,\n      len = width * height * 4 | 0;\n\n  while (ptr < len) {\n    dst[ptr] = 0xFF;\n    ptr = ptr + 4 | 0;\n  }\n}\n\nfunction asUint8Array(src) {\n  return new Uint8Array(src.buffer, 0, src.byteLength);\n}\n\nvar IS_LE = true; // should not crash everything on module load in old browsers\n\ntry {\n  IS_LE = new Uint32Array(new Uint8Array([1, 0, 0, 0]).buffer)[0] === 1;\n} catch (__) {}\n\nfunction copyInt16asLE(src, target, target_offset) {\n  if (IS_LE) {\n    target.set(asUint8Array(src), target_offset);\n    return;\n  }\n\n  for (var ptr = target_offset, i = 0; i < src.length; i++) {\n    var data = src[i];\n    target[ptr++] = data & 0xFF;\n    target[ptr++] = data >> 8 & 0xFF;\n  }\n}\n\nmodule.exports = function resize_wasm(options) {\n  var src = options.src;\n  var srcW = options.width;\n  var srcH = options.height;\n  var destW = options.toWidth;\n  var destH = options.toHeight;\n  var scaleX = options.scaleX || options.toWidth / options.width;\n  var scaleY = options.scaleY || options.toHeight / options.height;\n  var offsetX = options.offsetX || 0.0;\n  var offsetY = options.offsetY || 0.0;\n  var dest = options.dest || new Uint8Array(destW * destH * 4);\n  var quality = typeof options.quality === 'undefined' ? 3 : options.quality;\n  var alpha = options.alpha || false;\n  var filtersX = createFilters(quality, srcW, destW, scaleX, offsetX),\n      filtersY = createFilters(quality, srcH, destH, scaleY, offsetY); // destination is 0 too.\n\n  var src_offset = 0; // buffer between convolve passes\n\n  var tmp_offset = this.__align(src_offset + Math.max(src.byteLength, dest.byteLength));\n\n  var filtersX_offset = this.__align(tmp_offset + srcH * destW * 4);\n\n  var filtersY_offset = this.__align(filtersX_offset + filtersX.byteLength);\n\n  var alloc_bytes = filtersY_offset + filtersY.byteLength;\n\n  var instance = this.__instance('resize', alloc_bytes); //\n  // Fill memory block with data to process\n  //\n\n\n  var mem = new Uint8Array(this.__memory.buffer);\n  var mem32 = new Uint32Array(this.__memory.buffer); // 32-bit copy is much faster in chrome\n\n  var src32 = new Uint32Array(src.buffer);\n  mem32.set(src32); // We should guarantee LE bytes order. Filters are not big, so\n  // speed difference is not significant vs direct .set()\n\n  copyInt16asLE(filtersX, mem, filtersX_offset);\n  copyInt16asLE(filtersY, mem, filtersY_offset); //\n  // Now call webassembly method\n  // emsdk does method names with '_'\n\n  var fn = instance.exports.convolveHV || instance.exports._convolveHV;\n  fn(filtersX_offset, filtersY_offset, tmp_offset, srcW, srcH, destW, destH); //\n  // Copy data back to typed array\n  //\n  // 32-bit copy is much faster in chrome\n\n  var dest32 = new Uint32Array(dest.buffer);\n  dest32.set(new Uint32Array(this.__memory.buffer, 0, destH * destW)); // That's faster than doing checks in convolver.\n  // !!! Note, canvas data is not premultipled. We don't need other\n  // alpha corrections.\n\n  if (!alpha) resetAlpha(dest, destW, destH);\n  return dest;\n};\n\n},{\"./resize_filter_gen\":6}],9:[function(_dereq_,module,exports){\n\nmodule.exports = {\n  name: 'unsharp_mask',\n  fn: _dereq_('./unsharp_mask'),\n  wasm_fn: _dereq_('./unsharp_mask_wasm'),\n  wasm_src: _dereq_('./unsharp_mask_wasm_base64')\n};\n\n},{\"./unsharp_mask\":10,\"./unsharp_mask_wasm\":11,\"./unsharp_mask_wasm_base64\":12}],10:[function(_dereq_,module,exports){\n\nvar glur_mono16 = _dereq_('glur/mono16');\n\nfunction hsv_v16(img, width, height) {\n  var size = width * height;\n  var out = new Uint16Array(size);\n  var r, g, b, max;\n\n  for (var i = 0; i < size; i++) {\n    r = img[4 * i];\n    g = img[4 * i + 1];\n    b = img[4 * i + 2];\n    max = r >= g && r >= b ? r : g >= b && g >= r ? g : b;\n    out[i] = max << 8;\n  }\n\n  return out;\n}\n\nmodule.exports = function unsharp(img, width, height, amount, radius, threshold) {\n  var v1, v2, vmul;\n  var diff, iTimes4;\n\n  if (amount === 0 || radius < 0.5) {\n    return;\n  }\n\n  if (radius > 2.0) {\n    radius = 2.0;\n  }\n\n  var brightness = hsv_v16(img, width, height);\n  var blured = new Uint16Array(brightness); // copy, because blur modify src\n\n  glur_mono16(blured, width, height, radius);\n  var amountFp = amount / 100 * 0x1000 + 0.5 | 0;\n  var thresholdFp = threshold << 8;\n  var size = width * height;\n  /* eslint-disable indent */\n\n  for (var i = 0; i < size; i++) {\n    v1 = brightness[i];\n    diff = v1 - blured[i];\n\n    if (Math.abs(diff) >= thresholdFp) {\n      // add unsharp mask to the brightness channel\n      v2 = v1 + (amountFp * diff + 0x800 >> 12); // Both v1 and v2 are within [0.0 .. 255.0] (0000-FF00) range, never going into\n      // [255.003 .. 255.996] (FF01-FFFF). This allows to round this value as (x+.5)|0\n      // later without overflowing.\n\n      v2 = v2 > 0xff00 ? 0xff00 : v2;\n      v2 = v2 < 0x0000 ? 0x0000 : v2; // Avoid division by 0. V=0 means rgb(0,0,0), unsharp with unsharpAmount>0 cannot\n      // change this value (because diff between colors gets inflated), so no need to verify correctness.\n\n      v1 = v1 !== 0 ? v1 : 1; // Multiplying V in HSV model by a constant is equivalent to multiplying each component\n      // in RGB by the same constant (same for HSL), see also:\n      // https://beesbuzz.biz/code/16-hsv-color-transforms\n\n      vmul = (v2 << 12) / v1 | 0; // Result will be in [0..255] range because:\n      //  - all numbers are positive\n      //  - r,g,b <= (v1/256)\n      //  - r,g,b,(v1/256),(v2/256) <= 255\n      // So highest this number can get is X*255/X+0.5=255.5 which is < 256 and rounds down.\n\n      iTimes4 = i * 4;\n      img[iTimes4] = img[iTimes4] * vmul + 0x800 >> 12; // R\n\n      img[iTimes4 + 1] = img[iTimes4 + 1] * vmul + 0x800 >> 12; // G\n\n      img[iTimes4 + 2] = img[iTimes4 + 2] * vmul + 0x800 >> 12; // B\n    }\n  }\n};\n\n},{\"glur/mono16\":18}],11:[function(_dereq_,module,exports){\n\nmodule.exports = function unsharp(img, width, height, amount, radius, threshold) {\n  if (amount === 0 || radius < 0.5) {\n    return;\n  }\n\n  if (radius > 2.0) {\n    radius = 2.0;\n  }\n\n  var pixels = width * height;\n  var img_bytes_cnt = pixels * 4;\n  var hsv_bytes_cnt = pixels * 2;\n  var blur_bytes_cnt = pixels * 2;\n  var blur_line_byte_cnt = Math.max(width, height) * 4; // float32 array\n\n  var blur_coeffs_byte_cnt = 8 * 4; // float32 array\n\n  var img_offset = 0;\n  var hsv_offset = img_bytes_cnt;\n  var blur_offset = hsv_offset + hsv_bytes_cnt;\n  var blur_tmp_offset = blur_offset + blur_bytes_cnt;\n  var blur_line_offset = blur_tmp_offset + blur_bytes_cnt;\n  var blur_coeffs_offset = blur_line_offset + blur_line_byte_cnt;\n\n  var instance = this.__instance('unsharp_mask', img_bytes_cnt + hsv_bytes_cnt + blur_bytes_cnt * 2 + blur_line_byte_cnt + blur_coeffs_byte_cnt, {\n    exp: Math.exp\n  }); // 32-bit copy is much faster in chrome\n\n\n  var img32 = new Uint32Array(img.buffer);\n  var mem32 = new Uint32Array(this.__memory.buffer);\n  mem32.set(img32); // HSL\n\n  var fn = instance.exports.hsv_v16 || instance.exports._hsv_v16;\n  fn(img_offset, hsv_offset, width, height); // BLUR\n\n  fn = instance.exports.blurMono16 || instance.exports._blurMono16;\n  fn(hsv_offset, blur_offset, blur_tmp_offset, blur_line_offset, blur_coeffs_offset, width, height, radius); // UNSHARP\n\n  fn = instance.exports.unsharp || instance.exports._unsharp;\n  fn(img_offset, img_offset, hsv_offset, blur_offset, width, height, amount, threshold); // 32-bit copy is much faster in chrome\n\n  img32.set(new Uint32Array(this.__memory.buffer, 0, pixels));\n};\n\n},{}],12:[function(_dereq_,module,exports){\n/* eslint-disable max-len */\n\nmodule.exports = '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';\n\n},{}],13:[function(_dereq_,module,exports){\n\nvar GC_INTERVAL = 100;\n\nfunction Pool(create, idle) {\n  this.create = create;\n  this.available = [];\n  this.acquired = {};\n  this.lastId = 1;\n  this.timeoutId = 0;\n  this.idle = idle || 2000;\n}\n\nPool.prototype.acquire = function () {\n  var _this = this;\n\n  var resource;\n\n  if (this.available.length !== 0) {\n    resource = this.available.pop();\n  } else {\n    resource = this.create();\n    resource.id = this.lastId++;\n\n    resource.release = function () {\n      return _this.release(resource);\n    };\n  }\n\n  this.acquired[resource.id] = resource;\n  return resource;\n};\n\nPool.prototype.release = function (resource) {\n  var _this2 = this;\n\n  delete this.acquired[resource.id];\n  resource.lastUsed = Date.now();\n  this.available.push(resource);\n\n  if (this.timeoutId === 0) {\n    this.timeoutId = setTimeout(function () {\n      return _this2.gc();\n    }, GC_INTERVAL);\n  }\n};\n\nPool.prototype.gc = function () {\n  var _this3 = this;\n\n  var now = Date.now();\n  this.available = this.available.filter(function (resource) {\n    if (now - resource.lastUsed > _this3.idle) {\n      resource.destroy();\n      return false;\n    }\n\n    return true;\n  });\n\n  if (this.available.length !== 0) {\n    this.timeoutId = setTimeout(function () {\n      return _this3.gc();\n    }, GC_INTERVAL);\n  } else {\n    this.timeoutId = 0;\n  }\n};\n\nmodule.exports = Pool;\n\n},{}],14:[function(_dereq_,module,exports){\n// min size = 1 can consume large amount of memory\n\nvar MIN_INNER_TILE_SIZE = 2;\n\nmodule.exports = function createStages(fromWidth, fromHeight, toWidth, toHeight, srcTileSize, destTileBorder) {\n  var scaleX = toWidth / fromWidth;\n  var scaleY = toHeight / fromHeight; // derived from createRegions equation:\n  // innerTileWidth = pixelFloor(srcTileSize * scaleX) - 2 * destTileBorder;\n\n  var minScale = (2 * destTileBorder + MIN_INNER_TILE_SIZE + 1) / srcTileSize; // refuse to scale image multiple times by less than twice each time,\n  // it could only happen because of invalid options\n\n  if (minScale > 0.5) return [[toWidth, toHeight]];\n  var stageCount = Math.ceil(Math.log(Math.min(scaleX, scaleY)) / Math.log(minScale)); // no additional resizes are necessary,\n  // stageCount can be zero or be negative when enlarging the image\n\n  if (stageCount <= 1) return [[toWidth, toHeight]];\n  var result = [];\n\n  for (var i = 0; i < stageCount; i++) {\n    var width = Math.round(Math.pow(Math.pow(fromWidth, stageCount - i - 1) * Math.pow(toWidth, i + 1), 1 / stageCount));\n    var height = Math.round(Math.pow(Math.pow(fromHeight, stageCount - i - 1) * Math.pow(toHeight, i + 1), 1 / stageCount));\n    result.push([width, height]);\n  }\n\n  return result;\n};\n\n},{}],15:[function(_dereq_,module,exports){\n/*\n * pixelFloor and pixelCeil are modified versions of Math.floor and Math.ceil\n * functions which take into account floating point arithmetic errors.\n * Those errors can cause undesired increments/decrements of sizes and offsets:\n * Math.ceil(36 / (36 / 500)) = 501\n * pixelCeil(36 / (36 / 500)) = 500\n */\n\nvar PIXEL_EPSILON = 1e-5;\n\nfunction pixelFloor(x) {\n  var nearest = Math.round(x);\n\n  if (Math.abs(x - nearest) < PIXEL_EPSILON) {\n    return nearest;\n  }\n\n  return Math.floor(x);\n}\n\nfunction pixelCeil(x) {\n  var nearest = Math.round(x);\n\n  if (Math.abs(x - nearest) < PIXEL_EPSILON) {\n    return nearest;\n  }\n\n  return Math.ceil(x);\n}\n\nmodule.exports = function createRegions(options) {\n  var scaleX = options.toWidth / options.width;\n  var scaleY = options.toHeight / options.height;\n  var innerTileWidth = pixelFloor(options.srcTileSize * scaleX) - 2 * options.destTileBorder;\n  var innerTileHeight = pixelFloor(options.srcTileSize * scaleY) - 2 * options.destTileBorder; // prevent infinite loop, this should never happen\n\n  if (innerTileWidth < 1 || innerTileHeight < 1) {\n    throw new Error('Internal error in pica: target tile width/height is too small.');\n  }\n\n  var x, y;\n  var innerX, innerY, toTileWidth, toTileHeight;\n  var tiles = [];\n  var tile; // we go top-to-down instead of left-to-right to make image displayed from top to\n  // doesn in the browser\n\n  for (innerY = 0; innerY < options.toHeight; innerY += innerTileHeight) {\n    for (innerX = 0; innerX < options.toWidth; innerX += innerTileWidth) {\n      x = innerX - options.destTileBorder;\n\n      if (x < 0) {\n        x = 0;\n      }\n\n      toTileWidth = innerX + innerTileWidth + options.destTileBorder - x;\n\n      if (x + toTileWidth >= options.toWidth) {\n        toTileWidth = options.toWidth - x;\n      }\n\n      y = innerY - options.destTileBorder;\n\n      if (y < 0) {\n        y = 0;\n      }\n\n      toTileHeight = innerY + innerTileHeight + options.destTileBorder - y;\n\n      if (y + toTileHeight >= options.toHeight) {\n        toTileHeight = options.toHeight - y;\n      }\n\n      tile = {\n        toX: x,\n        toY: y,\n        toWidth: toTileWidth,\n        toHeight: toTileHeight,\n        toInnerX: innerX,\n        toInnerY: innerY,\n        toInnerWidth: innerTileWidth,\n        toInnerHeight: innerTileHeight,\n        offsetX: x / scaleX - pixelFloor(x / scaleX),\n        offsetY: y / scaleY - pixelFloor(y / scaleY),\n        scaleX: scaleX,\n        scaleY: scaleY,\n        x: pixelFloor(x / scaleX),\n        y: pixelFloor(y / scaleY),\n        width: pixelCeil(toTileWidth / scaleX),\n        height: pixelCeil(toTileHeight / scaleY)\n      };\n      tiles.push(tile);\n    }\n  }\n\n  return tiles;\n};\n\n},{}],16:[function(_dereq_,module,exports){\n\nfunction objClass(obj) {\n  return Object.prototype.toString.call(obj);\n}\n\nmodule.exports.isCanvas = function isCanvas(element) {\n  var cname = objClass(element);\n  return cname === '[object HTMLCanvasElement]'\n  /* browser */\n  || cname === '[object OffscreenCanvas]' || cname === '[object Canvas]'\n  /* node-canvas */\n  ;\n};\n\nmodule.exports.isImage = function isImage(element) {\n  return objClass(element) === '[object HTMLImageElement]';\n};\n\nmodule.exports.isImageBitmap = function isImageBitmap(element) {\n  return objClass(element) === '[object ImageBitmap]';\n};\n\nmodule.exports.limiter = function limiter(concurrency) {\n  var active = 0,\n      queue = [];\n\n  function roll() {\n    if (active < concurrency && queue.length) {\n      active++;\n      queue.shift()();\n    }\n  }\n\n  return function limit(fn) {\n    return new Promise(function (resolve, reject) {\n      queue.push(function () {\n        fn().then(function (result) {\n          resolve(result);\n          active--;\n          roll();\n        }, function (err) {\n          reject(err);\n          active--;\n          roll();\n        });\n      });\n      roll();\n    });\n  };\n};\n\nmodule.exports.cib_quality_name = function cib_quality_name(num) {\n  switch (num) {\n    case 0:\n      return 'pixelated';\n\n    case 1:\n      return 'low';\n\n    case 2:\n      return 'medium';\n  }\n\n  return 'high';\n};\n\nmodule.exports.cib_support = function cib_support(createCanvas) {\n  return Promise.resolve().then(function () {\n    if (typeof createImageBitmap === 'undefined') {\n      return false;\n    }\n\n    var c = createCanvas(100, 100);\n    return createImageBitmap(c, 0, 0, 100, 100, {\n      resizeWidth: 10,\n      resizeHeight: 10,\n      resizeQuality: 'high'\n    }).then(function (bitmap) {\n      var status = bitmap.width === 10; // Branch below is filtered on upper level. We do not call resize\n      // detection for basic ImageBitmap.\n      //\n      // https://developer.mozilla.org/en-US/docs/Web/API/ImageBitmap\n      // old Crome 51 has ImageBitmap without .close(). Then this code\n      // will throw and return 'false' as expected.\n      //\n\n      bitmap.close();\n      c = null;\n      return status;\n    });\n  })[\"catch\"](function () {\n    return false;\n  });\n};\n\nmodule.exports.worker_offscreen_canvas_support = function worker_offscreen_canvas_support() {\n  return new Promise(function (resolve, reject) {\n    if (typeof OffscreenCanvas === 'undefined') {\n      // if OffscreenCanvas is present, we assume browser supports Worker and built-in Promise as well\n      resolve(false);\n      return;\n    }\n\n    function workerPayload(self) {\n      if (typeof createImageBitmap === 'undefined') {\n        self.postMessage(false);\n        return;\n      }\n\n      Promise.resolve().then(function () {\n        var canvas = new OffscreenCanvas(10, 10); // test that 2d context can be used in worker\n\n        var ctx = canvas.getContext('2d');\n        ctx.rect(0, 0, 1, 1); // test that cib can be used to return image bitmap from worker\n\n        return createImageBitmap(canvas, 0, 0, 1, 1);\n      }).then(function () {\n        return self.postMessage(true);\n      }, function () {\n        return self.postMessage(false);\n      });\n    }\n\n    var code = btoa(\"(\".concat(workerPayload.toString(), \")(self);\"));\n    var w = new Worker(\"data:text/javascript;base64,\".concat(code));\n\n    w.onmessage = function (ev) {\n      return resolve(ev.data);\n    };\n\n    w.onerror = reject;\n  }).then(function (result) {\n    return result;\n  }, function () {\n    return false;\n  });\n}; // Check if canvas.getContext('2d').getImageData can be used,\n// FireFox randomizes the output of that function in `privacy.resistFingerprinting` mode\n\n\nmodule.exports.can_use_canvas = function can_use_canvas(createCanvas) {\n  var usable = false;\n\n  try {\n    var canvas = createCanvas(2, 1);\n    var ctx = canvas.getContext('2d');\n    var d = ctx.createImageData(2, 1);\n    d.data[0] = 12;\n    d.data[1] = 23;\n    d.data[2] = 34;\n    d.data[3] = 255;\n    d.data[4] = 45;\n    d.data[5] = 56;\n    d.data[6] = 67;\n    d.data[7] = 255;\n    ctx.putImageData(d, 0, 0);\n    d = null;\n    d = ctx.getImageData(0, 0, 2, 1);\n\n    if (d.data[0] === 12 && d.data[1] === 23 && d.data[2] === 34 && d.data[3] === 255 && d.data[4] === 45 && d.data[5] === 56 && d.data[6] === 67 && d.data[7] === 255) {\n      usable = true;\n    }\n  } catch (err) {}\n\n  return usable;\n}; // Check if createImageBitmap(img, sx, sy, sw, sh) signature works correctly\n// with JPEG images oriented with Exif;\n// https://bugs.chromium.org/p/chromium/issues/detail?id=1220671\n// TODO: remove after it's fixed in chrome for at least 2 releases\n\n\nmodule.exports.cib_can_use_region = function cib_can_use_region() {\n  return new Promise(function (resolve) {\n    if (typeof createImageBitmap === 'undefined') {\n      resolve(false);\n      return;\n    }\n\n    var image = new Image();\n    image.src = 'data:image/jpeg;base64,' + '/9j/4QBiRXhpZgAATU0AKgAAAAgABQESAAMAAAABAAYAAAEaAAUAAAABAAAASgEbAAUAA' + 'AABAAAAUgEoAAMAAAABAAIAAAITAAMAAAABAAEAAAAAAAAAAABIAAAAAQAAAEgAAAAB/9' + 'sAQwAEAwMEAwMEBAMEBQQEBQYKBwYGBgYNCQoICg8NEBAPDQ8OERMYFBESFxIODxUcFRc' + 'ZGRsbGxAUHR8dGh8YGhsa/9sAQwEEBQUGBQYMBwcMGhEPERoaGhoaGhoaGhoaGhoaGhoa' + 'GhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoa/8IAEQgAAQACAwERAAIRAQMRA' + 'f/EABQAAQAAAAAAAAAAAAAAAAAAAAf/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAA' + 'IQAxAAAAF/P//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAQUCf//EABQRAQAAAAA' + 'AAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIB' + 'AT8Bf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEABj8Cf//EABQQAQAAAAAAAAAAA' + 'AAAAAAAAAD/2gAIAQEAAT8hf//aAAwDAQACAAMAAAAQH//EABQRAQAAAAAAAAAAAAAAAA' + 'AAAAD/2gAIAQMBAT8Qf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Qf//EABQ' + 'QAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAT8Qf//Z';\n\n    image.onload = function () {\n      createImageBitmap(image, 0, 0, image.width, image.height).then(function (bitmap) {\n        if (bitmap.width === image.width && bitmap.height === image.height) {\n          resolve(true);\n        } else {\n          resolve(false);\n        }\n      }, function () {\n        return resolve(false);\n      });\n    };\n\n    image.onerror = function () {\n      return resolve(false);\n    };\n  });\n};\n\n},{}],17:[function(_dereq_,module,exports){\n\nmodule.exports = function () {\n  var MathLib = _dereq_('./mathlib');\n\n  var mathLib;\n  /* eslint-disable no-undef */\n\n  onmessage = function onmessage(ev) {\n    var tileOpts = ev.data.opts;\n    var returnBitmap = false;\n\n    if (!tileOpts.src && tileOpts.srcBitmap) {\n      var canvas = new OffscreenCanvas(tileOpts.width, tileOpts.height);\n      var ctx = canvas.getContext('2d', {\n        alpha: Boolean(tileOpts.alpha)\n      });\n      ctx.drawImage(tileOpts.srcBitmap, 0, 0);\n      tileOpts.src = ctx.getImageData(0, 0, tileOpts.width, tileOpts.height).data;\n      canvas.width = canvas.height = 0;\n      canvas = null;\n      tileOpts.srcBitmap.close();\n      tileOpts.srcBitmap = null;\n      returnBitmap = true;\n    }\n\n    if (!mathLib) mathLib = new MathLib(ev.data.features); // Use multimath's sync auto-init. Avoid Promise use in old browsers,\n    // because polyfills are not propagated to webworker.\n\n    var data = mathLib.resizeAndUnsharp(tileOpts);\n\n    if (returnBitmap) {\n      var toImageData = new ImageData(new Uint8ClampedArray(data), tileOpts.toWidth, tileOpts.toHeight);\n\n      var _canvas = new OffscreenCanvas(tileOpts.toWidth, tileOpts.toHeight);\n\n      var _ctx = _canvas.getContext('2d', {\n        alpha: Boolean(tileOpts.alpha)\n      });\n\n      _ctx.putImageData(toImageData, 0, 0);\n\n      createImageBitmap(_canvas).then(function (bitmap) {\n        postMessage({\n          bitmap: bitmap\n        }, [bitmap]);\n      });\n    } else {\n      postMessage({\n        data: data\n      }, [data.buffer]);\n    }\n  };\n};\n\n},{\"./mathlib\":1}],18:[function(_dereq_,module,exports){\n// Calculate Gaussian blur of an image using IIR filter\n// The method is taken from Intel's white paper and code example attached to it:\n// https://software.intel.com/en-us/articles/iir-gaussian-blur-filter\n// -implementation-using-intel-advanced-vector-extensions\n\nvar a0, a1, a2, a3, b1, b2, left_corner, right_corner;\n\nfunction gaussCoef(sigma) {\n  if (sigma < 0.5) {\n    sigma = 0.5;\n  }\n\n  var a = Math.exp(0.726 * 0.726) / sigma,\n      g1 = Math.exp(-a),\n      g2 = Math.exp(-2 * a),\n      k = (1 - g1) * (1 - g1) / (1 + 2 * a * g1 - g2);\n\n  a0 = k;\n  a1 = k * (a - 1) * g1;\n  a2 = k * (a + 1) * g1;\n  a3 = -k * g2;\n  b1 = 2 * g1;\n  b2 = -g2;\n  left_corner = (a0 + a1) / (1 - b1 - b2);\n  right_corner = (a2 + a3) / (1 - b1 - b2);\n\n  // Attempt to force type to FP32.\n  return new Float32Array([ a0, a1, a2, a3, b1, b2, left_corner, right_corner ]);\n}\n\nfunction convolveMono16(src, out, line, coeff, width, height) {\n  // takes src image and writes the blurred and transposed result into out\n\n  var prev_src, curr_src, curr_out, prev_out, prev_prev_out;\n  var src_index, out_index, line_index;\n  var i, j;\n  var coeff_a0, coeff_a1, coeff_b1, coeff_b2;\n\n  for (i = 0; i < height; i++) {\n    src_index = i * width;\n    out_index = i;\n    line_index = 0;\n\n    // left to right\n    prev_src = src[src_index];\n    prev_prev_out = prev_src * coeff[6];\n    prev_out = prev_prev_out;\n\n    coeff_a0 = coeff[0];\n    coeff_a1 = coeff[1];\n    coeff_b1 = coeff[4];\n    coeff_b2 = coeff[5];\n\n    for (j = 0; j < width; j++) {\n      curr_src = src[src_index];\n\n      curr_out = curr_src * coeff_a0 +\n                 prev_src * coeff_a1 +\n                 prev_out * coeff_b1 +\n                 prev_prev_out * coeff_b2;\n\n      prev_prev_out = prev_out;\n      prev_out = curr_out;\n      prev_src = curr_src;\n\n      line[line_index] = prev_out;\n      line_index++;\n      src_index++;\n    }\n\n    src_index--;\n    line_index--;\n    out_index += height * (width - 1);\n\n    // right to left\n    prev_src = src[src_index];\n    prev_prev_out = prev_src * coeff[7];\n    prev_out = prev_prev_out;\n    curr_src = prev_src;\n\n    coeff_a0 = coeff[2];\n    coeff_a1 = coeff[3];\n\n    for (j = width - 1; j >= 0; j--) {\n      curr_out = curr_src * coeff_a0 +\n                 prev_src * coeff_a1 +\n                 prev_out * coeff_b1 +\n                 prev_prev_out * coeff_b2;\n\n      prev_prev_out = prev_out;\n      prev_out = curr_out;\n\n      prev_src = curr_src;\n      curr_src = src[src_index];\n\n      out[out_index] = line[line_index] + prev_out;\n\n      src_index--;\n      line_index--;\n      out_index -= height;\n    }\n  }\n}\n\n\nfunction blurMono16(src, width, height, radius) {\n  // Quick exit on zero radius\n  if (!radius) { return; }\n\n  var out      = new Uint16Array(src.length),\n      tmp_line = new Float32Array(Math.max(width, height));\n\n  var coeff = gaussCoef(radius);\n\n  convolveMono16(src, out, tmp_line, coeff, width, height);\n  convolveMono16(out, src, tmp_line, coeff, height, width);\n}\n\nmodule.exports = blurMono16;\n\n},{}],19:[function(_dereq_,module,exports){\nif (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      });\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor;\n      var TempCtor = function () {};\n      TempCtor.prototype = superCtor.prototype;\n      ctor.prototype = new TempCtor();\n      ctor.prototype.constructor = ctor;\n    }\n  };\n}\n\n},{}],20:[function(_dereq_,module,exports){\n\n\nvar assign         = _dereq_('object-assign');\nvar base64decode   = _dereq_('./lib/base64decode');\nvar hasWebAssembly = _dereq_('./lib/wa_detect');\n\n\nvar DEFAULT_OPTIONS = {\n  js: true,\n  wasm: true\n};\n\n\nfunction MultiMath(options) {\n  if (!(this instanceof MultiMath)) return new MultiMath(options);\n\n  var opts = assign({}, DEFAULT_OPTIONS, options || {});\n\n  this.options         = opts;\n\n  this.__cache         = {};\n\n  this.__init_promise  = null;\n  this.__modules       = opts.modules || {};\n  this.__memory        = null;\n  this.__wasm          = {};\n\n  this.__isLE = ((new Uint32Array((new Uint8Array([ 1, 0, 0, 0 ])).buffer))[0] === 1);\n\n  if (!this.options.js && !this.options.wasm) {\n    throw new Error('mathlib: at least \"js\" or \"wasm\" should be enabled');\n  }\n}\n\n\nMultiMath.prototype.has_wasm = hasWebAssembly;\n\n\nMultiMath.prototype.use = function (module) {\n  this.__modules[module.name] = module;\n\n  // Pin the best possible implementation\n  if (this.options.wasm && this.has_wasm() && module.wasm_fn) {\n    this[module.name] = module.wasm_fn;\n  } else {\n    this[module.name] = module.fn;\n  }\n\n  return this;\n};\n\n\nMultiMath.prototype.init = function () {\n  if (this.__init_promise) return this.__init_promise;\n\n  if (!this.options.js && this.options.wasm && !this.has_wasm()) {\n    return Promise.reject(new Error('mathlib: only \"wasm\" was enabled, but it\\'s not supported'));\n  }\n\n  var self = this;\n\n  this.__init_promise = Promise.all(Object.keys(self.__modules).map(function (name) {\n    var module = self.__modules[name];\n\n    if (!self.options.wasm || !self.has_wasm() || !module.wasm_fn) return null;\n\n    // If already compiled - exit\n    if (self.__wasm[name]) return null;\n\n    // Compile wasm source\n    return WebAssembly.compile(self.__base64decode(module.wasm_src))\n      .then(function (m) { self.__wasm[name] = m; });\n  }))\n    .then(function () { return self; });\n\n  return this.__init_promise;\n};\n\n\n////////////////////////////////////////////////////////////////////////////////\n// Methods below are for internal use from plugins\n\n\n// Simple decode base64 to typed array. Useful to load embedded webassembly\n// code. You probably don't need to call this method directly.\n//\nMultiMath.prototype.__base64decode = base64decode;\n\n\n// Increase current memory to include specified number of bytes. Do nothing if\n// size is already ok. You probably don't need to call this method directly,\n// because it will be invoked from `.__instance()`.\n//\nMultiMath.prototype.__reallocate = function mem_grow_to(bytes) {\n  if (!this.__memory) {\n    this.__memory = new WebAssembly.Memory({\n      initial: Math.ceil(bytes / (64 * 1024))\n    });\n    return this.__memory;\n  }\n\n  var mem_size = this.__memory.buffer.byteLength;\n\n  if (mem_size < bytes) {\n    this.__memory.grow(Math.ceil((bytes - mem_size) / (64 * 1024)));\n  }\n\n  return this.__memory;\n};\n\n\n// Returns instantinated webassembly item by name, with specified memory size\n// and environment.\n// - use cache if available\n// - do sync module init, if async init was not called earlier\n// - allocate memory if not enougth\n// - can export functions to webassembly via \"env_extra\",\n//   for example, { exp: Math.exp }\n//\nMultiMath.prototype.__instance = function instance(name, memsize, env_extra) {\n  if (memsize) this.__reallocate(memsize);\n\n  // If .init() was not called, do sync compile\n  if (!this.__wasm[name]) {\n    var module = this.__modules[name];\n    this.__wasm[name] = new WebAssembly.Module(this.__base64decode(module.wasm_src));\n  }\n\n  if (!this.__cache[name]) {\n    var env_base = {\n      memoryBase: 0,\n      memory: this.__memory,\n      tableBase: 0,\n      table: new WebAssembly.Table({ initial: 0, element: 'anyfunc' })\n    };\n\n    this.__cache[name] = new WebAssembly.Instance(this.__wasm[name], {\n      env: assign(env_base, env_extra || {})\n    });\n  }\n\n  return this.__cache[name];\n};\n\n\n// Helper to calculate memory aligh for pointers. Webassembly does not require\n// this, but you may wish to experiment. Default base = 8;\n//\nMultiMath.prototype.__align = function align(number, base) {\n  base = base || 8;\n  var reminder = number % base;\n  return number + (reminder ? base - reminder : 0);\n};\n\n\nmodule.exports = MultiMath;\n\n},{\"./lib/base64decode\":21,\"./lib/wa_detect\":22,\"object-assign\":23}],21:[function(_dereq_,module,exports){\n\n\nvar BASE64_MAP = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\n\nmodule.exports = function base64decode(str) {\n  var input = str.replace(/[\\r\\n=]/g, ''), // remove CR/LF & padding to simplify scan\n      max   = input.length;\n\n  var out = new Uint8Array((max * 3) >> 2);\n\n  // Collect by 6*4 bits (3 bytes)\n\n  var bits = 0;\n  var ptr  = 0;\n\n  for (var idx = 0; idx < max; idx++) {\n    if ((idx % 4 === 0) && idx) {\n      out[ptr++] = (bits >> 16) & 0xFF;\n      out[ptr++] = (bits >> 8) & 0xFF;\n      out[ptr++] = bits & 0xFF;\n    }\n\n    bits = (bits << 6) | BASE64_MAP.indexOf(input.charAt(idx));\n  }\n\n  // Dump tail\n\n  var tailbits = (max % 4) * 6;\n\n  if (tailbits === 0) {\n    out[ptr++] = (bits >> 16) & 0xFF;\n    out[ptr++] = (bits >> 8) & 0xFF;\n    out[ptr++] = bits & 0xFF;\n  } else if (tailbits === 18) {\n    out[ptr++] = (bits >> 10) & 0xFF;\n    out[ptr++] = (bits >> 2) & 0xFF;\n  } else if (tailbits === 12) {\n    out[ptr++] = (bits >> 4) & 0xFF;\n  }\n\n  return out;\n};\n\n},{}],22:[function(_dereq_,module,exports){\n\n\nvar wa;\n\n\nmodule.exports = function hasWebAssembly() {\n  // use cache if called before;\n  if (typeof wa !== 'undefined') return wa;\n\n  wa = false;\n\n  if (typeof WebAssembly === 'undefined') return wa;\n\n  // If WebAssenbly is disabled, code can throw on compile\n  try {\n    // https://github.com/brion/min-wasm-fail/blob/master/min-wasm-fail.in.js\n    // Additional check that WA internals are correct\n\n    /* eslint-disable comma-spacing, max-len */\n    var bin      = new Uint8Array([ 0,97,115,109,1,0,0,0,1,6,1,96,1,127,1,127,3,2,1,0,5,3,1,0,1,7,8,1,4,116,101,115,116,0,0,10,16,1,14,0,32,0,65,1,54,2,0,32,0,40,2,0,11 ]);\n    var module   = new WebAssembly.Module(bin);\n    var instance = new WebAssembly.Instance(module, {});\n\n    // test storing to and loading from a non-zero location via a parameter.\n    // Safari on iOS 11.2.5 returns 0 unexpectedly at non-zero locations\n    if (instance.exports.test(4) !== 0) wa = true;\n\n    return wa;\n  } catch (__) {}\n\n  return wa;\n};\n\n},{}],23:[function(_dereq_,module,exports){\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n\n},{}],24:[function(_dereq_,module,exports){\nvar bundleFn = arguments[3];\nvar sources = arguments[4];\nvar cache = arguments[5];\n\nvar stringify = JSON.stringify;\n\nmodule.exports = function (fn, options) {\n    var wkey;\n    var cacheKeys = Object.keys(cache);\n\n    for (var i = 0, l = cacheKeys.length; i < l; i++) {\n        var key = cacheKeys[i];\n        var exp = cache[key].exports;\n        // Using babel as a transpiler to use esmodule, the export will always\n        // be an object with the default export as a property of it. To ensure\n        // the existing api and babel esmodule exports are both supported we\n        // check for both\n        if (exp === fn || exp && exp.default === fn) {\n            wkey = key;\n            break;\n        }\n    }\n\n    if (!wkey) {\n        wkey = Math.floor(Math.pow(16, 8) * Math.random()).toString(16);\n        var wcache = {};\n        for (var i = 0, l = cacheKeys.length; i < l; i++) {\n            var key = cacheKeys[i];\n            wcache[key] = key;\n        }\n        sources[wkey] = [\n            'function(require,module,exports){' + fn + '(self); }',\n            wcache\n        ];\n    }\n    var skey = Math.floor(Math.pow(16, 8) * Math.random()).toString(16);\n\n    var scache = {}; scache[wkey] = wkey;\n    sources[skey] = [\n        'function(require,module,exports){' +\n            // try to call default if defined to also support babel esmodule exports\n            'var f = require(' + stringify(wkey) + ');' +\n            '(f.default ? f.default : f)(self);' +\n        '}',\n        scache\n    ];\n\n    var workerSources = {};\n    resolveSources(skey);\n\n    function resolveSources(key) {\n        workerSources[key] = true;\n\n        for (var depPath in sources[key][1]) {\n            var depKey = sources[key][1][depPath];\n            if (!workerSources[depKey]) {\n                resolveSources(depKey);\n            }\n        }\n    }\n\n    var src = '(' + bundleFn + ')({'\n        + Object.keys(workerSources).map(function (key) {\n            return stringify(key) + ':['\n                + sources[key][0]\n                + ',' + stringify(sources[key][1]) + ']'\n            ;\n        }).join(',')\n        + '},{},[' + stringify(skey) + '])'\n    ;\n\n    var URL = window.URL || window.webkitURL || window.mozURL || window.msURL;\n\n    var blob = new Blob([src], { type: 'text/javascript' });\n    if (options && options.bare) { return blob; }\n    var workerUrl = URL.createObjectURL(blob);\n    var worker = new Worker(workerUrl);\n    worker.objectURL = workerUrl;\n    return worker;\n};\n\n},{}],\"/index.js\":[function(_dereq_,module,exports){\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nvar assign = _dereq_('object-assign');\n\nvar webworkify = _dereq_('webworkify');\n\nvar MathLib = _dereq_('./lib/mathlib');\n\nvar Pool = _dereq_('./lib/pool');\n\nvar utils = _dereq_('./lib/utils');\n\nvar worker = _dereq_('./lib/worker');\n\nvar createStages = _dereq_('./lib/stepper');\n\nvar createRegions = _dereq_('./lib/tiler'); // Deduplicate pools & limiters with the same configs\n// when user creates multiple pica instances.\n\n\nvar singletones = {};\nvar NEED_SAFARI_FIX = false;\n\ntry {\n  if (typeof navigator !== 'undefined' && navigator.userAgent) {\n    NEED_SAFARI_FIX = navigator.userAgent.indexOf('Safari') >= 0;\n  }\n} catch (e) {}\n\nvar concurrency = 1;\n\nif (typeof navigator !== 'undefined') {\n  concurrency = Math.min(navigator.hardwareConcurrency || 1, 4);\n}\n\nvar DEFAULT_PICA_OPTS = {\n  tile: 1024,\n  concurrency: concurrency,\n  features: ['js', 'wasm', 'ww'],\n  idle: 2000,\n  createCanvas: function createCanvas(width, height) {\n    var tmpCanvas = document.createElement('canvas');\n    tmpCanvas.width = width;\n    tmpCanvas.height = height;\n    return tmpCanvas;\n  }\n};\nvar DEFAULT_RESIZE_OPTS = {\n  quality: 3,\n  alpha: false,\n  unsharpAmount: 0,\n  unsharpRadius: 0.0,\n  unsharpThreshold: 0\n};\nvar CAN_NEW_IMAGE_DATA = false;\nvar CAN_CREATE_IMAGE_BITMAP = false;\nvar CAN_USE_CANVAS_GET_IMAGE_DATA = false;\nvar CAN_USE_OFFSCREEN_CANVAS = false;\nvar CAN_USE_CIB_REGION_FOR_IMAGE = false;\n\nfunction workerFabric() {\n  return {\n    value: webworkify(worker),\n    destroy: function destroy() {\n      this.value.terminate();\n\n      if (typeof window !== 'undefined') {\n        var url = window.URL || window.webkitURL || window.mozURL || window.msURL;\n\n        if (url && url.revokeObjectURL && this.value.objectURL) {\n          url.revokeObjectURL(this.value.objectURL);\n        }\n      }\n    }\n  };\n} ////////////////////////////////////////////////////////////////////////////////\n// API methods\n\n\nfunction Pica(options) {\n  if (!(this instanceof Pica)) return new Pica(options);\n  this.options = assign({}, DEFAULT_PICA_OPTS, options || {});\n  var limiter_key = \"lk_\".concat(this.options.concurrency); // Share limiters to avoid multiple parallel workers when user creates\n  // multiple pica instances.\n\n  this.__limit = singletones[limiter_key] || utils.limiter(this.options.concurrency);\n  if (!singletones[limiter_key]) singletones[limiter_key] = this.__limit; // List of supported features, according to options & browser/node.js\n\n  this.features = {\n    js: false,\n    // pure JS implementation, can be disabled for testing\n    wasm: false,\n    // webassembly implementation for heavy functions\n    cib: false,\n    // resize via createImageBitmap (only FF at this moment)\n    ww: false // webworkers\n\n  };\n  this.__workersPool = null; // Store requested features for webworkers\n\n  this.__requested_features = [];\n  this.__mathlib = null;\n}\n\nPica.prototype.init = function () {\n  var _this = this;\n\n  if (this.__initPromise) return this.__initPromise; // Test if we can create ImageData without canvas and memory copy\n\n  if (typeof ImageData !== 'undefined' && typeof Uint8ClampedArray !== 'undefined') {\n    try {\n      /* eslint-disable no-new */\n      new ImageData(new Uint8ClampedArray(400), 10, 10);\n      CAN_NEW_IMAGE_DATA = true;\n    } catch (__) {}\n  } // ImageBitmap can be effective in 2 places:\n  //\n  // 1. Threaded jpeg unpack (basic)\n  // 2. Built-in resize (blocked due problem in chrome, see issue #89)\n  //\n  // For basic use we also need ImageBitmap wo support .close() method,\n  // see https://developer.mozilla.org/ru/docs/Web/API/ImageBitmap\n\n\n  if (typeof ImageBitmap !== 'undefined') {\n    if (ImageBitmap.prototype && ImageBitmap.prototype.close) {\n      CAN_CREATE_IMAGE_BITMAP = true;\n    } else {\n      this.debug('ImageBitmap does not support .close(), disabled');\n    }\n  }\n\n  var features = this.options.features.slice();\n\n  if (features.indexOf('all') >= 0) {\n    features = ['cib', 'wasm', 'js', 'ww'];\n  }\n\n  this.__requested_features = features;\n  this.__mathlib = new MathLib(features); // Check WebWorker support if requested\n\n  if (features.indexOf('ww') >= 0) {\n    if (typeof window !== 'undefined' && 'Worker' in window) {\n      // IE <= 11 don't allow to create webworkers from string. We should check it.\n      // https://connect.microsoft.com/IE/feedback/details/801810/web-workers-from-blob-urls-in-ie-10-and-11\n      try {\n        var wkr = _dereq_('webworkify')(function () {});\n\n        wkr.terminate();\n        this.features.ww = true; // pool uniqueness depends on pool config + webworker config\n\n        var wpool_key = \"wp_\".concat(JSON.stringify(this.options));\n\n        if (singletones[wpool_key]) {\n          this.__workersPool = singletones[wpool_key];\n        } else {\n          this.__workersPool = new Pool(workerFabric, this.options.idle);\n          singletones[wpool_key] = this.__workersPool;\n        }\n      } catch (__) {}\n    }\n  }\n\n  var initMath = this.__mathlib.init().then(function (mathlib) {\n    // Copy detected features\n    assign(_this.features, mathlib.features);\n  });\n\n  var checkCibResize;\n\n  if (!CAN_CREATE_IMAGE_BITMAP) {\n    checkCibResize = Promise.resolve(false);\n  } else {\n    checkCibResize = utils.cib_support(this.options.createCanvas).then(function (status) {\n      if (_this.features.cib && features.indexOf('cib') < 0) {\n        _this.debug('createImageBitmap() resize supported, but disabled by config');\n\n        return;\n      }\n\n      if (features.indexOf('cib') >= 0) _this.features.cib = status;\n    });\n  }\n\n  CAN_USE_CANVAS_GET_IMAGE_DATA = utils.can_use_canvas(this.options.createCanvas);\n  var checkOffscreenCanvas;\n\n  if (CAN_CREATE_IMAGE_BITMAP && CAN_NEW_IMAGE_DATA && features.indexOf('ww') !== -1) {\n    checkOffscreenCanvas = utils.worker_offscreen_canvas_support();\n  } else {\n    checkOffscreenCanvas = Promise.resolve(false);\n  }\n\n  checkOffscreenCanvas = checkOffscreenCanvas.then(function (result) {\n    CAN_USE_OFFSCREEN_CANVAS = result;\n  }); // we use createImageBitmap to crop image data and pass it to workers,\n  // so need to check whether function works correctly;\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=1220671\n\n  var checkCibRegion = utils.cib_can_use_region().then(function (result) {\n    CAN_USE_CIB_REGION_FOR_IMAGE = result;\n  }); // Init math lib. That's async because can load some\n\n  this.__initPromise = Promise.all([initMath, checkCibResize, checkOffscreenCanvas, checkCibRegion]).then(function () {\n    return _this;\n  });\n  return this.__initPromise;\n}; // Call resizer in webworker or locally, depending on config\n\n\nPica.prototype.__invokeResize = function (tileOpts, opts) {\n  var _this2 = this;\n\n  // Share cache between calls:\n  //\n  // - wasm instance\n  // - wasm memory object\n  //\n  opts.__mathCache = opts.__mathCache || {};\n  return Promise.resolve().then(function () {\n    if (!_this2.features.ww) {\n      // not possible to have ImageBitmap here if user disabled WW\n      return {\n        data: _this2.__mathlib.resizeAndUnsharp(tileOpts, opts.__mathCache)\n      };\n    }\n\n    return new Promise(function (resolve, reject) {\n      var w = _this2.__workersPool.acquire();\n\n      if (opts.cancelToken) opts.cancelToken[\"catch\"](function (err) {\n        return reject(err);\n      });\n\n      w.value.onmessage = function (ev) {\n        w.release();\n        if (ev.data.err) reject(ev.data.err);else resolve(ev.data);\n      };\n\n      var transfer = [];\n      if (tileOpts.src) transfer.push(tileOpts.src.buffer);\n      if (tileOpts.srcBitmap) transfer.push(tileOpts.srcBitmap);\n      w.value.postMessage({\n        opts: tileOpts,\n        features: _this2.__requested_features,\n        preload: {\n          wasm_nodule: _this2.__mathlib.__\n        }\n      }, transfer);\n    });\n  });\n}; // this function can return promise if createImageBitmap is used\n\n\nPica.prototype.__extractTileData = function (tile, from, opts, stageEnv, extractTo) {\n  if (this.features.ww && CAN_USE_OFFSCREEN_CANVAS && ( // createImageBitmap doesn't work for images (Image, ImageBitmap) with Exif orientation in Chrome,\n  // can use canvas because canvas doesn't have orientation;\n  // see https://bugs.chromium.org/p/chromium/issues/detail?id=1220671\n  utils.isCanvas(from) || CAN_USE_CIB_REGION_FOR_IMAGE)) {\n    this.debug('Create tile for OffscreenCanvas');\n    return createImageBitmap(stageEnv.srcImageBitmap || from, tile.x, tile.y, tile.width, tile.height).then(function (bitmap) {\n      extractTo.srcBitmap = bitmap;\n      return extractTo;\n    });\n  } // Extract tile RGBA buffer, depending on input type\n\n\n  if (utils.isCanvas(from)) {\n    if (!stageEnv.srcCtx) stageEnv.srcCtx = from.getContext('2d', {\n      alpha: Boolean(opts.alpha)\n    }); // If input is Canvas - extract region data directly\n\n    this.debug('Get tile pixel data');\n    extractTo.src = stageEnv.srcCtx.getImageData(tile.x, tile.y, tile.width, tile.height).data;\n    return extractTo;\n  } // If input is Image or decoded to ImageBitmap,\n  // draw region to temporary canvas and extract data from it\n  //\n  // Note! Attempt to reuse this canvas causes significant slowdown in chrome\n  //\n\n\n  this.debug('Draw tile imageBitmap/image to temporary canvas');\n  var tmpCanvas = this.options.createCanvas(tile.width, tile.height);\n  var tmpCtx = tmpCanvas.getContext('2d', {\n    alpha: Boolean(opts.alpha)\n  });\n  tmpCtx.globalCompositeOperation = 'copy';\n  tmpCtx.drawImage(stageEnv.srcImageBitmap || from, tile.x, tile.y, tile.width, tile.height, 0, 0, tile.width, tile.height);\n  this.debug('Get tile pixel data');\n  extractTo.src = tmpCtx.getImageData(0, 0, tile.width, tile.height).data; // Safari 12 workaround\n  // https://github.com/nodeca/pica/issues/199\n\n  tmpCanvas.width = tmpCanvas.height = 0;\n  return extractTo;\n};\n\nPica.prototype.__landTileData = function (tile, result, stageEnv) {\n  var toImageData;\n  this.debug('Convert raw rgba tile result to ImageData');\n\n  if (result.bitmap) {\n    stageEnv.toCtx.drawImage(result.bitmap, tile.toX, tile.toY);\n    return null;\n  }\n\n  if (CAN_NEW_IMAGE_DATA) {\n    // this branch is for modern browsers\n    // If `new ImageData()` & Uint8ClampedArray suported\n    toImageData = new ImageData(new Uint8ClampedArray(result.data), tile.toWidth, tile.toHeight);\n  } else {\n    // fallback for `node-canvas` and old browsers\n    // (IE11 has ImageData but does not support `new ImageData()`)\n    toImageData = stageEnv.toCtx.createImageData(tile.toWidth, tile.toHeight);\n\n    if (toImageData.data.set) {\n      toImageData.data.set(result.data);\n    } else {\n      // IE9 don't have `.set()`\n      for (var i = toImageData.data.length - 1; i >= 0; i--) {\n        toImageData.data[i] = result.data[i];\n      }\n    }\n  }\n\n  this.debug('Draw tile');\n\n  if (NEED_SAFARI_FIX) {\n    // Safari draws thin white stripes between tiles without this fix\n    stageEnv.toCtx.putImageData(toImageData, tile.toX, tile.toY, tile.toInnerX - tile.toX, tile.toInnerY - tile.toY, tile.toInnerWidth + 1e-5, tile.toInnerHeight + 1e-5);\n  } else {\n    stageEnv.toCtx.putImageData(toImageData, tile.toX, tile.toY, tile.toInnerX - tile.toX, tile.toInnerY - tile.toY, tile.toInnerWidth, tile.toInnerHeight);\n  }\n\n  return null;\n};\n\nPica.prototype.__tileAndResize = function (from, to, opts) {\n  var _this3 = this;\n\n  var stageEnv = {\n    srcCtx: null,\n    srcImageBitmap: null,\n    isImageBitmapReused: false,\n    toCtx: null\n  };\n\n  var processTile = function processTile(tile) {\n    return _this3.__limit(function () {\n      if (opts.canceled) return opts.cancelToken;\n      var tileOpts = {\n        width: tile.width,\n        height: tile.height,\n        toWidth: tile.toWidth,\n        toHeight: tile.toHeight,\n        scaleX: tile.scaleX,\n        scaleY: tile.scaleY,\n        offsetX: tile.offsetX,\n        offsetY: tile.offsetY,\n        quality: opts.quality,\n        alpha: opts.alpha,\n        unsharpAmount: opts.unsharpAmount,\n        unsharpRadius: opts.unsharpRadius,\n        unsharpThreshold: opts.unsharpThreshold\n      };\n\n      _this3.debug('Invoke resize math');\n\n      return Promise.resolve(tileOpts).then(function (tileOpts) {\n        return _this3.__extractTileData(tile, from, opts, stageEnv, tileOpts);\n      }).then(function (tileOpts) {\n        _this3.debug('Invoke resize math');\n\n        return _this3.__invokeResize(tileOpts, opts);\n      }).then(function (result) {\n        if (opts.canceled) return opts.cancelToken;\n        stageEnv.srcImageData = null;\n        return _this3.__landTileData(tile, result, stageEnv);\n      });\n    });\n  }; // Need to normalize data source first. It can be canvas or image.\n  // If image - try to decode in background if possible\n\n\n  return Promise.resolve().then(function () {\n    stageEnv.toCtx = to.getContext('2d', {\n      alpha: Boolean(opts.alpha)\n    });\n    if (utils.isCanvas(from)) return null;\n\n    if (utils.isImageBitmap(from)) {\n      stageEnv.srcImageBitmap = from;\n      stageEnv.isImageBitmapReused = true;\n      return null;\n    }\n\n    if (utils.isImage(from)) {\n      // try do decode image in background for faster next operations;\n      // if we're using offscreen canvas, cib is called per tile, so not needed here\n      if (!CAN_CREATE_IMAGE_BITMAP) return null;\n\n      _this3.debug('Decode image via createImageBitmap');\n\n      return createImageBitmap(from).then(function (imageBitmap) {\n        stageEnv.srcImageBitmap = imageBitmap;\n      }) // Suppress error to use fallback, if method fails\n      // https://github.com/nodeca/pica/issues/190\n\n      /* eslint-disable no-unused-vars */\n      [\"catch\"](function (e) {\n        return null;\n      });\n    }\n\n    throw new Error('Pica: \".from\" should be Image, Canvas or ImageBitmap');\n  }).then(function () {\n    if (opts.canceled) return opts.cancelToken;\n\n    _this3.debug('Calculate tiles'); //\n    // Here we are with \"normalized\" source,\n    // follow to tiling\n    //\n\n\n    var regions = createRegions({\n      width: opts.width,\n      height: opts.height,\n      srcTileSize: _this3.options.tile,\n      toWidth: opts.toWidth,\n      toHeight: opts.toHeight,\n      destTileBorder: opts.__destTileBorder\n    });\n    var jobs = regions.map(function (tile) {\n      return processTile(tile);\n    });\n\n    function cleanup(stageEnv) {\n      if (stageEnv.srcImageBitmap) {\n        if (!stageEnv.isImageBitmapReused) stageEnv.srcImageBitmap.close();\n        stageEnv.srcImageBitmap = null;\n      }\n    }\n\n    _this3.debug('Process tiles');\n\n    return Promise.all(jobs).then(function () {\n      _this3.debug('Finished!');\n\n      cleanup(stageEnv);\n      return to;\n    }, function (err) {\n      cleanup(stageEnv);\n      throw err;\n    });\n  });\n};\n\nPica.prototype.__processStages = function (stages, from, to, opts) {\n  var _this4 = this;\n\n  if (opts.canceled) return opts.cancelToken;\n\n  var _stages$shift = stages.shift(),\n      _stages$shift2 = _slicedToArray(_stages$shift, 2),\n      toWidth = _stages$shift2[0],\n      toHeight = _stages$shift2[1];\n\n  var isLastStage = stages.length === 0;\n  opts = assign({}, opts, {\n    toWidth: toWidth,\n    toHeight: toHeight,\n    // only use user-defined quality for the last stage,\n    // use simpler (Hamming) filter for the first stages where\n    // scale factor is large enough (more than 2-3)\n    quality: isLastStage ? opts.quality : Math.min(1, opts.quality)\n  });\n  var tmpCanvas;\n\n  if (!isLastStage) {\n    // create temporary canvas\n    tmpCanvas = this.options.createCanvas(toWidth, toHeight);\n  }\n\n  return this.__tileAndResize(from, isLastStage ? to : tmpCanvas, opts).then(function () {\n    if (isLastStage) return to;\n    opts.width = toWidth;\n    opts.height = toHeight;\n    return _this4.__processStages(stages, tmpCanvas, to, opts);\n  }).then(function (res) {\n    if (tmpCanvas) {\n      // Safari 12 workaround\n      // https://github.com/nodeca/pica/issues/199\n      tmpCanvas.width = tmpCanvas.height = 0;\n    }\n\n    return res;\n  });\n};\n\nPica.prototype.__resizeViaCreateImageBitmap = function (from, to, opts) {\n  var _this5 = this;\n\n  var toCtx = to.getContext('2d', {\n    alpha: Boolean(opts.alpha)\n  });\n  this.debug('Resize via createImageBitmap()');\n  return createImageBitmap(from, {\n    resizeWidth: opts.toWidth,\n    resizeHeight: opts.toHeight,\n    resizeQuality: utils.cib_quality_name(opts.quality)\n  }).then(function (imageBitmap) {\n    if (opts.canceled) return opts.cancelToken; // if no unsharp - draw directly to output canvas\n\n    if (!opts.unsharpAmount) {\n      toCtx.drawImage(imageBitmap, 0, 0);\n      imageBitmap.close();\n      toCtx = null;\n\n      _this5.debug('Finished!');\n\n      return to;\n    }\n\n    _this5.debug('Unsharp result');\n\n    var tmpCanvas = _this5.options.createCanvas(opts.toWidth, opts.toHeight);\n\n    var tmpCtx = tmpCanvas.getContext('2d', {\n      alpha: Boolean(opts.alpha)\n    });\n    tmpCtx.drawImage(imageBitmap, 0, 0);\n    imageBitmap.close();\n    var iData = tmpCtx.getImageData(0, 0, opts.toWidth, opts.toHeight);\n\n    _this5.__mathlib.unsharp_mask(iData.data, opts.toWidth, opts.toHeight, opts.unsharpAmount, opts.unsharpRadius, opts.unsharpThreshold);\n\n    toCtx.putImageData(iData, 0, 0); // Safari 12 workaround\n    // https://github.com/nodeca/pica/issues/199\n\n    tmpCanvas.width = tmpCanvas.height = 0;\n    iData = tmpCtx = tmpCanvas = toCtx = null;\n\n    _this5.debug('Finished!');\n\n    return to;\n  });\n};\n\nPica.prototype.resize = function (from, to, options) {\n  var _this6 = this;\n\n  this.debug('Start resize...');\n  var opts = assign({}, DEFAULT_RESIZE_OPTS);\n\n  if (!isNaN(options)) {\n    opts = assign(opts, {\n      quality: options\n    });\n  } else if (options) {\n    opts = assign(opts, options);\n  }\n\n  opts.toWidth = to.width;\n  opts.toHeight = to.height;\n  opts.width = from.naturalWidth || from.width;\n  opts.height = from.naturalHeight || from.height; // Prevent stepper from infinite loop\n\n  if (to.width === 0 || to.height === 0) {\n    return Promise.reject(new Error(\"Invalid output size: \".concat(to.width, \"x\").concat(to.height)));\n  }\n\n  if (opts.unsharpRadius > 2) opts.unsharpRadius = 2;\n  opts.canceled = false;\n\n  if (opts.cancelToken) {\n    // Wrap cancelToken to avoid successive resolve & set flag\n    opts.cancelToken = opts.cancelToken.then(function (data) {\n      opts.canceled = true;\n      throw data;\n    }, function (err) {\n      opts.canceled = true;\n      throw err;\n    });\n  }\n\n  var DEST_TILE_BORDER = 3; // Max possible filter window size\n\n  opts.__destTileBorder = Math.ceil(Math.max(DEST_TILE_BORDER, 2.5 * opts.unsharpRadius | 0));\n  return this.init().then(function () {\n    if (opts.canceled) return opts.cancelToken; // if createImageBitmap supports resize, just do it and return\n\n    if (_this6.features.cib) {\n      return _this6.__resizeViaCreateImageBitmap(from, to, opts);\n    }\n\n    if (!CAN_USE_CANVAS_GET_IMAGE_DATA) {\n      var err = new Error('Pica: cannot use getImageData on canvas, ' + \"make sure fingerprinting protection isn't enabled\");\n      err.code = 'ERR_GET_IMAGE_DATA';\n      throw err;\n    } //\n    // No easy way, let's resize manually via arrays\n    //\n\n\n    var stages = createStages(opts.width, opts.height, opts.toWidth, opts.toHeight, _this6.options.tile, opts.__destTileBorder);\n    return _this6.__processStages(stages, from, to, opts);\n  });\n}; // RGBA buffer resize\n//\n\n\nPica.prototype.resizeBuffer = function (options) {\n  var _this7 = this;\n\n  var opts = assign({}, DEFAULT_RESIZE_OPTS, options);\n  return this.init().then(function () {\n    return _this7.__mathlib.resizeAndUnsharp(opts);\n  });\n};\n\nPica.prototype.toBlob = function (canvas, mimeType, quality) {\n  mimeType = mimeType || 'image/png';\n  return new Promise(function (resolve) {\n    if (canvas.toBlob) {\n      canvas.toBlob(function (blob) {\n        return resolve(blob);\n      }, mimeType, quality);\n      return;\n    }\n\n    if (canvas.convertToBlob) {\n      resolve(canvas.convertToBlob({\n        type: mimeType,\n        quality: quality\n      }));\n      return;\n    } // Fallback for old browsers\n\n\n    var asString = atob(canvas.toDataURL(mimeType, quality).split(',')[1]);\n    var len = asString.length;\n    var asBuffer = new Uint8Array(len);\n\n    for (var i = 0; i < len; i++) {\n      asBuffer[i] = asString.charCodeAt(i);\n    }\n\n    resolve(new Blob([asBuffer], {\n      type: mimeType\n    }));\n  });\n};\n\nPica.prototype.debug = function () {};\n\nmodule.exports = Pica;\n\n},{\"./lib/mathlib\":1,\"./lib/pool\":13,\"./lib/stepper\":14,\"./lib/tiler\":15,\"./lib/utils\":16,\"./lib/worker\":17,\"object-assign\":23,\"webworkify\":24}]},{},[])(\"/index.js\")\n});\n});\n\nvar image_traverse = createCommonjsModule(function (module) {\n\n//////////////////////////////////////////////////////////////////////////\n// Helpers\n//\nfunction error(message, code) {\n  var err = new Error(message);\n  err.code = code;\n  return err;\n}\n\n\n// Convert number to 0xHH string\n//\nfunction to_hex(number) {\n  var n = number.toString(16).toUpperCase();\n  for (var i = 2 - n.length; i > 0; i--) n = '0' + n;\n  return '0x' + n;\n}\n\n\nfunction utf8_encode(str) {\n  try {\n    return unescape(encodeURIComponent(str));\n  } catch (_) {\n    return str;\n  }\n}\n\n\nfunction utf8_decode(str) {\n  try {\n    return decodeURIComponent(escape(str));\n  } catch (_) {\n    return str;\n  }\n}\n\n\n// Check if input is a Uint8Array\n//\nfunction is_uint8array(bin) {\n  return Object.prototype.toString.call(bin) === '[object Uint8Array]';\n}\n\n\n//////////////////////////////////////////////////////////////////////////\n// Exif parser\n//\n// Input:\n//  - jpeg_bin:   Uint8Array - jpeg file\n//  - exif_start: Number     - start of TIFF header (after Exif\\0\\0)\n//  - exif_end:   Number     - end of Exif segment\n//  - on_entry:   Number     - callback\n//\nfunction ExifParser(jpeg_bin, exif_start, exif_end) {\n  // Uint8Array, exif without signature (which isn't included in offsets)\n  this.input      = jpeg_bin.subarray(exif_start, exif_end);\n\n  // offset correction for `on_entry` callback\n  this.start      = exif_start;\n\n  // Check TIFF header (includes byte alignment and first IFD offset)\n  var sig = String.fromCharCode.apply(null, this.input.subarray(0, 4));\n\n  if (sig !== 'II\\x2A\\0' && sig !== 'MM\\0\\x2A') {\n    throw error('invalid TIFF signature', 'EBADDATA');\n  }\n\n  // true if motorola (big endian) byte alignment, false if intel\n  this.big_endian = sig[0] === 'M';\n}\n\n\nExifParser.prototype.each = function (on_entry) {\n  // allow premature exit\n  this.aborted = false;\n\n  var offset = this.read_uint32(4);\n\n  this.ifds_to_read = [ {\n    id:     0,\n    offset: offset\n  } ];\n\n  while (this.ifds_to_read.length > 0 && !this.aborted) {\n    var i = this.ifds_to_read.shift();\n    if (!i.offset) continue;\n    this.scan_ifd(i.id, i.offset, on_entry);\n  }\n};\n\n\nExifParser.prototype.filter = function (on_entry) {\n  var ifds = {};\n\n  // make sure IFD0 always exists\n  ifds.ifd0 = { id: 0, entries: [] };\n\n  this.each(function (entry) {\n    if (on_entry(entry) === false && !entry.is_subifd_link) return;\n    if (entry.is_subifd_link && entry.count !== 1 && entry.format !== 4) return; // filter out bogus links\n\n    if (!ifds['ifd' + entry.ifd]) {\n      ifds['ifd' + entry.ifd] = { id: entry.ifd, entries: [] };\n    }\n\n    ifds['ifd' + entry.ifd].entries.push(entry);\n  });\n\n  // thumbnails are not supported just yet, so delete all information related to it\n  delete ifds.ifd1;\n\n  // Calculate output size\n  var length = 8;\n  Object.keys(ifds).forEach(function (ifd_no) {\n    length += 2;\n\n    ifds[ifd_no].entries.forEach(function (entry) {\n      length += 12 + (entry.data_length > 4 ? Math.ceil(entry.data_length / 2) * 2 : 0);\n    });\n\n    length += 4;\n  });\n\n  this.output = new Uint8Array(length);\n  this.output[0] = this.output[1] = (this.big_endian ? 'M' : 'I').charCodeAt(0);\n  this.write_uint16(2, 0x2A);\n\n  var offset = 8;\n  var self = this;\n  this.write_uint32(4, offset);\n\n  Object.keys(ifds).forEach(function (ifd_no) {\n    ifds[ifd_no].written_offset = offset;\n\n    var ifd_start = offset;\n    var ifd_end   = ifd_start + 2 + ifds[ifd_no].entries.length * 12 + 4;\n    offset = ifd_end;\n\n    self.write_uint16(ifd_start, ifds[ifd_no].entries.length);\n\n    ifds[ifd_no].entries.sort(function (a, b) {\n      // IFD entries must be in order of increasing tag IDs\n      return a.tag - b.tag;\n    }).forEach(function (entry, idx) {\n      var entry_offset = ifd_start + 2 + idx * 12;\n\n      self.write_uint16(entry_offset, entry.tag);\n      self.write_uint16(entry_offset + 2, entry.format);\n      self.write_uint32(entry_offset + 4, entry.count);\n\n      if (entry.is_subifd_link) {\n        // filled in later\n        if (ifds['ifd' + entry.tag]) ifds['ifd' + entry.tag].link_offset = entry_offset + 8;\n      } else if (entry.data_length <= 4) {\n        self.output.set(\n          self.input.subarray(entry.data_offset - self.start, entry.data_offset - self.start + 4),\n          entry_offset + 8\n        );\n      } else {\n        self.write_uint32(entry_offset + 8, offset);\n        self.output.set(\n          self.input.subarray(entry.data_offset - self.start, entry.data_offset - self.start + entry.data_length),\n          offset\n        );\n        offset += Math.ceil(entry.data_length / 2) * 2;\n      }\n    });\n\n    var next_ifd = ifds['ifd' + (ifds[ifd_no].id + 1)];\n    if (next_ifd) next_ifd.link_offset = ifd_end - 4;\n  });\n\n  Object.keys(ifds).forEach(function (ifd_no) {\n    if (ifds[ifd_no].written_offset && ifds[ifd_no].link_offset) {\n      self.write_uint32(ifds[ifd_no].link_offset, ifds[ifd_no].written_offset);\n    }\n  });\n\n  if (this.output.length !== offset) throw error('internal error: incorrect buffer size allocated');\n\n  return this.output;\n};\n\n\nExifParser.prototype.read_uint16 = function (offset) {\n  var d = this.input;\n  if (offset + 2 > d.length) throw error('unexpected EOF', 'EBADDATA');\n\n  return this.big_endian ?\n    d[offset] * 0x100 + d[offset + 1] :\n    d[offset] + d[offset + 1] * 0x100;\n};\n\n\nExifParser.prototype.read_uint32 = function (offset) {\n  var d = this.input;\n  if (offset + 4 > d.length) throw error('unexpected EOF', 'EBADDATA');\n\n  return this.big_endian ?\n    d[offset] * 0x1000000 + d[offset + 1] * 0x10000 + d[offset + 2] * 0x100 + d[offset + 3] :\n    d[offset] + d[offset + 1] * 0x100 + d[offset + 2] * 0x10000 + d[offset + 3] * 0x1000000;\n};\n\n\nExifParser.prototype.write_uint16 = function (offset, value) {\n  var d = this.output;\n\n  if (this.big_endian) {\n    d[offset]     = (value >>> 8) & 0xFF;\n    d[offset + 1] = value & 0xFF;\n  } else {\n    d[offset]     = value & 0xFF;\n    d[offset + 1] = (value >>> 8) & 0xFF;\n  }\n};\n\n\nExifParser.prototype.write_uint32 = function (offset, value) {\n  var d = this.output;\n\n  if (this.big_endian) {\n    d[offset]     = (value >>> 24) & 0xFF;\n    d[offset + 1] = (value >>> 16) & 0xFF;\n    d[offset + 2] = (value >>> 8) & 0xFF;\n    d[offset + 3] = value & 0xFF;\n  } else {\n    d[offset]     = value & 0xFF;\n    d[offset + 1] = (value >>> 8) & 0xFF;\n    d[offset + 2] = (value >>> 16) & 0xFF;\n    d[offset + 3] = (value >>> 24) & 0xFF;\n  }\n};\n\n\nExifParser.prototype.is_subifd_link = function (ifd, tag) {\n  return (ifd === 0 && tag === 0x8769) || // SubIFD\n         (ifd === 0 && tag === 0x8825) || // GPS Info\n         (ifd === 0x8769 && tag === 0xA005); // Interop IFD\n};\n\n\n// Returns byte length of a single component of a given format\n//\nExifParser.prototype.exif_format_length = function (format) {\n  switch (format) {\n    case 1: // byte\n    case 2: // ascii\n    case 6: // sbyte\n    case 7: // undefined\n      return 1;\n\n    case 3: // short\n    case 8: // sshort\n      return 2;\n\n    case 4:  // long\n    case 9:  // slong\n    case 11: // float\n      return 4;\n\n    case 5:  // rational\n    case 10: // srational\n    case 12: // double\n      return 8;\n\n    default:\n      // unknown type\n      return 0;\n  }\n};\n\n\n// Reads Exif data\n//\nExifParser.prototype.exif_format_read = function (format, offset) {\n  var v;\n\n  switch (format) {\n    case 1: // byte\n    case 2: // ascii\n      v = this.input[offset];\n      return v;\n\n    case 6: // sbyte\n      v = this.input[offset];\n      return v | (v & 0x80) * 0x1fffffe;\n\n    case 3: // short\n      v = this.read_uint16(offset);\n      return v;\n\n    case 8: // sshort\n      v = this.read_uint16(offset);\n      return v | (v & 0x8000) * 0x1fffe;\n\n    case 4: // long\n      v = this.read_uint32(offset);\n      return v;\n\n    case 9: // slong\n      v = this.read_uint32(offset);\n      return v | 0;\n\n    case 5:  // rational\n    case 10: // srational\n    case 11: // float\n    case 12: // double\n      return null; // not implemented\n\n    case 7: // undefined\n      return null; // blob\n\n    default:\n      // unknown type\n      return null;\n  }\n};\n\n\nExifParser.prototype.scan_ifd = function (ifd_no, offset, on_entry) {\n  var entry_count = this.read_uint16(offset);\n\n  offset += 2;\n\n  for (var i = 0; i < entry_count; i++) {\n    var tag    = this.read_uint16(offset);\n    var format = this.read_uint16(offset + 2);\n    var count  = this.read_uint32(offset + 4);\n\n    var comp_length    = this.exif_format_length(format);\n    var data_length    = count * comp_length;\n    var data_offset    = data_length <= 4 ? offset + 8 : this.read_uint32(offset + 8);\n    var is_subifd_link = false;\n\n    if (data_offset + data_length > this.input.length) {\n      throw error('unexpected EOF', 'EBADDATA');\n    }\n\n    var value = [];\n    var comp_offset = data_offset;\n\n    for (var j = 0; j < count; j++, comp_offset += comp_length) {\n      var item = this.exif_format_read(format, comp_offset);\n      if (item === null) {\n        value = null;\n        break;\n      }\n      value.push(item);\n    }\n\n    if (Array.isArray(value) && format === 2) {\n      try {\n        value = utf8_decode(String.fromCharCode.apply(null, value));\n      } catch (_) {\n        value = null;\n      }\n\n      if (value && value[value.length - 1] === '\\0') value = value.slice(0, -1);\n    }\n\n    if (this.is_subifd_link(ifd_no, tag)) {\n      if (Array.isArray(value) && Number.isInteger(value[0]) && value[0] > 0) {\n        this.ifds_to_read.push({\n          id:     tag,\n          offset: value[0]\n        });\n        is_subifd_link = true;\n      }\n    }\n\n    var entry = {\n      is_big_endian:  this.big_endian,\n      ifd:            ifd_no,\n      tag:            tag,\n      format:         format,\n      count:          count,\n      entry_offset:   offset + this.start,\n      data_length:    data_length,\n      data_offset:    data_offset + this.start,\n      value:          value,\n      is_subifd_link: is_subifd_link\n    };\n\n    if (on_entry(entry) === false) {\n      this.aborted = true;\n      return;\n    }\n\n    offset += 12;\n  }\n\n  if (ifd_no === 0) {\n    this.ifds_to_read.push({\n      id:     1,\n      offset: this.read_uint32(offset)\n    });\n  }\n};\n\n\n// Check whether input is a JPEG image\n//\n// Input:\n//  - jpeg_bin: Uint8Array - jpeg file\n//\n// Returns true if it is and false otherwise\n//\nmodule.exports.is_jpeg = function (jpeg_bin) {\n  return jpeg_bin.length >= 4 && jpeg_bin[0] === 0xFF && jpeg_bin[1] === 0xD8 && jpeg_bin[2] === 0xFF;\n};\n\n\n// Call an iterator on each segment in the given JPEG image\n//\n// Input:\n//  - jpeg_bin:   Uint8Array - jpeg file\n//  - on_segment: Function - callback executed on each JPEG marker segment\n//    - segment:  Object\n//      - code:   Number - marker type (2nd byte, e.g. 0xE0 for APP0)\n//      - offset: Number - offset of the first byte (0xFF) relative to `jpeg_bin` start\n//      - length: Number - length of the entire marker segment including first two bytes and length\n//        - 2 for standalone markers\n//        - 4+length for markers with data\n//\n// Iteration stops when `EOI` (0xFFD9) marker is reached or if `on_segment`\n// function returns `false`.\n//\nmodule.exports.jpeg_segments_each = function (jpeg_bin, on_segment) {\n  if (!is_uint8array(jpeg_bin)) {\n    throw error('Invalid argument (jpeg_bin), Uint8Array expected', 'EINVAL');\n  }\n\n  if (typeof on_segment !== 'function') {\n    throw error('Invalid argument (on_segment), Function expected', 'EINVAL');\n  }\n\n  if (!module.exports.is_jpeg(jpeg_bin)) {\n    throw error('Unknown file format', 'ENOTJPEG');\n  }\n\n  var offset = 0, length = jpeg_bin.length, inside_scan = false;\n\n  for (;;) {\n    var segment_code, segment_length;\n\n    if (offset + 1 >= length) throw error('Unexpected EOF', 'EBADDATA');\n    var byte1 = jpeg_bin[offset];\n    var byte2 = jpeg_bin[offset + 1];\n\n    if (byte1 === 0xFF && byte2 === 0xFF) {\n      // padding\n      segment_code = 0xFF;\n      segment_length = 1;\n\n    } else if (byte1 === 0xFF && byte2 !== 0) {\n      // marker\n      segment_code = byte2;\n      segment_length = 2;\n\n      if ((0xD0 <= segment_code && segment_code <= 0xD9) || segment_code === 0x01) ; else {\n        if (offset + 3 >= length) throw error('Unexpected EOF', 'EBADDATA');\n        segment_length += jpeg_bin[offset + 2] * 0x100 + jpeg_bin[offset + 3];\n        if (segment_length < 2) throw error('Invalid segment length', 'EBADDATA');\n        if (offset + segment_length - 1 >= length) throw error('Unexpected EOF', 'EBADDATA');\n      }\n\n      if (inside_scan) {\n        if (segment_code >= 0xD0 && segment_code <= 0xD7) ; else {\n          inside_scan = false;\n        }\n      }\n\n      if (segment_code === 0xDA /* SOS */) inside_scan = true;\n    } else if (inside_scan) {\n      // entropy-encoded segment\n      for (var pos = offset + 1; ; pos++) {\n        // scan until we find FF\n        if (pos >= length) throw error('Unexpected EOF', 'EBADDATA');\n        if (jpeg_bin[pos] === 0xFF) {\n          if (pos + 1 >= length) throw error('Unexpected EOF', 'EBADDATA');\n          if (jpeg_bin[pos + 1] !== 0) {\n            segment_code = 0;\n            segment_length = pos - offset;\n            break;\n          }\n        }\n      }\n    } else {\n      throw error('Unexpected byte at segment start: ' + to_hex(byte1) +\n        ' (offset ' + to_hex(offset) + ')', 'EBADDATA');\n    }\n\n    if (on_segment({ code: segment_code, offset: offset, length: segment_length }) === false) break;\n    if (segment_code === 0xD9 /* EOI */) break;\n    offset += segment_length;\n  }\n};\n\n\n// Replace or remove segments in the given JPEG image\n//\n// Input:\n//  - jpeg_bin:   Uint8Array - jpeg file\n//  - on_segment: Function - callback executed on each JPEG marker segment\n//    - segment:  Object\n//      - code:   Number - marker type (2nd byte, e.g. 0xE0 for APP0)\n//      - offset: Number - offset of the first byte (0xFF) relative to `jpeg_bin` start\n//      - length: Number - length of the entire marker segment including first two bytes and length\n//        - 2 for standalone markers\n//        - 4+length for markers with data\n//\n// `on_segment` function should return one of the following:\n//  - `false`        - segment is removed from the output\n//  - Uint8Array     - segment is replaced with the new data\n//  - [ Uint8Array ] - segment is replaced with the new data\n//  - anything else  - segment is copied to the output as is\n//\n// Any data after `EOI` (0xFFD9) marker is removed.\n//\nmodule.exports.jpeg_segments_filter = function (jpeg_bin, on_segment) {\n  if (!is_uint8array(jpeg_bin)) {\n    throw error('Invalid argument (jpeg_bin), Uint8Array expected', 'EINVAL');\n  }\n\n  if (typeof on_segment !== 'function') {\n    throw error('Invalid argument (on_segment), Function expected', 'EINVAL');\n  }\n\n  var ranges = [];\n  var out_length = 0;\n\n  module.exports.jpeg_segments_each(jpeg_bin, function (segment) {\n    var new_segment = on_segment(segment);\n\n    if (is_uint8array(new_segment)) {\n      ranges.push({ data: new_segment });\n      out_length += new_segment.length;\n    } else if (Array.isArray(new_segment)) {\n      new_segment.filter(is_uint8array).forEach(function (s) {\n        ranges.push({ data: s });\n        out_length += s.length;\n      });\n    } else if (new_segment !== false) {\n      var new_range = { start: segment.offset, end: segment.offset + segment.length };\n\n      if (ranges.length > 0 && ranges[ranges.length - 1].end === new_range.start) {\n        ranges[ranges.length - 1].end = new_range.end;\n      } else {\n        ranges.push(new_range);\n      }\n\n      out_length += segment.length;\n    }\n  });\n\n  var result = new Uint8Array(out_length);\n  var offset = 0;\n\n  ranges.forEach(function (range) {\n    var data = range.data || jpeg_bin.subarray(range.start, range.end);\n    result.set(data, offset);\n    offset += data.length;\n  });\n\n  return result;\n};\n\n\n// Call an iterator on each Exif entry in the given JPEG image\n//\n// Input:\n//  - jpeg_bin: Uint8Array - jpeg file\n//  - on_entry: Function - callback executed on each Exif entry\n//    - entry:  Object\n//      - is_big_endian:  Boolean - whether Exif uses big or little endian byte alignment\n//      - ifd:            Number  - IFD identifier (0 for IFD0, 1 for IFD1, 0x8769 for SubIFD,\n//                                 0x8825 for GPS Info, 0xA005 for Interop IFD)\n//      - tag:            Number  - exif entry tag (0x0110 - camera name, 0x0112 - orientation, etc. - see Exif spec)\n//      - format:         Number  - exif entry format (1 - byte, 2 - ascii, 3 - short, etc. - see Exif spec)\n//      - count:          Number  - number of components of the given format inside data\n//                                 (usually 1, or string length for ascii format)\n//      - entry_offset:   Number  - start of Exif entry (entry length is always 12, so not included)\n//      - data_offset:    Number  - start of data attached to Exif entry (will overlap with entry if length <= 4)\n//      - data_length:    Number  - length of data attached to Exif entry\n//      - value:          Array|String|Null - our best attempt at parsing data (not all formats supported right now)\n//      - is_subifd_link: Boolean - whether this entry is recognized to be a link to subifd (can't filter these out)\n//\n// Iteration stops early if iterator returns `false`.\n//\n// If Exif wasn't found anywhere (before start of the image data, SOS),\n// iterator is never executed.\n//\nmodule.exports.jpeg_exif_tags_each = function (jpeg_bin, on_exif_entry) {\n  if (!is_uint8array(jpeg_bin)) {\n    throw error('Invalid argument (jpeg_bin), Uint8Array expected', 'EINVAL');\n  }\n\n  if (typeof on_exif_entry !== 'function') {\n    throw error('Invalid argument (on_exif_entry), Function expected', 'EINVAL');\n  }\n\n  /* eslint-disable consistent-return */\n  module.exports.jpeg_segments_each(jpeg_bin, function (segment) {\n    if (segment.code === 0xDA /* SOS */) return false;\n\n    // look for APP1 segment and compare header with 'Exif\\0\\0'\n    if (segment.code === 0xE1 && segment.length >= 10 &&\n        jpeg_bin[segment.offset + 4] === 0x45 && jpeg_bin[segment.offset + 5] === 0x78 &&\n        jpeg_bin[segment.offset + 6] === 0x69 && jpeg_bin[segment.offset + 7] === 0x66 &&\n        jpeg_bin[segment.offset + 8] === 0x00 && jpeg_bin[segment.offset + 9] === 0x00) {\n\n      new ExifParser(jpeg_bin, segment.offset + 10, segment.offset + segment.length).each(on_exif_entry);\n      return false;\n    }\n  });\n};\n\n\n// Remove Exif entries in the given JPEG image\n//\n// Input:\n//  - jpeg_bin: Uint8Array - jpeg file\n//  - on_entry: Function - callback executed on each Exif entry\n//    - entry:  Object\n//      - is_big_endian:  Boolean - whether Exif uses big or little endian byte alignment\n//      - ifd:            Number  - IFD identifier (0 for IFD0, 1 for IFD1, 0x8769 for SubIFD,\n//                                  0x8825 for GPS Info, 0xA005 for Interop IFD)\n//      - tag:            Number  - exif entry tag (0x0110 - camera name, 0x0112 - orientation, etc. - see Exif spec)\n//      - format:         Number  - exif entry format (1 - byte, 2 - ascii, 3 - short, etc. - see Exif spec)\n//      - count:          Number  - number of components of the given format inside data\n//                                  (usually 1, or string length for ascii format)\n//      - entry_offset:   Number  - start of Exif entry (entry length is always 12, so not included)\n//      - data_offset:    Number  - start of data attached to Exif entry (will overlap with entry if length <= 4)\n//      - data_length:    Number  - length of data attached to Exif entry\n//      - value:          Array|String|Null - our best attempt at parsing data (not all formats supported right now)\n//      - is_subifd_link: Boolean - whether this entry is recognized to be a link to subifd (can't filter these out)\n//\n// This function removes following from Exif:\n//  - all entries where iterator returned false (except subifd links which are mandatory)\n//  - IFD1 and thumbnail image (the purpose of this function is to reduce file size,\n//    so thumbnail is usually the first thing to go)\n//  - all other data that isn't in IFD0, SubIFD, GPSIFD, InteropIFD\n//    (theoretically possible proprietary extensions, I haven't seen any of these yet)\n//\n// Changing data inside Exif entries is NOT supported yet (modifying `entry` object inside callback may break stuff).\n//\n// If Exif wasn't found anywhere (before start of the image data, SOS),\n// iterator is never executed, and original JPEG is returned as is.\n//\nmodule.exports.jpeg_exif_tags_filter = function (jpeg_bin, on_exif_entry) {\n  if (!is_uint8array(jpeg_bin)) {\n    throw error('Invalid argument (jpeg_bin), Uint8Array expected', 'EINVAL');\n  }\n\n  if (typeof on_exif_entry !== 'function') {\n    throw error('Invalid argument (on_exif_entry), Function expected', 'EINVAL');\n  }\n\n  var stop_search = false;\n\n  return module.exports.jpeg_segments_filter(jpeg_bin, function (segment) {\n    if (stop_search) return;\n    if (segment.code === 0xDA /* SOS */) stop_search = true;\n\n    // look for APP1 segment and compare header with 'Exif\\0\\0'\n    if (segment.code === 0xE1 && segment.length >= 10 &&\n        jpeg_bin[segment.offset + 4] === 0x45 && jpeg_bin[segment.offset + 5] === 0x78 &&\n        jpeg_bin[segment.offset + 6] === 0x69 && jpeg_bin[segment.offset + 7] === 0x66 &&\n        jpeg_bin[segment.offset + 8] === 0x00 && jpeg_bin[segment.offset + 9] === 0x00) {\n\n      var new_exif = new ExifParser(jpeg_bin, segment.offset + 10, segment.offset + segment.length)\n        .filter(on_exif_entry);\n      if (!new_exif) return false;\n\n      var header = new Uint8Array(10);\n\n      header.set(jpeg_bin.slice(segment.offset, segment.offset + 10));\n      header[2] = ((new_exif.length + 8) >>> 8) & 0xFF;\n      header[3] = (new_exif.length + 8) & 0xFF;\n\n      stop_search = true;\n      return [ header, new_exif ];\n    }\n  });\n};\n\n\n// Inserts a custom comment marker segment into JPEG file.\n//\n// Input:\n//  - jpeg_bin: Uint8Array - jpeg file\n//  - comment:  String\n//\n// Comment is inserted after first two bytes (FFD8, SOI).\n//\n// If JFIF (APP0) marker exists immediately after SOI (as mandated by the JFIF\n// spec), we insert comment after it instead.\n//\nmodule.exports.jpeg_add_comment = function (jpeg_bin, comment) {\n  var comment_inserted = false, segment_count = 0;\n\n  return module.exports.jpeg_segments_filter(jpeg_bin, function (segment) {\n    segment_count++;\n    if (segment_count === 1 && segment.code === 0xD8 /* SOI  */) return;\n    if (segment_count === 2 && segment.code === 0xE0 /* APP0 */) return;\n\n    if (comment_inserted) return;\n    comment = utf8_encode(comment);\n\n    // comment segment\n    var csegment = new Uint8Array(5 + comment.length);\n    var offset = 0;\n\n    csegment[offset++] = 0xFF;\n    csegment[offset++] = 0xFE;\n    csegment[offset++] = ((comment.length + 3) >>> 8) & 0xFF;\n    csegment[offset++] = (comment.length + 3) & 0xFF;\n\n    comment.split('').forEach(function (c) {\n      csegment[offset++] = c.charCodeAt(0) & 0xFF;\n    });\n\n    csegment[offset++] = 0;\n    comment_inserted = true;\n\n    return [ csegment, jpeg_bin.subarray(segment.offset, segment.offset + segment.length) ];\n  });\n};\n});\n\nfunction jpeg_patch_exif(env) {\n  return this._getUint8Array(env.blob).then(function (data) {\n    env.is_jpeg = image_traverse.is_jpeg(data);\n\n    if (!env.is_jpeg) return Promise.resolve(env);\n\n    env.orig_blob = env.blob;\n\n    try {\n      var exif_is_big_endian, orientation_offset;\n\n      /* eslint-disable consistent-return */\n      image_traverse.jpeg_exif_tags_each(data, function (entry) {\n        if (entry.ifd === 0 && entry.tag === 0x112 && Array.isArray(entry.value)) {\n          env.orientation    = entry.value[0] || 1;\n          exif_is_big_endian = entry.is_big_endian;\n          orientation_offset = entry.data_offset;\n          return false;\n        }\n      });\n\n      if (orientation_offset) {\n        var orientation_patch = exif_is_big_endian ?\n          new Uint8Array([ 0, 1 ]) :\n          new Uint8Array([ 1, 0 ]);\n\n        env.blob = new Blob([\n          data.slice(0, orientation_offset),\n          orientation_patch,\n          data.slice(orientation_offset + 2)\n        ], { type: 'image/jpeg' });\n      }\n    } catch (_) {}\n\n    return env;\n  });\n}\n\n\nfunction jpeg_rotate_canvas(env) {\n  if (!env.is_jpeg) return Promise.resolve(env);\n\n  var orientation = env.orientation - 1;\n  if (!orientation) return Promise.resolve(env);\n\n  var canvas;\n\n  if (orientation & 4) {\n    canvas = this.pica.options.createCanvas(env.out_canvas.height, env.out_canvas.width);\n  } else {\n    canvas = this.pica.options.createCanvas(env.out_canvas.width, env.out_canvas.height);\n  }\n\n  var ctx = canvas.getContext('2d');\n\n  ctx.save();\n\n  if (orientation & 1) ctx.transform(-1, 0, 0, 1, canvas.width, 0);\n  if (orientation & 2) ctx.transform(-1, 0, 0, -1, canvas.width, canvas.height);\n  if (orientation & 4) ctx.transform(0, 1, 1, 0, 0, 0);\n\n  ctx.drawImage(env.out_canvas, 0, 0);\n  ctx.restore();\n\n  // Safari 12 workaround\n  // https://github.com/nodeca/pica/issues/199\n  env.out_canvas.width = env.out_canvas.height = 0;\n\n  env.out_canvas = canvas;\n\n  return Promise.resolve(env);\n}\n\n\nfunction jpeg_attach_orig_segments(env) {\n  if (!env.is_jpeg) return Promise.resolve(env);\n\n  return Promise.all([\n    this._getUint8Array(env.blob),\n    this._getUint8Array(env.out_blob)\n  ]).then(function (res) {\n    var data = res[0];\n    var data_out = res[1];\n\n    if (!image_traverse.is_jpeg(data)) return Promise.resolve(env);\n\n    var segments = [];\n\n    image_traverse.jpeg_segments_each(data, function (segment) {\n      if (segment.code === 0xDA /* SOS */) return false;\n      segments.push(segment);\n    });\n\n    segments = segments\n      .filter(function (segment) {\n        // Drop ICC_PROFILE\n        //\n        if (segment.code === 0xE2) return false;\n\n        // Keep all APPn segments excluding APP2 (ICC_PROFILE),\n        // remove others because most of them depend on image data (DCT and such).\n        //\n        // APP0 - JFIF, APP1 - Exif, the rest are photoshop metadata and such\n        //\n        // See full list at https://www.w3.org/Graphics/JPEG/itu-t81.pdf (table B.1 on page 32)\n        //\n        if (segment.code >= 0xE0 && segment.code < 0xF0) return true;\n\n        // Keep comments\n        //\n        if (segment.code === 0xFE) return true;\n\n        return false;\n      })\n      .map(function (segment) {\n        return data.slice(segment.offset, segment.offset + segment.length);\n      });\n\n    env.out_blob = new Blob(\n      // intentionally omitting expected JFIF segment (offset 2 to 20)\n      [ data_out.slice(0, 2) ].concat(segments).concat([ data_out.slice(20) ]),\n      { type: 'image/jpeg' }\n    );\n\n    return env;\n  });\n}\n\n\nfunction assign(reducer) {\n  reducer.before('_blob_to_image', jpeg_patch_exif);\n  reducer.after('_transform',      jpeg_rotate_canvas);\n  reducer.after('_create_blob',    jpeg_attach_orig_segments);\n}\n\n\nvar jpeg_patch_exif_1 = jpeg_patch_exif;\nvar jpeg_rotate_canvas_1 = jpeg_rotate_canvas;\nvar jpeg_attach_orig_segments_1 = jpeg_attach_orig_segments;\nvar assign_1 = assign;\n\nvar jpeg_plugins = {\n\tjpeg_patch_exif: jpeg_patch_exif_1,\n\tjpeg_rotate_canvas: jpeg_rotate_canvas_1,\n\tjpeg_attach_orig_segments: jpeg_attach_orig_segments_1,\n\tassign: assign_1\n};\n\nfunction ImageBlobReduce(options) {\n  if (!(this instanceof ImageBlobReduce)) return new ImageBlobReduce(options);\n\n  options = options || {};\n\n  this.pica = options.pica || pica({});\n  this.initialized = false;\n\n  this.utils = utils;\n}\n\n\nImageBlobReduce.prototype.use = function (plugin /*, params, ... */) {\n  var args = [ this ].concat(Array.prototype.slice.call(arguments, 1));\n  plugin.apply(plugin, args);\n  return this;\n};\n\n\nImageBlobReduce.prototype.init = function () {\n  this.use(jpeg_plugins.assign);\n};\n\n\nImageBlobReduce.prototype.toBlob = function (blob, options) {\n  var opts = utils.assign({ max: Infinity }, options);\n  var env = {\n    blob: blob,\n    opts: opts\n  };\n\n  if (!this.initialized) {\n    this.init();\n    this.initialized = true;\n  }\n\n  return Promise.resolve(env)\n    .then(this._blob_to_image)\n    .then(this._calculate_size)\n    .then(this._transform)\n    .then(this._cleanup)\n    .then(this._create_blob)\n    .then(function (_env) {\n      // Safari 12 workaround\n      // https://github.com/nodeca/pica/issues/199\n      _env.out_canvas.width = _env.out_canvas.height = 0;\n\n      return _env.out_blob;\n    });\n};\n\n\nImageBlobReduce.prototype.toCanvas = function (blob, options) {\n  var opts = utils.assign({ max: Infinity }, options);\n  var env = {\n    blob: blob,\n    opts: opts\n  };\n\n  if (!this.initialized) {\n    this.init();\n    this.initialized = true;\n  }\n\n  return Promise.resolve(env)\n    .then(this._blob_to_image)\n    .then(this._calculate_size)\n    .then(this._transform)\n    .then(this._cleanup)\n    .then(function (_env) { return _env.out_canvas; });\n};\n\n\nImageBlobReduce.prototype.before = function (method_name, fn) {\n  if (!this[method_name]) throw new Error('Method \"' + method_name + '\" does not exist');\n  if (typeof fn !== 'function') throw new Error('Invalid argument \"fn\", function expected');\n\n  var old_fn = this[method_name];\n  var self = this;\n\n  this[method_name] = function (env) {\n    return fn.call(self, env).then(function (_env) {\n      return old_fn.call(self, _env);\n    });\n  };\n\n  return this;\n};\n\n\nImageBlobReduce.prototype.after = function (method_name, fn) {\n  if (!this[method_name]) throw new Error('Method \"' + method_name + '\" does not exist');\n  if (typeof fn !== 'function') throw new Error('Invalid argument \"fn\", function expected');\n\n  var old_fn = this[method_name];\n  var self = this;\n\n  this[method_name] = function (env) {\n    return old_fn.call(self, env).then(function (_env) {\n      return fn.call(self, _env);\n    });\n  };\n\n  return this;\n};\n\n\nImageBlobReduce.prototype._blob_to_image = function (env) {\n  var URL = window.URL || window.webkitURL || window.mozURL || window.msURL;\n\n  env.image = document.createElement('img');\n  env.image_url = URL.createObjectURL(env.blob);\n  env.image.src = env.image_url;\n\n  return new Promise(function (resolve, reject) {\n    env.image.onerror = function () { reject(new Error('ImageBlobReduce: failed to create Image() from blob')); };\n    env.image.onload = function () { resolve(env); };\n  });\n};\n\n\nImageBlobReduce.prototype._calculate_size = function (env) {\n  //\n  // Note, if your need not \"symmetric\" resize logic, you MUST check\n  // `env.orientation` (set by plugins) and swap width/height appropriately.\n  //\n  var scale_factor = env.opts.max / Math.max(env.image.width, env.image.height);\n\n  if (scale_factor > 1) scale_factor = 1;\n\n  env.transform_width = Math.max(Math.round(env.image.width * scale_factor), 1);\n  env.transform_height = Math.max(Math.round(env.image.height * scale_factor), 1);\n\n  // Info for user plugins, to check if scaling applied\n  env.scale_factor = scale_factor;\n\n  return Promise.resolve(env);\n};\n\n\nImageBlobReduce.prototype._transform = function (env) {\n  env.out_canvas = this.pica.options.createCanvas(env.transform_width, env.transform_height);\n\n  // Dim env temporary vars to prohibit use and avoid confusion when orientation\n  // changed. You should take real size from canvas.\n  env.transform_width = null;\n  env.transform_height = null;\n\n  // By default use alpha for png only\n  var pica_opts = { alpha: env.blob.type === 'image/png' };\n\n  // Extract pica options if been passed\n  this.utils.assign(pica_opts, this.utils.pick_pica_resize_options(env.opts));\n\n  return this.pica\n    .resize(env.image, env.out_canvas, pica_opts)\n    .then(function () { return env; });\n};\n\n\nImageBlobReduce.prototype._cleanup = function (env) {\n  env.image.src = '';\n  env.image = null;\n\n  var URL = window.URL || window.webkitURL || window.mozURL || window.msURL;\n  if (URL.revokeObjectURL) URL.revokeObjectURL(env.image_url);\n\n  env.image_url = null;\n\n  return Promise.resolve(env);\n};\n\n\nImageBlobReduce.prototype._create_blob = function (env) {\n  return this.pica.toBlob(env.out_canvas, env.blob.type)\n    .then(function (blob) {\n      env.out_blob = blob;\n      return env;\n    });\n};\n\n\nImageBlobReduce.prototype._getUint8Array = function (blob) {\n  if (blob.arrayBuffer) {\n    return blob.arrayBuffer().then(function (buf) {\n      return new Uint8Array(buf);\n    });\n  }\n\n  return new Promise(function (resolve, reject) {\n    var fr = new FileReader();\n\n    fr.readAsArrayBuffer(blob);\n\n    fr.onload = function () { resolve(new Uint8Array(fr.result)); };\n    fr.onerror = function () {\n      reject(new Error('ImageBlobReduce: failed to load data from input blob'));\n      fr.abort();\n    };\n    fr.onabort = function () {\n      reject(new Error('ImageBlobReduce: failed to load data from input blob (aborted)'));\n    };\n  });\n};\n\n\nImageBlobReduce.pica = pica;\n\nvar imageBlobReduce = ImageBlobReduce;\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (imageBlobReduce);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/image-blob-reduce/dist/image-blob-reduce.esm.mjs\n"));

/***/ })

}]);