"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_gitGraphDiagram-72cf32ee_js"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/gitGraphDiagram-72cf32ee.js":
/*!***************************************************************!*\
  !*** ./node_modules/mermaid/dist/gitGraphDiagram-72cf32ee.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [1, 3], $V1 = [1, 6], $V2 = [1, 4], $V3 = [1, 5], $V4 = [2, 5], $V5 = [1, 12], $V6 = [5, 7, 13, 19, 21, 23, 24, 26, 28, 31, 37, 40, 47], $V7 = [7, 13, 19, 21, 23, 24, 26, 28, 31, 37, 40], $V8 = [7, 12, 13, 19, 21, 23, 24, 26, 28, 31, 37, 40], $V9 = [7, 13, 47], $Va = [1, 42], $Vb = [1, 41], $Vc = [7, 13, 29, 32, 35, 38, 47], $Vd = [1, 55], $Ve = [1, 56], $Vf = [1, 57], $Vg = [7, 13, 32, 35, 42, 47];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"GG\": 5, \"document\": 6, \"EOF\": 7, \":\": 8, \"DIR\": 9, \"options\": 10, \"body\": 11, \"OPT\": 12, \"NL\": 13, \"line\": 14, \"statement\": 15, \"commitStatement\": 16, \"mergeStatement\": 17, \"cherryPickStatement\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"section\": 24, \"branchStatement\": 25, \"CHECKOUT\": 26, \"ref\": 27, \"BRANCH\": 28, \"ORDER\": 29, \"NUM\": 30, \"CHERRY_PICK\": 31, \"COMMIT_ID\": 32, \"STR\": 33, \"PARENT_COMMIT\": 34, \"COMMIT_TAG\": 35, \"EMPTYSTR\": 36, \"MERGE\": 37, \"COMMIT_TYPE\": 38, \"commitType\": 39, \"COMMIT\": 40, \"commit_arg\": 41, \"COMMIT_MSG\": 42, \"NORMAL\": 43, \"REVERSE\": 44, \"HIGHLIGHT\": 45, \"ID\": 46, \";\": 47, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"GG\", 7: \"EOF\", 8: \":\", 9: \"DIR\", 12: \"OPT\", 13: \"NL\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"section\", 26: \"CHECKOUT\", 28: \"BRANCH\", 29: \"ORDER\", 30: \"NUM\", 31: \"CHERRY_PICK\", 32: \"COMMIT_ID\", 33: \"STR\", 34: \"PARENT_COMMIT\", 35: \"COMMIT_TAG\", 36: \"EMPTYSTR\", 37: \"MERGE\", 38: \"COMMIT_TYPE\", 40: \"COMMIT\", 42: \"COMMIT_MSG\", 43: \"NORMAL\", 44: \"REVERSE\", 45: \"HIGHLIGHT\", 46: \"ID\", 47: \";\" },\n    productions_: [0, [3, 2], [3, 3], [3, 4], [3, 5], [6, 0], [6, 2], [10, 2], [10, 1], [11, 0], [11, 2], [14, 2], [14, 1], [15, 1], [15, 1], [15, 1], [15, 2], [15, 2], [15, 1], [15, 1], [15, 1], [15, 2], [25, 2], [25, 4], [18, 3], [18, 5], [18, 5], [18, 7], [18, 7], [18, 5], [18, 5], [18, 5], [18, 7], [18, 7], [18, 7], [18, 7], [17, 2], [17, 4], [17, 4], [17, 4], [17, 6], [17, 6], [17, 6], [17, 6], [17, 6], [17, 6], [17, 8], [17, 8], [17, 8], [17, 8], [17, 8], [17, 8], [16, 2], [16, 3], [16, 3], [16, 5], [16, 5], [16, 3], [16, 5], [16, 5], [16, 5], [16, 5], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 3], [16, 5], [16, 5], [16, 5], [16, 5], [16, 5], [16, 5], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 7], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [16, 9], [41, 0], [41, 1], [39, 1], [39, 1], [39, 1], [27, 1], [27, 1], [4, 1], [4, 1], [4, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 2:\n          return $$[$0];\n        case 3:\n          return $$[$0 - 1];\n        case 4:\n          yy.setDirection($$[$0 - 3]);\n          return $$[$0 - 1];\n        case 6:\n          yy.setOptions($$[$0 - 1]);\n          this.$ = $$[$0];\n          break;\n        case 7:\n          $$[$0 - 1] += $$[$0];\n          this.$ = $$[$0 - 1];\n          break;\n        case 9:\n          this.$ = [];\n          break;\n        case 10:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 11:\n          this.$ = $$[$0 - 1];\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 21:\n          yy.checkout($$[$0]);\n          break;\n        case 22:\n          yy.branch($$[$0]);\n          break;\n        case 23:\n          yy.branch($$[$0 - 2], $$[$0]);\n          break;\n        case 24:\n          yy.cherryPick($$[$0], \"\", void 0);\n          break;\n        case 25:\n          yy.cherryPick($$[$0 - 2], \"\", void 0, $$[$0]);\n          break;\n        case 26:\n          yy.cherryPick($$[$0 - 2], \"\", $$[$0]);\n          break;\n        case 27:\n          yy.cherryPick($$[$0 - 4], \"\", $$[$0], $$[$0 - 2]);\n          break;\n        case 28:\n          yy.cherryPick($$[$0 - 4], \"\", $$[$0 - 2], $$[$0]);\n          break;\n        case 29:\n          yy.cherryPick($$[$0], \"\", $$[$0 - 2]);\n          break;\n        case 30:\n          yy.cherryPick($$[$0], \"\", \"\");\n          break;\n        case 31:\n          yy.cherryPick($$[$0 - 2], \"\", \"\");\n          break;\n        case 32:\n          yy.cherryPick($$[$0 - 4], \"\", \"\", $$[$0 - 2]);\n          break;\n        case 33:\n          yy.cherryPick($$[$0 - 4], \"\", \"\", $$[$0]);\n          break;\n        case 34:\n          yy.cherryPick($$[$0 - 2], \"\", $$[$0 - 4], $$[$0]);\n          break;\n        case 35:\n          yy.cherryPick($$[$0 - 2], \"\", \"\", $$[$0]);\n          break;\n        case 36:\n          yy.merge($$[$0], \"\", \"\", \"\");\n          break;\n        case 37:\n          yy.merge($$[$0 - 2], $$[$0], \"\", \"\");\n          break;\n        case 38:\n          yy.merge($$[$0 - 2], \"\", $$[$0], \"\");\n          break;\n        case 39:\n          yy.merge($$[$0 - 2], \"\", \"\", $$[$0]);\n          break;\n        case 40:\n          yy.merge($$[$0 - 4], $$[$0], \"\", $$[$0 - 2]);\n          break;\n        case 41:\n          yy.merge($$[$0 - 4], \"\", $$[$0], $$[$0 - 2]);\n          break;\n        case 42:\n          yy.merge($$[$0 - 4], \"\", $$[$0 - 2], $$[$0]);\n          break;\n        case 43:\n          yy.merge($$[$0 - 4], $$[$0 - 2], $$[$0], \"\");\n          break;\n        case 44:\n          yy.merge($$[$0 - 4], $$[$0 - 2], \"\", $$[$0]);\n          break;\n        case 45:\n          yy.merge($$[$0 - 4], $$[$0], $$[$0 - 2], \"\");\n          break;\n        case 46:\n          yy.merge($$[$0 - 6], $$[$0 - 4], $$[$0 - 2], $$[$0]);\n          break;\n        case 47:\n          yy.merge($$[$0 - 6], $$[$0], $$[$0 - 4], $$[$0 - 2]);\n          break;\n        case 48:\n          yy.merge($$[$0 - 6], $$[$0 - 4], $$[$0], $$[$0 - 2]);\n          break;\n        case 49:\n          yy.merge($$[$0 - 6], $$[$0 - 2], $$[$0 - 4], $$[$0]);\n          break;\n        case 50:\n          yy.merge($$[$0 - 6], $$[$0], $$[$0 - 2], $$[$0 - 4]);\n          break;\n        case 51:\n          yy.merge($$[$0 - 6], $$[$0 - 2], $$[$0], $$[$0 - 4]);\n          break;\n        case 52:\n          yy.commit($$[$0]);\n          break;\n        case 53:\n          yy.commit(\"\", \"\", yy.commitType.NORMAL, $$[$0]);\n          break;\n        case 54:\n          yy.commit(\"\", \"\", $$[$0], \"\");\n          break;\n        case 55:\n          yy.commit(\"\", \"\", $$[$0], $$[$0 - 2]);\n          break;\n        case 56:\n          yy.commit(\"\", \"\", $$[$0 - 2], $$[$0]);\n          break;\n        case 57:\n          yy.commit(\"\", $$[$0], yy.commitType.NORMAL, \"\");\n          break;\n        case 58:\n          yy.commit(\"\", $$[$0 - 2], yy.commitType.NORMAL, $$[$0]);\n          break;\n        case 59:\n          yy.commit(\"\", $$[$0], yy.commitType.NORMAL, $$[$0 - 2]);\n          break;\n        case 60:\n          yy.commit(\"\", $$[$0 - 2], $$[$0], \"\");\n          break;\n        case 61:\n          yy.commit(\"\", $$[$0], $$[$0 - 2], \"\");\n          break;\n        case 62:\n          yy.commit(\"\", $$[$0 - 4], $$[$0 - 2], $$[$0]);\n          break;\n        case 63:\n          yy.commit(\"\", $$[$0 - 4], $$[$0], $$[$0 - 2]);\n          break;\n        case 64:\n          yy.commit(\"\", $$[$0 - 2], $$[$0 - 4], $$[$0]);\n          break;\n        case 65:\n          yy.commit(\"\", $$[$0], $$[$0 - 4], $$[$0 - 2]);\n          break;\n        case 66:\n          yy.commit(\"\", $$[$0], $$[$0 - 2], $$[$0 - 4]);\n          break;\n        case 67:\n          yy.commit(\"\", $$[$0 - 2], $$[$0], $$[$0 - 4]);\n          break;\n        case 68:\n          yy.commit($$[$0], \"\", yy.commitType.NORMAL, \"\");\n          break;\n        case 69:\n          yy.commit($$[$0], \"\", yy.commitType.NORMAL, $$[$0 - 2]);\n          break;\n        case 70:\n          yy.commit($$[$0 - 2], \"\", yy.commitType.NORMAL, $$[$0]);\n          break;\n        case 71:\n          yy.commit($$[$0 - 2], \"\", $$[$0], \"\");\n          break;\n        case 72:\n          yy.commit($$[$0], \"\", $$[$0 - 2], \"\");\n          break;\n        case 73:\n          yy.commit($$[$0], $$[$0 - 2], yy.commitType.NORMAL, \"\");\n          break;\n        case 74:\n          yy.commit($$[$0 - 2], $$[$0], yy.commitType.NORMAL, \"\");\n          break;\n        case 75:\n          yy.commit($$[$0 - 4], \"\", $$[$0 - 2], $$[$0]);\n          break;\n        case 76:\n          yy.commit($$[$0 - 4], \"\", $$[$0], $$[$0 - 2]);\n          break;\n        case 77:\n          yy.commit($$[$0 - 2], \"\", $$[$0 - 4], $$[$0]);\n          break;\n        case 78:\n          yy.commit($$[$0], \"\", $$[$0 - 4], $$[$0 - 2]);\n          break;\n        case 79:\n          yy.commit($$[$0], \"\", $$[$0 - 2], $$[$0 - 4]);\n          break;\n        case 80:\n          yy.commit($$[$0 - 2], \"\", $$[$0], $$[$0 - 4]);\n          break;\n        case 81:\n          yy.commit($$[$0 - 4], $$[$0], $$[$0 - 2], \"\");\n          break;\n        case 82:\n          yy.commit($$[$0 - 4], $$[$0 - 2], $$[$0], \"\");\n          break;\n        case 83:\n          yy.commit($$[$0 - 2], $$[$0], $$[$0 - 4], \"\");\n          break;\n        case 84:\n          yy.commit($$[$0], $$[$0 - 2], $$[$0 - 4], \"\");\n          break;\n        case 85:\n          yy.commit($$[$0], $$[$0 - 4], $$[$0 - 2], \"\");\n          break;\n        case 86:\n          yy.commit($$[$0 - 2], $$[$0 - 4], $$[$0], \"\");\n          break;\n        case 87:\n          yy.commit($$[$0 - 4], $$[$0], yy.commitType.NORMAL, $$[$0 - 2]);\n          break;\n        case 88:\n          yy.commit($$[$0 - 4], $$[$0 - 2], yy.commitType.NORMAL, $$[$0]);\n          break;\n        case 89:\n          yy.commit($$[$0 - 2], $$[$0], yy.commitType.NORMAL, $$[$0 - 4]);\n          break;\n        case 90:\n          yy.commit($$[$0], $$[$0 - 2], yy.commitType.NORMAL, $$[$0 - 4]);\n          break;\n        case 91:\n          yy.commit($$[$0], $$[$0 - 4], yy.commitType.NORMAL, $$[$0 - 2]);\n          break;\n        case 92:\n          yy.commit($$[$0 - 2], $$[$0 - 4], yy.commitType.NORMAL, $$[$0]);\n          break;\n        case 93:\n          yy.commit($$[$0 - 6], $$[$0 - 4], $$[$0 - 2], $$[$0]);\n          break;\n        case 94:\n          yy.commit($$[$0 - 6], $$[$0 - 4], $$[$0], $$[$0 - 2]);\n          break;\n        case 95:\n          yy.commit($$[$0 - 6], $$[$0 - 2], $$[$0 - 4], $$[$0]);\n          break;\n        case 96:\n          yy.commit($$[$0 - 6], $$[$0], $$[$0 - 4], $$[$0 - 2]);\n          break;\n        case 97:\n          yy.commit($$[$0 - 6], $$[$0 - 2], $$[$0], $$[$0 - 4]);\n          break;\n        case 98:\n          yy.commit($$[$0 - 6], $$[$0], $$[$0 - 2], $$[$0 - 4]);\n          break;\n        case 99:\n          yy.commit($$[$0 - 4], $$[$0 - 6], $$[$0 - 2], $$[$0]);\n          break;\n        case 100:\n          yy.commit($$[$0 - 4], $$[$0 - 6], $$[$0], $$[$0 - 2]);\n          break;\n        case 101:\n          yy.commit($$[$0 - 2], $$[$0 - 6], $$[$0 - 4], $$[$0]);\n          break;\n        case 102:\n          yy.commit($$[$0], $$[$0 - 6], $$[$0 - 4], $$[$0 - 2]);\n          break;\n        case 103:\n          yy.commit($$[$0 - 2], $$[$0 - 6], $$[$0], $$[$0 - 4]);\n          break;\n        case 104:\n          yy.commit($$[$0], $$[$0 - 6], $$[$0 - 2], $$[$0 - 4]);\n          break;\n        case 105:\n          yy.commit($$[$0], $$[$0 - 4], $$[$0 - 2], $$[$0 - 6]);\n          break;\n        case 106:\n          yy.commit($$[$0 - 2], $$[$0 - 4], $$[$0], $$[$0 - 6]);\n          break;\n        case 107:\n          yy.commit($$[$0], $$[$0 - 2], $$[$0 - 4], $$[$0 - 6]);\n          break;\n        case 108:\n          yy.commit($$[$0 - 2], $$[$0], $$[$0 - 4], $$[$0 - 6]);\n          break;\n        case 109:\n          yy.commit($$[$0 - 4], $$[$0 - 2], $$[$0], $$[$0 - 6]);\n          break;\n        case 110:\n          yy.commit($$[$0 - 4], $$[$0], $$[$0 - 2], $$[$0 - 6]);\n          break;\n        case 111:\n          yy.commit($$[$0 - 2], $$[$0 - 4], $$[$0 - 6], $$[$0]);\n          break;\n        case 112:\n          yy.commit($$[$0], $$[$0 - 4], $$[$0 - 6], $$[$0 - 2]);\n          break;\n        case 113:\n          yy.commit($$[$0 - 2], $$[$0], $$[$0 - 6], $$[$0 - 4]);\n          break;\n        case 114:\n          yy.commit($$[$0], $$[$0 - 2], $$[$0 - 6], $$[$0 - 4]);\n          break;\n        case 115:\n          yy.commit($$[$0 - 4], $$[$0 - 2], $$[$0 - 6], $$[$0]);\n          break;\n        case 116:\n          yy.commit($$[$0 - 4], $$[$0], $$[$0 - 6], $$[$0 - 2]);\n          break;\n        case 117:\n          this.$ = \"\";\n          break;\n        case 118:\n          this.$ = $$[$0];\n          break;\n        case 119:\n          this.$ = yy.commitType.NORMAL;\n          break;\n        case 120:\n          this.$ = yy.commitType.REVERSE;\n          break;\n        case 121:\n          this.$ = yy.commitType.HIGHLIGHT;\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: 2, 5: $V0, 7: $V1, 13: $V2, 47: $V3 }, { 1: [3] }, { 3: 7, 4: 2, 5: $V0, 7: $V1, 13: $V2, 47: $V3 }, { 6: 8, 7: $V4, 8: [1, 9], 9: [1, 10], 10: 11, 13: $V5 }, o($V6, [2, 124]), o($V6, [2, 125]), o($V6, [2, 126]), { 1: [2, 1] }, { 7: [1, 13] }, { 6: 14, 7: $V4, 10: 11, 13: $V5 }, { 8: [1, 15] }, o($V7, [2, 9], { 11: 16, 12: [1, 17] }), o($V8, [2, 8]), { 1: [2, 2] }, { 7: [1, 18] }, { 6: 19, 7: $V4, 10: 11, 13: $V5 }, { 7: [2, 6], 13: [1, 22], 14: 20, 15: 21, 16: 23, 17: 24, 18: 25, 19: [1, 26], 21: [1, 27], 23: [1, 28], 24: [1, 29], 25: 30, 26: [1, 31], 28: [1, 35], 31: [1, 34], 37: [1, 33], 40: [1, 32] }, o($V8, [2, 7]), { 1: [2, 3] }, { 7: [1, 36] }, o($V7, [2, 10]), { 4: 37, 7: $V1, 13: $V2, 47: $V3 }, o($V7, [2, 12]), o($V9, [2, 13]), o($V9, [2, 14]), o($V9, [2, 15]), { 20: [1, 38] }, { 22: [1, 39] }, o($V9, [2, 18]), o($V9, [2, 19]), o($V9, [2, 20]), { 27: 40, 33: $Va, 46: $Vb }, o($V9, [2, 117], { 41: 43, 32: [1, 46], 33: [1, 48], 35: [1, 44], 38: [1, 45], 42: [1, 47] }), { 27: 49, 33: $Va, 46: $Vb }, { 32: [1, 50], 35: [1, 51] }, { 27: 52, 33: $Va, 46: $Vb }, { 1: [2, 4] }, o($V7, [2, 11]), o($V9, [2, 16]), o($V9, [2, 17]), o($V9, [2, 21]), o($Vc, [2, 122]), o($Vc, [2, 123]), o($V9, [2, 52]), { 33: [1, 53] }, { 39: 54, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 58] }, { 33: [1, 59] }, o($V9, [2, 118]), o($V9, [2, 36], { 32: [1, 60], 35: [1, 62], 38: [1, 61] }), { 33: [1, 63] }, { 33: [1, 64], 36: [1, 65] }, o($V9, [2, 22], { 29: [1, 66] }), o($V9, [2, 53], { 32: [1, 68], 38: [1, 67], 42: [1, 69] }), o($V9, [2, 54], { 32: [1, 71], 35: [1, 70], 42: [1, 72] }), o($Vg, [2, 119]), o($Vg, [2, 120]), o($Vg, [2, 121]), o($V9, [2, 57], { 35: [1, 73], 38: [1, 74], 42: [1, 75] }), o($V9, [2, 68], { 32: [1, 78], 35: [1, 76], 38: [1, 77] }), { 33: [1, 79] }, { 39: 80, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 81] }, o($V9, [2, 24], { 34: [1, 82], 35: [1, 83] }), { 32: [1, 84] }, { 32: [1, 85] }, { 30: [1, 86] }, { 39: 87, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 88] }, { 33: [1, 89] }, { 33: [1, 90] }, { 33: [1, 91] }, { 33: [1, 92] }, { 33: [1, 93] }, { 39: 94, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 95] }, { 33: [1, 96] }, { 39: 97, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 98] }, o($V9, [2, 37], { 35: [1, 100], 38: [1, 99] }), o($V9, [2, 38], { 32: [1, 102], 35: [1, 101] }), o($V9, [2, 39], { 32: [1, 103], 38: [1, 104] }), { 33: [1, 105] }, { 33: [1, 106], 36: [1, 107] }, { 33: [1, 108] }, { 33: [1, 109] }, o($V9, [2, 23]), o($V9, [2, 55], { 32: [1, 110], 42: [1, 111] }), o($V9, [2, 59], { 38: [1, 112], 42: [1, 113] }), o($V9, [2, 69], { 32: [1, 115], 38: [1, 114] }), o($V9, [2, 56], { 32: [1, 116], 42: [1, 117] }), o($V9, [2, 61], { 35: [1, 118], 42: [1, 119] }), o($V9, [2, 72], { 32: [1, 121], 35: [1, 120] }), o($V9, [2, 58], { 38: [1, 122], 42: [1, 123] }), o($V9, [2, 60], { 35: [1, 124], 42: [1, 125] }), o($V9, [2, 73], { 35: [1, 127], 38: [1, 126] }), o($V9, [2, 70], { 32: [1, 129], 38: [1, 128] }), o($V9, [2, 71], { 32: [1, 131], 35: [1, 130] }), o($V9, [2, 74], { 35: [1, 133], 38: [1, 132] }), { 39: 134, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 135] }, { 33: [1, 136] }, { 33: [1, 137] }, { 33: [1, 138] }, { 39: 139, 43: $Vd, 44: $Ve, 45: $Vf }, o($V9, [2, 25], { 35: [1, 140] }), o($V9, [2, 26], { 34: [1, 141] }), o($V9, [2, 31], { 34: [1, 142] }), o($V9, [2, 29], { 34: [1, 143] }), o($V9, [2, 30], { 34: [1, 144] }), { 33: [1, 145] }, { 33: [1, 146] }, { 39: 147, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 148] }, { 39: 149, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 150] }, { 33: [1, 151] }, { 33: [1, 152] }, { 33: [1, 153] }, { 33: [1, 154] }, { 33: [1, 155] }, { 33: [1, 156] }, { 39: 157, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 158] }, { 33: [1, 159] }, { 33: [1, 160] }, { 39: 161, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 162] }, { 39: 163, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 164] }, { 33: [1, 165] }, { 33: [1, 166] }, { 39: 167, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 168] }, o($V9, [2, 43], { 35: [1, 169] }), o($V9, [2, 44], { 38: [1, 170] }), o($V9, [2, 42], { 32: [1, 171] }), o($V9, [2, 45], { 35: [1, 172] }), o($V9, [2, 40], { 38: [1, 173] }), o($V9, [2, 41], { 32: [1, 174] }), { 33: [1, 175], 36: [1, 176] }, { 33: [1, 177] }, { 33: [1, 178] }, { 33: [1, 179] }, { 33: [1, 180] }, o($V9, [2, 66], { 42: [1, 181] }), o($V9, [2, 79], { 32: [1, 182] }), o($V9, [2, 67], { 42: [1, 183] }), o($V9, [2, 90], { 38: [1, 184] }), o($V9, [2, 80], { 32: [1, 185] }), o($V9, [2, 89], { 38: [1, 186] }), o($V9, [2, 65], { 42: [1, 187] }), o($V9, [2, 78], { 32: [1, 188] }), o($V9, [2, 64], { 42: [1, 189] }), o($V9, [2, 84], { 35: [1, 190] }), o($V9, [2, 77], { 32: [1, 191] }), o($V9, [2, 83], { 35: [1, 192] }), o($V9, [2, 63], { 42: [1, 193] }), o($V9, [2, 91], { 38: [1, 194] }), o($V9, [2, 62], { 42: [1, 195] }), o($V9, [2, 85], { 35: [1, 196] }), o($V9, [2, 86], { 35: [1, 197] }), o($V9, [2, 92], { 38: [1, 198] }), o($V9, [2, 76], { 32: [1, 199] }), o($V9, [2, 87], { 38: [1, 200] }), o($V9, [2, 75], { 32: [1, 201] }), o($V9, [2, 81], { 35: [1, 202] }), o($V9, [2, 82], { 35: [1, 203] }), o($V9, [2, 88], { 38: [1, 204] }), { 33: [1, 205] }, { 39: 206, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 207] }, { 33: [1, 208] }, { 39: 209, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 210] }, o($V9, [2, 27]), o($V9, [2, 32]), o($V9, [2, 28]), o($V9, [2, 33]), o($V9, [2, 34]), o($V9, [2, 35]), { 33: [1, 211] }, { 33: [1, 212] }, { 33: [1, 213] }, { 39: 214, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 215] }, { 39: 216, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 217] }, { 33: [1, 218] }, { 33: [1, 219] }, { 33: [1, 220] }, { 33: [1, 221] }, { 33: [1, 222] }, { 33: [1, 223] }, { 39: 224, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 225] }, { 33: [1, 226] }, { 33: [1, 227] }, { 39: 228, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 229] }, { 39: 230, 43: $Vd, 44: $Ve, 45: $Vf }, { 33: [1, 231] }, { 33: [1, 232] }, { 33: [1, 233] }, { 39: 234, 43: $Vd, 44: $Ve, 45: $Vf }, o($V9, [2, 46]), o($V9, [2, 48]), o($V9, [2, 47]), o($V9, [2, 49]), o($V9, [2, 51]), o($V9, [2, 50]), o($V9, [2, 107]), o($V9, [2, 108]), o($V9, [2, 105]), o($V9, [2, 106]), o($V9, [2, 110]), o($V9, [2, 109]), o($V9, [2, 114]), o($V9, [2, 113]), o($V9, [2, 112]), o($V9, [2, 111]), o($V9, [2, 116]), o($V9, [2, 115]), o($V9, [2, 104]), o($V9, [2, 103]), o($V9, [2, 102]), o($V9, [2, 101]), o($V9, [2, 99]), o($V9, [2, 100]), o($V9, [2, 98]), o($V9, [2, 97]), o($V9, [2, 96]), o($V9, [2, 95]), o($V9, [2, 93]), o($V9, [2, 94])],\n    defaultActions: { 7: [2, 1], 13: [2, 2], 18: [2, 3], 36: [2, 4] },\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"acc_title\");\n            return 19;\n          case 1:\n            this.popState();\n            return \"acc_title_value\";\n          case 2:\n            this.begin(\"acc_descr\");\n            return 21;\n          case 3:\n            this.popState();\n            return \"acc_descr_value\";\n          case 4:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 5:\n            this.popState();\n            break;\n          case 6:\n            return \"acc_descr_multiline_value\";\n          case 7:\n            return 13;\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            return 5;\n          case 11:\n            return 40;\n          case 12:\n            return 32;\n          case 13:\n            return 38;\n          case 14:\n            return 42;\n          case 15:\n            return 43;\n          case 16:\n            return 44;\n          case 17:\n            return 45;\n          case 18:\n            return 35;\n          case 19:\n            return 28;\n          case 20:\n            return 29;\n          case 21:\n            return 37;\n          case 22:\n            return 31;\n          case 23:\n            return 34;\n          case 24:\n            return 26;\n          case 25:\n            return 9;\n          case 26:\n            return 9;\n          case 27:\n            return 8;\n          case 28:\n            return \"CARET\";\n          case 29:\n            this.begin(\"options\");\n            break;\n          case 30:\n            this.popState();\n            break;\n          case 31:\n            return 12;\n          case 32:\n            return 36;\n          case 33:\n            this.begin(\"string\");\n            break;\n          case 34:\n            this.popState();\n            break;\n          case 35:\n            return 33;\n          case 36:\n            return 30;\n          case 37:\n            return 46;\n          case 38:\n            return 7;\n        }\n      },\n      rules: [/^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:(\\r?\\n)+)/i, /^(?:#[^\\n]*)/i, /^(?:%[^\\n]*)/i, /^(?:gitGraph\\b)/i, /^(?:commit(?=\\s|$))/i, /^(?:id:)/i, /^(?:type:)/i, /^(?:msg:)/i, /^(?:NORMAL\\b)/i, /^(?:REVERSE\\b)/i, /^(?:HIGHLIGHT\\b)/i, /^(?:tag:)/i, /^(?:branch(?=\\s|$))/i, /^(?:order:)/i, /^(?:merge(?=\\s|$))/i, /^(?:cherry-pick(?=\\s|$))/i, /^(?:parent:)/i, /^(?:checkout(?=\\s|$))/i, /^(?:LR\\b)/i, /^(?:TB\\b)/i, /^(?::)/i, /^(?:\\^)/i, /^(?:options\\r?\\n)/i, /^(?:[ \\r\\n\\t]+end\\b)/i, /^(?:[\\s\\S]+(?=[ \\r\\n\\t]+end))/i, /^(?:[\"][\"])/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:[0-9]+(?=\\s|$))/i, /^(?:\\w([-\\./\\w]*[-\\w])?)/i, /^(?:$)/i, /^(?:\\s+)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [5, 6], \"inclusive\": false }, \"acc_descr\": { \"rules\": [3], \"inclusive\": false }, \"acc_title\": { \"rules\": [1], \"inclusive\": false }, \"options\": { \"rules\": [30, 31], \"inclusive\": false }, \"string\": { \"rules\": [34, 35], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 2, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 36, 37, 38, 39], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst gitGraphParser = parser;\nlet mainBranchName = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph.mainBranchName;\nlet mainBranchOrder = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph.mainBranchOrder;\nlet commits = {};\nlet head = null;\nlet branchesConfig = {};\nbranchesConfig[mainBranchName] = { name: mainBranchName, order: mainBranchOrder };\nlet branches = {};\nbranches[mainBranchName] = head;\nlet curBranch = mainBranchName;\nlet direction = \"LR\";\nlet seq = 0;\nfunction getId() {\n  return (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.y)({ length: 7 });\n}\nfunction uniqBy(list, fn) {\n  const recordMap = /* @__PURE__ */ Object.create(null);\n  return list.reduce((out, item) => {\n    const key = fn(item);\n    if (!recordMap[key]) {\n      recordMap[key] = true;\n      out.push(item);\n    }\n    return out;\n  }, []);\n}\nconst setDirection = function(dir2) {\n  direction = dir2;\n};\nlet options = {};\nconst setOptions = function(rawOptString) {\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"options str\", rawOptString);\n  rawOptString = rawOptString && rawOptString.trim();\n  rawOptString = rawOptString || \"{}\";\n  try {\n    options = JSON.parse(rawOptString);\n  } catch (e) {\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.error(\"error while parsing gitGraph options\", e.message);\n  }\n};\nconst getOptions = function() {\n  return options;\n};\nconst commit = function(msg, id, type, tag) {\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"Entering commit:\", msg, id, type, tag);\n  id = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(id, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  msg = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(msg, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  tag = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(tag, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  const commit2 = {\n    id: id ? id : seq + \"-\" + getId(),\n    message: msg,\n    seq: seq++,\n    type: type ? type : commitType$1.NORMAL,\n    tag: tag ? tag : \"\",\n    parents: head == null ? [] : [head.id],\n    branch: curBranch\n  };\n  head = commit2;\n  commits[commit2.id] = commit2;\n  branches[curBranch] = commit2.id;\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"in pushCommit \" + commit2.id);\n};\nconst branch = function(name, order) {\n  name = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(name, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  if (branches[name] === void 0) {\n    branches[name] = head != null ? head.id : null;\n    branchesConfig[name] = { name, order: order ? parseInt(order, 10) : null };\n    checkout(name);\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"in createBranch\");\n  } else {\n    let error = new Error(\n      'Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using \"checkout ' + name + '\")'\n    );\n    error.hash = {\n      text: \"branch \" + name,\n      token: \"branch \" + name,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: ['\"checkout ' + name + '\"']\n    };\n    throw error;\n  }\n};\nconst merge = function(otherBranch, custom_id, override_type, custom_tag) {\n  otherBranch = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(otherBranch, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  custom_id = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(custom_id, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  const currentCommit = commits[branches[curBranch]];\n  const otherCommit = commits[branches[otherBranch]];\n  if (curBranch === otherBranch) {\n    let error = new Error('Incorrect usage of \"merge\". Cannot merge a branch to itself');\n    error.hash = {\n      text: \"merge \" + otherBranch,\n      token: \"merge \" + otherBranch,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  } else if (currentCommit === void 0 || !currentCommit) {\n    let error = new Error(\n      'Incorrect usage of \"merge\". Current branch (' + curBranch + \")has no commits\"\n    );\n    error.hash = {\n      text: \"merge \" + otherBranch,\n      token: \"merge \" + otherBranch,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: [\"commit\"]\n    };\n    throw error;\n  } else if (branches[otherBranch] === void 0) {\n    let error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") does not exist\"\n    );\n    error.hash = {\n      text: \"merge \" + otherBranch,\n      token: \"merge \" + otherBranch,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: [\"branch \" + otherBranch]\n    };\n    throw error;\n  } else if (otherCommit === void 0 || !otherCommit) {\n    let error = new Error(\n      'Incorrect usage of \"merge\". Branch to be merged (' + otherBranch + \") has no commits\"\n    );\n    error.hash = {\n      text: \"merge \" + otherBranch,\n      token: \"merge \" + otherBranch,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: ['\"commit\"']\n    };\n    throw error;\n  } else if (currentCommit === otherCommit) {\n    let error = new Error('Incorrect usage of \"merge\". Both branches have same head');\n    error.hash = {\n      text: \"merge \" + otherBranch,\n      token: \"merge \" + otherBranch,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: [\"branch abc\"]\n    };\n    throw error;\n  } else if (custom_id && commits[custom_id] !== void 0) {\n    let error = new Error(\n      'Incorrect usage of \"merge\". Commit with id:' + custom_id + \" already exists, use different custom Id\"\n    );\n    error.hash = {\n      text: \"merge \" + otherBranch + custom_id + override_type + custom_tag,\n      token: \"merge \" + otherBranch + custom_id + override_type + custom_tag,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: [\n        \"merge \" + otherBranch + \" \" + custom_id + \"_UNIQUE \" + override_type + \" \" + custom_tag\n      ]\n    };\n    throw error;\n  }\n  const commit2 = {\n    id: custom_id ? custom_id : seq + \"-\" + getId(),\n    message: \"merged branch \" + otherBranch + \" into \" + curBranch,\n    seq: seq++,\n    parents: [head == null ? null : head.id, branches[otherBranch]],\n    branch: curBranch,\n    type: commitType$1.MERGE,\n    customType: override_type,\n    customId: custom_id ? true : false,\n    tag: custom_tag ? custom_tag : \"\"\n  };\n  head = commit2;\n  commits[commit2.id] = commit2;\n  branches[curBranch] = commit2.id;\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(branches);\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"in mergeBranch\");\n};\nconst cherryPick = function(sourceId, targetId, tag, parentCommitId) {\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"Entering cherryPick:\", sourceId, targetId, tag);\n  sourceId = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(sourceId, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  targetId = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(targetId, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  tag = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(tag, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  parentCommitId = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(parentCommitId, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  if (!sourceId || commits[sourceId] === void 0) {\n    let error = new Error(\n      'Incorrect usage of \"cherryPick\". Source commit id should exist and provided'\n    );\n    error.hash = {\n      text: \"cherryPick \" + sourceId + \" \" + targetId,\n      token: \"cherryPick \" + sourceId + \" \" + targetId,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: [\"cherry-pick abc\"]\n    };\n    throw error;\n  }\n  let sourceCommit = commits[sourceId];\n  let sourceCommitBranch = sourceCommit.branch;\n  if (parentCommitId && !(Array.isArray(sourceCommit.parents) && sourceCommit.parents.includes(parentCommitId))) {\n    let error = new Error(\n      \"Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.\"\n    );\n    throw error;\n  }\n  if (sourceCommit.type === commitType$1.MERGE && !parentCommitId) {\n    let error = new Error(\n      \"Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.\"\n    );\n    throw error;\n  }\n  if (!targetId || commits[targetId] === void 0) {\n    if (sourceCommitBranch === curBranch) {\n      let error = new Error(\n        'Incorrect usage of \"cherryPick\". Source commit is already on current branch'\n      );\n      error.hash = {\n        text: \"cherryPick \" + sourceId + \" \" + targetId,\n        token: \"cherryPick \" + sourceId + \" \" + targetId,\n        line: \"1\",\n        loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const currentCommit = commits[branches[curBranch]];\n    if (currentCommit === void 0 || !currentCommit) {\n      let error = new Error(\n        'Incorrect usage of \"cherry-pick\". Current branch (' + curBranch + \")has no commits\"\n      );\n      error.hash = {\n        text: \"cherryPick \" + sourceId + \" \" + targetId,\n        token: \"cherryPick \" + sourceId + \" \" + targetId,\n        line: \"1\",\n        loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n        expected: [\"cherry-pick abc\"]\n      };\n      throw error;\n    }\n    const commit2 = {\n      id: seq + \"-\" + getId(),\n      message: \"cherry-picked \" + sourceCommit + \" into \" + curBranch,\n      seq: seq++,\n      parents: [head == null ? null : head.id, sourceCommit.id],\n      branch: curBranch,\n      type: commitType$1.CHERRY_PICK,\n      tag: tag ?? `cherry-pick:${sourceCommit.id}${sourceCommit.type === commitType$1.MERGE ? `|parent:${parentCommitId}` : \"\"}`\n    };\n    head = commit2;\n    commits[commit2.id] = commit2;\n    branches[curBranch] = commit2.id;\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(branches);\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"in cherryPick\");\n  }\n};\nconst checkout = function(branch2) {\n  branch2 = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.e.sanitizeText(branch2, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  if (branches[branch2] === void 0) {\n    let error = new Error(\n      'Trying to checkout branch which is not yet created. (Help try using \"branch ' + branch2 + '\")'\n    );\n    error.hash = {\n      text: \"checkout \" + branch2,\n      token: \"checkout \" + branch2,\n      line: \"1\",\n      loc: { first_line: 1, last_line: 1, first_column: 1, last_column: 1 },\n      expected: ['\"branch ' + branch2 + '\"']\n    };\n    throw error;\n  } else {\n    curBranch = branch2;\n    const id = branches[curBranch];\n    head = commits[id];\n  }\n};\nfunction upsert(arr, key, newVal) {\n  const index = arr.indexOf(key);\n  if (index === -1) {\n    arr.push(newVal);\n  } else {\n    arr.splice(index, 1, newVal);\n  }\n}\nfunction prettyPrintCommitHistory(commitArr) {\n  const commit2 = commitArr.reduce((out, commit3) => {\n    if (out.seq > commit3.seq) {\n      return out;\n    }\n    return commit3;\n  }, commitArr[0]);\n  let line = \"\";\n  commitArr.forEach(function(c) {\n    if (c === commit2) {\n      line += \"\t*\";\n    } else {\n      line += \"\t|\";\n    }\n  });\n  const label = [line, commit2.id, commit2.seq];\n  for (let branch2 in branches) {\n    if (branches[branch2] === commit2.id) {\n      label.push(branch2);\n    }\n  }\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(label.join(\" \"));\n  if (commit2.parents && commit2.parents.length == 2) {\n    const newCommit = commits[commit2.parents[0]];\n    upsert(commitArr, commit2, newCommit);\n    commitArr.push(commits[commit2.parents[1]]);\n  } else if (commit2.parents.length == 0) {\n    return;\n  } else {\n    const nextCommit = commits[commit2.parents];\n    upsert(commitArr, commit2, nextCommit);\n  }\n  commitArr = uniqBy(commitArr, (c) => c.id);\n  prettyPrintCommitHistory(commitArr);\n}\nconst prettyPrint = function() {\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(commits);\n  const node = getCommitsArray()[0];\n  prettyPrintCommitHistory([node]);\n};\nconst clear$1 = function() {\n  commits = {};\n  head = null;\n  let mainBranch = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph.mainBranchName;\n  let mainBranchOrder2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph.mainBranchOrder;\n  branches = {};\n  branches[mainBranch] = null;\n  branchesConfig = {};\n  branchesConfig[mainBranch] = { name: mainBranch, order: mainBranchOrder2 };\n  curBranch = mainBranch;\n  seq = 0;\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.v)();\n};\nconst getBranchesAsObjArray = function() {\n  const branchesArray = Object.values(branchesConfig).map((branchConfig, i) => {\n    if (branchConfig.order !== null) {\n      return branchConfig;\n    }\n    return {\n      ...branchConfig,\n      order: parseFloat(`0.${i}`, 10)\n    };\n  }).sort((a, b) => a.order - b.order).map(({ name }) => ({ name }));\n  return branchesArray;\n};\nconst getBranches = function() {\n  return branches;\n};\nconst getCommits = function() {\n  return commits;\n};\nconst getCommitsArray = function() {\n  const commitArr = Object.keys(commits).map(function(key) {\n    return commits[key];\n  });\n  commitArr.forEach(function(o) {\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(o.id);\n  });\n  commitArr.sort((a, b) => a.seq - b.seq);\n  return commitArr;\n};\nconst getCurrentBranch = function() {\n  return curBranch;\n};\nconst getDirection = function() {\n  return direction;\n};\nconst getHead = function() {\n  return head;\n};\nconst commitType$1 = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4\n};\nconst gitGraphDb = {\n  getConfig: () => (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph,\n  setDirection,\n  setOptions,\n  getOptions,\n  commit,\n  branch,\n  merge,\n  cherryPick,\n  checkout,\n  //reset,\n  prettyPrint,\n  clear: clear$1,\n  getBranchesAsObjArray,\n  getBranches,\n  getCommits,\n  getCommitsArray,\n  getCurrentBranch,\n  getDirection,\n  getHead,\n  setAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.s,\n  getAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.g,\n  getAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  setAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.b,\n  setDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.q,\n  getDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.t,\n  commitType: commitType$1\n};\nlet allCommitsDict = {};\nconst commitType = {\n  NORMAL: 0,\n  REVERSE: 1,\n  HIGHLIGHT: 2,\n  MERGE: 3,\n  CHERRY_PICK: 4\n};\nconst THEME_COLOR_LIMIT = 8;\nlet branchPos = {};\nlet commitPos = {};\nlet lanes = [];\nlet maxPos = 0;\nlet dir = \"LR\";\nconst clear = () => {\n  branchPos = {};\n  commitPos = {};\n  allCommitsDict = {};\n  maxPos = 0;\n  lanes = [];\n  dir = \"LR\";\n};\nconst drawText = (txt) => {\n  const svgLabel = document.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n  let rows = [];\n  if (typeof txt === \"string\") {\n    rows = txt.split(/\\\\n|\\n|<br\\s*\\/?>/gi);\n  } else if (Array.isArray(txt)) {\n    rows = txt;\n  } else {\n    rows = [];\n  }\n  for (const row of rows) {\n    const tspan = document.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n    tspan.setAttributeNS(\"http://www.w3.org/XML/1998/namespace\", \"xml:space\", \"preserve\");\n    tspan.setAttribute(\"dy\", \"1em\");\n    tspan.setAttribute(\"x\", \"0\");\n    tspan.setAttribute(\"class\", \"row\");\n    tspan.textContent = row.trim();\n    svgLabel.appendChild(tspan);\n  }\n  return svgLabel;\n};\nconst findClosestParent = (parents) => {\n  let closestParent = \"\";\n  let maxPosition = 0;\n  parents.forEach((parent) => {\n    const parentPosition = dir === \"TB\" ? commitPos[parent].y : commitPos[parent].x;\n    if (parentPosition >= maxPosition) {\n      closestParent = parent;\n      maxPosition = parentPosition;\n    }\n  });\n  return closestParent || void 0;\n};\nconst drawCommits = (svg, commits2, modifyGraph) => {\n  const gitGraphConfig = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph;\n  const gBullets = svg.append(\"g\").attr(\"class\", \"commit-bullets\");\n  const gLabels = svg.append(\"g\").attr(\"class\", \"commit-labels\");\n  let pos = 0;\n  if (dir === \"TB\") {\n    pos = 30;\n  }\n  const keys = Object.keys(commits2);\n  const sortedKeys = keys.sort((a, b) => {\n    return commits2[a].seq - commits2[b].seq;\n  });\n  const isParallelCommits = gitGraphConfig.parallelCommits;\n  const layoutOffset = 10;\n  const commitStep = 40;\n  sortedKeys.forEach((key) => {\n    const commit2 = commits2[key];\n    if (isParallelCommits) {\n      if (commit2.parents.length) {\n        const closestParent = findClosestParent(commit2.parents);\n        pos = dir === \"TB\" ? commitPos[closestParent].y + commitStep : commitPos[closestParent].x + commitStep;\n      } else {\n        pos = 0;\n        if (dir === \"TB\") {\n          pos = 30;\n        }\n      }\n    }\n    const posWithOffset = pos + layoutOffset;\n    const y = dir === \"TB\" ? posWithOffset : branchPos[commit2.branch].pos;\n    const x = dir === \"TB\" ? branchPos[commit2.branch].pos : posWithOffset;\n    if (modifyGraph) {\n      let typeClass;\n      let commitSymbolType = commit2.customType !== void 0 && commit2.customType !== \"\" ? commit2.customType : commit2.type;\n      switch (commitSymbolType) {\n        case commitType.NORMAL:\n          typeClass = \"commit-normal\";\n          break;\n        case commitType.REVERSE:\n          typeClass = \"commit-reverse\";\n          break;\n        case commitType.HIGHLIGHT:\n          typeClass = \"commit-highlight\";\n          break;\n        case commitType.MERGE:\n          typeClass = \"commit-merge\";\n          break;\n        case commitType.CHERRY_PICK:\n          typeClass = \"commit-cherry-pick\";\n          break;\n        default:\n          typeClass = \"commit-normal\";\n      }\n      if (commitSymbolType === commitType.HIGHLIGHT) {\n        const circle = gBullets.append(\"rect\");\n        circle.attr(\"x\", x - 10);\n        circle.attr(\"y\", y - 10);\n        circle.attr(\"height\", 20);\n        circle.attr(\"width\", 20);\n        circle.attr(\n          \"class\",\n          `commit ${commit2.id} commit-highlight${branchPos[commit2.branch].index % THEME_COLOR_LIMIT} ${typeClass}-outer`\n        );\n        gBullets.append(\"rect\").attr(\"x\", x - 6).attr(\"y\", y - 6).attr(\"height\", 12).attr(\"width\", 12).attr(\n          \"class\",\n          `commit ${commit2.id} commit${branchPos[commit2.branch].index % THEME_COLOR_LIMIT} ${typeClass}-inner`\n        );\n      } else if (commitSymbolType === commitType.CHERRY_PICK) {\n        gBullets.append(\"circle\").attr(\"cx\", x).attr(\"cy\", y).attr(\"r\", 10).attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n        gBullets.append(\"circle\").attr(\"cx\", x - 3).attr(\"cy\", y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n        gBullets.append(\"circle\").attr(\"cx\", x + 3).attr(\"cy\", y + 2).attr(\"r\", 2.75).attr(\"fill\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n        gBullets.append(\"line\").attr(\"x1\", x + 3).attr(\"y1\", y + 1).attr(\"x2\", x).attr(\"y2\", y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n        gBullets.append(\"line\").attr(\"x1\", x - 3).attr(\"y1\", y + 1).attr(\"x2\", x).attr(\"y2\", y - 5).attr(\"stroke\", \"#fff\").attr(\"class\", `commit ${commit2.id} ${typeClass}`);\n      } else {\n        const circle = gBullets.append(\"circle\");\n        circle.attr(\"cx\", x);\n        circle.attr(\"cy\", y);\n        circle.attr(\"r\", commit2.type === commitType.MERGE ? 9 : 10);\n        circle.attr(\n          \"class\",\n          `commit ${commit2.id} commit${branchPos[commit2.branch].index % THEME_COLOR_LIMIT}`\n        );\n        if (commitSymbolType === commitType.MERGE) {\n          const circle2 = gBullets.append(\"circle\");\n          circle2.attr(\"cx\", x);\n          circle2.attr(\"cy\", y);\n          circle2.attr(\"r\", 6);\n          circle2.attr(\n            \"class\",\n            `commit ${typeClass} ${commit2.id} commit${branchPos[commit2.branch].index % THEME_COLOR_LIMIT}`\n          );\n        }\n        if (commitSymbolType === commitType.REVERSE) {\n          const cross = gBullets.append(\"path\");\n          cross.attr(\"d\", `M ${x - 5},${y - 5}L${x + 5},${y + 5}M${x - 5},${y + 5}L${x + 5},${y - 5}`).attr(\n            \"class\",\n            `commit ${typeClass} ${commit2.id} commit${branchPos[commit2.branch].index % THEME_COLOR_LIMIT}`\n          );\n        }\n      }\n    }\n    if (dir === \"TB\") {\n      commitPos[commit2.id] = { x, y: posWithOffset };\n    } else {\n      commitPos[commit2.id] = { x: posWithOffset, y };\n    }\n    if (modifyGraph) {\n      const px = 4;\n      const py = 2;\n      if (commit2.type !== commitType.CHERRY_PICK && (commit2.customId && commit2.type === commitType.MERGE || commit2.type !== commitType.MERGE) && gitGraphConfig.showCommitLabel) {\n        const wrapper = gLabels.append(\"g\");\n        const labelBkg = wrapper.insert(\"rect\").attr(\"class\", \"commit-label-bkg\");\n        const text = wrapper.append(\"text\").attr(\"x\", pos).attr(\"y\", y + 25).attr(\"class\", \"commit-label\").text(commit2.id);\n        let bbox = text.node().getBBox();\n        labelBkg.attr(\"x\", posWithOffset - bbox.width / 2 - py).attr(\"y\", y + 13.5).attr(\"width\", bbox.width + 2 * py).attr(\"height\", bbox.height + 2 * py);\n        if (dir === \"TB\") {\n          labelBkg.attr(\"x\", x - (bbox.width + 4 * px + 5)).attr(\"y\", y - 12);\n          text.attr(\"x\", x - (bbox.width + 4 * px)).attr(\"y\", y + bbox.height - 12);\n        }\n        if (dir !== \"TB\") {\n          text.attr(\"x\", posWithOffset - bbox.width / 2);\n        }\n        if (gitGraphConfig.rotateCommitLabel) {\n          if (dir === \"TB\") {\n            text.attr(\"transform\", \"rotate(-45, \" + x + \", \" + y + \")\");\n            labelBkg.attr(\"transform\", \"rotate(-45, \" + x + \", \" + y + \")\");\n          } else {\n            let r_x = -7.5 - (bbox.width + 10) / 25 * 9.5;\n            let r_y = 10 + bbox.width / 25 * 8.5;\n            wrapper.attr(\n              \"transform\",\n              \"translate(\" + r_x + \", \" + r_y + \") rotate(-45, \" + pos + \", \" + y + \")\"\n            );\n          }\n        }\n      }\n      if (commit2.tag) {\n        const rect = gLabels.insert(\"polygon\");\n        const hole = gLabels.append(\"circle\");\n        const tag = gLabels.append(\"text\").attr(\"y\", y - 16).attr(\"class\", \"tag-label\").text(commit2.tag);\n        let tagBbox = tag.node().getBBox();\n        tag.attr(\"x\", posWithOffset - tagBbox.width / 2);\n        const h2 = tagBbox.height / 2;\n        const ly = y - 19.2;\n        rect.attr(\"class\", \"tag-label-bkg\").attr(\n          \"points\",\n          `\n          ${pos - tagBbox.width / 2 - px / 2},${ly + py}\n          ${pos - tagBbox.width / 2 - px / 2},${ly - py}\n          ${posWithOffset - tagBbox.width / 2 - px},${ly - h2 - py}\n          ${posWithOffset + tagBbox.width / 2 + px},${ly - h2 - py}\n          ${posWithOffset + tagBbox.width / 2 + px},${ly + h2 + py}\n          ${posWithOffset - tagBbox.width / 2 - px},${ly + h2 + py}`\n        );\n        hole.attr(\"cx\", pos - tagBbox.width / 2 + px / 2).attr(\"cy\", ly).attr(\"r\", 1.5).attr(\"class\", \"tag-hole\");\n        if (dir === \"TB\") {\n          rect.attr(\"class\", \"tag-label-bkg\").attr(\n            \"points\",\n            `\n            ${x},${pos + py}\n            ${x},${pos - py}\n            ${x + layoutOffset},${pos - h2 - py}\n            ${x + layoutOffset + tagBbox.width + px},${pos - h2 - py}\n            ${x + layoutOffset + tagBbox.width + px},${pos + h2 + py}\n            ${x + layoutOffset},${pos + h2 + py}`\n          ).attr(\"transform\", \"translate(12,12) rotate(45, \" + x + \",\" + pos + \")\");\n          hole.attr(\"cx\", x + px / 2).attr(\"cy\", pos).attr(\"transform\", \"translate(12,12) rotate(45, \" + x + \",\" + pos + \")\");\n          tag.attr(\"x\", x + 5).attr(\"y\", pos + 3).attr(\"transform\", \"translate(14,14) rotate(45, \" + x + \",\" + pos + \")\");\n        }\n      }\n    }\n    pos += commitStep + layoutOffset;\n    if (pos > maxPos) {\n      maxPos = pos;\n    }\n  });\n};\nconst shouldRerouteArrow = (commitA, commitB, p1, p2, allCommits) => {\n  const commitBIsFurthest = dir === \"TB\" ? p1.x < p2.x : p1.y < p2.y;\n  const branchToGetCurve = commitBIsFurthest ? commitB.branch : commitA.branch;\n  const isOnBranchToGetCurve = (x) => x.branch === branchToGetCurve;\n  const isBetweenCommits = (x) => x.seq > commitA.seq && x.seq < commitB.seq;\n  return Object.values(allCommits).some((commitX) => {\n    return isBetweenCommits(commitX) && isOnBranchToGetCurve(commitX);\n  });\n};\nconst findLane = (y1, y2, depth = 0) => {\n  const candidate = y1 + Math.abs(y1 - y2) / 2;\n  if (depth > 5) {\n    return candidate;\n  }\n  let ok = lanes.every((lane) => Math.abs(lane - candidate) >= 10);\n  if (ok) {\n    lanes.push(candidate);\n    return candidate;\n  }\n  const diff = Math.abs(y1 - y2);\n  return findLane(y1, y2 - diff / 5, depth + 1);\n};\nconst drawArrow = (svg, commitA, commitB, allCommits) => {\n  const p1 = commitPos[commitA.id];\n  const p2 = commitPos[commitB.id];\n  const arrowNeedsRerouting = shouldRerouteArrow(commitA, commitB, p1, p2, allCommits);\n  let arc = \"\";\n  let arc2 = \"\";\n  let radius = 0;\n  let offset = 0;\n  let colorClassNum = branchPos[commitB.branch].index;\n  if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n    colorClassNum = branchPos[commitA.branch].index;\n  }\n  let lineDef;\n  if (arrowNeedsRerouting) {\n    arc = \"A 10 10, 0, 0, 0,\";\n    arc2 = \"A 10 10, 0, 0, 1,\";\n    radius = 10;\n    offset = 10;\n    const lineY = p1.y < p2.y ? findLane(p1.y, p2.y) : findLane(p2.y, p1.y);\n    const lineX = p1.x < p2.x ? findLane(p1.x, p2.x) : findLane(p2.x, p1.x);\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX - radius} ${p1.y} ${arc2} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc} ${lineX + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos[commitA.branch].index;\n        lineDef = `M ${p1.x} ${p1.y} L ${lineX + radius} ${p1.y} ${arc} ${lineX} ${p1.y + offset} L ${lineX} ${p2.y - radius} ${arc2} ${lineX - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY - radius} ${arc} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc2} ${p2.x} ${lineY + offset} L ${p2.x} ${p2.y}`;\n      } else {\n        colorClassNum = branchPos[commitA.branch].index;\n        lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${lineY + radius} ${arc2} ${p1.x + offset} ${lineY} L ${p2.x - radius} ${lineY} ${arc} ${p2.x} ${lineY - offset} L ${p2.x} ${p2.y}`;\n      }\n    }\n  } else {\n    arc = \"A 20 20, 0, 0, 0,\";\n    arc2 = \"A 20 20, 0, 0, 1,\";\n    radius = 20;\n    offset = 20;\n    if (dir === \"TB\") {\n      if (p1.x < p2.x) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x > p2.x) {\n        arc = \"A 20 20, 0, 0, 0,\";\n        arc2 = \"A 20 20, 0, 0, 1,\";\n        radius = 20;\n        offset = 20;\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc2} ${p1.x - offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x + radius} ${p1.y} ${arc} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.x === p2.x) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    } else {\n      if (p1.y < p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc2} ${p2.x} ${p1.y + offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y - radius} ${arc} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y > p2.y) {\n        if (commitB.type === commitType.MERGE && commitA.id !== commitB.parents[0]) {\n          lineDef = `M ${p1.x} ${p1.y} L ${p2.x - radius} ${p1.y} ${arc} ${p2.x} ${p1.y - offset} L ${p2.x} ${p2.y}`;\n        } else {\n          lineDef = `M ${p1.x} ${p1.y} L ${p1.x} ${p2.y + radius} ${arc2} ${p1.x + offset} ${p2.y} L ${p2.x} ${p2.y}`;\n        }\n      }\n      if (p1.y === p2.y) {\n        lineDef = `M ${p1.x} ${p1.y} L ${p2.x} ${p2.y}`;\n      }\n    }\n  }\n  svg.append(\"path\").attr(\"d\", lineDef).attr(\"class\", \"arrow arrow\" + colorClassNum % THEME_COLOR_LIMIT);\n};\nconst drawArrows = (svg, commits2) => {\n  const gArrows = svg.append(\"g\").attr(\"class\", \"commit-arrows\");\n  Object.keys(commits2).forEach((key) => {\n    const commit2 = commits2[key];\n    if (commit2.parents && commit2.parents.length > 0) {\n      commit2.parents.forEach((parent) => {\n        drawArrow(gArrows, commits2[parent], commit2, commits2);\n      });\n    }\n  });\n};\nconst drawBranches = (svg, branches2) => {\n  const gitGraphConfig = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)().gitGraph;\n  const g = svg.append(\"g\");\n  branches2.forEach((branch2, index) => {\n    const adjustIndexForTheme = index % THEME_COLOR_LIMIT;\n    const pos = branchPos[branch2.name].pos;\n    const line = g.append(\"line\");\n    line.attr(\"x1\", 0);\n    line.attr(\"y1\", pos);\n    line.attr(\"x2\", maxPos);\n    line.attr(\"y2\", pos);\n    line.attr(\"class\", \"branch branch\" + adjustIndexForTheme);\n    if (dir === \"TB\") {\n      line.attr(\"y1\", 30);\n      line.attr(\"x1\", pos);\n      line.attr(\"y2\", maxPos);\n      line.attr(\"x2\", pos);\n    }\n    lanes.push(pos);\n    let name = branch2.name;\n    const labelElement = drawText(name);\n    const bkg = g.insert(\"rect\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\" + adjustIndexForTheme);\n    label.node().appendChild(labelElement);\n    let bbox = labelElement.getBBox();\n    bkg.attr(\"class\", \"branchLabelBkg label\" + adjustIndexForTheme).attr(\"rx\", 4).attr(\"ry\", 4).attr(\"x\", -bbox.width - 4 - (gitGraphConfig.rotateCommitLabel === true ? 30 : 0)).attr(\"y\", -bbox.height / 2 + 8).attr(\"width\", bbox.width + 18).attr(\"height\", bbox.height + 4);\n    label.attr(\n      \"transform\",\n      \"translate(\" + (-bbox.width - 14 - (gitGraphConfig.rotateCommitLabel === true ? 30 : 0)) + \", \" + (pos - bbox.height / 2 - 1) + \")\"\n    );\n    if (dir === \"TB\") {\n      bkg.attr(\"x\", pos - bbox.width / 2 - 10).attr(\"y\", 0);\n      label.attr(\"transform\", \"translate(\" + (pos - bbox.width / 2 - 5) + \", 0)\");\n    }\n    if (dir !== \"TB\") {\n      bkg.attr(\"transform\", \"translate(-19, \" + (pos - bbox.height / 2) + \")\");\n    }\n  });\n};\nconst draw = function(txt, id, ver, diagObj) {\n  clear();\n  const conf = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)();\n  const gitGraphConfig = conf.gitGraph;\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"in gitgraph renderer\", txt + \"\\n\", \"id:\", id, ver);\n  allCommitsDict = diagObj.db.getCommits();\n  const branches2 = diagObj.db.getBranchesAsObjArray();\n  dir = diagObj.db.getDirection();\n  const diagram2 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(`[id=\"${id}\"]`);\n  let pos = 0;\n  branches2.forEach((branch2, index) => {\n    const labelElement = drawText(branch2.name);\n    const g = diagram2.append(\"g\");\n    const branchLabel = g.insert(\"g\").attr(\"class\", \"branchLabel\");\n    const label = branchLabel.insert(\"g\").attr(\"class\", \"label branch-label\");\n    label.node().appendChild(labelElement);\n    let bbox = labelElement.getBBox();\n    branchPos[branch2.name] = { pos, index };\n    pos += 50 + (gitGraphConfig.rotateCommitLabel ? 40 : 0) + (dir === \"TB\" ? bbox.width / 2 : 0);\n    label.remove();\n    branchLabel.remove();\n    g.remove();\n  });\n  drawCommits(diagram2, allCommitsDict, false);\n  if (gitGraphConfig.showBranches) {\n    drawBranches(diagram2, branches2);\n  }\n  drawArrows(diagram2, allCommitsDict);\n  drawCommits(diagram2, allCommitsDict, true);\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.u.insertTitle(\n    diagram2,\n    \"gitTitleText\",\n    gitGraphConfig.titleTopMargin,\n    diagObj.db.getDiagramTitle()\n  );\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.z)(\n    void 0,\n    diagram2,\n    gitGraphConfig.diagramPadding,\n    gitGraphConfig.useMaxWidth ?? conf.useMaxWidth\n  );\n};\nconst gitGraphRenderer = {\n  draw\n};\nconst getStyles = (options2) => `\n  .commit-id,\n  .commit-msg,\n  .branch-label {\n    fill: lightgrey;\n    color: lightgrey;\n    font-family: 'trebuchet ms', verdana, arial, sans-serif;\n    font-family: var(--mermaid-font-family);\n  }\n  ${[0, 1, 2, 3, 4, 5, 6, 7].map(\n  (i) => `\n        .branch-label${i} { fill: ${options2[\"gitBranchLabel\" + i]}; }\n        .commit${i} { stroke: ${options2[\"git\" + i]}; fill: ${options2[\"git\" + i]}; }\n        .commit-highlight${i} { stroke: ${options2[\"gitInv\" + i]}; fill: ${options2[\"gitInv\" + i]}; }\n        .label${i}  { fill: ${options2[\"git\" + i]}; }\n        .arrow${i} { stroke: ${options2[\"git\" + i]}; }\n        `\n).join(\"\\n\")}\n\n  .branch {\n    stroke-width: 1;\n    stroke: ${options2.lineColor};\n    stroke-dasharray: 2;\n  }\n  .commit-label { font-size: ${options2.commitLabelFontSize}; fill: ${options2.commitLabelColor};}\n  .commit-label-bkg { font-size: ${options2.commitLabelFontSize}; fill: ${options2.commitLabelBackground}; opacity: 0.5; }\n  .tag-label { font-size: ${options2.tagLabelFontSize}; fill: ${options2.tagLabelColor};}\n  .tag-label-bkg { fill: ${options2.tagLabelBackground}; stroke: ${options2.tagLabelBorder}; }\n  .tag-hole { fill: ${options2.textColor}; }\n\n  .commit-merge {\n    stroke: ${options2.primaryColor};\n    fill: ${options2.primaryColor};\n  }\n  .commit-reverse {\n    stroke: ${options2.primaryColor};\n    fill: ${options2.primaryColor};\n    stroke-width: 3;\n  }\n  .commit-highlight-outer {\n  }\n  .commit-highlight-inner {\n    stroke: ${options2.primaryColor};\n    fill: ${options2.primaryColor};\n  }\n\n  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}\n  .gitTitleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options2.textColor};\n  }\n`;\nconst gitGraphStyles = getStyles;\nconst diagram = {\n  parser: gitGraphParser,\n  db: gitGraphDb,\n  renderer: gitGraphRenderer,\n  styles: gitGraphStyles\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/gitGraphDiagram-72cf32ee.js\n"));

/***/ })

}]);