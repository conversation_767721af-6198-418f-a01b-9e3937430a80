"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_timeline-definition-85554ec2_js"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/timeline-definition-85554ec2.js":
/*!*******************************************************************!*\
  !*** ./node_modules/mermaid/dist/timeline-definition-85554ec2.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/is_dark.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/lighten.js\");\n/* harmony import */ var khroma__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! khroma */ \"(app-pages-browser)/./node_modules/khroma/dist/methods/darken.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [6, 8, 10, 11, 12, 14, 16, 17, 20, 21], $V1 = [1, 9], $V2 = [1, 10], $V3 = [1, 11], $V4 = [1, 12], $V5 = [1, 13], $V6 = [1, 16], $V7 = [1, 17];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"timeline\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NEWLINE\": 10, \"title\": 11, \"acc_title\": 12, \"acc_title_value\": 13, \"acc_descr\": 14, \"acc_descr_value\": 15, \"acc_descr_multiline_value\": 16, \"section\": 17, \"period_statement\": 18, \"event_statement\": 19, \"period\": 20, \"event\": 21, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"timeline\", 6: \"EOF\", 8: \"SPACE\", 10: \"NEWLINE\", 11: \"title\", 12: \"acc_title\", 13: \"acc_title_value\", 14: \"acc_descr\", 15: \"acc_descr_value\", 16: \"acc_descr_multiline_value\", 17: \"section\", 20: \"period\", 21: \"event\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 1], [18, 1], [19, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.getCommonDb().setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccTitle(this.$);\n          break;\n        case 10:\n        case 11:\n          this.$ = $$[$0].trim();\n          yy.getCommonDb().setAccDescription(this.$);\n          break;\n        case 12:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 15:\n          yy.addTask($$[$0], 0, \"\");\n          this.$ = $$[$0];\n          break;\n        case 16:\n          yy.addEvent($$[$0].substr(2));\n          this.$ = $$[$0];\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 18, 11: $V1, 12: $V2, 14: $V3, 16: $V4, 17: $V5, 18: 14, 19: 15, 20: $V6, 21: $V7 }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 8]), { 13: [1, 19] }, { 15: [1, 20] }, o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), o($V0, [2, 15]), o($V0, [2, 16]), o($V0, [2, 4]), o($V0, [2, 9]), o($V0, [2, 10])],\n    defaultActions: {},\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 10;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            return 4;\n          case 6:\n            return 11;\n          case 7:\n            this.begin(\"acc_title\");\n            return 12;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n          case 9:\n            this.begin(\"acc_descr\");\n            return 14;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n          case 14:\n            return 17;\n          case 15:\n            return 21;\n          case 16:\n            return 20;\n          case 17:\n            return 6;\n          case 18:\n            return \"INVALID\";\n        }\n      },\n      rules: [/^(?:%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:#[^\\n]*)/i, /^(?:timeline\\b)/i, /^(?:title\\s[^#\\n;]+)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:section\\s[^#:\\n;]+)/i, /^(?::\\s[^#:\\n;]+)/i, /^(?:[^#:\\n;]+)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nlet currentSection = \"\";\nlet currentTaskId = 0;\nconst sections = [];\nconst tasks = [];\nconst rawTasks = [];\nconst getCommonDb = () => _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.L;\nconst clear = function() {\n  sections.length = 0;\n  tasks.length = 0;\n  currentSection = \"\";\n  rawTasks.length = 0;\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.v)();\n};\nconst addSection = function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\nconst getSections = function() {\n  return sections;\n};\nconst getTasks = function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 100;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks.push(...rawTasks);\n  return tasks;\n};\nconst addTask = function(period, length, event) {\n  const rawTask = {\n    id: currentTaskId++,\n    section: currentSection,\n    type: currentSection,\n    task: period,\n    score: length ? length : 0,\n    //if event is defined, then add it the events array\n    events: event ? [event] : []\n  };\n  rawTasks.push(rawTask);\n};\nconst addEvent = function(event) {\n  const currentTask = rawTasks.find((task) => task.id === currentTaskId - 1);\n  currentTask.events.push(event);\n};\nconst addTaskOrg = function(descr) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  tasks.push(newTask);\n};\nconst compileTasks = function() {\n  const compileTask = function(pos) {\n    return rawTasks[pos].processed;\n  };\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\nconst timelineDb = {\n  clear,\n  getCommonDb,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  addTaskOrg,\n  addEvent\n};\nconst db = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  addEvent,\n  addSection,\n  addTask,\n  addTaskOrg,\n  clear,\n  default: timelineDb,\n  getCommonDb,\n  getSections,\n  getTasks\n}, Symbol.toStringTag, { value: \"Module\" }));\nconst MAX_SECTIONS = 12;\nconst drawRect = function(elem, rectData) {\n  const rectElem = elem.append(\"rect\");\n  rectElem.attr(\"x\", rectData.x);\n  rectElem.attr(\"y\", rectData.y);\n  rectElem.attr(\"fill\", rectData.fill);\n  rectElem.attr(\"stroke\", rectData.stroke);\n  rectElem.attr(\"width\", rectData.width);\n  rectElem.attr(\"height\", rectData.height);\n  rectElem.attr(\"rx\", rectData.rx);\n  rectElem.attr(\"ry\", rectData.ry);\n  if (rectData.class !== void 0) {\n    rectElem.attr(\"class\", rectData.class);\n  }\n  return rectElem;\n};\nconst drawFace = function(element, faceData) {\n  const radius = 15;\n  const circleElement = element.append(\"circle\").attr(\"cx\", faceData.cx).attr(\"cy\", faceData.cy).attr(\"class\", \"face\").attr(\"r\", radius).attr(\"stroke-width\", 2).attr(\"overflow\", \"visible\");\n  const face = element.append(\"g\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx - radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  face.append(\"circle\").attr(\"cx\", faceData.cx + radius / 3).attr(\"cy\", faceData.cy - radius / 3).attr(\"r\", 1.5).attr(\"stroke-width\", 2).attr(\"fill\", \"#666\").attr(\"stroke\", \"#666\");\n  function smile(face2) {\n    const arc$1 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.arc)().startAngle(Math.PI / 2).endAngle(3 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc$1).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 2) + \")\");\n  }\n  function sad(face2) {\n    const arc$1 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.arc)().startAngle(3 * Math.PI / 2).endAngle(5 * (Math.PI / 2)).innerRadius(radius / 2).outerRadius(radius / 2.2);\n    face2.append(\"path\").attr(\"class\", \"mouth\").attr(\"d\", arc$1).attr(\"transform\", \"translate(\" + faceData.cx + \",\" + (faceData.cy + 7) + \")\");\n  }\n  function ambivalent(face2) {\n    face2.append(\"line\").attr(\"class\", \"mouth\").attr(\"stroke\", 2).attr(\"x1\", faceData.cx - 5).attr(\"y1\", faceData.cy + 7).attr(\"x2\", faceData.cx + 5).attr(\"y2\", faceData.cy + 7).attr(\"class\", \"mouth\").attr(\"stroke-width\", \"1px\").attr(\"stroke\", \"#666\");\n  }\n  if (faceData.score > 3) {\n    smile(face);\n  } else if (faceData.score < 3) {\n    sad(face);\n  } else {\n    ambivalent(face);\n  }\n  return circleElement;\n};\nconst drawCircle = function(element, circleData) {\n  const circleElement = element.append(\"circle\");\n  circleElement.attr(\"cx\", circleData.cx);\n  circleElement.attr(\"cy\", circleData.cy);\n  circleElement.attr(\"class\", \"actor-\" + circleData.pos);\n  circleElement.attr(\"fill\", circleData.fill);\n  circleElement.attr(\"stroke\", circleData.stroke);\n  circleElement.attr(\"r\", circleData.r);\n  if (circleElement.class !== void 0) {\n    circleElement.attr(\"class\", circleElement.class);\n  }\n  if (circleData.title !== void 0) {\n    circleElement.append(\"title\").text(circleData.title);\n  }\n  return circleElement;\n};\nconst drawText = function(elem, textData) {\n  const nText = textData.text.replace(/<br\\s*\\/?>/gi, \" \");\n  const textElem = elem.append(\"text\");\n  textElem.attr(\"x\", textData.x);\n  textElem.attr(\"y\", textData.y);\n  textElem.attr(\"class\", \"legend\");\n  textElem.style(\"text-anchor\", textData.anchor);\n  if (textData.class !== void 0) {\n    textElem.attr(\"class\", textData.class);\n  }\n  const span = textElem.append(\"tspan\");\n  span.attr(\"x\", textData.x + textData.textMargin * 2);\n  span.text(nText);\n  return textElem;\n};\nconst drawLabel = function(elem, txtObject) {\n  function genPoints(x, y, width, height, cut) {\n    return x + \",\" + y + \" \" + (x + width) + \",\" + y + \" \" + (x + width) + \",\" + (y + height - cut) + \" \" + (x + width - cut * 1.2) + \",\" + (y + height) + \" \" + x + \",\" + (y + height);\n  }\n  const polygon = elem.append(\"polygon\");\n  polygon.attr(\"points\", genPoints(txtObject.x, txtObject.y, 50, 20, 7));\n  polygon.attr(\"class\", \"labelBox\");\n  txtObject.y = txtObject.y + txtObject.labelMargin;\n  txtObject.x = txtObject.x + 0.5 * txtObject.labelMargin;\n  drawText(elem, txtObject);\n};\nconst drawSection = function(elem, section, conf) {\n  const g = elem.append(\"g\");\n  const rect = getNoteRect();\n  rect.x = section.x;\n  rect.y = section.y;\n  rect.fill = section.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"journey-section section-type-\" + section.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  _drawTextCandidateFunc(conf)(\n    section.text,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"journey-section section-type-\" + section.num },\n    conf,\n    section.colour\n  );\n};\nlet taskCount = -1;\nconst drawTask = function(elem, task, conf) {\n  const center = task.x + conf.width / 2;\n  const g = elem.append(\"g\");\n  taskCount++;\n  const maxHeight = 300 + 5 * 30;\n  g.append(\"line\").attr(\"id\", \"task\" + taskCount).attr(\"x1\", center).attr(\"y1\", task.y).attr(\"x2\", center).attr(\"y2\", maxHeight).attr(\"class\", \"task-line\").attr(\"stroke-width\", \"1px\").attr(\"stroke-dasharray\", \"4 2\").attr(\"stroke\", \"#666\");\n  drawFace(g, {\n    cx: center,\n    cy: 300 + (5 - task.score) * 30,\n    score: task.score\n  });\n  const rect = getNoteRect();\n  rect.x = task.x;\n  rect.y = task.y;\n  rect.fill = task.fill;\n  rect.width = conf.width;\n  rect.height = conf.height;\n  rect.class = \"task task-type-\" + task.num;\n  rect.rx = 3;\n  rect.ry = 3;\n  drawRect(g, rect);\n  task.x + 14;\n  _drawTextCandidateFunc(conf)(\n    task.task,\n    g,\n    rect.x,\n    rect.y,\n    rect.width,\n    rect.height,\n    { class: \"task\" },\n    conf,\n    task.colour\n  );\n};\nconst drawBackgroundRect = function(elem, bounds) {\n  const rectElem = drawRect(elem, {\n    x: bounds.startx,\n    y: bounds.starty,\n    width: bounds.stopx - bounds.startx,\n    height: bounds.stopy - bounds.starty,\n    fill: bounds.fill,\n    class: \"rect\"\n  });\n  rectElem.lower();\n};\nconst getTextObj = function() {\n  return {\n    x: 0,\n    y: 0,\n    fill: void 0,\n    \"text-anchor\": \"start\",\n    width: 100,\n    height: 100,\n    textMargin: 0,\n    rx: 0,\n    ry: 0\n  };\n};\nconst getNoteRect = function() {\n  return {\n    x: 0,\n    y: 0,\n    width: 100,\n    anchor: \"start\",\n    height: 100,\n    rx: 0,\n    ry: 0\n  };\n};\nconst _drawTextCandidateFunc = function() {\n  function byText(content, g, x, y, width, height, textAttrs, colour) {\n    const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y + height / 2 + 5).style(\"font-color\", colour).style(\"text-anchor\", \"middle\").text(content);\n    _setTextAttrs(text, textAttrs);\n  }\n  function byTspan(content, g, x, y, width, height, textAttrs, conf, colour) {\n    const { taskFontSize, taskFontFamily } = conf;\n    const lines = content.split(/<br\\s*\\/?>/gi);\n    for (let i = 0; i < lines.length; i++) {\n      const dy = i * taskFontSize - taskFontSize * (lines.length - 1) / 2;\n      const text = g.append(\"text\").attr(\"x\", x + width / 2).attr(\"y\", y).attr(\"fill\", colour).style(\"text-anchor\", \"middle\").style(\"font-size\", taskFontSize).style(\"font-family\", taskFontFamily);\n      text.append(\"tspan\").attr(\"x\", x + width / 2).attr(\"dy\", dy).text(lines[i]);\n      text.attr(\"y\", y + height / 2).attr(\"dominant-baseline\", \"central\").attr(\"alignment-baseline\", \"central\");\n      _setTextAttrs(text, textAttrs);\n    }\n  }\n  function byFo(content, g, x, y, width, height, textAttrs, conf) {\n    const body = g.append(\"switch\");\n    const f = body.append(\"foreignObject\").attr(\"x\", x).attr(\"y\", y).attr(\"width\", width).attr(\"height\", height).attr(\"position\", \"fixed\");\n    const text = f.append(\"xhtml:div\").style(\"display\", \"table\").style(\"height\", \"100%\").style(\"width\", \"100%\");\n    text.append(\"div\").attr(\"class\", \"label\").style(\"display\", \"table-cell\").style(\"text-align\", \"center\").style(\"vertical-align\", \"middle\").text(content);\n    byTspan(content, body, x, y, width, height, textAttrs, conf);\n    _setTextAttrs(text, textAttrs);\n  }\n  function _setTextAttrs(toText, fromTextAttrsDict) {\n    for (const key in fromTextAttrsDict) {\n      if (key in fromTextAttrsDict) {\n        toText.attr(key, fromTextAttrsDict[key]);\n      }\n    }\n  }\n  return function(conf) {\n    return conf.textPlacement === \"fo\" ? byFo : conf.textPlacement === \"old\" ? byText : byTspan;\n  };\n}();\nconst initGraphics = function(graphics) {\n  graphics.append(\"defs\").append(\"marker\").attr(\"id\", \"arrowhead\").attr(\"refX\", 5).attr(\"refY\", 2).attr(\"markerWidth\", 6).attr(\"markerHeight\", 4).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 0,0 V 4 L6,2 Z\");\n};\nfunction wrap(text, width) {\n  text.each(function() {\n    var text2 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(this), words = text2.text().split(/(\\s+|<br>)/).reverse(), word, line = [], lineHeight = 1.1, y = text2.attr(\"y\"), dy = parseFloat(text2.attr(\"dy\")), tspan = text2.text(null).append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", dy + \"em\");\n    for (let j = 0; j < words.length; j++) {\n      word = words[words.length - 1 - j];\n      line.push(word);\n      tspan.text(line.join(\" \").trim());\n      if (tspan.node().getComputedTextLength() > width || word === \"<br>\") {\n        line.pop();\n        tspan.text(line.join(\" \").trim());\n        if (word === \"<br>\") {\n          line = [\"\"];\n        } else {\n          line = [word];\n        }\n        tspan = text2.append(\"tspan\").attr(\"x\", 0).attr(\"y\", y).attr(\"dy\", lineHeight + \"em\").text(word);\n      }\n    }\n  });\n}\nconst drawNode = function(elem, node, fullSection, conf) {\n  const section = fullSection % MAX_SECTIONS - 1;\n  const nodeElem = elem.append(\"g\");\n  node.section = section;\n  nodeElem.attr(\n    \"class\",\n    (node.class ? node.class + \" \" : \"\") + \"timeline-node \" + (\"section-\" + section)\n  );\n  const bkgElem = nodeElem.append(\"g\");\n  const textElem = nodeElem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize && conf.fontSize.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  node.height = bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n  node.height = Math.max(node.height, node.maxHeight);\n  node.width = node.width + 2 * node.padding;\n  textElem.attr(\"transform\", \"translate(\" + node.width / 2 + \", \" + node.padding / 2 + \")\");\n  defaultBkg(bkgElem, node, section);\n  return node;\n};\nconst getVirtualNodeHeight = function(elem, node, conf) {\n  const textElem = elem.append(\"g\");\n  const txt = textElem.append(\"text\").text(node.descr).attr(\"dy\", \"1em\").attr(\"alignment-baseline\", \"middle\").attr(\"dominant-baseline\", \"middle\").attr(\"text-anchor\", \"middle\").call(wrap, node.width);\n  const bbox = txt.node().getBBox();\n  const fontSize = conf.fontSize && conf.fontSize.replace ? conf.fontSize.replace(\"px\", \"\") : conf.fontSize;\n  textElem.remove();\n  return bbox.height + fontSize * 1.1 * 0.5 + node.padding;\n};\nconst defaultBkg = function(elem, node, section) {\n  const rd = 5;\n  elem.append(\"path\").attr(\"id\", \"node-\" + node.id).attr(\"class\", \"node-bkg node-\" + node.type).attr(\n    \"d\",\n    `M0 ${node.height - rd} v${-node.height + 2 * rd} q0,-5 5,-5 h${node.width - 2 * rd} q5,0 5,5 v${node.height - rd} H0 Z`\n  );\n  elem.append(\"line\").attr(\"class\", \"node-line-\" + section).attr(\"x1\", 0).attr(\"y1\", node.height).attr(\"x2\", node.width).attr(\"y2\", node.height);\n};\nconst svgDraw = {\n  drawRect,\n  drawCircle,\n  drawSection,\n  drawText,\n  drawLabel,\n  drawTask,\n  drawBackgroundRect,\n  getTextObj,\n  getNoteRect,\n  initGraphics,\n  drawNode,\n  getVirtualNodeHeight\n};\nconst draw = function(text, id, version, diagObj) {\n  var _a, _b;\n  const conf = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)();\n  const LEFT_MARGIN = conf.leftMargin ?? 50;\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"timeline\", diagObj.db);\n  const securityLevel = conf.securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_0__.select)(\"body\");\n  const svg = root.select(\"#\" + id);\n  svg.append(\"g\");\n  const tasks2 = diagObj.db.getTasks();\n  const title = diagObj.db.getCommonDb().getDiagramTitle();\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"task\", tasks2);\n  svgDraw.initGraphics(svg);\n  const sections2 = diagObj.db.getSections();\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"sections\", sections2);\n  let maxSectionHeight = 0;\n  let maxTaskHeight = 0;\n  let depthY = 0;\n  let sectionBeginY = 0;\n  let masterX = 50 + LEFT_MARGIN;\n  let masterY = 50;\n  sectionBeginY = 50;\n  let sectionNumber = 0;\n  let hasSections = true;\n  sections2.forEach(function(section) {\n    const sectionNode = {\n      number: sectionNumber,\n      descr: section,\n      section: sectionNumber,\n      width: 150,\n      padding: 20,\n      maxHeight: maxSectionHeight\n    };\n    const sectionHeight = svgDraw.getVirtualNodeHeight(svg, sectionNode, conf);\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"sectionHeight before draw\", sectionHeight);\n    maxSectionHeight = Math.max(maxSectionHeight, sectionHeight + 20);\n  });\n  let maxEventCount = 0;\n  let maxEventLineLength = 0;\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"tasks.length\", tasks2.length);\n  for (const [i, task] of tasks2.entries()) {\n    const taskNode = {\n      number: i,\n      descr: task,\n      section: task.section,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    const taskHeight = svgDraw.getVirtualNodeHeight(svg, taskNode, conf);\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"taskHeight before draw\", taskHeight);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight + 20);\n    maxEventCount = Math.max(maxEventCount, task.events.length);\n    let maxEventLineLengthTemp = 0;\n    for (let j = 0; j < task.events.length; j++) {\n      const event = task.events[j];\n      const eventNode = {\n        descr: event,\n        section: task.section,\n        number: task.section,\n        width: 150,\n        padding: 20,\n        maxHeight: 50\n      };\n      maxEventLineLengthTemp += svgDraw.getVirtualNodeHeight(svg, eventNode, conf);\n    }\n    maxEventLineLength = Math.max(maxEventLineLength, maxEventLineLengthTemp);\n  }\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"maxSectionHeight before draw\", maxSectionHeight);\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"maxTaskHeight before draw\", maxTaskHeight);\n  if (sections2 && sections2.length > 0) {\n    sections2.forEach((section) => {\n      const tasksForSection = tasks2.filter((task) => task.section === section);\n      const sectionNode = {\n        number: sectionNumber,\n        descr: section,\n        section: sectionNumber,\n        width: 200 * Math.max(tasksForSection.length, 1) - 50,\n        padding: 20,\n        maxHeight: maxSectionHeight\n      };\n      _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"sectionNode\", sectionNode);\n      const sectionNodeWrapper = svg.append(\"g\");\n      const node = svgDraw.drawNode(sectionNodeWrapper, sectionNode, sectionNumber, conf);\n      _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"sectionNode output\", node);\n      sectionNodeWrapper.attr(\"transform\", `translate(${masterX}, ${sectionBeginY})`);\n      masterY += maxSectionHeight + 50;\n      if (tasksForSection.length > 0) {\n        drawTasks(\n          svg,\n          tasksForSection,\n          sectionNumber,\n          masterX,\n          masterY,\n          maxTaskHeight,\n          conf,\n          maxEventCount,\n          maxEventLineLength,\n          maxSectionHeight,\n          false\n        );\n      }\n      masterX += 200 * Math.max(tasksForSection.length, 1);\n      masterY = sectionBeginY;\n      sectionNumber++;\n    });\n  } else {\n    hasSections = false;\n    drawTasks(\n      svg,\n      tasks2,\n      sectionNumber,\n      masterX,\n      masterY,\n      maxTaskHeight,\n      conf,\n      maxEventCount,\n      maxEventLineLength,\n      maxSectionHeight,\n      true\n    );\n  }\n  const box = svg.node().getBBox();\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"bounds\", box);\n  if (title) {\n    svg.append(\"text\").text(title).attr(\"x\", box.width / 2 - LEFT_MARGIN).attr(\"font-size\", \"4ex\").attr(\"font-weight\", \"bold\").attr(\"y\", 20);\n  }\n  depthY = hasSections ? maxSectionHeight + maxTaskHeight + 150 : maxTaskHeight + 100;\n  const lineWrapper = svg.append(\"g\").attr(\"class\", \"lineWrapper\");\n  lineWrapper.append(\"line\").attr(\"x1\", LEFT_MARGIN).attr(\"y1\", depthY).attr(\"x2\", box.width + 3 * LEFT_MARGIN).attr(\"y2\", depthY).attr(\"stroke-width\", 4).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\");\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.o)(\n    void 0,\n    svg,\n    ((_a = conf.timeline) == null ? void 0 : _a.padding) ?? 50,\n    ((_b = conf.timeline) == null ? void 0 : _b.useMaxWidth) ?? false\n  );\n};\nconst drawTasks = function(diagram2, tasks2, sectionColor, masterX, masterY, maxTaskHeight, conf, maxEventCount, maxEventLineLength, maxSectionHeight, isWithoutSections) {\n  var _a;\n  for (const task of tasks2) {\n    const taskNode = {\n      descr: task.task,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: maxTaskHeight\n    };\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"taskNode\", taskNode);\n    const taskWrapper = diagram2.append(\"g\").attr(\"class\", \"taskWrapper\");\n    const node = svgDraw.drawNode(taskWrapper, taskNode, sectionColor, conf);\n    const taskHeight = node.height;\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"taskHeight after draw\", taskHeight);\n    taskWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    maxTaskHeight = Math.max(maxTaskHeight, taskHeight);\n    if (task.events) {\n      const lineWrapper = diagram2.append(\"g\").attr(\"class\", \"lineWrapper\");\n      let lineLength = maxTaskHeight;\n      masterY += 100;\n      lineLength = lineLength + drawEvents(diagram2, task.events, sectionColor, masterX, masterY, conf);\n      masterY -= 100;\n      lineWrapper.append(\"line\").attr(\"x1\", masterX + 190 / 2).attr(\"y1\", masterY + maxTaskHeight).attr(\"x2\", masterX + 190 / 2).attr(\n        \"y2\",\n        masterY + maxTaskHeight + (isWithoutSections ? maxTaskHeight : maxSectionHeight) + maxEventLineLength + 120\n      ).attr(\"stroke-width\", 2).attr(\"stroke\", \"black\").attr(\"marker-end\", \"url(#arrowhead)\").attr(\"stroke-dasharray\", \"5,5\");\n    }\n    masterX = masterX + 200;\n    if (isWithoutSections && !((_a = conf.timeline) == null ? void 0 : _a.disableMulticolor)) {\n      sectionColor++;\n    }\n  }\n  masterY = masterY - 10;\n};\nconst drawEvents = function(diagram2, events, sectionColor, masterX, masterY, conf) {\n  let maxEventHeight = 0;\n  const eventBeginY = masterY;\n  masterY = masterY + 100;\n  for (const event of events) {\n    const eventNode = {\n      descr: event,\n      section: sectionColor,\n      number: sectionColor,\n      width: 150,\n      padding: 20,\n      maxHeight: 50\n    };\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"eventNode\", eventNode);\n    const eventWrapper = diagram2.append(\"g\").attr(\"class\", \"eventWrapper\");\n    const node = svgDraw.drawNode(eventWrapper, eventNode, sectionColor, conf);\n    const eventHeight = node.height;\n    maxEventHeight = maxEventHeight + eventHeight;\n    eventWrapper.attr(\"transform\", `translate(${masterX}, ${masterY})`);\n    masterY = masterY + 10 + eventHeight;\n  }\n  masterY = eventBeginY;\n  return maxEventHeight;\n};\nconst renderer = {\n  setConf: () => {\n  },\n  draw\n};\nconst genSections = (options) => {\n  let sections2 = \"\";\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    options[\"lineColor\" + i] = options[\"lineColor\" + i] || options[\"cScaleInv\" + i];\n    if ((0,khroma__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(options[\"lineColor\" + i])) {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(options[\"lineColor\" + i], 20);\n    } else {\n      options[\"lineColor\" + i] = (0,khroma__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(options[\"lineColor\" + i], 20);\n    }\n  }\n  for (let i = 0; i < options.THEME_COLOR_LIMIT; i++) {\n    const sw = \"\" + (17 - 3 * i);\n    sections2 += `\n    .section-${i - 1} rect, .section-${i - 1} path, .section-${i - 1} circle, .section-${i - 1} path  {\n      fill: ${options[\"cScale\" + i]};\n    }\n    .section-${i - 1} text {\n     fill: ${options[\"cScaleLabel\" + i]};\n    }\n    .node-icon-${i - 1} {\n      font-size: 40px;\n      color: ${options[\"cScaleLabel\" + i]};\n    }\n    .section-edge-${i - 1}{\n      stroke: ${options[\"cScale\" + i]};\n    }\n    .edge-depth-${i - 1}{\n      stroke-width: ${sw};\n    }\n    .section-${i - 1} line {\n      stroke: ${options[\"cScaleInv\" + i]} ;\n      stroke-width: 3;\n    }\n\n    .lineWrapper line{\n      stroke: ${options[\"cScaleLabel\" + i]} ;\n    }\n\n    .disabled, .disabled circle, .disabled text {\n      fill: lightgray;\n    }\n    .disabled text {\n      fill: #efefef;\n    }\n    `;\n  }\n  return sections2;\n};\nconst getStyles = (options) => `\n  .edge {\n    stroke-width: 3;\n  }\n  ${genSections(options)}\n  .section-root rect, .section-root path, .section-root circle  {\n    fill: ${options.git0};\n  }\n  .section-root text {\n    fill: ${options.gitBranchLabel0};\n  }\n  .icon-container {\n    height:100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  .edge {\n    fill: none;\n  }\n  .eventWrapper  {\n   filter: brightness(120%);\n  }\n`;\nconst styles = getStyles;\nconst diagram = {\n  db,\n  renderer,\n  parser: parser$1,\n  styles\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/timeline-definition-85554ec2.js\n"));

/***/ })

}]);