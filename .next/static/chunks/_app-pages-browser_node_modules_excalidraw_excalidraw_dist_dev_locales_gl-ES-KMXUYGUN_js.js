"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_gl-ES-KMXUYGUN_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/gl-ES-KMXUYGUN.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/gl-ES-KMXUYGUN.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ gl_ES_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/gl-ES.json\nvar labels = {\n  paste: \"Pegar\",\n  pasteAsPlaintext: \"Pegar coma texto sen formato\",\n  pasteCharts: \"Pegar gr\\xE1ficos\",\n  selectAll: \"Seleccionar todo\",\n  multiSelect: \"Engadir elemento \\xE1 selecci\\xF3n\",\n  moveCanvas: \"Mover o lenzo\",\n  cut: \"Cortar\",\n  copy: \"Copiar\",\n  copyAsPng: \"Copiar no portapapeis como PNG\",\n  copyAsSvg: \"Copiar no portapapeis como SVG\",\n  copyText: \"Copia no portapapeis como texto\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Traer cara adiante\",\n  sendToBack: \"Enviar cara atr\\xE1s\",\n  bringToFront: \"Traer \\xE1 fronte\",\n  sendBackward: \"Enviar ao fondo\",\n  delete: \"Borrar\",\n  copyStyles: \"Copiar estilo\",\n  pasteStyles: \"Pegar estilo\",\n  stroke: \"Trazo\",\n  background: \"Fondo\",\n  fill: \"Recheo\",\n  strokeWidth: \"Largo do trazo\",\n  strokeStyle: \"Estilo do trazo\",\n  strokeStyle_solid: \"S\\xF3lido\",\n  strokeStyle_dashed: \"Li\\xF1a de trazos\",\n  strokeStyle_dotted: \"Li\\xF1a de puntos\",\n  sloppiness: \"Estilo de trazo\",\n  opacity: \"Opacidade\",\n  textAlign: \"Ali\\xF1ar texto\",\n  edges: \"Bordos\",\n  sharp: \"Agudo\",\n  round: \"Redondo\",\n  arrowheads: \"Puntas de frecha\",\n  arrowhead_none: \"Ningunha\",\n  arrowhead_arrow: \"Frecha\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Tri\\xE1ngulo\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Tama\\xF1o da fonte\",\n  fontFamily: \"Tipo de fonte\",\n  addWatermark: 'Engadir \"Feito con Excalidraw\"',\n  handDrawn: \"Debuxado a man\",\n  normal: \"Normal\",\n  code: \"C\\xF3digo\",\n  small: \"Pequeno\",\n  medium: \"Mediano\",\n  large: \"Grande\",\n  veryLarge: \"Moi grande\",\n  solid: \"S\\xF3lido\",\n  hachure: \"Folleto\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Raiado transversal\",\n  thin: \"Estreito\",\n  bold: \"Groso\",\n  left: \"Esquerda\",\n  center: \"Centrado\",\n  right: \"Dereita\",\n  extraBold: \"Moi groso\",\n  architect: \"Arquitecto\",\n  artist: \"Artista\",\n  cartoonist: \"Caricatura\",\n  fileTitle: \"Nome do arquivo\",\n  colorPicker: \"Selector de cor\",\n  canvasColors: \"Usado en lenzo\",\n  canvasBackground: \"Fondo do lenzo\",\n  drawingCanvas: \"Lenzo de debuxo\",\n  layers: \"Capas\",\n  actions: \"Acci\\xF3ns\",\n  language: \"Idioma\",\n  liveCollaboration: \"Colaboraci\\xF3n en directo...\",\n  duplicateSelection: \"Duplicar\",\n  untitled: \"Sen t\\xEDtulo\",\n  name: \"Nome\",\n  yourName: \"O teu nome\",\n  madeWithExcalidraw: \"Feito con Excalidraw\",\n  group: \"Agrupar selecci\\xF3n\",\n  ungroup: \"Desagrupar selecci\\xF3n\",\n  collaborators: \"Colaboradores\",\n  showGrid: \"Mostrar cuadr\\xEDcula\",\n  addToLibrary: \"Engadir \\xE1 biblioteca\",\n  removeFromLibrary: \"Eliminar da biblioteca\",\n  libraryLoadingMessage: \"Cargando biblioteca\\u2026\",\n  libraries: \"Explorar bibliotecas\",\n  loadingScene: \"Cargando escena\\u2026\",\n  align: \"Ali\\xF1amento\",\n  alignTop: \"Ali\\xF1amento superior\",\n  alignBottom: \"Ali\\xF1amento inferior\",\n  alignLeft: \"Ali\\xF1ar a esquerda\",\n  alignRight: \"Ali\\xF1ar a dereita\",\n  centerVertically: \"Centrar verticalmente\",\n  centerHorizontally: \"Centrar horizontalmente\",\n  distributeHorizontally: \"Distribu\\xEDr horizontalmente\",\n  distributeVertically: \"Distribu\\xEDr verticalmente\",\n  flipHorizontal: \"Virar horizontalmente\",\n  flipVertical: \"Virar verticalmente\",\n  viewMode: \"Modo de visualizaci\\xF3n\",\n  share: \"Compartir\",\n  showStroke: \"Mostrar selector de cores do trazo\",\n  showBackground: \"Mostrar selector de cores do fondo\",\n  toggleTheme: \"Alternar tema\",\n  personalLib: \"Biblioteca Persoal\",\n  excalidrawLib: \"Biblioteca Excalidraw\",\n  decreaseFontSize: \"Diminu\\xEDr tama\\xF1o da fonte\",\n  increaseFontSize: \"Aumentar o tama\\xF1o da fonte\",\n  unbindText: \"Desvincular texto\",\n  bindText: \"Ligar o texto ao contedor\",\n  createContainerFromText: \"Envolver o texto nun contedor\",\n  link: {\n    edit: \"Editar ligaz\\xF3n\",\n    editEmbed: \"\",\n    create: \"Crear ligaz\\xF3n\",\n    createEmbed: \"\",\n    label: \"Ligaz\\xF3n\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Editar li\\xF1a\",\n    exit: \"Sa\\xEDr do editor de li\\xF1as\"\n  },\n  elementLock: {\n    lock: \"Bloquear\",\n    unlock: \"Desbloquear\",\n    lockAll: \"Bloquear todo\",\n    unlockAll: \"Desbloquear todo\"\n  },\n  statusPublished: \"Publicado\",\n  sidebarLock: \"Manter a barra lateral aberta\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"A\\xEDnda non hai elementos engadidos...\",\n  hint_emptyLibrary: \"Seleccione un elemento no lenzo para engadilo aqu\\xED, ou instale unha biblioteca dende o repositorio p\\xFAblico, como se detalla a continuaci\\xF3n.\",\n  hint_emptyPrivateLibrary: \"Seleccione un elemento do lenzo para engadilo aqu\\xED.\"\n};\nvar buttons = {\n  clearReset: \"Limpar o lenzo\",\n  exportJSON: \"Exportar a arquivo\",\n  exportImage: \"Exportar imaxe...\",\n  export: \"Gardar en...\",\n  copyToClipboard: \"Copiar ao portapapeis\",\n  save: \"Gardar no ficheiro actual\",\n  saveAs: \"Gardar como\",\n  load: \"Abrir\",\n  getShareableLink: \"Obter unha ligaz\\xF3n que se poida compartir\",\n  close: \"Pechar\",\n  selectLanguage: \"Seleccionar idioma\",\n  scrollBackToContent: \"Volver ao contido\",\n  zoomIn: \"Ampliar\",\n  zoomOut: \"Reducir\",\n  resetZoom: \"Reiniciar zoom\",\n  menu: \"Men\\xFA\",\n  done: \"Feito\",\n  edit: \"Editar\",\n  undo: \"Desfacer\",\n  redo: \"Refacer\",\n  resetLibrary: \"Reiniciar biblioteca\",\n  createNewRoom: \"Crear nova sala\",\n  fullScreen: \"Pantalla completa\",\n  darkMode: \"Modo escuro\",\n  lightMode: \"Modo claro\",\n  zenMode: \"Modo zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Sa\\xEDr do modo zen\",\n  cancel: \"Cancelar\",\n  clear: \"Limpar\",\n  remove: \"Eliminar\",\n  embed: \"\",\n  publishLibrary: \"Publicar\",\n  submit: \"Enviar\",\n  confirm: \"Confirmar\",\n  embeddableInteractionButton: \"Faga clic para interactuar\"\n};\nvar alerts = {\n  clearReset: \"Isto limpar\\xE1 todo o lenzo. Est\\xE1s seguro?\",\n  couldNotCreateShareableLink: \"Non se puido crear unha ligaz\\xF3n para compartir.\",\n  couldNotCreateShareableLinkTooBig: \"Non se puido crear a ligaz\\xF3n para compartir: a escena \\xE9 demasiado grande\",\n  couldNotLoadInvalidFile: \"Non se puido cargar o ficheiro non v\\xE1lido\",\n  importBackendFailed: \"A importaci\\xF3n dende o backend fallou.\",\n  cannotExportEmptyCanvas: \"Non se pode exportar un lenzo baleiro.\",\n  couldNotCopyToClipboard: \"Non se puido copiar ao portapapeis.\",\n  decryptFailed: \"Non se poideron descifrar os datos.\",\n  uploadedSecurly: \"A carga foi asegurada con cifrado de extremo a extremo, o que significa que o servidor de Excalidraw e terceiros non poden ler o contido.\",\n  loadSceneOverridePrompt: \"A carga dun debuxo externo substituir\\xE1 o contido existente. Desexa continuar?\",\n  collabStopOverridePrompt: \"Deter a sesi\\xF3n, sobrescribir\\xE1 o seu debuxo local previamente almacenado. Est\\xE1 seguro?\\n\\n(Se quere manter o seu debuxo local, simplemente peche a lapela do navegador.)\",\n  errorAddingToLibrary: \"Non se puido engadir o elemento \\xE1 biblioteca\",\n  errorRemovingFromLibrary: \"Non se puido eliminar o elemento da biblioteca\",\n  confirmAddLibrary: \"Isto engadir\\xE1 {{numShapes}} forma(s) a t\\xFAa biblioteca. Est\\xE1s seguro?\",\n  imageDoesNotContainScene: \"Esta imaxe non parece conter ning\\xFAn dato da escena. Activou a inserci\\xF3n de escenas durante a exportaci\\xF3n?\",\n  cannotRestoreFromImage: \"Non se puido restaurar a escena dende este arquivo de imaxe\",\n  invalidSceneUrl: \"Non se puido importar a escena dende a URL proporcionada. Ou ben est\\xE1 malformada ou non cont\\xE9n un JSON con informaci\\xF3n v\\xE1lida para Excalidraw.\",\n  resetLibrary: \"Isto limpar\\xE1 a s\\xFAa biblioteca. Est\\xE1 seguro?\",\n  removeItemsFromsLibrary: \"Eliminar {{count}} elemento(s) da biblioteca?\",\n  invalidEncryptionKey: \"A clave de cifrado debe ter 22 caracteres. A colaboraci\\xF3n en directo est\\xE1 desactivada.\",\n  collabOfflineWarning: \"Non hai conexi\\xF3n a Internet dispo\\xF1ible.\\nOs teus cambios non ser\\xE1n gardados!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipo de ficheiro non soportado.\",\n  imageInsertError: \"Non se puido inserir a imaxe. Probe de novo m\\xE1is tarde...\",\n  fileTooBig: \"O ficheiro \\xE9 demasiado grande. O tama\\xF1o m\\xE1ximo permitido \\xE9 {{maxSize}}.\",\n  svgImageInsertError: \"Non se puido inserir como imaxe SVG. O marcado SVG semella inv\\xE1lido.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG inv\\xE1lido.\",\n  cannotResolveCollabServer: \"Non se puido conectar ao servidor de colaboraci\\xF3n. Por favor recargue a p\\xE1xina e probe de novo.\",\n  importLibraryError: \"Non se puido cargar a biblioteca\",\n  collabSaveFailed: \"Non se puido gardar na base de datos. Se o problema persiste, deber\\xEDas gardar o teu arquivo de maneira local para asegurarte de non perdelo teu traballo.\",\n  collabSaveFailed_sizeExceeded: \"Non se puido gardar na base de datos, o lenzo semella demasiado grande. Deber\\xEDas gardar o teu arquivo de maneira local para asegurarte de non perdelo teu traballo.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Selecci\\xF3n\",\n  image: \"Inserir imaxe\",\n  rectangle: \"Rect\\xE1ngulo\",\n  diamond: \"Diamante\",\n  ellipse: \"Elipse\",\n  arrow: \"Frecha\",\n  line: \"Li\\xF1a\",\n  freedraw: \"Debuxar\",\n  text: \"Texto\",\n  library: \"Biblioteca\",\n  lock: \"Manter a ferramenta seleccionada activa despois de debuxar\",\n  penMode: \"Modo lapis - evitar o contacto\",\n  link: \"Engadir/ Actualizar ligaz\\xF3n para a forma seleccionada\",\n  eraser: \"Goma de borrar\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"Inserir na web\",\n  laser: \"Punteiro l\\xE1ser\",\n  hand: \"Man (ferramenta de desprazamento)\",\n  extraTools: \"M\\xE1is ferramentas\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Acci\\xF3ns do lenzo\",\n  selectedShapeActions: \"Acci\\xF3ns da forma seleccionada\",\n  shapes: \"Formas\"\n};\nvar hints = {\n  canvasPanning: \"Para mover o lenzo, mante\\xF1a pulsada a roda do rato ou a barra de espazo mentres arrastra, ou utilice a ferramenta da man\",\n  linearElement: \"Faga clic para iniciar varios puntos, arrastre para unha sola li\\xF1a\",\n  freeDraw: \"Fai clic e arrastra, solta cando acabes\",\n  text: \"Consello: tam\\xE9n podes engadir texto facendo dobre-clic en calquera lugar coa ferramenta de selecci\\xF3n\",\n  embeddable: \"Faga clic e arrastre para crear un sitio web embebido\",\n  text_selected: \"Dobre-clic ou prema ENTER para editar o texto\",\n  text_editing: \"Prema Escape ou CtrlOrCmd+ENTER para finalizar a edici\\xF3n\",\n  linearElementMulti: \"Faga clic no \\xFAltimo punto ou prema Escape ou Enter para rematar\",\n  lockAngle: \"Pode reducir o \\xE1ngulo mantendo SHIFT\",\n  resize: \"Pode reducir as proporci\\xF3ns mantendo SHIFT mentres axusta o tama\\xF1o,\\nmante\\xF1a ALT para axustalo dende o centro\",\n  resizeImage: \"Pode axustar o tama\\xF1o libremente mantendo SHIFT,\\nmante\\xF1a ALT para axustalo dende o centro\",\n  rotate: \"Podes reducir os \\xE1ngulos mantendo SHIFT mentres os rotas\",\n  lineEditor_info: \"Mante\\xF1a pulsado CtrlOrCmd e faga dobre clic ou prema CtrlOrCmd + Enter para editar puntos\",\n  lineEditor_pointSelected: \"Prema Suprimir para eliminar o(s) punto(s)\\nCtrlOrCmd+D para duplicalos, ou arrastre para movelos\",\n  lineEditor_nothingSelected: \"Seleccione un punto para editar (mante\\xF1a pulsado SHIFT para selecci\\xF3n m\\xFAltiple),\\nou mante\\xF1a pulsado Alt e faga clic para engadir novos puntos\",\n  placeImage: \"Faga clic para colocar a imaxe, ou faga clic e arrastre para establecer o seu tama\\xF1o manualmente\",\n  publishLibrary: \"Publica a t\\xFAa propia biblioteca\",\n  bindTextToElement: \"Prema a tecla enter para engadir texto\",\n  deepBoxSelect: \"Mante\\xF1a pulsado CtrlOrCmd para seleccionar en profundidade e evitar o arrastre\",\n  eraserRevert: \"Mante\\xF1a pulsado Alt para reverter os elementos marcados para a s\\xFAa eliminaci\\xF3n\",\n  firefox_clipboard_write: 'Esta funci\\xF3n p\\xF3dese activar establecendo a opci\\xF3n \"dom.events.asyncClipboard.clipboardItem\" a \"true\". Para cambiar as opci\\xF3ns do navegador en Firefox, visita a p\\xE1xina \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Non se pode mostrar a vista previa\",\n  canvasTooBig: \"Pode que o lenzo sexa demasiado grande.\",\n  canvasTooBigTip: \"Consello: Probe a acercar un pouco os elementos m\\xE1is afastados.\"\n};\nvar errorSplash = {\n  headingMain: \"Atopouse un erro. Probe <button>recargando a p\\xE1xina.</button>\",\n  clearCanvasMessage: \"Se recargar non funcionou, probe <button>limpando o lenzo.</button>\",\n  clearCanvasCaveat: \" Isto resultar\\xE1 nunha perda do seu traballo \",\n  trackedToSentry: \"O erro con identificador {{eventId}} foi rastrexado no noso sistema.\",\n  openIssueMessage: \"Fomos moi cautelosos de non inclu\\xEDr a informaci\\xF3n da s\\xFAa escena no erro. Se a s\\xFAa escena non \\xE9 privada, por favor, considere o seguimento do noso <button>rastrexador de erros.</button> Por favor incl\\xFAa a seguinte informaci\\xF3n copi\\xE1ndoa e peg\\xE1ndoa na issue de Github.\",\n  sceneContent: \"Contido da escena:\"\n};\nvar roomDialog = {\n  desc_intro: \"Podes invitar xente a colaborar contigo na t\\xFAa escena actual.\",\n  desc_privacy: \"Non te preocupes, a sesi\\xF3n usa cifrado de punto a punto, polo que calquera cousa que debuxes mantense privada. Nin tan sequera o noso servidor ser\\xE1 capaz de ver o que fas.\",\n  button_startSession: \"Comezar sesi\\xF3n\",\n  button_stopSession: \"Rematar sesi\\xF3n\",\n  desc_inProgressIntro: \"A sesi\\xF3n de colaboraci\\xF3n en directo est\\xE1 agora en progreso.\",\n  desc_shareLink: \"Comparte esta ligaz\\xF3n con calquera que queiras colaborar:\",\n  desc_exitSession: \"Deter a sesi\\xF3n desconectarao da sala, pero poder\\xE1 seguir traballando coa escena de maneira local. Te\\xF1a en conta que isto non afectar\\xE1 a outras persoas, que poder\\xE1n seguir colaborando na s\\xFAa versi\\xF3n.\",\n  shareTitle: \"\\xDAnase a unha sesi\\xF3n de colaboraci\\xF3n en directo en Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Erro\"\n};\nvar exportDialog = {\n  disk_title: \"Gardar no disco\",\n  disk_details: \"Exporte os datos da escena a un ficheiro que poder\\xE1s importar m\\xE1is tarde.\",\n  disk_button: \"Gardar nun ficheiro\",\n  link_title: \"Ligaz\\xF3n para compartir\",\n  link_details: \"Exportar como unha ligaz\\xF3n de s\\xF3 lectura.\",\n  link_button: \"Exportar a unha ligaz\\xF3n\",\n  excalidrawplus_description: \"Garde a escena no seu espazo de traballo en Excalidraw+.\",\n  excalidrawplus_button: \"Exportar\",\n  excalidrawplus_exportError: \"Non se puido exportar a Excalidraw+ neste momento...\"\n};\nvar helpDialog = {\n  blog: \"Le o noso blog\",\n  click: \"clic\",\n  deepSelect: \"Selecci\\xF3n en profundidade\",\n  deepBoxSelect: \"Selecci\\xF3n en profundidade dentro da caixa, evitando o arrastre\",\n  curvedArrow: \"Frecha curva\",\n  curvedLine: \"Li\\xF1a curva\",\n  documentation: \"Documentaci\\xF3n\",\n  doubleClick: \"dobre-clic\",\n  drag: \"arrastrar\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"Encontrou un problema? Env\\xEDeo\",\n  howto: \"Sigue as nosas normas\",\n  or: \"ou\",\n  preventBinding: \"Evitar a uni\\xF3n de frechas\",\n  tools: \"Ferramentas\",\n  shortcuts: \"Atallos de teclado\",\n  textFinish: \"Rematar de editar (editor de texto)\",\n  textNewLine: \"Engadir unha nova li\\xF1a (editor de texto)\",\n  title: \"Axuda\",\n  view: \"Vista\",\n  zoomToFit: \"Zoom que se axuste a todos os elementos\",\n  zoomToSelection: \"Zoom \\xE1 selecci\\xF3n\",\n  toggleElementLock: \"Bloquear/desbloquear selecci\\xF3n\",\n  movePageUpDown: \"Mover p\\xE1xina cara enriba/abaixo\",\n  movePageLeftRight: \"Mover p\\xE1xina cara a esquerda/dereita\"\n};\nvar clearCanvasDialog = {\n  title: \"Limpar lenzo\"\n};\nvar publishDialog = {\n  title: \"Publicar biblioteca\",\n  itemName: \"Nome do elemento\",\n  authorName: \"Nome do autor\",\n  githubUsername: \"Nome de usuario en Github\",\n  twitterUsername: \"Nome de usuario en Twitter\",\n  libraryName: \"Nome da biblioteca\",\n  libraryDesc: \"Descrici\\xF3n da biblioteca\",\n  website: \"P\\xE1xina web\",\n  placeholder: {\n    authorName: \"O seu nome ou nome de usuario\",\n    libraryName: \"Nome da s\\xFAa biblioteca\",\n    libraryDesc: \"Descrici\\xF3n da s\\xFAa biblioteca para axudar a xente a entender o seu uso\",\n    githubHandle: \"Nome de usuario de GitHub (opcional), as\\xED poder\\xE1s editar a biblioteca unha vez enviada para a s\\xFAa revisi\\xF3n\",\n    twitterHandle: \"Nome de usuario en Twitter(opcional), as\\xED sabemos a quen darlle cr\\xE9dito cando se lle de promoci\\xF3n a trav\\xE9s de Twitter\",\n    website: \"Ligaz\\xF3n ao teu sitio web persoal ou a outro sitio (opcional)\"\n  },\n  errors: {\n    required: \"Obrigatorio\",\n    website: \"Introduza unha URL v\\xE1lida\"\n  },\n  noteDescription: \"Env\\xEDe a s\\xFAa biblioteca para que sexa inclu\\xEDda no <link>repositorio p\\xFAblico de bibliotecas</link>para que outra xente a poida usar nos seus debuxos.\",\n  noteGuidelines: \"A biblioteca necesita ser aprobada manualmente primeiro. Por favor, lea as <link>normas</link> antes de ser enviado. Necesitar\\xE1s unha conta de GitHub para comunicarte ou facer cambios se se solicitan, pero non \\xE9 estritamente necesario.\",\n  noteLicense: \"Ao enviar, est\\xE1s de acordo con que a biblioteca sexa publicada baixo a <link>Licenza MIT, </link>o cal significa que, en resumo, calquera pode usalo sen restrici\\xF3ns.\",\n  noteItems: \"Cada elemento da biblioteca debe ter o seu nome propio para que se poida filtrar. Os seguintes elementos da biblioteca ser\\xE1n inclu\\xEDdos:\",\n  atleastOneLibItem: \"Por favor seleccione polo menos un elemento da biblioteca para comezar\",\n  republishWarning: \"Nota: alg\\xFAns dos elementos seleccionados est\\xE1n marcados como xa publicados/enviados. S\\xF3 deber\\xEDas reenviar elementos cando se actualice unha biblioteca ou env\\xEDo.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteca enviada\",\n  content: \"Grazas {{authorName}}. A s\\xFAa biblioteca foi enviada para ser revisada. Pode seguir o estado<link>aqu\\xED</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Restablecer biblioteca\",\n  removeItemsFromLib: \"Eliminar os elementos seleccionados da biblioteca\"\n};\nvar imageExportDialog = {\n  header: \"Exportar imaxe\",\n  label: {\n    withBackground: \"Fondo\",\n    onlySelected: \"\",\n    darkMode: \"Modo escuro\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"Exportar a PNG\",\n    exportToSvg: \"Exportar a SVG\",\n    copyPngToClipboard: \"Copiar PNG ao portapapeis\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copiar ao portapapeis\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Os teus debuxos est\\xE1n cifrados de punto a punto, polo que os servidores de Excalidraw nunca os ver\\xE1n.\",\n  link: \"Entrada do blog acerca do cifrado de punto a punto en Excalidraw\"\n};\nvar stats = {\n  angle: \"\\xC1ngulo\",\n  element: \"Elemento\",\n  elements: \"Elementos\",\n  height: \"Alto\",\n  scene: \"Escena\",\n  selected: \"Seleccionado\",\n  storage: \"Almacenamento\",\n  title: \"Estad\\xEDsticas para nerds\",\n  total: \"Total\",\n  version: \"Versi\\xF3n\",\n  versionCopy: \"Faga clic para copiar\",\n  versionNotAvailable: \"Versi\\xF3n non dispo\\xF1ible\",\n  width: \"Ancho\"\n};\nvar toast = {\n  addedToLibrary: \"Engadido \\xE1 biblioteca\",\n  copyStyles: \"Estilos copiados.\",\n  copyToClipboard: \"Copiado ao portapapeis.\",\n  copyToClipboardAsPng: \"Copiar {{exportSelection}} ao portapapeis como PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Ficheiro gardado.\",\n  fileSavedToFilename: \"Gardado en {filename}\",\n  canvas: \"lenzo\",\n  selection: \"selecci\\xF3n\",\n  pasteAsSingleElement: \"Usa {{shortcut}} para pegar como un \\xFAnico elemento\\nou pega nun editor de texto existente\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transparente\",\n  black: \"Negro\",\n  white: \"Branco\",\n  red: \"Vermello\",\n  pink: \"Rosa\",\n  grape: \"Uva\",\n  violet: \"Violeta\",\n  gray: \"Gris\",\n  blue: \"Azul\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"Verde\",\n  yellow: \"Marelo\",\n  orange: \"Laranxa\",\n  bronze: \"Bronce\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Toda a informaci\\xF3n \\xE9 gardada de maneira local no seu navegador.\",\n    center_heading_plus: \"Queres ir a Excalidraw+ no seu lugar?\",\n    menuHint: \"Exportar, preferencias, idiomas, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportar, preferencias, e m\\xE1is...\",\n    center_heading: \"Diagramas. Feito. Sinxelo.\",\n    toolbarHint: \"Escolle unha ferramenta & Comeza a debuxar!\",\n    helpHint: \"Atallos & axuda\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"Cores\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportar como imaxe\",\n      button: \"Exportar como imaxe\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"Gardar no disco\",\n      button: \"Gardar no disco\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exportar a Excalidraw+\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Cargar dende arquivo\",\n      button: \"Cargar dende arquivo\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"Cargar dende un enlace\",\n      button: \"Substitu\\xEDr o meu contido\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar gl_ES_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=gl-ES-KMXUYGUN.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/gl-ES-KMXUYGUN.js\n"));

/***/ })

}]);