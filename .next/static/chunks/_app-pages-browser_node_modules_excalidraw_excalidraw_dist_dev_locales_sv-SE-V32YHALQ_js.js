"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_sv-SE-V32YHALQ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/sv-SE-V32YHALQ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/sv-SE-V32YHALQ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ sv_SE_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/sv-SE.json\nvar labels = {\n  paste: \"Klistra in\",\n  pasteAsPlaintext: \"Klistra som oformaterad text\",\n  pasteCharts: \"Klistra in diagram\",\n  selectAll: \"Markera alla\",\n  multiSelect: \"L\\xE4gg till element till markering\",\n  moveCanvas: \"Flytta canvas\",\n  cut: \"Klipp ut\",\n  copy: \"Kopiera\",\n  copyAsPng: \"Kopiera till urklipp som PNG\",\n  copyAsSvg: \"Kopiera till urklipp som SVG\",\n  copyText: \"Kopiera till urklipp som text\",\n  copySource: \"Kopiera k\\xE4lla till urklipp\",\n  convertToCode: \"Konvertera till kod\",\n  bringForward: \"Flytta fram\\xE5t\",\n  sendToBack: \"Flytta underst\",\n  bringToFront: \"Flytta fr\\xE4mst\",\n  sendBackward: \"Skicka bak\\xE5t\",\n  delete: \"Ta bort\",\n  copyStyles: \"Kopiera stil\",\n  pasteStyles: \"Klistra in stil\",\n  stroke: \"Linje\",\n  background: \"Bakgrund\",\n  fill: \"Fyllnad\",\n  strokeWidth: \"Linjebredd\",\n  strokeStyle: \"Linjestil\",\n  strokeStyle_solid: \"Solid\",\n  strokeStyle_dashed: \"Streckad\",\n  strokeStyle_dotted: \"Punktad\",\n  sloppiness: \"Slarvighet\",\n  opacity: \"Genomskinlighet\",\n  textAlign: \"Textjustering\",\n  edges: \"Kanter\",\n  sharp: \"Skarp\",\n  round: \"Rund\",\n  arrowheads: \"Pilhuvuden\",\n  arrowhead_none: \"Inga\",\n  arrowhead_arrow: \"Pil\",\n  arrowhead_bar: \"Stolpe\",\n  arrowhead_circle: \"Cirkel\",\n  arrowhead_circle_outline: \"Cirkel (kontur)\",\n  arrowhead_triangle: \"Triangel\",\n  arrowhead_triangle_outline: \"Triangel (kontur)\",\n  arrowhead_diamond: \"Diamant\",\n  arrowhead_diamond_outline: \"Diamant (kontur)\",\n  fontSize: \"Teckenstorlek\",\n  fontFamily: \"Teckensnitt\",\n  addWatermark: 'L\\xE4gg till \"Skapad med Excalidraw\"',\n  handDrawn: \"Handritad\",\n  normal: \"Normal\",\n  code: \"Kod\",\n  small: \"Liten\",\n  medium: \"Medium\",\n  large: \"Stor\",\n  veryLarge: \"Mycket stor\",\n  solid: \"Solid\",\n  hachure: \"Skraffering\",\n  zigzag: \"Sicksack\",\n  crossHatch: \"Skraffera med kors\",\n  thin: \"Tunn\",\n  bold: \"Fet\",\n  left: \"V\\xE4nster\",\n  center: \"Centrera\",\n  right: \"H\\xF6ger\",\n  extraBold: \"Extra fet\",\n  architect: \"Arkitekt\",\n  artist: \"Artist\",\n  cartoonist: \"Serietecknare\",\n  fileTitle: \"Filnamn\",\n  colorPicker: \"F\\xE4rgv\\xE4ljare\",\n  canvasColors: \"Anv\\xE4nds p\\xE5 canvas\",\n  canvasBackground: \"Canvas-bakgrund\",\n  drawingCanvas: \"Ritar canvas\",\n  layers: \"Lager\",\n  actions: \"\\xC5tg\\xE4rder\",\n  language: \"Spr\\xE5k\",\n  liveCollaboration: \"Samarbeta live...\",\n  duplicateSelection: \"Duplicera\",\n  untitled: \"Namnl\\xF6s\",\n  name: \"Namn\",\n  yourName: \"Ditt namn\",\n  madeWithExcalidraw: \"Skapad med Excalidraw\",\n  group: \"Gruppera markering\",\n  ungroup: \"Avgruppera markering\",\n  collaborators: \"Medarbetare\",\n  showGrid: \"Visa rutn\\xE4t\",\n  addToLibrary: \"L\\xE4gg till i biblioteket\",\n  removeFromLibrary: \"Ta bort fr\\xE5n bibliotek\",\n  libraryLoadingMessage: \"Laddar bibliotek\\u2026\",\n  libraries: \"Bl\\xE4ddra i bibliotek\",\n  loadingScene: \"Laddar skiss\\u2026\",\n  align: \"Justera\",\n  alignTop: \"Justera \\xF6verkant\",\n  alignBottom: \"Justera underkant\",\n  alignLeft: \"Justera v\\xE4nster\",\n  alignRight: \"Justera h\\xF6ger\",\n  centerVertically: \"Centrera vertikalt\",\n  centerHorizontally: \"Centrera horisontellt\",\n  distributeHorizontally: \"F\\xF6rdela horisontellt\",\n  distributeVertically: \"F\\xF6rdela vertikalt\",\n  flipHorizontal: \"V\\xE4nd horisontellt\",\n  flipVertical: \"V\\xE4nd vertikalt\",\n  viewMode: \"Visningsl\\xE4ge\",\n  share: \"Dela\",\n  showStroke: \"Visa f\\xE4rgv\\xE4ljare f\\xF6r linjef\\xE4rg\",\n  showBackground: \"Visa f\\xE4rgv\\xE4ljare f\\xF6r bakgrundsf\\xE4rg\",\n  toggleTheme: \"V\\xE4xla tema\",\n  personalLib: \"Personligt bibliotek\",\n  excalidrawLib: \"Excalidraw bibliotek\",\n  decreaseFontSize: \"Minska fontstorleken\",\n  increaseFontSize: \"\\xD6ka fontstorleken\",\n  unbindText: \"Koppla bort text\",\n  bindText: \"Bind texten till beh\\xE5llaren\",\n  createContainerFromText: \"Radbryt text i en avgr\\xE4nsad yta\",\n  link: {\n    edit: \"Redigera l\\xE4nk\",\n    editEmbed: \"Redigera l\\xE4nk & b\\xE4dda in\",\n    create: \"Skapa l\\xE4nk\",\n    createEmbed: \"Skapa l\\xE4nk & b\\xE4dda in\",\n    label: \"L\\xE4nk\",\n    labelEmbed: \"L\\xE4nka & b\\xE4dda in\",\n    empty: \"Ingen l\\xE4nk \\xE4r angiven\"\n  },\n  lineEditor: {\n    edit: \"Redigera linje\",\n    exit: \"Avsluta linjeredigerare\"\n  },\n  elementLock: {\n    lock: \"L\\xE5s\",\n    unlock: \"L\\xE5s upp\",\n    lockAll: \"L\\xE5s alla\",\n    unlockAll: \"L\\xE5s upp alla\"\n  },\n  statusPublished: \"Publicerad\",\n  sidebarLock: \"H\\xE5ll sidof\\xE4ltet \\xF6ppet\",\n  selectAllElementsInFrame: \"Markera alla element i rutan\",\n  removeAllElementsFromFrame: \"Ta bort alla element fr\\xE5n rutan\",\n  eyeDropper: \"V\\xE4lj f\\xE4rg fr\\xE5n canvas\",\n  textToDiagram: \"Text till diagram\",\n  prompt: \"Fr\\xE5ga\"\n};\nvar library = {\n  noItems: \"Inga objekt tillagda \\xE4nnu...\",\n  hint_emptyLibrary: \"V\\xE4lj ett objekt p\\xE5 canvasen f\\xF6r att l\\xE4gga till det h\\xE4r, eller installera ett bibliotek fr\\xE5n det publika arkivet, nedan.\",\n  hint_emptyPrivateLibrary: \"V\\xE4lj ett objekt p\\xE5 canvasen f\\xF6r att l\\xE4gga till det h\\xE4r.\"\n};\nvar buttons = {\n  clearReset: \"\\xC5terst\\xE4ll canvasen\",\n  exportJSON: \"Exportera till fil\",\n  exportImage: \"Exportera bild...\",\n  export: \"Spara till...\",\n  copyToClipboard: \"Kopiera till urklipp\",\n  save: \"Spara till aktuell fil\",\n  saveAs: \"Spara som\",\n  load: \"\\xD6ppna\",\n  getShareableLink: \"H\\xE4mta delbar l\\xE4nk\",\n  close: \"St\\xE4ng\",\n  selectLanguage: \"V\\xE4lj spr\\xE5k\",\n  scrollBackToContent: \"Bl\\xE4ddra tillbaka till inneh\\xE5llet\",\n  zoomIn: \"Zooma in\",\n  zoomOut: \"Zooma ut\",\n  resetZoom: \"\\xC5terst\\xE4ll zoom\",\n  menu: \"Meny\",\n  done: \"Klart\",\n  edit: \"Redigera\",\n  undo: \"\\xC5ngra\",\n  redo: \"G\\xF6r om\",\n  resetLibrary: \"\\xC5terst\\xE4ll bibliotek\",\n  createNewRoom: \"Skapa ett nytt rum\",\n  fullScreen: \"Helsk\\xE4rm\",\n  darkMode: \"M\\xF6rkt l\\xE4ge\",\n  lightMode: \"Ljust l\\xE4ge\",\n  zenMode: \"Zen-l\\xE4ge\",\n  objectsSnapMode: \"F\\xE4st mot objekt\",\n  exitZenMode: \"G\\xE5 ur zen-l\\xE4ge\",\n  cancel: \"Avbryt\",\n  clear: \"Rensa\",\n  remove: \"Ta bort\",\n  embed: \"V\\xE4xla inb\\xE4ddning\",\n  publishLibrary: \"Publicera\",\n  submit: \"Skicka\",\n  confirm: \"Bekr\\xE4fta\",\n  embeddableInteractionButton: \"Klicka f\\xF6r att interagera\"\n};\nvar alerts = {\n  clearReset: \"Detta rensar hela canvasen. \\xC4r du s\\xE4ker?\",\n  couldNotCreateShareableLink: \"Kunde inte skapa delbar l\\xE4nk.\",\n  couldNotCreateShareableLinkTooBig: \"Kunde inte skapa delbar l\\xE4nk: skissen \\xE4r f\\xF6r stor\",\n  couldNotLoadInvalidFile: \"Kunde inte ladda ogiltig fil\",\n  importBackendFailed: \"Importering fr\\xE5n backend misslyckades.\",\n  cannotExportEmptyCanvas: \"Kan inte exportera tom canvas.\",\n  couldNotCopyToClipboard: \"Kunde inte kopiera till urklipp.\",\n  decryptFailed: \"Kunde inte avkryptera data.\",\n  uploadedSecurly: \"Uppladdning har s\\xE4krats med kryptering fr\\xE5n \\xE4nde till \\xE4nde. vilket inneb\\xE4r att Excalidraw server och tredje part inte kan l\\xE4sa inneh\\xE5llet.\",\n  loadSceneOverridePrompt: \"Laddning av extern skiss kommer att ers\\xE4tta ditt befintliga inneh\\xE5ll. Vill du forts\\xE4tta?\",\n  collabStopOverridePrompt: \"Att stoppa sessionen kommer att skriva \\xF6ver din f\\xF6reg\\xE5ende, lokalt lagrade skiss. \\xC4r du s\\xE4ker?\\n\\n(Om du vill beh\\xE5lla din lokala skiss, st\\xE4ng bara webbl\\xE4sarfliken ist\\xE4llet.)\",\n  errorAddingToLibrary: \"Kunde inte l\\xE4gga till objekt i biblioteket\",\n  errorRemovingFromLibrary: \"Kunde inte ta bort objekt fr\\xE5n biblioteket\",\n  confirmAddLibrary: \"Detta kommer att l\\xE4gga till {{numShapes}} form(er) till ditt bibliotek. \\xC4r du s\\xE4ker?\",\n  imageDoesNotContainScene: \"Den h\\xE4r bilden verkar inte inneh\\xE5lla n\\xE5gon skissdata. Har du aktiverat inb\\xE4ddning av skiss under export?\",\n  cannotRestoreFromImage: \"Skiss kunde inte \\xE5terst\\xE4llas fr\\xE5n denna bildfil\",\n  invalidSceneUrl: \"Det gick inte att importera skiss fr\\xE5n den angivna webbadressen. Antingen har den fel format, eller s\\xE5 inneh\\xE5ller den ingen giltig Excalidraw JSON data.\",\n  resetLibrary: \"Detta kommer att rensa ditt bibliotek. \\xC4r du s\\xE4ker?\",\n  removeItemsFromsLibrary: \"Ta bort {{count}} objekt fr\\xE5n biblioteket?\",\n  invalidEncryptionKey: \"Krypteringsnyckeln m\\xE5ste vara 22 tecken. Livesamarbetet \\xE4r inaktiverat.\",\n  collabOfflineWarning: \"Ingen internetanslutning tillg\\xE4nglig.\\nDina \\xE4ndringar kommer inte att sparas!\"\n};\nvar errors = {\n  unsupportedFileType: \"Filtypen st\\xF6ds inte.\",\n  imageInsertError: \"Kunde inte infoga bild. F\\xF6rs\\xF6k igen senare...\",\n  fileTooBig: \"Filen \\xE4r f\\xF6r stor. Maximal till\\xE5ten storlek \\xE4r {{maxSize}}.\",\n  svgImageInsertError: \"Kunde inte infoga SVG-bild. SVG-koden ser ogiltig ut.\",\n  failedToFetchImage: \"Kunde inte h\\xE4mta bilden.\",\n  invalidSVGString: \"Ogiltig SVG.\",\n  cannotResolveCollabServer: \"Det gick inte att ansluta till samarbets-servern. Ladda om sidan och f\\xF6rs\\xF6k igen.\",\n  importLibraryError: \"Kunde inte ladda bibliotek\",\n  collabSaveFailed: \"Det gick inte att spara i backend-databasen. Om problemen kvarst\\xE5r b\\xF6r du spara filen lokalt f\\xF6r att se till att du inte f\\xF6rlorar ditt arbete.\",\n  collabSaveFailed_sizeExceeded: \"Det gick inte att spara till backend-databasen, whiteboarden verkar vara f\\xF6r stor. Du b\\xF6r spara filen lokalt f\\xF6r att du inte ska f\\xF6rlora ditt arbete.\",\n  imageToolNotSupported: \"Bilder \\xE4r inaktiverade.\",\n  brave_measure_text_error: {\n    line1: \"Det ser ut som om du anv\\xE4nder Brave-webbl\\xE4saren med <bold>Aggressivt Blockera fingeravtryck</bold> inst\\xE4llningen aktiverad.\",\n    line2: \"Detta kan resultera i trasiga <bold>Textelement</bold> i dina ritningar.\",\n    line3: \"Vi rekommenderar starkt att du inaktiverar den h\\xE4r inst\\xE4llningen. Du kan f\\xF6lja <link>dessa steg</link> f\\xF6r att inaktivera den.\",\n    line4: \"Om inaktivering av denna inst\\xE4llning inte \\xE5tg\\xE4rdar visningen av textelement, \\xF6ppna ett <issueLink>\\xE4rende</issueLink> p\\xE5 v\\xE5r GitHub, eller skriv till oss p\\xE5 <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Inb\\xE4ddbara element kan inte l\\xE4ggas till i biblioteket.\",\n    iframe: \"IFrame-element kan inte l\\xE4ggas till i biblioteket.\",\n    image: \"St\\xF6d f\\xF6r att l\\xE4gga till bilder till biblioteket kommer snart!\"\n  },\n  asyncPasteFailedOnRead: \"Kunde inte klistra in (kunde inte l\\xE4sa fr\\xE5n urklipp).\",\n  asyncPasteFailedOnParse: \"Kunde inte klistra in.\",\n  copyToSystemClipboardFailed: \"Kunde inte kopiera till urklipp.\"\n};\nvar toolBar = {\n  selection: \"Markering\",\n  image: \"Infoga bild\",\n  rectangle: \"Rektangel\",\n  diamond: \"Diamant\",\n  ellipse: \"Ellips\",\n  arrow: \"Pil\",\n  line: \"Linje\",\n  freedraw: \"Rita\",\n  text: \"Text\",\n  library: \"Bibliotek\",\n  lock: \"H\\xE5ll valt verktyg aktivt efter ritande\",\n  penMode: \"Pennl\\xE4ge - f\\xF6rhindra touch\",\n  link: \"L\\xE4gg till / Uppdatera l\\xE4nk f\\xF6r en vald form\",\n  eraser: \"Radergummi\",\n  frame: \"Rutverktyg\",\n  magicframe: \"Tr\\xE5dram till kod\",\n  embeddable: \"B\\xE4dda in (web)\",\n  laser: \"Laserpekare\",\n  hand: \"Hand (panoreringsverktyg)\",\n  extraTools: \"Fler verktyg\",\n  mermaidToExcalidraw: \"Mermaid till Excalidraw\",\n  magicSettings: \"AI-inst\\xE4llningar\"\n};\nvar headings = {\n  canvasActions: \"Canvas-\\xE5tg\\xE4rder\",\n  selectedShapeActions: \"Valda form\\xE5tg\\xE4rder\",\n  shapes: \"Former\"\n};\nvar hints = {\n  canvasPanning: \"F\\xF6r att flytta whiteboarden, h\\xE5ll mushjulet eller mellanslagstangenten medan du drar eller anv\\xE4nd handverktyget\",\n  linearElement: \"Klicka f\\xF6r att starta flera punkter, dra f\\xF6r en linje\",\n  freeDraw: \"Klicka och dra, sl\\xE4pp n\\xE4r du \\xE4r klar\",\n  text: \"Tips: du kan ocks\\xE5 l\\xE4gga till text genom att dubbelklicka var som helst med markeringsverktyget\",\n  embeddable: \"Klicka-dra f\\xF6r att skapa en webbplats-inb\\xE4ddning\",\n  text_selected: \"Dubbelklicka eller tryck ENTER f\\xF6r att redigera text\",\n  text_editing: \"Tryck Escape eller CtrlOrCmd + ENTER f\\xF6r att slutf\\xF6ra redigeringen\",\n  linearElementMulti: \"Klicka p\\xE5 sista punkten eller tryck Escape eller Enter f\\xF6r att avsluta\",\n  lockAngle: \"Du kan begr\\xE4nsa vinkeln genom att h\\xE5lla SKIFT\",\n  resize: \"Du kan beh\\xE5lla proportioner genom att h\\xE5lla SHIFT medan du \\xE4ndrar storlek,\\nh\\xE5ller du ALT \\xE4ndras storlek relativt mitten\",\n  resizeImage: \"Du kan \\xE4ndra storlek fritt genom att h\\xE5lla SHIFT,\\nh\\xE5ll ALT f\\xF6r att \\xE4ndra storlek fr\\xE5n mitten\",\n  rotate: \"Du kan begr\\xE4nsa vinklar genom att h\\xE5lla SHIFT medan du roterar\",\n  lineEditor_info: \"H\\xE5ll Ctrl/Cmd och dubbelklicka eller tryck p\\xE5 Ctrl/Cmd + Enter f\\xF6r att redigera punkter\",\n  lineEditor_pointSelected: \"Tryck p\\xE5 Ta bort f\\xF6r att ta bort punkt(er), Ctrl + D eller Cmd + D f\\xF6r att duplicera, eller dra f\\xF6r att flytta\",\n  lineEditor_nothingSelected: \"V\\xE4lj en punkt att redigera (h\\xE5ll SHIFT f\\xF6r att v\\xE4lja flera),\\neller h\\xE5ll ned Alt och klicka f\\xF6r att l\\xE4gga till nya punkter\",\n  placeImage: \"Klicka f\\xF6r att placera bilden, eller klicka och dra f\\xF6r att st\\xE4lla in dess storlek manuellt\",\n  publishLibrary: \"Publicera ditt eget bibliotek\",\n  bindTextToElement: \"Tryck p\\xE5 Enter f\\xF6r att l\\xE4gga till text\",\n  deepBoxSelect: \"H\\xE5ll Ctrl eller Cmd f\\xF6r att djupv\\xE4lja, och f\\xF6r att f\\xF6rhindra att dra\",\n  eraserRevert: \"H\\xE5ll Alt f\\xF6r att \\xE5terst\\xE4lla de element som \\xE4r markerade f\\xF6r borttagning\",\n  firefox_clipboard_write: 'Denna funktion kan sannolikt aktiveras genom att st\\xE4lla in \"dom.events.asyncClipboard.clipboardItem\" flaggan till \"true\". F\\xF6r att \\xE4ndra webbl\\xE4sarens flaggor i Firefox, bes\\xF6k \"about:config\" sidan.',\n  disableSnapping: \"H\\xE5ll Ctrl eller Cmd f\\xF6r att inaktivera f\\xE4stning\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Kan inte visa f\\xF6rhandsgranskning\",\n  canvasTooBig: \"Canvasen kan vara f\\xF6r stor.\",\n  canvasTooBigTip: \"Tips: prova att flytta de mest avl\\xE4gsna elementen lite n\\xE4rmare varandra.\"\n};\nvar errorSplash = {\n  headingMain: \"Ett fel uppstod. F\\xF6rs\\xF6k <button>med att l\\xE4sa in sidan p\\xE5 nytt.</button>\",\n  clearCanvasMessage: \"Om omladdning inte fungerar, f\\xF6rs\\xF6k <button>rensa canvasen.</button>\",\n  clearCanvasCaveat: \" Detta kommer att leda till f\\xF6rlust av arbete \",\n  trackedToSentry: \"Felet med identifieraren {{eventId}} sp\\xE5rades p\\xE5 v\\xE5rt system.\",\n  openIssueMessage: \"Vi var mycket f\\xF6rsiktiga med att inte inkludera din skissinformation om felet. Om din skiss inte \\xE4r privat, v\\xE4nligen \\xF6verv\\xE4ga att f\\xF6lja upp p\\xE5 v\\xE5r <button>buggsp\\xE5rare.</button> V\\xE4nligen inkludera information nedan genom att kopiera och klistra in i GitHub-problemet.\",\n  sceneContent: \"Skissinneh\\xE5ll:\"\n};\nvar roomDialog = {\n  desc_intro: \"Du kan bjuda in personer till din nuvarande skiss f\\xF6r att samarbeta med dig.\",\n  desc_privacy: \"Oroa dig inte, sessionen anv\\xE4nder kryptering fr\\xE5n \\xE4nde till \\xE4nde, s\\xE5 vad du \\xE4n ritar kommer att f\\xF6rbli privat. Inte ens v\\xE5r server kommer att kunna se vad du skissar.\",\n  button_startSession: \"Starta sessionen\",\n  button_stopSession: \"Stoppa session\",\n  desc_inProgressIntro: \"Nu p\\xE5g\\xE5r en live-samarbetssession.\",\n  desc_shareLink: \"Dela denna l\\xE4nk med n\\xE5gon du vill samarbeta med:\",\n  desc_exitSession: \"Att avbryta sessionen kommer att koppla bort dig fr\\xE5n rummet, men du kommer att kunna forts\\xE4tta arbeta med skissen, lokalt. Observera att detta inte p\\xE5verkar andra m\\xE4nniskor, och de kommer fortfarande att kunna samarbeta p\\xE5 deras version.\",\n  shareTitle: \"Delta i en live-samarbetssession p\\xE5 Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Fel\"\n};\nvar exportDialog = {\n  disk_title: \"Spara till disk\",\n  disk_details: \"Exportera skissdata till en fil som du kan importera fr\\xE5n senare.\",\n  disk_button: \"Spara till fil\",\n  link_title: \"Delbar l\\xE4nk\",\n  link_details: \"Exportera som en skrivskyddad l\\xE4nk.\",\n  link_button: \"Exportera till l\\xE4nk\",\n  excalidrawplus_description: \"Spara skissen till din Excalidraw+ arbetsyta.\",\n  excalidrawplus_button: \"Exportera\",\n  excalidrawplus_exportError: \"Det gick inte att exportera till Excalidraw+ just nu...\"\n};\nvar helpDialog = {\n  blog: \"L\\xE4s v\\xE5r blogg\",\n  click: \"klicka\",\n  deepSelect: \"Djupval\",\n  deepBoxSelect: \"Djupval inom boxen, och f\\xF6rhindra att dra\",\n  curvedArrow: \"B\\xF6jd pil\",\n  curvedLine: \"B\\xF6jd linje\",\n  documentation: \"Dokumentation\",\n  doubleClick: \"dubbelklicka\",\n  drag: \"dra\",\n  editor: \"Redigerare\",\n  editLineArrowPoints: \"Redigera linje-/pilpunkter\",\n  editText: \"Redigera text / l\\xE4gg till etikett\",\n  github: \"Hittat ett problem? Rapportera\",\n  howto: \"F\\xF6lj v\\xE5ra guider\",\n  or: \"eller\",\n  preventBinding: \"F\\xF6rhindra pilbindning\",\n  tools: \"Verktyg\",\n  shortcuts: \"Tangentbordsgenv\\xE4gar\",\n  textFinish: \"Slutf\\xF6r redigering (text)\",\n  textNewLine: \"L\\xE4gg till ny rad (text)\",\n  title: \"Hj\\xE4lp\",\n  view: \"Visa\",\n  zoomToFit: \"Zooma f\\xF6r att rymma alla element\",\n  zoomToSelection: \"Zooma till markering\",\n  toggleElementLock: \"L\\xE5s/L\\xE5s upp valda\",\n  movePageUpDown: \"Flytta sida upp/ner\",\n  movePageLeftRight: \"Flytta sida v\\xE4nster/h\\xF6ger\"\n};\nvar clearCanvasDialog = {\n  title: \"Rensa canvas\"\n};\nvar publishDialog = {\n  title: \"Publicera bibliotek\",\n  itemName: \"Objektnamn\",\n  authorName: \"Upphovsmannens namn\",\n  githubUsername: \"GitHub-anv\\xE4ndarnamn\",\n  twitterUsername: \"Twitter-anv\\xE4ndarnamn\",\n  libraryName: \"Biblioteksnamn\",\n  libraryDesc: \"Biblioteksbeskrivning\",\n  website: \"Webbplats\",\n  placeholder: {\n    authorName: \"Ditt namn eller anv\\xE4ndarnamn\",\n    libraryName: \"Namn p\\xE5 ditt bibliotek\",\n    libraryDesc: \"Beskrivning av ditt bibliotek f\\xF6r att hj\\xE4lpa m\\xE4nniskor att f\\xF6rst\\xE5 dess anv\\xE4ndning\",\n    githubHandle: \"Github-anv\\xE4ndarnamn (valfritt), s\\xE5 att du kan redigera biblioteket n\\xE4r du har skickat in det f\\xF6r granskning\",\n    twitterHandle: \"Twitter-anv\\xE4ndarnamn (valfritt), s\\xE5 vi vet vem att kreditera n\\xE4r du marknadsf\\xF6r p\\xE5 Twitter\",\n    website: \"L\\xE4nk till din personliga webbplats eller n\\xE5gon annan (valfritt)\"\n  },\n  errors: {\n    required: \"Obligatoriskt\",\n    website: \"Ange en giltig URL\"\n  },\n  noteDescription: \"Skicka ditt bibliotek f\\xF6r att inkluderas i <link>det offentliga bibliotekets arkiv</link>f\\xF6r andra m\\xE4nniskor att anv\\xE4nda i sina skisser.\",\n  noteGuidelines: \"Biblioteket m\\xE5ste godk\\xE4nnas manuellt f\\xF6rst. V\\xE4nligen l\\xE4s <link>riktlinjerna</link> innan du skickar in. Du beh\\xF6ver ett GitHub-konto f\\xF6r att kommunicera och g\\xF6ra \\xE4ndringar om s\\xE5 \\xF6nskas, men det kr\\xE4vs inte.\",\n  noteLicense: \"Genom att skicka in godk\\xE4nner du att biblioteket kommer att publiceras under <link>MIT-licens, </link>vilket kort sagt betyder att vem som helst kan anv\\xE4nda det utan restriktioner.\",\n  noteItems: \"Varje objekt m\\xE5ste ha sitt eget namn s\\xE5 att det \\xE4r filtrerbart. F\\xF6ljande objekt kommer att inkluderas:\",\n  atleastOneLibItem: \"V\\xE4lj minst ett biblioteksobjekt f\\xF6r att komma ig\\xE5ng\",\n  republishWarning: \"Obs: n\\xE5gra av de markerade objekten \\xE4r redan markerade som publicerade/skickade. Du b\\xF6r endast skicka objekt igen n\\xE4r du uppdaterar ett befintligt bibliotek eller inl\\xE4mning.\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliotek inskickat\",\n  content: \"Tack {{authorName}}. Ditt bibliotek har skickats f\\xF6r granskning. Du kan f\\xF6lja status<link>h\\xE4r</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\\xC5terst\\xE4ll bibliotek\",\n  removeItemsFromLib: \"Ta bort markerade objekt fr\\xE5n biblioteket\"\n};\nvar imageExportDialog = {\n  header: \"Exportera bild\",\n  label: {\n    withBackground: \"Bakgrund\",\n    onlySelected: \"Endast markerade\",\n    darkMode: \"M\\xF6rkt l\\xE4ge\",\n    embedScene: \"B\\xE4dda in skiss\",\n    scale: \"Skala\",\n    padding: \"Utfyllnad\"\n  },\n  tooltip: {\n    embedScene: \"Skissdata kommer att sparas i den exporterade PNG/SVG-filen s\\xE5 att skissen kan \\xE5terst\\xE4llas fr\\xE5n den.\\nKommer att \\xF6ka exporterad filstorlek.\"\n  },\n  title: {\n    exportToPng: \"Exportera till PNG\",\n    exportToSvg: \"Exportera till SVG\",\n    copyPngToClipboard: \"Kopiera PNG till urklipp\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Kopiera till urklipp\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Dina skisser \\xE4r krypterade fr\\xE5n \\xE4nde till \\xE4nde s\\xE5 Excalidraws servrar kommer aldrig att se dem.\",\n  link: \"Blogginl\\xE4gg om kryptering fr\\xE5n \\xE4nde till \\xE4nde i Excalidraw\"\n};\nvar stats = {\n  angle: \"Vinkel\",\n  element: \"Element\",\n  elements: \"Element\",\n  height: \"H\\xF6jd\",\n  scene: \"Skiss\",\n  selected: \"Valda\",\n  storage: \"Lagring\",\n  title: \"Statistik f\\xF6r n\\xF6rdar\",\n  total: \"Totalt\",\n  version: \"Version\",\n  versionCopy: \"Klicka f\\xF6r att kopiera\",\n  versionNotAvailable: \"Versionen \\xE4r inte tillg\\xE4nglig\",\n  width: \"Bredd\"\n};\nvar toast = {\n  addedToLibrary: \"Tillagd i biblioteket\",\n  copyStyles: \"Kopierade stilar.\",\n  copyToClipboard: \"Kopierad till urklipp.\",\n  copyToClipboardAsPng: \"Kopierade {{exportSelection}} till urklipp som PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Fil sparad.\",\n  fileSavedToFilename: \"Sparad till {filename}\",\n  canvas: \"canvas\",\n  selection: \"markering\",\n  pasteAsSingleElement: \"Anv\\xE4nd {{shortcut}} f\\xF6r att klistra in som ett enda element,\\neller klistra in i en befintlig textredigerare\",\n  unableToEmbed: \"Att b\\xE4dda in denna webbadress \\xE4r f\\xF6r n\\xE4rvarande inte till\\xE5tet. Skapa en problemrapport p\\xE5 GitHub f\\xF6r att beg\\xE4ra att webbadressen vitlistas.\",\n  unrecognizedLinkFormat: \"L\\xE4nken du b\\xE4ddade in matchar inte det f\\xF6rv\\xE4ntade formatet. F\\xF6rs\\xF6k klistra in 'embed'-str\\xE4ngen som tillhandah\\xE5lls av k\\xE4llwebbplatsen\"\n};\nvar colors = {\n  transparent: \"Genomskinlig\",\n  black: \"Svart\",\n  white: \"Vit\",\n  red: \"R\\xF6d\",\n  pink: \"Rosa\",\n  grape: \"Lila\",\n  violet: \"Violett\",\n  gray: \"Gr\\xE5\",\n  blue: \"Bl\\xE5\",\n  cyan: \"Turkos\",\n  teal: \"Bl\\xE5gr\\xF6n\",\n  green: \"Gr\\xF6n\",\n  yellow: \"Gul\",\n  orange: \"Orange\",\n  bronze: \"Brons\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"All data sparas lokalt i din webbl\\xE4sare.\",\n    center_heading_plus: \"Ville du g\\xE5 till Excalidraw+ ist\\xE4llet?\",\n    menuHint: \"Exportera, inst\\xE4llningar, spr\\xE5k, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportera, inst\\xE4llningar och mer...\",\n    center_heading: \"F\\xF6renklade. Diagram.\",\n    toolbarHint: \"V\\xE4lj ett verktyg & b\\xF6rja rita!\",\n    helpHint: \"Genv\\xE4gar & hj\\xE4lp\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Mest frekvent anv\\xE4nda anpassade f\\xE4rger\",\n  colors: \"F\\xE4rger\",\n  shades: \"Nyanser\",\n  hexCode: \"Hex-kod\",\n  noShades: \"Inga nyanser tillg\\xE4ngliga f\\xF6r denna f\\xE4rg\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportera som bild\",\n      button: \"Exportera som bild\",\n      description: \"Exportera scendata som en bild fr\\xE5n vilken du kan importera senare.\"\n    },\n    saveToDisk: {\n      title: \"Spara till disk\",\n      button: \"Spara till disk\",\n      description: \"Exportera scendata till en fil fr\\xE5n vilken du kan importera senare.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exportera till Excalidraw+\",\n      description: \"Spara skissen till din Excalidraw+ arbetsyta.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"L\\xE4s in fr\\xE5n fil\",\n      button: \"L\\xE4s in fr\\xE5n fil\",\n      description: \"Laddar fr\\xE5n en fil kommer <bold>ers\\xE4tta ditt befintliga inneh\\xE5ll</bold>.<br></br>Du kan s\\xE4kerhetskopiera din ritning f\\xF6rst med hj\\xE4lp av ett av alternativen nedan.\"\n    },\n    shareableLink: {\n      title: \"L\\xE4s in fr\\xE5n l\\xE4nk\",\n      button: \"Ers\\xE4tt mitt inneh\\xE5ll\",\n      description: \"Inl\\xE4sning av en extern ritning kommer <bold>ers\\xE4tta ditt befintliga inneh\\xE5ll</bold>.<br></br>Du kan s\\xE4kerhetskopiera din ritning f\\xF6rst genom att anv\\xE4nda ett av alternativen nedan.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid till Excalidraw\",\n  button: \"Infoga\",\n  description: \"F\\xF6r n\\xE4rvarande st\\xF6ds endast <flowchartLink>Fl\\xF6desdiagram</flowchartLink>,<sequenceLink> Sekvensdiagram </sequenceLink> och <classLink>Klassdiagram</classLink>. De andra typerna kommer att \\xE5terges som bild i Excalidraw.\",\n  syntax: \"Mermaid-syntax\",\n  preview: \"F\\xF6rhandsgranska\"\n};\nvar sv_SE_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=sv-SE-V32YHALQ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/sv-SE-V32YHALQ.js\n"));

/***/ })

}]);