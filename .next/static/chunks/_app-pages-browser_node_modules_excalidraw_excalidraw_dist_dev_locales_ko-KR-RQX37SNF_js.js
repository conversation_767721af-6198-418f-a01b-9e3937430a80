"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_ko-KR-RQX37SNF_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/ko-KR-RQX37SNF.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/ko-KR-RQX37SNF.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ ko_KR_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/ko-KR.json\nvar labels = {\n  paste: \"\\uBD99\\uC5EC\\uB123\\uAE30\",\n  pasteAsPlaintext: \"\\uC77C\\uBC18 \\uD14D\\uC2A4\\uD2B8\\uB85C \\uBD99\\uC5EC\\uB123\\uAE30\",\n  pasteCharts: \"\\uCC28\\uD2B8 \\uBD99\\uC5EC\\uB123\\uAE30\",\n  selectAll: \"\\uC804\\uCCB4 \\uC120\\uD0DD\",\n  multiSelect: \"\\uC120\\uD0DD \\uC601\\uC5ED\\uC5D0 \\uCD94\\uAC00\\uD558\\uAE30\",\n  moveCanvas: \"\\uCE94\\uBC84\\uC2A4 \\uC774\\uB3D9\",\n  cut: \"\\uC798\\uB77C\\uB0B4\\uAE30\",\n  copy: \"\\uBCF5\\uC0AC\",\n  copyAsPng: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C PNG \\uC774\\uBBF8\\uC9C0 \\uBCF5\\uC0AC\",\n  copyAsSvg: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C SVG \\uC774\\uBBF8\\uC9C0 \\uBCF5\\uC0AC\",\n  copyText: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uD14D\\uC2A4\\uD2B8 \\uBCF5\\uC0AC\",\n  copySource: \"\\uC18C\\uC2A4\\uCF54\\uB4DC\\uB97C \\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uBCF5\\uC0AC\",\n  convertToCode: \"\\uCF54\\uB4DC\\uB85C \\uBCC0\\uD658\",\n  bringForward: \"\\uC55E\\uC73C\\uB85C \\uAC00\\uC838\\uC624\\uAE30\",\n  sendToBack: \"\\uB9E8 \\uB4A4\\uB85C \\uBCF4\\uB0B4\\uAE30\",\n  bringToFront: \"\\uB9E8 \\uC55E\\uC73C\\uB85C \\uAC00\\uC838\\uC624\\uAE30\",\n  sendBackward: \"\\uB4A4\\uB85C \\uBCF4\\uB0B4\\uAE30\",\n  delete: \"\\uC0AD\\uC81C\",\n  copyStyles: \"\\uC2A4\\uD0C0\\uC77C \\uBCF5\\uC0AC\\uD558\\uAE30\",\n  pasteStyles: \"\\uC2A4\\uD0C0\\uC77C \\uBD99\\uC5EC\\uB123\\uAE30\",\n  stroke: \"\\uC120 \\uC0C9\\uC0C1\",\n  background: \"\\uBC30\\uACBD\\uC0C9\",\n  fill: \"\\uCC44\\uC6B0\\uAE30\",\n  strokeWidth: \"\\uC120 \\uAD75\\uAE30\",\n  strokeStyle: \"\\uC120\",\n  strokeStyle_solid: \"\\uC2E4\\uC120\",\n  strokeStyle_dashed: \"\\uD30C\\uC120\",\n  strokeStyle_dotted: \"\\uC810\\uC120\",\n  sloppiness: \"\\uB300\\uCDA9 \\uAE0B\\uAE30\",\n  opacity: \"\\uBD88\\uD22C\\uBA85\\uB3C4\",\n  textAlign: \"\\uD14D\\uC2A4\\uD2B8 \\uC815\\uB82C\",\n  edges: \"\\uAC00\\uC7A5\\uC790\\uB9AC\",\n  sharp: \"\\uBFB0\\uC871\\uD558\\uAC8C\",\n  round: \"\\uB465\\uAE00\\uAC8C\",\n  arrowheads: \"\\uD654\\uC0B4\\uCD09\",\n  arrowhead_none: \"\\uC5C6\\uC74C\",\n  arrowhead_arrow: \"\\uD654\\uC0B4\\uD45C\",\n  arrowhead_bar: \"\\uB9C9\\uB300\",\n  arrowhead_circle: \"\\uC6D0\",\n  arrowhead_circle_outline: \"\\uC6D0 (\\uC678\\uACFD\\uC120)\",\n  arrowhead_triangle: \"\\uC0BC\\uAC01\\uD615\",\n  arrowhead_triangle_outline: \"\\uC0BC\\uAC01\\uD615 (\\uC678\\uACFD\\uC120)\",\n  arrowhead_diamond: \"\\uB9C8\\uB984\\uBAA8\",\n  arrowhead_diamond_outline: \"\\uB9C8\\uB984\\uBAA8 (\\uC678\\uACFD\\uC120)\",\n  fontSize: \"\\uAE00\\uC790 \\uD06C\\uAE30\",\n  fontFamily: \"\\uAE00\\uAF34\",\n  addWatermark: '\"Made with Excalidraw\" \\uCD94\\uAC00',\n  handDrawn: \"\\uC190\\uAE00\\uC528\",\n  normal: \"\\uC77C\\uBC18\",\n  code: \"\\uCF54\\uB4DC\",\n  small: \"\\uC791\\uAC8C\",\n  medium: \"\\uBCF4\\uD1B5\",\n  large: \"\\uD06C\\uAC8C\",\n  veryLarge: \"\\uB9E4\\uC6B0 \\uD06C\\uAC8C\",\n  solid: \"\\uB2E8\\uC0C9\",\n  hachure: \"\\uD3C9\\uD589\\uC120\",\n  zigzag: \"\\uC9C0\\uADF8\\uC7AC\\uADF8\",\n  crossHatch: \"\\uAD50\\uCC28\\uC120\",\n  thin: \"\\uC587\\uAC8C\",\n  bold: \"\\uAD75\\uAC8C\",\n  left: \"\\uC67C\\uCABD\",\n  center: \"\\uAC00\\uC6B4\\uB370\",\n  right: \"\\uC624\\uB978\\uCABD\",\n  extraBold: \"\\uB9E4\\uC6B0 \\uAD75\\uAC8C\",\n  architect: \"\\uAC74\\uCD95\\uAC00\",\n  artist: \"\\uC608\\uC220\\uAC00\",\n  cartoonist: \"\\uB9CC\\uD654\\uAC00\",\n  fileTitle: \"\\uD30C\\uC77C \\uC774\\uB984\",\n  colorPicker: \"\\uC0C9\\uC0C1 \\uC120\\uD0DD\\uAE30\",\n  canvasColors: \"\\uCE94\\uBC84\\uC2A4\\uC5D0\\uC11C \\uC0AC\\uC6A9\\uB418\\uC5C8\\uC74C\",\n  canvasBackground: \"\\uCE94\\uBC84\\uC2A4 \\uBC30\\uACBD\",\n  drawingCanvas: \"\\uCE94\\uBC84\\uC2A4 \\uADF8\\uB9AC\\uAE30\",\n  layers: \"\\uB808\\uC774\\uC5B4\",\n  actions: \"\\uB3D9\\uC791\",\n  language: \"\\uC5B8\\uC5B4\",\n  liveCollaboration: \"\\uC2E4\\uC2DC\\uAC04 \\uD611\\uC5C5...\",\n  duplicateSelection: \"\\uBCF5\\uC81C\",\n  untitled: \"\\uC81C\\uBAA9 \\uC5C6\\uC74C\",\n  name: \"\\uC774\\uB984\",\n  yourName: \"\\uC774\\uB984 \\uC785\\uB825\",\n  madeWithExcalidraw: \"Made with Excalidraw\",\n  group: \"\\uADF8\\uB8F9 \\uC0DD\\uC131\",\n  ungroup: \"\\uADF8\\uB8F9 \\uD574\\uC81C\",\n  collaborators: \"\\uACF5\\uB3D9 \\uC791\\uC5C5\\uC790\",\n  showGrid: \"\\uADF8\\uB9AC\\uB4DC \\uBCF4\\uAE30\",\n  addToLibrary: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uCD94\\uAC00\",\n  removeFromLibrary: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0\\uC11C \\uC81C\\uAC70\",\n  libraryLoadingMessage: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uBD88\\uB7EC\\uC624\\uB294 \\uC911\\u2026\",\n  libraries: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uCC3E\\uAE30\",\n  loadingScene: \"\\uD654\\uBA74 \\uBD88\\uB7EC\\uC624\\uB294 \\uC911\\u2026\",\n  align: \"\\uC815\\uB82C\",\n  alignTop: \"\\uC0C1\\uB2E8 \\uC815\\uB82C\",\n  alignBottom: \"\\uD558\\uB2E8 \\uC815\\uB82C\",\n  alignLeft: \"\\uC67C\\uCABD \\uC815\\uB82C\",\n  alignRight: \"\\uC624\\uB978\\uCABD \\uC815\\uB82C\",\n  centerVertically: \"\\uC218\\uC9C1\\uC73C\\uB85C \\uC911\\uC559 \\uC815\\uB82C\",\n  centerHorizontally: \"\\uC218\\uD3C9\\uC73C\\uB85C \\uC911\\uC559 \\uC815\\uB82C\",\n  distributeHorizontally: \"\\uC218\\uD3C9\\uC73C\\uB85C \\uBD84\\uBC30\",\n  distributeVertically: \"\\uC218\\uC9C1\\uC73C\\uB85C \\uBD84\\uBC30\",\n  flipHorizontal: \"\\uC88C\\uC6B0\\uBC18\\uC804\",\n  flipVertical: \"\\uC0C1\\uD558\\uBC18\\uC804\",\n  viewMode: \"\\uBCF4\\uAE30 \\uBAA8\\uB4DC\",\n  share: \"\\uACF5\\uC720\",\n  showStroke: \"\\uC724\\uACFD\\uC120 \\uC0C9\\uC0C1 \\uC120\\uD0DD\\uAE30 \\uC5F4\\uAE30\",\n  showBackground: \"\\uBC30\\uACBD \\uC0C9\\uC0C1 \\uC120\\uD0DD\\uAE30 \\uC5F4\\uAE30\",\n  toggleTheme: \"\\uD14C\\uB9C8 \\uC804\\uD658\",\n  personalLib: \"\\uAC1C\\uC778 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\",\n  excalidrawLib: \"Excalidraw \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\",\n  decreaseFontSize: \"\\uD3F0\\uD2B8 \\uC0AC\\uC774\\uC988 \\uC904\\uC774\\uAE30\",\n  increaseFontSize: \"\\uD3F0\\uD2B8 \\uC0AC\\uC774\\uC988 \\uD0A4\\uC6B0\\uAE30\",\n  unbindText: \"\\uD14D\\uC2A4\\uD2B8 \\uBD84\\uB9AC\",\n  bindText: \"\\uD14D\\uC2A4\\uD2B8\\uB97C \\uCEE8\\uD14C\\uC774\\uB108\\uC5D0 \\uACB0\\uD569\",\n  createContainerFromText: \"\\uD14D\\uC2A4\\uD2B8\\uB97C \\uCEE8\\uD14C\\uC774\\uB108\\uC5D0 \\uB2F4\\uAE30\",\n  link: {\n    edit: \"\\uB9C1\\uD06C \\uC218\\uC815\\uD558\\uAE30\",\n    editEmbed: \"\\uB9C1\\uD06C & \\uC784\\uBCA0\\uB4DC \\uC218\\uC815\\uD558\\uAE30\",\n    create: \"\\uB9C1\\uD06C \\uB9CC\\uB4E4\\uAE30\",\n    createEmbed: \"\\uB9C1\\uD06C & \\uC784\\uBCA0\\uB4DC \\uB9CC\\uB4E4\\uAE30\",\n    label: \"\\uB9C1\\uD06C\",\n    labelEmbed: \"\\uB9C1\\uD06C & \\uC784\\uBCA0\\uB4DC\",\n    empty: \"\\uB9C1\\uD06C\\uB97C \\uC9C0\\uC815\\uD558\\uC9C0 \\uC54A\\uC558\\uC2B5\\uB2C8\\uB2E4\"\n  },\n  lineEditor: {\n    edit: \"\\uC120 \\uC218\\uC815\\uD558\\uAE30\",\n    exit: \"\\uC120 \\uD3B8\\uC9D1\\uAE30 \\uC885\\uB8CC\"\n  },\n  elementLock: {\n    lock: \"\\uC7A0\\uAE08\",\n    unlock: \"\\uC7A0\\uAE08 \\uD574\\uC81C\",\n    lockAll: \"\\uBAA8\\uB450 \\uC7A0\\uAE08\",\n    unlockAll: \"\\uBAA8\\uB450 \\uC7A0\\uAE08 \\uD574\\uC81C\"\n  },\n  statusPublished: \"\\uAC8C\\uC2DC\\uB428\",\n  sidebarLock: \"\\uC0AC\\uC774\\uB4DC\\uBC14 \\uC720\\uC9C0\",\n  selectAllElementsInFrame: \"\\uD504\\uB808\\uC784\\uC758 \\uBAA8\\uB4E0 \\uC694\\uC18C \\uC120\\uD0DD\",\n  removeAllElementsFromFrame: \"\\uD504\\uB808\\uC784\\uC758 \\uBAA8\\uB4E0 \\uC694\\uC18C \\uC0AD\\uC81C\",\n  eyeDropper: \"\\uCE94\\uBC84\\uC2A4\\uC5D0\\uC11C \\uC0C9\\uC0C1 \\uACE0\\uB974\\uAE30\",\n  textToDiagram: \"\\uD14D\\uC2A4\\uD2B8\\uB97C \\uB2E4\\uC774\\uC5B4\\uADF8\\uB7A8\\uC73C\\uB85C\",\n  prompt: \"\\uD504\\uB86C\\uD504\\uD2B8\"\n};\nvar library = {\n  noItems: \"\\uCD94\\uAC00\\uB41C \\uC544\\uC774\\uD15C \\uC5C6\\uC74C\",\n  hint_emptyLibrary: \"\\uCE94\\uBC84\\uC2A4 \\uC704\\uC5D0\\uC11C \\uC544\\uC774\\uD15C\\uC744 \\uC120\\uD0DD\\uD558\\uC5EC \\uC5EC\\uAE30\\uC5D0 \\uCD94\\uAC00\\uB97C \\uD558\\uAC70\\uB098, \\uC544\\uB798\\uC758 \\uACF5\\uC6A9 \\uC800\\uC7A5\\uC18C\\uC5D0\\uC11C \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uC124\\uCE58\\uD558\\uC138\\uC694.\",\n  hint_emptyPrivateLibrary: \"\\uCE94\\uBC84\\uC2A4 \\uC704\\uC5D0\\uC11C \\uC544\\uC774\\uD15C\\uC744 \\uC120\\uD0DD\\uD558\\uC5EC \\uC5EC\\uAE30 \\uCD94\\uAC00\\uD558\\uC138\\uC694.\"\n};\nvar buttons = {\n  clearReset: \"\\uCE94\\uBC84\\uC2A4 \\uCD08\\uAE30\\uD654\",\n  exportJSON: \"\\uD30C\\uC77C\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n  exportImage: \"\\uC774\\uBBF8\\uC9C0 \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n  export: \"\\uB2E4\\uB978 \\uC774\\uB984\\uC73C\\uB85C \\uC800\\uC7A5...\",\n  copyToClipboard: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uBCF5\\uC0AC\",\n  save: \"\\uD604\\uC7AC \\uD30C\\uC77C\\uC5D0 \\uC800\\uC7A5\",\n  saveAs: \"\\uB2E4\\uB978 \\uC774\\uB984\\uC73C\\uB85C \\uC800\\uC7A5\",\n  load: \"\\uC5F4\\uAE30\",\n  getShareableLink: \"\\uACF5\\uC720 \\uAC00\\uB2A5\\uD55C \\uB9C1\\uD06C \\uC0DD\\uC131\",\n  close: \"\\uB2EB\\uAE30\",\n  selectLanguage: \"\\uC5B8\\uC5B4 \\uC120\\uD0DD\",\n  scrollBackToContent: \"\\uCF58\\uD150\\uCE20 \\uC601\\uC5ED\\uC73C\\uB85C \\uC2A4\\uD06C\\uB864\\uD558\\uAE30\",\n  zoomIn: \"\\uD655\\uB300\",\n  zoomOut: \"\\uCD95\\uC18C\",\n  resetZoom: \"\\uD655\\uB300/\\uCD95\\uC18C \\uCD08\\uAE30\\uD654\",\n  menu: \"\\uBA54\\uB274\",\n  done: \"\\uC644\\uB8CC\",\n  edit: \"\\uC218\\uC815\",\n  undo: \"\\uC2E4\\uD589 \\uCDE8\\uC18C\",\n  redo: \"\\uB2E4\\uC2DC \\uC2E4\\uD589\",\n  resetLibrary: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uB9AC\\uC14B\",\n  createNewRoom: \"\\uBC29 \\uB9CC\\uB4E4\\uAE30\",\n  fullScreen: \"\\uC804\\uCCB4\\uD654\\uBA74\",\n  darkMode: \"\\uB2E4\\uD06C \\uBAA8\\uB4DC\",\n  lightMode: \"\\uBC1D\\uC740 \\uBAA8\\uB4DC\",\n  zenMode: \"\\uC820 \\uBAA8\\uB4DC\",\n  objectsSnapMode: \"\\uB2E4\\uB978 \\uC694\\uC18C\\uB4E4\\uC5D0 \\uC815\\uB82C\\uC2DC\\uD0A4\\uAE30\",\n  exitZenMode: \"\\uC820 \\uBAA8\\uB4DC \\uC885\\uB8CC\\uD558\\uAE30\",\n  cancel: \"\\uCDE8\\uC18C\",\n  clear: \"\\uC9C0\\uC6B0\\uAE30\",\n  remove: \"\\uC0AD\\uC81C\",\n  embed: \"\\uC784\\uBCA0\\uB529 \\uD1A0\\uAE00\",\n  publishLibrary: \"\\uAC8C\\uC2DC\\uD558\\uAE30\",\n  submit: \"\\uC81C\\uCD9C\",\n  confirm: \"\\uD655\\uC778\",\n  embeddableInteractionButton: \"\\uD074\\uB9AD\\uD558\\uC5EC \\uC0C1\\uD638\\uC791\\uC6A9\"\n};\nvar alerts = {\n  clearReset: \"\\uBAA8\\uB4E0 \\uC791\\uC5C5 \\uB0B4\\uC6A9\\uC774 \\uCD08\\uAE30\\uD654\\uB429\\uB2C8\\uB2E4. \\uACC4\\uC18D\\uD558\\uC2DC\\uACA0\\uC2B5\\uB2C8\\uAE4C?\",\n  couldNotCreateShareableLink: \"\\uACF5\\uC720 \\uAC00\\uB2A5\\uD55C \\uB9C1\\uD06C\\uB97C \\uC0DD\\uC131\\uD560 \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n  couldNotCreateShareableLinkTooBig: \"\\uACF5\\uC720 \\uAC00\\uB2A5\\uD55C \\uB9C1\\uD06C\\uB97C \\uC0DD\\uC131\\uD560 \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4: \\uD654\\uBA74\\uC774 \\uB108\\uBB34 \\uD07D\\uB2C8\\uB2E4.\",\n  couldNotLoadInvalidFile: \"\\uC720\\uD6A8\\uD558\\uC9C0 \\uC54A\\uC740 \\uD30C\\uC77C\\uC785\\uB2C8\\uB2E4.\",\n  importBackendFailed: \"\\uC11C\\uBC84\\uB85C\\uBD80\\uD130 \\uBD88\\uB7EC \\uC624\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4.\",\n  cannotExportEmptyCanvas: \"\\uBE48 \\uCE94\\uBC84\\uC2A4\\uB97C \\uB0B4\\uBCF4\\uB0BC \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n  couldNotCopyToClipboard: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uBCF5\\uC0AC\\uD558\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4.\",\n  decryptFailed: \"\\uB370\\uC774\\uD130\\uB97C \\uBCF5\\uD638\\uD654\\uD558\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4.\",\n  uploadedSecurly: \"\\uC5C5\\uB85C\\uB4DC\\uB294 \\uC885\\uB2E8 \\uAC04 \\uC554\\uD638\\uD654\\uB85C \\uBCF4\\uD638\\uB418\\uBBC0\\uB85C Excalidraw \\uC11C\\uBC84 \\uBC0F \\uD0C0\\uC0AC\\uAC00 \\uCF58\\uD150\\uCE20\\uB97C \\uC77D\\uC744 \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n  loadSceneOverridePrompt: \"\\uC678\\uBD80 \\uD30C\\uC77C\\uC744 \\uBD88\\uB7EC \\uC624\\uBA74 \\uAE30\\uC874 \\uCF58\\uD150\\uCE20\\uAC00 \\uB300\\uCCB4\\uB429\\uB2C8\\uB2E4. \\uACC4\\uC18D \\uC9C4\\uD589\\uD560\\uAE4C\\uC694?\",\n  collabStopOverridePrompt: \"\\uD611\\uC5C5 \\uC138\\uC158\\uC744 \\uC885\\uB8CC\\uD558\\uBA74 \\uB85C\\uCEEC \\uC800\\uC7A5\\uC18C\\uC5D0 \\uC788\\uB294 \\uADF8\\uB9BC\\uC774 \\uD611\\uC5C5 \\uC138\\uC158\\uC758 \\uADF8\\uB9BC\\uC73C\\uB85C \\uB300\\uCCB4\\uB429\\uB2C8\\uB2E4. \\uC9C4\\uD589\\uD558\\uACA0\\uC2B5\\uB2C8\\uAE4C?\\n\\n(\\uB85C\\uCEEC \\uC800\\uC7A5\\uC18C\\uC5D0 \\uC788\\uB294 \\uADF8\\uB9BC\\uC744 \\uC720\\uC9C0\\uD558\\uB824\\uBA74 \\uD604\\uC7AC \\uBE0C\\uB77C\\uC6B0\\uC800 \\uD0ED\\uC744 \\uB2EB\\uC544\\uC8FC\\uC138\\uC694.)\",\n  errorAddingToLibrary: \"\\uC544\\uC774\\uD15C\\uC744 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uCD94\\uAC00 \\uD560\\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4\",\n  errorRemovingFromLibrary: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0\\uC11C \\uC544\\uC774\\uD15C\\uC744 \\uC0AD\\uC81C\\uD560\\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4\",\n  confirmAddLibrary: \"{{numShapes}}\\uAC1C\\uC758 \\uBAA8\\uC591\\uC774 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uCD94\\uAC00\\uB429\\uB2C8\\uB2E4. \\uACC4\\uC18D\\uD558\\uC2DC\\uACA0\\uC5B4\\uC694?\",\n  imageDoesNotContainScene: \"\\uC774 \\uC774\\uBBF8\\uC9C0\\uB294 \\uD654\\uBA74 \\uB370\\uC774\\uD130\\uB97C \\uD3EC\\uD568\\uD558\\uACE0 \\uC788\\uC9C0 \\uC54A\\uC740 \\uAC83 \\uAC19\\uC2B5\\uB2C8\\uB2E4. \\uB0B4\\uBCF4\\uB0BC \\uB54C \\uD654\\uBA74\\uC744 \\uCCA8\\uBD80\\uD558\\uB3C4\\uB85D \\uC124\\uC815\\uD558\\uC168\\uB098\\uC694?\",\n  cannotRestoreFromImage: \"\\uC774\\uBBF8\\uC9C0 \\uD30C\\uC77C\\uC5D0\\uC11C \\uD654\\uBA74\\uC744 \\uBCF5\\uAD6C\\uD560 \\uC218 \\uC5C6\\uC5C8\\uC2B5\\uB2C8\\uB2E4\",\n  invalidSceneUrl: \"\\uC81C\\uACF5\\uB41C URL\\uC5D0\\uC11C \\uD654\\uBA74\\uC744 \\uAC00\\uC838\\uC624\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4. \\uC8FC\\uC18C\\uAC00 \\uC798\\uBABB\\uB418\\uAC70\\uB098, \\uC720\\uD6A8\\uD55C Excalidraw JSON \\uB370\\uC774\\uD130\\uB97C \\uD3EC\\uD568\\uD558\\uACE0 \\uC788\\uC9C0 \\uC54A\\uC740 \\uAC83\\uC77C \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\",\n  resetLibrary: \"\\uB2F9\\uC2E0\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uCD08\\uAE30\\uD654 \\uD569\\uB2C8\\uB2E4. \\uACC4\\uC18D\\uD558\\uC2DC\\uACA0\\uC2B5\\uB2C8\\uAE4C?\",\n  removeItemsFromsLibrary: \"{{count}}\\uAC1C\\uC758 \\uC544\\uC774\\uD15C\\uC744 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0\\uC11C \\uC0AD\\uC81C\\uD558\\uC2DC\\uACA0\\uC2B5\\uB2C8\\uAE4C?\",\n  invalidEncryptionKey: \"\\uC554\\uD638\\uD654 \\uD0A4\\uB294 \\uBC18\\uB4DC\\uC2DC 22\\uAE00\\uC790\\uC5EC\\uC57C \\uD569\\uB2C8\\uB2E4. \\uC2E4\\uC2DC\\uAC04 \\uD611\\uC5C5\\uC774 \\uBE44\\uD65C\\uC131\\uD654\\uB429\\uB2C8\\uB2E4.\",\n  collabOfflineWarning: \"\\uC778\\uD130\\uB137\\uC5D0 \\uC5F0\\uACB0\\uB418\\uC5B4 \\uC788\\uC9C0 \\uC54A\\uC2B5\\uB2C8\\uB2E4.\\n\\uBCC0\\uACBD \\uC0AC\\uD56D\\uB4E4\\uC774 \\uC800\\uC7A5\\uB418\\uC9C0 \\uC54A\\uC2B5\\uB2C8\\uB2E4!\"\n};\nvar errors = {\n  unsupportedFileType: \"\\uC9C0\\uC6D0\\uD558\\uC9C0 \\uC54A\\uB294 \\uD30C\\uC77C \\uD615\\uC2DD \\uC785\\uB2C8\\uB2E4.\",\n  imageInsertError: \"\\uC774\\uBBF8\\uC9C0\\uB97C \\uC0BD\\uC785\\uD560 \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4. \\uB098\\uC911\\uC5D0 \\uB2E4\\uC2DC \\uC2DC\\uB3C4 \\uD558\\uC2ED\\uC2DC\\uC624\",\n  fileTooBig: \"\\uD30C\\uC77C\\uC774 \\uB108\\uBB34 \\uD07D\\uB2C8\\uB2E4. \\uCD5C\\uB300 \\uD06C\\uAE30\\uB294 {{maxSize}} \\uC785\\uB2C8\\uB2E4.\",\n  svgImageInsertError: \"SVG \\uC774\\uBBF8\\uC9C0\\uB97C \\uC0BD\\uC785\\uD558\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4. SVG \\uBB38\\uBC95\\uC774 \\uC720\\uD6A8\\uD558\\uC9C0 \\uC54A\\uC740 \\uAC83 \\uAC19\\uC2B5\\uB2C8\\uB2E4.\",\n  failedToFetchImage: \"\\uC774\\uBBF8\\uC9C0\\uB97C \\uAC00\\uC838\\uC624\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4.\",\n  invalidSVGString: \"\\uC720\\uD6A8\\uD558\\uC9C0 \\uC54A\\uC740 SVG\\uC785\\uB2C8\\uB2E4.\",\n  cannotResolveCollabServer: \"\\uD611\\uC5C5 \\uC11C\\uBC84\\uC5D0 \\uC811\\uC18D\\uD558\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4. \\uD398\\uC774\\uC9C0\\uB97C \\uC0C8\\uB85C\\uACE0\\uCE68\\uD558\\uACE0 \\uB2E4\\uC2DC \\uC2DC\\uB3C4\\uD574\\uBCF4\\uC138\\uC694.\",\n  importLibraryError: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uBD88\\uB7EC\\uC624\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4.\",\n  collabSaveFailed: \"\\uB370\\uC774\\uD130\\uBCA0\\uC774\\uC2A4\\uC5D0 \\uC800\\uC7A5\\uD558\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4. \\uBB38\\uC81C\\uAC00 \\uACC4\\uC18D \\uB41C\\uB2E4\\uBA74, \\uC791\\uC5C5 \\uB0B4\\uC6A9\\uC744 \\uC783\\uC9C0 \\uC54A\\uB3C4\\uB85D \\uB85C\\uCEEC \\uC800\\uC7A5\\uC18C\\uC5D0 \\uC800\\uC7A5\\uD574 \\uC8FC\\uC138\\uC694.\",\n  collabSaveFailed_sizeExceeded: \"\\uB370\\uC774\\uD130\\uBCA0\\uC774\\uC2A4\\uC5D0 \\uC800\\uC7A5\\uD558\\uC9C0 \\uBABB\\uD588\\uC2B5\\uB2C8\\uB2E4. \\uCE94\\uBC84\\uC2A4\\uAC00 \\uB108\\uBB34 \\uD070 \\uAC70 \\uAC19\\uC2B5\\uB2C8\\uB2E4. \\uBB38\\uC81C\\uAC00 \\uACC4\\uC18D \\uB41C\\uB2E4\\uBA74, \\uC791\\uC5C5 \\uB0B4\\uC6A9\\uC744 \\uC783\\uC9C0 \\uC54A\\uB3C4\\uB85D \\uB85C\\uCEEC \\uC800\\uC7A5\\uC18C\\uC5D0 \\uC800\\uC7A5\\uD574 \\uC8FC\\uC138\\uC694.\",\n  imageToolNotSupported: \"\\uC774\\uBBF8\\uC9C0\\uAC00 \\uBE44\\uD65C\\uC131\\uD654 \\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4.\",\n  brave_measure_text_error: {\n    line1: \"\\uADC0\\uD558\\uAED8\\uC11C\\uB294 <bold>\\uAC15\\uB825\\uD55C \\uC9C0\\uBB38 \\uCC28\\uB2E8 \\uC124\\uC815</bold>\\uC774 \\uD65C\\uC131\\uD654\\uB41C Brave browser\\uB97C \\uC0AC\\uC6A9\\uD558\\uACE0 \\uACC4\\uC2E0 \\uAC83 \\uAC19\\uC2B5\\uB2C8\\uB2E4.\",\n    line2: \"\\uC774 \\uAE30\\uB2A5\\uC73C\\uB85C \\uC778\\uD574 \\uD654\\uC774\\uD2B8\\uBCF4\\uB4DC\\uC758 <bold>\\uD14D\\uC2A4\\uD2B8 \\uC694\\uC18C\\uB4E4</bold>\\uC774 \\uC190\\uC0C1\\uB420 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\",\n    line3: \"\\uC800\\uD76C\\uB294 \\uD574\\uB2F9 \\uAE30\\uB2A5\\uC744 \\uBE44\\uD65C\\uC131\\uD654\\uD558\\uB294 \\uAC83\\uC744 \\uAC15\\uB825\\uD788 \\uAD8C\\uC7A5 \\uB4DC\\uB9BD\\uB2C8\\uB2E4. \\uBE44\\uD65C\\uC131\\uD654 \\uBC29\\uBC95\\uC5D0 \\uB300\\uD574\\uC11C\\uB294 <link>\\uC774 \\uAC8C\\uC2DC\\uAE00</link>\\uC744 \\uCC38\\uACE0\\uD574\\uC8FC\\uC138\\uC694.\",\n    line4: \"\\uB9CC\\uC57D \\uC774 \\uC124\\uC815\\uC744 \\uAED0\\uC74C\\uC5D0\\uB3C4 \\uD14D\\uC2A4\\uD2B8 \\uC694\\uC18C\\uB4E4\\uC774 \\uC62C\\uBC14\\uB974\\uAC8C \\uD45C\\uC2DC\\uB418\\uC9C0 \\uC54A\\uB294\\uB2E4\\uBA74, \\uC800\\uD76C Github\\uC5D0 <issueLink>\\uC774\\uC288</issueLink>\\uB97C \\uC62C\\uB824\\uC8FC\\uC2DC\\uAC70\\uB098 <discordLink>Discord</discordLink>\\uB85C \\uC54C\\uB824\\uC8FC\\uC138\\uC694.\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\\uC784\\uBCA0\\uB4DC \\uC694\\uC18C\\uB4E4\\uC740 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uCD94\\uAC00\\uD560 \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n    iframe: \"IFrame \\uC694\\uC18C\\uB4E4\\uC740 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uCD94\\uAC00\\uD560 \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n    image: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uC774\\uBBF8\\uC9C0 \\uC0BD\\uC785 \\uAE30\\uB2A5\\uC740 \\uACE7 \\uC9C0\\uC6D0\\uB420 \\uC608\\uC815\\uC785\\uB2C8\\uB2E4!\"\n  },\n  asyncPasteFailedOnRead: \"\\uBD99\\uC5EC\\uB123\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4. (\\uC2DC\\uC2A4\\uD15C \\uD074\\uB9BD\\uBCF4\\uB4DC\\uB97C \\uC77D\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4)\",\n  asyncPasteFailedOnParse: \"\\uBD99\\uC5EC\\uB123\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4.\",\n  copyToSystemClipboardFailed: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uBCF5\\uC0AC\\uD558\\uB294\\uB370 \\uC2E4\\uD328\\uD588\\uC2B5\\uB2C8\\uB2E4.\"\n};\nvar toolBar = {\n  selection: \"\\uC120\\uD0DD\",\n  image: \"\\uC774\\uBBF8\\uC9C0 \\uC0BD\\uC785\",\n  rectangle: \"\\uC0AC\\uAC01\\uD615\",\n  diamond: \"\\uB2E4\\uC774\\uC544\\uBAAC\\uB4DC\",\n  ellipse: \"\\uD0C0\\uC6D0\",\n  arrow: \"\\uD654\\uC0B4\\uD45C\",\n  line: \"\\uC120\",\n  freedraw: \"\\uADF8\\uB9AC\\uAE30\",\n  text: \"\\uD14D\\uC2A4\\uD2B8\",\n  library: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\",\n  lock: \"\\uC120\\uD0DD\\uB41C \\uB3C4\\uAD6C \\uC720\\uC9C0\\uD558\\uAE30\",\n  penMode: \"\\uD39C \\uBAA8\\uB4DC - \\uD130\\uCE58 \\uBC29\\uC9C0\",\n  link: \"\\uC120\\uD0DD\\uD55C \\uB3C4\\uD615\\uC5D0 \\uB300\\uD574\\uC11C \\uB9C1\\uD06C\\uB97C \\uCD94\\uAC00/\\uC5C5\\uB370\\uC774\\uD2B8\",\n  eraser: \"\\uC9C0\\uC6B0\\uAC1C\",\n  frame: \"\\uD504\\uB808\\uC784 \\uB3C4\\uAD6C\",\n  magicframe: \"\\uC640\\uC774\\uC5B4\\uD504\\uB808\\uC784\\uC744 \\uCF54\\uB4DC\\uB85C\",\n  embeddable: \"\\uC6F9 \\uC784\\uBCA0\\uB4DC\",\n  laser: \"\\uB808\\uC774\\uC800 \\uD3EC\\uC778\\uD130\",\n  hand: \"\\uC190 (\\uD328\\uB2DD \\uB3C4\\uAD6C)\",\n  extraTools: \"\\uB2E4\\uB978 \\uB3C4\\uAD6C\",\n  mermaidToExcalidraw: \"Mermaid\\uC5D0\\uC11C \\uBD88\\uB7EC\\uC624\\uAE30\",\n  magicSettings: \"AI \\uC124\\uC815\"\n};\nvar headings = {\n  canvasActions: \"\\uCE94\\uBC84\\uC2A4 \\uB3D9\\uC791\",\n  selectedShapeActions: \"\\uC120\\uD0DD\\uB41C \\uBAA8\\uC591 \\uB3D9\\uC791\",\n  shapes: \"\\uBAA8\\uC591\"\n};\nvar hints = {\n  canvasPanning: \"\\uCE94\\uBC84\\uC2A4\\uB97C \\uC62E\\uAE30\\uB824\\uBA74 \\uB9C8\\uC6B0\\uC2A4 \\uD720\\uC774\\uB098 \\uC2A4\\uD398\\uC774\\uC2A4\\uBC14\\uB97C \\uB204\\uB974\\uACE0 \\uB4DC\\uB798\\uADF8\\uD558\\uAC70\\uB098, \\uC190 \\uB3C4\\uAD6C\\uB97C \\uC0AC\\uC6A9\\uD558\\uAE30\",\n  linearElement: \"\\uC5EC\\uB7EC \\uC810\\uC744 \\uC5F0\\uACB0\\uD558\\uB824\\uBA74 \\uD074\\uB9AD\\uD558\\uACE0, \\uC9C1\\uC120\\uC744 \\uADF8\\uB9AC\\uB824\\uBA74 \\uBC14\\uB85C \\uB4DC\\uB798\\uADF8\\uD558\\uC138\\uC694.\",\n  freeDraw: \"\\uD074\\uB9AD \\uD6C4 \\uB4DC\\uB798\\uADF8\\uD558\\uC138\\uC694. \\uC644\\uB8CC\\uB418\\uBA74 \\uB193\\uC73C\\uC138\\uC694.\",\n  text: \"\\uD301: \\uC120\\uD0DD \\uD234\\uB85C \\uC544\\uBB34 \\uACF3\\uC774\\uB098 \\uB354\\uBE14 \\uD074\\uB9AD\\uD574 \\uD14D\\uC2A4\\uD2B8\\uB97C \\uCD94\\uAC00\\uD560 \\uC218\\uB3C4 \\uC788\\uC2B5\\uB2C8\\uB2E4.\",\n  embeddable: \"\\uD074\\uB9AD \\uBC0F \\uB4DC\\uB798\\uADF8\\uD558\\uC5EC \\uC6F9\\uC0AC\\uC774\\uD2B8 \\uC784\\uBCA0\\uB4DC \\uB9CC\\uB4E4\\uAE30\",\n  text_selected: \"\\uB354\\uBE14 \\uD074\\uB9AD \\uB610\\uB294 ENTER\\uB97C \\uB20C\\uB7EC\\uC11C \\uD14D\\uC2A4\\uD2B8 \\uC218\\uC815\",\n  text_editing: \"ESC\\uB098 CtrlOrCmd+ENTER\\uB97C \\uB20C\\uB7EC\\uC11C \\uC218\\uC815\\uC744 \\uC885\\uB8CC\\uD558\\uAE30\",\n  linearElementMulti: \"\\uB9C8\\uC9C0\\uB9C9 \\uC9C0\\uC810\\uC744 \\uD074\\uB9AD\\uD558\\uAC70\\uB098 Esc \\uB610\\uB294 Enter \\uD0A4\\uB97C \\uB20C\\uB7EC \\uC644\\uB8CC\\uD558\\uC138\\uC694.\",\n  lockAngle: \"SHIFT \\uD0A4\\uB97C \\uB204\\uB974\\uBA74\\uC11C \\uD68C\\uC804\\uD558\\uBA74 \\uAC01\\uB3C4\\uB97C \\uC81C\\uD55C\\uD560 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\",\n  resize: \"SHIFT \\uD0A4\\uB97C \\uB204\\uB974\\uBA74\\uC11C \\uC870\\uC815\\uD558\\uBA74 \\uD06C\\uAE30\\uC758 \\uBE44\\uC728\\uC774 \\uC81C\\uD55C\\uB429\\uB2C8\\uB2E4.\\nALT\\uB97C \\uB204\\uB974\\uBA74\\uC11C \\uC870\\uC815\\uD558\\uBA74 \\uC911\\uC559\\uC744 \\uAE30\\uC900\\uC73C\\uB85C \\uD06C\\uAE30\\uB97C \\uC870\\uC815\\uD569\\uB2C8\\uB2E4.\",\n  resizeImage: \"SHIFT\\uB97C \\uB20C\\uB7EC\\uC11C \\uC790\\uC720\\uB86D\\uAC8C \\uD06C\\uAE30\\uB97C \\uBCC0\\uACBD\\uD558\\uAC70\\uB098,\\nALT\\uB97C \\uB20C\\uB7EC\\uC11C \\uC911\\uC559\\uC744 \\uACE0\\uC815\\uD558\\uACE0 \\uD06C\\uAE30\\uB97C \\uBCC0\\uACBD\\uD558\\uAE30\",\n  rotate: \"SHIFT \\uD0A4\\uB97C \\uB204\\uB974\\uBA74\\uC11C \\uD68C\\uC804\\uD558\\uBA74 \\uAC01\\uB3C4\\uB97C \\uC81C\\uD55C\\uD560 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\",\n  lineEditor_info: \"\\uAF2D\\uC9D3\\uC810\\uC744 \\uC218\\uC815\\uD558\\uB824\\uBA74 CtrlOrCmd \\uD0A4\\uB97C \\uB204\\uB974\\uACE0 \\uB354\\uBE14 \\uD074\\uB9AD\\uC744 \\uD558\\uAC70\\uB098 CtrlOrCmd + Enter\\uB97C \\uB204\\uB974\\uC138\\uC694.\",\n  lineEditor_pointSelected: \"Delete \\uD0A4\\uB85C \\uAF2D\\uC9D3\\uC810\\uC744 \\uC81C\\uAC70\\uD558\\uAC70\\uB098,\\nCtrlOrCmd+D \\uB85C \\uBCF5\\uC81C\\uD558\\uAC70\\uB098, \\uB4DC\\uB798\\uADF8 \\uD574\\uC11C \\uC774\\uB3D9\\uC2DC\\uD0A4\\uAE30\",\n  lineEditor_nothingSelected: \"\\uAF2D\\uC9D3\\uC810\\uC744 \\uC120\\uD0DD\\uD574\\uC11C \\uC218\\uC815\\uD558\\uAC70\\uB098 (SHIFT\\uB97C \\uB20C\\uB7EC\\uC11C \\uC5EC\\uB7EC\\uAC1C \\uC120\\uD0DD),\\nAlt\\uB97C \\uB204\\uB974\\uACE0 \\uD074\\uB9AD\\uD574\\uC11C \\uC0C8\\uB85C\\uC6B4 \\uAF2D\\uC9D3\\uC810 \\uCD94\\uAC00\\uD558\\uAE30\",\n  placeImage: \"\\uD074\\uB9AD\\uD574\\uC11C \\uC774\\uBBF8\\uC9C0\\uB97C \\uBC30\\uCE58\\uD558\\uAC70\\uB098, \\uD074\\uB9AD\\uD558\\uACE0 \\uB4DC\\uB798\\uADF8\\uD574\\uC11C \\uC0AC\\uC774\\uC988\\uB97C \\uC870\\uC815\\uD558\\uAE30\",\n  publishLibrary: \"\\uB2F9\\uC2E0\\uB9CC\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uAC8C\\uC2DC\\uD558\\uAE30\",\n  bindTextToElement: \"Enter \\uD0A4\\uB97C \\uB20C\\uB7EC\\uC11C \\uD14D\\uC2A4\\uD2B8 \\uCD94\\uAC00\\uD558\\uAE30\",\n  deepBoxSelect: \"CtrlOrCmd \\uD0A4\\uB97C \\uB20C\\uB7EC\\uC11C \\uAE4A\\uAC8C \\uC120\\uD0DD\\uD558\\uACE0, \\uB4DC\\uB798\\uADF8\\uD558\\uC9C0 \\uC54A\\uB3C4\\uB85D \\uD558\\uAE30\",\n  eraserRevert: \"Alt\\uB97C \\uB20C\\uB7EC\\uC11C \\uC0AD\\uC81C\\uD558\\uB3C4\\uB85D \\uC9C0\\uC815\\uB41C \\uC694\\uC18C\\uB97C \\uB418\\uB3CC\\uB9AC\\uAE30\",\n  firefox_clipboard_write: '\\uC774 \\uAE30\\uB2A5\\uC740 \\uC124\\uC815\\uC5D0\\uC11C \"dom.events.asyncClipboard.clipboardItem\" \\uD50C\\uB798\\uADF8\\uB97C \"true\"\\uB85C \\uC124\\uC815\\uD558\\uC5EC \\uD65C\\uC131\\uD654\\uD560 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4. Firefox\\uC5D0\\uC11C \\uBE0C\\uB77C\\uC6B0\\uC800 \\uD50C\\uB798\\uADF8\\uB97C \\uC218\\uC815\\uD558\\uB824\\uBA74, \"about:config\" \\uD398\\uC774\\uC9C0\\uC5D0 \\uC811\\uC18D\\uD558\\uC138\\uC694.',\n  disableSnapping: \"CtrlOrCmd \\uD0A4\\uB97C \\uB20C\\uB7EC\\uC11C \\uB2E4\\uB978 \\uC694\\uC18C\\uC640\\uC758 \\uC815\\uB82C \\uBB34\\uC2DC\\uD558\\uAE30\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\\uBBF8\\uB9AC\\uBCF4\\uAE30\\uB97C \\uBCFC \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4\",\n  canvasTooBig: \"\\uCE94\\uBC84\\uC2A4\\uAC00 \\uB108\\uBB34 \\uD07D\\uB2C8\\uB2E4.\",\n  canvasTooBigTip: \"\\uD301: \\uBA40\\uB9AC \\uC788\\uB294 \\uC694\\uC18C\\uB4E4\\uC744 \\uC880 \\uB354 \\uAC00\\uAE4C\\uC774\\uB85C \\uBD99\\uC5EC \\uBCF4\\uC138\\uC694.\"\n};\nvar errorSplash = {\n  headingMain: \"\\uC624\\uB958\\uAC00 \\uBC1C\\uC0DD\\uD588\\uC2B5\\uB2C8\\uB2E4. <button>\\uD398\\uC774\\uC9C0 \\uC0C8\\uB85C\\uACE0\\uCE68</button>\",\n  clearCanvasMessage: \"\\uC0C8\\uB85C\\uACE0\\uCE68\\uC73C\\uB85C \\uD574\\uACB0\\uB418\\uC9C0 \\uC54A\\uC744 \\uACBD\\uC6B0, <button>\\uCE94\\uBC84\\uC2A4 \\uBE44\\uC6B0\\uAE30</button>\",\n  clearCanvasCaveat: \" \\uC791\\uC5C5 \\uB0B4\\uC6A9\\uC744 \\uC783\\uAC8C \\uB429\\uB2C8\\uB2E4 \",\n  trackedToSentry: \"\\uC624\\uB958 {{eventId}} \\uAC00 \\uC2DC\\uC2A4\\uD15C\\uC5D0\\uC11C \\uBC1C\\uACAC\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4.\",\n  openIssueMessage: \"\\uC800\\uD76C\\uB294 \\uD654\\uBA74 \\uC815\\uBCF4\\uB97C \\uC624\\uB958\\uC5D0 \\uD3EC\\uD568\\uD558\\uC9C0 \\uC54A\\uB3C4\\uB85D \\uB9E4\\uC6B0 \\uC8FC\\uC758\\uD558\\uACE0 \\uC788\\uC2B5\\uB2C8\\uB2E4. \\uD639\\uC2DC \\uD654\\uBA74\\uC5D0 \\uBBFC\\uAC10\\uD55C \\uB0B4\\uC6A9\\uC774 \\uC5C6\\uB2E4\\uBA74 \\uC774\\uACF3\\uC5D0 \\uC5C5\\uB85C\\uB4DC\\uB97C \\uACE0\\uB824\\uD574\\uC8FC\\uC138\\uC694.<button>\\uBC84\\uADF8 \\uD2B8\\uB798\\uCEE4</button> \\uC544\\uB798 \\uC815\\uBCF4\\uB97C GitHub \\uC774\\uC288\\uC5D0 \\uBCF5\\uC0AC \\uBC0F \\uBD99\\uC5EC\\uB123\\uAE30\\uD574 \\uC8FC\\uC138\\uC694.\",\n  sceneContent: \"\\uD654\\uBA74 \\uB0B4\\uC6A9:\"\n};\nvar roomDialog = {\n  desc_intro: \"\\uD604\\uC7AC \\uD654\\uBA74\\uC5D0 \\uACF5\\uB3D9 \\uC791\\uC5C5\\uC790\\uB97C \\uCD08\\uB300\\uD574 \\uD611\\uC5C5\\uD560 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\",\n  desc_privacy: \"\\uC548\\uC2EC\\uD558\\uC138\\uC694, \\uC138\\uC158\\uC740 \\uC885\\uB2E8 \\uAC04 \\uC554\\uD638\\uD654\\uB97C \\uC0AC\\uC6A9\\uD558\\uBBC0\\uB85C \\uB2F9\\uC2E0\\uC758 \\uC791\\uC5C5\\uC740 \\uBE44\\uACF5\\uAC1C\\uB85C \\uC720\\uC9C0\\uB418\\uBA70 \\uC11C\\uBC84\\uC870\\uCC28\\uB3C4 \\uC791\\uC5C5 \\uB0B4\\uC6A9\\uC744 \\uC54C \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n  button_startSession: \"\\uC138\\uC158 \\uC2DC\\uC791\",\n  button_stopSession: \"\\uC138\\uC158 \\uC911\\uB2E8\",\n  desc_inProgressIntro: \"\\uC2E4\\uC2DC\\uAC04 \\uD611\\uC5C5 \\uC138\\uC158\\uC774 \\uC9C4\\uD589 \\uC911\\uC785\\uB2C8\\uB2E4.\",\n  desc_shareLink: \"\\uACF5\\uB3D9 \\uC791\\uC5C5\\uC790\\uC5D0\\uAC8C \\uC774 \\uB9C1\\uD06C\\uB97C \\uACF5\\uC720\\uD558\\uC138\\uC694.\",\n  desc_exitSession: \"\\uC138\\uC158\\uC744 \\uC911\\uB2E8\\uD558\\uBA74 \\uC5F0\\uACB0\\uC740 \\uB04A\\uC5B4\\uC9C0\\uB098 \\uC791\\uC5C5\\uC744 \\uC774\\uC5B4\\uAC08 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4. \\uC774 \\uC791\\uC5C5\\uC740 \\uB2E4\\uB978 \\uC791\\uC5C5\\uC790\\uC5D0\\uAC8C \\uC601\\uD5A5\\uC744 \\uBBF8\\uCE58\\uC9C0 \\uC54A\\uC73C\\uBA70 \\uAC01\\uC790\\uC758 \\uACF5\\uB3D9 \\uC791\\uC5C5\\uC740 \\uACC4\\uC18D \\uC720\\uC9C0\\uB429\\uB2C8\\uB2E4.\",\n  shareTitle: \"Excalidraw\\uC758 \\uC2E4\\uC2DC\\uAC04 \\uD611\\uC5C5 \\uC138\\uC158\\uC5D0 \\uCC38\\uAC00\\uD558\\uAE30\"\n};\nvar errorDialog = {\n  title: \"\\uC624\\uB958\"\n};\nvar exportDialog = {\n  disk_title: \"\\uB514\\uC2A4\\uD06C\\uC5D0 \\uC800\\uC7A5\",\n  disk_details: \"\\uB098\\uC911\\uC5D0 \\uB2E4\\uC2DC \\uBD88\\uB7EC\\uC62C \\uC218 \\uC788\\uB3C4\\uB85D \\uD654\\uBA74 \\uB370\\uC774\\uD130\\uB97C \\uB0B4\\uBCF4\\uB0C5\\uB2C8\\uB2E4.\",\n  disk_button: \"\\uD30C\\uC77C\\uB85C \\uC800\\uC7A5\",\n  link_title: \"\\uACF5\\uC720 \\uAC00\\uB2A5\\uD55C \\uB9C1\\uD06C \\uC0DD\\uC131\",\n  link_details: \"\\uC77D\\uAE30 \\uC804\\uC6A9 \\uB9C1\\uD06C\\uB85C \\uB0B4\\uBCF4\\uB0C5\\uB2C8\\uB2E4.\",\n  link_button: \"\\uB9C1\\uD06C\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n  excalidrawplus_description: \"\\uD654\\uBA74\\uC744 \\uB2F9\\uC2E0\\uC758 Excalidraw+ \\uC791\\uC5C5 \\uACF5\\uAC04\\uC73C\\uB85C \\uC800\\uC7A5\\uD569\\uB2C8\\uB2E4.\",\n  excalidrawplus_button: \"\\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n  excalidrawplus_exportError: \"\\uC9C0\\uAE08\\uC740 Excalidraw+\\uB85C \\uB0B4\\uBCF4\\uB0BC \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4...\"\n};\nvar helpDialog = {\n  blog: \"\\uBE14\\uB85C\\uADF8 \\uC77D\\uC5B4\\uBCF4\\uAE30\",\n  click: \"\\uD074\\uB9AD\",\n  deepSelect: \"\\uAE4A\\uAC8C \\uC120\\uD0DD\",\n  deepBoxSelect: \"\\uC601\\uC5ED\\uC744 \\uAE4A\\uAC8C \\uC120\\uD0DD\\uD558\\uACE0, \\uB4DC\\uB798\\uADF8\\uD558\\uC9C0 \\uC54A\\uB3C4\\uB85D \\uD558\\uAE30\",\n  curvedArrow: \"\\uACE1\\uC120 \\uD654\\uC0B4\\uD45C\",\n  curvedLine: \"\\uACE1\\uC120\",\n  documentation: \"\\uC124\\uBA85\\uC11C\",\n  doubleClick: \"\\uB354\\uBE14 \\uD074\\uB9AD\",\n  drag: \"\\uB4DC\\uB798\\uADF8\",\n  editor: \"\\uC5D0\\uB514\\uD130\",\n  editLineArrowPoints: \"\\uC9C1\\uC120 / \\uD654\\uC0B4\\uD45C \\uAF2D\\uC9D3\\uC810 \\uC218\\uC815\",\n  editText: \"\\uD14D\\uC2A4\\uD2B8 \\uC218\\uC815 / \\uB77C\\uBCA8 \\uCD94\\uAC00\",\n  github: \"\\uBB38\\uC81C \\uC81C\\uBCF4\\uD558\\uAE30\",\n  howto: \"\\uAC00\\uC774\\uB4DC \\uCC38\\uACE0\\uD558\\uAE30\",\n  or: \"\\uB610\\uB294\",\n  preventBinding: \"\\uD654\\uC0B4\\uD45C\\uAC00 \\uBD99\\uC9C0 \\uC54A\\uAC8C \\uD558\\uAE30\",\n  tools: \"\\uB3C4\\uAD6C\",\n  shortcuts: \"\\uD0A4\\uBCF4\\uB4DC \\uB2E8\\uCD95\\uD0A4\",\n  textFinish: \"\\uD3B8\\uC9D1 \\uC644\\uB8CC (\\uD14D\\uC2A4\\uD2B8 \\uC5D0\\uB514\\uD130)\",\n  textNewLine: \"\\uC904\\uBC14\\uAFC8(\\uD14D\\uC2A4\\uD2B8 \\uC5D0\\uB514\\uD130)\",\n  title: \"\\uB3C4\\uC6C0\\uB9D0\",\n  view: \"\\uBCF4\\uAE30\",\n  zoomToFit: \"\\uBAA8\\uB4E0 \\uC694\\uC18C\\uAC00 \\uBCF4\\uC774\\uB3C4\\uB85D \\uD655\\uB300/\\uCD95\\uC18C\",\n  zoomToSelection: \"\\uC120\\uD0DD \\uC601\\uC5ED\\uC73C\\uB85C \\uD655\\uB300/\\uCD95\\uC18C\",\n  toggleElementLock: \"\\uC120\\uD0DD\\uD55C \\uD56D\\uBAA9\\uC744 \\uC7A0\\uAE08/\\uC7A0\\uAE08 \\uD574\\uC81C\",\n  movePageUpDown: \"\\uD398\\uC774\\uC9C0 \\uC6C0\\uC9C1\\uC774\\uAE30 \\uC704/\\uC544\\uB798\",\n  movePageLeftRight: \"\\uD398\\uC774\\uC9C0 \\uC6C0\\uC9C1\\uC774\\uAE30 \\uC88C/\\uC6B0\"\n};\nvar clearCanvasDialog = {\n  title: \"\\uCE94\\uBC84\\uC2A4 \\uC9C0\\uC6B0\\uAE30\"\n};\nvar publishDialog = {\n  title: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uAC8C\\uC2DC\\uD558\\uAE30\",\n  itemName: \"\\uC544\\uC774\\uD15C \\uC774\\uB984\",\n  authorName: \"\\uC800\\uC790\\uBA85\",\n  githubUsername: \"\\uAE43\\uD5C8\\uBE0C \\uC0AC\\uC6A9\\uC790\\uC774\\uB984\",\n  twitterUsername: \"\\uD2B8\\uC704\\uD130 \\uC0AC\\uC6A9\\uC790\\uC774\\uB984\",\n  libraryName: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uC774\\uB984\",\n  libraryDesc: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uC124\\uBA85\",\n  website: \"\\uC6F9\\uC0AC\\uC774\\uD2B8\",\n  placeholder: {\n    authorName: \"\\uC774\\uB984 \\uB610\\uB294 \\uC0AC\\uC6A9\\uC790\\uBA85\",\n    libraryName: \"\\uB2F9\\uC2E0\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uC774\\uB984\",\n    libraryDesc: \"\\uC0AC\\uB78C\\uB4E4\\uC5D0\\uAC8C \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC758 \\uC6A9\\uB3C4\\uB97C \\uC54C\\uAE30 \\uC27D\\uAC8C \\uC124\\uBA85\\uD574\\uC8FC\\uC138\\uC694\",\n    githubHandle: \"GitHub \\uC0AC\\uC6A9\\uC790\\uBA85 (\\uC120\\uD0DD), \\uC81C\\uCD9C\\uD55C \\uB4A4\\uC5D0\\uB3C4 \\uC2EC\\uC0AC\\uB97C \\uC704\\uD574\\uC11C \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uC218\\uC815\\uD560 \\uB54C \\uC0AC\\uC6A9\\uB429\\uB2C8\\uB2E4\",\n    twitterHandle: \"Twitter \\uC0AC\\uC6A9\\uC790\\uBA85 (\\uC120\\uD0DD), Twitter\\uB97C \\uD1B5\\uD574\\uC11C \\uD64D\\uBCF4\\uD560 \\uB54C \\uC81C\\uC791\\uC790\\uB97C \\uBC1D\\uD788\\uAE30 \\uC704\\uD574 \\uC0AC\\uC6A9\\uB429\\uB2C8\\uB2E4\",\n    website: \"\\uAC1C\\uC778 \\uC6F9\\uC0AC\\uC774\\uD2B8\\uB098 \\uB2E4\\uB978 \\uC5B4\\uB518\\uAC00\\uC758 \\uB9C1\\uD06C (\\uC120\\uD0DD)\"\n  },\n  errors: {\n    required: \"\\uD544\\uC218\\uC0AC\\uD56D\",\n    website: \"\\uC720\\uD6A8\\uD55C URL\\uC744 \\uC785\\uB825\\uD558\\uC138\\uC694\"\n  },\n  noteDescription: \"\\uB2F9\\uC2E0\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uC81C\\uCD9C\\uD558\\uC5EC <link>\\uACF5\\uAC1C \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uC800\\uC7A5\\uC18C</link>\\uC5D0\\uC11C \\uB2E4\\uB978 \\uC0AC\\uB78C\\uB4E4\\uC758 \\uADF8\\uB9BC\\uC5D0 \\uC0AC\\uC6A9\\uD560 \\uC218 \\uC788\\uB3C4\\uB85D \\uD558\\uC138\\uC694.\",\n  noteGuidelines: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB294 \\uBA3C\\uC800 \\uC218\\uB3D9\\uC73C\\uB85C \\uC2B9\\uC778\\uB418\\uC5B4\\uC57C \\uD569\\uB2C8\\uB2E4. \\uC81C\\uCD9C\\uD558\\uAE30 \\uC804\\uC5D0 <link>\\uAC00\\uC774\\uB4DC\\uB77C\\uC778</link>\\uC744 \\uBA3C\\uC800 \\uC77D\\uC5B4\\uBCF4\\uC138\\uC694. \\uC758\\uACAC\\uC744 \\uACF5\\uC720\\uD558\\uAC70\\uB098 \\uBCC0\\uACBD\\uC0AC\\uD56D\\uC744 \\uB9CC\\uB4E4\\uAE30 \\uC704\\uD574\\uC120 GitHub \\uACC4\\uC815\\uC774 \\uD544\\uC694\\uD558\\uC9C0\\uB9CC, \\uBC18\\uB4DC\\uC2DC \\uD544\\uC694\\uD558\\uC9C4 \\uC54A\\uC2B5\\uB2C8\\uB2E4.\",\n  noteLicense: \"\\uC81C\\uCD9C\\uD568\\uC73C\\uB85C\\uC368, \\uB2F9\\uC2E0\\uC740 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uAC00 <link>MIT \\uB77C\\uC774\\uC120\\uC2A4 </link>\\uD558\\uC5D0 \\uBC30\\uD3EC\\uB428\\uC744, \\uC989 \\uC544\\uBB34\\uB098 \\uC81C\\uC57D \\uC5C6\\uC774 \\uC0AC\\uC6A9\\uD560 \\uC218 \\uC788\\uC74C\\uC5D0 \\uB3D9\\uC758\\uD569\\uB2C8\\uB2E4.\",\n  noteItems: \"\\uAC01\\uAC01\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB294 \\uBD84\\uB958\\uD560 \\uC218 \\uC788\\uB3C4\\uB85D \\uACE0\\uC720\\uD55C \\uC774\\uB984\\uC744 \\uAC00\\uC838\\uC57C \\uD569\\uB2C8\\uB2E4. \\uB2E4\\uC74C\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uD56D\\uBAA9\\uC774 \\uD3EC\\uD568\\uB429\\uB2C8\\uB2E4:\",\n  atleastOneLibItem: \"\\uCD5C\\uC18C\\uD55C \\uD558\\uB098\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB97C \\uC120\\uD0DD\\uD574\\uC8FC\\uC138\\uC694\",\n  republishWarning: \"\\uCC38\\uACE0: \\uC120\\uD0DD\\uB41C \\uD56D\\uBAA9\\uC758 \\uC77C\\uBD80\\uB294 \\uC774\\uBBF8 \\uC81C\\uCD9C/\\uAC8C\\uC2DC\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4. \\uAE30\\uC874\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uB098 \\uC81C\\uCD9C\\uBB3C\\uC744 \\uC5C5\\uB370\\uC774\\uD2B8\\uD558\\uB294 \\uACBD\\uC6B0\\uC5D0\\uB9CC \\uC81C\\uCD9C\\uD558\\uC138\\uC694.\"\n};\nvar publishSuccessDialog = {\n  title: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uC81C\\uCD9C\\uB428\",\n  content: \"{{authorName}}\\uB2D8 \\uAC10\\uC0AC\\uD569\\uB2C8\\uB2E4. \\uB2F9\\uC2E0\\uC758 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uAC00 \\uC2EC\\uC0AC\\uB97C \\uC704\\uD574 \\uC81C\\uCD9C\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4. \\uC9C4\\uD589 \\uC0C1\\uD669\\uC744<link>\\uC5EC\\uAE30\\uC5D0\\uC11C \\uD655\\uC778\\uD558\\uC2E4 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC \\uB9AC\\uC14B\",\n  removeItemsFromLib: \"\\uC120\\uD0DD\\uD55C \\uD56D\\uBAA9\\uC744 \\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0\\uC11C \\uC81C\\uAC70\"\n};\nvar imageExportDialog = {\n  header: \"\\uC774\\uBBF8\\uC9C0 \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n  label: {\n    withBackground: \"\\uBC30\\uACBD\",\n    onlySelected: \"\\uC120\\uD0DD\\uD55C \\uD56D\\uBAA9\\uB9CC\",\n    darkMode: \"\\uB2E4\\uD06C \\uBAA8\\uB4DC\",\n    embedScene: \"\\uD654\\uBA74\\uC744 \\uB2F4\\uAE30\",\n    scale: \"\\uD06C\\uAE30\",\n    padding: \"\\uC5EC\\uBC31\"\n  },\n  tooltip: {\n    embedScene: \"\\uD654\\uBA74 \\uC815\\uBCF4\\uAC00 \\uB0B4\\uBCF4\\uB0B4\\uB294 PNG/SVG \\uD30C\\uC77C\\uC5D0 \\uC800\\uC7A5\\uB418\\uC5B4 \\uC774\\uD6C4\\uC5D0 \\uD30C\\uC77C\\uC5D0\\uC11C \\uD654\\uBA74\\uC744 \\uBCF5\\uAD6C\\uD560 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4. \\uD30C\\uC77C \\uD06C\\uAE30\\uAC00 \\uC99D\\uAC00\\uD569\\uB2C8\\uB2E4.\"\n  },\n  title: {\n    exportToPng: \"PNG\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n    exportToSvg: \"SVG\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n    copyPngToClipboard: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C PNG \\uBCF5\\uC0AC\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uBCF5\\uC0AC\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\\uADF8\\uB9BC\\uC740 \\uC885\\uB2E8 \\uAC04 \\uC554\\uD638\\uD654\\uB418\\uBBC0\\uB85C Excalidraw\\uC758 \\uC11C\\uBC84\\uB294 \\uC808\\uB300\\uB85C \\uB0B4\\uC6A9\\uC744 \\uC54C \\uC218 \\uC5C6\\uC2B5\\uB2C8\\uB2E4.\",\n  link: \"Excalidraw\\uC758 \\uC885\\uB2E8 \\uAC04 \\uC554\\uD638\\uD654\\uC5D0 \\uB300\\uD55C \\uBE14\\uB85C\\uADF8 \\uD3EC\\uC2A4\\uD2B8\"\n};\nvar stats = {\n  angle: \"\\uAC01\\uB3C4\",\n  element: \"\\uC694\\uC18C\",\n  elements: \"\\uC694\\uC18C\",\n  height: \"\\uB192\\uC774\",\n  scene: \"\\uD654\\uBA74\",\n  selected: \"\\uC120\\uD0DD\\uB428\",\n  storage: \"\\uC800\\uC7A5\\uACF5\\uAC04\",\n  title: \"\\uB355\\uD6C4\\uB4E4\\uC744 \\uC704\\uD55C \\uD1B5\\uACC4\",\n  total: \"\\uD569\\uACC4\",\n  version: \"\\uBC84\\uC804\",\n  versionCopy: \"\\uBCF5\\uC0AC\\uD558\\uB824\\uBA74 \\uD074\\uB9AD\",\n  versionNotAvailable: \"\\uD574\\uB2F9 \\uBC84\\uC804 \\uC0AC\\uC6A9 \\uBD88\\uAC00\\uB2A5\",\n  width: \"\\uB108\\uBE44\"\n};\nvar toast = {\n  addedToLibrary: \"\\uB77C\\uC774\\uBE0C\\uB7EC\\uB9AC\\uC5D0 \\uCD94\\uAC00\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4\",\n  copyStyles: \"\\uC2A4\\uD0C0\\uC77C \\uBCF5\\uC0AC.\",\n  copyToClipboard: \"\\uD074\\uB9BD\\uBCF4\\uB4DC\\uB85C \\uBCF5\\uC0AC.\",\n  copyToClipboardAsPng: \"{{exportSelection}}\\uB97C \\uD074\\uB9BD\\uBCF4\\uB4DC\\uC5D0 PNG\\uB85C \\uBCF5\\uC0AC\\uD588\\uC2B5\\uB2C8\\uB2E4\\n({{exportColorScheme}})\",\n  fileSaved: \"\\uD30C\\uC77C\\uC774 \\uC800\\uC7A5\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4.\",\n  fileSavedToFilename: \"{filename} \\uB85C \\uC800\\uC7A5\\uB418\\uC5C8\\uC2B5\\uB2C8\\uB2E4\",\n  canvas: \"\\uCE94\\uBC84\\uC2A4\",\n  selection: \"\\uC120\\uD0DD\\uD55C \\uC694\\uC18C\",\n  pasteAsSingleElement: \"\\uB2E8\\uC77C \\uC694\\uC18C\\uB85C \\uBD99\\uC5EC\\uB123\\uAC70\\uB098, \\uAE30\\uC874 \\uD14D\\uC2A4\\uD2B8 \\uC5D0\\uB514\\uD130\\uC5D0 \\uBD99\\uC5EC\\uB123\\uC73C\\uB824\\uBA74 {{shortcut}} \\uC744 \\uC0AC\\uC6A9\\uD558\\uC138\\uC694.\",\n  unableToEmbed: \"\\uC774 URL\\uC758 \\uC784\\uBCA0\\uB529\\uC774 \\uD5C8\\uC6A9\\uB418\\uC9C0 \\uC54A\\uC558\\uC2B5\\uB2C8\\uB2E4. GitHub\\uC5D0 \\uC774\\uC288\\uB97C \\uB0A8\\uACA8\\uC11C \\uC774 URL\\uC774 \\uD654\\uC774\\uD2B8\\uB9AC\\uC2A4\\uD2B8\\uC5D0 \\uB4F1\\uC7AC\\uB420 \\uC218 \\uC788\\uB3C4\\uB85D \\uC694\\uCCAD\\uD558\\uC138\\uC694\",\n  unrecognizedLinkFormat: '\\uC784\\uBCA0\\uB529\\uD558\\uB824\\uB294 \\uB9C1\\uD06C\\uC758 \\uD615\\uC2DD\\uC774 \\uC798\\uBABB\\uB41C \\uAC83 \\uAC19\\uC2B5\\uB2C8\\uB2E4. \\uC6D0\\uBCF8 \\uC0AC\\uC774\\uD2B8\\uC5D0\\uC11C \\uC81C\\uACF5\\uD558\\uB294 \"\\uC784\\uBCA0\\uB529\" \\uD14D\\uC2A4\\uD2B8\\uB97C \\uADF8\\uB300\\uB85C \\uBD99\\uC5EC \\uB123\\uC5B4 \\uC8FC\\uC138\\uC694'\n};\nvar colors = {\n  transparent: \"\\uD22C\\uBA85\",\n  black: \"\\uBE14\\uB799\",\n  white: \"\\uD654\\uC774\\uD2B8\",\n  red: \"\\uB808\\uB4DC\",\n  pink: \"\\uD551\\uD06C\",\n  grape: \"\\uADF8\\uB808\\uC774\\uD504\",\n  violet: \"\\uBC14\\uC774\\uC62C\\uB81B\",\n  gray: \"\\uADF8\\uB808\\uC774\",\n  blue: \"\\uBE14\\uB8E8\",\n  cyan: \"\\uC2DC\\uC548\",\n  teal: \"\\uD2F8\",\n  green: \"\\uADF8\\uB9B0\",\n  yellow: \"\\uC610\\uB85C\\uC6B0\",\n  orange: \"\\uC624\\uB80C\\uC9C0\",\n  bronze: \"\\uBE0C\\uB860\\uC988\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\\uBAA8\\uB4E0 \\uB370\\uC774\\uD130\\uB294 \\uBE0C\\uB77C\\uC6B0\\uC800\\uC5D0 \\uC548\\uC804\\uD558\\uAC8C \\uC800\\uC7A5\\uB429\\uB2C8\\uB2E4.\",\n    center_heading_plus: \"\\uB300\\uC2E0 Excalidraw+\\uB85C \\uC774\\uB3D9\\uD558\\uC2DC\\uACA0\\uC2B5\\uB2C8\\uAE4C?\",\n    menuHint: \"\\uB0B4\\uBCF4\\uB0B4\\uAE30, \\uC124\\uC815, \\uC5B8\\uC5B4, ...\"\n  },\n  defaults: {\n    menuHint: \"\\uB0B4\\uBCF4\\uB0B4\\uAE30, \\uC124\\uC815, \\uB4F1\\uB4F1...\",\n    center_heading: \"\\uAC04\\uB2E8\\uD558\\uAC8C \\uB9CC\\uB4DC\\uB294 \\uB2E4\\uC774\\uC5B4\\uADF8\\uB7A8.\",\n    toolbarHint: \"\\uB3C4\\uAD6C\\uB97C \\uC120\\uD0DD\\uD558\\uACE0, \\uADF8\\uB9AC\\uC138\\uC694!\",\n    helpHint: \"\\uB2E8\\uCD95\\uD0A4 & \\uB3C4\\uC6C0\\uB9D0\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\\uAC00\\uC7A5 \\uB9CE\\uC774 \\uC0AC\\uC6A9\\uB41C \\uC0C9\\uC0C1\\uB4E4\",\n  colors: \"\\uC0C9\\uC0C1\",\n  shades: \"\\uC0C9\\uC870\",\n  hexCode: \"Hex \\uCF54\\uB4DC\",\n  noShades: \"\\uC0AC\\uC6A9\\uD560 \\uC218 \\uC788\\uB294 \\uC0C9\\uC870\\uAC00 \\uC5C6\\uC74C\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\\uC774\\uBBF8\\uC9C0\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n      button: \"\\uC774\\uBBF8\\uC9C0\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n      description: \"\\uB098\\uC911\\uC5D0 \\uB2E4\\uC2DC \\uBD88\\uB7EC\\uC62C \\uC218 \\uC788\\uB3C4\\uB85D \\uD654\\uBA74 \\uB370\\uC774\\uD130\\uB97C \\uC774\\uBBF8\\uC9C0\\uB85C \\uB0B4\\uBCF4\\uB0C5\\uB2C8\\uB2E4.\"\n    },\n    saveToDisk: {\n      title: \"\\uB514\\uC2A4\\uD06C\\uC5D0 \\uC800\\uC7A5\",\n      button: \"\\uB514\\uC2A4\\uD06C\\uC5D0 \\uC800\\uC7A5\",\n      description: \"\\uB098\\uC911\\uC5D0 \\uB2E4\\uC2DC \\uBD88\\uB7EC\\uC62C \\uC218 \\uC788\\uB3C4\\uB85D \\uD654\\uBA74 \\uB370\\uC774\\uD130\\uB97C \\uB0B4\\uBCF4\\uB0C5\\uB2C8\\uB2E4.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Excalidraw+\\uB85C \\uB0B4\\uBCF4\\uB0B4\\uAE30\",\n      description: \"\\uD654\\uBA74\\uC744 \\uB2F9\\uC2E0\\uC758 Excalidraw+ \\uC791\\uC5C5 \\uACF5\\uAC04\\uC73C\\uB85C \\uC800\\uC7A5\\uD569\\uB2C8\\uB2E4.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\\uD30C\\uC77C\\uC5D0\\uC11C \\uBD88\\uB7EC\\uC624\\uAE30\",\n      button: \"\\uD30C\\uC77C\\uC5D0\\uC11C \\uBD88\\uB7EC\\uC624\\uAE30\",\n      description: \"\\uD30C\\uC77C\\uC744 \\uBD88\\uB7EC\\uC624\\uBA74 <bold>\\uD604\\uC7AC \\uC791\\uC131\\uB41C \\uB370\\uC774\\uD130\\uB97C \\uB36E\\uC5B4\\uC4F0\\uAC8C \\uB429\\uB2C8\\uB2E4</bold>.<br></br>\\uB2E4\\uC74C \\uC635\\uC158 \\uC911 \\uD558\\uB098\\uB97C \\uC120\\uD0DD\\uD558\\uC5EC \\uC791\\uC5C5\\uBB3C\\uC744 \\uBC31\\uC5C5\\uD574 \\uB458 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\"\n    },\n    shareableLink: {\n      title: \"\\uC8FC\\uC18C\\uC5D0\\uC11C \\uBD88\\uB7EC\\uC624\\uAE30\",\n      button: \"\\uCEE8\\uD150\\uCE20\\uB97C \\uB36E\\uC5B4\\uC4F0\\uAE30\",\n      description: \"\\uC678\\uBD80 \\uC791\\uC5C5\\uBB3C\\uC744 \\uBD88\\uB7EC\\uC624\\uBA74 <bold>\\uD604\\uC7AC \\uC791\\uC131\\uB41C \\uB370\\uC774\\uD130\\uB97C \\uB36E\\uC5B4\\uC4F0\\uAC8C \\uB429\\uB2C8\\uB2E4</bold>.<br></br>\\uB2E4\\uC74C \\uC635\\uC158 \\uC911 \\uD558\\uB098\\uB97C \\uC120\\uD0DD\\uD558\\uC5EC \\uC791\\uC5C5\\uBB3C\\uC744 \\uBC31\\uC5C5\\uD574 \\uB458 \\uC218 \\uC788\\uC2B5\\uB2C8\\uB2E4.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid\\uC5D0\\uC11C \\uBD88\\uB7EC\\uC624\\uAE30\",\n  button: \"\\uC0BD\\uC785\\uD558\\uAE30\",\n  description: \"\\uC9C0\\uAE08\\uC740 <flowchartLink>\\uC21C\\uC11C\\uB3C4</flowchartLink>,<sequenceLink> \\uC2DC\\uD000\\uC2A4</sequenceLink>, <classLink>\\uD074\\uB798\\uC2A4 </classLink>\\uB2E4\\uC774\\uC5B4\\uADF8\\uB7A8\\uB9CC \\uC9C0\\uC6D0\\uD569\\uB2C8\\uB2E4. \\uB2E4\\uB978 \\uD615\\uC2DD\\uB4E4\\uC740 Excalidraw\\uC5D0\\uC11C\\uB294 \\uC774\\uBBF8\\uC9C0\\uB85C \\uD45C\\uC2DC\\uB429\\uB2C8\\uB2E4.\",\n  syntax: \"Mermaid \\uAD6C\\uBB38\",\n  preview: \"\\uBBF8\\uB9AC\\uBCF4\\uAE30\"\n};\nvar ko_KR_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=ko-KR-RQX37SNF.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMva28tS1ItUlFYMzdTTkYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLFdBQVc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsT0FBTztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxR0FBcUcsVUFBVTtBQUMvRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsVUFBVTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsWUFBWTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixpQkFBaUIseUZBQXlGLG1CQUFtQjtBQUN4SjtBQUNBLHlCQUF5QixVQUFVO0FBQ25DO0FBQ0E7QUFDQSx5TEFBeUwsV0FBVztBQUNwTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQThCRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vbm9kZV9tb2R1bGVzL0BleGNhbGlkcmF3L2V4Y2FsaWRyYXcvZGlzdC9kZXYvbG9jYWxlcy9rby1LUi1SUVgzN1NORi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgXCIuLi9jaHVuay1YREZDVVVUNi5qc1wiO1xuXG4vLyBsb2NhbGVzL2tvLUtSLmpzb25cbnZhciBsYWJlbHMgPSB7XG4gIHBhc3RlOiBcIlxcdUJEOTlcXHVDNUVDXFx1QjEyM1xcdUFFMzBcIixcbiAgcGFzdGVBc1BsYWludGV4dDogXCJcXHVDNzdDXFx1QkMxOCBcXHVEMTREXFx1QzJBNFxcdUQyQjhcXHVCODVDIFxcdUJEOTlcXHVDNUVDXFx1QjEyM1xcdUFFMzBcIixcbiAgcGFzdGVDaGFydHM6IFwiXFx1Q0MyOFxcdUQyQjggXFx1QkQ5OVxcdUM1RUNcXHVCMTIzXFx1QUUzMFwiLFxuICBzZWxlY3RBbGw6IFwiXFx1QzgwNFxcdUNDQjQgXFx1QzEyMFxcdUQwRERcIixcbiAgbXVsdGlTZWxlY3Q6IFwiXFx1QzEyMFxcdUQwREQgXFx1QzYwMVxcdUM1RURcXHVDNUQwIFxcdUNEOTRcXHVBQzAwXFx1RDU1OFxcdUFFMzBcIixcbiAgbW92ZUNhbnZhczogXCJcXHVDRTk0XFx1QkM4NFxcdUMyQTQgXFx1Qzc3NFxcdUIzRDlcIixcbiAgY3V0OiBcIlxcdUM3OThcXHVCNzdDXFx1QjBCNFxcdUFFMzBcIixcbiAgY29weTogXCJcXHVCQ0Y1XFx1QzBBQ1wiLFxuICBjb3B5QXNQbmc6IFwiXFx1RDA3NFxcdUI5QkRcXHVCQ0Y0XFx1QjREQ1xcdUI4NUMgUE5HIFxcdUM3NzRcXHVCQkY4XFx1QzlDMCBcXHVCQ0Y1XFx1QzBBQ1wiLFxuICBjb3B5QXNTdmc6IFwiXFx1RDA3NFxcdUI5QkRcXHVCQ0Y0XFx1QjREQ1xcdUI4NUMgU1ZHIFxcdUM3NzRcXHVCQkY4XFx1QzlDMCBcXHVCQ0Y1XFx1QzBBQ1wiLFxuICBjb3B5VGV4dDogXCJcXHVEMDc0XFx1QjlCRFxcdUJDRjRcXHVCNERDXFx1Qjg1QyBcXHVEMTREXFx1QzJBNFxcdUQyQjggXFx1QkNGNVxcdUMwQUNcIixcbiAgY29weVNvdXJjZTogXCJcXHVDMThDXFx1QzJBNFxcdUNGNTRcXHVCNERDXFx1Qjk3QyBcXHVEMDc0XFx1QjlCRFxcdUJDRjRcXHVCNERDXFx1Qjg1QyBcXHVCQ0Y1XFx1QzBBQ1wiLFxuICBjb252ZXJ0VG9Db2RlOiBcIlxcdUNGNTRcXHVCNERDXFx1Qjg1QyBcXHVCQ0MwXFx1RDY1OFwiLFxuICBicmluZ0ZvcndhcmQ6IFwiXFx1QzU1RVxcdUM3M0NcXHVCODVDIFxcdUFDMDBcXHVDODM4XFx1QzYyNFxcdUFFMzBcIixcbiAgc2VuZFRvQmFjazogXCJcXHVCOUU4IFxcdUI0QTRcXHVCODVDIFxcdUJDRjRcXHVCMEI0XFx1QUUzMFwiLFxuICBicmluZ1RvRnJvbnQ6IFwiXFx1QjlFOCBcXHVDNTVFXFx1QzczQ1xcdUI4NUMgXFx1QUMwMFxcdUM4MzhcXHVDNjI0XFx1QUUzMFwiLFxuICBzZW5kQmFja3dhcmQ6IFwiXFx1QjRBNFxcdUI4NUMgXFx1QkNGNFxcdUIwQjRcXHVBRTMwXCIsXG4gIGRlbGV0ZTogXCJcXHVDMEFEXFx1QzgxQ1wiLFxuICBjb3B5U3R5bGVzOiBcIlxcdUMyQTRcXHVEMEMwXFx1Qzc3QyBcXHVCQ0Y1XFx1QzBBQ1xcdUQ1NThcXHVBRTMwXCIsXG4gIHBhc3RlU3R5bGVzOiBcIlxcdUMyQTRcXHVEMEMwXFx1Qzc3QyBcXHVCRDk5XFx1QzVFQ1xcdUIxMjNcXHVBRTMwXCIsXG4gIHN0cm9rZTogXCJcXHVDMTIwIFxcdUMwQzlcXHVDMEMxXCIsXG4gIGJhY2tncm91bmQ6IFwiXFx1QkMzMFxcdUFDQkRcXHVDMEM5XCIsXG4gIGZpbGw6IFwiXFx1Q0M0NFxcdUM2QjBcXHVBRTMwXCIsXG4gIHN0cm9rZVdpZHRoOiBcIlxcdUMxMjAgXFx1QUQ3NVxcdUFFMzBcIixcbiAgc3Ryb2tlU3R5bGU6IFwiXFx1QzEyMFwiLFxuICBzdHJva2VTdHlsZV9zb2xpZDogXCJcXHVDMkU0XFx1QzEyMFwiLFxuICBzdHJva2VTdHlsZV9kYXNoZWQ6IFwiXFx1RDMwQ1xcdUMxMjBcIixcbiAgc3Ryb2tlU3R5bGVfZG90dGVkOiBcIlxcdUM4MTBcXHVDMTIwXCIsXG4gIHNsb3BwaW5lc3M6IFwiXFx1QjMwMFxcdUNEQTkgXFx1QUUwQlxcdUFFMzBcIixcbiAgb3BhY2l0eTogXCJcXHVCRDg4XFx1RDIyQ1xcdUJBODVcXHVCM0M0XCIsXG4gIHRleHRBbGlnbjogXCJcXHVEMTREXFx1QzJBNFxcdUQyQjggXFx1QzgxNVxcdUI4MkNcIixcbiAgZWRnZXM6IFwiXFx1QUMwMFxcdUM3QTVcXHVDNzkwXFx1QjlBQ1wiLFxuICBzaGFycDogXCJcXHVCRkIwXFx1Qzg3MVxcdUQ1NThcXHVBQzhDXCIsXG4gIHJvdW5kOiBcIlxcdUI0NjVcXHVBRTAwXFx1QUM4Q1wiLFxuICBhcnJvd2hlYWRzOiBcIlxcdUQ2NTRcXHVDMEI0XFx1Q0QwOVwiLFxuICBhcnJvd2hlYWRfbm9uZTogXCJcXHVDNUM2XFx1Qzc0Q1wiLFxuICBhcnJvd2hlYWRfYXJyb3c6IFwiXFx1RDY1NFxcdUMwQjRcXHVENDVDXCIsXG4gIGFycm93aGVhZF9iYXI6IFwiXFx1QjlDOVxcdUIzMDBcIixcbiAgYXJyb3doZWFkX2NpcmNsZTogXCJcXHVDNkQwXCIsXG4gIGFycm93aGVhZF9jaXJjbGVfb3V0bGluZTogXCJcXHVDNkQwIChcXHVDNjc4XFx1QUNGRFxcdUMxMjApXCIsXG4gIGFycm93aGVhZF90cmlhbmdsZTogXCJcXHVDMEJDXFx1QUMwMVxcdUQ2MTVcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlX291dGxpbmU6IFwiXFx1QzBCQ1xcdUFDMDFcXHVENjE1IChcXHVDNjc4XFx1QUNGRFxcdUMxMjApXCIsXG4gIGFycm93aGVhZF9kaWFtb25kOiBcIlxcdUI5QzhcXHVCOTg0XFx1QkFBOFwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZF9vdXRsaW5lOiBcIlxcdUI5QzhcXHVCOTg0XFx1QkFBOCAoXFx1QzY3OFxcdUFDRkRcXHVDMTIwKVwiLFxuICBmb250U2l6ZTogXCJcXHVBRTAwXFx1Qzc5MCBcXHVEMDZDXFx1QUUzMFwiLFxuICBmb250RmFtaWx5OiBcIlxcdUFFMDBcXHVBRjM0XCIsXG4gIGFkZFdhdGVybWFyazogJ1wiTWFkZSB3aXRoIEV4Y2FsaWRyYXdcIiBcXHVDRDk0XFx1QUMwMCcsXG4gIGhhbmREcmF3bjogXCJcXHVDMTkwXFx1QUUwMFxcdUM1MjhcIixcbiAgbm9ybWFsOiBcIlxcdUM3N0NcXHVCQzE4XCIsXG4gIGNvZGU6IFwiXFx1Q0Y1NFxcdUI0RENcIixcbiAgc21hbGw6IFwiXFx1Qzc5MVxcdUFDOENcIixcbiAgbWVkaXVtOiBcIlxcdUJDRjRcXHVEMUI1XCIsXG4gIGxhcmdlOiBcIlxcdUQwNkNcXHVBQzhDXCIsXG4gIHZlcnlMYXJnZTogXCJcXHVCOUU0XFx1QzZCMCBcXHVEMDZDXFx1QUM4Q1wiLFxuICBzb2xpZDogXCJcXHVCMkU4XFx1QzBDOVwiLFxuICBoYWNodXJlOiBcIlxcdUQzQzlcXHVENTg5XFx1QzEyMFwiLFxuICB6aWd6YWc6IFwiXFx1QzlDMFxcdUFERjhcXHVDN0FDXFx1QURGOFwiLFxuICBjcm9zc0hhdGNoOiBcIlxcdUFENTBcXHVDQzI4XFx1QzEyMFwiLFxuICB0aGluOiBcIlxcdUM1ODdcXHVBQzhDXCIsXG4gIGJvbGQ6IFwiXFx1QUQ3NVxcdUFDOENcIixcbiAgbGVmdDogXCJcXHVDNjdDXFx1Q0FCRFwiLFxuICBjZW50ZXI6IFwiXFx1QUMwMFxcdUM2QjRcXHVCMzcwXCIsXG4gIHJpZ2h0OiBcIlxcdUM2MjRcXHVCOTc4XFx1Q0FCRFwiLFxuICBleHRyYUJvbGQ6IFwiXFx1QjlFNFxcdUM2QjAgXFx1QUQ3NVxcdUFDOENcIixcbiAgYXJjaGl0ZWN0OiBcIlxcdUFDNzRcXHVDRDk1XFx1QUMwMFwiLFxuICBhcnRpc3Q6IFwiXFx1QzYwOFxcdUMyMjBcXHVBQzAwXCIsXG4gIGNhcnRvb25pc3Q6IFwiXFx1QjlDQ1xcdUQ2NTRcXHVBQzAwXCIsXG4gIGZpbGVUaXRsZTogXCJcXHVEMzBDXFx1Qzc3QyBcXHVDNzc0XFx1Qjk4NFwiLFxuICBjb2xvclBpY2tlcjogXCJcXHVDMEM5XFx1QzBDMSBcXHVDMTIwXFx1RDBERFxcdUFFMzBcIixcbiAgY2FudmFzQ29sb3JzOiBcIlxcdUNFOTRcXHVCQzg0XFx1QzJBNFxcdUM1RDBcXHVDMTFDIFxcdUMwQUNcXHVDNkE5XFx1QjQxOFxcdUM1QzhcXHVDNzRDXCIsXG4gIGNhbnZhc0JhY2tncm91bmQ6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0IFxcdUJDMzBcXHVBQ0JEXCIsXG4gIGRyYXdpbmdDYW52YXM6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0IFxcdUFERjhcXHVCOUFDXFx1QUUzMFwiLFxuICBsYXllcnM6IFwiXFx1QjgwOFxcdUM3NzRcXHVDNUI0XCIsXG4gIGFjdGlvbnM6IFwiXFx1QjNEOVxcdUM3OTFcIixcbiAgbGFuZ3VhZ2U6IFwiXFx1QzVCOFxcdUM1QjRcIixcbiAgbGl2ZUNvbGxhYm9yYXRpb246IFwiXFx1QzJFNFxcdUMyRENcXHVBQzA0IFxcdUQ2MTFcXHVDNUM1Li4uXCIsXG4gIGR1cGxpY2F0ZVNlbGVjdGlvbjogXCJcXHVCQ0Y1XFx1QzgxQ1wiLFxuICB1bnRpdGxlZDogXCJcXHVDODFDXFx1QkFBOSBcXHVDNUM2XFx1Qzc0Q1wiLFxuICBuYW1lOiBcIlxcdUM3NzRcXHVCOTg0XCIsXG4gIHlvdXJOYW1lOiBcIlxcdUM3NzRcXHVCOTg0IFxcdUM3ODVcXHVCODI1XCIsXG4gIG1hZGVXaXRoRXhjYWxpZHJhdzogXCJNYWRlIHdpdGggRXhjYWxpZHJhd1wiLFxuICBncm91cDogXCJcXHVBREY4XFx1QjhGOSBcXHVDMEREXFx1QzEzMVwiLFxuICB1bmdyb3VwOiBcIlxcdUFERjhcXHVCOEY5IFxcdUQ1NzRcXHVDODFDXCIsXG4gIGNvbGxhYm9yYXRvcnM6IFwiXFx1QUNGNVxcdUIzRDkgXFx1Qzc5MVxcdUM1QzVcXHVDNzkwXCIsXG4gIHNob3dHcmlkOiBcIlxcdUFERjhcXHVCOUFDXFx1QjREQyBcXHVCQ0Y0XFx1QUUzMFwiLFxuICBhZGRUb0xpYnJhcnk6IFwiXFx1Qjc3Q1xcdUM3NzRcXHVCRTBDXFx1QjdFQ1xcdUI5QUNcXHVDNUQwIFxcdUNEOTRcXHVBQzAwXCIsXG4gIHJlbW92ZUZyb21MaWJyYXJ5OiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1QzVEMFxcdUMxMUMgXFx1QzgxQ1xcdUFDNzBcIixcbiAgbGlicmFyeUxvYWRpbmdNZXNzYWdlOiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDIFxcdUJEODhcXHVCN0VDXFx1QzYyNFxcdUIyOTQgXFx1QzkxMVxcdTIwMjZcIixcbiAgbGlicmFyaWVzOiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDIFxcdUNDM0VcXHVBRTMwXCIsXG4gIGxvYWRpbmdTY2VuZTogXCJcXHVENjU0XFx1QkE3NCBcXHVCRDg4XFx1QjdFQ1xcdUM2MjRcXHVCMjk0IFxcdUM5MTFcXHUyMDI2XCIsXG4gIGFsaWduOiBcIlxcdUM4MTVcXHVCODJDXCIsXG4gIGFsaWduVG9wOiBcIlxcdUMwQzFcXHVCMkU4IFxcdUM4MTVcXHVCODJDXCIsXG4gIGFsaWduQm90dG9tOiBcIlxcdUQ1NThcXHVCMkU4IFxcdUM4MTVcXHVCODJDXCIsXG4gIGFsaWduTGVmdDogXCJcXHVDNjdDXFx1Q0FCRCBcXHVDODE1XFx1QjgyQ1wiLFxuICBhbGlnblJpZ2h0OiBcIlxcdUM2MjRcXHVCOTc4XFx1Q0FCRCBcXHVDODE1XFx1QjgyQ1wiLFxuICBjZW50ZXJWZXJ0aWNhbGx5OiBcIlxcdUMyMThcXHVDOUMxXFx1QzczQ1xcdUI4NUMgXFx1QzkxMVxcdUM1NTkgXFx1QzgxNVxcdUI4MkNcIixcbiAgY2VudGVySG9yaXpvbnRhbGx5OiBcIlxcdUMyMThcXHVEM0M5XFx1QzczQ1xcdUI4NUMgXFx1QzkxMVxcdUM1NTkgXFx1QzgxNVxcdUI4MkNcIixcbiAgZGlzdHJpYnV0ZUhvcml6b250YWxseTogXCJcXHVDMjE4XFx1RDNDOVxcdUM3M0NcXHVCODVDIFxcdUJEODRcXHVCQzMwXCIsXG4gIGRpc3RyaWJ1dGVWZXJ0aWNhbGx5OiBcIlxcdUMyMThcXHVDOUMxXFx1QzczQ1xcdUI4NUMgXFx1QkQ4NFxcdUJDMzBcIixcbiAgZmxpcEhvcml6b250YWw6IFwiXFx1Qzg4Q1xcdUM2QjBcXHVCQzE4XFx1QzgwNFwiLFxuICBmbGlwVmVydGljYWw6IFwiXFx1QzBDMVxcdUQ1NThcXHVCQzE4XFx1QzgwNFwiLFxuICB2aWV3TW9kZTogXCJcXHVCQ0Y0XFx1QUUzMCBcXHVCQUE4XFx1QjREQ1wiLFxuICBzaGFyZTogXCJcXHVBQ0Y1XFx1QzcyMFwiLFxuICBzaG93U3Ryb2tlOiBcIlxcdUM3MjRcXHVBQ0ZEXFx1QzEyMCBcXHVDMEM5XFx1QzBDMSBcXHVDMTIwXFx1RDBERFxcdUFFMzAgXFx1QzVGNFxcdUFFMzBcIixcbiAgc2hvd0JhY2tncm91bmQ6IFwiXFx1QkMzMFxcdUFDQkQgXFx1QzBDOVxcdUMwQzEgXFx1QzEyMFxcdUQwRERcXHVBRTMwIFxcdUM1RjRcXHVBRTMwXCIsXG4gIHRvZ2dsZVRoZW1lOiBcIlxcdUQxNENcXHVCOUM4IFxcdUM4MDRcXHVENjU4XCIsXG4gIHBlcnNvbmFsTGliOiBcIlxcdUFDMUNcXHVDNzc4IFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXCIsXG4gIGV4Y2FsaWRyYXdMaWI6IFwiRXhjYWxpZHJhdyBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1wiLFxuICBkZWNyZWFzZUZvbnRTaXplOiBcIlxcdUQzRjBcXHVEMkI4IFxcdUMwQUNcXHVDNzc0XFx1Qzk4OCBcXHVDOTA0XFx1Qzc3NFxcdUFFMzBcIixcbiAgaW5jcmVhc2VGb250U2l6ZTogXCJcXHVEM0YwXFx1RDJCOCBcXHVDMEFDXFx1Qzc3NFxcdUM5ODggXFx1RDBBNFxcdUM2QjBcXHVBRTMwXCIsXG4gIHVuYmluZFRleHQ6IFwiXFx1RDE0RFxcdUMyQTRcXHVEMkI4IFxcdUJEODRcXHVCOUFDXCIsXG4gIGJpbmRUZXh0OiBcIlxcdUQxNERcXHVDMkE0XFx1RDJCOFxcdUI5N0MgXFx1Q0VFOFxcdUQxNENcXHVDNzc0XFx1QjEwOFxcdUM1RDAgXFx1QUNCMFxcdUQ1NjlcIixcbiAgY3JlYXRlQ29udGFpbmVyRnJvbVRleHQ6IFwiXFx1RDE0RFxcdUMyQTRcXHVEMkI4XFx1Qjk3QyBcXHVDRUU4XFx1RDE0Q1xcdUM3NzRcXHVCMTA4XFx1QzVEMCBcXHVCMkY0XFx1QUUzMFwiLFxuICBsaW5rOiB7XG4gICAgZWRpdDogXCJcXHVCOUMxXFx1RDA2QyBcXHVDMjE4XFx1QzgxNVxcdUQ1NThcXHVBRTMwXCIsXG4gICAgZWRpdEVtYmVkOiBcIlxcdUI5QzFcXHVEMDZDICYgXFx1Qzc4NFxcdUJDQTBcXHVCNERDIFxcdUMyMThcXHVDODE1XFx1RDU1OFxcdUFFMzBcIixcbiAgICBjcmVhdGU6IFwiXFx1QjlDMVxcdUQwNkMgXFx1QjlDQ1xcdUI0RTRcXHVBRTMwXCIsXG4gICAgY3JlYXRlRW1iZWQ6IFwiXFx1QjlDMVxcdUQwNkMgJiBcXHVDNzg0XFx1QkNBMFxcdUI0REMgXFx1QjlDQ1xcdUI0RTRcXHVBRTMwXCIsXG4gICAgbGFiZWw6IFwiXFx1QjlDMVxcdUQwNkNcIixcbiAgICBsYWJlbEVtYmVkOiBcIlxcdUI5QzFcXHVEMDZDICYgXFx1Qzc4NFxcdUJDQTBcXHVCNERDXCIsXG4gICAgZW1wdHk6IFwiXFx1QjlDMVxcdUQwNkNcXHVCOTdDIFxcdUM5QzBcXHVDODE1XFx1RDU1OFxcdUM5QzAgXFx1QzU0QVxcdUM1NThcXHVDMkI1XFx1QjJDOFxcdUIyRTRcIlxuICB9LFxuICBsaW5lRWRpdG9yOiB7XG4gICAgZWRpdDogXCJcXHVDMTIwIFxcdUMyMThcXHVDODE1XFx1RDU1OFxcdUFFMzBcIixcbiAgICBleGl0OiBcIlxcdUMxMjAgXFx1RDNCOFxcdUM5RDFcXHVBRTMwIFxcdUM4ODVcXHVCOENDXCJcbiAgfSxcbiAgZWxlbWVudExvY2s6IHtcbiAgICBsb2NrOiBcIlxcdUM3QTBcXHVBRTA4XCIsXG4gICAgdW5sb2NrOiBcIlxcdUM3QTBcXHVBRTA4IFxcdUQ1NzRcXHVDODFDXCIsXG4gICAgbG9ja0FsbDogXCJcXHVCQUE4XFx1QjQ1MCBcXHVDN0EwXFx1QUUwOFwiLFxuICAgIHVubG9ja0FsbDogXCJcXHVCQUE4XFx1QjQ1MCBcXHVDN0EwXFx1QUUwOCBcXHVENTc0XFx1QzgxQ1wiXG4gIH0sXG4gIHN0YXR1c1B1Ymxpc2hlZDogXCJcXHVBQzhDXFx1QzJEQ1xcdUI0MjhcIixcbiAgc2lkZWJhckxvY2s6IFwiXFx1QzBBQ1xcdUM3NzRcXHVCNERDXFx1QkMxNCBcXHVDNzIwXFx1QzlDMFwiLFxuICBzZWxlY3RBbGxFbGVtZW50c0luRnJhbWU6IFwiXFx1RDUwNFxcdUI4MDhcXHVDNzg0XFx1Qzc1OCBcXHVCQUE4XFx1QjRFMCBcXHVDNjk0XFx1QzE4QyBcXHVDMTIwXFx1RDBERFwiLFxuICByZW1vdmVBbGxFbGVtZW50c0Zyb21GcmFtZTogXCJcXHVENTA0XFx1QjgwOFxcdUM3ODRcXHVDNzU4IFxcdUJBQThcXHVCNEUwIFxcdUM2OTRcXHVDMThDIFxcdUMwQURcXHVDODFDXCIsXG4gIGV5ZURyb3BwZXI6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0XFx1QzVEMFxcdUMxMUMgXFx1QzBDOVxcdUMwQzEgXFx1QUNFMFxcdUI5NzRcXHVBRTMwXCIsXG4gIHRleHRUb0RpYWdyYW06IFwiXFx1RDE0RFxcdUMyQTRcXHVEMkI4XFx1Qjk3QyBcXHVCMkU0XFx1Qzc3NFxcdUM1QjRcXHVBREY4XFx1QjdBOFxcdUM3M0NcXHVCODVDXCIsXG4gIHByb21wdDogXCJcXHVENTA0XFx1Qjg2Q1xcdUQ1MDRcXHVEMkI4XCJcbn07XG52YXIgbGlicmFyeSA9IHtcbiAgbm9JdGVtczogXCJcXHVDRDk0XFx1QUMwMFxcdUI0MUMgXFx1QzU0NFxcdUM3NzRcXHVEMTVDIFxcdUM1QzZcXHVDNzRDXCIsXG4gIGhpbnRfZW1wdHlMaWJyYXJ5OiBcIlxcdUNFOTRcXHVCQzg0XFx1QzJBNCBcXHVDNzA0XFx1QzVEMFxcdUMxMUMgXFx1QzU0NFxcdUM3NzRcXHVEMTVDXFx1Qzc0NCBcXHVDMTIwXFx1RDBERFxcdUQ1NThcXHVDNUVDIFxcdUM1RUNcXHVBRTMwXFx1QzVEMCBcXHVDRDk0XFx1QUMwMFxcdUI5N0MgXFx1RDU1OFxcdUFDNzBcXHVCMDk4LCBcXHVDNTQ0XFx1Qjc5OFxcdUM3NTggXFx1QUNGNVxcdUM2QTkgXFx1QzgwMFxcdUM3QTVcXHVDMThDXFx1QzVEMFxcdUMxMUMgXFx1Qjc3Q1xcdUM3NzRcXHVCRTBDXFx1QjdFQ1xcdUI5QUNcXHVCOTdDIFxcdUMxMjRcXHVDRTU4XFx1RDU1OFxcdUMxMzhcXHVDNjk0LlwiLFxuICBoaW50X2VtcHR5UHJpdmF0ZUxpYnJhcnk6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0IFxcdUM3MDRcXHVDNUQwXFx1QzExQyBcXHVDNTQ0XFx1Qzc3NFxcdUQxNUNcXHVDNzQ0IFxcdUMxMjBcXHVEMEREXFx1RDU1OFxcdUM1RUMgXFx1QzVFQ1xcdUFFMzAgXFx1Q0Q5NFxcdUFDMDBcXHVENTU4XFx1QzEzOFxcdUM2OTQuXCJcbn07XG52YXIgYnV0dG9ucyA9IHtcbiAgY2xlYXJSZXNldDogXCJcXHVDRTk0XFx1QkM4NFxcdUMyQTQgXFx1Q0QwOFxcdUFFMzBcXHVENjU0XCIsXG4gIGV4cG9ydEpTT046IFwiXFx1RDMwQ1xcdUM3N0NcXHVCODVDIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCNFxcdUFFMzBcIixcbiAgZXhwb3J0SW1hZ2U6IFwiXFx1Qzc3NFxcdUJCRjhcXHVDOUMwIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCNFxcdUFFMzBcIixcbiAgZXhwb3J0OiBcIlxcdUIyRTRcXHVCOTc4IFxcdUM3NzRcXHVCOTg0XFx1QzczQ1xcdUI4NUMgXFx1QzgwMFxcdUM3QTUuLi5cIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIlxcdUQwNzRcXHVCOUJEXFx1QkNGNFxcdUI0RENcXHVCODVDIFxcdUJDRjVcXHVDMEFDXCIsXG4gIHNhdmU6IFwiXFx1RDYwNFxcdUM3QUMgXFx1RDMwQ1xcdUM3N0NcXHVDNUQwIFxcdUM4MDBcXHVDN0E1XCIsXG4gIHNhdmVBczogXCJcXHVCMkU0XFx1Qjk3OCBcXHVDNzc0XFx1Qjk4NFxcdUM3M0NcXHVCODVDIFxcdUM4MDBcXHVDN0E1XCIsXG4gIGxvYWQ6IFwiXFx1QzVGNFxcdUFFMzBcIixcbiAgZ2V0U2hhcmVhYmxlTGluazogXCJcXHVBQ0Y1XFx1QzcyMCBcXHVBQzAwXFx1QjJBNVxcdUQ1NUMgXFx1QjlDMVxcdUQwNkMgXFx1QzBERFxcdUMxMzFcIixcbiAgY2xvc2U6IFwiXFx1QjJFQlxcdUFFMzBcIixcbiAgc2VsZWN0TGFuZ3VhZ2U6IFwiXFx1QzVCOFxcdUM1QjQgXFx1QzEyMFxcdUQwRERcIixcbiAgc2Nyb2xsQmFja1RvQ29udGVudDogXCJcXHVDRjU4XFx1RDE1MFxcdUNFMjAgXFx1QzYwMVxcdUM1RURcXHVDNzNDXFx1Qjg1QyBcXHVDMkE0XFx1RDA2Q1xcdUI4NjRcXHVENTU4XFx1QUUzMFwiLFxuICB6b29tSW46IFwiXFx1RDY1NVxcdUIzMDBcIixcbiAgem9vbU91dDogXCJcXHVDRDk1XFx1QzE4Q1wiLFxuICByZXNldFpvb206IFwiXFx1RDY1NVxcdUIzMDAvXFx1Q0Q5NVxcdUMxOEMgXFx1Q0QwOFxcdUFFMzBcXHVENjU0XCIsXG4gIG1lbnU6IFwiXFx1QkE1NFxcdUIyNzRcIixcbiAgZG9uZTogXCJcXHVDNjQ0XFx1QjhDQ1wiLFxuICBlZGl0OiBcIlxcdUMyMThcXHVDODE1XCIsXG4gIHVuZG86IFwiXFx1QzJFNFxcdUQ1ODkgXFx1Q0RFOFxcdUMxOENcIixcbiAgcmVkbzogXCJcXHVCMkU0XFx1QzJEQyBcXHVDMkU0XFx1RDU4OVwiLFxuICByZXNldExpYnJhcnk6IFwiXFx1Qjc3Q1xcdUM3NzRcXHVCRTBDXFx1QjdFQ1xcdUI5QUMgXFx1QjlBQ1xcdUMxNEJcIixcbiAgY3JlYXRlTmV3Um9vbTogXCJcXHVCQzI5IFxcdUI5Q0NcXHVCNEU0XFx1QUUzMFwiLFxuICBmdWxsU2NyZWVuOiBcIlxcdUM4MDRcXHVDQ0I0XFx1RDY1NFxcdUJBNzRcIixcbiAgZGFya01vZGU6IFwiXFx1QjJFNFxcdUQwNkMgXFx1QkFBOFxcdUI0RENcIixcbiAgbGlnaHRNb2RlOiBcIlxcdUJDMURcXHVDNzQwIFxcdUJBQThcXHVCNERDXCIsXG4gIHplbk1vZGU6IFwiXFx1QzgyMCBcXHVCQUE4XFx1QjREQ1wiLFxuICBvYmplY3RzU25hcE1vZGU6IFwiXFx1QjJFNFxcdUI5NzggXFx1QzY5NFxcdUMxOENcXHVCNEU0XFx1QzVEMCBcXHVDODE1XFx1QjgyQ1xcdUMyRENcXHVEMEE0XFx1QUUzMFwiLFxuICBleGl0WmVuTW9kZTogXCJcXHVDODIwIFxcdUJBQThcXHVCNERDIFxcdUM4ODVcXHVCOENDXFx1RDU1OFxcdUFFMzBcIixcbiAgY2FuY2VsOiBcIlxcdUNERThcXHVDMThDXCIsXG4gIGNsZWFyOiBcIlxcdUM5QzBcXHVDNkIwXFx1QUUzMFwiLFxuICByZW1vdmU6IFwiXFx1QzBBRFxcdUM4MUNcIixcbiAgZW1iZWQ6IFwiXFx1Qzc4NFxcdUJDQTBcXHVCNTI5IFxcdUQxQTBcXHVBRTAwXCIsXG4gIHB1Ymxpc2hMaWJyYXJ5OiBcIlxcdUFDOENcXHVDMkRDXFx1RDU1OFxcdUFFMzBcIixcbiAgc3VibWl0OiBcIlxcdUM4MUNcXHVDRDlDXCIsXG4gIGNvbmZpcm06IFwiXFx1RDY1NVxcdUM3NzhcIixcbiAgZW1iZWRkYWJsZUludGVyYWN0aW9uQnV0dG9uOiBcIlxcdUQwNzRcXHVCOUFEXFx1RDU1OFxcdUM1RUMgXFx1QzBDMVxcdUQ2MzhcXHVDNzkxXFx1QzZBOVwiXG59O1xudmFyIGFsZXJ0cyA9IHtcbiAgY2xlYXJSZXNldDogXCJcXHVCQUE4XFx1QjRFMCBcXHVDNzkxXFx1QzVDNSBcXHVCMEI0XFx1QzZBOVxcdUM3NzQgXFx1Q0QwOFxcdUFFMzBcXHVENjU0XFx1QjQyOVxcdUIyQzhcXHVCMkU0LiBcXHVBQ0M0XFx1QzE4RFxcdUQ1NThcXHVDMkRDXFx1QUNBMFxcdUMyQjVcXHVCMkM4XFx1QUU0Qz9cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rOiBcIlxcdUFDRjVcXHVDNzIwIFxcdUFDMDBcXHVCMkE1XFx1RDU1QyBcXHVCOUMxXFx1RDA2Q1xcdUI5N0MgXFx1QzBERFxcdUMxMzFcXHVENTYwIFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rVG9vQmlnOiBcIlxcdUFDRjVcXHVDNzIwIFxcdUFDMDBcXHVCMkE1XFx1RDU1QyBcXHVCOUMxXFx1RDA2Q1xcdUI5N0MgXFx1QzBERFxcdUMxMzFcXHVENTYwIFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNDogXFx1RDY1NFxcdUJBNzRcXHVDNzc0IFxcdUIxMDhcXHVCQjM0IFxcdUQwN0RcXHVCMkM4XFx1QjJFNC5cIixcbiAgY291bGROb3RMb2FkSW52YWxpZEZpbGU6IFwiXFx1QzcyMFxcdUQ2QThcXHVENTU4XFx1QzlDMCBcXHVDNTRBXFx1Qzc0MCBcXHVEMzBDXFx1Qzc3Q1xcdUM3ODVcXHVCMkM4XFx1QjJFNC5cIixcbiAgaW1wb3J0QmFja2VuZEZhaWxlZDogXCJcXHVDMTFDXFx1QkM4NFxcdUI4NUNcXHVCRDgwXFx1RDEzMCBcXHVCRDg4XFx1QjdFQyBcXHVDNjI0XFx1QzlDMCBcXHVCQUJCXFx1RDU4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgY2Fubm90RXhwb3J0RW1wdHlDYW52YXM6IFwiXFx1QkU0OCBcXHVDRTk0XFx1QkM4NFxcdUMyQTRcXHVCOTdDIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCQyBcXHVDMjE4IFxcdUM1QzZcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIGNvdWxkTm90Q29weVRvQ2xpcGJvYXJkOiBcIlxcdUQwNzRcXHVCOUJEXFx1QkNGNFxcdUI0RENcXHVCODVDIFxcdUJDRjVcXHVDMEFDXFx1RDU1OFxcdUM5QzAgXFx1QkFCQlxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIGRlY3J5cHRGYWlsZWQ6IFwiXFx1QjM3MFxcdUM3NzRcXHVEMTMwXFx1Qjk3QyBcXHVCQ0Y1XFx1RDYzOFxcdUQ2NTRcXHVENTU4XFx1QzlDMCBcXHVCQUJCXFx1RDU4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgdXBsb2FkZWRTZWN1cmx5OiBcIlxcdUM1QzVcXHVCODVDXFx1QjREQ1xcdUIyOTQgXFx1Qzg4NVxcdUIyRTggXFx1QUMwNCBcXHVDNTU0XFx1RDYzOFxcdUQ2NTRcXHVCODVDIFxcdUJDRjRcXHVENjM4XFx1QjQxOFxcdUJCQzBcXHVCODVDIEV4Y2FsaWRyYXcgXFx1QzExQ1xcdUJDODQgXFx1QkMwRiBcXHVEMEMwXFx1QzBBQ1xcdUFDMDAgXFx1Q0Y1OFxcdUQxNTBcXHVDRTIwXFx1Qjk3QyBcXHVDNzdEXFx1Qzc0NCBcXHVDMjE4IFxcdUM1QzZcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIGxvYWRTY2VuZU92ZXJyaWRlUHJvbXB0OiBcIlxcdUM2NzhcXHVCRDgwIFxcdUQzMENcXHVDNzdDXFx1Qzc0NCBcXHVCRDg4XFx1QjdFQyBcXHVDNjI0XFx1QkE3NCBcXHVBRTMwXFx1Qzg3NCBcXHVDRjU4XFx1RDE1MFxcdUNFMjBcXHVBQzAwIFxcdUIzMDBcXHVDQ0I0XFx1QjQyOVxcdUIyQzhcXHVCMkU0LiBcXHVBQ0M0XFx1QzE4RCBcXHVDOUM0XFx1RDU4OVxcdUQ1NjBcXHVBRTRDXFx1QzY5ND9cIixcbiAgY29sbGFiU3RvcE92ZXJyaWRlUHJvbXB0OiBcIlxcdUQ2MTFcXHVDNUM1IFxcdUMxMzhcXHVDMTU4XFx1Qzc0NCBcXHVDODg1XFx1QjhDQ1xcdUQ1NThcXHVCQTc0IFxcdUI4NUNcXHVDRUVDIFxcdUM4MDBcXHVDN0E1XFx1QzE4Q1xcdUM1RDAgXFx1Qzc4OFxcdUIyOTQgXFx1QURGOFxcdUI5QkNcXHVDNzc0IFxcdUQ2MTFcXHVDNUM1IFxcdUMxMzhcXHVDMTU4XFx1Qzc1OCBcXHVBREY4XFx1QjlCQ1xcdUM3M0NcXHVCODVDIFxcdUIzMDBcXHVDQ0I0XFx1QjQyOVxcdUIyQzhcXHVCMkU0LiBcXHVDOUM0XFx1RDU4OVxcdUQ1NThcXHVBQ0EwXFx1QzJCNVxcdUIyQzhcXHVBRTRDP1xcblxcbihcXHVCODVDXFx1Q0VFQyBcXHVDODAwXFx1QzdBNVxcdUMxOENcXHVDNUQwIFxcdUM3ODhcXHVCMjk0IFxcdUFERjhcXHVCOUJDXFx1Qzc0NCBcXHVDNzIwXFx1QzlDMFxcdUQ1NThcXHVCODI0XFx1QkE3NCBcXHVENjA0XFx1QzdBQyBcXHVCRTBDXFx1Qjc3Q1xcdUM2QjBcXHVDODAwIFxcdUQwRURcXHVDNzQ0IFxcdUIyRUJcXHVDNTQ0XFx1QzhGQ1xcdUMxMzhcXHVDNjk0LilcIixcbiAgZXJyb3JBZGRpbmdUb0xpYnJhcnk6IFwiXFx1QzU0NFxcdUM3NzRcXHVEMTVDXFx1Qzc0NCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUM1RDAgXFx1Q0Q5NFxcdUFDMDAgXFx1RDU2MFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNFwiLFxuICBlcnJvclJlbW92aW5nRnJvbUxpYnJhcnk6IFwiXFx1Qjc3Q1xcdUM3NzRcXHVCRTBDXFx1QjdFQ1xcdUI5QUNcXHVDNUQwXFx1QzExQyBcXHVDNTQ0XFx1Qzc3NFxcdUQxNUNcXHVDNzQ0IFxcdUMwQURcXHVDODFDXFx1RDU2MFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNFwiLFxuICBjb25maXJtQWRkTGlicmFyeTogXCJ7e251bVNoYXBlc319XFx1QUMxQ1xcdUM3NTggXFx1QkFBOFxcdUM1OTFcXHVDNzc0IFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1QzVEMCBcXHVDRDk0XFx1QUMwMFxcdUI0MjlcXHVCMkM4XFx1QjJFNC4gXFx1QUNDNFxcdUMxOERcXHVENTU4XFx1QzJEQ1xcdUFDQTBcXHVDNUI0XFx1QzY5ND9cIixcbiAgaW1hZ2VEb2VzTm90Q29udGFpblNjZW5lOiBcIlxcdUM3NzQgXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1QjI5NCBcXHVENjU0XFx1QkE3NCBcXHVCMzcwXFx1Qzc3NFxcdUQxMzBcXHVCOTdDIFxcdUQzRUNcXHVENTY4XFx1RDU1OFxcdUFDRTAgXFx1Qzc4OFxcdUM5QzAgXFx1QzU0QVxcdUM3NDAgXFx1QUM4MyBcXHVBQzE5XFx1QzJCNVxcdUIyQzhcXHVCMkU0LiBcXHVCMEI0XFx1QkNGNFxcdUIwQkMgXFx1QjU0QyBcXHVENjU0XFx1QkE3NFxcdUM3NDQgXFx1Q0NBOFxcdUJEODBcXHVENTU4XFx1QjNDNFxcdUI4NUQgXFx1QzEyNFxcdUM4MTVcXHVENTU4XFx1QzE2OFxcdUIwOThcXHVDNjk0P1wiLFxuICBjYW5ub3RSZXN0b3JlRnJvbUltYWdlOiBcIlxcdUM3NzRcXHVCQkY4XFx1QzlDMCBcXHVEMzBDXFx1Qzc3Q1xcdUM1RDBcXHVDMTFDIFxcdUQ2NTRcXHVCQTc0XFx1Qzc0NCBcXHVCQ0Y1XFx1QUQ2Q1xcdUQ1NjAgXFx1QzIxOCBcXHVDNUM2XFx1QzVDOFxcdUMyQjVcXHVCMkM4XFx1QjJFNFwiLFxuICBpbnZhbGlkU2NlbmVVcmw6IFwiXFx1QzgxQ1xcdUFDRjVcXHVCNDFDIFVSTFxcdUM1RDBcXHVDMTFDIFxcdUQ2NTRcXHVCQTc0XFx1Qzc0NCBcXHVBQzAwXFx1QzgzOFxcdUM2MjRcXHVCMjk0XFx1QjM3MCBcXHVDMkU0XFx1RDMyOFxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIFxcdUM4RkNcXHVDMThDXFx1QUMwMCBcXHVDNzk4XFx1QkFCQlxcdUI0MThcXHVBQzcwXFx1QjA5OCwgXFx1QzcyMFxcdUQ2QThcXHVENTVDIEV4Y2FsaWRyYXcgSlNPTiBcXHVCMzcwXFx1Qzc3NFxcdUQxMzBcXHVCOTdDIFxcdUQzRUNcXHVENTY4XFx1RDU1OFxcdUFDRTAgXFx1Qzc4OFxcdUM5QzAgXFx1QzU0QVxcdUM3NDAgXFx1QUM4M1xcdUM3N0MgXFx1QzIxOCBcXHVDNzg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiLFxuICByZXNldExpYnJhcnk6IFwiXFx1QjJGOVxcdUMyRTBcXHVDNzU4IFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1Qjk3QyBcXHVDRDA4XFx1QUUzMFxcdUQ2NTQgXFx1RDU2OVxcdUIyQzhcXHVCMkU0LiBcXHVBQ0M0XFx1QzE4RFxcdUQ1NThcXHVDMkRDXFx1QUNBMFxcdUMyQjVcXHVCMkM4XFx1QUU0Qz9cIixcbiAgcmVtb3ZlSXRlbXNGcm9tc0xpYnJhcnk6IFwie3tjb3VudH19XFx1QUMxQ1xcdUM3NTggXFx1QzU0NFxcdUM3NzRcXHVEMTVDXFx1Qzc0NCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUM1RDBcXHVDMTFDIFxcdUMwQURcXHVDODFDXFx1RDU1OFxcdUMyRENcXHVBQ0EwXFx1QzJCNVxcdUIyQzhcXHVBRTRDP1wiLFxuICBpbnZhbGlkRW5jcnlwdGlvbktleTogXCJcXHVDNTU0XFx1RDYzOFxcdUQ2NTQgXFx1RDBBNFxcdUIyOTQgXFx1QkMxOFxcdUI0RENcXHVDMkRDIDIyXFx1QUUwMFxcdUM3OTBcXHVDNUVDXFx1QzU3QyBcXHVENTY5XFx1QjJDOFxcdUIyRTQuIFxcdUMyRTRcXHVDMkRDXFx1QUMwNCBcXHVENjExXFx1QzVDNVxcdUM3NzQgXFx1QkU0NFxcdUQ2NUNcXHVDMTMxXFx1RDY1NFxcdUI0MjlcXHVCMkM4XFx1QjJFNC5cIixcbiAgY29sbGFiT2ZmbGluZVdhcm5pbmc6IFwiXFx1Qzc3OFxcdUQxMzBcXHVCMTM3XFx1QzVEMCBcXHVDNUYwXFx1QUNCMFxcdUI0MThcXHVDNUI0IFxcdUM3ODhcXHVDOUMwIFxcdUM1NEFcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXFxuXFx1QkNDMFxcdUFDQkQgXFx1QzBBQ1xcdUQ1NkRcXHVCNEU0XFx1Qzc3NCBcXHVDODAwXFx1QzdBNVxcdUI0MThcXHVDOUMwIFxcdUM1NEFcXHVDMkI1XFx1QjJDOFxcdUIyRTQhXCJcbn07XG52YXIgZXJyb3JzID0ge1xuICB1bnN1cHBvcnRlZEZpbGVUeXBlOiBcIlxcdUM5QzBcXHVDNkQwXFx1RDU1OFxcdUM5QzAgXFx1QzU0QVxcdUIyOTQgXFx1RDMwQ1xcdUM3N0MgXFx1RDYxNVxcdUMyREQgXFx1Qzc4NVxcdUIyQzhcXHVCMkU0LlwiLFxuICBpbWFnZUluc2VydEVycm9yOiBcIlxcdUM3NzRcXHVCQkY4XFx1QzlDMFxcdUI5N0MgXFx1QzBCRFxcdUM3ODVcXHVENTYwIFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gXFx1QjA5OFxcdUM5MTFcXHVDNUQwIFxcdUIyRTRcXHVDMkRDIFxcdUMyRENcXHVCM0M0IFxcdUQ1NThcXHVDMkVEXFx1QzJEQ1xcdUM2MjRcIixcbiAgZmlsZVRvb0JpZzogXCJcXHVEMzBDXFx1Qzc3Q1xcdUM3NzQgXFx1QjEwOFxcdUJCMzQgXFx1RDA3RFxcdUIyQzhcXHVCMkU0LiBcXHVDRDVDXFx1QjMwMCBcXHVEMDZDXFx1QUUzMFxcdUIyOTQge3ttYXhTaXplfX0gXFx1Qzc4NVxcdUIyQzhcXHVCMkU0LlwiLFxuICBzdmdJbWFnZUluc2VydEVycm9yOiBcIlNWRyBcXHVDNzc0XFx1QkJGOFxcdUM5QzBcXHVCOTdDIFxcdUMwQkRcXHVDNzg1XFx1RDU1OFxcdUM5QzAgXFx1QkFCQlxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIFNWRyBcXHVCQjM4XFx1QkM5NVxcdUM3NzQgXFx1QzcyMFxcdUQ2QThcXHVENTU4XFx1QzlDMCBcXHVDNTRBXFx1Qzc0MCBcXHVBQzgzIFxcdUFDMTlcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIGZhaWxlZFRvRmV0Y2hJbWFnZTogXCJcXHVDNzc0XFx1QkJGOFxcdUM5QzBcXHVCOTdDIFxcdUFDMDBcXHVDODM4XFx1QzYyNFxcdUIyOTRcXHVCMzcwIFxcdUMyRTRcXHVEMzI4XFx1RDU4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgaW52YWxpZFNWR1N0cmluZzogXCJcXHVDNzIwXFx1RDZBOFxcdUQ1NThcXHVDOUMwIFxcdUM1NEFcXHVDNzQwIFNWR1xcdUM3ODVcXHVCMkM4XFx1QjJFNC5cIixcbiAgY2Fubm90UmVzb2x2ZUNvbGxhYlNlcnZlcjogXCJcXHVENjExXFx1QzVDNSBcXHVDMTFDXFx1QkM4NFxcdUM1RDAgXFx1QzgxMVxcdUMxOERcXHVENTU4XFx1QjI5NFxcdUIzNzAgXFx1QzJFNFxcdUQzMjhcXHVENTg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LiBcXHVEMzk4XFx1Qzc3NFxcdUM5QzBcXHVCOTdDIFxcdUMwQzhcXHVCODVDXFx1QUNFMFxcdUNFNjhcXHVENTU4XFx1QUNFMCBcXHVCMkU0XFx1QzJEQyBcXHVDMkRDXFx1QjNDNFxcdUQ1NzRcXHVCQ0Y0XFx1QzEzOFxcdUM2OTQuXCIsXG4gIGltcG9ydExpYnJhcnlFcnJvcjogXCJcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUI5N0MgXFx1QkQ4OFxcdUI3RUNcXHVDNjI0XFx1QzlDMCBcXHVCQUJCXFx1RDU4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgY29sbGFiU2F2ZUZhaWxlZDogXCJcXHVCMzcwXFx1Qzc3NFxcdUQxMzBcXHVCQ0EwXFx1Qzc3NFxcdUMyQTRcXHVDNUQwIFxcdUM4MDBcXHVDN0E1XFx1RDU1OFxcdUM5QzAgXFx1QkFCQlxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIFxcdUJCMzhcXHVDODFDXFx1QUMwMCBcXHVBQ0M0XFx1QzE4RCBcXHVCNDFDXFx1QjJFNFxcdUJBNzQsIFxcdUM3OTFcXHVDNUM1IFxcdUIwQjRcXHVDNkE5XFx1Qzc0NCBcXHVDNzgzXFx1QzlDMCBcXHVDNTRBXFx1QjNDNFxcdUI4NUQgXFx1Qjg1Q1xcdUNFRUMgXFx1QzgwMFxcdUM3QTVcXHVDMThDXFx1QzVEMCBcXHVDODAwXFx1QzdBNVxcdUQ1NzQgXFx1QzhGQ1xcdUMxMzhcXHVDNjk0LlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkX3NpemVFeGNlZWRlZDogXCJcXHVCMzcwXFx1Qzc3NFxcdUQxMzBcXHVCQ0EwXFx1Qzc3NFxcdUMyQTRcXHVDNUQwIFxcdUM4MDBcXHVDN0E1XFx1RDU1OFxcdUM5QzAgXFx1QkFCQlxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIFxcdUNFOTRcXHVCQzg0XFx1QzJBNFxcdUFDMDAgXFx1QjEwOFxcdUJCMzQgXFx1RDA3MCBcXHVBQzcwIFxcdUFDMTlcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIFxcdUJCMzhcXHVDODFDXFx1QUMwMCBcXHVBQ0M0XFx1QzE4RCBcXHVCNDFDXFx1QjJFNFxcdUJBNzQsIFxcdUM3OTFcXHVDNUM1IFxcdUIwQjRcXHVDNkE5XFx1Qzc0NCBcXHVDNzgzXFx1QzlDMCBcXHVDNTRBXFx1QjNDNFxcdUI4NUQgXFx1Qjg1Q1xcdUNFRUMgXFx1QzgwMFxcdUM3QTVcXHVDMThDXFx1QzVEMCBcXHVDODAwXFx1QzdBNVxcdUQ1NzQgXFx1QzhGQ1xcdUMxMzhcXHVDNjk0LlwiLFxuICBpbWFnZVRvb2xOb3RTdXBwb3J0ZWQ6IFwiXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1QUMwMCBcXHVCRTQ0XFx1RDY1Q1xcdUMxMzFcXHVENjU0IFxcdUI0MThcXHVDNUM4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJcXHVBREMwXFx1RDU1OFxcdUFFRDhcXHVDMTFDXFx1QjI5NCA8Ym9sZD5cXHVBQzE1XFx1QjgyNVxcdUQ1NUMgXFx1QzlDMFxcdUJCMzggXFx1Q0MyOFxcdUIyRTggXFx1QzEyNFxcdUM4MTU8L2JvbGQ+XFx1Qzc3NCBcXHVENjVDXFx1QzEzMVxcdUQ2NTRcXHVCNDFDIEJyYXZlIGJyb3dzZXJcXHVCOTdDIFxcdUMwQUNcXHVDNkE5XFx1RDU1OFxcdUFDRTAgXFx1QUNDNFxcdUMyRTAgXFx1QUM4MyBcXHVBQzE5XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiLFxuICAgIGxpbmUyOiBcIlxcdUM3NzQgXFx1QUUzMFxcdUIyQTVcXHVDNzNDXFx1Qjg1QyBcXHVDNzc4XFx1RDU3NCBcXHVENjU0XFx1Qzc3NFxcdUQyQjhcXHVCQ0Y0XFx1QjREQ1xcdUM3NTggPGJvbGQ+XFx1RDE0RFxcdUMyQTRcXHVEMkI4IFxcdUM2OTRcXHVDMThDXFx1QjRFNDwvYm9sZD5cXHVDNzc0IFxcdUMxOTBcXHVDMEMxXFx1QjQyMCBcXHVDMjE4IFxcdUM3ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gICAgbGluZTM6IFwiXFx1QzgwMFxcdUQ3NkNcXHVCMjk0IFxcdUQ1NzRcXHVCMkY5IFxcdUFFMzBcXHVCMkE1XFx1Qzc0NCBcXHVCRTQ0XFx1RDY1Q1xcdUMxMzFcXHVENjU0XFx1RDU1OFxcdUIyOTQgXFx1QUM4M1xcdUM3NDQgXFx1QUMxNVxcdUI4MjVcXHVENzg4IFxcdUFEOENcXHVDN0E1IFxcdUI0RENcXHVCOUJEXFx1QjJDOFxcdUIyRTQuIFxcdUJFNDRcXHVENjVDXFx1QzEzMVxcdUQ2NTQgXFx1QkMyOVxcdUJDOTVcXHVDNUQwIFxcdUIzMDBcXHVENTc0XFx1QzExQ1xcdUIyOTQgPGxpbms+XFx1Qzc3NCBcXHVBQzhDXFx1QzJEQ1xcdUFFMDA8L2xpbms+XFx1Qzc0NCBcXHVDQzM4XFx1QUNFMFxcdUQ1NzRcXHVDOEZDXFx1QzEzOFxcdUM2OTQuXCIsXG4gICAgbGluZTQ6IFwiXFx1QjlDQ1xcdUM1N0QgXFx1Qzc3NCBcXHVDMTI0XFx1QzgxNVxcdUM3NDQgXFx1QUVEMFxcdUM3NENcXHVDNUQwXFx1QjNDNCBcXHVEMTREXFx1QzJBNFxcdUQyQjggXFx1QzY5NFxcdUMxOENcXHVCNEU0XFx1Qzc3NCBcXHVDNjJDXFx1QkMxNFxcdUI5NzRcXHVBQzhDIFxcdUQ0NUNcXHVDMkRDXFx1QjQxOFxcdUM5QzAgXFx1QzU0QVxcdUIyOTRcXHVCMkU0XFx1QkE3NCwgXFx1QzgwMFxcdUQ3NkMgR2l0aHViXFx1QzVEMCA8aXNzdWVMaW5rPlxcdUM3NzRcXHVDMjg4PC9pc3N1ZUxpbms+XFx1Qjk3QyBcXHVDNjJDXFx1QjgyNFxcdUM4RkNcXHVDMkRDXFx1QUM3MFxcdUIwOTggPGRpc2NvcmRMaW5rPkRpc2NvcmQ8L2Rpc2NvcmRMaW5rPlxcdUI4NUMgXFx1QzU0Q1xcdUI4MjRcXHVDOEZDXFx1QzEzOFxcdUM2OTQuXCJcbiAgfSxcbiAgbGlicmFyeUVsZW1lbnRUeXBlRXJyb3I6IHtcbiAgICBlbWJlZGRhYmxlOiBcIlxcdUM3ODRcXHVCQ0EwXFx1QjREQyBcXHVDNjk0XFx1QzE4Q1xcdUI0RTRcXHVDNzQwIFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1QzVEMCBcXHVDRDk0XFx1QUMwMFxcdUQ1NjAgXFx1QzIxOCBcXHVDNUM2XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiLFxuICAgIGlmcmFtZTogXCJJRnJhbWUgXFx1QzY5NFxcdUMxOENcXHVCNEU0XFx1Qzc0MCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUM1RDAgXFx1Q0Q5NFxcdUFDMDBcXHVENTYwIFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgICBpbWFnZTogXCJcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUM1RDAgXFx1Qzc3NFxcdUJCRjhcXHVDOUMwIFxcdUMwQkRcXHVDNzg1IFxcdUFFMzBcXHVCMkE1XFx1Qzc0MCBcXHVBQ0U3IFxcdUM5QzBcXHVDNkQwXFx1QjQyMCBcXHVDNjA4XFx1QzgxNVxcdUM3ODVcXHVCMkM4XFx1QjJFNCFcIlxuICB9LFxuICBhc3luY1Bhc3RlRmFpbGVkT25SZWFkOiBcIlxcdUJEOTlcXHVDNUVDXFx1QjEyM1xcdUIyOTRcXHVCMzcwIFxcdUMyRTRcXHVEMzI4XFx1RDU4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gKFxcdUMyRENcXHVDMkE0XFx1RDE1QyBcXHVEMDc0XFx1QjlCRFxcdUJDRjRcXHVCNERDXFx1Qjk3QyBcXHVDNzdEXFx1QjI5NFxcdUIzNzAgXFx1QzJFNFxcdUQzMjhcXHVENTg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0KVwiLFxuICBhc3luY1Bhc3RlRmFpbGVkT25QYXJzZTogXCJcXHVCRDk5XFx1QzVFQ1xcdUIxMjNcXHVCMjk0XFx1QjM3MCBcXHVDMkU0XFx1RDMyOFxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIGNvcHlUb1N5c3RlbUNsaXBib2FyZEZhaWxlZDogXCJcXHVEMDc0XFx1QjlCRFxcdUJDRjRcXHVCNERDXFx1Qjg1QyBcXHVCQ0Y1XFx1QzBBQ1xcdUQ1NThcXHVCMjk0XFx1QjM3MCBcXHVDMkU0XFx1RDMyOFxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCJcbn07XG52YXIgdG9vbEJhciA9IHtcbiAgc2VsZWN0aW9uOiBcIlxcdUMxMjBcXHVEMEREXCIsXG4gIGltYWdlOiBcIlxcdUM3NzRcXHVCQkY4XFx1QzlDMCBcXHVDMEJEXFx1Qzc4NVwiLFxuICByZWN0YW5nbGU6IFwiXFx1QzBBQ1xcdUFDMDFcXHVENjE1XCIsXG4gIGRpYW1vbmQ6IFwiXFx1QjJFNFxcdUM3NzRcXHVDNTQ0XFx1QkFBQ1xcdUI0RENcIixcbiAgZWxsaXBzZTogXCJcXHVEMEMwXFx1QzZEMFwiLFxuICBhcnJvdzogXCJcXHVENjU0XFx1QzBCNFxcdUQ0NUNcIixcbiAgbGluZTogXCJcXHVDMTIwXCIsXG4gIGZyZWVkcmF3OiBcIlxcdUFERjhcXHVCOUFDXFx1QUUzMFwiLFxuICB0ZXh0OiBcIlxcdUQxNERcXHVDMkE0XFx1RDJCOFwiLFxuICBsaWJyYXJ5OiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXCIsXG4gIGxvY2s6IFwiXFx1QzEyMFxcdUQwRERcXHVCNDFDIFxcdUIzQzRcXHVBRDZDIFxcdUM3MjBcXHVDOUMwXFx1RDU1OFxcdUFFMzBcIixcbiAgcGVuTW9kZTogXCJcXHVEMzlDIFxcdUJBQThcXHVCNERDIC0gXFx1RDEzMFxcdUNFNTggXFx1QkMyOVxcdUM5QzBcIixcbiAgbGluazogXCJcXHVDMTIwXFx1RDBERFxcdUQ1NUMgXFx1QjNDNFxcdUQ2MTVcXHVDNUQwIFxcdUIzMDBcXHVENTc0XFx1QzExQyBcXHVCOUMxXFx1RDA2Q1xcdUI5N0MgXFx1Q0Q5NFxcdUFDMDAvXFx1QzVDNVxcdUIzNzBcXHVDNzc0XFx1RDJCOFwiLFxuICBlcmFzZXI6IFwiXFx1QzlDMFxcdUM2QjBcXHVBQzFDXCIsXG4gIGZyYW1lOiBcIlxcdUQ1MDRcXHVCODA4XFx1Qzc4NCBcXHVCM0M0XFx1QUQ2Q1wiLFxuICBtYWdpY2ZyYW1lOiBcIlxcdUM2NDBcXHVDNzc0XFx1QzVCNFxcdUQ1MDRcXHVCODA4XFx1Qzc4NFxcdUM3NDQgXFx1Q0Y1NFxcdUI0RENcXHVCODVDXCIsXG4gIGVtYmVkZGFibGU6IFwiXFx1QzZGOSBcXHVDNzg0XFx1QkNBMFxcdUI0RENcIixcbiAgbGFzZXI6IFwiXFx1QjgwOFxcdUM3NzRcXHVDODAwIFxcdUQzRUNcXHVDNzc4XFx1RDEzMFwiLFxuICBoYW5kOiBcIlxcdUMxOTAgKFxcdUQzMjhcXHVCMkREIFxcdUIzQzRcXHVBRDZDKVwiLFxuICBleHRyYVRvb2xzOiBcIlxcdUIyRTRcXHVCOTc4IFxcdUIzQzRcXHVBRDZDXCIsXG4gIG1lcm1haWRUb0V4Y2FsaWRyYXc6IFwiTWVybWFpZFxcdUM1RDBcXHVDMTFDIFxcdUJEODhcXHVCN0VDXFx1QzYyNFxcdUFFMzBcIixcbiAgbWFnaWNTZXR0aW5nczogXCJBSSBcXHVDMTI0XFx1QzgxNVwiXG59O1xudmFyIGhlYWRpbmdzID0ge1xuICBjYW52YXNBY3Rpb25zOiBcIlxcdUNFOTRcXHVCQzg0XFx1QzJBNCBcXHVCM0Q5XFx1Qzc5MVwiLFxuICBzZWxlY3RlZFNoYXBlQWN0aW9uczogXCJcXHVDMTIwXFx1RDBERFxcdUI0MUMgXFx1QkFBOFxcdUM1OTEgXFx1QjNEOVxcdUM3OTFcIixcbiAgc2hhcGVzOiBcIlxcdUJBQThcXHVDNTkxXCJcbn07XG52YXIgaGludHMgPSB7XG4gIGNhbnZhc1Bhbm5pbmc6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0XFx1Qjk3QyBcXHVDNjJFXFx1QUUzMFxcdUI4MjRcXHVCQTc0IFxcdUI5QzhcXHVDNkIwXFx1QzJBNCBcXHVENzIwXFx1Qzc3NFxcdUIwOTggXFx1QzJBNFxcdUQzOThcXHVDNzc0XFx1QzJBNFxcdUJDMTRcXHVCOTdDIFxcdUIyMDRcXHVCOTc0XFx1QUNFMCBcXHVCNERDXFx1Qjc5OFxcdUFERjhcXHVENTU4XFx1QUM3MFxcdUIwOTgsIFxcdUMxOTAgXFx1QjNDNFxcdUFENkNcXHVCOTdDIFxcdUMwQUNcXHVDNkE5XFx1RDU1OFxcdUFFMzBcIixcbiAgbGluZWFyRWxlbWVudDogXCJcXHVDNUVDXFx1QjdFQyBcXHVDODEwXFx1Qzc0NCBcXHVDNUYwXFx1QUNCMFxcdUQ1NThcXHVCODI0XFx1QkE3NCBcXHVEMDc0XFx1QjlBRFxcdUQ1NThcXHVBQ0UwLCBcXHVDOUMxXFx1QzEyMFxcdUM3NDQgXFx1QURGOFxcdUI5QUNcXHVCODI0XFx1QkE3NCBcXHVCQzE0XFx1Qjg1QyBcXHVCNERDXFx1Qjc5OFxcdUFERjhcXHVENTU4XFx1QzEzOFxcdUM2OTQuXCIsXG4gIGZyZWVEcmF3OiBcIlxcdUQwNzRcXHVCOUFEIFxcdUQ2QzQgXFx1QjREQ1xcdUI3OThcXHVBREY4XFx1RDU1OFxcdUMxMzhcXHVDNjk0LiBcXHVDNjQ0XFx1QjhDQ1xcdUI0MThcXHVCQTc0IFxcdUIxOTNcXHVDNzNDXFx1QzEzOFxcdUM2OTQuXCIsXG4gIHRleHQ6IFwiXFx1RDMwMTogXFx1QzEyMFxcdUQwREQgXFx1RDIzNFxcdUI4NUMgXFx1QzU0NFxcdUJCMzQgXFx1QUNGM1xcdUM3NzRcXHVCMDk4IFxcdUIzNTRcXHVCRTE0IFxcdUQwNzRcXHVCOUFEXFx1RDU3NCBcXHVEMTREXFx1QzJBNFxcdUQyQjhcXHVCOTdDIFxcdUNEOTRcXHVBQzAwXFx1RDU2MCBcXHVDMjE4XFx1QjNDNCBcXHVDNzg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiLFxuICBlbWJlZGRhYmxlOiBcIlxcdUQwNzRcXHVCOUFEIFxcdUJDMEYgXFx1QjREQ1xcdUI3OThcXHVBREY4XFx1RDU1OFxcdUM1RUMgXFx1QzZGOVxcdUMwQUNcXHVDNzc0XFx1RDJCOCBcXHVDNzg0XFx1QkNBMFxcdUI0REMgXFx1QjlDQ1xcdUI0RTRcXHVBRTMwXCIsXG4gIHRleHRfc2VsZWN0ZWQ6IFwiXFx1QjM1NFxcdUJFMTQgXFx1RDA3NFxcdUI5QUQgXFx1QjYxMFxcdUIyOTQgRU5URVJcXHVCOTdDIFxcdUIyMENcXHVCN0VDXFx1QzExQyBcXHVEMTREXFx1QzJBNFxcdUQyQjggXFx1QzIxOFxcdUM4MTVcIixcbiAgdGV4dF9lZGl0aW5nOiBcIkVTQ1xcdUIwOTggQ3RybE9yQ21kK0VOVEVSXFx1Qjk3QyBcXHVCMjBDXFx1QjdFQ1xcdUMxMUMgXFx1QzIxOFxcdUM4MTVcXHVDNzQ0IFxcdUM4ODVcXHVCOENDXFx1RDU1OFxcdUFFMzBcIixcbiAgbGluZWFyRWxlbWVudE11bHRpOiBcIlxcdUI5QzhcXHVDOUMwXFx1QjlDOSBcXHVDOUMwXFx1QzgxMFxcdUM3NDQgXFx1RDA3NFxcdUI5QURcXHVENTU4XFx1QUM3MFxcdUIwOTggRXNjIFxcdUI2MTBcXHVCMjk0IEVudGVyIFxcdUQwQTRcXHVCOTdDIFxcdUIyMENcXHVCN0VDIFxcdUM2NDRcXHVCOENDXFx1RDU1OFxcdUMxMzhcXHVDNjk0LlwiLFxuICBsb2NrQW5nbGU6IFwiU0hJRlQgXFx1RDBBNFxcdUI5N0MgXFx1QjIwNFxcdUI5NzRcXHVCQTc0XFx1QzExQyBcXHVENjhDXFx1QzgwNFxcdUQ1NThcXHVCQTc0IFxcdUFDMDFcXHVCM0M0XFx1Qjk3QyBcXHVDODFDXFx1RDU1Q1xcdUQ1NjAgXFx1QzIxOCBcXHVDNzg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiLFxuICByZXNpemU6IFwiU0hJRlQgXFx1RDBBNFxcdUI5N0MgXFx1QjIwNFxcdUI5NzRcXHVCQTc0XFx1QzExQyBcXHVDODcwXFx1QzgxNVxcdUQ1NThcXHVCQTc0IFxcdUQwNkNcXHVBRTMwXFx1Qzc1OCBcXHVCRTQ0XFx1QzcyOFxcdUM3NzQgXFx1QzgxQ1xcdUQ1NUNcXHVCNDI5XFx1QjJDOFxcdUIyRTQuXFxuQUxUXFx1Qjk3QyBcXHVCMjA0XFx1Qjk3NFxcdUJBNzRcXHVDMTFDIFxcdUM4NzBcXHVDODE1XFx1RDU1OFxcdUJBNzQgXFx1QzkxMVxcdUM1NTlcXHVDNzQ0IFxcdUFFMzBcXHVDOTAwXFx1QzczQ1xcdUI4NUMgXFx1RDA2Q1xcdUFFMzBcXHVCOTdDIFxcdUM4NzBcXHVDODE1XFx1RDU2OVxcdUIyQzhcXHVCMkU0LlwiLFxuICByZXNpemVJbWFnZTogXCJTSElGVFxcdUI5N0MgXFx1QjIwQ1xcdUI3RUNcXHVDMTFDIFxcdUM3OTBcXHVDNzIwXFx1Qjg2RFxcdUFDOEMgXFx1RDA2Q1xcdUFFMzBcXHVCOTdDIFxcdUJDQzBcXHVBQ0JEXFx1RDU1OFxcdUFDNzBcXHVCMDk4LFxcbkFMVFxcdUI5N0MgXFx1QjIwQ1xcdUI3RUNcXHVDMTFDIFxcdUM5MTFcXHVDNTU5XFx1Qzc0NCBcXHVBQ0UwXFx1QzgxNVxcdUQ1NThcXHVBQ0UwIFxcdUQwNkNcXHVBRTMwXFx1Qjk3QyBcXHVCQ0MwXFx1QUNCRFxcdUQ1NThcXHVBRTMwXCIsXG4gIHJvdGF0ZTogXCJTSElGVCBcXHVEMEE0XFx1Qjk3QyBcXHVCMjA0XFx1Qjk3NFxcdUJBNzRcXHVDMTFDIFxcdUQ2OENcXHVDODA0XFx1RDU1OFxcdUJBNzQgXFx1QUMwMVxcdUIzQzRcXHVCOTdDIFxcdUM4MUNcXHVENTVDXFx1RDU2MCBcXHVDMjE4IFxcdUM3ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIGxpbmVFZGl0b3JfaW5mbzogXCJcXHVBRjJEXFx1QzlEM1xcdUM4MTBcXHVDNzQ0IFxcdUMyMThcXHVDODE1XFx1RDU1OFxcdUI4MjRcXHVCQTc0IEN0cmxPckNtZCBcXHVEMEE0XFx1Qjk3QyBcXHVCMjA0XFx1Qjk3NFxcdUFDRTAgXFx1QjM1NFxcdUJFMTQgXFx1RDA3NFxcdUI5QURcXHVDNzQ0IFxcdUQ1NThcXHVBQzcwXFx1QjA5OCBDdHJsT3JDbWQgKyBFbnRlclxcdUI5N0MgXFx1QjIwNFxcdUI5NzRcXHVDMTM4XFx1QzY5NC5cIixcbiAgbGluZUVkaXRvcl9wb2ludFNlbGVjdGVkOiBcIkRlbGV0ZSBcXHVEMEE0XFx1Qjg1QyBcXHVBRjJEXFx1QzlEM1xcdUM4MTBcXHVDNzQ0IFxcdUM4MUNcXHVBQzcwXFx1RDU1OFxcdUFDNzBcXHVCMDk4LFxcbkN0cmxPckNtZCtEIFxcdUI4NUMgXFx1QkNGNVxcdUM4MUNcXHVENTU4XFx1QUM3MFxcdUIwOTgsIFxcdUI0RENcXHVCNzk4XFx1QURGOCBcXHVENTc0XFx1QzExQyBcXHVDNzc0XFx1QjNEOVxcdUMyRENcXHVEMEE0XFx1QUUzMFwiLFxuICBsaW5lRWRpdG9yX25vdGhpbmdTZWxlY3RlZDogXCJcXHVBRjJEXFx1QzlEM1xcdUM4MTBcXHVDNzQ0IFxcdUMxMjBcXHVEMEREXFx1RDU3NFxcdUMxMUMgXFx1QzIxOFxcdUM4MTVcXHVENTU4XFx1QUM3MFxcdUIwOTggKFNISUZUXFx1Qjk3QyBcXHVCMjBDXFx1QjdFQ1xcdUMxMUMgXFx1QzVFQ1xcdUI3RUNcXHVBQzFDIFxcdUMxMjBcXHVEMEREKSxcXG5BbHRcXHVCOTdDIFxcdUIyMDRcXHVCOTc0XFx1QUNFMCBcXHVEMDc0XFx1QjlBRFxcdUQ1NzRcXHVDMTFDIFxcdUMwQzhcXHVCODVDXFx1QzZCNCBcXHVBRjJEXFx1QzlEM1xcdUM4MTAgXFx1Q0Q5NFxcdUFDMDBcXHVENTU4XFx1QUUzMFwiLFxuICBwbGFjZUltYWdlOiBcIlxcdUQwNzRcXHVCOUFEXFx1RDU3NFxcdUMxMUMgXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1Qjk3QyBcXHVCQzMwXFx1Q0U1OFxcdUQ1NThcXHVBQzcwXFx1QjA5OCwgXFx1RDA3NFxcdUI5QURcXHVENTU4XFx1QUNFMCBcXHVCNERDXFx1Qjc5OFxcdUFERjhcXHVENTc0XFx1QzExQyBcXHVDMEFDXFx1Qzc3NFxcdUM5ODhcXHVCOTdDIFxcdUM4NzBcXHVDODE1XFx1RDU1OFxcdUFFMzBcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiXFx1QjJGOVxcdUMyRTBcXHVCOUNDXFx1Qzc1OCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUI5N0MgXFx1QUM4Q1xcdUMyRENcXHVENTU4XFx1QUUzMFwiLFxuICBiaW5kVGV4dFRvRWxlbWVudDogXCJFbnRlciBcXHVEMEE0XFx1Qjk3QyBcXHVCMjBDXFx1QjdFQ1xcdUMxMUMgXFx1RDE0RFxcdUMyQTRcXHVEMkI4IFxcdUNEOTRcXHVBQzAwXFx1RDU1OFxcdUFFMzBcIixcbiAgZGVlcEJveFNlbGVjdDogXCJDdHJsT3JDbWQgXFx1RDBBNFxcdUI5N0MgXFx1QjIwQ1xcdUI3RUNcXHVDMTFDIFxcdUFFNEFcXHVBQzhDIFxcdUMxMjBcXHVEMEREXFx1RDU1OFxcdUFDRTAsIFxcdUI0RENcXHVCNzk4XFx1QURGOFxcdUQ1NThcXHVDOUMwIFxcdUM1NEFcXHVCM0M0XFx1Qjg1RCBcXHVENTU4XFx1QUUzMFwiLFxuICBlcmFzZXJSZXZlcnQ6IFwiQWx0XFx1Qjk3QyBcXHVCMjBDXFx1QjdFQ1xcdUMxMUMgXFx1QzBBRFxcdUM4MUNcXHVENTU4XFx1QjNDNFxcdUI4NUQgXFx1QzlDMFxcdUM4MTVcXHVCNDFDIFxcdUM2OTRcXHVDMThDXFx1Qjk3QyBcXHVCNDE4XFx1QjNDQ1xcdUI5QUNcXHVBRTMwXCIsXG4gIGZpcmVmb3hfY2xpcGJvYXJkX3dyaXRlOiAnXFx1Qzc3NCBcXHVBRTMwXFx1QjJBNVxcdUM3NDAgXFx1QzEyNFxcdUM4MTVcXHVDNUQwXFx1QzExQyBcImRvbS5ldmVudHMuYXN5bmNDbGlwYm9hcmQuY2xpcGJvYXJkSXRlbVwiIFxcdUQ1MENcXHVCNzk4XFx1QURGOFxcdUI5N0MgXCJ0cnVlXCJcXHVCODVDIFxcdUMxMjRcXHVDODE1XFx1RDU1OFxcdUM1RUMgXFx1RDY1Q1xcdUMxMzFcXHVENjU0XFx1RDU2MCBcXHVDMjE4IFxcdUM3ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIEZpcmVmb3hcXHVDNUQwXFx1QzExQyBcXHVCRTBDXFx1Qjc3Q1xcdUM2QjBcXHVDODAwIFxcdUQ1MENcXHVCNzk4XFx1QURGOFxcdUI5N0MgXFx1QzIxOFxcdUM4MTVcXHVENTU4XFx1QjgyNFxcdUJBNzQsIFwiYWJvdXQ6Y29uZmlnXCIgXFx1RDM5OFxcdUM3NzRcXHVDOUMwXFx1QzVEMCBcXHVDODExXFx1QzE4RFxcdUQ1NThcXHVDMTM4XFx1QzY5NC4nLFxuICBkaXNhYmxlU25hcHBpbmc6IFwiQ3RybE9yQ21kIFxcdUQwQTRcXHVCOTdDIFxcdUIyMENcXHVCN0VDXFx1QzExQyBcXHVCMkU0XFx1Qjk3OCBcXHVDNjk0XFx1QzE4Q1xcdUM2NDBcXHVDNzU4IFxcdUM4MTVcXHVCODJDIFxcdUJCMzRcXHVDMkRDXFx1RDU1OFxcdUFFMzBcIlxufTtcbnZhciBjYW52YXNFcnJvciA9IHtcbiAgY2Fubm90U2hvd1ByZXZpZXc6IFwiXFx1QkJGOFxcdUI5QUNcXHVCQ0Y0XFx1QUUzMFxcdUI5N0MgXFx1QkNGQyBcXHVDMjE4IFxcdUM1QzZcXHVDMkI1XFx1QjJDOFxcdUIyRTRcIixcbiAgY2FudmFzVG9vQmlnOiBcIlxcdUNFOTRcXHVCQzg0XFx1QzJBNFxcdUFDMDAgXFx1QjEwOFxcdUJCMzQgXFx1RDA3RFxcdUIyQzhcXHVCMkU0LlwiLFxuICBjYW52YXNUb29CaWdUaXA6IFwiXFx1RDMwMTogXFx1QkE0MFxcdUI5QUMgXFx1Qzc4OFxcdUIyOTQgXFx1QzY5NFxcdUMxOENcXHVCNEU0XFx1Qzc0NCBcXHVDODgwIFxcdUIzNTQgXFx1QUMwMFxcdUFFNENcXHVDNzc0XFx1Qjg1QyBcXHVCRDk5XFx1QzVFQyBcXHVCQ0Y0XFx1QzEzOFxcdUM2OTQuXCJcbn07XG52YXIgZXJyb3JTcGxhc2ggPSB7XG4gIGhlYWRpbmdNYWluOiBcIlxcdUM2MjRcXHVCOTU4XFx1QUMwMCBcXHVCQzFDXFx1QzBERFxcdUQ1ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIDxidXR0b24+XFx1RDM5OFxcdUM3NzRcXHVDOUMwIFxcdUMwQzhcXHVCODVDXFx1QUNFMFxcdUNFNjg8L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNNZXNzYWdlOiBcIlxcdUMwQzhcXHVCODVDXFx1QUNFMFxcdUNFNjhcXHVDNzNDXFx1Qjg1QyBcXHVENTc0XFx1QUNCMFxcdUI0MThcXHVDOUMwIFxcdUM1NEFcXHVDNzQ0IFxcdUFDQkRcXHVDNkIwLCA8YnV0dG9uPlxcdUNFOTRcXHVCQzg0XFx1QzJBNCBcXHVCRTQ0XFx1QzZCMFxcdUFFMzA8L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNDYXZlYXQ6IFwiIFxcdUM3OTFcXHVDNUM1IFxcdUIwQjRcXHVDNkE5XFx1Qzc0NCBcXHVDNzgzXFx1QUM4QyBcXHVCNDI5XFx1QjJDOFxcdUIyRTQgXCIsXG4gIHRyYWNrZWRUb1NlbnRyeTogXCJcXHVDNjI0XFx1Qjk1OCB7e2V2ZW50SWR9fSBcXHVBQzAwIFxcdUMyRENcXHVDMkE0XFx1RDE1Q1xcdUM1RDBcXHVDMTFDIFxcdUJDMUNcXHVBQ0FDXFx1QjQxOFxcdUM1QzhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIG9wZW5Jc3N1ZU1lc3NhZ2U6IFwiXFx1QzgwMFxcdUQ3NkNcXHVCMjk0IFxcdUQ2NTRcXHVCQTc0IFxcdUM4MTVcXHVCQ0Y0XFx1Qjk3QyBcXHVDNjI0XFx1Qjk1OFxcdUM1RDAgXFx1RDNFQ1xcdUQ1NjhcXHVENTU4XFx1QzlDMCBcXHVDNTRBXFx1QjNDNFxcdUI4NUQgXFx1QjlFNFxcdUM2QjAgXFx1QzhGQ1xcdUM3NThcXHVENTU4XFx1QUNFMCBcXHVDNzg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LiBcXHVENjM5XFx1QzJEQyBcXHVENjU0XFx1QkE3NFxcdUM1RDAgXFx1QkJGQ1xcdUFDMTBcXHVENTVDIFxcdUIwQjRcXHVDNkE5XFx1Qzc3NCBcXHVDNUM2XFx1QjJFNFxcdUJBNzQgXFx1Qzc3NFxcdUFDRjNcXHVDNUQwIFxcdUM1QzVcXHVCODVDXFx1QjREQ1xcdUI5N0MgXFx1QUNFMFxcdUI4MjRcXHVENTc0XFx1QzhGQ1xcdUMxMzhcXHVDNjk0LjxidXR0b24+XFx1QkM4NFxcdUFERjggXFx1RDJCOFxcdUI3OThcXHVDRUU0PC9idXR0b24+IFxcdUM1NDRcXHVCNzk4IFxcdUM4MTVcXHVCQ0Y0XFx1Qjk3QyBHaXRIdWIgXFx1Qzc3NFxcdUMyODhcXHVDNUQwIFxcdUJDRjVcXHVDMEFDIFxcdUJDMEYgXFx1QkQ5OVxcdUM1RUNcXHVCMTIzXFx1QUUzMFxcdUQ1NzQgXFx1QzhGQ1xcdUMxMzhcXHVDNjk0LlwiLFxuICBzY2VuZUNvbnRlbnQ6IFwiXFx1RDY1NFxcdUJBNzQgXFx1QjBCNFxcdUM2QTk6XCJcbn07XG52YXIgcm9vbURpYWxvZyA9IHtcbiAgZGVzY19pbnRybzogXCJcXHVENjA0XFx1QzdBQyBcXHVENjU0XFx1QkE3NFxcdUM1RDAgXFx1QUNGNVxcdUIzRDkgXFx1Qzc5MVxcdUM1QzVcXHVDNzkwXFx1Qjk3QyBcXHVDRDA4XFx1QjMwMFxcdUQ1NzQgXFx1RDYxMVxcdUM1QzVcXHVENTYwIFxcdUMyMTggXFx1Qzc4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgZGVzY19wcml2YWN5OiBcIlxcdUM1NDhcXHVDMkVDXFx1RDU1OFxcdUMxMzhcXHVDNjk0LCBcXHVDMTM4XFx1QzE1OFxcdUM3NDAgXFx1Qzg4NVxcdUIyRTggXFx1QUMwNCBcXHVDNTU0XFx1RDYzOFxcdUQ2NTRcXHVCOTdDIFxcdUMwQUNcXHVDNkE5XFx1RDU1OFxcdUJCQzBcXHVCODVDIFxcdUIyRjlcXHVDMkUwXFx1Qzc1OCBcXHVDNzkxXFx1QzVDNVxcdUM3NDAgXFx1QkU0NFxcdUFDRjVcXHVBQzFDXFx1Qjg1QyBcXHVDNzIwXFx1QzlDMFxcdUI0MThcXHVCQTcwIFxcdUMxMUNcXHVCQzg0XFx1Qzg3MFxcdUNDMjhcXHVCM0M0IFxcdUM3OTFcXHVDNUM1IFxcdUIwQjRcXHVDNkE5XFx1Qzc0NCBcXHVDNTRDIFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgYnV0dG9uX3N0YXJ0U2Vzc2lvbjogXCJcXHVDMTM4XFx1QzE1OCBcXHVDMkRDXFx1Qzc5MVwiLFxuICBidXR0b25fc3RvcFNlc3Npb246IFwiXFx1QzEzOFxcdUMxNTggXFx1QzkxMVxcdUIyRThcIixcbiAgZGVzY19pblByb2dyZXNzSW50cm86IFwiXFx1QzJFNFxcdUMyRENcXHVBQzA0IFxcdUQ2MTFcXHVDNUM1IFxcdUMxMzhcXHVDMTU4XFx1Qzc3NCBcXHVDOUM0XFx1RDU4OSBcXHVDOTExXFx1Qzc4NVxcdUIyQzhcXHVCMkU0LlwiLFxuICBkZXNjX3NoYXJlTGluazogXCJcXHVBQ0Y1XFx1QjNEOSBcXHVDNzkxXFx1QzVDNVxcdUM3OTBcXHVDNUQwXFx1QUM4QyBcXHVDNzc0IFxcdUI5QzFcXHVEMDZDXFx1Qjk3QyBcXHVBQ0Y1XFx1QzcyMFxcdUQ1NThcXHVDMTM4XFx1QzY5NC5cIixcbiAgZGVzY19leGl0U2Vzc2lvbjogXCJcXHVDMTM4XFx1QzE1OFxcdUM3NDQgXFx1QzkxMVxcdUIyRThcXHVENTU4XFx1QkE3NCBcXHVDNUYwXFx1QUNCMFxcdUM3NDAgXFx1QjA0QVxcdUM1QjRcXHVDOUMwXFx1QjA5OCBcXHVDNzkxXFx1QzVDNVxcdUM3NDQgXFx1Qzc3NFxcdUM1QjRcXHVBQzA4IFxcdUMyMTggXFx1Qzc4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gXFx1Qzc3NCBcXHVDNzkxXFx1QzVDNVxcdUM3NDAgXFx1QjJFNFxcdUI5NzggXFx1Qzc5MVxcdUM1QzVcXHVDNzkwXFx1QzVEMFxcdUFDOEMgXFx1QzYwMVxcdUQ1QTVcXHVDNzQ0IFxcdUJCRjhcXHVDRTU4XFx1QzlDMCBcXHVDNTRBXFx1QzczQ1xcdUJBNzAgXFx1QUMwMVxcdUM3OTBcXHVDNzU4IFxcdUFDRjVcXHVCM0Q5IFxcdUM3OTFcXHVDNUM1XFx1Qzc0MCBcXHVBQ0M0XFx1QzE4RCBcXHVDNzIwXFx1QzlDMFxcdUI0MjlcXHVCMkM4XFx1QjJFNC5cIixcbiAgc2hhcmVUaXRsZTogXCJFeGNhbGlkcmF3XFx1Qzc1OCBcXHVDMkU0XFx1QzJEQ1xcdUFDMDQgXFx1RDYxMVxcdUM1QzUgXFx1QzEzOFxcdUMxNThcXHVDNUQwIFxcdUNDMzhcXHVBQzAwXFx1RDU1OFxcdUFFMzBcIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiXFx1QzYyNFxcdUI5NThcIlxufTtcbnZhciBleHBvcnREaWFsb2cgPSB7XG4gIGRpc2tfdGl0bGU6IFwiXFx1QjUxNFxcdUMyQTRcXHVEMDZDXFx1QzVEMCBcXHVDODAwXFx1QzdBNVwiLFxuICBkaXNrX2RldGFpbHM6IFwiXFx1QjA5OFxcdUM5MTFcXHVDNUQwIFxcdUIyRTRcXHVDMkRDIFxcdUJEODhcXHVCN0VDXFx1QzYyQyBcXHVDMjE4IFxcdUM3ODhcXHVCM0M0XFx1Qjg1RCBcXHVENjU0XFx1QkE3NCBcXHVCMzcwXFx1Qzc3NFxcdUQxMzBcXHVCOTdDIFxcdUIwQjRcXHVCQ0Y0XFx1QjBDNVxcdUIyQzhcXHVCMkU0LlwiLFxuICBkaXNrX2J1dHRvbjogXCJcXHVEMzBDXFx1Qzc3Q1xcdUI4NUMgXFx1QzgwMFxcdUM3QTVcIixcbiAgbGlua190aXRsZTogXCJcXHVBQ0Y1XFx1QzcyMCBcXHVBQzAwXFx1QjJBNVxcdUQ1NUMgXFx1QjlDMVxcdUQwNkMgXFx1QzBERFxcdUMxMzFcIixcbiAgbGlua19kZXRhaWxzOiBcIlxcdUM3N0RcXHVBRTMwIFxcdUM4MDRcXHVDNkE5IFxcdUI5QzFcXHVEMDZDXFx1Qjg1QyBcXHVCMEI0XFx1QkNGNFxcdUIwQzVcXHVCMkM4XFx1QjJFNC5cIixcbiAgbGlua19idXR0b246IFwiXFx1QjlDMVxcdUQwNkNcXHVCODVDIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCNFxcdUFFMzBcIixcbiAgZXhjYWxpZHJhd3BsdXNfZGVzY3JpcHRpb246IFwiXFx1RDY1NFxcdUJBNzRcXHVDNzQ0IFxcdUIyRjlcXHVDMkUwXFx1Qzc1OCBFeGNhbGlkcmF3KyBcXHVDNzkxXFx1QzVDNSBcXHVBQ0Y1XFx1QUMwNFxcdUM3M0NcXHVCODVDIFxcdUM4MDBcXHVDN0E1XFx1RDU2OVxcdUIyQzhcXHVCMkU0LlwiLFxuICBleGNhbGlkcmF3cGx1c19idXR0b246IFwiXFx1QjBCNFxcdUJDRjRcXHVCMEI0XFx1QUUzMFwiLFxuICBleGNhbGlkcmF3cGx1c19leHBvcnRFcnJvcjogXCJcXHVDOUMwXFx1QUUwOFxcdUM3NDAgRXhjYWxpZHJhdytcXHVCODVDIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCQyBcXHVDMjE4IFxcdUM1QzZcXHVDMkI1XFx1QjJDOFxcdUIyRTQuLi5cIlxufTtcbnZhciBoZWxwRGlhbG9nID0ge1xuICBibG9nOiBcIlxcdUJFMTRcXHVCODVDXFx1QURGOCBcXHVDNzdEXFx1QzVCNFxcdUJDRjRcXHVBRTMwXCIsXG4gIGNsaWNrOiBcIlxcdUQwNzRcXHVCOUFEXCIsXG4gIGRlZXBTZWxlY3Q6IFwiXFx1QUU0QVxcdUFDOEMgXFx1QzEyMFxcdUQwRERcIixcbiAgZGVlcEJveFNlbGVjdDogXCJcXHVDNjAxXFx1QzVFRFxcdUM3NDQgXFx1QUU0QVxcdUFDOEMgXFx1QzEyMFxcdUQwRERcXHVENTU4XFx1QUNFMCwgXFx1QjREQ1xcdUI3OThcXHVBREY4XFx1RDU1OFxcdUM5QzAgXFx1QzU0QVxcdUIzQzRcXHVCODVEIFxcdUQ1NThcXHVBRTMwXCIsXG4gIGN1cnZlZEFycm93OiBcIlxcdUFDRTFcXHVDMTIwIFxcdUQ2NTRcXHVDMEI0XFx1RDQ1Q1wiLFxuICBjdXJ2ZWRMaW5lOiBcIlxcdUFDRTFcXHVDMTIwXCIsXG4gIGRvY3VtZW50YXRpb246IFwiXFx1QzEyNFxcdUJBODVcXHVDMTFDXCIsXG4gIGRvdWJsZUNsaWNrOiBcIlxcdUIzNTRcXHVCRTE0IFxcdUQwNzRcXHVCOUFEXCIsXG4gIGRyYWc6IFwiXFx1QjREQ1xcdUI3OThcXHVBREY4XCIsXG4gIGVkaXRvcjogXCJcXHVDNUQwXFx1QjUxNFxcdUQxMzBcIixcbiAgZWRpdExpbmVBcnJvd1BvaW50czogXCJcXHVDOUMxXFx1QzEyMCAvIFxcdUQ2NTRcXHVDMEI0XFx1RDQ1QyBcXHVBRjJEXFx1QzlEM1xcdUM4MTAgXFx1QzIxOFxcdUM4MTVcIixcbiAgZWRpdFRleHQ6IFwiXFx1RDE0RFxcdUMyQTRcXHVEMkI4IFxcdUMyMThcXHVDODE1IC8gXFx1Qjc3Q1xcdUJDQTggXFx1Q0Q5NFxcdUFDMDBcIixcbiAgZ2l0aHViOiBcIlxcdUJCMzhcXHVDODFDIFxcdUM4MUNcXHVCQ0Y0XFx1RDU1OFxcdUFFMzBcIixcbiAgaG93dG86IFwiXFx1QUMwMFxcdUM3NzRcXHVCNERDIFxcdUNDMzhcXHVBQ0UwXFx1RDU1OFxcdUFFMzBcIixcbiAgb3I6IFwiXFx1QjYxMFxcdUIyOTRcIixcbiAgcHJldmVudEJpbmRpbmc6IFwiXFx1RDY1NFxcdUMwQjRcXHVENDVDXFx1QUMwMCBcXHVCRDk5XFx1QzlDMCBcXHVDNTRBXFx1QUM4QyBcXHVENTU4XFx1QUUzMFwiLFxuICB0b29sczogXCJcXHVCM0M0XFx1QUQ2Q1wiLFxuICBzaG9ydGN1dHM6IFwiXFx1RDBBNFxcdUJDRjRcXHVCNERDIFxcdUIyRThcXHVDRDk1XFx1RDBBNFwiLFxuICB0ZXh0RmluaXNoOiBcIlxcdUQzQjhcXHVDOUQxIFxcdUM2NDRcXHVCOENDIChcXHVEMTREXFx1QzJBNFxcdUQyQjggXFx1QzVEMFxcdUI1MTRcXHVEMTMwKVwiLFxuICB0ZXh0TmV3TGluZTogXCJcXHVDOTA0XFx1QkMxNFxcdUFGQzgoXFx1RDE0RFxcdUMyQTRcXHVEMkI4IFxcdUM1RDBcXHVCNTE0XFx1RDEzMClcIixcbiAgdGl0bGU6IFwiXFx1QjNDNFxcdUM2QzBcXHVCOUQwXCIsXG4gIHZpZXc6IFwiXFx1QkNGNFxcdUFFMzBcIixcbiAgem9vbVRvRml0OiBcIlxcdUJBQThcXHVCNEUwIFxcdUM2OTRcXHVDMThDXFx1QUMwMCBcXHVCQ0Y0XFx1Qzc3NFxcdUIzQzRcXHVCODVEIFxcdUQ2NTVcXHVCMzAwL1xcdUNEOTVcXHVDMThDXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJcXHVDMTIwXFx1RDBERCBcXHVDNjAxXFx1QzVFRFxcdUM3M0NcXHVCODVDIFxcdUQ2NTVcXHVCMzAwL1xcdUNEOTVcXHVDMThDXCIsXG4gIHRvZ2dsZUVsZW1lbnRMb2NrOiBcIlxcdUMxMjBcXHVEMEREXFx1RDU1QyBcXHVENTZEXFx1QkFBOVxcdUM3NDQgXFx1QzdBMFxcdUFFMDgvXFx1QzdBMFxcdUFFMDggXFx1RDU3NFxcdUM4MUNcIixcbiAgbW92ZVBhZ2VVcERvd246IFwiXFx1RDM5OFxcdUM3NzRcXHVDOUMwIFxcdUM2QzBcXHVDOUMxXFx1Qzc3NFxcdUFFMzAgXFx1QzcwNC9cXHVDNTQ0XFx1Qjc5OFwiLFxuICBtb3ZlUGFnZUxlZnRSaWdodDogXCJcXHVEMzk4XFx1Qzc3NFxcdUM5QzAgXFx1QzZDMFxcdUM5QzFcXHVDNzc0XFx1QUUzMCBcXHVDODhDL1xcdUM2QjBcIlxufTtcbnZhciBjbGVhckNhbnZhc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0IFxcdUM5QzBcXHVDNkIwXFx1QUUzMFwiXG59O1xudmFyIHB1Ymxpc2hEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDIFxcdUFDOENcXHVDMkRDXFx1RDU1OFxcdUFFMzBcIixcbiAgaXRlbU5hbWU6IFwiXFx1QzU0NFxcdUM3NzRcXHVEMTVDIFxcdUM3NzRcXHVCOTg0XCIsXG4gIGF1dGhvck5hbWU6IFwiXFx1QzgwMFxcdUM3OTBcXHVCQTg1XCIsXG4gIGdpdGh1YlVzZXJuYW1lOiBcIlxcdUFFNDNcXHVENUM4XFx1QkUwQyBcXHVDMEFDXFx1QzZBOVxcdUM3OTBcXHVDNzc0XFx1Qjk4NFwiLFxuICB0d2l0dGVyVXNlcm5hbWU6IFwiXFx1RDJCOFxcdUM3MDRcXHVEMTMwIFxcdUMwQUNcXHVDNkE5XFx1Qzc5MFxcdUM3NzRcXHVCOTg0XCIsXG4gIGxpYnJhcnlOYW1lOiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDIFxcdUM3NzRcXHVCOTg0XCIsXG4gIGxpYnJhcnlEZXNjOiBcIlxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDIFxcdUMxMjRcXHVCQTg1XCIsXG4gIHdlYnNpdGU6IFwiXFx1QzZGOVxcdUMwQUNcXHVDNzc0XFx1RDJCOFwiLFxuICBwbGFjZWhvbGRlcjoge1xuICAgIGF1dGhvck5hbWU6IFwiXFx1Qzc3NFxcdUI5ODQgXFx1QjYxMFxcdUIyOTQgXFx1QzBBQ1xcdUM2QTlcXHVDNzkwXFx1QkE4NVwiLFxuICAgIGxpYnJhcnlOYW1lOiBcIlxcdUIyRjlcXHVDMkUwXFx1Qzc1OCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQyBcXHVDNzc0XFx1Qjk4NFwiLFxuICAgIGxpYnJhcnlEZXNjOiBcIlxcdUMwQUNcXHVCNzhDXFx1QjRFNFxcdUM1RDBcXHVBQzhDIFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1Qzc1OCBcXHVDNkE5XFx1QjNDNFxcdUI5N0MgXFx1QzU0Q1xcdUFFMzAgXFx1QzI3RFxcdUFDOEMgXFx1QzEyNFxcdUJBODVcXHVENTc0XFx1QzhGQ1xcdUMxMzhcXHVDNjk0XCIsXG4gICAgZ2l0aHViSGFuZGxlOiBcIkdpdEh1YiBcXHVDMEFDXFx1QzZBOVxcdUM3OTBcXHVCQTg1IChcXHVDMTIwXFx1RDBERCksIFxcdUM4MUNcXHVDRDlDXFx1RDU1QyBcXHVCNEE0XFx1QzVEMFxcdUIzQzQgXFx1QzJFQ1xcdUMwQUNcXHVCOTdDIFxcdUM3MDRcXHVENTc0XFx1QzExQyBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUI5N0MgXFx1QzIxOFxcdUM4MTVcXHVENTYwIFxcdUI1NEMgXFx1QzBBQ1xcdUM2QTlcXHVCNDI5XFx1QjJDOFxcdUIyRTRcIixcbiAgICB0d2l0dGVySGFuZGxlOiBcIlR3aXR0ZXIgXFx1QzBBQ1xcdUM2QTlcXHVDNzkwXFx1QkE4NSAoXFx1QzEyMFxcdUQwREQpLCBUd2l0dGVyXFx1Qjk3QyBcXHVEMUI1XFx1RDU3NFxcdUMxMUMgXFx1RDY0RFxcdUJDRjRcXHVENTYwIFxcdUI1NEMgXFx1QzgxQ1xcdUM3OTFcXHVDNzkwXFx1Qjk3QyBcXHVCQzFEXFx1RDc4OFxcdUFFMzAgXFx1QzcwNFxcdUQ1NzQgXFx1QzBBQ1xcdUM2QTlcXHVCNDI5XFx1QjJDOFxcdUIyRTRcIixcbiAgICB3ZWJzaXRlOiBcIlxcdUFDMUNcXHVDNzc4IFxcdUM2RjlcXHVDMEFDXFx1Qzc3NFxcdUQyQjhcXHVCMDk4IFxcdUIyRTRcXHVCOTc4IFxcdUM1QjRcXHVCNTE4XFx1QUMwMFxcdUM3NTggXFx1QjlDMVxcdUQwNkMgKFxcdUMxMjBcXHVEMEREKVwiXG4gIH0sXG4gIGVycm9yczoge1xuICAgIHJlcXVpcmVkOiBcIlxcdUQ1NDRcXHVDMjE4XFx1QzBBQ1xcdUQ1NkRcIixcbiAgICB3ZWJzaXRlOiBcIlxcdUM3MjBcXHVENkE4XFx1RDU1QyBVUkxcXHVDNzQ0IFxcdUM3ODVcXHVCODI1XFx1RDU1OFxcdUMxMzhcXHVDNjk0XCJcbiAgfSxcbiAgbm90ZURlc2NyaXB0aW9uOiBcIlxcdUIyRjlcXHVDMkUwXFx1Qzc1OCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUI5N0MgXFx1QzgxQ1xcdUNEOUNcXHVENTU4XFx1QzVFQyA8bGluaz5cXHVBQ0Y1XFx1QUMxQyBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQyBcXHVDODAwXFx1QzdBNVxcdUMxOEM8L2xpbms+XFx1QzVEMFxcdUMxMUMgXFx1QjJFNFxcdUI5NzggXFx1QzBBQ1xcdUI3OENcXHVCNEU0XFx1Qzc1OCBcXHVBREY4XFx1QjlCQ1xcdUM1RDAgXFx1QzBBQ1xcdUM2QTlcXHVENTYwIFxcdUMyMTggXFx1Qzc4OFxcdUIzQzRcXHVCODVEIFxcdUQ1NThcXHVDMTM4XFx1QzY5NC5cIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiXFx1Qjc3Q1xcdUM3NzRcXHVCRTBDXFx1QjdFQ1xcdUI5QUNcXHVCMjk0IFxcdUJBM0NcXHVDODAwIFxcdUMyMThcXHVCM0Q5XFx1QzczQ1xcdUI4NUMgXFx1QzJCOVxcdUM3NzhcXHVCNDE4XFx1QzVCNFxcdUM1N0MgXFx1RDU2OVxcdUIyQzhcXHVCMkU0LiBcXHVDODFDXFx1Q0Q5Q1xcdUQ1NThcXHVBRTMwIFxcdUM4MDRcXHVDNUQwIDxsaW5rPlxcdUFDMDBcXHVDNzc0XFx1QjREQ1xcdUI3N0NcXHVDNzc4PC9saW5rPlxcdUM3NDQgXFx1QkEzQ1xcdUM4MDAgXFx1Qzc3RFxcdUM1QjRcXHVCQ0Y0XFx1QzEzOFxcdUM2OTQuIFxcdUM3NThcXHVBQ0FDXFx1Qzc0NCBcXHVBQ0Y1XFx1QzcyMFxcdUQ1NThcXHVBQzcwXFx1QjA5OCBcXHVCQ0MwXFx1QUNCRFxcdUMwQUNcXHVENTZEXFx1Qzc0NCBcXHVCOUNDXFx1QjRFNFxcdUFFMzAgXFx1QzcwNFxcdUQ1NzRcXHVDMTIwIEdpdEh1YiBcXHVBQ0M0XFx1QzgxNVxcdUM3NzQgXFx1RDU0NFxcdUM2OTRcXHVENTU4XFx1QzlDMFxcdUI5Q0MsIFxcdUJDMThcXHVCNERDXFx1QzJEQyBcXHVENTQ0XFx1QzY5NFxcdUQ1NThcXHVDOUM0IFxcdUM1NEFcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCIsXG4gIG5vdGVMaWNlbnNlOiBcIlxcdUM4MUNcXHVDRDlDXFx1RDU2OFxcdUM3M0NcXHVCODVDXFx1QzM2OCwgXFx1QjJGOVxcdUMyRTBcXHVDNzQwIFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1QUMwMCA8bGluaz5NSVQgXFx1Qjc3Q1xcdUM3NzRcXHVDMTIwXFx1QzJBNCA8L2xpbms+XFx1RDU1OFxcdUM1RDAgXFx1QkMzMFxcdUQzRUNcXHVCNDI4XFx1Qzc0NCwgXFx1Qzk4OSBcXHVDNTQ0XFx1QkIzNFxcdUIwOTggXFx1QzgxQ1xcdUM1N0QgXFx1QzVDNlxcdUM3NzQgXFx1QzBBQ1xcdUM2QTlcXHVENTYwIFxcdUMyMTggXFx1Qzc4OFxcdUM3NENcXHVDNUQwIFxcdUIzRDlcXHVDNzU4XFx1RDU2OVxcdUIyQzhcXHVCMkU0LlwiLFxuICBub3RlSXRlbXM6IFwiXFx1QUMwMVxcdUFDMDFcXHVDNzU4IFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1QjI5NCBcXHVCRDg0XFx1Qjk1OFxcdUQ1NjAgXFx1QzIxOCBcXHVDNzg4XFx1QjNDNFxcdUI4NUQgXFx1QUNFMFxcdUM3MjBcXHVENTVDIFxcdUM3NzRcXHVCOTg0XFx1Qzc0NCBcXHVBQzAwXFx1QzgzOFxcdUM1N0MgXFx1RDU2OVxcdUIyQzhcXHVCMkU0LiBcXHVCMkU0XFx1Qzc0Q1xcdUM3NTggXFx1Qjc3Q1xcdUM3NzRcXHVCRTBDXFx1QjdFQ1xcdUI5QUMgXFx1RDU2RFxcdUJBQTlcXHVDNzc0IFxcdUQzRUNcXHVENTY4XFx1QjQyOVxcdUIyQzhcXHVCMkU0OlwiLFxuICBhdGxlYXN0T25lTGliSXRlbTogXCJcXHVDRDVDXFx1QzE4Q1xcdUQ1NUMgXFx1RDU1OFxcdUIwOThcXHVDNzU4IFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1Qjk3QyBcXHVDMTIwXFx1RDBERFxcdUQ1NzRcXHVDOEZDXFx1QzEzOFxcdUM2OTRcIixcbiAgcmVwdWJsaXNoV2FybmluZzogXCJcXHVDQzM4XFx1QUNFMDogXFx1QzEyMFxcdUQwRERcXHVCNDFDIFxcdUQ1NkRcXHVCQUE5XFx1Qzc1OCBcXHVDNzdDXFx1QkQ4MFxcdUIyOTQgXFx1Qzc3NFxcdUJCRjggXFx1QzgxQ1xcdUNEOUMvXFx1QUM4Q1xcdUMyRENcXHVCNDE4XFx1QzVDOFxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gXFx1QUUzMFxcdUM4NzRcXHVDNzU4IFxcdUI3N0NcXHVDNzc0XFx1QkUwQ1xcdUI3RUNcXHVCOUFDXFx1QjA5OCBcXHVDODFDXFx1Q0Q5Q1xcdUJCM0NcXHVDNzQ0IFxcdUM1QzVcXHVCMzcwXFx1Qzc3NFxcdUQyQjhcXHVENTU4XFx1QjI5NCBcXHVBQ0JEXFx1QzZCMFxcdUM1RDBcXHVCOUNDIFxcdUM4MUNcXHVDRDlDXFx1RDU1OFxcdUMxMzhcXHVDNjk0LlwiXG59O1xudmFyIHB1Ymxpc2hTdWNjZXNzRGlhbG9nID0ge1xuICB0aXRsZTogXCJcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQyBcXHVDODFDXFx1Q0Q5Q1xcdUI0MjhcIixcbiAgY29udGVudDogXCJ7e2F1dGhvck5hbWV9fVxcdUIyRDggXFx1QUMxMFxcdUMwQUNcXHVENTY5XFx1QjJDOFxcdUIyRTQuIFxcdUIyRjlcXHVDMkUwXFx1Qzc1OCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUFDMDAgXFx1QzJFQ1xcdUMwQUNcXHVCOTdDIFxcdUM3MDRcXHVENTc0IFxcdUM4MUNcXHVDRDlDXFx1QjQxOFxcdUM1QzhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuIFxcdUM5QzRcXHVENTg5IFxcdUMwQzFcXHVENjY5XFx1Qzc0NDxsaW5rPlxcdUM1RUNcXHVBRTMwXFx1QzVEMFxcdUMxMUMgXFx1RDY1NVxcdUM3NzhcXHVENTU4XFx1QzJFNCBcXHVDMjE4IFxcdUM3ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuPC9saW5rPlwiXG59O1xudmFyIGNvbmZpcm1EaWFsb2cgPSB7XG4gIHJlc2V0TGlicmFyeTogXCJcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQyBcXHVCOUFDXFx1QzE0QlwiLFxuICByZW1vdmVJdGVtc0Zyb21MaWI6IFwiXFx1QzEyMFxcdUQwRERcXHVENTVDIFxcdUQ1NkRcXHVCQUE5XFx1Qzc0NCBcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUM1RDBcXHVDMTFDIFxcdUM4MUNcXHVBQzcwXCJcbn07XG52YXIgaW1hZ2VFeHBvcnREaWFsb2cgPSB7XG4gIGhlYWRlcjogXCJcXHVDNzc0XFx1QkJGOFxcdUM5QzAgXFx1QjBCNFxcdUJDRjRcXHVCMEI0XFx1QUUzMFwiLFxuICBsYWJlbDoge1xuICAgIHdpdGhCYWNrZ3JvdW5kOiBcIlxcdUJDMzBcXHVBQ0JEXCIsXG4gICAgb25seVNlbGVjdGVkOiBcIlxcdUMxMjBcXHVEMEREXFx1RDU1QyBcXHVENTZEXFx1QkFBOVxcdUI5Q0NcIixcbiAgICBkYXJrTW9kZTogXCJcXHVCMkU0XFx1RDA2QyBcXHVCQUE4XFx1QjREQ1wiLFxuICAgIGVtYmVkU2NlbmU6IFwiXFx1RDY1NFxcdUJBNzRcXHVDNzQ0IFxcdUIyRjRcXHVBRTMwXCIsXG4gICAgc2NhbGU6IFwiXFx1RDA2Q1xcdUFFMzBcIixcbiAgICBwYWRkaW5nOiBcIlxcdUM1RUNcXHVCQzMxXCJcbiAgfSxcbiAgdG9vbHRpcDoge1xuICAgIGVtYmVkU2NlbmU6IFwiXFx1RDY1NFxcdUJBNzQgXFx1QzgxNVxcdUJDRjRcXHVBQzAwIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCNFxcdUIyOTQgUE5HL1NWRyBcXHVEMzBDXFx1Qzc3Q1xcdUM1RDAgXFx1QzgwMFxcdUM3QTVcXHVCNDE4XFx1QzVCNCBcXHVDNzc0XFx1RDZDNFxcdUM1RDAgXFx1RDMwQ1xcdUM3N0NcXHVDNUQwXFx1QzExQyBcXHVENjU0XFx1QkE3NFxcdUM3NDQgXFx1QkNGNVxcdUFENkNcXHVENTYwIFxcdUMyMTggXFx1Qzc4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gXFx1RDMwQ1xcdUM3N0MgXFx1RDA2Q1xcdUFFMzBcXHVBQzAwIFxcdUM5OURcXHVBQzAwXFx1RDU2OVxcdUIyQzhcXHVCMkU0LlwiXG4gIH0sXG4gIHRpdGxlOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiUE5HXFx1Qjg1QyBcXHVCMEI0XFx1QkNGNFxcdUIwQjRcXHVBRTMwXCIsXG4gICAgZXhwb3J0VG9Tdmc6IFwiU1ZHXFx1Qjg1QyBcXHVCMEI0XFx1QkNGNFxcdUIwQjRcXHVBRTMwXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIlxcdUQwNzRcXHVCOUJEXFx1QkNGNFxcdUI0RENcXHVCODVDIFBORyBcXHVCQ0Y1XFx1QzBBQ1wiXG4gIH0sXG4gIGJ1dHRvbjoge1xuICAgIGV4cG9ydFRvUG5nOiBcIlBOR1wiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIlNWR1wiLFxuICAgIGNvcHlQbmdUb0NsaXBib2FyZDogXCJcXHVEMDc0XFx1QjlCRFxcdUJDRjRcXHVCNERDXFx1Qjg1QyBcXHVCQ0Y1XFx1QzBBQ1wiXG4gIH1cbn07XG52YXIgZW5jcnlwdGVkID0ge1xuICB0b29sdGlwOiBcIlxcdUFERjhcXHVCOUJDXFx1Qzc0MCBcXHVDODg1XFx1QjJFOCBcXHVBQzA0IFxcdUM1NTRcXHVENjM4XFx1RDY1NFxcdUI0MThcXHVCQkMwXFx1Qjg1QyBFeGNhbGlkcmF3XFx1Qzc1OCBcXHVDMTFDXFx1QkM4NFxcdUIyOTQgXFx1QzgwOFxcdUIzMDBcXHVCODVDIFxcdUIwQjRcXHVDNkE5XFx1Qzc0NCBcXHVDNTRDIFxcdUMyMTggXFx1QzVDNlxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgbGluazogXCJFeGNhbGlkcmF3XFx1Qzc1OCBcXHVDODg1XFx1QjJFOCBcXHVBQzA0IFxcdUM1NTRcXHVENjM4XFx1RDY1NFxcdUM1RDAgXFx1QjMwMFxcdUQ1NUMgXFx1QkUxNFxcdUI4NUNcXHVBREY4IFxcdUQzRUNcXHVDMkE0XFx1RDJCOFwiXG59O1xudmFyIHN0YXRzID0ge1xuICBhbmdsZTogXCJcXHVBQzAxXFx1QjNDNFwiLFxuICBlbGVtZW50OiBcIlxcdUM2OTRcXHVDMThDXCIsXG4gIGVsZW1lbnRzOiBcIlxcdUM2OTRcXHVDMThDXCIsXG4gIGhlaWdodDogXCJcXHVCMTkyXFx1Qzc3NFwiLFxuICBzY2VuZTogXCJcXHVENjU0XFx1QkE3NFwiLFxuICBzZWxlY3RlZDogXCJcXHVDMTIwXFx1RDBERFxcdUI0MjhcIixcbiAgc3RvcmFnZTogXCJcXHVDODAwXFx1QzdBNVxcdUFDRjVcXHVBQzA0XCIsXG4gIHRpdGxlOiBcIlxcdUIzNTVcXHVENkM0XFx1QjRFNFxcdUM3NDQgXFx1QzcwNFxcdUQ1NUMgXFx1RDFCNVxcdUFDQzRcIixcbiAgdG90YWw6IFwiXFx1RDU2OVxcdUFDQzRcIixcbiAgdmVyc2lvbjogXCJcXHVCQzg0XFx1QzgwNFwiLFxuICB2ZXJzaW9uQ29weTogXCJcXHVCQ0Y1XFx1QzBBQ1xcdUQ1NThcXHVCODI0XFx1QkE3NCBcXHVEMDc0XFx1QjlBRFwiLFxuICB2ZXJzaW9uTm90QXZhaWxhYmxlOiBcIlxcdUQ1NzRcXHVCMkY5IFxcdUJDODRcXHVDODA0IFxcdUMwQUNcXHVDNkE5IFxcdUJEODhcXHVBQzAwXFx1QjJBNVwiLFxuICB3aWR0aDogXCJcXHVCMTA4XFx1QkU0NFwiXG59O1xudmFyIHRvYXN0ID0ge1xuICBhZGRlZFRvTGlicmFyeTogXCJcXHVCNzdDXFx1Qzc3NFxcdUJFMENcXHVCN0VDXFx1QjlBQ1xcdUM1RDAgXFx1Q0Q5NFxcdUFDMDBcXHVCNDE4XFx1QzVDOFxcdUMyQjVcXHVCMkM4XFx1QjJFNFwiLFxuICBjb3B5U3R5bGVzOiBcIlxcdUMyQTRcXHVEMEMwXFx1Qzc3QyBcXHVCQ0Y1XFx1QzBBQy5cIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIlxcdUQwNzRcXHVCOUJEXFx1QkNGNFxcdUI0RENcXHVCODVDIFxcdUJDRjVcXHVDMEFDLlwiLFxuICBjb3B5VG9DbGlwYm9hcmRBc1BuZzogXCJ7e2V4cG9ydFNlbGVjdGlvbn19XFx1Qjk3QyBcXHVEMDc0XFx1QjlCRFxcdUJDRjRcXHVCNERDXFx1QzVEMCBQTkdcXHVCODVDIFxcdUJDRjVcXHVDMEFDXFx1RDU4OFxcdUMyQjVcXHVCMkM4XFx1QjJFNFxcbih7e2V4cG9ydENvbG9yU2NoZW1lfX0pXCIsXG4gIGZpbGVTYXZlZDogXCJcXHVEMzBDXFx1Qzc3Q1xcdUM3NzQgXFx1QzgwMFxcdUM3QTVcXHVCNDE4XFx1QzVDOFxcdUMyQjVcXHVCMkM4XFx1QjJFNC5cIixcbiAgZmlsZVNhdmVkVG9GaWxlbmFtZTogXCJ7ZmlsZW5hbWV9IFxcdUI4NUMgXFx1QzgwMFxcdUM3QTVcXHVCNDE4XFx1QzVDOFxcdUMyQjVcXHVCMkM4XFx1QjJFNFwiLFxuICBjYW52YXM6IFwiXFx1Q0U5NFxcdUJDODRcXHVDMkE0XCIsXG4gIHNlbGVjdGlvbjogXCJcXHVDMTIwXFx1RDBERFxcdUQ1NUMgXFx1QzY5NFxcdUMxOENcIixcbiAgcGFzdGVBc1NpbmdsZUVsZW1lbnQ6IFwiXFx1QjJFOFxcdUM3N0MgXFx1QzY5NFxcdUMxOENcXHVCODVDIFxcdUJEOTlcXHVDNUVDXFx1QjEyM1xcdUFDNzBcXHVCMDk4LCBcXHVBRTMwXFx1Qzg3NCBcXHVEMTREXFx1QzJBNFxcdUQyQjggXFx1QzVEMFxcdUI1MTRcXHVEMTMwXFx1QzVEMCBcXHVCRDk5XFx1QzVFQ1xcdUIxMjNcXHVDNzNDXFx1QjgyNFxcdUJBNzQge3tzaG9ydGN1dH19IFxcdUM3NDQgXFx1QzBBQ1xcdUM2QTlcXHVENTU4XFx1QzEzOFxcdUM2OTQuXCIsXG4gIHVuYWJsZVRvRW1iZWQ6IFwiXFx1Qzc3NCBVUkxcXHVDNzU4IFxcdUM3ODRcXHVCQ0EwXFx1QjUyOVxcdUM3NzQgXFx1RDVDOFxcdUM2QTlcXHVCNDE4XFx1QzlDMCBcXHVDNTRBXFx1QzU1OFxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gR2l0SHViXFx1QzVEMCBcXHVDNzc0XFx1QzI4OFxcdUI5N0MgXFx1QjBBOFxcdUFDQThcXHVDMTFDIFxcdUM3NzQgVVJMXFx1Qzc3NCBcXHVENjU0XFx1Qzc3NFxcdUQyQjhcXHVCOUFDXFx1QzJBNFxcdUQyQjhcXHVDNUQwIFxcdUI0RjFcXHVDN0FDXFx1QjQyMCBcXHVDMjE4IFxcdUM3ODhcXHVCM0M0XFx1Qjg1RCBcXHVDNjk0XFx1Q0NBRFxcdUQ1NThcXHVDMTM4XFx1QzY5NFwiLFxuICB1bnJlY29nbml6ZWRMaW5rRm9ybWF0OiAnXFx1Qzc4NFxcdUJDQTBcXHVCNTI5XFx1RDU1OFxcdUI4MjRcXHVCMjk0IFxcdUI5QzFcXHVEMDZDXFx1Qzc1OCBcXHVENjE1XFx1QzJERFxcdUM3NzQgXFx1Qzc5OFxcdUJBQkJcXHVCNDFDIFxcdUFDODMgXFx1QUMxOVxcdUMyQjVcXHVCMkM4XFx1QjJFNC4gXFx1QzZEMFxcdUJDRjggXFx1QzBBQ1xcdUM3NzRcXHVEMkI4XFx1QzVEMFxcdUMxMUMgXFx1QzgxQ1xcdUFDRjVcXHVENTU4XFx1QjI5NCBcIlxcdUM3ODRcXHVCQ0EwXFx1QjUyOVwiIFxcdUQxNERcXHVDMkE0XFx1RDJCOFxcdUI5N0MgXFx1QURGOFxcdUIzMDBcXHVCODVDIFxcdUJEOTlcXHVDNUVDIFxcdUIxMjNcXHVDNUI0IFxcdUM4RkNcXHVDMTM4XFx1QzY5NCdcbn07XG52YXIgY29sb3JzID0ge1xuICB0cmFuc3BhcmVudDogXCJcXHVEMjJDXFx1QkE4NVwiLFxuICBibGFjazogXCJcXHVCRTE0XFx1Qjc5OVwiLFxuICB3aGl0ZTogXCJcXHVENjU0XFx1Qzc3NFxcdUQyQjhcIixcbiAgcmVkOiBcIlxcdUI4MDhcXHVCNERDXCIsXG4gIHBpbms6IFwiXFx1RDU1MVxcdUQwNkNcIixcbiAgZ3JhcGU6IFwiXFx1QURGOFxcdUI4MDhcXHVDNzc0XFx1RDUwNFwiLFxuICB2aW9sZXQ6IFwiXFx1QkMxNFxcdUM3NzRcXHVDNjJDXFx1QjgxQlwiLFxuICBncmF5OiBcIlxcdUFERjhcXHVCODA4XFx1Qzc3NFwiLFxuICBibHVlOiBcIlxcdUJFMTRcXHVCOEU4XCIsXG4gIGN5YW46IFwiXFx1QzJEQ1xcdUM1NDhcIixcbiAgdGVhbDogXCJcXHVEMkY4XCIsXG4gIGdyZWVuOiBcIlxcdUFERjhcXHVCOUIwXCIsXG4gIHllbGxvdzogXCJcXHVDNjEwXFx1Qjg1Q1xcdUM2QjBcIixcbiAgb3JhbmdlOiBcIlxcdUM2MjRcXHVCODBDXFx1QzlDMFwiLFxuICBicm9uemU6IFwiXFx1QkUwQ1xcdUI4NjBcXHVDOTg4XCJcbn07XG52YXIgd2VsY29tZVNjcmVlbiA9IHtcbiAgYXBwOiB7XG4gICAgY2VudGVyX2hlYWRpbmc6IFwiXFx1QkFBOFxcdUI0RTAgXFx1QjM3MFxcdUM3NzRcXHVEMTMwXFx1QjI5NCBcXHVCRTBDXFx1Qjc3Q1xcdUM2QjBcXHVDODAwXFx1QzVEMCBcXHVDNTQ4XFx1QzgwNFxcdUQ1NThcXHVBQzhDIFxcdUM4MDBcXHVDN0E1XFx1QjQyOVxcdUIyQzhcXHVCMkU0LlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nX3BsdXM6IFwiXFx1QjMwMFxcdUMyRTAgRXhjYWxpZHJhdytcXHVCODVDIFxcdUM3NzRcXHVCM0Q5XFx1RDU1OFxcdUMyRENcXHVBQ0EwXFx1QzJCNVxcdUIyQzhcXHVBRTRDP1wiLFxuICAgIG1lbnVIaW50OiBcIlxcdUIwQjRcXHVCQ0Y0XFx1QjBCNFxcdUFFMzAsIFxcdUMxMjRcXHVDODE1LCBcXHVDNUI4XFx1QzVCNCwgLi4uXCJcbiAgfSxcbiAgZGVmYXVsdHM6IHtcbiAgICBtZW51SGludDogXCJcXHVCMEI0XFx1QkNGNFxcdUIwQjRcXHVBRTMwLCBcXHVDMTI0XFx1QzgxNSwgXFx1QjRGMVxcdUI0RjEuLi5cIixcbiAgICBjZW50ZXJfaGVhZGluZzogXCJcXHVBQzA0XFx1QjJFOFxcdUQ1NThcXHVBQzhDIFxcdUI5Q0NcXHVCNERDXFx1QjI5NCBcXHVCMkU0XFx1Qzc3NFxcdUM1QjRcXHVBREY4XFx1QjdBOC5cIixcbiAgICB0b29sYmFySGludDogXCJcXHVCM0M0XFx1QUQ2Q1xcdUI5N0MgXFx1QzEyMFxcdUQwRERcXHVENTU4XFx1QUNFMCwgXFx1QURGOFxcdUI5QUNcXHVDMTM4XFx1QzY5NCFcIixcbiAgICBoZWxwSGludDogXCJcXHVCMkU4XFx1Q0Q5NVxcdUQwQTQgJiBcXHVCM0M0XFx1QzZDMFxcdUI5RDBcIlxuICB9XG59O1xudmFyIGNvbG9yUGlja2VyID0ge1xuICBtb3N0VXNlZEN1c3RvbUNvbG9yczogXCJcXHVBQzAwXFx1QzdBNSBcXHVCOUNFXFx1Qzc3NCBcXHVDMEFDXFx1QzZBOVxcdUI0MUMgXFx1QzBDOVxcdUMwQzFcXHVCNEU0XCIsXG4gIGNvbG9yczogXCJcXHVDMEM5XFx1QzBDMVwiLFxuICBzaGFkZXM6IFwiXFx1QzBDOVxcdUM4NzBcIixcbiAgaGV4Q29kZTogXCJIZXggXFx1Q0Y1NFxcdUI0RENcIixcbiAgbm9TaGFkZXM6IFwiXFx1QzBBQ1xcdUM2QTlcXHVENTYwIFxcdUMyMTggXFx1Qzc4OFxcdUIyOTQgXFx1QzBDOVxcdUM4NzBcXHVBQzAwIFxcdUM1QzZcXHVDNzRDXCJcbn07XG52YXIgb3ZlcndyaXRlQ29uZmlybSA9IHtcbiAgYWN0aW9uOiB7XG4gICAgZXhwb3J0VG9JbWFnZToge1xuICAgICAgdGl0bGU6IFwiXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1Qjg1QyBcXHVCMEI0XFx1QkNGNFxcdUIwQjRcXHVBRTMwXCIsXG4gICAgICBidXR0b246IFwiXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1Qjg1QyBcXHVCMEI0XFx1QkNGNFxcdUIwQjRcXHVBRTMwXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcXHVCMDk4XFx1QzkxMVxcdUM1RDAgXFx1QjJFNFxcdUMyREMgXFx1QkQ4OFxcdUI3RUNcXHVDNjJDIFxcdUMyMTggXFx1Qzc4OFxcdUIzQzRcXHVCODVEIFxcdUQ2NTRcXHVCQTc0IFxcdUIzNzBcXHVDNzc0XFx1RDEzMFxcdUI5N0MgXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1Qjg1QyBcXHVCMEI0XFx1QkNGNFxcdUIwQzVcXHVCMkM4XFx1QjJFNC5cIlxuICAgIH0sXG4gICAgc2F2ZVRvRGlzazoge1xuICAgICAgdGl0bGU6IFwiXFx1QjUxNFxcdUMyQTRcXHVEMDZDXFx1QzVEMCBcXHVDODAwXFx1QzdBNVwiLFxuICAgICAgYnV0dG9uOiBcIlxcdUI1MTRcXHVDMkE0XFx1RDA2Q1xcdUM1RDAgXFx1QzgwMFxcdUM3QTVcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlxcdUIwOThcXHVDOTExXFx1QzVEMCBcXHVCMkU0XFx1QzJEQyBcXHVCRDg4XFx1QjdFQ1xcdUM2MkMgXFx1QzIxOCBcXHVDNzg4XFx1QjNDNFxcdUI4NUQgXFx1RDY1NFxcdUJBNzQgXFx1QjM3MFxcdUM3NzRcXHVEMTMwXFx1Qjk3QyBcXHVCMEI0XFx1QkNGNFxcdUIwQzVcXHVCMkM4XFx1QjJFNC5cIlxuICAgIH0sXG4gICAgZXhjYWxpZHJhd1BsdXM6IHtcbiAgICAgIHRpdGxlOiBcIkV4Y2FsaWRyYXcrXCIsXG4gICAgICBidXR0b246IFwiRXhjYWxpZHJhdytcXHVCODVDIFxcdUIwQjRcXHVCQ0Y0XFx1QjBCNFxcdUFFMzBcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlxcdUQ2NTRcXHVCQTc0XFx1Qzc0NCBcXHVCMkY5XFx1QzJFMFxcdUM3NTggRXhjYWxpZHJhdysgXFx1Qzc5MVxcdUM1QzUgXFx1QUNGNVxcdUFDMDRcXHVDNzNDXFx1Qjg1QyBcXHVDODAwXFx1QzdBNVxcdUQ1NjlcXHVCMkM4XFx1QjJFNC5cIlxuICAgIH1cbiAgfSxcbiAgbW9kYWw6IHtcbiAgICBsb2FkRnJvbUZpbGU6IHtcbiAgICAgIHRpdGxlOiBcIlxcdUQzMENcXHVDNzdDXFx1QzVEMFxcdUMxMUMgXFx1QkQ4OFxcdUI3RUNcXHVDNjI0XFx1QUUzMFwiLFxuICAgICAgYnV0dG9uOiBcIlxcdUQzMENcXHVDNzdDXFx1QzVEMFxcdUMxMUMgXFx1QkQ4OFxcdUI3RUNcXHVDNjI0XFx1QUUzMFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXFx1RDMwQ1xcdUM3N0NcXHVDNzQ0IFxcdUJEODhcXHVCN0VDXFx1QzYyNFxcdUJBNzQgPGJvbGQ+XFx1RDYwNFxcdUM3QUMgXFx1Qzc5MVxcdUMxMzFcXHVCNDFDIFxcdUIzNzBcXHVDNzc0XFx1RDEzMFxcdUI5N0MgXFx1QjM2RVxcdUM1QjRcXHVDNEYwXFx1QUM4QyBcXHVCNDI5XFx1QjJDOFxcdUIyRTQ8L2JvbGQ+Ljxicj48L2JyPlxcdUIyRTRcXHVDNzRDIFxcdUM2MzVcXHVDMTU4IFxcdUM5MTEgXFx1RDU1OFxcdUIwOThcXHVCOTdDIFxcdUMxMjBcXHVEMEREXFx1RDU1OFxcdUM1RUMgXFx1Qzc5MVxcdUM1QzVcXHVCQjNDXFx1Qzc0NCBcXHVCQzMxXFx1QzVDNVxcdUQ1NzQgXFx1QjQ1OCBcXHVDMjE4IFxcdUM3ODhcXHVDMkI1XFx1QjJDOFxcdUIyRTQuXCJcbiAgICB9LFxuICAgIHNoYXJlYWJsZUxpbms6IHtcbiAgICAgIHRpdGxlOiBcIlxcdUM4RkNcXHVDMThDXFx1QzVEMFxcdUMxMUMgXFx1QkQ4OFxcdUI3RUNcXHVDNjI0XFx1QUUzMFwiLFxuICAgICAgYnV0dG9uOiBcIlxcdUNFRThcXHVEMTUwXFx1Q0UyMFxcdUI5N0MgXFx1QjM2RVxcdUM1QjRcXHVDNEYwXFx1QUUzMFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXFx1QzY3OFxcdUJEODAgXFx1Qzc5MVxcdUM1QzVcXHVCQjNDXFx1Qzc0NCBcXHVCRDg4XFx1QjdFQ1xcdUM2MjRcXHVCQTc0IDxib2xkPlxcdUQ2MDRcXHVDN0FDIFxcdUM3OTFcXHVDMTMxXFx1QjQxQyBcXHVCMzcwXFx1Qzc3NFxcdUQxMzBcXHVCOTdDIFxcdUIzNkVcXHVDNUI0XFx1QzRGMFxcdUFDOEMgXFx1QjQyOVxcdUIyQzhcXHVCMkU0PC9ib2xkPi48YnI+PC9icj5cXHVCMkU0XFx1Qzc0QyBcXHVDNjM1XFx1QzE1OCBcXHVDOTExIFxcdUQ1NThcXHVCMDk4XFx1Qjk3QyBcXHVDMTIwXFx1RDBERFxcdUQ1NThcXHVDNUVDIFxcdUM3OTFcXHVDNUM1XFx1QkIzQ1xcdUM3NDQgXFx1QkMzMVxcdUM1QzVcXHVENTc0IFxcdUI0NTggXFx1QzIxOCBcXHVDNzg4XFx1QzJCNVxcdUIyQzhcXHVCMkU0LlwiXG4gICAgfVxuICB9XG59O1xudmFyIG1lcm1haWQgPSB7XG4gIHRpdGxlOiBcIk1lcm1haWRcXHVDNUQwXFx1QzExQyBcXHVCRDg4XFx1QjdFQ1xcdUM2MjRcXHVBRTMwXCIsXG4gIGJ1dHRvbjogXCJcXHVDMEJEXFx1Qzc4NVxcdUQ1NThcXHVBRTMwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlxcdUM5QzBcXHVBRTA4XFx1Qzc0MCA8Zmxvd2NoYXJ0TGluaz5cXHVDMjFDXFx1QzExQ1xcdUIzQzQ8L2Zsb3djaGFydExpbms+LDxzZXF1ZW5jZUxpbms+IFxcdUMyRENcXHVEMDAwXFx1QzJBNDwvc2VxdWVuY2VMaW5rPiwgPGNsYXNzTGluaz5cXHVEMDc0XFx1Qjc5OFxcdUMyQTQgPC9jbGFzc0xpbms+XFx1QjJFNFxcdUM3NzRcXHVDNUI0XFx1QURGOFxcdUI3QThcXHVCOUNDIFxcdUM5QzBcXHVDNkQwXFx1RDU2OVxcdUIyQzhcXHVCMkU0LiBcXHVCMkU0XFx1Qjk3OCBcXHVENjE1XFx1QzJERFxcdUI0RTRcXHVDNzQwIEV4Y2FsaWRyYXdcXHVDNUQwXFx1QzExQ1xcdUIyOTQgXFx1Qzc3NFxcdUJCRjhcXHVDOUMwXFx1Qjg1QyBcXHVENDVDXFx1QzJEQ1xcdUI0MjlcXHVCMkM4XFx1QjJFNC5cIixcbiAgc3ludGF4OiBcIk1lcm1haWQgXFx1QUQ2Q1xcdUJCMzhcIixcbiAgcHJldmlldzogXCJcXHVCQkY4XFx1QjlBQ1xcdUJDRjRcXHVBRTMwXCJcbn07XG52YXIga29fS1JfZGVmYXVsdCA9IHtcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBidXR0b25zLFxuICBhbGVydHMsXG4gIGVycm9ycyxcbiAgdG9vbEJhcixcbiAgaGVhZGluZ3MsXG4gIGhpbnRzLFxuICBjYW52YXNFcnJvcixcbiAgZXJyb3JTcGxhc2gsXG4gIHJvb21EaWFsb2csXG4gIGVycm9yRGlhbG9nLFxuICBleHBvcnREaWFsb2csXG4gIGhlbHBEaWFsb2csXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgY29uZmlybURpYWxvZyxcbiAgaW1hZ2VFeHBvcnREaWFsb2csXG4gIGVuY3J5cHRlZCxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICBjb2xvcnMsXG4gIHdlbGNvbWVTY3JlZW4sXG4gIGNvbG9yUGlja2VyLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBtZXJtYWlkXG59O1xuZXhwb3J0IHtcbiAgYWxlcnRzLFxuICBidXR0b25zLFxuICBjYW52YXNFcnJvcixcbiAgY2xlYXJDYW52YXNEaWFsb2csXG4gIGNvbG9yUGlja2VyLFxuICBjb2xvcnMsXG4gIGNvbmZpcm1EaWFsb2csXG4gIGtvX0tSX2RlZmF1bHQgYXMgZGVmYXVsdCxcbiAgZW5jcnlwdGVkLFxuICBlcnJvckRpYWxvZyxcbiAgZXJyb3JTcGxhc2gsXG4gIGVycm9ycyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWFkaW5ncyxcbiAgaGVscERpYWxvZyxcbiAgaGludHMsXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBsYWJlbHMsXG4gIGxpYnJhcnksXG4gIG1lcm1haWQsXG4gIG92ZXJ3cml0ZUNvbmZpcm0sXG4gIHB1Ymxpc2hEaWFsb2csXG4gIHB1Ymxpc2hTdWNjZXNzRGlhbG9nLFxuICByb29tRGlhbG9nLFxuICBzdGF0cyxcbiAgdG9hc3QsXG4gIHRvb2xCYXIsXG4gIHdlbGNvbWVTY3JlZW5cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1rby1LUi1SUVgzN1NORi5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/ko-KR-RQX37SNF.js\n"));

/***/ })

}]);