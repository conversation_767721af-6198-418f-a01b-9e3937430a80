"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_bg-BG-AAABLFCY_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/bg-BG-AAABLFCY.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/bg-BG-AAABLFCY.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ bg_BG_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/bg-BG.json\nvar labels = {\n  paste: \"\\u041F\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438\",\n  pasteAsPlaintext: \"\\u041F\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438 \\u043A\\u0430\\u0442\\u043E \\u043E\\u0431\\u0438\\u043A\\u043D\\u043E\\u0432\\u0435\\u043D \\u0442\\u0435\\u043A\\u0441\\u0442\",\n  pasteCharts: \"\\u041F\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438 \\u0433\\u0440\\u0430\\u0444\\u0438\\u043A\\u0438\",\n  selectAll: \"\\u041C\\u0430\\u0440\\u043A\\u0438\\u0440\\u0430\\u0439 \\u0432\\u0441\\u0438\\u0447\\u043A\\u043E\",\n  multiSelect: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438 \\u0435\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442 \\u043A\\u044A\\u043C \\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\",\n  moveCanvas: \"\\u041F\\u0440\\u0435\\u043C\\u0435\\u0441\\u0442\\u0438 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\",\n  cut: \"\\u0418\\u0437\\u0440\\u0435\\u0436\\u0438\",\n  copy: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u0439\",\n  copyAsPng: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043D\\u0435 \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430\",\n  copyAsSvg: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043D\\u043E \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430 \\u043A\\u0430\\u0442\\u043E SVG\",\n  copyText: \"\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"\\u041F\\u0440\\u0435\\u043C\\u0435\\u0441\\u0442\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430\\u043F\\u0440\\u0435\\u0434\",\n  sendToBack: \"\\u0418\\u0437\\u043D\\u0430\\u0441\\u044F\\u043D\\u0435 \\u043D\\u0430\\u0437\\u0430\\u0434\",\n  bringToFront: \"\\u0418\\u0437\\u043D\\u0430\\u0441\\u044F\\u043D\\u0435 \\u043E\\u0442\\u043F\\u0440\\u0435\\u0434\",\n  sendBackward: \"\\u0418\\u0437\\u043F\\u0440\\u0430\\u0442\\u0438 \\u043E\\u0442\\u0437\\u0430\\u0434\",\n  delete: \"\\u0418\\u0437\\u0442\\u0440\\u0438\\u0439\",\n  copyStyles: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u0439\\u0442\\u0435 \\u0441\\u0442\\u0438\\u043B\\u043E\\u0432\\u0435\",\n  pasteStyles: \"\\u041F\\u043E\\u0441\\u0442\\u0430\\u0432\\u0438 \\u0441\\u0442\\u0438\\u043B\\u043E\\u0432\\u0435\",\n  stroke: \"\\u0429\\u0440\\u0438\\u0445\",\n  background: \"\\u0424\\u043E\\u043D\",\n  fill: \"\\u041D\\u0430\\u0441\\u0438\\u0442\\u0435\\u043D\\u043E\\u0441\\u0442\",\n  strokeWidth: \"\\u0428\\u0438\\u0440\\u0438\\u043D\\u0430 \\u043D\\u0430 \\u0449\\u0440\\u0438\\u0445\\u0430\",\n  strokeStyle: \"\\u0421\\u0442\\u0438\\u043B \\u043D\\u0430 \\u043B\\u0438\\u043D\\u0438\\u044F\",\n  strokeStyle_solid: \"\\u041F\\u043B\\u044A\\u0442\\u0435\\u043D\",\n  strokeStyle_dashed: \"\\u041F\\u0443\\u043D\\u043A\\u0442\\u0438\\u0440\",\n  strokeStyle_dotted: \"\\u041F\\u0443\\u043D\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u043E\",\n  sloppiness: \"\\u041D\\u0435\\u0431\\u0440\\u0435\\u0436\\u043D\\u043E\\u0441\\u0442\",\n  opacity: \"\\u041F\\u0440\\u043E\\u0437\\u0440\\u0430\\u0447\\u043D\\u043E\\u0441\\u0442\",\n  textAlign: \"\\u041F\\u043E\\u0434\\u0440\\u0430\\u0432\\u043D\\u044F\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\",\n  edges: \"\\u041A\\u0440\\u0430\\u0439\\u0449\\u0430\",\n  sharp: \"\\u041E\\u0441\\u0442\\u044A\\u0440\",\n  round: \"\\u0417\\u0430\\u043A\\u0440\\u044A\\u0433\\u043B\\u0435\\u043D\\u043E\",\n  arrowheads: \"\\u0421\\u0442\\u0440\\u0435\\u043B\\u043A\\u0438\",\n  arrowhead_none: \"\\u0411\\u0435\\u0437\",\n  arrowhead_arrow: \"\\u0421\\u0442\\u0440\\u0435\\u043B\\u043A\\u0430\",\n  arrowhead_bar: \"\\u0412\\u0440\\u044A\\u0445 \\u043D\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043B\\u043A\\u0430\\u0442\\u0430\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"\\u0422\\u0440\\u0438\\u044A\\u0433\\u044A\\u043B\\u043D\\u0438\\u043A\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"\\u0420\\u0430\\u0437\\u043C\\u0435\\u0440 \\u043D\\u0430 \\u0448\\u0440\\u0438\\u0444\\u0442\\u0430\",\n  fontFamily: \"\\u0421\\u0435\\u043C\\u0435\\u0439\\u0441\\u0442\\u0432\\u043E \\u0448\\u0440\\u0438\\u0444\\u0442\\u043E\\u0432\\u0435\",\n  addWatermark: '\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438 \"\\u041D\\u0430\\u043F\\u0440\\u0430\\u0432\\u0435\\u043D\\u043E \\u0441 Excalidraw\"',\n  handDrawn: \"\\u041D\\u0430\\u0440\\u0438\\u0441\\u0443\\u0432\\u0430\\u043D\\u043E \\u043D\\u0430 \\u0440\\u044A\\u043A\\u0430\",\n  normal: \"\\u041D\\u043E\\u0440\\u043C\\u0430\\u043B\\u0435\\u043D\",\n  code: \"\\u041A\\u043E\\u0434\",\n  small: \"\\u041C\\u0430\\u043B\\u044A\\u043A\",\n  medium: \"\\u0421\\u0440\\u0435\\u0434\\u0435\\u043D\",\n  large: \"\\u0413\\u043E\\u043B\\u044F\\u043C\",\n  veryLarge: \"\\u041C\\u043D\\u043E\\u0433\\u043E \\u0433\\u043E\\u043B\\u044F\\u043C\",\n  solid: \"\\u0421\\u043E\\u043B\\u0438\\u0434\\u0435\\u043D\",\n  hachure: \"\\u0425\\u0435\\u0440\\u0430\\u043B\\u0434\\u0438\\u043A\\u0430\",\n  zigzag: \"\\u0417\\u0438\\u0433\\u0437\\u0430\\u0433\",\n  crossHatch: \"\\u0414\\u0432\\u043E\\u0439\\u043D\\u043E-\\u043F\\u0440\\u0435\\u0441\\u0435\\u0447\\u0435\\u043D\\u043E\",\n  thin: \"\\u0422\\u044A\\u043D\\u044A\\u043A\",\n  bold: \"\\u042F\\u0441\\u043D\\u043E \\u043E\\u0447\\u0435\\u0440\\u0442\\u0430\\u043D\",\n  left: \"\\u041B\\u044F\\u0432\\u043E\",\n  center: \"\\u0426\\u0435\\u043D\\u0442\\u044A\\u0440\",\n  right: \"\\u0414\\u044F\\u0441\\u043D\\u043E\",\n  extraBold: \"\\u041C\\u043D\\u043E\\u0433\\u043E \\u044F\\u0441\\u043D\\u043E \\u043E\\u0447\\u0435\\u0440\\u0442\\u0430\\u043D\",\n  architect: \"\\u0410\\u0440\\u0445\\u0438\\u0442\\u0435\\u043A\\u0442\",\n  artist: \"\\u0425\\u0443\\u0434\\u043E\\u0436\\u043D\\u0438\\u043A\",\n  cartoonist: \"\\u041A\\u0430\\u0440\\u0438\\u043A\\u0430\\u0442\\u0443\\u0440\\u0438\\u0441\\u0442\",\n  fileTitle: \"\\u0418\\u043C\\u0435 \\u043D\\u0430 \\u0444\\u0430\\u0439\\u043B\",\n  colorPicker: \"\\u0418\\u0437\\u0431\\u043E\\u0440 \\u043D\\u0430 \\u0446\\u0432\\u044F\\u0442\",\n  canvasColors: \"\\u0418\\u0437\\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u043D \\u043D\\u0430 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\",\n  canvasBackground: \"\\u0424\\u043E\\u043D \\u043D\\u0430 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\",\n  drawingCanvas: \"\\u041F\\u043B\\u0430\\u0442\\u043D\\u043E \\u0437\\u0430 \\u0440\\u0438\\u0441\\u0443\\u0432\\u0430\\u043D\\u0435\",\n  layers: \"\\u0421\\u043B\\u043E\\u0435\\u0432\\u0435\",\n  actions: \"\\u0414\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F\",\n  language: \"\\u0415\\u0437\\u0438\\u043A\",\n  liveCollaboration: \"\",\n  duplicateSelection: \"\\u0414\\u0443\\u0431\\u043B\\u0438\\u0440\\u0430\\u0439\",\n  untitled: \"\\u041D\\u0435\\u043E\\u0437\\u0430\\u0433\\u043B\\u0430\\u0432\\u0435\\u043D\\u043E\",\n  name: \"\\u0418\\u043C\\u0435\",\n  yourName: \"\\u0412\\u0430\\u0448\\u0435\\u0442\\u043E \\u0438\\u043C\\u0435\",\n  madeWithExcalidraw: \"\\u041D\\u0430\\u043F\\u0440\\u0430\\u0432\\u0435\\u043D\\u043E \\u0441 Excalidraw\",\n  group: \"\\u0413\\u0440\\u0443\\u043F\\u0438\\u0440\\u0430\\u0439 \\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\\u0442\\u0430\",\n  ungroup: \"\\u0421\\u043F\\u0440\\u0438 \\u0433\\u0440\\u0443\\u043F\\u0438\\u0440\\u0430\\u043D\\u0435\\u0442\\u043E \\u043D\\u0430 \\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\\u0442\\u0430\",\n  collaborators: \"\\u0421\\u044A\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0446\\u0438\",\n  showGrid: \"\\u041F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u043C\\u0440\\u0435\\u0436\\u0430\",\n  addToLibrary: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u044F\\u043D\\u0435 \\u043A\\u044A\\u043C \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  removeFromLibrary: \"\\u041F\\u0440\\u0435\\u043C\\u0430\\u0445\\u0432\\u0430\\u043D\\u0435 \\u043E\\u0442 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  libraryLoadingMessage: \"\\u0417\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\\u2026\",\n  libraries: \"\\u0420\\u0430\\u0437\\u0433\\u043B\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0438\\u0442\\u0435\",\n  loadingScene: \"\\u0417\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0446\\u0435\\u043D\\u0430\\u2026\",\n  align: \"\\u041F\\u043E\\u0434\\u0440\\u0430\\u0432\\u043D\\u044F\\u0432\\u0430\\u043D\\u0435\",\n  alignTop: \"\\u041F\\u043E\\u0434\\u0440\\u0430\\u0432\\u043D\\u044F\\u0432\\u0430\\u043D\\u0435 \\u043E\\u0442\\u0433\\u043E\\u0440\\u0435\",\n  alignBottom: \"\\u041F\\u043E\\u0434\\u0440\\u0430\\u0432\\u043D\\u044F\\u0432\\u0430\\u043D\\u0435 \\u043E\\u0442\\u0434\\u043E\\u043B\\u0443\",\n  alignLeft: \"\\u041F\\u043E\\u0434\\u0440\\u0430\\u0432\\u043D\\u044F\\u0432\\u0430\\u043D\\u0435 \\u043E\\u0442\\u043B\\u044F\\u0432\\u043E\",\n  alignRight: \"\\u041F\\u043E\\u0434\\u0440\\u0430\\u0432\\u043D\\u044F\\u0432\\u0430\\u043D\\u0435 \\u043E\\u0442\\u0434\\u044F\\u0441\\u043D\\u043E\",\n  centerVertically: \"\\u0426\\u0435\\u043D\\u0442\\u0440\\u0438\\u0440\\u0430\\u0439 \\u0432\\u0435\\u0440\\u0442\\u0438\\u043A\\u0430\\u043B\\u043D\\u043E\",\n  centerHorizontally: \"\\u0426\\u0435\\u043D\\u0442\\u0440\\u0438\\u0440\\u0430\\u0439 \\u0445\\u043E\\u0440\\u0438\\u0437\\u043E\\u043D\\u0442\\u0430\\u043B\\u043D\\u043E\",\n  distributeHorizontally: \"\\u0420\\u0430\\u0437\\u043F\\u0440\\u0435\\u0434\\u0435\\u043B\\u0438 \\u0445\\u043E\\u0440\\u0438\\u0437\\u043E\\u043D\\u0442\\u0430\\u043B\\u043D\\u043E\",\n  distributeVertically: \"\\u0420\\u0430\\u0437\\u043F\\u0440\\u0435\\u0434\\u0435\\u043B\\u0438 \\u0432\\u0435\\u0440\\u0442\\u0438\\u043A\\u0430\\u043B\\u043D\\u043E\",\n  flipHorizontal: \"\\u0425\\u043E\\u0440\\u0438\\u0437\\u043E\\u043D\\u0442\\u0430\\u043B\\u043D\\u043E \\u043E\\u0431\\u0440\\u044A\\u0449\\u0430\\u043D\\u0435\",\n  flipVertical: \"\\u0412\\u0435\\u0440\\u0442\\u0438\\u043A\\u0430\\u043B\\u043D\\u043E \\u043E\\u0431\\u0440\\u044A\\u0449\\u0430\\u043D\\u0435\",\n  viewMode: \"\\u0418\\u0437\\u0433\\u043B\\u0435\\u0434\",\n  share: \"\\u0421\\u043F\\u043E\\u0434\\u0435\\u043B\\u0438\",\n  showStroke: \"\",\n  showBackground: \"\",\n  toggleTheme: \"\\u0412\\u043A\\u043B\\u044E\\u0447\\u0438 \\u0442\\u0435\\u043C\\u0430\",\n  personalLib: \"\\u041B\\u0438\\u0447\\u043D\\u0430 \\u0411\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\",\n  excalidrawLib: \"Excalidraw \\u0411\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\",\n  decreaseFontSize: \"\\u041D\\u0430\\u043C\\u0430\\u043B\\u0438 \\u0440\\u0430\\u0437\\u043C\\u0435\\u0440\\u0430 \\u043D\\u0430 \\u0448\\u0440\\u0438\\u0444\\u0442\\u0430\",\n  increaseFontSize: \"\\u0423\\u0432\\u0435\\u043B\\u0438\\u0447\\u0438 \\u0440\\u0430\\u0437\\u043C\\u0435\\u0440\\u0430 \\u043D\\u0430 \\u0448\\u0440\\u0438\\u0444\\u0442\\u0430\",\n  unbindText: \"\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"\\u0420\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u0439 \\u043B\\u0438\\u043D\\u043A\",\n    editEmbed: \"\",\n    create: \"\",\n    createEmbed: \"\",\n    label: \"\\u041B\\u0438\\u043D\\u043A\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"\\u0417\\u0430\\u043A\\u043B\\u044E\\u0447\\u0438\",\n    unlock: \"\\u041E\\u0442\\u043A\\u043B\\u044E\\u0447\\u0438\",\n    lockAll: \"\\u0417\\u0430\\u043A\\u043B\\u044E\\u0447\\u0438 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438\",\n    unlockAll: \"\\u041E\\u0442\\u043A\\u043B\\u044E\\u0447\\u0438 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438\"\n  },\n  statusPublished: \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u043D\\u0438\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0438 \\u0446\\u0432\\u044F\\u0442 \\u043E\\u0442 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\\u0442\\u043E\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\\u041D\\u044F\\u043C\\u0430 \\u0434\\u043E\\u0431\\u0430\\u0432\\u0435\\u043D\\u0438 \\u043D\\u0435\\u0449\\u0430 \\u0432\\u0441\\u0435 \\u043E\\u0449\\u0435...\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"\\u041D\\u0443\\u043B\\u0438\\u0440\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\",\n  exportJSON: \"\",\n  exportImage: \"\",\n  export: \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0438 \\u043D\\u0430...\",\n  copyToClipboard: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043D\\u0435 \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430\",\n  save: \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0438 \\u043A\\u044A\\u043C \\u0442\\u0435\\u043A\\u0443\\u0449 \\u0444\\u0430\\u0439\\u043B\",\n  saveAs: \"\\u0417\\u0430\\u043F\\u0438\\u0448\\u0438 \\u043A\\u0430\\u0442\\u043E\",\n  load: \"\\u041E\\u0442\\u0432\\u043E\\u0440\\u0438\",\n  getShareableLink: \"\\u041F\\u043E\\u043B\\u0443\\u0447\\u0430\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0432\\u0440\\u044A\\u0437\\u043A\\u0430 \\u0437\\u0430 \\u0441\\u043F\\u043E\\u0434\\u0435\\u043B\\u044F\\u043D\\u0435\",\n  close: \"\\u0417\\u0430\\u0442\\u0432\\u043E\\u0440\\u0438\",\n  selectLanguage: \"\\u0418\\u0437\\u0431\\u043E\\u0440 \\u043D\\u0430 \\u0435\\u0437\\u0438\\u043A\",\n  scrollBackToContent: \"\\u041F\\u0440\\u0435\\u0432\\u044A\\u0440\\u0442\\u0435\\u0442\\u0435 \\u043E\\u0431\\u0440\\u0430\\u0442\\u043D\\u043E \\u043A\\u044A\\u043C \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435\\u0442\\u043E\",\n  zoomIn: \"\\u041F\\u0440\\u0438\\u0431\\u043B\\u0438\\u0436\\u0430\\u0432\\u0430\\u043D\\u0435\",\n  zoomOut: \"\\u041E\\u0442\\u0434\\u0430\\u043B\\u0435\\u0447\\u0430\\u0432\\u0430\\u043D\\u0435\",\n  resetZoom: \"\\u0421\\u0442\\u0430\\u043D\\u0434\\u0430\\u0440\\u0442\\u0435\\u043D \\u043C\\u0430\\u0449\\u0430\\u0431\",\n  menu: \"\\u041C\\u0435\\u043D\\u044E\",\n  done: \"\\u0417\\u0430\\u0432\\u044A\\u0440\\u0448\\u0435\\u043D\\u043E\",\n  edit: \"\\u0420\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435\",\n  undo: \"\\u041E\\u0442\\u043C\\u044F\\u043D\\u0430\",\n  redo: \"\\u041F\\u043E\\u0432\\u0442\\u043E\\u0440\\u0438\",\n  resetLibrary: \"\\u041D\\u0443\\u043B\\u0438\\u0440\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  createNewRoom: \"\\u0421\\u044A\\u0437\\u0434\\u0430\\u0439 \\u043D\\u043E\\u0432\\u0430 \\u0441\\u0442\\u0430\\u044F\",\n  fullScreen: \"\\u041D\\u0430 \\u0446\\u044F\\u043B \\u0435\\u043A\\u0440\\u0430\\u043D\",\n  darkMode: \"\\u0422\\u044A\\u043C\\u0435\\u043D \\u0440\\u0435\\u0436\\u0438\\u043C\",\n  lightMode: \"\\u0421\\u0432\\u0435\\u0442\\u044A\\u043B \\u0440\\u0435\\u0436\\u0438\\u043C\",\n  zenMode: \"\\u0420\\u0435\\u0436\\u0438\\u043C Zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"\\u0421\\u043F\\u0438\\u0440\\u0430\\u043D\\u0435 \\u043D\\u0430 Zen \\u0440\\u0435\\u0436\\u0438\\u043C\",\n  cancel: \"\\u041E\\u0442\\u043C\\u0435\\u043D\\u0438\",\n  clear: \"\\u0418\\u0437\\u0447\\u0438\\u0441\\u0442\\u0438\",\n  remove: \"\\u041F\\u0440\\u0435\\u043C\\u0430\\u0445\\u0432\\u0430\\u043D\\u0435\",\n  embed: \"\",\n  publishLibrary: \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u0439\",\n  submit: \"\\u0418\\u0437\\u043F\\u0440\\u0430\\u0442\\u0438\",\n  confirm: \"\\u041F\\u043E\\u0442\\u0432\\u044A\\u0440\\u0436\\u0434\\u0430\\u0432\\u0430\\u043D\\u0435\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"\\u0422\\u043E\\u0432\\u0430 \\u0449\\u0435 \\u0438\\u0437\\u0447\\u0438\\u0441\\u0442\\u0438 \\u0446\\u044F\\u043B\\u043E\\u0442\\u043E \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E. \\u0421\\u0438\\u0433\\u0443\\u0440\\u043D\\u0438 \\u043B\\u0438 \\u0441\\u0442\\u0435?\",\n  couldNotCreateShareableLink: \"\\u0412\\u0440\\u044A\\u0437\\u043A\\u0430\\u0442\\u0430 \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u0441\\u044A\\u0437\\u0434\\u0430\\u0434\\u0435\\u043D\\u0430.\",\n  couldNotCreateShareableLinkTooBig: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0441\\u0435 \\u0441\\u044A\\u0437\\u0434\\u0430\\u0434\\u0435 \\u0432\\u0440\\u044A\\u0437\\u043A\\u0430 \\u0437\\u0430 \\u0441\\u043F\\u043E\\u0434\\u0435\\u043B\\u044F\\u043D\\u0435: \\u0441\\u0446\\u0435\\u043D\\u0430\\u0442\\u0430 \\u0435 \\u0442\\u0432\\u044A\\u0440\\u0434\\u0435 \\u0433\\u043E\\u043B\\u044F\\u043C\\u0430\",\n  couldNotLoadInvalidFile: \"\\u041D\\u0435\\u0432\\u0430\\u043B\\u0438\\u0434\\u0435\\u043D \\u0444\\u0430\\u0439\\u043B \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0441\\u0435 \\u0437\\u0430\\u0440\\u0435\\u0434\\u0438\",\n  importBackendFailed: \"\\u0418\\u043C\\u043F\\u043E\\u0440\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435\\u0442\\u043E \\u043E\\u0442 \\u0431\\u0435\\u043A\\u0435\\u043D\\u0434 \\u043D\\u0435 \\u0431\\u0435\\u0448\\u0435 \\u0443\\u0441\\u043F\\u0435\\u0448\\u043D\\u043E.\",\n  cannotExportEmptyCanvas: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0441\\u0435 \\u0435\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0438\\u0440\\u0430 \\u043F\\u0440\\u0430\\u0437\\u043D\\u043E \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E.\",\n  couldNotCopyToClipboard: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435\\u043C \\u0434\\u0430 \\u043A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043C\\u0435 \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0430\\u0440\\u0434\\u0430.\",\n  decryptFailed: \"\\u0414\\u0430\\u043D\\u043D\\u0438\\u0442\\u0435 \\u043D\\u0435 \\u043C\\u043E\\u0436\\u0430\\u0445\\u0430 \\u0434\\u0430 \\u0441\\u0435 \\u0434\\u0435\\u0448\\u0438\\u0444\\u0440\\u0438\\u0440\\u0430\\u0442.\",\n  uploadedSecurly: \"\\u041A\\u0430\\u0447\\u0432\\u0430\\u043D\\u0435\\u0442\\u043E \\u0435 \\u0437\\u0430\\u0449\\u0438\\u0442\\u0435\\u043D\\u043E \\u0441 \\u043A\\u0440\\u0438\\u043F\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435 \\u043E\\u0442 \\u043A\\u0440\\u0430\\u0439 \\u0434\\u043E \\u043A\\u0440\\u0430\\u0439, \\u043A\\u043E\\u0435\\u0442\\u043E \\u043E\\u0437\\u043D\\u0430\\u0447\\u0430\\u0432\\u0430, \\u0447\\u0435 \\u0441\\u044A\\u0440\\u0432\\u044A\\u0440\\u044A\\u0442 Excalidraw \\u0438 \\u0442\\u0440\\u0435\\u0442\\u0438 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438 \\u043D\\u0435 \\u043C\\u043E\\u0433\\u0430\\u0442 \\u0434\\u0430 \\u0447\\u0435\\u0442\\u0430\\u0442 \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435\\u0442\\u043E.\",\n  loadSceneOverridePrompt: \"\\u0417\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435\\u0442\\u043E \\u043D\\u0430 \\u0432\\u044A\\u043D\\u0448\\u043D\\u0430 \\u0440\\u0438\\u0441\\u0443\\u043D\\u043A\\u0430 \\u0449\\u0435 \\u043F\\u0440\\u0435\\u0437\\u0430\\u043F\\u0438\\u0448\\u0435 \\u043D\\u0430\\u0441\\u0442\\u043E\\u044F\\u0449\\u043E\\u0442\\u043E \\u0432\\u0438 \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435. \\u0416\\u0435\\u043B\\u0430\\u0435\\u0442\\u0435 \\u043B\\u0438 \\u0434\\u0430 \\u043F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438\\u0442\\u0435?\",\n  collabStopOverridePrompt: \"\\u041F\\u0440\\u0435\\u043A\\u0440\\u0430\\u0442\\u044F\\u0432\\u0430\\u043D\\u0435\\u0442\\u043E \\u043D\\u0430 \\u0441\\u0435\\u0441\\u0438\\u044F\\u0442\\u0430 \\u0449\\u0435 \\u043F\\u0440\\u0435\\u0437\\u0430\\u043F\\u0438\\u0448\\u0435 \\u043F\\u0440\\u0435\\u0434\\u0438\\u0448\\u043D\\u0430\\u0442\\u0430, \\u043B\\u043E\\u043A\\u0430\\u043B\\u043D\\u043E \\u0437\\u0430\\u043F\\u0430\\u0437\\u0435\\u043D\\u0430, \\u0440\\u0438\\u0441\\u0443\\u043D\\u043A\\u0430. \\u0421\\u0438\\u0433\\u0443\\u0440\\u043D\\u0438 \\u043B\\u0438 \\u0441\\u0442\\u0435?\\n\\n(\\u0410\\u043A\\u043E \\u0438\\u0441\\u043A\\u0430\\u0442\\u0435 \\u0434\\u0430 \\u043F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438\\u0442\\u0435 \\u0441 \\u043B\\u043E\\u043A\\u0430\\u043B\\u043D\\u0430\\u0442\\u0430 \\u0440\\u0438\\u0441\\u0443\\u043D\\u043A\\u0430, \\u043F\\u0440\\u043E\\u0441\\u0442\\u043E \\u0437\\u0430\\u0442\\u0432\\u043E\\u0440\\u0435\\u0442\\u0435 \\u0442\\u0430\\u0431\\u0430 \\u043D\\u0430 \\u0431\\u0440\\u0430\\u0443\\u0437\\u044A\\u0440\\u0430.)\",\n  errorAddingToLibrary: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435\\u043C \\u0434\\u0430 \\u0437\\u0430\\u0440\\u0435\\u0434\\u0438\\u043C \\u043E\\u0442 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  errorRemovingFromLibrary: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435\\u043C \\u0434\\u0430 \\u043F\\u0440\\u0435\\u043C\\u0430\\u0445\\u043D\\u0435\\u043C \\u0435\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442 \\u043E\\u0442 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  confirmAddLibrary: \"\\u0429\\u0435 \\u0441\\u0435 \\u0434\\u043E\\u0431\\u0430\\u0432\\u044F\\u0442 {{numShapes}} \\u0444\\u0438\\u0433\\u0443\\u0440\\u0430(\\u0438) \\u0432\\u044A\\u0432 \\u0432\\u0430\\u0448\\u0430\\u0442\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430. \\u0421\\u0438\\u0433\\u0443\\u0440\\u043D\\u0438 \\u043B\\u0438 \\u0441\\u0442\\u0435?\",\n  imageDoesNotContainScene: \"\",\n  cannotRestoreFromImage: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0431\\u044A\\u0434\\u0435 \\u0432\\u044A\\u0437\\u0441\\u0442\\u0430\\u043D\\u043E\\u0432\\u0435\\u043D\\u0430 \\u0441\\u0446\\u0435\\u043D\\u0430 \\u043E\\u0442 \\u0442\\u043E\\u0437\\u0438 \\u0444\\u0430\\u0439\\u043B\",\n  invalidSceneUrl: \"\",\n  resetLibrary: \"\",\n  removeItemsFromsLibrary: \"\\u0418\\u0437\\u0442\\u0440\\u0438\\u0439 {{count}} \\u0435\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442(\\u0430) \\u043E\\u0442 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430?\",\n  invalidEncryptionKey: \"\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"\\u0422\\u043E\\u0437\\u0438 \\u0444\\u0430\\u0439\\u043B\\u043E\\u0432 \\u0444\\u043E\\u0440\\u043C\\u0430\\u0442 \\u043D\\u0435 \\u0441\\u0435 \\u043F\\u043E\\u0434\\u0434\\u044A\\u0440\\u0436\\u0430.\",\n  imageInsertError: \"\",\n  fileTooBig: \"\\u0424\\u0430\\u0439\\u043B\\u044A\\u0442 \\u0435 \\u0442\\u0432\\u044A\\u0440\\u0434\\u0435 \\u0433\\u043E\\u043B\\u044F\\u043C. \\u041C\\u0430\\u043A\\u0441\\u0438\\u043C\\u0430\\u043B\\u043D\\u0438\\u044F \\u0434\\u043E\\u043F\\u0443\\u0441\\u0442\\u0438\\u043C \\u0440\\u0430\\u0437\\u043C\\u0435\\u0440 \\u0435 {{maxSize}}.\",\n  svgImageInsertError: \"\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"\\u041D\\u0435\\u0432\\u0430\\u043B\\u0438\\u0434\\u0435\\u043D SVG.\",\n  cannotResolveCollabServer: \"\",\n  importLibraryError: \"\\u041D\\u0435 \\u043C\\u043E\\u0436\\u0435\\u043C \\u0434\\u0430 \\u0437\\u0430\\u0440\\u0435\\u0434\\u0438\\u043C \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\\u0421\\u0438\\u043B\\u043D\\u043E \\u043F\\u0440\\u0435\\u043F\\u043E\\u0440\\u044A\\u0447\\u0432\\u0430\\u043C\\u0435 \\u0434\\u0430 \\u0438\\u0437\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435 \\u0442\\u0430\\u0437\\u0438 \\u043D\\u0430\\u0441\\u0442\\u0440\\u043E\\u0439\\u043A\\u0430. \\u041C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u0441\\u043B\\u0435\\u0434\\u0432\\u0430\\u0442\\u0435 <link>\\u0442\\u0435\\u0437\\u0438 \\u0441\\u0442\\u044A\\u043F\\u043A\\u0438</link> \\u0437\\u0430 \\u0442\\u043E\\u0432\\u0430 \\u043A\\u0430\\u043A \\u0434\\u0430 \\u0433\\u043E \\u043D\\u0430\\u043F\\u0440\\u0430\\u0432\\u0438\\u0442\\u0435.\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"\\u0421\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\",\n  image: \"\\u0412\\u043C\\u044A\\u043A\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0438\\u0437\\u043E\\u0431\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u0435\",\n  rectangle: \"\\u041F\\u0440\\u0430\\u0432\\u043E\\u044A\\u0433\\u044A\\u043B\\u043D\\u0438\\u043A\",\n  diamond: \"\\u0414\\u0438\\u0430\\u043C\\u0430\\u043D\\u0442\",\n  ellipse: \"\\u0415\\u043B\\u0438\\u043F\\u0441\",\n  arrow: \"\\u0421\\u0442\\u0440\\u0435\\u043B\\u043A\\u0430\",\n  line: \"\\u041B\\u0438\\u043D\\u0438\\u044F\",\n  freedraw: \"\\u0420\\u0438\\u0441\\u0443\\u0432\\u0430\\u043D\\u0435\",\n  text: \"\\u0422\\u0435\\u043A\\u0441\\u0442\",\n  library: \"\\u0411\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\",\n  lock: \"\\u041F\\u043E\\u0434\\u0434\\u044A\\u0440\\u0436\\u0430\\u0439\\u0442\\u0435 \\u0438\\u0437\\u0431\\u0440\\u0430\\u043D\\u0438\\u044F \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442 \\u0430\\u043A\\u0442\\u0438\\u0432\\u0435\\u043D \\u0441\\u043B\\u0435\\u0434 \\u0440\\u0438\\u0441\\u0443\\u0432\\u0430\\u043D\\u0435\",\n  penMode: \"\",\n  link: \"\",\n  eraser: \"\\u0413\\u0443\\u043C\\u0430\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\\u041E\\u0449\\u0435 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u0438\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"\\u0414\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u043F\\u043E \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\\u0442\\u043E\",\n  selectedShapeActions: \"\\u0418\\u0437\\u0431\\u0440\\u0430\\u043D\\u0438 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F\",\n  shapes: \"\\u0424\\u0438\\u0433\\u0443\\u0440\\u0438\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"\\u041A\\u043B\\u0438\\u043A\\u043D\\u0435\\u0442\\u0435, \\u0437\\u0430 \\u0434\\u0430 \\u0441\\u0442\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430\\u0442\\u0435 \\u043D\\u044F\\u043A\\u043E\\u043B\\u043A\\u043E \\u0442\\u043E\\u0447\\u043A\\u0438, \\u043F\\u043B\\u044A\\u0437\\u043D\\u0435\\u0442\\u0435 \\u0437\\u0430 \\u0435\\u0434\\u043D\\u0430 \\u043B\\u0438\\u043D\\u0438\\u044F\",\n  freeDraw: \"\\u041D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 \\u0438 \\u0432\\u043B\\u0430\\u0447\\u0435\\u0442\\u0435, \\u043F\\u0443\\u0441\\u043D\\u0435\\u0442\\u0435 \\u043A\\u0430\\u0442\\u043E \\u0441\\u0442\\u0435 \\u0433\\u043E\\u0442\\u043E\\u0432\\u0438\",\n  text: \"\\u041F\\u043E\\u0434\\u0441\\u043A\\u0430\\u0437\\u043A\\u0430: \\u041C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0441\\u044A\\u0449\\u043E \\u0434\\u0430 \\u0434\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u0435 \\u0442\\u0435\\u043A\\u0441\\u0442 \\u043A\\u0430\\u0442\\u043E \\u043D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 \\u043D\\u044F\\u043A\\u044A\\u0434\\u0435 \\u0434\\u0432\\u0430 \\u043F\\u044A\\u0442 \\u0441 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u0430 \\u0437\\u0430 \\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\",\n  embeddable: \"\",\n  text_selected: \"\",\n  text_editing: \"\",\n  linearElementMulti: \"\\u041A\\u043B\\u0438\\u043A\\u043D\\u0435\\u0442\\u0435 \\u0432\\u044A\\u0440\\u0445\\u0443 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u043D\\u0430\\u0442\\u0430 \\u0442\\u043E\\u0447\\u043A\\u0430 \\u0438\\u043B\\u0438 \\u043D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 Escape \\u0438\\u043B\\u0438 Enter, \\u0437\\u0430 \\u0434\\u0430 \\u0437\\u0430\\u0432\\u044A\\u0440\\u0448\\u0438\\u0442\\u0435\",\n  lockAngle: \"\\u041C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u043E\\u0433\\u0440\\u0430\\u043D\\u0438\\u0447\\u0438\\u0442\\u0435 \\u044A\\u0433\\u044A\\u043B\\u0430, \\u043A\\u0430\\u0442\\u043E \\u0437\\u0430\\u0434\\u044A\\u0440\\u0436\\u0438\\u0442\\u0435 SHIFT\",\n  resize: \"\\u041C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u043E\\u0433\\u0440\\u0430\\u043D\\u0438\\u0447\\u0438\\u0442\\u0435 \\u043F\\u0440\\u0438 \\u043F\\u0440\\u0435\\u043E\\u0440\\u0430\\u0437\\u043C\\u0435\\u0440\\u044F\\u0432\\u0430\\u043D\\u0435 \\u043A\\u0430\\u0442\\u043E \\u0437\\u0430\\u0434\\u044A\\u0440\\u0436\\u0438\\u0442\\u0435 SHIFT,\\n\\u0437\\u0430\\u0434\\u0440\\u044A\\u0436\\u0442\\u0435 ALT \\u0437\\u0430 \\u043F\\u0440\\u0435\\u043E\\u0440\\u0430\\u0437\\u043C\\u0435\\u0440\\u0438\\u0442\\u0435 \\u043F\\u0440\\u0435\\u0437 \\u0446\\u0435\\u043D\\u0442\\u044A\\u0440\\u0430\",\n  resizeImage: \"\",\n  rotate: \"\\u041C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u043E\\u0433\\u0440\\u0430\\u043D\\u0438\\u0447\\u0438\\u0442\\u0435 \\u044A\\u0433\\u043B\\u0438\\u0442\\u0435, \\u043A\\u0430\\u0442\\u043E \\u0434\\u044A\\u0440\\u0436\\u0438\\u0442\\u0435 SHIFT, \\u0434\\u043E\\u043A\\u0430\\u0442\\u043E \\u0441\\u0435 \\u0432\\u044A\\u0440\\u0442\\u0438\\u0442\\u0435\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\\u041D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 Delete \\u0437\\u0430 \\u0434\\u0430 \\u0438\\u0437\\u0442\\u0440\\u0438\\u0435\\u0442\\u0435 \\u0442\\u043E\\u0447\\u043A\\u0430(\\u0438), CtrlOrCmd+D \\u0437\\u0430 \\u0434\\u0443\\u043F\\u043B\\u0438\\u0446\\u0438\\u0440\\u0430\\u043D\\u0435, \\u0438\\u043B\\u0438 \\u0438\\u0437\\u0432\\u043B\\u0430\\u0447\\u0435\\u0442\\u0435 \\u0437\\u0430 \\u0434\\u0430 \\u043F\\u0440\\u0435\\u043C\\u0435\\u0441\\u0442\\u0438\\u0442\\u0435\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\\u041D\\u0430\\u0442\\u0438\\u0441\\u043D\\u0435\\u0442\\u0435 Enter, \\u0437\\u0430 \\u0434\\u0430 \\u0434\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u0435\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\\u041D\\u0435\\u0432\\u044A\\u0437\\u043C\\u043E\\u0436\\u043D\\u043E\\u0441\\u0442 \\u0437\\u0430 \\u043F\\u043E\\u043A\\u0430\\u0437\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 preview\",\n  canvasTooBig: \"\\u041F\\u043B\\u0430\\u0442\\u043D\\u043E\\u0442\\u043E \\u0435 \\u0442\\u0432\\u044A\\u0440\\u0434\\u0435 \\u0433\\u043E\\u043B\\u044F\\u043C\\u043E.\",\n  canvasTooBigTip: \"\\u041F\\u043E\\u0434\\u0441\\u043A\\u0430\\u0437\\u043A\\u0430: \\u043F\\u0440\\u043E\\u0431\\u0432\\u0430\\u0439\\u0442\\u0435 \\u0434\\u0430 \\u043F\\u0440\\u0438\\u0431\\u043B\\u0438\\u0436\\u0438\\u0442\\u0435 \\u0434\\u0430\\u043B\\u0435\\u0447\\u043D\\u0438\\u0442\\u0435 \\u0435\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\\u0438 \\u043F\\u043E-\\u0431\\u043B\\u0438\\u0437\\u043A\\u043E.\"\n};\nvar errorSplash = {\n  headingMain: \"\\u0421\\u0440\\u0435\\u0449\\u0430 \\u0433\\u0440\\u0435\\u0448\\u043A\\u0430. \\u041E\\u043F\\u0438\\u0442\\u0430\\u0439\\u0442\\u0435 <button>\\u043F\\u0440\\u0435\\u0437\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430\\u0442\\u0430.</button>\",\n  clearCanvasMessage: \"\\u0410\\u043A\\u043E \\u043F\\u0440\\u0435\\u0437\\u0430\\u0440\\u0435\\u0436\\u0434\\u0430\\u043D\\u0435\\u0442\\u043E \\u043D\\u0435 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0438, \\u043E\\u043F\\u0438\\u0442\\u0430\\u0439\\u0442\\u0435 <button>\\u0438\\u0437\\u0447\\u0438\\u0441\\u0442\\u0432\\u0430\\u043D\\u0435 \\u043D\\u0430 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\\u0442\\u043E.</button>\",\n  clearCanvasCaveat: \" \\u0422\\u043E\\u0432\\u0430 \\u0449\\u0435 \\u0434\\u043E\\u0432\\u0435\\u0434\\u0435 \\u0434\\u043E \\u0437\\u0430\\u0433\\u0443\\u0431\\u0430 \\u043D\\u0430 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0430 \",\n  trackedToSentry: \"\\u0413\\u0440\\u0435\\u0448\\u043A\\u0430\\u0442\\u0430 \\u0441 \\u0438\\u0434\\u0435\\u043D\\u0442\\u0438\\u0444\\u0438\\u043A\\u0430\\u0442\\u043E\\u0440 {{eventId}} \\u0431\\u0435\\u0448\\u0435 \\u043F\\u0440\\u043E\\u0441\\u043B\\u0435\\u0434\\u0435\\u043D \\u0432 \\u043D\\u0430\\u0448\\u0430\\u0442\\u0430 \\u0441\\u0438\\u0441\\u0442\\u0435\\u043C\\u0430.\",\n  openIssueMessage: \"\\u0411\\u044F\\u0445\\u043C\\u0435 \\u043C\\u043D\\u043E\\u0433\\u043E \\u043F\\u0440\\u0435\\u0434\\u043F\\u0430\\u0437\\u043B\\u0438\\u0432\\u0438 \\u0434\\u0430 \\u043D\\u0435 \\u0432\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u0435 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u0432\\u0430\\u0448\\u0430\\u0442\\u0430 \\u0441\\u0446\\u0435\\u043D\\u0430 \\u043F\\u0440\\u0438 \\u0433\\u0440\\u0435\\u0448\\u043A\\u0430\\u0442\\u0430. \\u0410\\u043A\\u043E \\u0441\\u0446\\u0435\\u043D\\u0430\\u0442\\u0430 \\u0432\\u0438 \\u043D\\u0435 \\u0435 \\u0447\\u0430\\u0441\\u0442\\u043D\\u0430, \\u043C\\u043E\\u043B\\u044F, \\u043F\\u043E\\u043C\\u0438\\u0441\\u043B\\u0435\\u0442\\u0435 \\u0437\\u0430 \\u043F\\u043E\\u0441\\u043B\\u0435\\u0434\\u0432\\u0430\\u0449\\u0438 \\u0434\\u0435\\u0439\\u0441\\u0442\\u0432\\u0438\\u044F \\u043D\\u0430 \\u043D\\u0430\\u0448\\u0430\\u0442\\u0430 <button>\\u0442\\u0440\\u0430\\u043A\\u0435\\u0440 \\u0437\\u0430 \\u0433\\u0440\\u0435\\u0448\\u043A\\u0438.</button> \\u041C\\u043E\\u043B\\u044F, \\u0432\\u043A\\u043B\\u044E\\u0447\\u0435\\u0442\\u0435 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u044F \\u043F\\u043E-\\u0434\\u043E\\u043B\\u0443, \\u043A\\u0430\\u0442\\u043E \\u044F \\u043A\\u043E\\u043F\\u0438\\u0440\\u0430\\u0442\\u0435 \\u0438 \\u0434\\u043E\\u0431\\u0430\\u0432\\u0438\\u0442\\u0435 \\u0432 GitHub.\",\n  sceneContent: \"\\u0421\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0441\\u0446\\u0435\\u043D\\u0430\\u0442\\u0430:\"\n};\nvar roomDialog = {\n  desc_intro: \"\\u041C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u043F\\u043E\\u043A\\u0430\\u043D\\u0438\\u0442\\u0435 \\u0445\\u043E\\u0440\\u0430 \\u043D\\u0430 \\u0442\\u0435\\u043A\\u0443\\u0449\\u0430\\u0442\\u0430 \\u0441\\u0438 \\u0441\\u0446\\u0435\\u043D\\u0430 \\u0434\\u0430 \\u0441\\u0438 \\u0441\\u044A\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0447\\u0430\\u0442 \\u0441 \\u0432\\u0430\\u0441.\",\n  desc_privacy: \"\\u041D\\u0435 \\u0441\\u0435 \\u043F\\u0440\\u0438\\u0442\\u0435\\u0441\\u043D\\u044F\\u0432\\u0430\\u0439\\u0442\\u0435, \\u0441\\u0435\\u0441\\u0438\\u044F\\u0442\\u0430 \\u0438\\u0437\\u043F\\u043E\\u043B\\u0437\\u0432\\u0430 \\u043A\\u0440\\u0438\\u043F\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435 \\u043E\\u0442 \\u043A\\u0440\\u0430\\u0439 \\u0434\\u043E \\u043A\\u0440\\u0430\\u0439, \\u0442\\u0430\\u043A\\u0430 \\u0447\\u0435 \\u043A\\u0430\\u043A\\u0432\\u043E\\u0442\\u043E \\u043D\\u0430\\u0440\\u0438\\u0441\\u0443\\u0432\\u0430\\u0442\\u0435 \\u0449\\u0435 \\u043E\\u0441\\u0442\\u0430\\u043D\\u0435 \\u0447\\u0430\\u0441\\u0442\\u043D\\u043E. \\u0414\\u043E\\u0440\\u0438 \\u043D\\u0430\\u0448\\u0438\\u044F\\u0442 \\u0441\\u044A\\u0440\\u0432\\u044A\\u0440 \\u043D\\u044F\\u043C\\u0430 \\u0434\\u0430 \\u043C\\u043E\\u0436\\u0435 \\u0434\\u0430 \\u0432\\u0438\\u0434\\u0438 \\u043A\\u0430\\u043A\\u0432\\u043E \\u043F\\u0440\\u0435\\u0434\\u043B\\u0430\\u0433\\u0430\\u0442\\u0435.\",\n  button_startSession: \"\\u0421\\u0442\\u0430\\u0440\\u0442\\u0438\\u0440\\u0430\\u0439\\u0442\\u0435 \\u0441\\u0435\\u0441\\u0438\\u044F\\u0442\\u0430\",\n  button_stopSession: \"\\u0421\\u0442\\u043E\\u043F \\u043D\\u0430 \\u0441\\u0435\\u0441\\u0438\\u044F\\u0442\\u0430\",\n  desc_inProgressIntro: \"\\u0421\\u0435\\u0441\\u0438\\u044F\\u0442\\u0430 \\u0437\\u0430 \\u0441\\u044A\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0447\\u0435\\u0441\\u0442\\u0432\\u043E \\u043D\\u0430 \\u0436\\u0438\\u0432\\u043E \\u0435 \\u0432 \\u0445\\u043E\\u0434.\",\n  desc_shareLink: \"\\u0421\\u043F\\u043E\\u0434\\u0435\\u043B\\u0435\\u0442\\u0435 \\u0442\\u0430\\u0437\\u0438 \\u0432\\u0440\\u044A\\u0437\\u043A\\u0430 \\u0441 \\u0432\\u0441\\u0435\\u043A\\u0438, \\u0441 \\u043A\\u043E\\u0433\\u043E\\u0442\\u043E \\u0438\\u0441\\u043A\\u0430\\u0442\\u0435 \\u0434\\u0430 \\u0441\\u0438 \\u0441\\u044A\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0447\\u0438\\u0442\\u0435:\",\n  desc_exitSession: \"\\u0421\\u043F\\u0438\\u0440\\u0430\\u043D\\u0435\\u0442\\u043E \\u043D\\u0430 \\u0441\\u0435\\u0441\\u0438\\u044F\\u0442\\u0430 \\u0449\\u0435 \\u0432\\u0438 \\u0438\\u0437\\u043A\\u043B\\u044E\\u0447\\u0438 \\u043E\\u0442 \\u0441\\u0442\\u0430\\u044F\\u0442\\u0430, \\u043D\\u043E \\u0449\\u0435 \\u043C\\u043E\\u0436\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u043F\\u0440\\u043E\\u0434\\u044A\\u043B\\u0436\\u0438\\u0442\\u0435 \\u0434\\u0430 \\u0440\\u0430\\u0431\\u043E\\u0442\\u0438\\u0442\\u0435 \\u0441\\u044A\\u0441 \\u0441\\u0446\\u0435\\u043D\\u0430\\u0442\\u0430, \\u043B\\u043E\\u043A\\u0430\\u043B\\u043D\\u043E. \\u0418\\u043C\\u0430\\u0439\\u0442\\u0435 \\u043F\\u0440\\u0435\\u0434\\u0432\\u0438\\u0434, \\u0447\\u0435 \\u0442\\u043E\\u0432\\u0430 \\u043D\\u044F\\u043C\\u0430 \\u0434\\u0430 \\u0437\\u0430\\u0441\\u0435\\u0433\\u043D\\u0435 \\u0434\\u0440\\u0443\\u0433\\u0438 \\u0445\\u043E\\u0440\\u0430 \\u0438 \\u0442\\u0435 \\u0432\\u0441\\u0435 \\u043E\\u0449\\u0435 \\u0449\\u0435 \\u043C\\u043E\\u0433\\u0430\\u0442 \\u0434\\u0430 \\u0441\\u0438 \\u0441\\u044A\\u0442\\u0440\\u0443\\u0434\\u043D\\u0438\\u0447\\u0430\\u0442 \\u0441 \\u0442\\u044F\\u0445\\u043D\\u0430\\u0442\\u0430 \\u0432\\u0435\\u0440\\u0441\\u0438\\u044F.\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"\\u0413\\u0440\\u0435\\u0448\\u043A\\u0430\"\n};\nvar exportDialog = {\n  disk_title: \"\",\n  disk_details: \"\",\n  disk_button: \"\",\n  link_title: \"\",\n  link_details: \"\",\n  link_button: \"\",\n  excalidrawplus_description: \"\",\n  excalidrawplus_button: \"\\u0415\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\",\n  excalidrawplus_exportError: \"\"\n};\nvar helpDialog = {\n  blog: \"\\u041F\\u0440\\u043E\\u0447\\u0435\\u0442\\u0435\\u0442\\u0435 \\u043D\\u0430\\u0448\\u0438\\u044F \\u0431\\u043B\\u043E\\u0433\",\n  click: \"\\u043A\\u043B\\u0438\\u043A\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"\\u0418\\u0437\\u0432\\u0438\\u0442\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043B\\u043A\\u0430\",\n  curvedLine: \"\\u0418\\u0437\\u0432\\u0438\\u0442\\u0430 \\u043B\\u0438\\u043D\\u0438\\u044F\",\n  documentation: \"\\u0414\\u043E\\u043A\\u0443\\u043C\\u0435\\u043D\\u0442\\u0430\\u0446\\u0438\\u044F\",\n  doubleClick: \"\\u0434\\u0432\\u043E\\u0439\\u043D\\u043E-\\u0449\\u0440\\u0430\\u043A\\u0432\\u0430\\u043D\\u0435\",\n  drag: \"\\u043F\\u043B\\u044A\\u0437\\u043D\\u0435\\u0442\\u0435\",\n  editor: \"\\u0420\\u0435\\u0434\\u0430\\u043A\\u0442\\u043E\\u0440\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"\\u041D\\u0430\\u043C\\u0435\\u0440\\u0438\\u0445\\u0442\\u0435 \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C? \\u0418\\u0437\\u043F\\u0440\\u0430\\u0442\\u0435\\u0442\\u0435\",\n  howto: \"\\u0421\\u043B\\u0435\\u0434\\u0432\\u0430\\u0439\\u0442\\u0435 \\u043D\\u0430\\u0448\\u0438\\u0442\\u0435 \\u0440\\u044A\\u043A\\u043E\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u0430\",\n  or: \"\\u0438\\u043B\\u0438\",\n  preventBinding: \"\\u0421\\u043F\\u0440\\u0438 \\u043F\\u0440\\u0438\\u043B\\u0435\\u043F\\u044F\\u043D\\u0435 \\u043D\\u0430 \\u0441\\u0442\\u0440\\u0435\\u043B\\u043A\\u0438\\u0442\\u0435\",\n  tools: \"\\u0418\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442\\u0438\",\n  shortcuts: \"\\u041A\\u043B\\u0430\\u0432\\u0438\\u0448\\u0438 \\u0437\\u0430 \\u0431\\u044A\\u0440\\u0437 \\u0434\\u043E\\u0441\\u0442\\u044A\\u043F\",\n  textFinish: \"\\u0417\\u0430\\u0432\\u044A\\u0440\\u0448\\u0438 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u0435 (\\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u0432 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u043E\\u0440)\",\n  textNewLine: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0438 \\u043D\\u043E\\u0432\\u0430 \\u043B\\u0438\\u043D\\u0438\\u044F (\\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u0432 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u043E\\u0440)\",\n  title: \"\\u041F\\u043E\\u043C\\u043E\\u0449\",\n  view: \"\\u041F\\u0440\\u0435\\u0433\\u043B\\u0435\\u0434\",\n  zoomToFit: \"\\u041F\\u0440\\u0438\\u0431\\u043B\\u0438\\u0436\\u0438 \\u0434\\u043E\\u043A\\u0430\\u0442\\u043E \\u0441\\u0435 \\u0432\\u0438\\u0436\\u0434\\u0430\\u0442 \\u0432\\u0441\\u0438\\u0447\\u043A\\u0438 \\u0435\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\\u0438\",\n  zoomToSelection: \"\\u041F\\u0440\\u0438\\u0431\\u043B\\u0438\\u0436\\u0438 \\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\\u0442\\u0430\",\n  toggleElementLock: \"\\u0417\\u0430\\u043A\\u043B\\u044E\\u0447\\u0438/\\u041E\\u0442\\u043A\\u043B\\u044E\\u0447\\u0438 \\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\",\n  movePageUpDown: \"\\u041F\\u0440\\u0435\\u043C\\u0435\\u0441\\u0442\\u0438 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430 \\u043D\\u0430\\u0433\\u043E\\u0440\\u0435/\\u043D\\u0430\\u0434\\u043E\\u043B\\u0443\",\n  movePageLeftRight: \"\\u041F\\u0440\\u0435\\u043C\\u0435\\u0441\\u0442\\u0438 \\u0441\\u0442\\u0440\\u0430\\u043D\\u0438\\u0446\\u0430 \\u043D\\u0430\\u043B\\u044F\\u0432\\u043E/\\u043D\\u0430\\u0434\\u044F\\u0441\\u043D\\u043E\"\n};\nvar clearCanvasDialog = {\n  title: \"\\u0418\\u0437\\u0447\\u0438\\u0441\\u0442\\u0438 \\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\\u0442\\u043E\"\n};\nvar publishDialog = {\n  title: \"\\u041F\\u0443\\u0431\\u043B\\u0438\\u043A\\u0443\\u0432\\u0430\\u0439 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\",\n  itemName: \"\\u0418\\u043C\\u0435\",\n  authorName: \"\\u0410\\u0432\\u0442\\u043E\\u0440\\u0441\\u043A\\u043E \\u0438\\u043C\\u0435\",\n  githubUsername: \"GitHub \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u0438\\u0442\\u0435\\u043B\\u0441\\u043A\\u043E \\u0438\\u043C\\u0435\",\n  twitterUsername: \"Twitter \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u0438\\u0442\\u0435\\u043B\\u0441\\u043A\\u043E \\u0438\\u043C\\u0435\",\n  libraryName: \"\\u0418\\u043C\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  libraryDesc: \"\\u041E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  website: \"\\u0423\\u0435\\u0431\\u0441\\u0430\\u0439\\u0442\",\n  placeholder: {\n    authorName: \"\\u0418\\u043C\\u0435\\u0442\\u043E \\u0438\\u043B\\u0438 \\u043F\\u043E\\u0442\\u0440\\u0435\\u0431\\u0438\\u0442\\u0435\\u043B\\u0441\\u043A\\u043E\\u0442\\u043E \\u0412\\u0438 \\u0438\\u043C\\u0435\",\n    libraryName: \"\\u0418\\u043C\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430 \\u0412\\u0438\",\n    libraryDesc: \"\\u041E\\u043F\\u0438\\u0441\\u0430\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430 \\u0432\\u0438, \\u0437\\u0430 \\u0434\\u0430 \\u043F\\u043E\\u043C\\u043E\\u0433\\u043D\\u0435\\u0442\\u0435 \\u043D\\u0430 \\u0445\\u043E\\u0440\\u0430\\u0442\\u0430 \\u0434\\u0430 \\u0440\\u0430\\u0437\\u0431\\u0435\\u0440\\u0430\\u0442 \\u043F\\u0440\\u0438\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F\\u0442\\u0430 \\u045D\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"\\u0417\\u0430\\u0434\\u044A\\u043B\\u0436\\u0438\\u0442\\u0435\\u043B\\u043D\\u043E\",\n    website: \"\\u0412\\u044A\\u0432\\u0435\\u0434\\u0435\\u0442\\u0435 \\u0432\\u0430\\u043B\\u0438\\u0434\\u0435\\u043D URL \\u0430\\u0434\\u0440\\u0435\\u0441\"\n  },\n  noteDescription: \"\",\n  noteGuidelines: \"\",\n  noteLicense: \"\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"\",\n  content: \"\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\\u041D\\u0443\\u043B\\u0438\\u0440\\u0430\\u0439 \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\",\n  removeItemsFromLib: \"\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\\u0424\\u043E\\u043D\",\n    onlySelected: \"\\u0421\\u0430\\u043C\\u043E \\u0438\\u0437\\u0431\\u0440\\u0430\\u043D\\u043E\\u0442\\u043E\",\n    darkMode: \"\\u0422\\u044A\\u043C\\u0435\\u043D \\u0440\\u0435\\u0436\\u0438\\u043C\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\\u0418\\u0437\\u043D\\u0430\\u0441\\u044F\\u043D\\u0435 \\u0432 PNG\",\n    exportToSvg: \"\\u0418\\u0437\\u043D\\u0430\\u0441\\u044F\\u043D\\u0435 \\u0432 SVG\",\n    copyPngToClipboard: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u0439 PNG \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043D\\u0435 \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\\u0412\\u0430\\u0448\\u0438\\u0442\\u0435 \\u0440\\u0438\\u0441\\u0443\\u043D\\u043A\\u0438 \\u0441\\u0430 \\u043A\\u0440\\u0438\\u043F\\u0442\\u0438\\u0440\\u0430\\u043D\\u0438 \\u043E\\u0442 \\u043A\\u0440\\u0430\\u0439 \\u0434\\u043E \\u043A\\u0440\\u0430\\u0439, \\u0442\\u0430\\u043A\\u0430 \\u0447\\u0435 \\u0441\\u044A\\u0440\\u0432\\u044A\\u0440\\u0438\\u0442\\u0435 \\u043D\\u0430 Excalidraw \\u043D\\u044F\\u043C\\u0430 \\u0434\\u0430 \\u043C\\u043E\\u0433\\u0430\\u0442 \\u0434\\u0430 \\u0433\\u0438 \\u0432\\u0438\\u0436\\u0434\\u0430\\u0442.\",\n  link: \"\"\n};\nvar stats = {\n  angle: \"\\u042A\\u0433\\u044A\\u043B\",\n  element: \"\\u0415\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\",\n  elements: \"\\u0415\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\\u0438\",\n  height: \"\\u0412\\u0438\\u0441\\u043E\\u0447\\u0438\\u043D\\u0430\",\n  scene: \"\\u0421\\u0446\\u0435\\u043D\\u0430\",\n  selected: \"\\u0421\\u0435\\u043B\\u0435\\u043A\\u0442\\u0438\\u0440\\u0430\\u043D\\u043E\",\n  storage: \"\\u0421\\u044A\\u0445\\u0440\\u0430\\u043D\\u0435\\u043D\\u0438\\u0435 \\u043D\\u0430 \\u0434\\u0430\\u043D\\u043D\\u0438\",\n  title: \"\\u0421\\u0442\\u0430\\u0442\\u0438\\u0441\\u0442\\u0438\\u043A\\u0430 \\u0437\\u0430 \\u0445\\u0430\\u043A\\u0435\\u0440\\u0438\",\n  total: \"\\u041E\\u0431\\u0449\\u043E\",\n  version: \"\\u0412\\u0435\\u0440\\u0441\\u0438\\u044F\",\n  versionCopy: \"\\u041D\\u0430\\u0441\\u0442\\u0438\\u0441\\u043D\\u0438 \\u0437\\u0430 \\u0434\\u0430 \\u043A\\u043E\\u043F\\u0438\\u0440\\u0430\\u0448\",\n  versionNotAvailable: \"\\u0412\\u0435\\u0440\\u0441\\u0438\\u044F\\u0442\\u0430 \\u043D\\u0435 \\u0435 \\u043D\\u0430\\u043B\\u0438\\u0447\\u043D\\u0430\",\n  width: \"\\u0428\\u0438\\u0440\\u043E\\u0447\\u0438\\u043D\\u0430\"\n};\nvar toast = {\n  addedToLibrary: \"\\u0414\\u043E\\u0431\\u0430\\u0432\\u0435\\u043D\\u0430 \\u043A\\u044A\\u043C \\u0431\\u0438\\u0431\\u043B\\u0438\\u043E\\u0442\\u0435\\u043A\\u0430\\u0442\\u0430\",\n  copyStyles: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043D\\u0438 \\u0441\\u0442\\u0438\\u043B\\u043E\\u0432\\u0435.\",\n  copyToClipboard: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430\\u043D\\u043E \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430.\",\n  copyToClipboardAsPng: \"\\u041A\\u043E\\u043F\\u0438\\u0440\\u0430 {{exportSelection}} \\u0432 \\u043A\\u043B\\u0438\\u043F\\u0431\\u043E\\u0440\\u0434\\u0430 \\u043A\\u0430\\u0442\\u043E PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"\\u0424\\u0430\\u0439\\u043B\\u044A\\u0442 \\u0435 \\u0437\\u0430\\u043F\\u0430\\u0437\\u0435\\u043D.\",\n  fileSavedToFilename: \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0435\\u043D \\u043A\\u044A\\u043C {filename}\",\n  canvas: \"\\u043F\\u043B\\u0430\\u0442\\u043D\\u043E\",\n  selection: \"\\u0441\\u0435\\u043B\\u0435\\u043A\\u0446\\u0438\\u044F\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\\u041F\\u0440\\u043E\\u0437\\u0440\\u0430\\u0447\\u0435\\u043D\",\n  black: \"\\u0427\\u0435\\u0440\\u0435\\u043D\",\n  white: \"\\u0411\\u044F\\u043B\",\n  red: \"\\u0427\\u0435\\u0440\\u0432\\u0435\\u043D\",\n  pink: \"\\u0420\\u043E\\u0437\\u043E\\u0432\",\n  grape: \"\\u0413\\u0440\\u043E\\u0437\\u0434\\u0435\",\n  violet: \"\\u0412\\u0438\\u043E\\u043B\\u0435\\u0442\\u043E\\u0432\\u043E\",\n  gray: \"\\u0421\\u0438\\u0432\",\n  blue: \"\\u0421\\u0438\\u043D\",\n  cyan: \"\\u0421\\u0438\\u043D\\u044C\\u043E\\u0437\\u0435\\u043B\\u0435\\u043D\\u043E\",\n  teal: \"\\u0422\\u044A\\u043C\\u043D\\u043E \\u0441\\u0438\\u043D\\u044C\\u043E-\\u0437\\u0435\\u043B\\u0435\\u043D\\u043E\",\n  green: \"\\u0417\\u0435\\u043B\\u0435\\u043D\\u043E\",\n  yellow: \"\\u0416\\u044A\\u043B\\u0442\\u043E\",\n  orange: \"\\u041E\\u0440\\u0430\\u043D\\u0436\\u0435\\u0432\\u043E\",\n  bronze: \"\\u0411\\u0440\\u043E\\u043D\\u0437\\u043E\\u0432\\u043E\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\\u0412\\u0441\\u0438\\u0447\\u043A\\u0438\\u0442\\u0435 \\u0412\\u0438 \\u0434\\u0430\\u043D\\u043D\\u0438 \\u0441\\u0430 \\u0437\\u0430\\u043F\\u0430\\u0437\\u0435\\u043D\\u0438 \\u043B\\u043E\\u043A\\u0430\\u043B\\u043D\\u043E \\u0432 \\u0431\\u0440\\u0430\\u0443\\u0437\\u044A\\u0440\\u0430 \\u0412\\u0438.\",\n    center_heading_plus: \"\",\n    menuHint: \"\\u0415\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442, \\u043F\\u0440\\u0435\\u0434\\u043F\\u043E\\u0447\\u0438\\u0442\\u0430\\u043D\\u0438\\u044F, \\u0435\\u0437\\u0438\\u0446\\u0438, ...\"\n  },\n  defaults: {\n    menuHint: \"\\u0415\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442, \\u043F\\u0440\\u0435\\u0434\\u043F\\u043E\\u0447\\u0438\\u0442\\u0430\\u043D\\u0438\\u044F, \\u0438 \\u043E\\u0449\\u0435...\",\n    center_heading: \"\\u0414\\u0438\\u0430\\u0433\\u0440\\u0430\\u043C\\u0438. \\u041D\\u0430\\u043F\\u0440\\u0430\\u0432\\u0435\\u043D\\u0438. \\u041F\\u0440\\u043E\\u0441\\u0442\\u043E.\",\n    toolbarHint: \"\\u0418\\u0437\\u0431\\u0435\\u0440\\u0435\\u0442\\u0435 \\u0438\\u043D\\u0441\\u0442\\u0440\\u0443\\u043C\\u0435\\u043D\\u0442 & \\u0417\\u0430\\u043F\\u043E\\u0447\\u043D\\u0435\\u0442\\u0435 \\u0434\\u0430 \\u0440\\u0438\\u0441\\u0443\\u0432\\u0430\\u0442\\u0435!\",\n    helpHint: \"\\u041F\\u0440\\u0435\\u043A\\u0438 \\u043F\\u044A\\u0442\\u0438\\u0449\\u0430 & \\u043F\\u043E\\u043C\\u043E\\u0449\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\\u041D\\u0430\\u0439-\\u0447\\u0435\\u0441\\u0442\\u043E \\u0438\\u0437\\u043F\\u043E\\u043B\\u0437\\u0432\\u0430\\u043D\\u0438 \\u0446\\u0432\\u0435\\u0442\\u043E\\u0432\\u0435\",\n  colors: \"\\u0426\\u0432\\u0435\\u0442\\u043E\\u0432\\u0435\",\n  shades: \"\\u041D\\u044E\\u0430\\u043D\\u0441\\u0438\",\n  hexCode: \"\\u0428\\u0435\\u0441\\u0442\\u043D\\u0430\\u0434\\u0435\\u0441\\u0435\\u0442\\u0438\\u0447\\u0435\\u043D \\u043A\\u043E\\u0434\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\\u0418\\u0437\\u043D\\u0435\\u0441\\u0438 \\u043A\\u0430\\u0442\\u043E \\u0438\\u0437\\u043E\\u0431\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u0435\",\n      button: \"\\u0418\\u0437\\u043D\\u0435\\u0441\\u0438 \\u043A\\u0430\\u0442\\u043E \\u0438\\u0437\\u043E\\u0431\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u0435\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0438 \\u043A\\u044A\\u043C \\u0434\\u0438\\u0441\\u043A\",\n      button: \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0438 \\u043A\\u044A\\u043C \\u0434\\u0438\\u0441\\u043A\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"\\u0415\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0438\\u0440\\u0430\\u0439 \\u043A\\u044A\\u043C Excalidraw+\",\n      description: \"\\u0417\\u0430\\u043F\\u0430\\u0437\\u0438 \\u0441\\u0446\\u0435\\u043D\\u0430\\u0442\\u0430 \\u043A\\u044A\\u043C Excalidraw+ \\u0440\\u0430\\u0431\\u043E\\u0442\\u043D\\u043E\\u0442\\u043E \\u043C\\u044F\\u0441\\u0442\\u043E.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\\u0417\\u0430\\u0440\\u0435\\u0434\\u0438 \\u043E\\u0442 \\u0444\\u0430\\u0439\\u043B\",\n      button: \"\\u0417\\u0430\\u0440\\u0435\\u0434\\u0438 \\u043E\\u0442 \\u0444\\u0430\\u0439\\u043B\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\\u0417\\u0430\\u0440\\u0435\\u0434\\u0438 \\u043E\\u0442 \\u043B\\u0438\\u043D\\u043A\",\n      button: \"\\u0417\\u0430\\u043C\\u0435\\u043D\\u0438 \\u043C\\u043E\\u0435\\u0442\\u043E \\u0441\\u044A\\u0434\\u044A\\u0440\\u0436\\u0430\\u043D\\u0438\\u0435\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar bg_BG_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=bg-BG-AAABLFCY.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/bg-BG-AAABLFCY.js\n"));

/***/ })

}]);