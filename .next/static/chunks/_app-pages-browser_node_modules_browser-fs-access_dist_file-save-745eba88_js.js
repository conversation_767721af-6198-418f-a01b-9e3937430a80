"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_browser-fs-access_dist_file-save-745eba88_js"],{

/***/ "(app-pages-browser)/./node_modules/browser-fs-access/dist/file-save-745eba88.js":
/*!*******************************************************************!*\
  !*** ./node_modules/browser-fs-access/dist/file-save-745eba88.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\nvar e=async(e,t=[{}],a=null,i=!1,n=null)=>{Array.isArray(t)||(t=[t]),t[0].fileName=t[0].fileName||\"Untitled\";const s=[];let c=null;if(e instanceof Blob&&e.type?c=e.type:e.headers&&e.headers.get(\"content-type\")&&(c=e.headers.get(\"content-type\")),t.forEach((e,t)=>{s[t]={description:e.description||\"\",accept:{}},e.mimeTypes?(0===t&&c&&e.mimeTypes.push(c),e.mimeTypes.map(a=>{s[t].accept[a]=e.extensions||[]})):c&&(s[t].accept[c]=e.extensions||[])}),a)try{await a.getFile()}catch(e){if(a=null,i)throw e}const r=a||await window.showSaveFilePicker({suggestedName:t[0].fileName,id:t[0].id,startIn:t[0].startIn,types:s,excludeAcceptAllOption:t[0].excludeAcceptAllOption||!1});!a&&n&&n();const l=await r.createWritable();if(\"stream\"in e){const t=e.stream();return await t.pipeTo(l),r}return\"body\"in e?(await e.body.pipeTo(l),r):(await l.write(await e),await l.close(),r)};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2ZpbGUtc2F2ZS03NDVlYmE4OC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsbUJBQW1CLHdCQUF3QixrRUFBa0UsV0FBVyxXQUFXLG9JQUFvSSxNQUFNLHdDQUF3QyxnRUFBZ0UsZ0NBQWdDLHdDQUF3QyxRQUFRLGtCQUFrQixTQUFTLG9CQUFvQiw0Q0FBNEMsMkhBQTJILEVBQUUsV0FBVyxpQ0FBaUMsaUJBQWlCLG1CQUFtQiwyQkFBMkIsd0ZBQTZHIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vbm9kZV9tb2R1bGVzL2Jyb3dzZXItZnMtYWNjZXNzL2Rpc3QvZmlsZS1zYXZlLTc0NWViYTg4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBlPWFzeW5jKGUsdD1be31dLGE9bnVsbCxpPSExLG49bnVsbCk9PntBcnJheS5pc0FycmF5KHQpfHwodD1bdF0pLHRbMF0uZmlsZU5hbWU9dFswXS5maWxlTmFtZXx8XCJVbnRpdGxlZFwiO2NvbnN0IHM9W107bGV0IGM9bnVsbDtpZihlIGluc3RhbmNlb2YgQmxvYiYmZS50eXBlP2M9ZS50eXBlOmUuaGVhZGVycyYmZS5oZWFkZXJzLmdldChcImNvbnRlbnQtdHlwZVwiKSYmKGM9ZS5oZWFkZXJzLmdldChcImNvbnRlbnQtdHlwZVwiKSksdC5mb3JFYWNoKChlLHQpPT57c1t0XT17ZGVzY3JpcHRpb246ZS5kZXNjcmlwdGlvbnx8XCJcIixhY2NlcHQ6e319LGUubWltZVR5cGVzPygwPT09dCYmYyYmZS5taW1lVHlwZXMucHVzaChjKSxlLm1pbWVUeXBlcy5tYXAoYT0+e3NbdF0uYWNjZXB0W2FdPWUuZXh0ZW5zaW9uc3x8W119KSk6YyYmKHNbdF0uYWNjZXB0W2NdPWUuZXh0ZW5zaW9uc3x8W10pfSksYSl0cnl7YXdhaXQgYS5nZXRGaWxlKCl9Y2F0Y2goZSl7aWYoYT1udWxsLGkpdGhyb3cgZX1jb25zdCByPWF8fGF3YWl0IHdpbmRvdy5zaG93U2F2ZUZpbGVQaWNrZXIoe3N1Z2dlc3RlZE5hbWU6dFswXS5maWxlTmFtZSxpZDp0WzBdLmlkLHN0YXJ0SW46dFswXS5zdGFydEluLHR5cGVzOnMsZXhjbHVkZUFjY2VwdEFsbE9wdGlvbjp0WzBdLmV4Y2x1ZGVBY2NlcHRBbGxPcHRpb258fCExfSk7IWEmJm4mJm4oKTtjb25zdCBsPWF3YWl0IHIuY3JlYXRlV3JpdGFibGUoKTtpZihcInN0cmVhbVwiaW4gZSl7Y29uc3QgdD1lLnN0cmVhbSgpO3JldHVybiBhd2FpdCB0LnBpcGVUbyhsKSxyfXJldHVyblwiYm9keVwiaW4gZT8oYXdhaXQgZS5ib2R5LnBpcGVUbyhsKSxyKTooYXdhaXQgbC53cml0ZShhd2FpdCBlKSxhd2FpdCBsLmNsb3NlKCkscil9O2V4cG9ydHtlIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/browser-fs-access/dist/file-save-745eba88.js\n"));

/***/ })

}]);