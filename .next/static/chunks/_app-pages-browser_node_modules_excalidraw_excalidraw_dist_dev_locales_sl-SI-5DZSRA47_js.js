"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_sl-SI-5DZSRA47_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/sl-SI-5DZSRA47.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/sl-SI-5DZSRA47.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ sl_SI_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/sl-SI.json\nvar labels = {\n  paste: \"Prilepi\",\n  pasteAsPlaintext: \"Prilepi kot navadno besedilo\",\n  pasteCharts: \"Prilepi grafikone\",\n  selectAll: \"Izberi vse\",\n  multiSelect: \"Dodaj element v izbor\",\n  moveCanvas: \"Premakni platno\",\n  cut: \"Izre\\u017Ei\",\n  copy: \"Kopiraj\",\n  copyAsPng: \"Kopiraj v odlo\\u017Ei\\u0161\\u010De kot PNG\",\n  copyAsSvg: \"Kopiraj v odlo\\u017Ei\\u0161\\u010De kot SVG\",\n  copyText: \"Kopiraj v odlo\\u017Ei\\u0161\\u010De kot besedilo\",\n  copySource: \"Kopiraj vir v odlo\\u017Ei\\u0161\\u010De\",\n  convertToCode: \"Pretvori v kodo\",\n  bringForward: \"Postavi naprej\",\n  sendToBack: \"Pomakni v ozadje\",\n  bringToFront: \"Pomakni v ospredje\",\n  sendBackward: \"Po\\u0161lji nazaj\",\n  delete: \"Izbri\\u0161i\",\n  copyStyles: \"Kopiraj slog\",\n  pasteStyles: \"Prilepi slog\",\n  stroke: \"Poteza\",\n  background: \"Ozadje\",\n  fill: \"Polnilo\",\n  strokeWidth: \"Debelina poteze\",\n  strokeStyle: \"Slog poteze\",\n  strokeStyle_solid: \"Polna\",\n  strokeStyle_dashed: \"\\u010Crtkana\",\n  strokeStyle_dotted: \"Pikasta\",\n  sloppiness: \"Povr\\u0161nost\",\n  opacity: \"Prekrivnost\",\n  textAlign: \"Poravnava besedila\",\n  edges: \"Robovi\",\n  sharp: \"Ostri\",\n  round: \"Okrogli\",\n  arrowheads: \"Pu\\u0161\\u010Dice\",\n  arrowhead_none: \"Brez\",\n  arrowhead_arrow: \"Pu\\u0161\\u010Dica\",\n  arrowhead_bar: \"Palica\",\n  arrowhead_circle: \"Krog\",\n  arrowhead_circle_outline: \"Krog (oris)\",\n  arrowhead_triangle: \"Trikotnik\",\n  arrowhead_triangle_outline: \"Trikotnik (oris)\",\n  arrowhead_diamond: \"Diamant\",\n  arrowhead_diamond_outline: \"Diamant (oris)\",\n  fontSize: \"Velikost pisave\",\n  fontFamily: \"Dru\\u017Eina pisave\",\n  addWatermark: 'Dodaj \"Izdelano z Excalidraw\"',\n  handDrawn: \"Ro\\u010Dno narisano\",\n  normal: \"Obi\\u010Dajno\",\n  code: \"Koda\",\n  small: \"Majhna\",\n  medium: \"Srednja\",\n  large: \"Velika\",\n  veryLarge: \"Zelo velika\",\n  solid: \"Polno\",\n  hachure: \"\\u0160rafura\",\n  zigzag: \"Cikcak\",\n  crossHatch: \"Kri\\u017Eno\",\n  thin: \"Tanko\",\n  bold: \"Krepko\",\n  left: \"Levo\",\n  center: \"Sredina\",\n  right: \"Desno\",\n  extraBold: \"Ekstra krepko\",\n  architect: \"Arhitekt\",\n  artist: \"Umetnik\",\n  cartoonist: \"Risar\",\n  fileTitle: \"Ime datoteke\",\n  colorPicker: \"Izbor barve\",\n  canvasColors: \"Uporabljeno na platnu\",\n  canvasBackground: \"Ozadje platna\",\n  drawingCanvas: \"Platno za risanje\",\n  layers: \"Plasti\",\n  actions: \"Dejanja\",\n  language: \"Jezik\",\n  liveCollaboration: \"Sodelovanje v \\u017Eivo...\",\n  duplicateSelection: \"Podvoji\",\n  untitled: \"Neimenovana\",\n  name: \"Ime\",\n  yourName: \"Va\\u0161e ime\",\n  madeWithExcalidraw: \"Izdelano z Excalidraw\",\n  group: \"Zdru\\u017Ei izbor\",\n  ungroup: \"Razdru\\u017Ei izbor\",\n  collaborators: \"Sodelavci\",\n  showGrid: \"Prika\\u017Ei mre\\u017Eo\",\n  addToLibrary: \"Dodaj v knji\\u017Enico\",\n  removeFromLibrary: \"Odstrani iz knji\\u017Enice\",\n  libraryLoadingMessage: \"Nalaganje knji\\u017Enice ...\",\n  libraries: \"Brskaj po knji\\u017Enicah\",\n  loadingScene: \"Nalaganje scene...\",\n  align: \"Poravnava\",\n  alignTop: \"Poravnaj na vrh\",\n  alignBottom: \"Poravnaj na dno\",\n  alignLeft: \"Poravnaj levo\",\n  alignRight: \"Poravnaj desno\",\n  centerVertically: \"Navpi\\u010Dno na sredini\",\n  centerHorizontally: \"Vodoravno na sredini\",\n  distributeHorizontally: \"Porazdeli vodoravno\",\n  distributeVertically: \"Porazdeli navpi\\u010Dno\",\n  flipHorizontal: \"Obrni vodoravno\",\n  flipVertical: \"Obrni navpi\\u010Dno\",\n  viewMode: \"Na\\u010Din ogleda\",\n  share: \"Deli\",\n  showStroke: \"Prika\\u017Ei izbirnik barv poteze\",\n  showBackground: \"Prika\\u017Ei izbirnik barv ozadja\",\n  toggleTheme: \"Obrni temo\",\n  personalLib: \"Osebna knji\\u017Enica\",\n  excalidrawLib: \"Knji\\u017Enica Excalidraw\",\n  decreaseFontSize: \"Zmanj\\u0161aj velikost pisave\",\n  increaseFontSize: \"Pove\\u010Daj velikost pisave\",\n  unbindText: \"Ve\\u017Ei besedilo\",\n  bindText: \"Ve\\u017Ei besedilo na element\",\n  createContainerFromText: \"Zavij besedilo v vsebnik\",\n  link: {\n    edit: \"Uredi povezavo\",\n    editEmbed: \"Uredi povezavo in vdelaj\",\n    create: \"Ustvari povezavo\",\n    createEmbed: \"Ustvari povezavo in vdelaj\",\n    label: \"Povezava\",\n    labelEmbed: \"Povezava in vdelovanje\",\n    empty: \"Povezava ni nastavljena\"\n  },\n  lineEditor: {\n    edit: \"Uredi \\u010Drto\",\n    exit: \"Zapri urejanje \\u010Drte\"\n  },\n  elementLock: {\n    lock: \"Zakleni\",\n    unlock: \"Odkleni\",\n    lockAll: \"Zakleni vse\",\n    unlockAll: \"Odkleni vse\"\n  },\n  statusPublished: \"Objavljeno\",\n  sidebarLock: \"Obdr\\u017Ei stransko vrstico odprto\",\n  selectAllElementsInFrame: \"Izberi vse elemente v okvirju\",\n  removeAllElementsFromFrame: \"Izbri\\u0161i vse elemente v okvirju\",\n  eyeDropper: \"Izberi barvo s platna\",\n  textToDiagram: \"Besedilo v diagram\",\n  prompt: \"Poziv\"\n};\nvar library = {\n  noItems: \"Dodan \\u0161e ni noben element...\",\n  hint_emptyLibrary: \"Izberite element na platnu, da ga dodate sem, ali namestite knji\\u017Enico iz javnega skladi\\u0161\\u010Da spodaj.\",\n  hint_emptyPrivateLibrary: \"Izberite element na platnu, da ga dodate sem.\"\n};\nvar buttons = {\n  clearReset: \"Ponastavi platno\",\n  exportJSON: \"Izvozi v datoteko\",\n  exportImage: \"Izvozi sliko...\",\n  export: \"Shrani v...\",\n  copyToClipboard: \"Kopiraj v odlo\\u017Ei\\u0161\\u010De\",\n  save: \"Shrani v trenutno datoteko\",\n  saveAs: \"Shrani kot\",\n  load: \"Odpri\",\n  getShareableLink: \"Pridobi povezavo za deljenje\",\n  close: \"Zapri\",\n  selectLanguage: \"Izberi jezik\",\n  scrollBackToContent: \"Pomakni se nazaj na vsebino\",\n  zoomIn: \"Pove\\u010Daj\",\n  zoomOut: \"Pomanj\\u0161aj\",\n  resetZoom: \"Ponastavi pove\\u010Davo\",\n  menu: \"Meni\",\n  done: \"Kon\\u010Dano\",\n  edit: \"Uredi\",\n  undo: \"Razveljavi\",\n  redo: \"Ponovi\",\n  resetLibrary: \"Ponastavi knji\\u017Enico\",\n  createNewRoom: \"Ustvari novo sobo\",\n  fullScreen: \"Celozaslonski na\\u010Din\",\n  darkMode: \"Temni na\\u010Din\",\n  lightMode: \"Svetli na\\u010Din\",\n  zenMode: \"Na\\u010Din Zen\",\n  objectsSnapMode: \"Pripenjanje na predmete\",\n  exitZenMode: \"Zapri na\\u010Din Zen\",\n  cancel: \"Prekli\\u010Di\",\n  clear: \"Po\\u010Disti\",\n  remove: \"Odstrani\",\n  embed: \"Preklopi vdelavo\",\n  publishLibrary: \"Objavi\",\n  submit: \"Po\\u0161lji\",\n  confirm: \"Potrdi\",\n  embeddableInteractionButton: \"Kliknite za interakcijo\"\n};\nvar alerts = {\n  clearReset: \"To bo po\\u010Distilo celotno platno. Ali ste prepri\\u010Dani?\",\n  couldNotCreateShareableLink: \"Povezave za deljenje ni bilo mogo\\u010De ustvariti.\",\n  couldNotCreateShareableLinkTooBig: \"Povezave za deljenje ni bilo mogo\\u010De ustvariti: scena je prevelika\",\n  couldNotLoadInvalidFile: \"Neveljavne datoteke ni bilo mogo\\u010De nalo\\u017Eiti\",\n  importBackendFailed: \"Uvoz iz zaledja ni uspel.\",\n  cannotExportEmptyCanvas: \"Izvoz prazne scene ni mogo\\u010D.\",\n  couldNotCopyToClipboard: \"Kopiranje v odlo\\u017Ei\\u0161\\u010De ni uspelo.\",\n  decryptFailed: \"De\\u0161ifriranje podatkov ni uspelo.\",\n  uploadedSecurly: \"Nalaganje je bilo za\\u0161\\u010Diteno s \\u0161ifriranjem od konca do konca, kar pomeni, da stre\\u017Enik Excalidraw in tretje osebe ne morejo brati vsebine.\",\n  loadSceneOverridePrompt: \"Nalaganje zunanje risbe bo nadomestilo va\\u0161o obstoje\\u010Do vsebino. Ali \\u017Eelite nadaljevati?\",\n  collabStopOverridePrompt: \"Ustavitev seje bo prepisala va\\u0161o prej\\u0161njo, lokalno shranjeno risbo. Ali ste prepri\\u010Dani?\\n\\n(\\u010Ce \\u017Eelite obdr\\u017Eati lokalno risbo, preprosto zaprite zavihek brskalnika.)\",\n  errorAddingToLibrary: \"Elementa ni bilo mogo\\u010De dodati v knji\\u017Enico\",\n  errorRemovingFromLibrary: \"Elementa ni bilo mogo\\u010De izbrisati iz knji\\u017Enice\",\n  confirmAddLibrary: \"S tem boste v va\\u0161o knji\\u017Enico dodali oblike ({{numShapes}}). Ali ste prepri\\u010Dani?\",\n  imageDoesNotContainScene: \"Zdi se, da ta slika ne vsebuje podatkov o sceni. Ali ste med izvozom omogo\\u010Dili vdelavo scene?\",\n  cannotRestoreFromImage: \"Scene ni bilo mogo\\u010De obnoviti iz te slikovne datoteke\",\n  invalidSceneUrl: \"S prilo\\u017Eenega URL-ja ni bilo mogo\\u010De uvoziti scene. Je napa\\u010Dno oblikovana ali pa ne vsebuje veljavnih podatkov Excalidraw JSON.\",\n  resetLibrary: \"To bo po\\u010Distilo va\\u0161o knji\\u017Enico. Ali ste prepri\\u010Dani?\",\n  removeItemsFromsLibrary: \"Izbri\\u0161i elemente ({{count}}) iz knji\\u017Enice?\",\n  invalidEncryptionKey: \"Klju\\u010D za \\u0161ifriranje mora vsebovati 22 znakov. Sodelovanje v \\u017Eivo je onemogo\\u010Deno.\",\n  collabOfflineWarning: \"Internetna povezava ni na voljo.\\nVa\\u0161e spremembe ne bodo shranjene!\"\n};\nvar errors = {\n  unsupportedFileType: \"Nepodprt tip datoteke.\",\n  imageInsertError: \"Vstavljanje slike ni bilo uspe\\u0161no. Poskusite ponovno kasneje...\",\n  fileTooBig: \"Datoteka je prevelika. Najve\\u010Dja dovoljena velikost je {{maxSize}}.\",\n  svgImageInsertError: \"Vstavljanje slike SVG ni uspelo. Oznake SVG so videti neveljavne.\",\n  failedToFetchImage: \"Pridobivanje slike ni uspelo.\",\n  invalidSVGString: \"Neveljaven SVG.\",\n  cannotResolveCollabServer: \"Povezave s stre\\u017Enikom za sodelovanje ni bilo mogo\\u010De vzpostaviti. Ponovno nalo\\u017Eite stran in poskusite znova.\",\n  importLibraryError: \"Nalaganje knji\\u017Enice ni uspelo\",\n  collabSaveFailed: \"Ni bilo mogo\\u010De shraniti v zaledno bazo podatkov. \\u010Ce se te\\u017Eave nadaljujejo, shranite datoteko lokalno, da ne boste izgubili svojega dela.\",\n  collabSaveFailed_sizeExceeded: \"Ni bilo mogo\\u010De shraniti v zaledno bazo podatkov, zdi se, da je platno preveliko. Datoteko shranite lokalno, da ne izgubite svojega dela.\",\n  imageToolNotSupported: \"Slike so onemogo\\u010Dene.\",\n  brave_measure_text_error: {\n    line1: \"Videti je, da uporabljate brskalnik Brave z omogo\\u010Deno nastavitvijo <bold>Agresivno blokiranje prstnih odtisov</bold>.\",\n    line2: \"To bi lahko povzro\\u010Dilo motnje v obna\\u0161anju <bold>besedilnih elementov</bold> v va\\u0161ih risbah.\",\n    line3: \"Mo\\u010Dno priporo\\u010Damo, da onemogo\\u010Dite to nastavitev. Sledite <link>tem korakom</link>, kako to storiti.\",\n    line4: \"\\u010Ce onemogo\\u010Danje te nastavitve ne popravi prikaza besedilnih elementov, odprite <issueLink>vpra\\u0161anje</issueLink> na na\\u0161em GitHubu ali nam pi\\u0161ite na <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Vdelani elementi ne morejo biti dodani v knji\\u017Enico.\",\n    iframe: \"Elementov iFrame ni mogo\\u010De dodati v knji\\u017Enico.\",\n    image: \"Podpora za dodajanje slik v knji\\u017Enico prihaja kmalu!\"\n  },\n  asyncPasteFailedOnRead: \"Ni bilo mogo\\u010De prilepiti (ni bilo mogo\\u010De brati iz sistemskega odlo\\u017Ei\\u0161\\u010Da).\",\n  asyncPasteFailedOnParse: \"Ni bilo mogo\\u010De prilepiti.\",\n  copyToSystemClipboardFailed: \"Ni bilo mogo\\u010De kopirati v odlo\\u017Ei\\u0161\\u010De.\"\n};\nvar toolBar = {\n  selection: \"Izbor\",\n  image: \"Vstavi sliko\",\n  rectangle: \"Pravokotnik\",\n  diamond: \"Diamant\",\n  ellipse: \"Elipsa\",\n  arrow: \"Pu\\u0161\\u010Dica\",\n  line: \"\\u010Crta\",\n  freedraw: \"Risanje\",\n  text: \"Besedilo\",\n  library: \"Knji\\u017Enica\",\n  lock: \"Ohrani izbrano orodje aktivno po risanju\",\n  penMode: \"Na\\u010Din peresa - prepre\\u010Di dotik\",\n  link: \"Dodaj/posodobi povezavo za izbrano obliko\",\n  eraser: \"Radirka\",\n  frame: \"Okvir\",\n  magicframe: \"\\u017Di\\u010Dni okvir v kodo\",\n  embeddable: \"Spletna vdelava\",\n  laser: \"Laserski kazalec\",\n  hand: \"Roka (orodje za premikanje)\",\n  extraTools: \"Ve\\u010D orodij\",\n  mermaidToExcalidraw: \"Mermaid v Excalidraw\",\n  magicSettings: \"Nastavitve AI\"\n};\nvar headings = {\n  canvasActions: \"Dejanja za platno\",\n  selectedShapeActions: \"Dejanja za izbrane oblike\",\n  shapes: \"Oblike\"\n};\nvar hints = {\n  canvasPanning: \"Za premikanje platna med vle\\u010Denjem dr\\u017Eite kolesce mi\\u0161ke ali preslednico ali uporabite orodje roka\",\n  linearElement: \"Kliknite za za\\u010Detek ve\\u010D to\\u010Dk, povlecite za posamezno \\u010Drto\",\n  freeDraw: \"Kliknite in povlecite, spustite, ko kon\\u010Date\",\n  text: \"Namig: besedilo lahko dodate tudi z dvoklikom kjer koli z orodjem za izbiro\",\n  embeddable: \"Kliknite in povlecite, da ustvarite spletno vdelavo\",\n  text_selected: \"Dvokliknite ali pritisnite tipko Enter, da uredite besedilo\",\n  text_editing: \"Pritisnite tipko Escape ali CtrlOrCmd+Enter za zaklju\\u010Dek urejanja\",\n  linearElementMulti: \"Kliknite zadnjo to\\u010Dko ali pritisnite Escape ali Enter, da kon\\u010Date\",\n  lockAngle: \"Kot lahko omejite tako, da dr\\u017Eite tipko Shift\",\n  resize: \"Razmerja lahko omejite tako, da dr\\u017Eite tipko Shift med spreminjanjem velikosti. Dr\\u017Eite tipko Alt, da spremenite velikost od sredi\\u0161\\u010Da\",\n  resizeImage: \"Velikost lahko prosto spreminjate tako, da dr\\u017Eite tipko Shift. Dr\\u017Eite tipko Alt, da spremenite velikost od sredi\\u0161\\u010Da\",\n  rotate: \"Kote lahko omejite tako, da med vrtenjem dr\\u017Eite tipko Shift\",\n  lineEditor_info: \"Dr\\u017Eite CtrlOrCmd in dvokliknite ali pritisnite CtrlOrCmd + Enter za urejanje to\\u010Dk\",\n  lineEditor_pointSelected: \"Pritisnite tipko Delete, da odstranite to\\u010Dko(e), CtrlOrCmd+D za podvojitev ali povlecite za premikanje\",\n  lineEditor_nothingSelected: \"Izberite to\\u010Dko za urejanje (pridr\\u017Eite tipko Shift za izbiro ve\\u010D to\\u010Dk), ali dr\\u017Eite tipko Alt in kliknite za dodajanje novih to\\u010Dk\",\n  placeImage: \"Kliknite, da postavite sliko, ali kliknite in povlecite, da ro\\u010Dno nastavite njeno velikost\",\n  publishLibrary: \"Objavi svojo knji\\u017Enico\",\n  bindTextToElement: \"Pritisnite tipko Enter za dodajanje besedila\",\n  deepBoxSelect: \"Dr\\u017Eite tipko CtrlOrCmd za globoko izbiro in prepre\\u010Ditev vle\\u010Denja\",\n  eraserRevert: \"Pridr\\u017Eite tipko Alt, da razveljavite elemente, ozna\\u010Dene za brisanje\",\n  firefox_clipboard_write: 'To funkcijo lahko verjetno omogo\\u010Dite z nastavitvijo zastavice \"dom.events.asyncClipboard.clipboardItem\" na \"true\". \\u010Ce \\u017Eelite spremeniti zastavice brskalnika v Firefoxu, obi\\u0161\\u010Dite stran \"about:config\".',\n  disableSnapping: \"Dr\\u017Eite CtrlOrCmd, da onemogo\\u010Dite pripenjanje\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Predogleda ni bilo mogo\\u010De prikazati\",\n  canvasTooBig: \"Morda je platno preveliko.\",\n  canvasTooBigTip: \"Nasvet: poskusite premakniti najbolj oddaljene elemente nekoliko bli\\u017Eje skupaj.\"\n};\nvar errorSplash = {\n  headingMain: \"Pri\\u0161lo je do napake. Poskusite <button>ponovno nalo\\u017Eiti stran.</button>\",\n  clearCanvasMessage: \"\\u010Ce ponovno nalaganje ne deluje, poskusite <button>po\\u010Distiti platno.</button>\",\n  clearCanvasCaveat: \" To bo povzro\\u010Dilo izgubo dela \",\n  trackedToSentry: \"Napaka z identifikatorjem {{eventId}} smo zabele\\u017Eili v na\\u0161 sistem.\",\n  openIssueMessage: \"Zelo smo bili previdni, da v podatke o napaki nismo vklju\\u010Dili va\\u0161ih podatkov o sceni. \\u010Ce va\\u0161a scena ni zasebna, vas prosimo, da napi\\u0161ete ve\\u010D podrobnosti na na\\u0161em <button>sledilniku hro\\u0161\\u010Dev.</button> Prosimo, vklju\\u010Dite spodnje informacije tako, da jih kopirate in prilepite v GitHub vpra\\u0161anje.\",\n  sceneContent: \"Vsebina scene:\"\n};\nvar roomDialog = {\n  desc_intro: \"Na va\\u0161o trenutno sceno lahko povabite osebe, ki bodo sodelovale z vami.\",\n  desc_privacy: \"Brez skrbi. Seja uporablja \\u0161ifriranje od konca do konca, tako da bo vse, kar nari\\u0161ete, ostalo zasebno. Niti na\\u0161 stre\\u017Enik ne bo mogel videti, kaj si izmislite.\",\n  button_startSession: \"Za\\u010Dni sejo\",\n  button_stopSession: \"Ustavi sejo\",\n  desc_inProgressIntro: \"Seja sodelovanja v \\u017Eivo je v teku.\",\n  desc_shareLink: \"Delite to povezavo z vsemi, s katerimi \\u017Eelite sodelovati:\",\n  desc_exitSession: \"Ustavitev seje vas bo odklopila od sobe, vendar boste lahko lokalno nadaljevali delo s sceno. To pa ne bo vplivalo na druge osebe. Druge osebe bodo \\u0161e vedno lahko sodelovale v svoji razli\\u010Dici.\",\n  shareTitle: \"Pridru\\u017Ei se seji sodelovanja v \\u017Eivo na Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Napaka\"\n};\nvar exportDialog = {\n  disk_title: \"Shrani na disk\",\n  disk_details: \"Izvozite podatke scene v datoteko, iz katere jo lahko pozneje uvozite.\",\n  disk_button: \"Shrani v datoteko\",\n  link_title: \"Povezava za deljenje\",\n  link_details: \"Izvoz kot povezava samo za branje.\",\n  link_button: \"Izvoz kot povezava\",\n  excalidrawplus_description: \"Shrani sceno v svoj delovni prostor Excalidraw+.\",\n  excalidrawplus_button: \"Izvoz\",\n  excalidrawplus_exportError: \"Trenutno ni bilo mogo\\u010De izvoziti v Excalidraw+...\"\n};\nvar helpDialog = {\n  blog: \"Preberite na\\u0161 blog\",\n  click: \"klik\",\n  deepSelect: \"Globoka izbira\",\n  deepBoxSelect: \"Globoka izbira znotraj polja in prepre\\u010Ditev vle\\u010Denja\",\n  curvedArrow: \"Ukrivljena pu\\u0161\\u010Dica\",\n  curvedLine: \"Ukrivljena \\u010Drta\",\n  documentation: \"Dokumentacija\",\n  doubleClick: \"dvojni klik\",\n  drag: \"vleci\",\n  editor: \"Urejevalnik\",\n  editLineArrowPoints: \"Uredi \\u010Drto/to\\u010Dke pu\\u0161\\u010Dice\",\n  editText: \"Uredi besedilo / dodaj oznako\",\n  github: \"Ste na\\u0161li te\\u017Eavo? Po\\u0161ljite\",\n  howto: \"Sledite na\\u0161im vodi\\u010Dem\",\n  or: \"ali\",\n  preventBinding: \"Prepre\\u010Di vezanje pu\\u0161\\u010Dice\",\n  tools: \"Orodja\",\n  shortcuts: \"Bli\\u017Enjice na tipkovnici\",\n  textFinish: \"Zaklju\\u010Di urejanje (urejevalnik besedila)\",\n  textNewLine: \"Dodaj novo vrstico (urejevalnik besedila)\",\n  title: \"Pomo\\u010D\",\n  view: \"Pogled\",\n  zoomToFit: \"Pribli\\u017Eaj na vse elemente\",\n  zoomToSelection: \"Pribli\\u017Eaj na izbor\",\n  toggleElementLock: \"Zakleni/odkleni izbor\",\n  movePageUpDown: \"Premakni stran gor/dol\",\n  movePageLeftRight: \"Premakni stran levo/desno\"\n};\nvar clearCanvasDialog = {\n  title: \"Po\\u010Disti platno\"\n};\nvar publishDialog = {\n  title: \"Objavi knji\\u017Enico\",\n  itemName: \"Ime elementa\",\n  authorName: \"Ime avtorja\",\n  githubUsername: \"GitHub uporabni\\u0161ko ime\",\n  twitterUsername: \"Twitter uporabni\\u0161ko ime\",\n  libraryName: \"Ime knji\\u017Enice\",\n  libraryDesc: \"Opis knij\\u017Enice\",\n  website: \"Spletna stran\",\n  placeholder: {\n    authorName: \"Va\\u0161e ime ali uporabni\\u0161ko ime\",\n    libraryName: \"Ime va\\u0161e knji\\u017Enice\",\n    libraryDesc: \"Opis va\\u0161e knji\\u017Enice, da bodo ljudje la\\u017Eje razumeli njeno uporabo\",\n    githubHandle: \"GitHub uporabni\\u0161ko ime (neobvezno), tako da lahko urejate knji\\u017Enico potem, ko jo po\\u0161ljete v pregled\",\n    twitterHandle: \"Twitter uporabni\\u0161ko ime (neobvezno), tako da vemo, koga omeniti pri promociji prek Twitterja\",\n    website: \"Povezava na va\\u0161o osebno spletno stran ali drugam (neobvezno)\"\n  },\n  errors: {\n    required: \"Obvezno\",\n    website: \"Vnesite veljaven URL\"\n  },\n  noteDescription: \"Predlo\\u017Eite svojo knji\\u017Enico, da bo vklju\\u010Dena v <link>javno skladi\\u0161\\u010De knji\\u017Enic,</link>da jih drugi lahko uporabljajo v svojih risbah.\",\n  noteGuidelines: \"Knji\\u017Enica mora biti najprej ro\\u010Dno odobrena. Prosimo vas, da pred oddajanjem preberete na\\u0161e <link>smernice.</link>Za komunikacijo in spreminjanje po potrebi boste potrebovali ra\\u010Dun GitHub, vendar to ni obvezno.\",\n  noteLicense: \"Z oddajo se strinjate, da bo knji\\u017Enica objavljena pod <link>licenco MIT, </link>kar na kratko pomeni, da jo lahko kdorkoli uporablja brez omejitev.\",\n  noteItems: \"Vsak element knji\\u017Enice mora imeti svoje ime, tako da ga je mogo\\u010De filtrirati. Vklju\\u010Deni bodo naslednji elementi knji\\u017Enice:\",\n  atleastOneLibItem: \"Za za\\u010Detek izberite vsaj en element knji\\u017Enice\",\n  republishWarning: \"Opomba: nekateri izbrani predmeti so ozna\\u010Deni kot \\u017Ee objavljeni/oddani. Elemente lahko znova oddate samo, ko posodabljate obstoje\\u010Do knji\\u017Enico ali oddajo.\"\n};\nvar publishSuccessDialog = {\n  title: \"Knji\\u017Enica oddana\",\n  content: \"{{authorName}}, hvala. Va\\u0161a knji\\u017Enica je bila poslana v pregled. Stanje lahko spremljate<link>tukaj</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Ponastavi knji\\u017Enico\",\n  removeItemsFromLib: \"Odstran izbrane elemente iz knji\\u017Enice\"\n};\nvar imageExportDialog = {\n  header: \"Izvozi sliko\",\n  label: {\n    withBackground: \"Ozadje\",\n    onlySelected: \"Samo izbor\",\n    darkMode: \"Temni na\\u010Din\",\n    embedScene: \"Vdelaj sceno\",\n    scale: \"Pove\\u010Dava\",\n    padding: \"Odmik\"\n  },\n  tooltip: {\n    embedScene: \"Podatki o sceni bodo shranjeni v izvo\\u017Eeno datoteko PNG/SVG, tako da bo sceno mogo\\u010De obnoviti iz nje.\\nTo bo pove\\u010Dalo velikost izvo\\u017Eene datoteke.\"\n  },\n  title: {\n    exportToPng: \"Izvozi v PNG\",\n    exportToSvg: \"Izvozi v SVG\",\n    copyPngToClipboard: \"Kopiraj PNG v odlo\\u017Ei\\u0161\\u010De\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Kopiraj v odlo\\u017Ei\\u0161\\u010De\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Va\\u0161e risbe so \\u0161ifrirane od konca do konca, tako da jih stre\\u017Eniki Excalidraw nikoli ne bodo videli.\",\n  link: \"Blog objava o \\u0161ifriranju od konca do konca v Excalidraw\"\n};\nvar stats = {\n  angle: \"Kot\",\n  element: \"Element\",\n  elements: \"Elementi\",\n  height: \"Vi\\u0161ina\",\n  scene: \"Scena\",\n  selected: \"Izbrano\",\n  storage: \"Shramba\",\n  title: \"Statistika za napredne uporabnike\",\n  total: \"Skupaj\",\n  version: \"Razli\\u010Dica\",\n  versionCopy: \"Kliknite za kopiranje\",\n  versionNotAvailable: \"Razli\\u010Dica ni na voljo\",\n  width: \"\\u0160irina\"\n};\nvar toast = {\n  addedToLibrary: \"Dodano v knji\\u017Enico\",\n  copyStyles: \"Slogi kopirani.\",\n  copyToClipboard: \"Kopirano v odlo\\u017Ei\\u0161\\u010De.\",\n  copyToClipboardAsPng: \"Kopirano v odlo\\u017Ei\\u0161\\u010De kot PNG ({{exportSelection}}, {{exportColorScheme}})\",\n  fileSaved: \"Datoteka shranjena.\",\n  fileSavedToFilename: \"Shranjeno v {filename}\",\n  canvas: \"platno\",\n  selection: \"izbor\",\n  pasteAsSingleElement: \"Uporabite {{shortcut}}, da prilepite kot en element,\\n ali prilepite v obstoje\\u010D urejevalnik besedil\",\n  unableToEmbed: \"Vdelava tega URL-ja trenutno ni dovoljena. Ustvarite vpra\\u0161anje na GitHub-u in prosite za vmestitev URL-ja na seznam dovoljenih\",\n  unrecognizedLinkFormat: \"Povezava, ki ste jo vdelali, se ne ujema s pri\\u010Dakovano obliko. Poskusite prilepiti niz za vdelavo, ki ste ga prejeli na izvorni strani\"\n};\nvar colors = {\n  transparent: \"Prosojno\",\n  black: \"\\u010Crna\",\n  white: \"Bela\",\n  red: \"Rde\\u010Da\",\n  pink: \"Roza\",\n  grape: \"Grozdje\",\n  violet: \"Vijoli\\u010Dna\",\n  gray: \"Siva\",\n  blue: \"Modra\",\n  cyan: \"Cijan\",\n  teal: \"Turkizna\",\n  green: \"Zelena\",\n  yellow: \"Rumena\",\n  orange: \"Oran\\u017Ena\",\n  bronze: \"Bronasta\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Vsi va\\u0161i podatki so shranjeni lokalno v va\\u0161em brskalniku.\",\n    center_heading_plus: \"Ste namesto tega \\u017Eeleli odpreti Excalidraw+?\",\n    menuHint: \"Izvoz, nastavitve, jeziki, ...\"\n  },\n  defaults: {\n    menuHint: \"Izvoz, nastavitve in ve\\u010D ...\",\n    center_heading: \"Diagrami. Enostavno.\",\n    toolbarHint: \"Izberi orodje in za\\u010Dni z risanjem!\",\n    helpHint: \"Bli\\u017Enjice in pomo\\u010D\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Najpogosteje uporabljene barve po meri\",\n  colors: \"Barve\",\n  shades: \"Odtenki\",\n  hexCode: \"Hex koda\",\n  noShades: \"Odtenki za to barvo niso na voljo\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Izvozi kot sliko\",\n      button: \"Izvozi kot sliko\",\n      description: \"Izvozite podatke scene kot sliko, iz katere jo lahko pozneje uvozite.\"\n    },\n    saveToDisk: {\n      title: \"Shrani na disk\",\n      button: \"Shrani na disk\",\n      description: \"Izvozite podatke scene v datoteko, iz katere jo lahko pozneje uvozite.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Izvozi v Excalidraw+\",\n      description: \"Shrani sceno v svoj delovni prostor Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Nalo\\u017Ei iz datoteke\",\n      button: \"Nalo\\u017Ei iz datoteke\",\n      description: \"Nalaganje iz datoteke bo <bold>prepisalo va\\u0161o obstoje\\u010Do vsebino</bold>.<br></br>Svojo risbo lahko najprej varnostno kopirate z eno od spodnjih mo\\u017Enosti.\"\n    },\n    shareableLink: {\n      title: \"Nalo\\u017Ei iz povezave\",\n      button: \"Zamenjaj mojo vsebino\",\n      description: \"Nalaganje zunanje risbe bo <bold>prepisalo va\\u0161o obstoje\\u010Do vsebino</bold>.<br></br>Svojo risbo lahko najprej varnostno kopirate z eno od spodnjih mo\\u017Enosti.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid v Excalidraw\",\n  button: \"Vstavi\",\n  description: \"Trenutno so podprti samo <flowchartLink>diagrami poteka</flowchartLink>, <sequenceLink>diagrami zaporedij</sequenceLink> in <classLink>Razredni diagrami</classLink>. Druge vrste bodo upodobljene kot slike v Excalidraw.\",\n  syntax: \"Sintaksa Mermaid\",\n  preview: \"Predogled\"\n};\nvar sl_SI_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=sl-SI-5DZSRA47.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/sl-SI-5DZSRA47.js\n"));

/***/ })

}]);