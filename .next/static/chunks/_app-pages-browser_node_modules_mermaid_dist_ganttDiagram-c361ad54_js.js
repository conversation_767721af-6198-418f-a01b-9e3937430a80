/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_ganttDiagram-c361ad54_js"],{

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js":
/*!*****************************************************!*\
  !*** ./node_modules/dayjs/plugin/advancedFormat.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";return function(e,t){var r=t.prototype,n=r.format;r.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return n.bind(this)(e);var s=this.$utils(),a=(e||\"YYYY-MM-DDTHH:mm:ssZ\").replace(/\\[([^\\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case\"Q\":return Math.ceil((t.$M+1)/3);case\"Do\":return r.ordinal(t.$D);case\"gggg\":return t.weekYear();case\"GGGG\":return t.isoWeekYear();case\"wo\":return r.ordinal(t.week(),\"W\");case\"w\":case\"ww\":return s.s(t.week(),\"w\"===e?1:2,\"0\");case\"W\":case\"WW\":return s.s(t.isoWeek(),\"W\"===e?1:2,\"0\");case\"k\":case\"kk\":return s.s(String(0===t.$H?24:t.$H),\"k\"===e?1:2,\"0\");case\"X\":return Math.floor(t.$d.getTime()/1e3);case\"x\":return t.$d.getTime();case\"z\":return\"[\"+t.offsetName()+\"]\";case\"zzz\":return\"[\"+t.offsetName(\"long\")+\"]\";default:return e}}));return n.bind(this)(a)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js":
/*!********************************************************!*\
  !*** ./node_modules/dayjs/plugin/customParseFormat.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e={LTS:\"h:mm:ss A\",LT:\"h:mm A\",L:\"MM/DD/YYYY\",LL:\"MMMM D, YYYY\",LLL:\"MMMM D, YYYY h:mm A\",LLLL:\"dddd, MMMM D, YYYY h:mm A\"},t=/(\\[[^[]*\\])|([-_:/.,()\\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\\d/,r=/\\d\\d/,i=/\\d\\d?/,o=/\\d*[^-_:/,()\\s\\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)};var f=function(e){return function(t){this[e]=+t}},h=[/[+-]\\d\\d:?(\\d\\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if(\"Z\"===e)return 0;var t=e.match(/([+-]|\\d\\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:\"+\"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?\"pm\":\"PM\");return n},c={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\\d{3}/,function(e){this.milliseconds=+e}],s:[i,f(\"seconds\")],ss:[i,f(\"seconds\")],m:[i,f(\"minutes\")],mm:[i,f(\"minutes\")],H:[i,f(\"hours\")],h:[i,f(\"hours\")],HH:[i,f(\"hours\")],hh:[i,f(\"hours\")],D:[i,f(\"day\")],DD:[r,f(\"day\")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\\[|\\]/g,\"\")===e&&(this.day=r)}],w:[i,f(\"week\")],ww:[r,f(\"week\")],M:[i,f(\"month\")],MM:[r,f(\"month\")],MMM:[o,function(e){var t=u(\"months\"),n=(u(\"monthsShort\")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u(\"months\").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\\d+/,f(\"year\")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\\d{4}/,f(\"year\")],Z:h,ZZ:h};function l(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\\[[^\\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\\[[^\\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,f=0;f<a;f+=1){var h=o[f],u=c[h],d=u&&u[0],l=u&&u[1];o[f]=l?{regex:d,parser:l}:h.replace(/^\\[|\\]$/g,\"\")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if(\"string\"==typeof i)r+=i.length;else{var s=i.regex,f=i.parser,h=e.slice(r),u=s.exec(h)[0];f.call(t,u),e=e.replace(u,\"\")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if(\"string\"==typeof a){var f=!0===o[2],h=!0===o[3],u=f||h,d=o[2];h&&(d=o[2]),s=this.$locale(),!f&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if([\"x\",\"X\"].indexOf(t)>-1)return new Date((\"X\"===t?1e3:1)*e);var i=l(t)(e),o=i.year,s=i.month,a=i.day,f=i.hours,h=i.minutes,u=i.seconds,d=i.milliseconds,c=i.zone,m=i.week,M=new Date,Y=a||(o||s?1:M.getDate()),p=o||M.getFullYear(),v=0;o&&!s||(v=s>0?s-1:M.getMonth());var D,w=f||0,g=h||0,y=u||0,L=d||0;return c?new Date(Date.UTC(p,v,Y,w,g,y,L+60*c.offset*1e3)):n?new Date(Date.UTC(p,v,Y,w,g,y,L)):(D=new Date(p,v,Y,w,g,y,L),m&&(D=r(D).week(m).toDate()),D)}catch(e){return new Date(\"\")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date(\"\")),s={}}else if(a instanceof Array)for(var c=a.length,m=1;m<=c;m+=1){o[1]=a[m-1];var M=n.apply(this,o);if(M.isValid()){this.$d=M.$d,this.$L=M.$L,this.init();break}m===c&&(this.$d=new Date(\"\"))}else i.call(this,e)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js":
/*!**********************************************!*\
  !*** ./node_modules/dayjs/plugin/isoWeek.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("!function(e,t){ true?module.exports=t():0}(this,(function(){\"use strict\";var e=\"day\";return function(t,i,s){var a=function(t){return t.add(4-t.isoWeekday(),e)},d=i.prototype;d.isoWeekYear=function(){return a(this).year()},d.isoWeek=function(t){if(!this.$utils().u(t))return this.add(7*(t-this.isoWeek()),e);var i,d,n,o,r=a(this),u=(i=this.isoWeekYear(),d=this.$u,n=(d?s.utc:s)().year(i).startOf(\"year\"),o=4-n.isoWeekday(),n.isoWeekday()>4&&(o+=7),n.add(o,e));return r.diff(u,\"week\")+1},d.isoWeekday=function(e){return this.$utils().u(e)?this.day()||7:this.day(this.day()%7?e:e-7)};var n=d.startOf;d.startOf=function(e,t){var i=this.$utils(),s=!!i.u(t)||t;return\"isoweek\"===i.p(e)?s?this.date(this.date()-(this.isoWeekday()-1)).startOf(\"day\"):this.date(this.date()-1-(this.isoWeekday()-1)+7).endOf(\"day\"):n.bind(this)(e,t)}}}));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNvV2Vlay5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLEtBQW9ELG9CQUFvQixDQUE4SCxDQUFDLGtCQUFrQixhQUFhLFlBQVksdUJBQXVCLGtCQUFrQixpQ0FBaUMsZUFBZSx5QkFBeUIsc0JBQXNCLHVCQUF1QiwrREFBK0Qsd0pBQXdKLDBCQUEwQiwwQkFBMEIsc0VBQXNFLGdCQUFnQix3QkFBd0Isa0NBQWtDLHlLQUF5SyIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9kYXlqcy9wbHVnaW4vaXNvV2Vlay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIhZnVuY3Rpb24oZSx0KXtcIm9iamVjdFwiPT10eXBlb2YgZXhwb3J0cyYmXCJ1bmRlZmluZWRcIiE9dHlwZW9mIG1vZHVsZT9tb2R1bGUuZXhwb3J0cz10KCk6XCJmdW5jdGlvblwiPT10eXBlb2YgZGVmaW5lJiZkZWZpbmUuYW1kP2RlZmluZSh0KTooZT1cInVuZGVmaW5lZFwiIT10eXBlb2YgZ2xvYmFsVGhpcz9nbG9iYWxUaGlzOmV8fHNlbGYpLmRheWpzX3BsdWdpbl9pc29XZWVrPXQoKX0odGhpcywoZnVuY3Rpb24oKXtcInVzZSBzdHJpY3RcIjt2YXIgZT1cImRheVwiO3JldHVybiBmdW5jdGlvbih0LGkscyl7dmFyIGE9ZnVuY3Rpb24odCl7cmV0dXJuIHQuYWRkKDQtdC5pc29XZWVrZGF5KCksZSl9LGQ9aS5wcm90b3R5cGU7ZC5pc29XZWVrWWVhcj1mdW5jdGlvbigpe3JldHVybiBhKHRoaXMpLnllYXIoKX0sZC5pc29XZWVrPWZ1bmN0aW9uKHQpe2lmKCF0aGlzLiR1dGlscygpLnUodCkpcmV0dXJuIHRoaXMuYWRkKDcqKHQtdGhpcy5pc29XZWVrKCkpLGUpO3ZhciBpLGQsbixvLHI9YSh0aGlzKSx1PShpPXRoaXMuaXNvV2Vla1llYXIoKSxkPXRoaXMuJHUsbj0oZD9zLnV0YzpzKSgpLnllYXIoaSkuc3RhcnRPZihcInllYXJcIiksbz00LW4uaXNvV2Vla2RheSgpLG4uaXNvV2Vla2RheSgpPjQmJihvKz03KSxuLmFkZChvLGUpKTtyZXR1cm4gci5kaWZmKHUsXCJ3ZWVrXCIpKzF9LGQuaXNvV2Vla2RheT1mdW5jdGlvbihlKXtyZXR1cm4gdGhpcy4kdXRpbHMoKS51KGUpP3RoaXMuZGF5KCl8fDc6dGhpcy5kYXkodGhpcy5kYXkoKSU3P2U6ZS03KX07dmFyIG49ZC5zdGFydE9mO2Quc3RhcnRPZj1mdW5jdGlvbihlLHQpe3ZhciBpPXRoaXMuJHV0aWxzKCkscz0hIWkudSh0KXx8dDtyZXR1cm5cImlzb3dlZWtcIj09PWkucChlKT9zP3RoaXMuZGF0ZSh0aGlzLmRhdGUoKS0odGhpcy5pc29XZWVrZGF5KCktMSkpLnN0YXJ0T2YoXCJkYXlcIik6dGhpcy5kYXRlKHRoaXMuZGF0ZSgpLTEtKHRoaXMuaXNvV2Vla2RheSgpLTEpKzcpLmVuZE9mKFwiZGF5XCIpOm4uYmluZCh0aGlzKShlLHQpfX19KSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/ganttDiagram-c361ad54.js":
/*!************************************************************!*\
  !*** ./node_modules/mermaid/dist/ganttDiagram-c361ad54.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs/plugin/isoWeek.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/isoWeek.js\");\n/* harmony import */ var dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs/plugin/customParseFormat.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/customParseFormat.js\");\n/* harmony import */ var dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs/plugin/advancedFormat.js */ \"(app-pages-browser)/./node_modules/dayjs/plugin/advancedFormat.js\");\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [6, 8, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 30, 32, 33, 35, 37], $V1 = [1, 25], $V2 = [1, 26], $V3 = [1, 27], $V4 = [1, 28], $V5 = [1, 29], $V6 = [1, 30], $V7 = [1, 31], $V8 = [1, 9], $V9 = [1, 10], $Va = [1, 11], $Vb = [1, 12], $Vc = [1, 13], $Vd = [1, 14], $Ve = [1, 15], $Vf = [1, 16], $Vg = [1, 18], $Vh = [1, 19], $Vi = [1, 20], $Vj = [1, 21], $Vk = [1, 22], $Vl = [1, 24], $Vm = [1, 32];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"gantt\": 4, \"document\": 5, \"EOF\": 6, \"line\": 7, \"SPACE\": 8, \"statement\": 9, \"NL\": 10, \"weekday\": 11, \"weekday_monday\": 12, \"weekday_tuesday\": 13, \"weekday_wednesday\": 14, \"weekday_thursday\": 15, \"weekday_friday\": 16, \"weekday_saturday\": 17, \"weekday_sunday\": 18, \"dateFormat\": 19, \"inclusiveEndDates\": 20, \"topAxis\": 21, \"axisFormat\": 22, \"tickInterval\": 23, \"excludes\": 24, \"includes\": 25, \"todayMarker\": 26, \"title\": 27, \"acc_title\": 28, \"acc_title_value\": 29, \"acc_descr\": 30, \"acc_descr_value\": 31, \"acc_descr_multiline_value\": 32, \"section\": 33, \"clickStatement\": 34, \"taskTxt\": 35, \"taskData\": 36, \"click\": 37, \"callbackname\": 38, \"callbackargs\": 39, \"href\": 40, \"clickStatementDebug\": 41, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"gantt\", 6: \"EOF\", 8: \"SPACE\", 10: \"NL\", 12: \"weekday_monday\", 13: \"weekday_tuesday\", 14: \"weekday_wednesday\", 15: \"weekday_thursday\", 16: \"weekday_friday\", 17: \"weekday_saturday\", 18: \"weekday_sunday\", 19: \"dateFormat\", 20: \"inclusiveEndDates\", 21: \"topAxis\", 22: \"axisFormat\", 23: \"tickInterval\", 24: \"excludes\", 25: \"includes\", 26: \"todayMarker\", 27: \"title\", 28: \"acc_title\", 29: \"acc_title_value\", 30: \"acc_descr\", 31: \"acc_descr_value\", 32: \"acc_descr_multiline_value\", 33: \"section\", 35: \"taskTxt\", 36: \"taskData\", 37: \"click\", 38: \"callbackname\", 39: \"callbackargs\", 40: \"href\" },\n    productions_: [0, [3, 3], [5, 0], [5, 2], [7, 2], [7, 1], [7, 1], [7, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [11, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 1], [9, 2], [9, 2], [9, 1], [9, 1], [9, 1], [9, 2], [34, 2], [34, 3], [34, 3], [34, 4], [34, 3], [34, 4], [34, 2], [41, 2], [41, 3], [41, 3], [41, 4], [41, 3], [41, 4], [41, 2]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 1:\n          return $$[$0 - 1];\n        case 2:\n          this.$ = [];\n          break;\n        case 3:\n          $$[$0 - 1].push($$[$0]);\n          this.$ = $$[$0 - 1];\n          break;\n        case 4:\n        case 5:\n          this.$ = $$[$0];\n          break;\n        case 6:\n        case 7:\n          this.$ = [];\n          break;\n        case 8:\n          yy.setWeekday(\"monday\");\n          break;\n        case 9:\n          yy.setWeekday(\"tuesday\");\n          break;\n        case 10:\n          yy.setWeekday(\"wednesday\");\n          break;\n        case 11:\n          yy.setWeekday(\"thursday\");\n          break;\n        case 12:\n          yy.setWeekday(\"friday\");\n          break;\n        case 13:\n          yy.setWeekday(\"saturday\");\n          break;\n        case 14:\n          yy.setWeekday(\"sunday\");\n          break;\n        case 15:\n          yy.setDateFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 16:\n          yy.enableInclusiveEndDates();\n          this.$ = $$[$0].substr(18);\n          break;\n        case 17:\n          yy.TopAxis();\n          this.$ = $$[$0].substr(8);\n          break;\n        case 18:\n          yy.setAxisFormat($$[$0].substr(11));\n          this.$ = $$[$0].substr(11);\n          break;\n        case 19:\n          yy.setTickInterval($$[$0].substr(13));\n          this.$ = $$[$0].substr(13);\n          break;\n        case 20:\n          yy.setExcludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 21:\n          yy.setIncludes($$[$0].substr(9));\n          this.$ = $$[$0].substr(9);\n          break;\n        case 22:\n          yy.setTodayMarker($$[$0].substr(12));\n          this.$ = $$[$0].substr(12);\n          break;\n        case 24:\n          yy.setDiagramTitle($$[$0].substr(6));\n          this.$ = $$[$0].substr(6);\n          break;\n        case 25:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 26:\n        case 27:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 28:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n        case 30:\n          yy.addTask($$[$0 - 1], $$[$0]);\n          this.$ = \"task\";\n          break;\n        case 31:\n          this.$ = $$[$0 - 1];\n          yy.setClickEvent($$[$0 - 1], $$[$0], null);\n          break;\n        case 32:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], $$[$0]);\n          break;\n        case 33:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0 - 1], null);\n          yy.setLink($$[$0 - 2], $$[$0]);\n          break;\n        case 34:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 2], $$[$0 - 1]);\n          yy.setLink($$[$0 - 3], $$[$0]);\n          break;\n        case 35:\n          this.$ = $$[$0 - 2];\n          yy.setClickEvent($$[$0 - 2], $$[$0], null);\n          yy.setLink($$[$0 - 2], $$[$0 - 1]);\n          break;\n        case 36:\n          this.$ = $$[$0 - 3];\n          yy.setClickEvent($$[$0 - 3], $$[$0 - 1], $$[$0]);\n          yy.setLink($$[$0 - 3], $$[$0 - 2]);\n          break;\n        case 37:\n          this.$ = $$[$0 - 1];\n          yy.setLink($$[$0 - 1], $$[$0]);\n          break;\n        case 38:\n        case 44:\n          this.$ = $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 39:\n        case 40:\n        case 42:\n          this.$ = $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n        case 41:\n        case 43:\n          this.$ = $$[$0 - 3] + \" \" + $$[$0 - 2] + \" \" + $$[$0 - 1] + \" \" + $$[$0];\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, o($V0, [2, 2], { 5: 3 }), { 6: [1, 4], 7: 5, 8: [1, 6], 9: 7, 10: [1, 8], 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: $V8, 20: $V9, 21: $Va, 22: $Vb, 23: $Vc, 24: $Vd, 25: $Ve, 26: $Vf, 27: $Vg, 28: $Vh, 30: $Vi, 32: $Vj, 33: $Vk, 34: 23, 35: $Vl, 37: $Vm }, o($V0, [2, 7], { 1: [2, 1] }), o($V0, [2, 3]), { 9: 33, 11: 17, 12: $V1, 13: $V2, 14: $V3, 15: $V4, 16: $V5, 17: $V6, 18: $V7, 19: $V8, 20: $V9, 21: $Va, 22: $Vb, 23: $Vc, 24: $Vd, 25: $Ve, 26: $Vf, 27: $Vg, 28: $Vh, 30: $Vi, 32: $Vj, 33: $Vk, 34: 23, 35: $Vl, 37: $Vm }, o($V0, [2, 5]), o($V0, [2, 6]), o($V0, [2, 15]), o($V0, [2, 16]), o($V0, [2, 17]), o($V0, [2, 18]), o($V0, [2, 19]), o($V0, [2, 20]), o($V0, [2, 21]), o($V0, [2, 22]), o($V0, [2, 23]), o($V0, [2, 24]), { 29: [1, 34] }, { 31: [1, 35] }, o($V0, [2, 27]), o($V0, [2, 28]), o($V0, [2, 29]), { 36: [1, 36] }, o($V0, [2, 8]), o($V0, [2, 9]), o($V0, [2, 10]), o($V0, [2, 11]), o($V0, [2, 12]), o($V0, [2, 13]), o($V0, [2, 14]), { 38: [1, 37], 40: [1, 38] }, o($V0, [2, 4]), o($V0, [2, 25]), o($V0, [2, 26]), o($V0, [2, 30]), o($V0, [2, 31], { 39: [1, 39], 40: [1, 40] }), o($V0, [2, 37], { 38: [1, 41] }), o($V0, [2, 32], { 40: [1, 42] }), o($V0, [2, 33]), o($V0, [2, 35], { 39: [1, 43] }), o($V0, [2, 34]), o($V0, [2, 36])],\n    defaultActions: {},\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.begin(\"open_directive\");\n            return \"open_directive\";\n          case 1:\n            this.begin(\"acc_title\");\n            return 28;\n          case 2:\n            this.popState();\n            return \"acc_title_value\";\n          case 3:\n            this.begin(\"acc_descr\");\n            return 30;\n          case 4:\n            this.popState();\n            return \"acc_descr_value\";\n          case 5:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 6:\n            this.popState();\n            break;\n          case 7:\n            return \"acc_descr_multiline_value\";\n          case 8:\n            break;\n          case 9:\n            break;\n          case 10:\n            break;\n          case 11:\n            return 10;\n          case 12:\n            break;\n          case 13:\n            break;\n          case 14:\n            this.begin(\"href\");\n            break;\n          case 15:\n            this.popState();\n            break;\n          case 16:\n            return 40;\n          case 17:\n            this.begin(\"callbackname\");\n            break;\n          case 18:\n            this.popState();\n            break;\n          case 19:\n            this.popState();\n            this.begin(\"callbackargs\");\n            break;\n          case 20:\n            return 38;\n          case 21:\n            this.popState();\n            break;\n          case 22:\n            return 39;\n          case 23:\n            this.begin(\"click\");\n            break;\n          case 24:\n            this.popState();\n            break;\n          case 25:\n            return 37;\n          case 26:\n            return 4;\n          case 27:\n            return 19;\n          case 28:\n            return 20;\n          case 29:\n            return 21;\n          case 30:\n            return 22;\n          case 31:\n            return 23;\n          case 32:\n            return 25;\n          case 33:\n            return 24;\n          case 34:\n            return 26;\n          case 35:\n            return 12;\n          case 36:\n            return 13;\n          case 37:\n            return 14;\n          case 38:\n            return 15;\n          case 39:\n            return 16;\n          case 40:\n            return 17;\n          case 41:\n            return 18;\n          case 42:\n            return \"date\";\n          case 43:\n            return 27;\n          case 44:\n            return \"accDescription\";\n          case 45:\n            return 33;\n          case 46:\n            return 35;\n          case 47:\n            return 36;\n          case 48:\n            return \":\";\n          case 49:\n            return 6;\n          case 50:\n            return \"INVALID\";\n        }\n      },\n      rules: [/^(?:%%\\{)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:%%(?!\\{)*[^\\n]*)/i, /^(?:[^\\}]%%*[^\\n]*)/i, /^(?:%%*[^\\n]*[\\n]*)/i, /^(?:[\\n]+)/i, /^(?:\\s+)/i, /^(?:%[^\\n]*)/i, /^(?:href[\\s]+[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:call[\\s]+)/i, /^(?:\\([\\s]*\\))/i, /^(?:\\()/i, /^(?:[^(]*)/i, /^(?:\\))/i, /^(?:[^)]*)/i, /^(?:click[\\s]+)/i, /^(?:[\\s\\n])/i, /^(?:[^\\s\\n]*)/i, /^(?:gantt\\b)/i, /^(?:dateFormat\\s[^#\\n;]+)/i, /^(?:inclusiveEndDates\\b)/i, /^(?:topAxis\\b)/i, /^(?:axisFormat\\s[^#\\n;]+)/i, /^(?:tickInterval\\s[^#\\n;]+)/i, /^(?:includes\\s[^#\\n;]+)/i, /^(?:excludes\\s[^#\\n;]+)/i, /^(?:todayMarker\\s[^\\n;]+)/i, /^(?:weekday\\s+monday\\b)/i, /^(?:weekday\\s+tuesday\\b)/i, /^(?:weekday\\s+wednesday\\b)/i, /^(?:weekday\\s+thursday\\b)/i, /^(?:weekday\\s+friday\\b)/i, /^(?:weekday\\s+saturday\\b)/i, /^(?:weekday\\s+sunday\\b)/i, /^(?:\\d\\d\\d\\d-\\d\\d-\\d\\d\\b)/i, /^(?:title\\s[^\\n]+)/i, /^(?:accDescription\\s[^#\\n;]+)/i, /^(?:section\\s[^\\n]+)/i, /^(?:[^:\\n]+)/i, /^(?::[^#\\n;]+)/i, /^(?::)/i, /^(?:$)/i, /^(?:.)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [6, 7], \"inclusive\": false }, \"acc_descr\": { \"rules\": [4], \"inclusive\": false }, \"acc_title\": { \"rules\": [2], \"inclusive\": false }, \"callbackargs\": { \"rules\": [21, 22], \"inclusive\": false }, \"callbackname\": { \"rules\": [18, 19, 20], \"inclusive\": false }, \"href\": { \"rules\": [15, 16], \"inclusive\": false }, \"click\": { \"rules\": [24, 25], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 3, 5, 8, 9, 10, 11, 12, 13, 14, 17, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst ganttParser = parser;\ndayjs__WEBPACK_IMPORTED_MODULE_1__.extend(dayjs_plugin_isoWeek_js__WEBPACK_IMPORTED_MODULE_2__);\ndayjs__WEBPACK_IMPORTED_MODULE_1__.extend(dayjs_plugin_customParseFormat_js__WEBPACK_IMPORTED_MODULE_3__);\ndayjs__WEBPACK_IMPORTED_MODULE_1__.extend(dayjs_plugin_advancedFormat_js__WEBPACK_IMPORTED_MODULE_4__);\nlet dateFormat = \"\";\nlet axisFormat = \"\";\nlet tickInterval = void 0;\nlet todayMarker = \"\";\nlet includes = [];\nlet excludes = [];\nlet links = {};\nlet sections = [];\nlet tasks = [];\nlet currentSection = \"\";\nlet displayMode = \"\";\nconst tags = [\"active\", \"done\", \"crit\", \"milestone\"];\nlet funs = [];\nlet inclusiveEndDates = false;\nlet topAxis = false;\nlet weekday = \"sunday\";\nlet lastOrder = 0;\nconst clear = function() {\n  sections = [];\n  tasks = [];\n  currentSection = \"\";\n  funs = [];\n  taskCnt = 0;\n  lastTask = void 0;\n  lastTaskID = void 0;\n  rawTasks = [];\n  dateFormat = \"\";\n  axisFormat = \"\";\n  displayMode = \"\";\n  tickInterval = void 0;\n  todayMarker = \"\";\n  includes = [];\n  excludes = [];\n  inclusiveEndDates = false;\n  topAxis = false;\n  lastOrder = 0;\n  links = {};\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.v)();\n  weekday = \"sunday\";\n};\nconst setAxisFormat = function(txt) {\n  axisFormat = txt;\n};\nconst getAxisFormat = function() {\n  return axisFormat;\n};\nconst setTickInterval = function(txt) {\n  tickInterval = txt;\n};\nconst getTickInterval = function() {\n  return tickInterval;\n};\nconst setTodayMarker = function(txt) {\n  todayMarker = txt;\n};\nconst getTodayMarker = function() {\n  return todayMarker;\n};\nconst setDateFormat = function(txt) {\n  dateFormat = txt;\n};\nconst enableInclusiveEndDates = function() {\n  inclusiveEndDates = true;\n};\nconst endDatesAreInclusive = function() {\n  return inclusiveEndDates;\n};\nconst enableTopAxis = function() {\n  topAxis = true;\n};\nconst topAxisEnabled = function() {\n  return topAxis;\n};\nconst setDisplayMode = function(txt) {\n  displayMode = txt;\n};\nconst getDisplayMode = function() {\n  return displayMode;\n};\nconst getDateFormat = function() {\n  return dateFormat;\n};\nconst setIncludes = function(txt) {\n  includes = txt.toLowerCase().split(/[\\s,]+/);\n};\nconst getIncludes = function() {\n  return includes;\n};\nconst setExcludes = function(txt) {\n  excludes = txt.toLowerCase().split(/[\\s,]+/);\n};\nconst getExcludes = function() {\n  return excludes;\n};\nconst getLinks = function() {\n  return links;\n};\nconst addSection = function(txt) {\n  currentSection = txt;\n  sections.push(txt);\n};\nconst getSections = function() {\n  return sections;\n};\nconst getTasks = function() {\n  let allItemsProcessed = compileTasks();\n  const maxDepth = 10;\n  let iterationCount = 0;\n  while (!allItemsProcessed && iterationCount < maxDepth) {\n    allItemsProcessed = compileTasks();\n    iterationCount++;\n  }\n  tasks = rawTasks;\n  return tasks;\n};\nconst isInvalidDate = function(date, dateFormat2, excludes2, includes2) {\n  if (includes2.includes(date.format(dateFormat2.trim()))) {\n    return false;\n  }\n  if (date.isoWeekday() >= 6 && excludes2.includes(\"weekends\")) {\n    return true;\n  }\n  if (excludes2.includes(date.format(\"dddd\").toLowerCase())) {\n    return true;\n  }\n  return excludes2.includes(date.format(dateFormat2.trim()));\n};\nconst setWeekday = function(txt) {\n  weekday = txt;\n};\nconst getWeekday = function() {\n  return weekday;\n};\nconst checkTaskDates = function(task, dateFormat2, excludes2, includes2) {\n  if (!excludes2.length || task.manualEndTime) {\n    return;\n  }\n  let startTime;\n  if (task.startTime instanceof Date) {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(task.startTime);\n  } else {\n    startTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(task.startTime, dateFormat2, true);\n  }\n  startTime = startTime.add(1, \"d\");\n  let originalEndTime;\n  if (task.endTime instanceof Date) {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(task.endTime);\n  } else {\n    originalEndTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(task.endTime, dateFormat2, true);\n  }\n  const [fixedEndTime, renderEndTime] = fixTaskDates(\n    startTime,\n    originalEndTime,\n    dateFormat2,\n    excludes2,\n    includes2\n  );\n  task.endTime = fixedEndTime.toDate();\n  task.renderEndTime = renderEndTime;\n};\nconst fixTaskDates = function(startTime, endTime, dateFormat2, excludes2, includes2) {\n  let invalid = false;\n  let renderEndTime = null;\n  while (startTime <= endTime) {\n    if (!invalid) {\n      renderEndTime = endTime.toDate();\n    }\n    invalid = isInvalidDate(startTime, dateFormat2, excludes2, includes2);\n    if (invalid) {\n      endTime = endTime.add(1, \"d\");\n    }\n    startTime = startTime.add(1, \"d\");\n  }\n  return [endTime, renderEndTime];\n};\nconst getStartDate = function(prevTime, dateFormat2, str) {\n  str = str.trim();\n  const afterRePattern = /^after\\s+(?<ids>[\\d\\w- ]+)/;\n  const afterStatement = afterRePattern.exec(str);\n  if (afterStatement !== null) {\n    let latestTask = null;\n    for (const id of afterStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!latestTask || task.endTime > latestTask.endTime)) {\n        latestTask = task;\n      }\n    }\n    if (latestTask) {\n      return latestTask.endTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let mDate = dayjs__WEBPACK_IMPORTED_MODULE_1__(str, dateFormat2.trim(), true);\n  if (mDate.isValid()) {\n    return mDate.toDate();\n  } else {\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.l.debug(\"Invalid date:\" + str);\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.l.debug(\"With date format:\" + dateFormat2.trim());\n    const d = new Date(str);\n    if (d === void 0 || isNaN(d.getTime()) || // WebKit browsers can mis-parse invalid dates to be ridiculously\n    // huge numbers, e.g. new Date('202304') gets parsed as January 1, 202304.\n    // This can cause virtually infinite loops while rendering, so for the\n    // purposes of Gantt charts we'll just treat any date beyond 10,000 AD/BC as\n    // invalid.\n    d.getFullYear() < -1e4 || d.getFullYear() > 1e4) {\n      throw new Error(\"Invalid date:\" + str);\n    }\n    return d;\n  }\n};\nconst parseDuration = function(str) {\n  const statement = /^(\\d+(?:\\.\\d+)?)([Mdhmswy]|ms)$/.exec(str.trim());\n  if (statement !== null) {\n    return [Number.parseFloat(statement[1]), statement[2]];\n  }\n  return [NaN, \"ms\"];\n};\nconst getEndDate = function(prevTime, dateFormat2, str, inclusive = false) {\n  str = str.trim();\n  const untilRePattern = /^until\\s+(?<ids>[\\d\\w- ]+)/;\n  const untilStatement = untilRePattern.exec(str);\n  if (untilStatement !== null) {\n    let earliestTask = null;\n    for (const id of untilStatement.groups.ids.split(\" \")) {\n      let task = findTaskById(id);\n      if (task !== void 0 && (!earliestTask || task.startTime < earliestTask.startTime)) {\n        earliestTask = task;\n      }\n    }\n    if (earliestTask) {\n      return earliestTask.startTime;\n    }\n    const today = /* @__PURE__ */ new Date();\n    today.setHours(0, 0, 0, 0);\n    return today;\n  }\n  let parsedDate = dayjs__WEBPACK_IMPORTED_MODULE_1__(str, dateFormat2.trim(), true);\n  if (parsedDate.isValid()) {\n    if (inclusive) {\n      parsedDate = parsedDate.add(1, \"d\");\n    }\n    return parsedDate.toDate();\n  }\n  let endTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(prevTime);\n  const [durationValue, durationUnit] = parseDuration(str);\n  if (!Number.isNaN(durationValue)) {\n    const newEndTime = endTime.add(durationValue, durationUnit);\n    if (newEndTime.isValid()) {\n      endTime = newEndTime;\n    }\n  }\n  return endTime.toDate();\n};\nlet taskCnt = 0;\nconst parseId = function(idStr) {\n  if (idStr === void 0) {\n    taskCnt = taskCnt + 1;\n    return \"task\" + taskCnt;\n  }\n  return idStr;\n};\nconst compileData = function(prevTask, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  let endTimeData = \"\";\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = prevTask.endTime;\n      endTimeData = data[0];\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = getStartDate(void 0, dateFormat, data[0]);\n      endTimeData = data[1];\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = getStartDate(void 0, dateFormat, data[1]);\n      endTimeData = data[2];\n      break;\n  }\n  if (endTimeData) {\n    task.endTime = getEndDate(task.startTime, dateFormat, endTimeData, inclusiveEndDates);\n    task.manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(endTimeData, \"YYYY-MM-DD\", true).isValid();\n    checkTaskDates(task, dateFormat, excludes, includes);\n  }\n  return task;\n};\nconst parseData = function(prevTaskId, dataStr) {\n  let ds;\n  if (dataStr.substr(0, 1) === \":\") {\n    ds = dataStr.substr(1, dataStr.length);\n  } else {\n    ds = dataStr;\n  }\n  const data = ds.split(\",\");\n  const task = {};\n  getTaskTags(data, task, tags);\n  for (let i = 0; i < data.length; i++) {\n    data[i] = data[i].trim();\n  }\n  switch (data.length) {\n    case 1:\n      task.id = parseId();\n      task.startTime = {\n        type: \"prevTaskEnd\",\n        id: prevTaskId\n      };\n      task.endTime = {\n        data: data[0]\n      };\n      break;\n    case 2:\n      task.id = parseId();\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[0]\n      };\n      task.endTime = {\n        data: data[1]\n      };\n      break;\n    case 3:\n      task.id = parseId(data[0]);\n      task.startTime = {\n        type: \"getStartDate\",\n        startData: data[1]\n      };\n      task.endTime = {\n        data: data[2]\n      };\n      break;\n  }\n  return task;\n};\nlet lastTask;\nlet lastTaskID;\nlet rawTasks = [];\nconst taskDb = {};\nconst addTask = function(descr, data) {\n  const rawTask = {\n    section: currentSection,\n    type: currentSection,\n    processed: false,\n    manualEndTime: false,\n    renderEndTime: null,\n    raw: { data },\n    task: descr,\n    classes: []\n  };\n  const taskInfo = parseData(lastTaskID, data);\n  rawTask.raw.startTime = taskInfo.startTime;\n  rawTask.raw.endTime = taskInfo.endTime;\n  rawTask.id = taskInfo.id;\n  rawTask.prevTaskId = lastTaskID;\n  rawTask.active = taskInfo.active;\n  rawTask.done = taskInfo.done;\n  rawTask.crit = taskInfo.crit;\n  rawTask.milestone = taskInfo.milestone;\n  rawTask.order = lastOrder;\n  lastOrder++;\n  const pos = rawTasks.push(rawTask);\n  lastTaskID = rawTask.id;\n  taskDb[rawTask.id] = pos - 1;\n};\nconst findTaskById = function(id) {\n  const pos = taskDb[id];\n  return rawTasks[pos];\n};\nconst addTaskOrg = function(descr, data) {\n  const newTask = {\n    section: currentSection,\n    type: currentSection,\n    description: descr,\n    task: descr,\n    classes: []\n  };\n  const taskInfo = compileData(lastTask, data);\n  newTask.startTime = taskInfo.startTime;\n  newTask.endTime = taskInfo.endTime;\n  newTask.id = taskInfo.id;\n  newTask.active = taskInfo.active;\n  newTask.done = taskInfo.done;\n  newTask.crit = taskInfo.crit;\n  newTask.milestone = taskInfo.milestone;\n  lastTask = newTask;\n  tasks.push(newTask);\n};\nconst compileTasks = function() {\n  const compileTask = function(pos) {\n    const task = rawTasks[pos];\n    let startTime = \"\";\n    switch (rawTasks[pos].raw.startTime.type) {\n      case \"prevTaskEnd\": {\n        const prevTask = findTaskById(task.prevTaskId);\n        task.startTime = prevTask.endTime;\n        break;\n      }\n      case \"getStartDate\":\n        startTime = getStartDate(void 0, dateFormat, rawTasks[pos].raw.startTime.startData);\n        if (startTime) {\n          rawTasks[pos].startTime = startTime;\n        }\n        break;\n    }\n    if (rawTasks[pos].startTime) {\n      rawTasks[pos].endTime = getEndDate(\n        rawTasks[pos].startTime,\n        dateFormat,\n        rawTasks[pos].raw.endTime.data,\n        inclusiveEndDates\n      );\n      if (rawTasks[pos].endTime) {\n        rawTasks[pos].processed = true;\n        rawTasks[pos].manualEndTime = dayjs__WEBPACK_IMPORTED_MODULE_1__(\n          rawTasks[pos].raw.endTime.data,\n          \"YYYY-MM-DD\",\n          true\n        ).isValid();\n        checkTaskDates(rawTasks[pos], dateFormat, excludes, includes);\n      }\n    }\n    return rawTasks[pos].processed;\n  };\n  let allProcessed = true;\n  for (const [i, rawTask] of rawTasks.entries()) {\n    compileTask(i);\n    allProcessed = allProcessed && rawTask.processed;\n  }\n  return allProcessed;\n};\nconst setLink = function(ids, _linkStr) {\n  let linkStr = _linkStr;\n  if ((0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.c)().securityLevel !== \"loose\") {\n    linkStr = (0,_braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_0__.sanitizeUrl)(_linkStr);\n  }\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      pushFun(id, () => {\n        window.open(linkStr, \"_self\");\n      });\n      links[id] = linkStr;\n    }\n  });\n  setClass(ids, \"clickable\");\n};\nconst setClass = function(ids, className) {\n  ids.split(\",\").forEach(function(id) {\n    let rawTask = findTaskById(id);\n    if (rawTask !== void 0) {\n      rawTask.classes.push(className);\n    }\n  });\n};\nconst setClickFun = function(id, functionName, functionArgs) {\n  if ((0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.c)().securityLevel !== \"loose\") {\n    return;\n  }\n  if (functionName === void 0) {\n    return;\n  }\n  let argList = [];\n  if (typeof functionArgs === \"string\") {\n    argList = functionArgs.split(/,(?=(?:(?:[^\"]*\"){2})*[^\"]*$)/);\n    for (let i = 0; i < argList.length; i++) {\n      let item = argList[i].trim();\n      if (item.charAt(0) === '\"' && item.charAt(item.length - 1) === '\"') {\n        item = item.substr(1, item.length - 2);\n      }\n      argList[i] = item;\n    }\n  }\n  if (argList.length === 0) {\n    argList.push(id);\n  }\n  let rawTask = findTaskById(id);\n  if (rawTask !== void 0) {\n    pushFun(id, () => {\n      _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.u.runFunc(functionName, ...argList);\n    });\n  }\n};\nconst pushFun = function(id, callbackFunction) {\n  funs.push(\n    function() {\n      const elem = document.querySelector(`[id=\"${id}\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    },\n    function() {\n      const elem = document.querySelector(`[id=\"${id}-text\"]`);\n      if (elem !== null) {\n        elem.addEventListener(\"click\", function() {\n          callbackFunction();\n        });\n      }\n    }\n  );\n};\nconst setClickEvent = function(ids, functionName, functionArgs) {\n  ids.split(\",\").forEach(function(id) {\n    setClickFun(id, functionName, functionArgs);\n  });\n  setClass(ids, \"clickable\");\n};\nconst bindFunctions = function(element) {\n  funs.forEach(function(fun) {\n    fun(element);\n  });\n};\nconst ganttDb = {\n  getConfig: () => (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.c)().gantt,\n  clear,\n  setDateFormat,\n  getDateFormat,\n  enableInclusiveEndDates,\n  endDatesAreInclusive,\n  enableTopAxis,\n  topAxisEnabled,\n  setAxisFormat,\n  getAxisFormat,\n  setTickInterval,\n  getTickInterval,\n  setTodayMarker,\n  getTodayMarker,\n  setAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.s,\n  getAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.g,\n  setDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.q,\n  getDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.t,\n  setDisplayMode,\n  getDisplayMode,\n  setAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.b,\n  getAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.a,\n  addSection,\n  getSections,\n  getTasks,\n  addTask,\n  findTaskById,\n  addTaskOrg,\n  setIncludes,\n  getIncludes,\n  setExcludes,\n  getExcludes,\n  setClickEvent,\n  setLink,\n  getLinks,\n  bindFunctions,\n  parseDuration,\n  isInvalidDate,\n  setWeekday,\n  getWeekday\n};\nfunction getTaskTags(data, task, tags2) {\n  let matchFound = true;\n  while (matchFound) {\n    matchFound = false;\n    tags2.forEach(function(t) {\n      const pattern = \"^\\\\s*\" + t + \"\\\\s*$\";\n      const regex = new RegExp(pattern);\n      if (data[0].match(regex)) {\n        task[t] = true;\n        data.shift(1);\n        matchFound = true;\n      }\n    });\n  }\n}\nconst setConf = function() {\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.l.debug(\"Something is calling, setConf, remove the call\");\n};\nconst mapWeekdayToTimeFunction = {\n  monday: d3__WEBPACK_IMPORTED_MODULE_5__.timeMonday,\n  tuesday: d3__WEBPACK_IMPORTED_MODULE_5__.timeTuesday,\n  wednesday: d3__WEBPACK_IMPORTED_MODULE_5__.timeWednesday,\n  thursday: d3__WEBPACK_IMPORTED_MODULE_5__.timeThursday,\n  friday: d3__WEBPACK_IMPORTED_MODULE_5__.timeFriday,\n  saturday: d3__WEBPACK_IMPORTED_MODULE_5__.timeSaturday,\n  sunday: d3__WEBPACK_IMPORTED_MODULE_5__.timeSunday\n};\nconst getMaxIntersections = (tasks2, orderOffset) => {\n  let timeline = [...tasks2].map(() => -Infinity);\n  let sorted = [...tasks2].sort((a, b) => a.startTime - b.startTime || a.order - b.order);\n  let maxIntersections = 0;\n  for (const element of sorted) {\n    for (let j = 0; j < timeline.length; j++) {\n      if (element.startTime >= timeline[j]) {\n        timeline[j] = element.endTime;\n        element.order = j + orderOffset;\n        if (j > maxIntersections) {\n          maxIntersections = j;\n        }\n        break;\n      }\n    }\n  }\n  return maxIntersections;\n};\nlet w;\nconst draw = function(text, id, version, diagObj) {\n  const conf = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.c)().gantt;\n  const securityLevel = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.c)().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = (0,d3__WEBPACK_IMPORTED_MODULE_5__.select)(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? (0,d3__WEBPACK_IMPORTED_MODULE_5__.select)(sandboxElement.nodes()[0].contentDocument.body) : (0,d3__WEBPACK_IMPORTED_MODULE_5__.select)(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  const elem = doc.getElementById(id);\n  w = elem.parentElement.offsetWidth;\n  if (w === void 0) {\n    w = 1200;\n  }\n  if (conf.useWidth !== void 0) {\n    w = conf.useWidth;\n  }\n  const taskArray = diagObj.db.getTasks();\n  let categories = [];\n  for (const element of taskArray) {\n    categories.push(element.type);\n  }\n  categories = checkUnique(categories);\n  const categoryHeights = {};\n  let h = 2 * conf.topPadding;\n  if (diagObj.db.getDisplayMode() === \"compact\" || conf.displayMode === \"compact\") {\n    const categoryElements = {};\n    for (const element of taskArray) {\n      if (categoryElements[element.section] === void 0) {\n        categoryElements[element.section] = [element];\n      } else {\n        categoryElements[element.section].push(element);\n      }\n    }\n    let intersections = 0;\n    for (const category of Object.keys(categoryElements)) {\n      const categoryHeight = getMaxIntersections(categoryElements[category], intersections) + 1;\n      intersections += categoryHeight;\n      h += categoryHeight * (conf.barHeight + conf.barGap);\n      categoryHeights[category] = categoryHeight;\n    }\n  } else {\n    h += taskArray.length * (conf.barHeight + conf.barGap);\n    for (const category of categories) {\n      categoryHeights[category] = taskArray.filter((task) => task.type === category).length;\n    }\n  }\n  elem.setAttribute(\"viewBox\", \"0 0 \" + w + \" \" + h);\n  const svg = root.select(`[id=\"${id}\"]`);\n  const timeScale = (0,d3__WEBPACK_IMPORTED_MODULE_5__.scaleTime)().domain([\n    (0,d3__WEBPACK_IMPORTED_MODULE_5__.min)(taskArray, function(d) {\n      return d.startTime;\n    }),\n    (0,d3__WEBPACK_IMPORTED_MODULE_5__.max)(taskArray, function(d) {\n      return d.endTime;\n    })\n  ]).rangeRound([0, w - conf.leftPadding - conf.rightPadding]);\n  function taskCompare(a, b) {\n    const taskA = a.startTime;\n    const taskB = b.startTime;\n    let result = 0;\n    if (taskA > taskB) {\n      result = 1;\n    } else if (taskA < taskB) {\n      result = -1;\n    }\n    return result;\n  }\n  taskArray.sort(taskCompare);\n  makeGantt(taskArray, w, h);\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.i)(svg, h, w, conf.useMaxWidth);\n  svg.append(\"text\").text(diagObj.db.getDiagramTitle()).attr(\"x\", w / 2).attr(\"y\", conf.titleTopMargin).attr(\"class\", \"titleText\");\n  function makeGantt(tasks2, pageWidth, pageHeight) {\n    const barHeight = conf.barHeight;\n    const gap = barHeight + conf.barGap;\n    const topPadding = conf.topPadding;\n    const leftPadding = conf.leftPadding;\n    const colorScale = (0,d3__WEBPACK_IMPORTED_MODULE_5__.scaleLinear)().domain([0, categories.length]).range([\"#00B9FA\", \"#F95002\"]).interpolate(d3__WEBPACK_IMPORTED_MODULE_5__.interpolateHcl);\n    drawExcludeDays(\n      gap,\n      topPadding,\n      leftPadding,\n      pageWidth,\n      pageHeight,\n      tasks2,\n      diagObj.db.getExcludes(),\n      diagObj.db.getIncludes()\n    );\n    makeGrid(leftPadding, topPadding, pageWidth, pageHeight);\n    drawRects(tasks2, gap, topPadding, leftPadding, barHeight, colorScale, pageWidth);\n    vertLabels(gap, topPadding);\n    drawToday(leftPadding, topPadding, pageWidth, pageHeight);\n  }\n  function drawRects(theArray, theGap, theTopPad, theSidePad, theBarHeight, theColorScale, w2) {\n    const uniqueTaskOrderIds = [...new Set(theArray.map((item) => item.order))];\n    const uniqueTasks = uniqueTaskOrderIds.map((id2) => theArray.find((item) => item.order === id2));\n    svg.append(\"g\").selectAll(\"rect\").data(uniqueTasks).enter().append(\"rect\").attr(\"x\", 0).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad - 2;\n    }).attr(\"width\", function() {\n      return w2 - conf.rightPadding / 2;\n    }).attr(\"height\", theGap).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          return \"section section\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"section section0\";\n    });\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(theArray).enter();\n    const links2 = diagObj.db.getLinks();\n    rectangles.append(\"rect\").attr(\"id\", function(d) {\n      return d.id;\n    }).attr(\"rx\", 3).attr(\"ry\", 3).attr(\"x\", function(d) {\n      if (d.milestone) {\n        return timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      return timeScale(d.startTime) + theSidePad;\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + theTopPad;\n    }).attr(\"width\", function(d) {\n      if (d.milestone) {\n        return theBarHeight;\n      }\n      return timeScale(d.renderEndTime || d.endTime) - timeScale(d.startTime);\n    }).attr(\"height\", theBarHeight).attr(\"transform-origin\", function(d, i) {\n      i = d.order;\n      return (timeScale(d.startTime) + theSidePad + 0.5 * (timeScale(d.endTime) - timeScale(d.startTime))).toString() + \"px \" + (i * theGap + theTopPad + 0.5 * theBarHeight).toString() + \"px\";\n    }).attr(\"class\", function(d) {\n      const res = \"task\";\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskClass = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskClass += \" activeCrit\";\n        } else {\n          taskClass = \" active\";\n        }\n      } else if (d.done) {\n        if (d.crit) {\n          taskClass = \" doneCrit\";\n        } else {\n          taskClass = \" done\";\n        }\n      } else {\n        if (d.crit) {\n          taskClass += \" crit\";\n        }\n      }\n      if (taskClass.length === 0) {\n        taskClass = \" task\";\n      }\n      if (d.milestone) {\n        taskClass = \" milestone \" + taskClass;\n      }\n      taskClass += secNum;\n      taskClass += \" \" + classStr;\n      return res + taskClass;\n    });\n    rectangles.append(\"text\").attr(\"id\", function(d) {\n      return d.id + \"-text\";\n    }).text(function(d) {\n      return d.task;\n    }).attr(\"font-size\", conf.fontSize).attr(\"x\", function(d) {\n      let startX = timeScale(d.startTime);\n      let endX = timeScale(d.renderEndTime || d.endTime);\n      if (d.milestone) {\n        startX += 0.5 * (timeScale(d.endTime) - timeScale(d.startTime)) - 0.5 * theBarHeight;\n      }\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return startX + theSidePad - 5;\n        } else {\n          return endX + theSidePad + 5;\n        }\n      } else {\n        return (endX - startX) / 2 + startX + theSidePad;\n      }\n    }).attr(\"y\", function(d, i) {\n      i = d.order;\n      return i * theGap + conf.barHeight / 2 + (conf.fontSize / 2 - 2) + theTopPad;\n    }).attr(\"text-height\", theBarHeight).attr(\"class\", function(d) {\n      const startX = timeScale(d.startTime);\n      let endX = timeScale(d.endTime);\n      if (d.milestone) {\n        endX = startX + theBarHeight;\n      }\n      const textWidth = this.getBBox().width;\n      let classStr = \"\";\n      if (d.classes.length > 0) {\n        classStr = d.classes.join(\" \");\n      }\n      let secNum = 0;\n      for (const [i, category] of categories.entries()) {\n        if (d.type === category) {\n          secNum = i % conf.numberSectionStyles;\n        }\n      }\n      let taskType = \"\";\n      if (d.active) {\n        if (d.crit) {\n          taskType = \"activeCritText\" + secNum;\n        } else {\n          taskType = \"activeText\" + secNum;\n        }\n      }\n      if (d.done) {\n        if (d.crit) {\n          taskType = taskType + \" doneCritText\" + secNum;\n        } else {\n          taskType = taskType + \" doneText\" + secNum;\n        }\n      } else {\n        if (d.crit) {\n          taskType = taskType + \" critText\" + secNum;\n        }\n      }\n      if (d.milestone) {\n        taskType += \" milestoneText\";\n      }\n      if (textWidth > endX - startX) {\n        if (endX + textWidth + 1.5 * conf.leftPadding > w2) {\n          return classStr + \" taskTextOutsideLeft taskTextOutside\" + secNum + \" \" + taskType;\n        } else {\n          return classStr + \" taskTextOutsideRight taskTextOutside\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n        }\n      } else {\n        return classStr + \" taskText taskText\" + secNum + \" \" + taskType + \" width-\" + textWidth;\n      }\n    });\n    const securityLevel2 = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.c)().securityLevel;\n    if (securityLevel2 === \"sandbox\") {\n      let sandboxElement2;\n      sandboxElement2 = (0,d3__WEBPACK_IMPORTED_MODULE_5__.select)(\"#i\" + id);\n      const doc2 = sandboxElement2.nodes()[0].contentDocument;\n      rectangles.filter(function(d) {\n        return links2[d.id] !== void 0;\n      }).each(function(o) {\n        var taskRect = doc2.querySelector(\"#\" + o.id);\n        var taskText = doc2.querySelector(\"#\" + o.id + \"-text\");\n        const oldParent = taskRect.parentNode;\n        var Link = doc2.createElement(\"a\");\n        Link.setAttribute(\"xlink:href\", links2[o.id]);\n        Link.setAttribute(\"target\", \"_top\");\n        oldParent.appendChild(Link);\n        Link.appendChild(taskRect);\n        Link.appendChild(taskText);\n      });\n    }\n  }\n  function drawExcludeDays(theGap, theTopPad, theSidePad, w2, h2, tasks2, excludes2, includes2) {\n    if (excludes2.length === 0 && includes2.length === 0) {\n      return;\n    }\n    let minTime;\n    let maxTime;\n    for (const { startTime, endTime } of tasks2) {\n      if (minTime === void 0 || startTime < minTime) {\n        minTime = startTime;\n      }\n      if (maxTime === void 0 || endTime > maxTime) {\n        maxTime = endTime;\n      }\n    }\n    if (!minTime || !maxTime) {\n      return;\n    }\n    if (dayjs__WEBPACK_IMPORTED_MODULE_1__(maxTime).diff(dayjs__WEBPACK_IMPORTED_MODULE_1__(minTime), \"year\") > 5) {\n      _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.l.warn(\n        \"The difference between the min and max time is more than 5 years. This will cause performance issues. Skipping drawing exclude days.\"\n      );\n      return;\n    }\n    const dateFormat2 = diagObj.db.getDateFormat();\n    const excludeRanges = [];\n    let range = null;\n    let d = dayjs__WEBPACK_IMPORTED_MODULE_1__(minTime);\n    while (d.valueOf() <= maxTime) {\n      if (diagObj.db.isInvalidDate(d, dateFormat2, excludes2, includes2)) {\n        if (!range) {\n          range = {\n            start: d,\n            end: d\n          };\n        } else {\n          range.end = d;\n        }\n      } else {\n        if (range) {\n          excludeRanges.push(range);\n          range = null;\n        }\n      }\n      d = d.add(1, \"d\");\n    }\n    const rectangles = svg.append(\"g\").selectAll(\"rect\").data(excludeRanges).enter();\n    rectangles.append(\"rect\").attr(\"id\", function(d2) {\n      return \"exclude-\" + d2.start.format(\"YYYY-MM-DD\");\n    }).attr(\"x\", function(d2) {\n      return timeScale(d2.start) + theSidePad;\n    }).attr(\"y\", conf.gridLineStartPadding).attr(\"width\", function(d2) {\n      const renderEnd = d2.end.add(1, \"day\");\n      return timeScale(renderEnd) - timeScale(d2.start);\n    }).attr(\"height\", h2 - theTopPad - conf.gridLineStartPadding).attr(\"transform-origin\", function(d2, i) {\n      return (timeScale(d2.start) + theSidePad + 0.5 * (timeScale(d2.end) - timeScale(d2.start))).toString() + \"px \" + (i * theGap + 0.5 * h2).toString() + \"px\";\n    }).attr(\"class\", \"exclude-range\");\n  }\n  function makeGrid(theSidePad, theTopPad, w2, h2) {\n    let bottomXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_5__.axisBottom)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_5__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n    const reTickInterval = /^([1-9]\\d*)(millisecond|second|minute|hour|day|week|month)$/;\n    const resultTickInterval = reTickInterval.exec(\n      diagObj.db.getTickInterval() || conf.tickInterval\n    );\n    if (resultTickInterval !== null) {\n      const every = resultTickInterval[1];\n      const interval = resultTickInterval[2];\n      const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n      switch (interval) {\n        case \"millisecond\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeMillisecond.every(every));\n          break;\n        case \"second\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeSecond.every(every));\n          break;\n        case \"minute\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeMinute.every(every));\n          break;\n        case \"hour\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeHour.every(every));\n          break;\n        case \"day\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeDay.every(every));\n          break;\n        case \"week\":\n          bottomXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n          break;\n        case \"month\":\n          bottomXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeMonth.every(every));\n          break;\n      }\n    }\n    svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + (h2 - 50) + \")\").call(bottomXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10).attr(\"dy\", \"1em\");\n    if (diagObj.db.topAxisEnabled() || conf.topAxis) {\n      let topXAxis = (0,d3__WEBPACK_IMPORTED_MODULE_5__.axisTop)(timeScale).tickSize(-h2 + theTopPad + conf.gridLineStartPadding).tickFormat((0,d3__WEBPACK_IMPORTED_MODULE_5__.timeFormat)(diagObj.db.getAxisFormat() || conf.axisFormat || \"%Y-%m-%d\"));\n      if (resultTickInterval !== null) {\n        const every = resultTickInterval[1];\n        const interval = resultTickInterval[2];\n        const weekday2 = diagObj.db.getWeekday() || conf.weekday;\n        switch (interval) {\n          case \"millisecond\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeMillisecond.every(every));\n            break;\n          case \"second\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeSecond.every(every));\n            break;\n          case \"minute\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeMinute.every(every));\n            break;\n          case \"hour\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeHour.every(every));\n            break;\n          case \"day\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeDay.every(every));\n            break;\n          case \"week\":\n            topXAxis.ticks(mapWeekdayToTimeFunction[weekday2].every(every));\n            break;\n          case \"month\":\n            topXAxis.ticks(d3__WEBPACK_IMPORTED_MODULE_5__.timeMonth.every(every));\n            break;\n        }\n      }\n      svg.append(\"g\").attr(\"class\", \"grid\").attr(\"transform\", \"translate(\" + theSidePad + \", \" + theTopPad + \")\").call(topXAxis).selectAll(\"text\").style(\"text-anchor\", \"middle\").attr(\"fill\", \"#000\").attr(\"stroke\", \"none\").attr(\"font-size\", 10);\n    }\n  }\n  function vertLabels(theGap, theTopPad) {\n    let prevGap = 0;\n    const numOccurrences = Object.keys(categoryHeights).map((d) => [d, categoryHeights[d]]);\n    svg.append(\"g\").selectAll(\"text\").data(numOccurrences).enter().append(function(d) {\n      const rows = d[0].split(_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_8__.e.lineBreakRegex);\n      const dy = -(rows.length - 1) / 2;\n      const svgLabel = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"text\");\n      svgLabel.setAttribute(\"dy\", dy + \"em\");\n      for (const [j, row] of rows.entries()) {\n        const tspan = doc.createElementNS(\"http://www.w3.org/2000/svg\", \"tspan\");\n        tspan.setAttribute(\"alignment-baseline\", \"central\");\n        tspan.setAttribute(\"x\", \"10\");\n        if (j > 0) {\n          tspan.setAttribute(\"dy\", \"1em\");\n        }\n        tspan.textContent = row;\n        svgLabel.appendChild(tspan);\n      }\n      return svgLabel;\n    }).attr(\"x\", 10).attr(\"y\", function(d, i) {\n      if (i > 0) {\n        for (let j = 0; j < i; j++) {\n          prevGap += numOccurrences[i - 1][1];\n          return d[1] * theGap / 2 + prevGap * theGap + theTopPad;\n        }\n      } else {\n        return d[1] * theGap / 2 + theTopPad;\n      }\n    }).attr(\"font-size\", conf.sectionFontSize).attr(\"class\", function(d) {\n      for (const [i, category] of categories.entries()) {\n        if (d[0] === category) {\n          return \"sectionTitle sectionTitle\" + i % conf.numberSectionStyles;\n        }\n      }\n      return \"sectionTitle\";\n    });\n  }\n  function drawToday(theSidePad, theTopPad, w2, h2) {\n    const todayMarker2 = diagObj.db.getTodayMarker();\n    if (todayMarker2 === \"off\") {\n      return;\n    }\n    const todayG = svg.append(\"g\").attr(\"class\", \"today\");\n    const today = /* @__PURE__ */ new Date();\n    const todayLine = todayG.append(\"line\");\n    todayLine.attr(\"x1\", timeScale(today) + theSidePad).attr(\"x2\", timeScale(today) + theSidePad).attr(\"y1\", conf.titleTopMargin).attr(\"y2\", h2 - conf.titleTopMargin).attr(\"class\", \"today\");\n    if (todayMarker2 !== \"\") {\n      todayLine.attr(\"style\", todayMarker2.replace(/,/g, \";\"));\n    }\n  }\n  function checkUnique(arr) {\n    const hash = {};\n    const result = [];\n    for (let i = 0, l = arr.length; i < l; ++i) {\n      if (!Object.prototype.hasOwnProperty.call(hash, arr[i])) {\n        hash[arr[i]] = true;\n        result.push(arr[i]);\n      }\n    }\n    return result;\n  }\n};\nconst ganttRenderer = {\n  setConf,\n  draw\n};\nconst getStyles = (options) => `\n  .mermaid-main-font {\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n  .exclude-range {\n    fill: ${options.excludeBkgColor};\n  }\n\n  .section {\n    stroke: none;\n    opacity: 0.2;\n  }\n\n  .section0 {\n    fill: ${options.sectionBkgColor};\n  }\n\n  .section2 {\n    fill: ${options.sectionBkgColor2};\n  }\n\n  .section1,\n  .section3 {\n    fill: ${options.altSectionBkgColor};\n    opacity: 0.2;\n  }\n\n  .sectionTitle0 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle1 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle2 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle3 {\n    fill: ${options.titleColor};\n  }\n\n  .sectionTitle {\n    text-anchor: start;\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n\n  /* Grid and axis */\n\n  .grid .tick {\n    stroke: ${options.gridColor};\n    opacity: 0.8;\n    shape-rendering: crispEdges;\n  }\n\n  .grid .tick text {\n    font-family: ${options.fontFamily};\n    fill: ${options.textColor};\n  }\n\n  .grid path {\n    stroke-width: 0;\n  }\n\n\n  /* Today line */\n\n  .today {\n    fill: none;\n    stroke: ${options.todayLineColor};\n    stroke-width: 2px;\n  }\n\n\n  /* Task styling */\n\n  /* Default task */\n\n  .task {\n    stroke-width: 2;\n  }\n\n  .taskText {\n    text-anchor: middle;\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n  .taskTextOutsideRight {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: start;\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n\n  .taskTextOutsideLeft {\n    fill: ${options.taskTextDarkColor};\n    text-anchor: end;\n  }\n\n\n  /* Special case clickable */\n\n  .task.clickable {\n    cursor: pointer;\n  }\n\n  .taskText.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideLeft.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n  .taskTextOutsideRight.clickable {\n    cursor: pointer;\n    fill: ${options.taskTextClickableColor} !important;\n    font-weight: bold;\n  }\n\n\n  /* Specific task settings for the sections*/\n\n  .taskText0,\n  .taskText1,\n  .taskText2,\n  .taskText3 {\n    fill: ${options.taskTextColor};\n  }\n\n  .task0,\n  .task1,\n  .task2,\n  .task3 {\n    fill: ${options.taskBkgColor};\n    stroke: ${options.taskBorderColor};\n  }\n\n  .taskTextOutside0,\n  .taskTextOutside2\n  {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n  .taskTextOutside1,\n  .taskTextOutside3 {\n    fill: ${options.taskTextOutsideColor};\n  }\n\n\n  /* Active task */\n\n  .active0,\n  .active1,\n  .active2,\n  .active3 {\n    fill: ${options.activeTaskBkgColor};\n    stroke: ${options.activeTaskBorderColor};\n  }\n\n  .activeText0,\n  .activeText1,\n  .activeText2,\n  .activeText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Completed task */\n\n  .done0,\n  .done1,\n  .done2,\n  .done3 {\n    stroke: ${options.doneTaskBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneText0,\n  .doneText1,\n  .doneText2,\n  .doneText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n\n  /* Tasks on the critical line */\n\n  .crit0,\n  .crit1,\n  .crit2,\n  .crit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.critBkgColor};\n    stroke-width: 2;\n  }\n\n  .activeCrit0,\n  .activeCrit1,\n  .activeCrit2,\n  .activeCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.activeTaskBkgColor};\n    stroke-width: 2;\n  }\n\n  .doneCrit0,\n  .doneCrit1,\n  .doneCrit2,\n  .doneCrit3 {\n    stroke: ${options.critBorderColor};\n    fill: ${options.doneTaskBkgColor};\n    stroke-width: 2;\n    cursor: pointer;\n    shape-rendering: crispEdges;\n  }\n\n  .milestone {\n    transform: rotate(45deg) scale(0.8,0.8);\n  }\n\n  .milestoneText {\n    font-style: italic;\n  }\n  .doneCritText0,\n  .doneCritText1,\n  .doneCritText2,\n  .doneCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .activeCritText0,\n  .activeCritText1,\n  .activeCritText2,\n  .activeCritText3 {\n    fill: ${options.taskTextDarkColor} !important;\n  }\n\n  .titleText {\n    text-anchor: middle;\n    font-size: 18px;\n    fill: ${options.titleColor || options.textColor};\n    font-family: var(--mermaid-font-family, \"trebuchet ms\", verdana, arial, sans-serif);\n  }\n`;\nconst ganttStyles = getStyles;\nconst diagram = {\n  parser: ganttParser,\n  db: ganttDb,\n  renderer: ganttRenderer,\n  styles: ganttStyles\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/ganttDiagram-c361ad54.js\n"));

/***/ })

}]);