"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_hu-HU-VIYZI3X4_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/hu-HU-VIYZI3X4.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/hu-HU-VIYZI3X4.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ hu_HU_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/hu-HU.json\nvar labels = {\n  paste: \"Beilleszt\\xE9s\",\n  pasteAsPlaintext: \"Beilleszt\\xE9s form\\xE1zatlan sz\\xF6vegk\\xE9nt\",\n  pasteCharts: \"Grafikon beilleszt\\xE9se\",\n  selectAll: \"\\xD6sszes kijel\\xF6l\\xE9se\",\n  multiSelect: \"Elem hozz\\xE1ad\\xE1sa a kijel\\xF6l\\xE9shez\",\n  moveCanvas: \"V\\xE1szon mozgat\\xE1sa\",\n  cut: \"Kiv\\xE1g\\xE1s\",\n  copy: \"M\\xE1sol\\xE1s\",\n  copyAsPng: \"V\\xE1g\\xF3lapra m\\xE1sol\\xE1s mint PNG\",\n  copyAsSvg: \"V\\xE1g\\xF3lapra m\\xE1sol\\xE1s mint SVG\",\n  copyText: \"V\\xE1g\\xF3lapra m\\xE1sol\\xE1s sz\\xF6vegk\\xE9nt\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"El\\u0151r\\xE9bb hoz\\xE1s\",\n  sendToBack: \"H\\xE1trak\\xFCld\\xE9s\",\n  bringToFront: \"El\\u0151rehoz\\xE1s\",\n  sendBackward: \"H\\xE1tr\\xE9bb k\\xFCld\\xE9s\",\n  delete: \"T\\xF6rl\\xE9s\",\n  copyStyles: \"St\\xEDlus m\\xE1sol\\xE1sa\",\n  pasteStyles: \"St\\xEDlus beilleszt\\xE9se\",\n  stroke: \"K\\xF6rvonal\",\n  background: \"H\\xE1tt\\xE9r\",\n  fill: \"Kit\\xF6lt\\xE9s\",\n  strokeWidth: \"K\\xF6rvonal vastags\\xE1ga\",\n  strokeStyle: \"K\\xF6rvonal st\\xEDlusa\",\n  strokeStyle_solid: \"Kit\\xF6lt\\xF6tt\",\n  strokeStyle_dashed: \"Szaggatott\",\n  strokeStyle_dotted: \"Pontozott\",\n  sloppiness: \"St\\xEDlus\",\n  opacity: \"\\xC1ttetsz\\u0151s\\xE9g\",\n  textAlign: \"Sz\\xF6veg igaz\\xEDt\\xE1sa\",\n  edges: \"Sz\\xE9lek\",\n  sharp: \"\\xC9les\",\n  round: \"Kerek\",\n  arrowheads: \"Ny\\xEDlhegyek\",\n  arrowhead_none: \"Nincs\",\n  arrowhead_arrow: \"Ny\\xEDl\",\n  arrowhead_bar: \"Oszlop\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"H\\xE1romsz\\xF6g\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Bet\\u0171m\\xE9ret\",\n  fontFamily: \"Bet\\u0171k\\xE9szlet csal\\xE1d\",\n  addWatermark: 'Add hozz\\xE1, hogy \"Excalidraw-val k\\xE9sz\\xFClt\"',\n  handDrawn: \"K\\xE9zzel rajzolt\",\n  normal: \"Norm\\xE1l\",\n  code: \"K\\xF3d\",\n  small: \"Kicsi\",\n  medium: \"K\\xF6zepes\",\n  large: \"Nagy\",\n  veryLarge: \"Nagyon nagy\",\n  solid: \"Kit\\xF6lt\\xF6tt\",\n  hachure: \"Vonalk\\xE1zott\",\n  zigzag: \"Cikkcakk\",\n  crossHatch: \"Keresztcs\\xEDkozott\",\n  thin: \"V\\xE9kony\",\n  bold: \"F\\xE9lk\\xF6v\\xE9r\",\n  left: \"Bal\",\n  center: \"K\\xF6z\\xE9p\",\n  right: \"Jobb\",\n  extraBold: \"Extra F\\xE9lk\\xF6v\\xE9r\",\n  architect: \"Tervez\\u0151i\",\n  artist: \"M\\u0171v\\xE9szi\",\n  cartoonist: \"Karikat\\xFAr\\xE1s\",\n  fileTitle: \"F\\xE1jln\\xE9v\",\n  colorPicker: \"Sz\\xEDnv\\xE1laszt\\xF3\",\n  canvasColors: \"Rajzv\\xE1szonon haszn\\xE1lt\",\n  canvasBackground: \"V\\xE1szon h\\xE1tt\\xE9rsz\\xEDne\",\n  drawingCanvas: \"Rajzv\\xE1szon\",\n  layers: \"R\\xE9tegek\",\n  actions: \"M\\u0171veletek\",\n  language: \"Nyelv\",\n  liveCollaboration: \"\\xC9l\\u0151 egy\\xFCttm\\u0171k\\xF6d\\xE9s...\",\n  duplicateSelection: \"Duplik\\xE1l\\xE1s\",\n  untitled: \"N\\xE9vtelen\",\n  name: \"N\\xE9v\",\n  yourName: \"Neved\",\n  madeWithExcalidraw: \"Excalidraw-val k\\xE9sz\\xFClt\",\n  group: \"Csoportos\\xEDt\\xE1s\",\n  ungroup: \"Csoportbont\\xE1s\",\n  collaborators: \"K\\xF6zrem\\u0171k\\xF6d\\u0151k\",\n  showGrid: \"R\\xE1cs megjelen\\xEDt\\xE9se\",\n  addToLibrary: \"Hozz\\xE1ad\\xE1s a k\\xF6nyvt\\xE1rhoz\",\n  removeFromLibrary: \"Elt\\xE1v\\xF3l\\xEDt\\xE1s a k\\xF6nyvt\\xE1rb\\xF3l\",\n  libraryLoadingMessage: \"K\\xF6nyvt\\xE1r bet\\xF6lt\\xE9se\\u2026\",\n  libraries: \"K\\xF6nyvt\\xE1rak b\\xF6ng\\xE9sz\\xE9se\",\n  loadingScene: \"Jelenet bet\\xF6lt\\xE9se\\u2026\",\n  align: \"Igaz\\xEDt\\xE1s\",\n  alignTop: \"Fel\\xFClre igaz\\xEDt\\xE1s\",\n  alignBottom: \"Alulra igaz\\xEDt\\xE1s\",\n  alignLeft: \"Balra igaz\\xEDt\\xE1s\",\n  alignRight: \"Jobbra igaz\\xEDt\\xE1s\",\n  centerVertically: \"F\\xFCgg\\u0151legesen k\\xF6z\\xE9pre igaz\\xEDtott\",\n  centerHorizontally: \"V\\xEDzszintesen k\\xF6z\\xE9pre igaz\\xEDtott\",\n  distributeHorizontally: \"V\\xEDzszintes eloszt\\xE1s\",\n  distributeVertically: \"F\\xFCgg\\u0151leges eloszt\\xE1s\",\n  flipHorizontal: \"V\\xEDzszintes t\\xFCkr\\xF6z\\xE9s\",\n  flipVertical: \"F\\xFCgg\\u0151leges t\\xFCkr\\xF6z\\xE9s\",\n  viewMode: \"N\\xE9zet\",\n  share: \"Megoszt\\xE1s\",\n  showStroke: \"K\\xF6rvonal sz\\xEDnv\\xE1laszt\\xF3 megjelen\\xEDt\\xE9se\",\n  showBackground: \"H\\xE1tt\\xE9rsz\\xEDn-v\\xE1laszt\\xF3 megjelen\\xEDt\\xE9se\",\n  toggleTheme: \"T\\xE9ma v\\xE1lt\\xE1sa\",\n  personalLib: \"Szem\\xE9lyes k\\xF6nyvt\\xE1r\",\n  excalidrawLib: \"Excalidraw k\\xF6nyvt\\xE1r\",\n  decreaseFontSize: \"Bet\\u0171m\\xE9ret cs\\xF6kkent\\xE9se\",\n  increaseFontSize: \"Bet\\u0171m\\xE9ret n\\xF6vel\\xE9se\",\n  unbindText: \"Sz\\xF6vegk\\xF6t\\xE9s felold\\xE1sa\",\n  bindText: \"\",\n  createContainerFromText: \"Sz\\xF6veg bekeretez\\xE9se\",\n  link: {\n    edit: \"Hivatkoz\\xE1s szerkeszt\\xE9se\",\n    editEmbed: \"Link szerkeszt\\xE9se / be\\xE1gyaz\\xE1sa\",\n    create: \"Hivatkoz\\xE1s l\\xE9trehoz\\xE1sa\",\n    createEmbed: \"Link l\\xE9trehoz\\xE1sa / be\\xE1gyaz\\xE1sa\",\n    label: \"Hivatkoz\\xE1s\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"R\\xF6gz\\xEDt\\xE9s\",\n    unlock: \"R\\xF6gz\\xEDt\\xE9s felold\\xE1sa\",\n    lockAll: \"\\xD6sszes r\\xF6gz\\xEDt\\xE9se\",\n    unlockAll: \"\\xD6sszes felold\\xE1sa\"\n  },\n  statusPublished: \"\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"V\\xE1szon t\\xF6rl\\xE9se\",\n  exportJSON: \"Export\\xE1l\\xE1s f\\xE1jlba\",\n  exportImage: \"K\\xE9p export\\xE1l\\xE1sa...\",\n  export: \"Ment\\xE9s m\\xE1sk\\xE9nt...\",\n  copyToClipboard: \"V\\xE1g\\xF3lapra m\\xE1sol\\xE1s\",\n  save: \"Ment\\xE9s az aktu\\xE1lis f\\xE1jlba\",\n  saveAs: \"Ment\\xE9s m\\xE1sk\\xE9nt\",\n  load: \"Megnyit\\xE1s\",\n  getShareableLink: \"Megoszthat\\xF3 link l\\xE9trehoz\\xE1sa\",\n  close: \"Bez\\xE1r\\xE1s\",\n  selectLanguage: \"Nyelv kiv\\xE1laszt\\xE1sa\",\n  scrollBackToContent: \"Visszag\\xF6rget\\xE9s a tartalomhoz\",\n  zoomIn: \"Nagy\\xEDt\\xE1s\",\n  zoomOut: \"Kicsiny\\xEDt\\xE9s\",\n  resetZoom: \"Nagy\\xEDt\\xE1s alaphelyzetbe\",\n  menu: \"Men\\xFC\",\n  done: \"K\\xE9sz\",\n  edit: \"Szerkeszt\\xE9s\",\n  undo: \"Vissza\",\n  redo: \"\\xDAjra\",\n  resetLibrary: \"K\\xF6nyvt\\xE1r alaphelyzetbe \\xE1ll\\xEDt\\xE1sa\",\n  createNewRoom: \"\\xDAj szoba l\\xE9trehoz\\xE1sa\",\n  fullScreen: \"Teljes k\\xE9perny\\u0151\",\n  darkMode: \"S\\xF6t\\xE9t m\\xF3d\",\n  lightMode: \"Vil\\xE1gos m\\xF3d\",\n  zenMode: \"Letisztult m\\xF3d\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Kil\\xE9p\\xE9s a letisztult m\\xF3db\\xF3l\",\n  cancel: \"M\\xE9gsem\",\n  clear: \"Ki\\u0171r\\xEDt\\xE9s\",\n  remove: \"Elt\\xE1vol\\xEDt\\xE1s\",\n  embed: \"\",\n  publishLibrary: \"K\\xF6zz\\xE9t\\xE9tel\",\n  submit: \"Elk\\xFCld\\xE9s\",\n  confirm: \"Meger\\u0151s\\xEDt\\xE9s\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Ez a m\\u0171velet t\\xF6rli a v\\xE1szont. Biztos benne?\",\n  couldNotCreateShareableLink: \"Nem siker\\xFClt megoszthat\\xF3 linket l\\xE9trehozni.\",\n  couldNotCreateShareableLinkTooBig: \"Nem siker\\xFClt megoszthat\\xF3 linket l\\xE1trehozni: t\\xFAl nagy a jelenet\",\n  couldNotLoadInvalidFile: \"Nem siker\\xFClt bet\\xF6lteni a helytelen f\\xE1jlt\",\n  importBackendFailed: \"Nem siker\\xFClt bet\\xF6lteni a szerverr\\u0151l.\",\n  cannotExportEmptyCanvas: \"\\xDCres v\\xE1szont nem lehet export\\xE1lni.\",\n  couldNotCopyToClipboard: \"\",\n  decryptFailed: \"Nem siker\\xFClt visszafejteni a titkos\\xEDtott adatot.\",\n  uploadedSecurly: \"A felt\\xF6lt\\xE9st v\\xE9gpontok k\\xF6z\\xF6tti titkos\\xEDt\\xE1ssal biztos\\xEDtottuk, ami azt jelenti, hogy egy harmadik f\\xE9l nem tudja megn\\xE9zni a tartalm\\xE1t, bele\\xE9rtve az Excalidraw szervereit is.\",\n  loadSceneOverridePrompt: \"A bet\\xF6lt\\xF6tt k\\xFCls\\u0151 rajz fel\\xFCl fogja \\xEDrnia megl\\xE9v\\u0151t. Szeretn\\xE9d folytatni?\",\n  collabStopOverridePrompt: \"A munkamenet le\\xE1ll\\xEDt\\xE1sa fel\\xFCl fogja \\xEDrni az el\\u0151z\\u0151leg helyben t\\xE1rolt rajzot. Biztosan ezt akarod?\\n(Ha meg akarod tartani a helyben t\\xE1rolt rajzot, egyszer\\u0171en csak z\\xE1rd be a b\\xF6ng\\xE9sz\\u0151 f\\xFClet)\",\n  errorAddingToLibrary: \"A t\\xE9tel nem addhat\\xF3 hozz\\xE1 a k\\xF6nyvt\\xE1rhoz\",\n  errorRemovingFromLibrary: \"A t\\xE9tel nem t\\xE1vol\\xEDthat\\xF3 el a k\\xF6nyvt\\xE1rb\\xF3l\",\n  confirmAddLibrary: \"Ez a m\\u0171velet {{numShapes}} form\\xE1t fog hozz\\xE1adni a k\\xF6nyvt\\xE1radhoz. Biztos vagy benne?\",\n  imageDoesNotContainScene: \"\\xDAgy t\\u0171nik, hogy ez a k\\xE9p nem tartalmaz jelenetadatokat. Enged\\xE9lyezted a jelenetbe\\xE1gyaz\\xE1st az export\\xE1l\\xE1s sor\\xE1n?\",\n  cannotRestoreFromImage: \"A jelenet vissza\\xE1ll\\xEDt\\xE1sa nem siker\\xFClt ebb\\u0151l a k\\xE9p f\\xE1jlb\\xF3l\",\n  invalidSceneUrl: \"Nem siker\\xFClt import\\xE1lni a jelenetet a megadott URL-r\\u0151l. Rossz form\\xE1tum\\xFA, vagy nem tartalmaz \\xE9rv\\xE9nyes Excalidraw JSON-adatokat.\",\n  resetLibrary: \"Ezzel t\\xF6rl\\xF6d a k\\xF6nyvt\\xE1r\\xE1t. biztos vagy ebben?\",\n  removeItemsFromsLibrary: \"{{count}} elemet t\\xF6r\\xF6lsz a k\\xF6nyvt\\xE1rb\\xF3l?\",\n  invalidEncryptionKey: \"A titkos\\xEDt\\xE1si kulcsnak 22 karakterb\\u0151l kell \\xE1llnia. Az \\xE9l\\u0151 egy\\xFCttm\\u0171k\\xF6d\\xE9s le van tiltva.\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"Nem t\\xE1mogatott f\\xE1jlt\\xEDpus.\",\n  imageInsertError: \"Nem siker\\xFClt besz\\xFArni a k\\xE9pet. Pr\\xF3b\\xE1ld \\xFAjra k\\xE9s\\u0151bb...\",\n  fileTooBig: \"A f\\xE1jl t\\xFAl nagy. A megengedett maxim\\xE1lis m\\xE9ret {{maxSize}}.\",\n  svgImageInsertError: \"Nem siker\\xFClt besz\\xFArni az SVG-k\\xE9pet. Az SVG szintaktika \\xE9rv\\xE9nytelennek t\\u0171nik.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"\\xC9rv\\xE9nytelen SVG.\",\n  cannotResolveCollabServer: \"\",\n  importLibraryError: \"\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Kijel\\xF6l\\xE9s\",\n  image: \"K\\xE9p besz\\xFAr\\xE1sa\",\n  rectangle: \"T\\xE9glalap\",\n  diamond: \"Rombusz\",\n  ellipse: \"Ellipszis\",\n  arrow: \"Ny\\xEDl\",\n  line: \"Vonal\",\n  freedraw: \"Rajzol\\xE1s\",\n  text: \"Sz\\xF6veg\",\n  library: \"K\\xF6nyvt\\xE1r\",\n  lock: \"Rajzol\\xE1s ut\\xE1n az akt\\xEDv eszk\\xF6zt tartsa kijel\\xF6lve\",\n  penMode: \"\",\n  link: \"Hivatkoz\\xE1s hozz\\xE1ad\\xE1sa/friss\\xEDt\\xE9se a kiv\\xE1lasztott alakzathoz\",\n  eraser: \"Rad\\xEDr\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"Weblap be\\xE1gyaz\\xE1sa\",\n  laser: \"L\\xE9zermutat\\xF3\",\n  hand: \"\",\n  extraTools: \"Tov\\xE1bbi eszk\\xF6z\\xF6k\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"V\\xE1szon m\\u0171veletek\",\n  selectedShapeActions: \"Kijel\\xF6lt forma m\\u0171veletei\",\n  shapes: \"Alakzatok\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"Kattint\\xE1ssal g\\xF6rbe, az eger h\\xFAz\\xE1s\\xE1val pedig egyenes nyilat rajzolhatsz\",\n  freeDraw: \"Kattints \\xE9s h\\xFAzd, majd engedd el, amikor v\\xE9gezt\\xE9l\",\n  text: \"Tipp: A kijel\\xF6l\\xE9s eszk\\xF6zzel a dupla kattint\\xE1s \\xFAj sz\\xF6veget hoz l\\xE9tre\",\n  embeddable: \"\",\n  text_selected: \"Kattints dupl\\xE1n, vagy nyomj entert a sz\\xF6veg szerkeszt\\xE9s\\xE9hez\",\n  text_editing: \"Nyomjd meg az Escape vagy a Ctrl/Cmd+ENTER billenty\\u0171kombin\\xE1ci\\xF3t a szerkeszt\\xE9s befejez\\xE9s\\xE9hez\",\n  linearElementMulti: \"Kattints a k\\xF6vetkez\\u0151 \\xEDv poz\\xEDci\\xF3j\\xE1ra, vagy fejezd be a nyilat az Escape vagy Enter megnyom\\xE1s\\xE1val\",\n  lockAngle: \"A SHIFT billenty\\u0171 lenyomva tart\\xE1s\\xE1val korl\\xE1tozhatja forgat\\xE1s sz\\xF6g\\xE9t\",\n  resize: \"A SHIFT billenty\\u0171 lenyomva tart\\xE1s\\xE1val az \\xE1tm\\xE9retez\\xE9s megtartja az ar\\xE1nyokat,\\naz ALT lenyomva tart\\xE1s\\xE1val pedig a k\\xF6z\\xE9ppont egy helyben marad\",\n  resizeImage: \"A SHIFT billenty\\u0171 lenyomva tart\\xE1s\\xE1val szabadon \\xE1tm\\xE9retezheted,\\ntartsd lenyomva az ALT billenty\\u0171t a k\\xF6z\\xE9pr\\u0151l val\\xF3 \\xE1tm\\xE9retez\\xE9shez\",\n  rotate: \"A SHIFT billenty\\u0171 lenyomva tart\\xE1s\\xE1val korl\\xE1tozhatja a sz\\xF6gek illeszt\\xE9s\\xE9t\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"Nyomd meg a T\\xF6rl\\xE9s gombot a pont(ok) elt\\xE1vol\\xEDt\\xE1s\\xE1hoz,\\nA Ctrl/Cmd+D a t\\xF6bbsz\\xF6r\\xF6z\\xE9shez, vagy h\\xFAz\\xE1ssal mozgathatja\",\n  lineEditor_nothingSelected: \"V\\xE1laszd ki a szerkeszteni k\\xEDv\\xE1nt pontot (t\\xF6bb kijel\\xF6l\\xE9s\\xE9hez tartsd lenyomva a SHIFT billenty\\u0171t),\\nvagy Alt, \\xE9s kattintson az \\xFAj pontok hozz\\xE1ad\\xE1s\\xE1hoz\",\n  placeImage: \"Kattints a k\\xE9p elhelyez\\xE9s\\xE9hez, vagy kattints \\xE9s m\\xE9retezd manu\\xE1lisan\",\n  publishLibrary: \"Tedd k\\xF6zz\\xE9 saj\\xE1t k\\xF6nyvt\\xE1radat\",\n  bindTextToElement: \"Nyomd meg az Entert sz\\xF6veg hozz\\xE1ad\\xE1shoz\",\n  deepBoxSelect: \"Tartsd lenyomva a Ctrl/Cmd billenty\\u0171t a m\\xE9ly kijel\\xF6l\\xE9shez \\xE9s a h\\xFAz\\xE1s megakad\\xE1lyoz\\xE1s\\xE1hoz\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"El\\u0151n\\xE9zet nem jelen\\xEDthet\\u0151 meg\",\n  canvasTooBig: \"A v\\xE1szon tal\\xE1n t\\xFAl nagy.\",\n  canvasTooBigTip: \"Tipp: pr\\xF3b\\xE1ld meg a legt\\xE1volabbi elemeket k\\xF6zelebb hozni egy m\\xE1shoz.\"\n};\nvar errorSplash = {\n  headingMain: \"Hiba t\\xF6rt\\xE9nt. Pr\\xF3b\\xE1ld <button>\\xFAjrat\\xF6lteni az oldalt.</button>\",\n  clearCanvasMessage: \"Ha az \\xFAjrat\\xF6lt\\xE9s nem m\\u0171k\\xF6dik, pr\\xF3b\\xE1ld <button>let\\xF6r\\xF6lni a v\\xE1szont.</button>\",\n  clearCanvasCaveat: \" Ezzel az eddigi munka elveszik \",\n  trackedToSentry: \"A hibak\\xF3d azonos\\xEDt\\xF3val {{eventId}} nyomon van k\\xF6vetve a rendszer\\xFCnkben.\",\n  openIssueMessage: \"Vigy\\xE1ztunk arra, hogy a jelenthez tartoz\\xF3 inform\\xE1ci\\xF3 ne jelenjen meg a hiba\\xFCzenetben. Ha a jeleneted nem bizalmas, k\\xE9rj\\xFCk add hozz\\xE1 a <button>hibak\\xF6vet\\u0151 rendszer\\xFCnkh\\xF6z.</button> K\\xE9rj\\xFCk, m\\xE1solja be az al\\xE1bbi inform\\xE1ci\\xF3kat a GitHub probl\\xE9m\\xE1ba.\",\n  sceneContent: \"Jelenet tartalma:\"\n};\nvar roomDialog = {\n  desc_intro: \"Megh\\xEDvhat embereket a jelenlegi jelenetbe, hogy egy\\xFCttm\\u0171k\\xF6djenek \\xF6nnel.\",\n  desc_privacy: \"Ne agg\\xF3dj, a munkamenet v\\xE9gpontok k\\xF6z\\xF6tti titkos\\xEDt\\xE1st haszn\\xE1l, teh\\xE1t b\\xE1rmit rajzolsz, priv\\xE1t marad. M\\xE9g a szerver\\xFCnkr\\u0151l se lehet belen\\xE9zni.\",\n  button_startSession: \"Munkamenet ind\\xEDt\\xE1sa\",\n  button_stopSession: \"Munkamenet le\\xE1ll\\xEDt\\xE1sa\",\n  desc_inProgressIntro: \"Az \\xE9l\\u0151 egy\\xFCttm\\u0171k\\xF6d\\xE9si munkamenet folyamatban van.\",\n  desc_shareLink: \"Ossza meg ezt a linket b\\xE1rkivel, akivel egy\\xFCtt szeretne m\\u0171k\\xF6dni:\",\n  desc_exitSession: \"Az munkamenet le\\xE1ll\\xEDt\\xE1sa kil\\xE9pteti \\xF6nt a szob\\xE1b\\xF3l, de folytathatja a munk\\xE1t a saj\\xE1t g\\xE9p\\xE9n. Vegye figyelembe, hogy ez nem \\xE9rinti m\\xE1s emberek munk\\xE1j\\xE1t \\xE9s \\u0151k tov\\xE1bbra is egy\\xFCttm\\u0171k\\xF6dhetnek a saj\\xE1t v\\xE1ltozatukon.\",\n  shareTitle: \"Csatlakoz\\xE1s egy \\xE9l\\u0151 egy\\xFCttm\\u0171k\\xF6d\\xE9si munkamenethez az Excalidraw-ban\"\n};\nvar errorDialog = {\n  title: \"Hiba\"\n};\nvar exportDialog = {\n  disk_title: \"Ment\\xE9s lemezre\",\n  disk_details: \"Export\\xE1lja a jelenetadatokat egy f\\xE1jlba, amelyb\\u0151l k\\xE9s\\u0151bb import\\xE1lhatja.\",\n  disk_button: \"Ment\\xE9s f\\xE1jlba\",\n  link_title: \"Megoszthat\\xF3 hivatkoz\\xE1s\",\n  link_details: \"Export\\xE1l\\xE1s csak olvashat\\xF3 hivatkoz\\xE1sk\\xE9nt.\",\n  link_button: \"Export\\xE1l\\xE1s hivatkoz\\xE1sba\",\n  excalidrawplus_description: \"Mentse el a jelenetet az Excalidraw+ munkater\\xFClet\\xE9re.\",\n  excalidrawplus_button: \"Export\\xE1l\\xE1s\",\n  excalidrawplus_exportError: \"Jelenleg nem lehet export\\xE1lni az Excalidraw+-ba...\"\n};\nvar helpDialog = {\n  blog: \"Olvasd a blogunkat\",\n  click: \"kattint\\xE1s\",\n  deepSelect: \"M\\xE9ly kijel\\xF6l\\xE9s\",\n  deepBoxSelect: \"M\\xE9ly kijel\\xF6l\\xE9s a dobozon bel\\xFCl, \\xE9s a h\\xFAz\\xE1s megakad\\xE1lyoz\\xE1sa\",\n  curvedArrow: \"\\xCDvelt ny\\xEDl\",\n  curvedLine: \"\\xCDvelt vonal\",\n  documentation: \"Dokument\\xE1ci\\xF3\",\n  doubleClick: \"dupla kattint\\xE1s\",\n  drag: \"vonszol\\xE1s\",\n  editor: \"Szerkeszt\\u0151\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"Hib\\xE1t tal\\xE1lt\\xE1l? K\\xFCld be\",\n  howto: \"K\\xF6vesd az \\xFAtmutat\\xF3inkat\",\n  or: \"vagy\",\n  preventBinding: \"A ny\\xEDl ne ragadjon\",\n  tools: \"\",\n  shortcuts: \"Gyorsbillenty\\u0171k\",\n  textFinish: \"Szerkeszt\\xE9s befejez\\xE9se (sz\\xF6veg)\",\n  textNewLine: \"\\xDAj sor hozz\\xE1ad\\xE1sa (sz\\xF6veg)\",\n  title: \"Seg\\xEDts\\xE9g\",\n  view: \"N\\xE9zet\",\n  zoomToFit: \"Az \\xF6sszes elem l\\xE1t\\xF3t\\xE9rbe hoz\\xE1sa\",\n  zoomToSelection: \"Kijel\\xF6l\\xE9sre nagy\\xEDt\\xE1s\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\"\n};\nvar clearCanvasDialog = {\n  title: \"Rajzv\\xE1szon alaphelyzetbe\"\n};\nvar publishDialog = {\n  title: \"K\\xF6nyvt\\xE1r k\\xF6zz\\xE9t\\xE9tele\",\n  itemName: \"T\\xE9tel neve\",\n  authorName: \"Szerz\\u0151 neve\",\n  githubUsername: \"GitHub felhaszn\\xE1l\\xF3n\\xE9v\",\n  twitterUsername: \"Twitter felhaszn\\xE1l\\xF3n\\xE9v\",\n  libraryName: \"K\\xF6nyvt\\xE1r neve\",\n  libraryDesc: \"K\\xF6nyvt\\xE1r le\\xEDr\\xE1sa\",\n  website: \"Weboldal\",\n  placeholder: {\n    authorName: \"Neved vagy felhaszn\\xE1l\\xF3neved\",\n    libraryName: \"A k\\xF6nyvt\\xE1rad neve\",\n    libraryDesc: \"A k\\xF6nyvt\\xE1rad haszn\\xE1lat\\xE1t seg\\xEDt\\u0151 le\\xEDr\\xE1s\",\n    githubHandle: \"GitHub-handle(opcion\\xE1lis), \\xEDgy szerkesztheted a k\\xF6nyvt\\xE1rat, miut\\xE1n elk\\xFCldted ellen\\u0151rz\\xE9sre\",\n    twitterHandle: \"Twitter-felhaszn\\xE1l\\xF3n\\xE9v (opcion\\xE1lis), \\xEDgy tudjuk, kinek kell j\\xF3v\\xE1\\xEDrni a Twitteren kereszt\\xFCli rekl\\xE1moz\\xE1st\",\n    website: \"Hivatkoz\\xE1s szem\\xE9lyes webhelyedre vagy m\\xE1shov\\xE1 (nem k\\xF6telez\\u0151)\"\n  },\n  errors: {\n    required: \"K\\xF6telez\\u0151\",\n    website: \"Adj meg egy \\xE9rv\\xE9nyes URL-t\"\n  },\n  noteDescription: \"K\\xFCld be k\\xF6nyvt\\xE1radat, hogy beker\\xFClj\\xF6n a <link>nyilv\\xE1nos k\\xF6nyvt\\xE1r t\\xE1rol\\xF3ba</link>hogy m\\xE1sok is felhaszn\\xE1lhass\\xE1k a rajzaikban.\",\n  noteGuidelines: \"A k\\xF6nyvt\\xE1rat el\\u0151sz\\xF6r manu\\xE1lisan kell j\\xF3v\\xE1hagyni. K\\xE9rj\\xFCk, olvassa el a <link>seg\\xE9dletet</link> beny\\xFAjt\\xE1sa el\\u0151tt. Sz\\xFCks\\xE9ge lesz egy GitHub-fi\\xF3kra a kommunik\\xE1ci\\xF3hoz \\xE9s a m\\xF3dos\\xEDt\\xE1sokhoz, ha k\\xE9rik, de ez nem felt\\xE9tlen\\xFCl sz\\xFCks\\xE9ges.\",\n  noteLicense: \"A bek\\xFCld\\xE9ssel elfogadja, hogy a k\\xF6nyvt\\xE1r a k\\xF6vetkez\\u0151 alatt ker\\xFCl k\\xF6zz\\xE9t\\xE9telre <link>MIT Licensz </link>ami r\\xF6viden azt jelenti, hogy b\\xE1rki korl\\xE1toz\\xE1s n\\xE9lk\\xFCl haszn\\xE1lhatja \\u0151ket.\",\n  noteItems: \"Minden k\\xF6nyvt\\xE1relemnek saj\\xE1t nev\\xE9vel kell rendelkeznie, hogy sz\\u0171rhet\\u0151 legyen. A k\\xF6vetkez\\u0151 k\\xF6nyvt\\xE1ri t\\xE9telek ker\\xFClnek bele:\",\n  atleastOneLibItem: \"A kezd\\xE9shez v\\xE1lassz ki legal\\xE1bb egy k\\xF6nyvt\\xE1ri elemet\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"A k\\xF6nyvt\\xE1r bek\\xFCldve\",\n  content: \"K\\xF6sz\\xF6nj\\xFCk {{authorName}}. K\\xF6nyvt\\xE1radat elk\\xFCldt\\xFCk fel\\xFClvizsg\\xE1latra. Nyomon k\\xF6vetheted az \\xE1llapotot<link>itt</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"K\\xF6nyvt\\xE1r alaphelyzetbe \\xE1ll\\xEDt\\xE1sa\",\n  removeItemsFromLib: \"A kiv\\xE1lasztott elemek elt\\xE1vol\\xEDt\\xE1sa a k\\xF6nyvt\\xE1rb\\xF3l\"\n};\nvar imageExportDialog = {\n  header: \"K\\xE9p export\\xE1l\\xE1sa\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"V\\xE1g\\xF3lapra m\\xE1sol\\xE1s\"\n  }\n};\nvar encrypted = {\n  tooltip: \"A rajzaidat v\\xE9gpontok k\\xF6z\\xF6tti titkos\\xEDt\\xE1ssal t\\xE1roljuk, teh\\xE1t az Excalidraw szervereir\\u0151l se tud m\\xE1s belen\\xE9zni.\",\n  link: \"Blogbejegyz\\xE9s a v\\xE9gpontok k\\xF6z\\xF6tti titkos\\xEDt\\xE1sr\\xF3l az Excalidraw-ban\"\n};\nvar stats = {\n  angle: \"Sz\\xF6g\",\n  element: \"Elem\",\n  elements: \"Elemek\",\n  height: \"Magass\\xE1g\",\n  scene: \"Jelenet\",\n  selected: \"Kijel\\xF6lt\",\n  storage: \"T\\xE1rhely\",\n  title: \"Statisztik\\xE1k\",\n  total: \"\\xD6sszesen\",\n  version: \"Verzi\\xF3\",\n  versionCopy: \"Kattints a m\\xE1sol\\xE1shoz\",\n  versionNotAvailable: \"A verzi\\xF3 nem el\\xE9rhet\\u0151\",\n  width: \"Sz\\xE9less\\xE9g\"\n};\nvar toast = {\n  addedToLibrary: \"K\\xF6nyvt\\xE1rhoz adva\",\n  copyStyles: \"M\\xE1solt st\\xEDlusok.\",\n  copyToClipboard: \"V\\xE1g\\xF3lapra m\\xE1solva.\",\n  copyToClipboardAsPng: \"Az {{exportSelection}} PNG form\\xE1tumban a v\\xE1g\\xF3lapra m\\xE1solva \\n({{exportColorScheme}})\",\n  fileSaved: \"F\\xE1jl elmentve.\",\n  fileSavedToFilename: \"Mentve mint {filename}\",\n  canvas: \"rajzv\\xE1szon\",\n  selection: \"kijel\\xF6l\\xE9s\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\\xC1tl\\xE1tsz\\xF3\",\n  black: \"Fekete\",\n  white: \"Feh\\xE9r\",\n  red: \"Piros\",\n  pink: \"R\\xF3zsasz\\xEDn\",\n  grape: \"\",\n  violet: \"Ibolya\",\n  gray: \"Sz\\xFCrke\",\n  blue: \"K\\xE9k\",\n  cyan: \"Ci\\xE1n\",\n  teal: \"K\\xE9kes-z\\xF6ld\",\n  green: \"Z\\xF6ld\",\n  yellow: \"S\\xE1rga\",\n  orange: \"Narancss\\xE1rga\",\n  bronze: \"Bronz\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"Hexadecim\\xE1lis k\\xF3d\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Export\\xE1l\\xE1s k\\xE9pk\\xE9nt\",\n      button: \"Export\\xE1l\\xE1s k\\xE9pk\\xE9nt\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"Ment\\xE9s a lemezre\",\n      button: \"Ment\\xE9s a lemezre\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Bet\\xF6lt\\xE9s f\\xE1jlb\\xF3l\",\n      button: \"Bet\\xF6lt\\xE9s f\\xE1jlb\\xF3l\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"Felt\\xF6lt\\xE1s linkb\\u0151l\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar hu_HU_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=hu-HU-VIYZI3X4.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvaHUtSFUtVklZWkkzWDQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLFlBQVk7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsUUFBUTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RUFBNEUsU0FBUztBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsVUFBVTtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxZQUFZO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLGtCQUFrQixxREFBcUQsbUJBQW1CO0FBQ3hIO0FBQ0EscUNBQXFDLFNBQVM7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUE4QkU7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvaHUtSFUtVklZWkkzWDQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiLi4vY2h1bmstWERGQ1VVVDYuanNcIjtcblxuLy8gbG9jYWxlcy9odS1IVS5qc29uXG52YXIgbGFiZWxzID0ge1xuICBwYXN0ZTogXCJCZWlsbGVzenRcXHhFOXNcIixcbiAgcGFzdGVBc1BsYWludGV4dDogXCJCZWlsbGVzenRcXHhFOXMgZm9ybVxceEUxemF0bGFuIHN6XFx4RjZ2ZWdrXFx4RTludFwiLFxuICBwYXN0ZUNoYXJ0czogXCJHcmFmaWtvbiBiZWlsbGVzenRcXHhFOXNlXCIsXG4gIHNlbGVjdEFsbDogXCJcXHhENnNzemVzIGtpamVsXFx4RjZsXFx4RTlzZVwiLFxuICBtdWx0aVNlbGVjdDogXCJFbGVtIGhvenpcXHhFMWFkXFx4RTFzYSBhIGtpamVsXFx4RjZsXFx4RTlzaGV6XCIsXG4gIG1vdmVDYW52YXM6IFwiVlxceEUxc3pvbiBtb3pnYXRcXHhFMXNhXCIsXG4gIGN1dDogXCJLaXZcXHhFMWdcXHhFMXNcIixcbiAgY29weTogXCJNXFx4RTFzb2xcXHhFMXNcIixcbiAgY29weUFzUG5nOiBcIlZcXHhFMWdcXHhGM2xhcHJhIG1cXHhFMXNvbFxceEUxcyBtaW50IFBOR1wiLFxuICBjb3B5QXNTdmc6IFwiVlxceEUxZ1xceEYzbGFwcmEgbVxceEUxc29sXFx4RTFzIG1pbnQgU1ZHXCIsXG4gIGNvcHlUZXh0OiBcIlZcXHhFMWdcXHhGM2xhcHJhIG1cXHhFMXNvbFxceEUxcyBzelxceEY2dmVna1xceEU5bnRcIixcbiAgY29weVNvdXJjZTogXCJcIixcbiAgY29udmVydFRvQ29kZTogXCJcIixcbiAgYnJpbmdGb3J3YXJkOiBcIkVsXFx1MDE1MXJcXHhFOWJiIGhvelxceEUxc1wiLFxuICBzZW5kVG9CYWNrOiBcIkhcXHhFMXRyYWtcXHhGQ2xkXFx4RTlzXCIsXG4gIGJyaW5nVG9Gcm9udDogXCJFbFxcdTAxNTFyZWhvelxceEUxc1wiLFxuICBzZW5kQmFja3dhcmQ6IFwiSFxceEUxdHJcXHhFOWJiIGtcXHhGQ2xkXFx4RTlzXCIsXG4gIGRlbGV0ZTogXCJUXFx4RjZybFxceEU5c1wiLFxuICBjb3B5U3R5bGVzOiBcIlN0XFx4RURsdXMgbVxceEUxc29sXFx4RTFzYVwiLFxuICBwYXN0ZVN0eWxlczogXCJTdFxceEVEbHVzIGJlaWxsZXN6dFxceEU5c2VcIixcbiAgc3Ryb2tlOiBcIktcXHhGNnJ2b25hbFwiLFxuICBiYWNrZ3JvdW5kOiBcIkhcXHhFMXR0XFx4RTlyXCIsXG4gIGZpbGw6IFwiS2l0XFx4RjZsdFxceEU5c1wiLFxuICBzdHJva2VXaWR0aDogXCJLXFx4RjZydm9uYWwgdmFzdGFnc1xceEUxZ2FcIixcbiAgc3Ryb2tlU3R5bGU6IFwiS1xceEY2cnZvbmFsIHN0XFx4RURsdXNhXCIsXG4gIHN0cm9rZVN0eWxlX3NvbGlkOiBcIktpdFxceEY2bHRcXHhGNnR0XCIsXG4gIHN0cm9rZVN0eWxlX2Rhc2hlZDogXCJTemFnZ2F0b3R0XCIsXG4gIHN0cm9rZVN0eWxlX2RvdHRlZDogXCJQb250b3pvdHRcIixcbiAgc2xvcHBpbmVzczogXCJTdFxceEVEbHVzXCIsXG4gIG9wYWNpdHk6IFwiXFx4QzF0dGV0c3pcXHUwMTUxc1xceEU5Z1wiLFxuICB0ZXh0QWxpZ246IFwiU3pcXHhGNnZlZyBpZ2F6XFx4RUR0XFx4RTFzYVwiLFxuICBlZGdlczogXCJTelxceEU5bGVrXCIsXG4gIHNoYXJwOiBcIlxceEM5bGVzXCIsXG4gIHJvdW5kOiBcIktlcmVrXCIsXG4gIGFycm93aGVhZHM6IFwiTnlcXHhFRGxoZWd5ZWtcIixcbiAgYXJyb3doZWFkX25vbmU6IFwiTmluY3NcIixcbiAgYXJyb3doZWFkX2Fycm93OiBcIk55XFx4RURsXCIsXG4gIGFycm93aGVhZF9iYXI6IFwiT3N6bG9wXCIsXG4gIGFycm93aGVhZF9jaXJjbGU6IFwiXCIsXG4gIGFycm93aGVhZF9jaXJjbGVfb3V0bGluZTogXCJcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlOiBcIkhcXHhFMXJvbXN6XFx4RjZnXCIsXG4gIGFycm93aGVhZF90cmlhbmdsZV9vdXRsaW5lOiBcIlwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZDogXCJcIixcbiAgYXJyb3doZWFkX2RpYW1vbmRfb3V0bGluZTogXCJcIixcbiAgZm9udFNpemU6IFwiQmV0XFx1MDE3MW1cXHhFOXJldFwiLFxuICBmb250RmFtaWx5OiBcIkJldFxcdTAxNzFrXFx4RTlzemxldCBjc2FsXFx4RTFkXCIsXG4gIGFkZFdhdGVybWFyazogJ0FkZCBob3p6XFx4RTEsIGhvZ3kgXCJFeGNhbGlkcmF3LXZhbCBrXFx4RTlzelxceEZDbHRcIicsXG4gIGhhbmREcmF3bjogXCJLXFx4RTl6emVsIHJhanpvbHRcIixcbiAgbm9ybWFsOiBcIk5vcm1cXHhFMWxcIixcbiAgY29kZTogXCJLXFx4RjNkXCIsXG4gIHNtYWxsOiBcIktpY3NpXCIsXG4gIG1lZGl1bTogXCJLXFx4RjZ6ZXBlc1wiLFxuICBsYXJnZTogXCJOYWd5XCIsXG4gIHZlcnlMYXJnZTogXCJOYWd5b24gbmFneVwiLFxuICBzb2xpZDogXCJLaXRcXHhGNmx0XFx4RjZ0dFwiLFxuICBoYWNodXJlOiBcIlZvbmFsa1xceEUxem90dFwiLFxuICB6aWd6YWc6IFwiQ2lra2Nha2tcIixcbiAgY3Jvc3NIYXRjaDogXCJLZXJlc3p0Y3NcXHhFRGtvem90dFwiLFxuICB0aGluOiBcIlZcXHhFOWtvbnlcIixcbiAgYm9sZDogXCJGXFx4RTlsa1xceEY2dlxceEU5clwiLFxuICBsZWZ0OiBcIkJhbFwiLFxuICBjZW50ZXI6IFwiS1xceEY2elxceEU5cFwiLFxuICByaWdodDogXCJKb2JiXCIsXG4gIGV4dHJhQm9sZDogXCJFeHRyYSBGXFx4RTlsa1xceEY2dlxceEU5clwiLFxuICBhcmNoaXRlY3Q6IFwiVGVydmV6XFx1MDE1MWlcIixcbiAgYXJ0aXN0OiBcIk1cXHUwMTcxdlxceEU5c3ppXCIsXG4gIGNhcnRvb25pc3Q6IFwiS2FyaWthdFxceEZBclxceEUxc1wiLFxuICBmaWxlVGl0bGU6IFwiRlxceEUxamxuXFx4RTl2XCIsXG4gIGNvbG9yUGlja2VyOiBcIlN6XFx4RURudlxceEUxbGFzenRcXHhGM1wiLFxuICBjYW52YXNDb2xvcnM6IFwiUmFqenZcXHhFMXN6b25vbiBoYXN6blxceEUxbHRcIixcbiAgY2FudmFzQmFja2dyb3VuZDogXCJWXFx4RTFzem9uIGhcXHhFMXR0XFx4RTlyc3pcXHhFRG5lXCIsXG4gIGRyYXdpbmdDYW52YXM6IFwiUmFqenZcXHhFMXN6b25cIixcbiAgbGF5ZXJzOiBcIlJcXHhFOXRlZ2VrXCIsXG4gIGFjdGlvbnM6IFwiTVxcdTAxNzF2ZWxldGVrXCIsXG4gIGxhbmd1YWdlOiBcIk55ZWx2XCIsXG4gIGxpdmVDb2xsYWJvcmF0aW9uOiBcIlxceEM5bFxcdTAxNTEgZWd5XFx4RkN0dG1cXHUwMTcxa1xceEY2ZFxceEU5cy4uLlwiLFxuICBkdXBsaWNhdGVTZWxlY3Rpb246IFwiRHVwbGlrXFx4RTFsXFx4RTFzXCIsXG4gIHVudGl0bGVkOiBcIk5cXHhFOXZ0ZWxlblwiLFxuICBuYW1lOiBcIk5cXHhFOXZcIixcbiAgeW91ck5hbWU6IFwiTmV2ZWRcIixcbiAgbWFkZVdpdGhFeGNhbGlkcmF3OiBcIkV4Y2FsaWRyYXctdmFsIGtcXHhFOXN6XFx4RkNsdFwiLFxuICBncm91cDogXCJDc29wb3J0b3NcXHhFRHRcXHhFMXNcIixcbiAgdW5ncm91cDogXCJDc29wb3J0Ym9udFxceEUxc1wiLFxuICBjb2xsYWJvcmF0b3JzOiBcIktcXHhGNnpyZW1cXHUwMTcxa1xceEY2ZFxcdTAxNTFrXCIsXG4gIHNob3dHcmlkOiBcIlJcXHhFMWNzIG1lZ2plbGVuXFx4RUR0XFx4RTlzZVwiLFxuICBhZGRUb0xpYnJhcnk6IFwiSG96elxceEUxYWRcXHhFMXMgYSBrXFx4RjZueXZ0XFx4RTFyaG96XCIsXG4gIHJlbW92ZUZyb21MaWJyYXJ5OiBcIkVsdFxceEUxdlxceEYzbFxceEVEdFxceEUxcyBhIGtcXHhGNm55dnRcXHhFMXJiXFx4RjNsXCIsXG4gIGxpYnJhcnlMb2FkaW5nTWVzc2FnZTogXCJLXFx4RjZueXZ0XFx4RTFyIGJldFxceEY2bHRcXHhFOXNlXFx1MjAyNlwiLFxuICBsaWJyYXJpZXM6IFwiS1xceEY2bnl2dFxceEUxcmFrIGJcXHhGNm5nXFx4RTlzelxceEU5c2VcIixcbiAgbG9hZGluZ1NjZW5lOiBcIkplbGVuZXQgYmV0XFx4RjZsdFxceEU5c2VcXHUyMDI2XCIsXG4gIGFsaWduOiBcIklnYXpcXHhFRHRcXHhFMXNcIixcbiAgYWxpZ25Ub3A6IFwiRmVsXFx4RkNscmUgaWdhelxceEVEdFxceEUxc1wiLFxuICBhbGlnbkJvdHRvbTogXCJBbHVscmEgaWdhelxceEVEdFxceEUxc1wiLFxuICBhbGlnbkxlZnQ6IFwiQmFscmEgaWdhelxceEVEdFxceEUxc1wiLFxuICBhbGlnblJpZ2h0OiBcIkpvYmJyYSBpZ2F6XFx4RUR0XFx4RTFzXCIsXG4gIGNlbnRlclZlcnRpY2FsbHk6IFwiRlxceEZDZ2dcXHUwMTUxbGVnZXNlbiBrXFx4RjZ6XFx4RTlwcmUgaWdhelxceEVEdG90dFwiLFxuICBjZW50ZXJIb3Jpem9udGFsbHk6IFwiVlxceEVEenN6aW50ZXNlbiBrXFx4RjZ6XFx4RTlwcmUgaWdhelxceEVEdG90dFwiLFxuICBkaXN0cmlidXRlSG9yaXpvbnRhbGx5OiBcIlZcXHhFRHpzemludGVzIGVsb3N6dFxceEUxc1wiLFxuICBkaXN0cmlidXRlVmVydGljYWxseTogXCJGXFx4RkNnZ1xcdTAxNTFsZWdlcyBlbG9zenRcXHhFMXNcIixcbiAgZmxpcEhvcml6b250YWw6IFwiVlxceEVEenN6aW50ZXMgdFxceEZDa3JcXHhGNnpcXHhFOXNcIixcbiAgZmxpcFZlcnRpY2FsOiBcIkZcXHhGQ2dnXFx1MDE1MWxlZ2VzIHRcXHhGQ2tyXFx4RjZ6XFx4RTlzXCIsXG4gIHZpZXdNb2RlOiBcIk5cXHhFOXpldFwiLFxuICBzaGFyZTogXCJNZWdvc3p0XFx4RTFzXCIsXG4gIHNob3dTdHJva2U6IFwiS1xceEY2cnZvbmFsIHN6XFx4RURudlxceEUxbGFzenRcXHhGMyBtZWdqZWxlblxceEVEdFxceEU5c2VcIixcbiAgc2hvd0JhY2tncm91bmQ6IFwiSFxceEUxdHRcXHhFOXJzelxceEVEbi12XFx4RTFsYXN6dFxceEYzIG1lZ2plbGVuXFx4RUR0XFx4RTlzZVwiLFxuICB0b2dnbGVUaGVtZTogXCJUXFx4RTltYSB2XFx4RTFsdFxceEUxc2FcIixcbiAgcGVyc29uYWxMaWI6IFwiU3plbVxceEU5bHllcyBrXFx4RjZueXZ0XFx4RTFyXCIsXG4gIGV4Y2FsaWRyYXdMaWI6IFwiRXhjYWxpZHJhdyBrXFx4RjZueXZ0XFx4RTFyXCIsXG4gIGRlY3JlYXNlRm9udFNpemU6IFwiQmV0XFx1MDE3MW1cXHhFOXJldCBjc1xceEY2a2tlbnRcXHhFOXNlXCIsXG4gIGluY3JlYXNlRm9udFNpemU6IFwiQmV0XFx1MDE3MW1cXHhFOXJldCBuXFx4RjZ2ZWxcXHhFOXNlXCIsXG4gIHVuYmluZFRleHQ6IFwiU3pcXHhGNnZlZ2tcXHhGNnRcXHhFOXMgZmVsb2xkXFx4RTFzYVwiLFxuICBiaW5kVGV4dDogXCJcIixcbiAgY3JlYXRlQ29udGFpbmVyRnJvbVRleHQ6IFwiU3pcXHhGNnZlZyBiZWtlcmV0ZXpcXHhFOXNlXCIsXG4gIGxpbms6IHtcbiAgICBlZGl0OiBcIkhpdmF0a296XFx4RTFzIHN6ZXJrZXN6dFxceEU5c2VcIixcbiAgICBlZGl0RW1iZWQ6IFwiTGluayBzemVya2VzenRcXHhFOXNlIC8gYmVcXHhFMWd5YXpcXHhFMXNhXCIsXG4gICAgY3JlYXRlOiBcIkhpdmF0a296XFx4RTFzIGxcXHhFOXRyZWhvelxceEUxc2FcIixcbiAgICBjcmVhdGVFbWJlZDogXCJMaW5rIGxcXHhFOXRyZWhvelxceEUxc2EgLyBiZVxceEUxZ3lhelxceEUxc2FcIixcbiAgICBsYWJlbDogXCJIaXZhdGtvelxceEUxc1wiLFxuICAgIGxhYmVsRW1iZWQ6IFwiXCIsXG4gICAgZW1wdHk6IFwiXCJcbiAgfSxcbiAgbGluZUVkaXRvcjoge1xuICAgIGVkaXQ6IFwiXCIsXG4gICAgZXhpdDogXCJcIlxuICB9LFxuICBlbGVtZW50TG9jazoge1xuICAgIGxvY2s6IFwiUlxceEY2Z3pcXHhFRHRcXHhFOXNcIixcbiAgICB1bmxvY2s6IFwiUlxceEY2Z3pcXHhFRHRcXHhFOXMgZmVsb2xkXFx4RTFzYVwiLFxuICAgIGxvY2tBbGw6IFwiXFx4RDZzc3plcyByXFx4RjZnelxceEVEdFxceEU5c2VcIixcbiAgICB1bmxvY2tBbGw6IFwiXFx4RDZzc3plcyBmZWxvbGRcXHhFMXNhXCJcbiAgfSxcbiAgc3RhdHVzUHVibGlzaGVkOiBcIlwiLFxuICBzaWRlYmFyTG9jazogXCJcIixcbiAgc2VsZWN0QWxsRWxlbWVudHNJbkZyYW1lOiBcIlwiLFxuICByZW1vdmVBbGxFbGVtZW50c0Zyb21GcmFtZTogXCJcIixcbiAgZXllRHJvcHBlcjogXCJcIixcbiAgdGV4dFRvRGlhZ3JhbTogXCJcIixcbiAgcHJvbXB0OiBcIlwiXG59O1xudmFyIGxpYnJhcnkgPSB7XG4gIG5vSXRlbXM6IFwiXCIsXG4gIGhpbnRfZW1wdHlMaWJyYXJ5OiBcIlwiLFxuICBoaW50X2VtcHR5UHJpdmF0ZUxpYnJhcnk6IFwiXCJcbn07XG52YXIgYnV0dG9ucyA9IHtcbiAgY2xlYXJSZXNldDogXCJWXFx4RTFzem9uIHRcXHhGNnJsXFx4RTlzZVwiLFxuICBleHBvcnRKU09OOiBcIkV4cG9ydFxceEUxbFxceEUxcyBmXFx4RTFqbGJhXCIsXG4gIGV4cG9ydEltYWdlOiBcIktcXHhFOXAgZXhwb3J0XFx4RTFsXFx4RTFzYS4uLlwiLFxuICBleHBvcnQ6IFwiTWVudFxceEU5cyBtXFx4RTFza1xceEU5bnQuLi5cIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIlZcXHhFMWdcXHhGM2xhcHJhIG1cXHhFMXNvbFxceEUxc1wiLFxuICBzYXZlOiBcIk1lbnRcXHhFOXMgYXogYWt0dVxceEUxbGlzIGZcXHhFMWpsYmFcIixcbiAgc2F2ZUFzOiBcIk1lbnRcXHhFOXMgbVxceEUxc2tcXHhFOW50XCIsXG4gIGxvYWQ6IFwiTWVnbnlpdFxceEUxc1wiLFxuICBnZXRTaGFyZWFibGVMaW5rOiBcIk1lZ29zenRoYXRcXHhGMyBsaW5rIGxcXHhFOXRyZWhvelxceEUxc2FcIixcbiAgY2xvc2U6IFwiQmV6XFx4RTFyXFx4RTFzXCIsXG4gIHNlbGVjdExhbmd1YWdlOiBcIk55ZWx2IGtpdlxceEUxbGFzenRcXHhFMXNhXCIsXG4gIHNjcm9sbEJhY2tUb0NvbnRlbnQ6IFwiVmlzc3phZ1xceEY2cmdldFxceEU5cyBhIHRhcnRhbG9taG96XCIsXG4gIHpvb21JbjogXCJOYWd5XFx4RUR0XFx4RTFzXCIsXG4gIHpvb21PdXQ6IFwiS2ljc2lueVxceEVEdFxceEU5c1wiLFxuICByZXNldFpvb206IFwiTmFneVxceEVEdFxceEUxcyBhbGFwaGVseXpldGJlXCIsXG4gIG1lbnU6IFwiTWVuXFx4RkNcIixcbiAgZG9uZTogXCJLXFx4RTlzelwiLFxuICBlZGl0OiBcIlN6ZXJrZXN6dFxceEU5c1wiLFxuICB1bmRvOiBcIlZpc3N6YVwiLFxuICByZWRvOiBcIlxceERBanJhXCIsXG4gIHJlc2V0TGlicmFyeTogXCJLXFx4RjZueXZ0XFx4RTFyIGFsYXBoZWx5emV0YmUgXFx4RTFsbFxceEVEdFxceEUxc2FcIixcbiAgY3JlYXRlTmV3Um9vbTogXCJcXHhEQWogc3pvYmEgbFxceEU5dHJlaG96XFx4RTFzYVwiLFxuICBmdWxsU2NyZWVuOiBcIlRlbGplcyBrXFx4RTlwZXJueVxcdTAxNTFcIixcbiAgZGFya01vZGU6IFwiU1xceEY2dFxceEU5dCBtXFx4RjNkXCIsXG4gIGxpZ2h0TW9kZTogXCJWaWxcXHhFMWdvcyBtXFx4RjNkXCIsXG4gIHplbk1vZGU6IFwiTGV0aXN6dHVsdCBtXFx4RjNkXCIsXG4gIG9iamVjdHNTbmFwTW9kZTogXCJcIixcbiAgZXhpdFplbk1vZGU6IFwiS2lsXFx4RTlwXFx4RTlzIGEgbGV0aXN6dHVsdCBtXFx4RjNkYlxceEYzbFwiLFxuICBjYW5jZWw6IFwiTVxceEU5Z3NlbVwiLFxuICBjbGVhcjogXCJLaVxcdTAxNzFyXFx4RUR0XFx4RTlzXCIsXG4gIHJlbW92ZTogXCJFbHRcXHhFMXZvbFxceEVEdFxceEUxc1wiLFxuICBlbWJlZDogXCJcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiS1xceEY2enpcXHhFOXRcXHhFOXRlbFwiLFxuICBzdWJtaXQ6IFwiRWxrXFx4RkNsZFxceEU5c1wiLFxuICBjb25maXJtOiBcIk1lZ2VyXFx1MDE1MXNcXHhFRHRcXHhFOXNcIixcbiAgZW1iZWRkYWJsZUludGVyYWN0aW9uQnV0dG9uOiBcIlwiXG59O1xudmFyIGFsZXJ0cyA9IHtcbiAgY2xlYXJSZXNldDogXCJFeiBhIG1cXHUwMTcxdmVsZXQgdFxceEY2cmxpIGEgdlxceEUxc3pvbnQuIEJpenRvcyBiZW5uZT9cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rOiBcIk5lbSBzaWtlclxceEZDbHQgbWVnb3N6dGhhdFxceEYzIGxpbmtldCBsXFx4RTl0cmVob3puaS5cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rVG9vQmlnOiBcIk5lbSBzaWtlclxceEZDbHQgbWVnb3N6dGhhdFxceEYzIGxpbmtldCBsXFx4RTF0cmVob3puaTogdFxceEZBbCBuYWd5IGEgamVsZW5ldFwiLFxuICBjb3VsZE5vdExvYWRJbnZhbGlkRmlsZTogXCJOZW0gc2lrZXJcXHhGQ2x0IGJldFxceEY2bHRlbmkgYSBoZWx5dGVsZW4gZlxceEUxamx0XCIsXG4gIGltcG9ydEJhY2tlbmRGYWlsZWQ6IFwiTmVtIHNpa2VyXFx4RkNsdCBiZXRcXHhGNmx0ZW5pIGEgc3plcnZlcnJcXHUwMTUxbC5cIixcbiAgY2Fubm90RXhwb3J0RW1wdHlDYW52YXM6IFwiXFx4RENyZXMgdlxceEUxc3pvbnQgbmVtIGxlaGV0IGV4cG9ydFxceEUxbG5pLlwiLFxuICBjb3VsZE5vdENvcHlUb0NsaXBib2FyZDogXCJcIixcbiAgZGVjcnlwdEZhaWxlZDogXCJOZW0gc2lrZXJcXHhGQ2x0IHZpc3N6YWZlanRlbmkgYSB0aXRrb3NcXHhFRHRvdHQgYWRhdG90LlwiLFxuICB1cGxvYWRlZFNlY3VybHk6IFwiQSBmZWx0XFx4RjZsdFxceEU5c3QgdlxceEU5Z3BvbnRvayBrXFx4RjZ6XFx4RjZ0dGkgdGl0a29zXFx4RUR0XFx4RTFzc2FsIGJpenRvc1xceEVEdG90dHVrLCBhbWkgYXp0IGplbGVudGksIGhvZ3kgZWd5IGhhcm1hZGlrIGZcXHhFOWwgbmVtIHR1ZGphIG1lZ25cXHhFOXpuaSBhIHRhcnRhbG1cXHhFMXQsIGJlbGVcXHhFOXJ0dmUgYXogRXhjYWxpZHJhdyBzemVydmVyZWl0IGlzLlwiLFxuICBsb2FkU2NlbmVPdmVycmlkZVByb21wdDogXCJBIGJldFxceEY2bHRcXHhGNnR0IGtcXHhGQ2xzXFx1MDE1MSByYWp6IGZlbFxceEZDbCBmb2dqYSBcXHhFRHJuaWEgbWVnbFxceEU5dlxcdTAxNTF0LiBTemVyZXRuXFx4RTlkIGZvbHl0YXRuaT9cIixcbiAgY29sbGFiU3RvcE92ZXJyaWRlUHJvbXB0OiBcIkEgbXVua2FtZW5ldCBsZVxceEUxbGxcXHhFRHRcXHhFMXNhIGZlbFxceEZDbCBmb2dqYSBcXHhFRHJuaSBheiBlbFxcdTAxNTF6XFx1MDE1MWxlZyBoZWx5YmVuIHRcXHhFMXJvbHQgcmFqem90LiBCaXp0b3NhbiBlenQgYWthcm9kP1xcbihIYSBtZWcgYWthcm9kIHRhcnRhbmkgYSBoZWx5YmVuIHRcXHhFMXJvbHQgcmFqem90LCBlZ3lzemVyXFx1MDE3MWVuIGNzYWsgelxceEUxcmQgYmUgYSBiXFx4RjZuZ1xceEU5c3pcXHUwMTUxIGZcXHhGQ2xldClcIixcbiAgZXJyb3JBZGRpbmdUb0xpYnJhcnk6IFwiQSB0XFx4RTl0ZWwgbmVtIGFkZGhhdFxceEYzIGhvenpcXHhFMSBhIGtcXHhGNm55dnRcXHhFMXJob3pcIixcbiAgZXJyb3JSZW1vdmluZ0Zyb21MaWJyYXJ5OiBcIkEgdFxceEU5dGVsIG5lbSB0XFx4RTF2b2xcXHhFRHRoYXRcXHhGMyBlbCBhIGtcXHhGNm55dnRcXHhFMXJiXFx4RjNsXCIsXG4gIGNvbmZpcm1BZGRMaWJyYXJ5OiBcIkV6IGEgbVxcdTAxNzF2ZWxldCB7e251bVNoYXBlc319IGZvcm1cXHhFMXQgZm9nIGhvenpcXHhFMWFkbmkgYSBrXFx4RjZueXZ0XFx4RTFyYWRob3ouIEJpenRvcyB2YWd5IGJlbm5lP1wiLFxuICBpbWFnZURvZXNOb3RDb250YWluU2NlbmU6IFwiXFx4REFneSB0XFx1MDE3MW5paywgaG9neSBleiBhIGtcXHhFOXAgbmVtIHRhcnRhbG1heiBqZWxlbmV0YWRhdG9rYXQuIEVuZ2VkXFx4RTlseWV6dGVkIGEgamVsZW5ldGJlXFx4RTFneWF6XFx4RTFzdCBheiBleHBvcnRcXHhFMWxcXHhFMXMgc29yXFx4RTFuP1wiLFxuICBjYW5ub3RSZXN0b3JlRnJvbUltYWdlOiBcIkEgamVsZW5ldCB2aXNzemFcXHhFMWxsXFx4RUR0XFx4RTFzYSBuZW0gc2lrZXJcXHhGQ2x0IGViYlxcdTAxNTFsIGEga1xceEU5cCBmXFx4RTFqbGJcXHhGM2xcIixcbiAgaW52YWxpZFNjZW5lVXJsOiBcIk5lbSBzaWtlclxceEZDbHQgaW1wb3J0XFx4RTFsbmkgYSBqZWxlbmV0ZXQgYSBtZWdhZG90dCBVUkwtclxcdTAxNTFsLiBSb3NzeiBmb3JtXFx4RTF0dW1cXHhGQSwgdmFneSBuZW0gdGFydGFsbWF6IFxceEU5cnZcXHhFOW55ZXMgRXhjYWxpZHJhdyBKU09OLWFkYXRva2F0LlwiLFxuICByZXNldExpYnJhcnk6IFwiRXp6ZWwgdFxceEY2cmxcXHhGNmQgYSBrXFx4RjZueXZ0XFx4RTFyXFx4RTF0LiBiaXp0b3MgdmFneSBlYmJlbj9cIixcbiAgcmVtb3ZlSXRlbXNGcm9tc0xpYnJhcnk6IFwie3tjb3VudH19IGVsZW1ldCB0XFx4RjZyXFx4RjZsc3ogYSBrXFx4RjZueXZ0XFx4RTFyYlxceEYzbD9cIixcbiAgaW52YWxpZEVuY3J5cHRpb25LZXk6IFwiQSB0aXRrb3NcXHhFRHRcXHhFMXNpIGt1bGNzbmFrIDIyIGthcmFrdGVyYlxcdTAxNTFsIGtlbGwgXFx4RTFsbG5pYS4gQXogXFx4RTlsXFx1MDE1MSBlZ3lcXHhGQ3R0bVxcdTAxNzFrXFx4RjZkXFx4RTlzIGxlIHZhbiB0aWx0dmEuXCIsXG4gIGNvbGxhYk9mZmxpbmVXYXJuaW5nOiBcIlwiXG59O1xudmFyIGVycm9ycyA9IHtcbiAgdW5zdXBwb3J0ZWRGaWxlVHlwZTogXCJOZW0gdFxceEUxbW9nYXRvdHQgZlxceEUxamx0XFx4RURwdXMuXCIsXG4gIGltYWdlSW5zZXJ0RXJyb3I6IFwiTmVtIHNpa2VyXFx4RkNsdCBiZXN6XFx4RkFybmkgYSBrXFx4RTlwZXQuIFByXFx4RjNiXFx4RTFsZCBcXHhGQWpyYSBrXFx4RTlzXFx1MDE1MWJiLi4uXCIsXG4gIGZpbGVUb29CaWc6IFwiQSBmXFx4RTFqbCB0XFx4RkFsIG5hZ3kuIEEgbWVnZW5nZWRldHQgbWF4aW1cXHhFMWxpcyBtXFx4RTlyZXQge3ttYXhTaXplfX0uXCIsXG4gIHN2Z0ltYWdlSW5zZXJ0RXJyb3I6IFwiTmVtIHNpa2VyXFx4RkNsdCBiZXN6XFx4RkFybmkgYXogU1ZHLWtcXHhFOXBldC4gQXogU1ZHIHN6aW50YWt0aWthIFxceEU5cnZcXHhFOW55dGVsZW5uZWsgdFxcdTAxNzFuaWsuXCIsXG4gIGZhaWxlZFRvRmV0Y2hJbWFnZTogXCJcIixcbiAgaW52YWxpZFNWR1N0cmluZzogXCJcXHhDOXJ2XFx4RTlueXRlbGVuIFNWRy5cIixcbiAgY2Fubm90UmVzb2x2ZUNvbGxhYlNlcnZlcjogXCJcIixcbiAgaW1wb3J0TGlicmFyeUVycm9yOiBcIlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkOiBcIlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkX3NpemVFeGNlZWRlZDogXCJcIixcbiAgaW1hZ2VUb29sTm90U3VwcG9ydGVkOiBcIlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJcIixcbiAgICBsaW5lMjogXCJcIixcbiAgICBsaW5lMzogXCJcIixcbiAgICBsaW5lNDogXCJcIlxuICB9LFxuICBsaWJyYXJ5RWxlbWVudFR5cGVFcnJvcjoge1xuICAgIGVtYmVkZGFibGU6IFwiXCIsXG4gICAgaWZyYW1lOiBcIlwiLFxuICAgIGltYWdlOiBcIlwiXG4gIH0sXG4gIGFzeW5jUGFzdGVGYWlsZWRPblJlYWQ6IFwiXCIsXG4gIGFzeW5jUGFzdGVGYWlsZWRPblBhcnNlOiBcIlwiLFxuICBjb3B5VG9TeXN0ZW1DbGlwYm9hcmRGYWlsZWQ6IFwiXCJcbn07XG52YXIgdG9vbEJhciA9IHtcbiAgc2VsZWN0aW9uOiBcIktpamVsXFx4RjZsXFx4RTlzXCIsXG4gIGltYWdlOiBcIktcXHhFOXAgYmVzelxceEZBclxceEUxc2FcIixcbiAgcmVjdGFuZ2xlOiBcIlRcXHhFOWdsYWxhcFwiLFxuICBkaWFtb25kOiBcIlJvbWJ1c3pcIixcbiAgZWxsaXBzZTogXCJFbGxpcHN6aXNcIixcbiAgYXJyb3c6IFwiTnlcXHhFRGxcIixcbiAgbGluZTogXCJWb25hbFwiLFxuICBmcmVlZHJhdzogXCJSYWp6b2xcXHhFMXNcIixcbiAgdGV4dDogXCJTelxceEY2dmVnXCIsXG4gIGxpYnJhcnk6IFwiS1xceEY2bnl2dFxceEUxclwiLFxuICBsb2NrOiBcIlJhanpvbFxceEUxcyB1dFxceEUxbiBheiBha3RcXHhFRHYgZXN6a1xceEY2enQgdGFydHNhIGtpamVsXFx4RjZsdmVcIixcbiAgcGVuTW9kZTogXCJcIixcbiAgbGluazogXCJIaXZhdGtvelxceEUxcyBob3p6XFx4RTFhZFxceEUxc2EvZnJpc3NcXHhFRHRcXHhFOXNlIGEga2l2XFx4RTFsYXN6dG90dCBhbGFremF0aG96XCIsXG4gIGVyYXNlcjogXCJSYWRcXHhFRHJcIixcbiAgZnJhbWU6IFwiXCIsXG4gIG1hZ2ljZnJhbWU6IFwiXCIsXG4gIGVtYmVkZGFibGU6IFwiV2VibGFwIGJlXFx4RTFneWF6XFx4RTFzYVwiLFxuICBsYXNlcjogXCJMXFx4RTl6ZXJtdXRhdFxceEYzXCIsXG4gIGhhbmQ6IFwiXCIsXG4gIGV4dHJhVG9vbHM6IFwiVG92XFx4RTFiYmkgZXN6a1xceEY2elxceEY2a1wiLFxuICBtZXJtYWlkVG9FeGNhbGlkcmF3OiBcIlwiLFxuICBtYWdpY1NldHRpbmdzOiBcIlwiXG59O1xudmFyIGhlYWRpbmdzID0ge1xuICBjYW52YXNBY3Rpb25zOiBcIlZcXHhFMXN6b24gbVxcdTAxNzF2ZWxldGVrXCIsXG4gIHNlbGVjdGVkU2hhcGVBY3Rpb25zOiBcIktpamVsXFx4RjZsdCBmb3JtYSBtXFx1MDE3MXZlbGV0ZWlcIixcbiAgc2hhcGVzOiBcIkFsYWt6YXRva1wiXG59O1xudmFyIGhpbnRzID0ge1xuICBjYW52YXNQYW5uaW5nOiBcIlwiLFxuICBsaW5lYXJFbGVtZW50OiBcIkthdHRpbnRcXHhFMXNzYWwgZ1xceEY2cmJlLCBheiBlZ2VyIGhcXHhGQXpcXHhFMXNcXHhFMXZhbCBwZWRpZyBlZ3llbmVzIG55aWxhdCByYWp6b2xoYXRzelwiLFxuICBmcmVlRHJhdzogXCJLYXR0aW50cyBcXHhFOXMgaFxceEZBemQsIG1hamQgZW5nZWRkIGVsLCBhbWlrb3IgdlxceEU5Z2V6dFxceEU5bFwiLFxuICB0ZXh0OiBcIlRpcHA6IEEga2lqZWxcXHhGNmxcXHhFOXMgZXN6a1xceEY2enplbCBhIGR1cGxhIGthdHRpbnRcXHhFMXMgXFx4RkFqIHN6XFx4RjZ2ZWdldCBob3ogbFxceEU5dHJlXCIsXG4gIGVtYmVkZGFibGU6IFwiXCIsXG4gIHRleHRfc2VsZWN0ZWQ6IFwiS2F0dGludHMgZHVwbFxceEUxbiwgdmFneSBueW9taiBlbnRlcnQgYSBzelxceEY2dmVnIHN6ZXJrZXN6dFxceEU5c1xceEU5aGV6XCIsXG4gIHRleHRfZWRpdGluZzogXCJOeW9tamQgbWVnIGF6IEVzY2FwZSB2YWd5IGEgQ3RybC9DbWQrRU5URVIgYmlsbGVudHlcXHUwMTcxa29tYmluXFx4RTFjaVxceEYzdCBhIHN6ZXJrZXN6dFxceEU5cyBiZWZlamV6XFx4RTlzXFx4RTloZXpcIixcbiAgbGluZWFyRWxlbWVudE11bHRpOiBcIkthdHRpbnRzIGEga1xceEY2dmV0a2V6XFx1MDE1MSBcXHhFRHYgcG96XFx4RURjaVxceEYzalxceEUxcmEsIHZhZ3kgZmVqZXpkIGJlIGEgbnlpbGF0IGF6IEVzY2FwZSB2YWd5IEVudGVyIG1lZ255b21cXHhFMXNcXHhFMXZhbFwiLFxuICBsb2NrQW5nbGU6IFwiQSBTSElGVCBiaWxsZW50eVxcdTAxNzEgbGVueW9tdmEgdGFydFxceEUxc1xceEUxdmFsIGtvcmxcXHhFMXRvemhhdGphIGZvcmdhdFxceEUxcyBzelxceEY2Z1xceEU5dFwiLFxuICByZXNpemU6IFwiQSBTSElGVCBiaWxsZW50eVxcdTAxNzEgbGVueW9tdmEgdGFydFxceEUxc1xceEUxdmFsIGF6IFxceEUxdG1cXHhFOXJldGV6XFx4RTlzIG1lZ3RhcnRqYSBheiBhclxceEUxbnlva2F0LFxcbmF6IEFMVCBsZW55b212YSB0YXJ0XFx4RTFzXFx4RTF2YWwgcGVkaWcgYSBrXFx4RjZ6XFx4RTlwcG9udCBlZ3kgaGVseWJlbiBtYXJhZFwiLFxuICByZXNpemVJbWFnZTogXCJBIFNISUZUIGJpbGxlbnR5XFx1MDE3MSBsZW55b212YSB0YXJ0XFx4RTFzXFx4RTF2YWwgc3phYmFkb24gXFx4RTF0bVxceEU5cmV0ZXpoZXRlZCxcXG50YXJ0c2QgbGVueW9tdmEgYXogQUxUIGJpbGxlbnR5XFx1MDE3MXQgYSBrXFx4RjZ6XFx4RTlwclxcdTAxNTFsIHZhbFxceEYzIFxceEUxdG1cXHhFOXJldGV6XFx4RTlzaGV6XCIsXG4gIHJvdGF0ZTogXCJBIFNISUZUIGJpbGxlbnR5XFx1MDE3MSBsZW55b212YSB0YXJ0XFx4RTFzXFx4RTF2YWwga29ybFxceEUxdG96aGF0amEgYSBzelxceEY2Z2VrIGlsbGVzenRcXHhFOXNcXHhFOXRcIixcbiAgbGluZUVkaXRvcl9pbmZvOiBcIlwiLFxuICBsaW5lRWRpdG9yX3BvaW50U2VsZWN0ZWQ6IFwiTnlvbWQgbWVnIGEgVFxceEY2cmxcXHhFOXMgZ29tYm90IGEgcG9udChvaykgZWx0XFx4RTF2b2xcXHhFRHRcXHhFMXNcXHhFMWhveixcXG5BIEN0cmwvQ21kK0QgYSB0XFx4RjZiYnN6XFx4RjZyXFx4RjZ6XFx4RTlzaGV6LCB2YWd5IGhcXHhGQXpcXHhFMXNzYWwgbW96Z2F0aGF0amFcIixcbiAgbGluZUVkaXRvcl9ub3RoaW5nU2VsZWN0ZWQ6IFwiVlxceEUxbGFzemQga2kgYSBzemVya2VzenRlbmkga1xceEVEdlxceEUxbnQgcG9udG90ICh0XFx4RjZiYiBraWplbFxceEY2bFxceEU5c1xceEU5aGV6IHRhcnRzZCBsZW55b212YSBhIFNISUZUIGJpbGxlbnR5XFx1MDE3MXQpLFxcbnZhZ3kgQWx0LCBcXHhFOXMga2F0dGludHNvbiBheiBcXHhGQWogcG9udG9rIGhvenpcXHhFMWFkXFx4RTFzXFx4RTFob3pcIixcbiAgcGxhY2VJbWFnZTogXCJLYXR0aW50cyBhIGtcXHhFOXAgZWxoZWx5ZXpcXHhFOXNcXHhFOWhleiwgdmFneSBrYXR0aW50cyBcXHhFOXMgbVxceEU5cmV0ZXpkIG1hbnVcXHhFMWxpc2FuXCIsXG4gIHB1Ymxpc2hMaWJyYXJ5OiBcIlRlZGQga1xceEY2enpcXHhFOSBzYWpcXHhFMXQga1xceEY2bnl2dFxceEUxcmFkYXRcIixcbiAgYmluZFRleHRUb0VsZW1lbnQ6IFwiTnlvbWQgbWVnIGF6IEVudGVydCBzelxceEY2dmVnIGhvenpcXHhFMWFkXFx4RTFzaG96XCIsXG4gIGRlZXBCb3hTZWxlY3Q6IFwiVGFydHNkIGxlbnlvbXZhIGEgQ3RybC9DbWQgYmlsbGVudHlcXHUwMTcxdCBhIG1cXHhFOWx5IGtpamVsXFx4RjZsXFx4RTlzaGV6IFxceEU5cyBhIGhcXHhGQXpcXHhFMXMgbWVnYWthZFxceEUxbHlvelxceEUxc1xceEUxaG96XCIsXG4gIGVyYXNlclJldmVydDogXCJcIixcbiAgZmlyZWZveF9jbGlwYm9hcmRfd3JpdGU6IFwiXCIsXG4gIGRpc2FibGVTbmFwcGluZzogXCJcIlxufTtcbnZhciBjYW52YXNFcnJvciA9IHtcbiAgY2Fubm90U2hvd1ByZXZpZXc6IFwiRWxcXHUwMTUxblxceEU5emV0IG5lbSBqZWxlblxceEVEdGhldFxcdTAxNTEgbWVnXCIsXG4gIGNhbnZhc1Rvb0JpZzogXCJBIHZcXHhFMXN6b24gdGFsXFx4RTFuIHRcXHhGQWwgbmFneS5cIixcbiAgY2FudmFzVG9vQmlnVGlwOiBcIlRpcHA6IHByXFx4RjNiXFx4RTFsZCBtZWcgYSBsZWd0XFx4RTF2b2xhYmJpIGVsZW1la2V0IGtcXHhGNnplbGViYiBob3puaSBlZ3kgbVxceEUxc2hvei5cIlxufTtcbnZhciBlcnJvclNwbGFzaCA9IHtcbiAgaGVhZGluZ01haW46IFwiSGliYSB0XFx4RjZydFxceEU5bnQuIFByXFx4RjNiXFx4RTFsZCA8YnV0dG9uPlxceEZBanJhdFxceEY2bHRlbmkgYXogb2xkYWx0LjwvYnV0dG9uPlwiLFxuICBjbGVhckNhbnZhc01lc3NhZ2U6IFwiSGEgYXogXFx4RkFqcmF0XFx4RjZsdFxceEU5cyBuZW0gbVxcdTAxNzFrXFx4RjZkaWssIHByXFx4RjNiXFx4RTFsZCA8YnV0dG9uPmxldFxceEY2clxceEY2bG5pIGEgdlxceEUxc3pvbnQuPC9idXR0b24+XCIsXG4gIGNsZWFyQ2FudmFzQ2F2ZWF0OiBcIiBFenplbCBheiBlZGRpZ2kgbXVua2EgZWx2ZXN6aWsgXCIsXG4gIHRyYWNrZWRUb1NlbnRyeTogXCJBIGhpYmFrXFx4RjNkIGF6b25vc1xceEVEdFxceEYzdmFsIHt7ZXZlbnRJZH19IG55b21vbiB2YW4ga1xceEY2dmV0dmUgYSByZW5kc3plclxceEZDbmtiZW4uXCIsXG4gIG9wZW5Jc3N1ZU1lc3NhZ2U6IFwiVmlneVxceEUxenR1bmsgYXJyYSwgaG9neSBhIGplbGVudGhleiB0YXJ0b3pcXHhGMyBpbmZvcm1cXHhFMWNpXFx4RjMgbmUgamVsZW5qZW4gbWVnIGEgaGliYVxceEZDemVuZXRiZW4uIEhhIGEgamVsZW5ldGVkIG5lbSBiaXphbG1hcywga1xceEU5cmpcXHhGQ2sgYWRkIGhvenpcXHhFMSBhIDxidXR0b24+aGliYWtcXHhGNnZldFxcdTAxNTEgcmVuZHN6ZXJcXHhGQ25raFxceEY2ei48L2J1dHRvbj4gS1xceEU5cmpcXHhGQ2ssIG1cXHhFMXNvbGphIGJlIGF6IGFsXFx4RTFiYmkgaW5mb3JtXFx4RTFjaVxceEYza2F0IGEgR2l0SHViIHByb2JsXFx4RTltXFx4RTFiYS5cIixcbiAgc2NlbmVDb250ZW50OiBcIkplbGVuZXQgdGFydGFsbWE6XCJcbn07XG52YXIgcm9vbURpYWxvZyA9IHtcbiAgZGVzY19pbnRybzogXCJNZWdoXFx4RUR2aGF0IGVtYmVyZWtldCBhIGplbGVubGVnaSBqZWxlbmV0YmUsIGhvZ3kgZWd5XFx4RkN0dG1cXHUwMTcxa1xceEY2ZGplbmVrIFxceEY2bm5lbC5cIixcbiAgZGVzY19wcml2YWN5OiBcIk5lIGFnZ1xceEYzZGosIGEgbXVua2FtZW5ldCB2XFx4RTlncG9udG9rIGtcXHhGNnpcXHhGNnR0aSB0aXRrb3NcXHhFRHRcXHhFMXN0IGhhc3puXFx4RTFsLCB0ZWhcXHhFMXQgYlxceEUxcm1pdCByYWp6b2xzeiwgcHJpdlxceEUxdCBtYXJhZC4gTVxceEU5ZyBhIHN6ZXJ2ZXJcXHhGQ25rclxcdTAxNTFsIHNlIGxlaGV0IGJlbGVuXFx4RTl6bmkuXCIsXG4gIGJ1dHRvbl9zdGFydFNlc3Npb246IFwiTXVua2FtZW5ldCBpbmRcXHhFRHRcXHhFMXNhXCIsXG4gIGJ1dHRvbl9zdG9wU2Vzc2lvbjogXCJNdW5rYW1lbmV0IGxlXFx4RTFsbFxceEVEdFxceEUxc2FcIixcbiAgZGVzY19pblByb2dyZXNzSW50cm86IFwiQXogXFx4RTlsXFx1MDE1MSBlZ3lcXHhGQ3R0bVxcdTAxNzFrXFx4RjZkXFx4RTlzaSBtdW5rYW1lbmV0IGZvbHlhbWF0YmFuIHZhbi5cIixcbiAgZGVzY19zaGFyZUxpbms6IFwiT3NzemEgbWVnIGV6dCBhIGxpbmtldCBiXFx4RTFya2l2ZWwsIGFraXZlbCBlZ3lcXHhGQ3R0IHN6ZXJldG5lIG1cXHUwMTcxa1xceEY2ZG5pOlwiLFxuICBkZXNjX2V4aXRTZXNzaW9uOiBcIkF6IG11bmthbWVuZXQgbGVcXHhFMWxsXFx4RUR0XFx4RTFzYSBraWxcXHhFOXB0ZXRpIFxceEY2bnQgYSBzem9iXFx4RTFiXFx4RjNsLCBkZSBmb2x5dGF0aGF0amEgYSBtdW5rXFx4RTF0IGEgc2FqXFx4RTF0IGdcXHhFOXBcXHhFOW4uIFZlZ3llIGZpZ3llbGVtYmUsIGhvZ3kgZXogbmVtIFxceEU5cmludGkgbVxceEUxcyBlbWJlcmVrIG11bmtcXHhFMWpcXHhFMXQgXFx4RTlzIFxcdTAxNTFrIHRvdlxceEUxYmJyYSBpcyBlZ3lcXHhGQ3R0bVxcdTAxNzFrXFx4RjZkaGV0bmVrIGEgc2FqXFx4RTF0IHZcXHhFMWx0b3phdHVrb24uXCIsXG4gIHNoYXJlVGl0bGU6IFwiQ3NhdGxha296XFx4RTFzIGVneSBcXHhFOWxcXHUwMTUxIGVneVxceEZDdHRtXFx1MDE3MWtcXHhGNmRcXHhFOXNpIG11bmthbWVuZXRoZXogYXogRXhjYWxpZHJhdy1iYW5cIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiSGliYVwiXG59O1xudmFyIGV4cG9ydERpYWxvZyA9IHtcbiAgZGlza190aXRsZTogXCJNZW50XFx4RTlzIGxlbWV6cmVcIixcbiAgZGlza19kZXRhaWxzOiBcIkV4cG9ydFxceEUxbGphIGEgamVsZW5ldGFkYXRva2F0IGVneSBmXFx4RTFqbGJhLCBhbWVseWJcXHUwMTUxbCBrXFx4RTlzXFx1MDE1MWJiIGltcG9ydFxceEUxbGhhdGphLlwiLFxuICBkaXNrX2J1dHRvbjogXCJNZW50XFx4RTlzIGZcXHhFMWpsYmFcIixcbiAgbGlua190aXRsZTogXCJNZWdvc3p0aGF0XFx4RjMgaGl2YXRrb3pcXHhFMXNcIixcbiAgbGlua19kZXRhaWxzOiBcIkV4cG9ydFxceEUxbFxceEUxcyBjc2FrIG9sdmFzaGF0XFx4RjMgaGl2YXRrb3pcXHhFMXNrXFx4RTludC5cIixcbiAgbGlua19idXR0b246IFwiRXhwb3J0XFx4RTFsXFx4RTFzIGhpdmF0a296XFx4RTFzYmFcIixcbiAgZXhjYWxpZHJhd3BsdXNfZGVzY3JpcHRpb246IFwiTWVudHNlIGVsIGEgamVsZW5ldGV0IGF6IEV4Y2FsaWRyYXcrIG11bmthdGVyXFx4RkNsZXRcXHhFOXJlLlwiLFxuICBleGNhbGlkcmF3cGx1c19idXR0b246IFwiRXhwb3J0XFx4RTFsXFx4RTFzXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2V4cG9ydEVycm9yOiBcIkplbGVubGVnIG5lbSBsZWhldCBleHBvcnRcXHhFMWxuaSBheiBFeGNhbGlkcmF3Ky1iYS4uLlwiXG59O1xudmFyIGhlbHBEaWFsb2cgPSB7XG4gIGJsb2c6IFwiT2x2YXNkIGEgYmxvZ3Vua2F0XCIsXG4gIGNsaWNrOiBcImthdHRpbnRcXHhFMXNcIixcbiAgZGVlcFNlbGVjdDogXCJNXFx4RTlseSBraWplbFxceEY2bFxceEU5c1wiLFxuICBkZWVwQm94U2VsZWN0OiBcIk1cXHhFOWx5IGtpamVsXFx4RjZsXFx4RTlzIGEgZG9ib3pvbiBiZWxcXHhGQ2wsIFxceEU5cyBhIGhcXHhGQXpcXHhFMXMgbWVnYWthZFxceEUxbHlvelxceEUxc2FcIixcbiAgY3VydmVkQXJyb3c6IFwiXFx4Q0R2ZWx0IG55XFx4RURsXCIsXG4gIGN1cnZlZExpbmU6IFwiXFx4Q0R2ZWx0IHZvbmFsXCIsXG4gIGRvY3VtZW50YXRpb246IFwiRG9rdW1lbnRcXHhFMWNpXFx4RjNcIixcbiAgZG91YmxlQ2xpY2s6IFwiZHVwbGEga2F0dGludFxceEUxc1wiLFxuICBkcmFnOiBcInZvbnN6b2xcXHhFMXNcIixcbiAgZWRpdG9yOiBcIlN6ZXJrZXN6dFxcdTAxNTFcIixcbiAgZWRpdExpbmVBcnJvd1BvaW50czogXCJcIixcbiAgZWRpdFRleHQ6IFwiXCIsXG4gIGdpdGh1YjogXCJIaWJcXHhFMXQgdGFsXFx4RTFsdFxceEUxbD8gS1xceEZDbGQgYmVcIixcbiAgaG93dG86IFwiS1xceEY2dmVzZCBheiBcXHhGQXRtdXRhdFxceEYzaW5rYXRcIixcbiAgb3I6IFwidmFneVwiLFxuICBwcmV2ZW50QmluZGluZzogXCJBIG55XFx4RURsIG5lIHJhZ2Fkam9uXCIsXG4gIHRvb2xzOiBcIlwiLFxuICBzaG9ydGN1dHM6IFwiR3lvcnNiaWxsZW50eVxcdTAxNzFrXCIsXG4gIHRleHRGaW5pc2g6IFwiU3plcmtlc3p0XFx4RTlzIGJlZmVqZXpcXHhFOXNlIChzelxceEY2dmVnKVwiLFxuICB0ZXh0TmV3TGluZTogXCJcXHhEQWogc29yIGhvenpcXHhFMWFkXFx4RTFzYSAoc3pcXHhGNnZlZylcIixcbiAgdGl0bGU6IFwiU2VnXFx4RUR0c1xceEU5Z1wiLFxuICB2aWV3OiBcIk5cXHhFOXpldFwiLFxuICB6b29tVG9GaXQ6IFwiQXogXFx4RjZzc3plcyBlbGVtIGxcXHhFMXRcXHhGM3RcXHhFOXJiZSBob3pcXHhFMXNhXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJLaWplbFxceEY2bFxceEU5c3JlIG5hZ3lcXHhFRHRcXHhFMXNcIixcbiAgdG9nZ2xlRWxlbWVudExvY2s6IFwiXCIsXG4gIG1vdmVQYWdlVXBEb3duOiBcIlwiLFxuICBtb3ZlUGFnZUxlZnRSaWdodDogXCJcIlxufTtcbnZhciBjbGVhckNhbnZhc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiUmFqenZcXHhFMXN6b24gYWxhcGhlbHl6ZXRiZVwiXG59O1xudmFyIHB1Ymxpc2hEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIktcXHhGNm55dnRcXHhFMXIga1xceEY2enpcXHhFOXRcXHhFOXRlbGVcIixcbiAgaXRlbU5hbWU6IFwiVFxceEU5dGVsIG5ldmVcIixcbiAgYXV0aG9yTmFtZTogXCJTemVyelxcdTAxNTEgbmV2ZVwiLFxuICBnaXRodWJVc2VybmFtZTogXCJHaXRIdWIgZmVsaGFzem5cXHhFMWxcXHhGM25cXHhFOXZcIixcbiAgdHdpdHRlclVzZXJuYW1lOiBcIlR3aXR0ZXIgZmVsaGFzem5cXHhFMWxcXHhGM25cXHhFOXZcIixcbiAgbGlicmFyeU5hbWU6IFwiS1xceEY2bnl2dFxceEUxciBuZXZlXCIsXG4gIGxpYnJhcnlEZXNjOiBcIktcXHhGNm55dnRcXHhFMXIgbGVcXHhFRHJcXHhFMXNhXCIsXG4gIHdlYnNpdGU6IFwiV2Vib2xkYWxcIixcbiAgcGxhY2Vob2xkZXI6IHtcbiAgICBhdXRob3JOYW1lOiBcIk5ldmVkIHZhZ3kgZmVsaGFzem5cXHhFMWxcXHhGM25ldmVkXCIsXG4gICAgbGlicmFyeU5hbWU6IFwiQSBrXFx4RjZueXZ0XFx4RTFyYWQgbmV2ZVwiLFxuICAgIGxpYnJhcnlEZXNjOiBcIkEga1xceEY2bnl2dFxceEUxcmFkIGhhc3puXFx4RTFsYXRcXHhFMXQgc2VnXFx4RUR0XFx1MDE1MSBsZVxceEVEclxceEUxc1wiLFxuICAgIGdpdGh1YkhhbmRsZTogXCJHaXRIdWItaGFuZGxlKG9wY2lvblxceEUxbGlzKSwgXFx4RURneSBzemVya2VzenRoZXRlZCBhIGtcXHhGNm55dnRcXHhFMXJhdCwgbWl1dFxceEUxbiBlbGtcXHhGQ2xkdGVkIGVsbGVuXFx1MDE1MXJ6XFx4RTlzcmVcIixcbiAgICB0d2l0dGVySGFuZGxlOiBcIlR3aXR0ZXItZmVsaGFzem5cXHhFMWxcXHhGM25cXHhFOXYgKG9wY2lvblxceEUxbGlzKSwgXFx4RURneSB0dWRqdWssIGtpbmVrIGtlbGwgalxceEYzdlxceEUxXFx4RURybmkgYSBUd2l0dGVyZW4ga2VyZXN6dFxceEZDbGkgcmVrbFxceEUxbW96XFx4RTFzdFwiLFxuICAgIHdlYnNpdGU6IFwiSGl2YXRrb3pcXHhFMXMgc3plbVxceEU5bHllcyB3ZWJoZWx5ZWRyZSB2YWd5IG1cXHhFMXNob3ZcXHhFMSAobmVtIGtcXHhGNnRlbGV6XFx1MDE1MSlcIlxuICB9LFxuICBlcnJvcnM6IHtcbiAgICByZXF1aXJlZDogXCJLXFx4RjZ0ZWxlelxcdTAxNTFcIixcbiAgICB3ZWJzaXRlOiBcIkFkaiBtZWcgZWd5IFxceEU5cnZcXHhFOW55ZXMgVVJMLXRcIlxuICB9LFxuICBub3RlRGVzY3JpcHRpb246IFwiS1xceEZDbGQgYmUga1xceEY2bnl2dFxceEUxcmFkYXQsIGhvZ3kgYmVrZXJcXHhGQ2xqXFx4RjZuIGEgPGxpbms+bnlpbHZcXHhFMW5vcyBrXFx4RjZueXZ0XFx4RTFyIHRcXHhFMXJvbFxceEYzYmE8L2xpbms+aG9neSBtXFx4RTFzb2sgaXMgZmVsaGFzem5cXHhFMWxoYXNzXFx4RTFrIGEgcmFqemFpa2Jhbi5cIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiQSBrXFx4RjZueXZ0XFx4RTFyYXQgZWxcXHUwMTUxc3pcXHhGNnIgbWFudVxceEUxbGlzYW4ga2VsbCBqXFx4RjN2XFx4RTFoYWd5bmkuIEtcXHhFOXJqXFx4RkNrLCBvbHZhc3NhIGVsIGEgPGxpbms+c2VnXFx4RTlkbGV0ZXQ8L2xpbms+IGJlbnlcXHhGQWp0XFx4RTFzYSBlbFxcdTAxNTF0dC4gU3pcXHhGQ2tzXFx4RTlnZSBsZXN6IGVneSBHaXRIdWItZmlcXHhGM2tyYSBhIGtvbW11bmlrXFx4RTFjaVxceEYzaG96IFxceEU5cyBhIG1cXHhGM2Rvc1xceEVEdFxceEUxc29raG96LCBoYSBrXFx4RTlyaWssIGRlIGV6IG5lbSBmZWx0XFx4RTl0bGVuXFx4RkNsIHN6XFx4RkNrc1xceEU5Z2VzLlwiLFxuICBub3RlTGljZW5zZTogXCJBIGJla1xceEZDbGRcXHhFOXNzZWwgZWxmb2dhZGphLCBob2d5IGEga1xceEY2bnl2dFxceEUxciBhIGtcXHhGNnZldGtlelxcdTAxNTEgYWxhdHQga2VyXFx4RkNsIGtcXHhGNnp6XFx4RTl0XFx4RTl0ZWxyZSA8bGluaz5NSVQgTGljZW5zeiA8L2xpbms+YW1pIHJcXHhGNnZpZGVuIGF6dCBqZWxlbnRpLCBob2d5IGJcXHhFMXJraSBrb3JsXFx4RTF0b3pcXHhFMXMgblxceEU5bGtcXHhGQ2wgaGFzem5cXHhFMWxoYXRqYSBcXHUwMTUxa2V0LlwiLFxuICBub3RlSXRlbXM6IFwiTWluZGVuIGtcXHhGNm55dnRcXHhFMXJlbGVtbmVrIHNhalxceEUxdCBuZXZcXHhFOXZlbCBrZWxsIHJlbmRlbGtlem5pZSwgaG9neSBzelxcdTAxNzFyaGV0XFx1MDE1MSBsZWd5ZW4uIEEga1xceEY2dmV0a2V6XFx1MDE1MSBrXFx4RjZueXZ0XFx4RTFyaSB0XFx4RTl0ZWxlayBrZXJcXHhGQ2xuZWsgYmVsZTpcIixcbiAgYXRsZWFzdE9uZUxpYkl0ZW06IFwiQSBrZXpkXFx4RTlzaGV6IHZcXHhFMWxhc3N6IGtpIGxlZ2FsXFx4RTFiYiBlZ3kga1xceEY2bnl2dFxceEUxcmkgZWxlbWV0XCIsXG4gIHJlcHVibGlzaFdhcm5pbmc6IFwiXCJcbn07XG52YXIgcHVibGlzaFN1Y2Nlc3NEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIkEga1xceEY2bnl2dFxceEUxciBiZWtcXHhGQ2xkdmVcIixcbiAgY29udGVudDogXCJLXFx4RjZzelxceEY2bmpcXHhGQ2sge3thdXRob3JOYW1lfX0uIEtcXHhGNm55dnRcXHhFMXJhZGF0IGVsa1xceEZDbGR0XFx4RkNrIGZlbFxceEZDbHZpenNnXFx4RTFsYXRyYS4gTnlvbW9uIGtcXHhGNnZldGhldGVkIGF6IFxceEUxbGxhcG90b3Q8bGluaz5pdHQ8L2xpbms+XCJcbn07XG52YXIgY29uZmlybURpYWxvZyA9IHtcbiAgcmVzZXRMaWJyYXJ5OiBcIktcXHhGNm55dnRcXHhFMXIgYWxhcGhlbHl6ZXRiZSBcXHhFMWxsXFx4RUR0XFx4RTFzYVwiLFxuICByZW1vdmVJdGVtc0Zyb21MaWI6IFwiQSBraXZcXHhFMWxhc3p0b3R0IGVsZW1layBlbHRcXHhFMXZvbFxceEVEdFxceEUxc2EgYSBrXFx4RjZueXZ0XFx4RTFyYlxceEYzbFwiXG59O1xudmFyIGltYWdlRXhwb3J0RGlhbG9nID0ge1xuICBoZWFkZXI6IFwiS1xceEU5cCBleHBvcnRcXHhFMWxcXHhFMXNhXCIsXG4gIGxhYmVsOiB7XG4gICAgd2l0aEJhY2tncm91bmQ6IFwiXCIsXG4gICAgb25seVNlbGVjdGVkOiBcIlwiLFxuICAgIGRhcmtNb2RlOiBcIlwiLFxuICAgIGVtYmVkU2NlbmU6IFwiXCIsXG4gICAgc2NhbGU6IFwiXCIsXG4gICAgcGFkZGluZzogXCJcIlxuICB9LFxuICB0b29sdGlwOiB7XG4gICAgZW1iZWRTY2VuZTogXCJcIlxuICB9LFxuICB0aXRsZToge1xuICAgIGV4cG9ydFRvUG5nOiBcIlwiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIlwiLFxuICAgIGNvcHlQbmdUb0NsaXBib2FyZDogXCJcIlxuICB9LFxuICBidXR0b246IHtcbiAgICBleHBvcnRUb1BuZzogXCJQTkdcIixcbiAgICBleHBvcnRUb1N2ZzogXCJTVkdcIixcbiAgICBjb3B5UG5nVG9DbGlwYm9hcmQ6IFwiVlxceEUxZ1xceEYzbGFwcmEgbVxceEUxc29sXFx4RTFzXCJcbiAgfVxufTtcbnZhciBlbmNyeXB0ZWQgPSB7XG4gIHRvb2x0aXA6IFwiQSByYWp6YWlkYXQgdlxceEU5Z3BvbnRvayBrXFx4RjZ6XFx4RjZ0dGkgdGl0a29zXFx4RUR0XFx4RTFzc2FsIHRcXHhFMXJvbGp1aywgdGVoXFx4RTF0IGF6IEV4Y2FsaWRyYXcgc3plcnZlcmVpclxcdTAxNTFsIHNlIHR1ZCBtXFx4RTFzIGJlbGVuXFx4RTl6bmkuXCIsXG4gIGxpbms6IFwiQmxvZ2JlamVneXpcXHhFOXMgYSB2XFx4RTlncG9udG9rIGtcXHhGNnpcXHhGNnR0aSB0aXRrb3NcXHhFRHRcXHhFMXNyXFx4RjNsIGF6IEV4Y2FsaWRyYXctYmFuXCJcbn07XG52YXIgc3RhdHMgPSB7XG4gIGFuZ2xlOiBcIlN6XFx4RjZnXCIsXG4gIGVsZW1lbnQ6IFwiRWxlbVwiLFxuICBlbGVtZW50czogXCJFbGVtZWtcIixcbiAgaGVpZ2h0OiBcIk1hZ2Fzc1xceEUxZ1wiLFxuICBzY2VuZTogXCJKZWxlbmV0XCIsXG4gIHNlbGVjdGVkOiBcIktpamVsXFx4RjZsdFwiLFxuICBzdG9yYWdlOiBcIlRcXHhFMXJoZWx5XCIsXG4gIHRpdGxlOiBcIlN0YXRpc3p0aWtcXHhFMWtcIixcbiAgdG90YWw6IFwiXFx4RDZzc3plc2VuXCIsXG4gIHZlcnNpb246IFwiVmVyemlcXHhGM1wiLFxuICB2ZXJzaW9uQ29weTogXCJLYXR0aW50cyBhIG1cXHhFMXNvbFxceEUxc2hvelwiLFxuICB2ZXJzaW9uTm90QXZhaWxhYmxlOiBcIkEgdmVyemlcXHhGMyBuZW0gZWxcXHhFOXJoZXRcXHUwMTUxXCIsXG4gIHdpZHRoOiBcIlN6XFx4RTlsZXNzXFx4RTlnXCJcbn07XG52YXIgdG9hc3QgPSB7XG4gIGFkZGVkVG9MaWJyYXJ5OiBcIktcXHhGNm55dnRcXHhFMXJob3ogYWR2YVwiLFxuICBjb3B5U3R5bGVzOiBcIk1cXHhFMXNvbHQgc3RcXHhFRGx1c29rLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiVlxceEUxZ1xceEYzbGFwcmEgbVxceEUxc29sdmEuXCIsXG4gIGNvcHlUb0NsaXBib2FyZEFzUG5nOiBcIkF6IHt7ZXhwb3J0U2VsZWN0aW9ufX0gUE5HIGZvcm1cXHhFMXR1bWJhbiBhIHZcXHhFMWdcXHhGM2xhcHJhIG1cXHhFMXNvbHZhIFxcbih7e2V4cG9ydENvbG9yU2NoZW1lfX0pXCIsXG4gIGZpbGVTYXZlZDogXCJGXFx4RTFqbCBlbG1lbnR2ZS5cIixcbiAgZmlsZVNhdmVkVG9GaWxlbmFtZTogXCJNZW50dmUgbWludCB7ZmlsZW5hbWV9XCIsXG4gIGNhbnZhczogXCJyYWp6dlxceEUxc3pvblwiLFxuICBzZWxlY3Rpb246IFwia2lqZWxcXHhGNmxcXHhFOXNcIixcbiAgcGFzdGVBc1NpbmdsZUVsZW1lbnQ6IFwiXCIsXG4gIHVuYWJsZVRvRW1iZWQ6IFwiXCIsXG4gIHVucmVjb2duaXplZExpbmtGb3JtYXQ6IFwiXCJcbn07XG52YXIgY29sb3JzID0ge1xuICB0cmFuc3BhcmVudDogXCJcXHhDMXRsXFx4RTF0c3pcXHhGM1wiLFxuICBibGFjazogXCJGZWtldGVcIixcbiAgd2hpdGU6IFwiRmVoXFx4RTlyXCIsXG4gIHJlZDogXCJQaXJvc1wiLFxuICBwaW5rOiBcIlJcXHhGM3pzYXN6XFx4RURuXCIsXG4gIGdyYXBlOiBcIlwiLFxuICB2aW9sZXQ6IFwiSWJvbHlhXCIsXG4gIGdyYXk6IFwiU3pcXHhGQ3JrZVwiLFxuICBibHVlOiBcIktcXHhFOWtcIixcbiAgY3lhbjogXCJDaVxceEUxblwiLFxuICB0ZWFsOiBcIktcXHhFOWtlcy16XFx4RjZsZFwiLFxuICBncmVlbjogXCJaXFx4RjZsZFwiLFxuICB5ZWxsb3c6IFwiU1xceEUxcmdhXCIsXG4gIG9yYW5nZTogXCJOYXJhbmNzc1xceEUxcmdhXCIsXG4gIGJyb256ZTogXCJCcm9uelwiXG59O1xudmFyIHdlbGNvbWVTY3JlZW4gPSB7XG4gIGFwcDoge1xuICAgIGNlbnRlcl9oZWFkaW5nOiBcIlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nX3BsdXM6IFwiXCIsXG4gICAgbWVudUhpbnQ6IFwiXCJcbiAgfSxcbiAgZGVmYXVsdHM6IHtcbiAgICBtZW51SGludDogXCJcIixcbiAgICBjZW50ZXJfaGVhZGluZzogXCJcIixcbiAgICB0b29sYmFySGludDogXCJcIixcbiAgICBoZWxwSGludDogXCJcIlxuICB9XG59O1xudmFyIGNvbG9yUGlja2VyID0ge1xuICBtb3N0VXNlZEN1c3RvbUNvbG9yczogXCJcIixcbiAgY29sb3JzOiBcIlwiLFxuICBzaGFkZXM6IFwiXCIsXG4gIGhleENvZGU6IFwiSGV4YWRlY2ltXFx4RTFsaXMga1xceEYzZFwiLFxuICBub1NoYWRlczogXCJcIlxufTtcbnZhciBvdmVyd3JpdGVDb25maXJtID0ge1xuICBhY3Rpb246IHtcbiAgICBleHBvcnRUb0ltYWdlOiB7XG4gICAgICB0aXRsZTogXCJFeHBvcnRcXHhFMWxcXHhFMXMga1xceEU5cGtcXHhFOW50XCIsXG4gICAgICBidXR0b246IFwiRXhwb3J0XFx4RTFsXFx4RTFzIGtcXHhFOXBrXFx4RTludFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9LFxuICAgIHNhdmVUb0Rpc2s6IHtcbiAgICAgIHRpdGxlOiBcIk1lbnRcXHhFOXMgYSBsZW1lenJlXCIsXG4gICAgICBidXR0b246IFwiTWVudFxceEU5cyBhIGxlbWV6cmVcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfSxcbiAgICBleGNhbGlkcmF3UGx1czoge1xuICAgICAgdGl0bGU6IFwiRXhjYWxpZHJhdytcIixcbiAgICAgIGJ1dHRvbjogXCJcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfVxuICB9LFxuICBtb2RhbDoge1xuICAgIGxvYWRGcm9tRmlsZToge1xuICAgICAgdGl0bGU6IFwiQmV0XFx4RjZsdFxceEU5cyBmXFx4RTFqbGJcXHhGM2xcIixcbiAgICAgIGJ1dHRvbjogXCJCZXRcXHhGNmx0XFx4RTlzIGZcXHhFMWpsYlxceEYzbFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9LFxuICAgIHNoYXJlYWJsZUxpbms6IHtcbiAgICAgIHRpdGxlOiBcIkZlbHRcXHhGNmx0XFx4RTFzIGxpbmtiXFx1MDE1MWxcIixcbiAgICAgIGJ1dHRvbjogXCJcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfVxuICB9XG59O1xudmFyIG1lcm1haWQgPSB7XG4gIHRpdGxlOiBcIlwiLFxuICBidXR0b246IFwiXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlwiLFxuICBzeW50YXg6IFwiXCIsXG4gIHByZXZpZXc6IFwiXCJcbn07XG52YXIgaHVfSFVfZGVmYXVsdCA9IHtcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBidXR0b25zLFxuICBhbGVydHMsXG4gIGVycm9ycyxcbiAgdG9vbEJhcixcbiAgaGVhZGluZ3MsXG4gIGhpbnRzLFxuICBjYW52YXNFcnJvcixcbiAgZXJyb3JTcGxhc2gsXG4gIHJvb21EaWFsb2csXG4gIGVycm9yRGlhbG9nLFxuICBleHBvcnREaWFsb2csXG4gIGhlbHBEaWFsb2csXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgY29uZmlybURpYWxvZyxcbiAgaW1hZ2VFeHBvcnREaWFsb2csXG4gIGVuY3J5cHRlZCxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICBjb2xvcnMsXG4gIHdlbGNvbWVTY3JlZW4sXG4gIGNvbG9yUGlja2VyLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBtZXJtYWlkXG59O1xuZXhwb3J0IHtcbiAgYWxlcnRzLFxuICBidXR0b25zLFxuICBjYW52YXNFcnJvcixcbiAgY2xlYXJDYW52YXNEaWFsb2csXG4gIGNvbG9yUGlja2VyLFxuICBjb2xvcnMsXG4gIGNvbmZpcm1EaWFsb2csXG4gIGh1X0hVX2RlZmF1bHQgYXMgZGVmYXVsdCxcbiAgZW5jcnlwdGVkLFxuICBlcnJvckRpYWxvZyxcbiAgZXJyb3JTcGxhc2gsXG4gIGVycm9ycyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWFkaW5ncyxcbiAgaGVscERpYWxvZyxcbiAgaGludHMsXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBsYWJlbHMsXG4gIGxpYnJhcnksXG4gIG1lcm1haWQsXG4gIG92ZXJ3cml0ZUNvbmZpcm0sXG4gIHB1Ymxpc2hEaWFsb2csXG4gIHB1Ymxpc2hTdWNjZXNzRGlhbG9nLFxuICByb29tRGlhbG9nLFxuICBzdGF0cyxcbiAgdG9hc3QsXG4gIHRvb2xCYXIsXG4gIHdlbGNvbWVTY3JlZW5cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1odS1IVS1WSVlaSTNYNC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/hu-HU-VIYZI3X4.js\n"));

/***/ })

}]);