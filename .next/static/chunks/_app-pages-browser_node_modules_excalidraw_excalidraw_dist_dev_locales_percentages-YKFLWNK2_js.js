"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_percentages-YKFLWNK2_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/percentages-YKFLWNK2.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/percentages-YKFLWNK2.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _chunk_MFAYKRVR_js__WEBPACK_IMPORTED_MODULE_0__.percentages_default),\n/* harmony export */   en: () => (/* reexport safe */ _chunk_MFAYKRVR_js__WEBPACK_IMPORTED_MODULE_0__.en),\n/* harmony export */   kaa: () => (/* reexport safe */ _chunk_MFAYKRVR_js__WEBPACK_IMPORTED_MODULE_0__.kaa)\n/* harmony export */ });\n/* harmony import */ var _chunk_MFAYKRVR_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-MFAYKRVR.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-MFAYKRVR.js\");\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n\n//# sourceMappingURL=percentages-YKFLWNK2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvcGVyY2VudGFnZXMtWUtGTFdOSzIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFJOEI7QUFDQTtBQUs1QjtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vbm9kZV9tb2R1bGVzL0BleGNhbGlkcmF3L2V4Y2FsaWRyYXcvZGlzdC9kZXYvbG9jYWxlcy9wZXJjZW50YWdlcy1ZS0ZMV05LMi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBlbixcbiAga2FhLFxuICBwZXJjZW50YWdlc19kZWZhdWx0XG59IGZyb20gXCIuLi9jaHVuay1NRkFZS1JWUi5qc1wiO1xuaW1wb3J0IFwiLi4vY2h1bmstWERGQ1VVVDYuanNcIjtcbmV4cG9ydCB7XG4gIHBlcmNlbnRhZ2VzX2RlZmF1bHQgYXMgZGVmYXVsdCxcbiAgZW4sXG4gIGthYVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBlcmNlbnRhZ2VzLVlLRkxXTksyLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/percentages-YKFLWNK2.js\n"));

/***/ })

}]);