"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_pt-PT-W56WCN7P_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/pt-PT-W56WCN7P.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/pt-PT-W56WCN7P.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ pt_PT_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/pt-PT.json\nvar labels = {\n  paste: \"Colar\",\n  pasteAsPlaintext: \"Colar como texto simples\",\n  pasteCharts: \"Colar gr\\xE1ficos\",\n  selectAll: \"Selecionar tudo\",\n  multiSelect: \"Adicionar elemento \\xE0 sele\\xE7\\xE3o\",\n  moveCanvas: \"Mover tela\",\n  cut: \"Cortar\",\n  copy: \"Copiar\",\n  copyAsPng: \"Copiar para a \\xE1rea de transfer\\xEAncia como PNG\",\n  copyAsSvg: \"Copiar para a \\xE1rea de transfer\\xEAncia como SVG\",\n  copyText: \"Copiar para \\xC1rea de Transfer\\xEAncia como texto\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Trazer para o primeiro plano\",\n  sendToBack: \"Enviar para o plano de fundo\",\n  bringToFront: \"Trazer para o primeiro plano\",\n  sendBackward: \"Enviar para tr\\xE1s\",\n  delete: \"Apagar\",\n  copyStyles: \"Copiar os estilos\",\n  pasteStyles: \"Colar os estilos\",\n  stroke: \"Contornos\",\n  background: \"Fundo\",\n  fill: \"Preenchimento\",\n  strokeWidth: \"Espessura do tra\\xE7o\",\n  strokeStyle: \"Estilo de tra\\xE7o\",\n  strokeStyle_solid: \"S\\xF3lido\",\n  strokeStyle_dashed: \"Tracejado\",\n  strokeStyle_dotted: \"Pontilhado\",\n  sloppiness: \"Desleixo\",\n  opacity: \"Opacidade\",\n  textAlign: \"Alinhamento do texto\",\n  edges: \"Arestas\",\n  sharp: \"Agu\\xE7ado\",\n  round: \"Redondo\",\n  arrowheads: \"Pontas\",\n  arrowhead_none: \"Nenhuma\",\n  arrowhead_arrow: \"Seta\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Tri\\xE2ngulo\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Tamanho da fonte\",\n  fontFamily: \"Fam\\xEDlia da fontes\",\n  addWatermark: 'Adicionar \"Feito com Excalidraw\"',\n  handDrawn: \"Manuscrito\",\n  normal: \"Normal\",\n  code: \"C\\xF3digo\",\n  small: \"Pequeno\",\n  medium: \"M\\xE9dio\",\n  large: \"Grande\",\n  veryLarge: \"Muito grande\",\n  solid: \"S\\xF3lido\",\n  hachure: \"Eclos\\xE3o\",\n  zigzag: \"ziguezague\",\n  crossHatch: \"Sombreado\",\n  thin: \"Fino\",\n  bold: \"Espesso\",\n  left: \"Esquerda\",\n  center: \"Centralizar\",\n  right: \"Direita\",\n  extraBold: \"Muito espesso\",\n  architect: \"Arquitecto\",\n  artist: \"Artista\",\n  cartoonist: \"Caricaturista\",\n  fileTitle: \"Nome do ficheiro\",\n  colorPicker: \"Seletor de cores\",\n  canvasColors: \"Usado na tela\",\n  canvasBackground: \"Fundo da \\xE1rea de desenho\",\n  drawingCanvas: \"\\xC1rea de desenho\",\n  layers: \"Camadas\",\n  actions: \"A\\xE7\\xF5es\",\n  language: \"Idioma\",\n  liveCollaboration: \"Colabora\\xE7\\xE3o ao vivo...\",\n  duplicateSelection: \"Duplicar\",\n  untitled: \"Sem t\\xEDtulo\",\n  name: \"Nome\",\n  yourName: \"O seu nome\",\n  madeWithExcalidraw: \"Feito com Excalidraw\",\n  group: \"Agrupar sele\\xE7\\xE3o\",\n  ungroup: \"Desagrupar sele\\xE7\\xE3o\",\n  collaborators: \"Colaboradores\",\n  showGrid: \"Mostrar grelha\",\n  addToLibrary: \"Adicionar \\xE0 biblioteca\",\n  removeFromLibrary: \"Remover da biblioteca\",\n  libraryLoadingMessage: \"A carregar a biblioteca\\u2026\",\n  libraries: \"Procurar bibliotecas\",\n  loadingScene: \"A carregar a cena\\u2026\",\n  align: \"Alinhamento\",\n  alignTop: \"Alinhar ao topo\",\n  alignBottom: \"Alinhar ao fundo\",\n  alignLeft: \"Alinhar \\xE0 esquerda\",\n  alignRight: \"Alinhar \\xE0 direita\",\n  centerVertically: \"Centrar verticalmente\",\n  centerHorizontally: \"Centrar horizontalmente\",\n  distributeHorizontally: \"Distribuir horizontalmente\",\n  distributeVertically: \"Distribuir verticalmente\",\n  flipHorizontal: \"Inverter horizontalmente\",\n  flipVertical: \"Inverter verticalmente\",\n  viewMode: \"Modo de visualiza\\xE7\\xE3o\",\n  share: \"Partilhar\",\n  showStroke: \"Mostrar seletor de cores do tra\\xE7o\",\n  showBackground: \"Mostrar seletor de cores do fundo\",\n  toggleTheme: \"Alternar tema\",\n  personalLib: \"Biblioteca pessoal\",\n  excalidrawLib: \"Biblioteca do Excalidraw\",\n  decreaseFontSize: \"Reduzir o tamanho do tipo de letra\",\n  increaseFontSize: \"Aumentar o tamanho do tipo de letra\",\n  unbindText: \"Desvincular texto\",\n  bindText: \"Ligar texto ao recipiente\",\n  createContainerFromText: \"Envolver texto num recipiente\",\n  link: {\n    edit: \"Editar liga\\xE7\\xE3o\",\n    editEmbed: \"\",\n    create: \"Criar liga\\xE7\\xE3o\",\n    createEmbed: \"\",\n    label: \"Liga\\xE7\\xE3o\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Editar linha\",\n    exit: \"Sair do editor de linha\"\n  },\n  elementLock: {\n    lock: \"Bloquear\",\n    unlock: \"Desbloquear\",\n    lockAll: \"Bloquear todos\",\n    unlockAll: \"Desbloquear todos\"\n  },\n  statusPublished: \"Publicado\",\n  sidebarLock: \"Manter a barra lateral aberta\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Ainda n\\xE3o foram adicionados nenhuns itens...\",\n  hint_emptyLibrary: \"Seleccione um item na tela para adicion\\xE1-lo aqui, ou ent\\xE3o instale uma biblioteca do reposit\\xF3rio p\\xFAblico abaixo.\",\n  hint_emptyPrivateLibrary: \"Seleccione um item na tela para adicion\\xE1-lo aqui.\"\n};\nvar buttons = {\n  clearReset: \"Limpar a \\xE1rea de desenho e redefinir a cor de fundo\",\n  exportJSON: \"Exportar para ficheiro\",\n  exportImage: \"Exportar imagem...\",\n  export: \"Guardar para...\",\n  copyToClipboard: \"Copiar para o clipboard\",\n  save: \"Guardar no ficheiro atual\",\n  saveAs: \"Guardar como\",\n  load: \"Abrir\",\n  getShareableLink: \"Obter um link de partilha\",\n  close: \"Fechar\",\n  selectLanguage: \"Selecionar idioma\",\n  scrollBackToContent: \"Voltar ao conte\\xFAdo\",\n  zoomIn: \"Aumentar zoom\",\n  zoomOut: \"Diminuir zoom\",\n  resetZoom: \"Redefinir zoom\",\n  menu: \"Menu\",\n  done: \"Conclu\\xEDdo\",\n  edit: \"Editar\",\n  undo: \"Desfazer\",\n  redo: \"Refazer\",\n  resetLibrary: \"Repor a biblioteca\",\n  createNewRoom: \"Criar nova sala\",\n  fullScreen: \"Ecr\\xE3 inteiro\",\n  darkMode: \"Modo escuro\",\n  lightMode: \"Modo claro\",\n  zenMode: \"Modo zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Sair do modo zen\",\n  cancel: \"Cancelar\",\n  clear: \"Limpar\",\n  remove: \"Remover\",\n  embed: \"\",\n  publishLibrary: \"Publicar\",\n  submit: \"Enviar\",\n  confirm: \"Confirmar\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Isto ir\\xE1 limpar toda a \\xE1rea de desenho. Tem a certeza?\",\n  couldNotCreateShareableLink: \"N\\xE3o foi poss\\xEDvel criar um link partilh\\xE1vel.\",\n  couldNotCreateShareableLinkTooBig: \"N\\xE3o foi poss\\xEDvel criar um link partilh\\xE1vel: a cena \\xE9 muito grande\",\n  couldNotLoadInvalidFile: \"N\\xE3o foi poss\\xEDvel carregar o ficheiro inv\\xE1lido\",\n  importBackendFailed: \"A importa\\xE7\\xE3o do servidor falhou.\",\n  cannotExportEmptyCanvas: \"N\\xE3o \\xE9 poss\\xEDvel exportar uma \\xE1rea de desenho vazia.\",\n  couldNotCopyToClipboard: \"N\\xE3o foi poss\\xEDvel copiar para a \\xE1rea de transfer\\xEAncia.\",\n  decryptFailed: \"N\\xE3o foi poss\\xEDvel desencriptar os dados.\",\n  uploadedSecurly: \"O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros n\\xE3o podem ler o conte\\xFAdo.\",\n  loadSceneOverridePrompt: \"Se carregar um desenho externo substituir\\xE1 o conte\\xFAdo existente. Quer continuar?\",\n  collabStopOverridePrompt: \"Ao interromper a sess\\xE3o ir\\xE1 substituir o \\xFAltimo desenho guardado. Tem a certeza?\\n\\n(Caso queira manter o \\xFAltimo desenho, simplesmente feche a janela do navegador.)\",\n  errorAddingToLibrary: \"N\\xE3o foi poss\\xEDvel adicionar o item \\xE0 biblioteca\",\n  errorRemovingFromLibrary: \"N\\xE3o foi poss\\xEDvel remover o item da biblioteca\",\n  confirmAddLibrary: \"Isso adicionar\\xE1 {{numShapes}} forma(s) \\xE0 sua biblioteca. Tem a certeza?\",\n  imageDoesNotContainScene: \"Esta imagem parece n\\xE3o conter dados de cenas. Ativou a incorpora\\xE7\\xE3o da cena durante a exporta\\xE7\\xE3o?\",\n  cannotRestoreFromImage: \"N\\xE3o foi poss\\xEDvel restaurar a cena deste ficheiro de imagem\",\n  invalidSceneUrl: \"N\\xE3o foi poss\\xEDvel importar a cena a partir do URL fornecido. Ou est\\xE1 mal formado ou n\\xE3o cont\\xE9m dados JSON do Excalidraw v\\xE1lidos.\",\n  resetLibrary: \"Isto ir\\xE1 limpar a sua biblioteca. Tem a certeza?\",\n  removeItemsFromsLibrary: \"Apagar {{count}} item(ns) da biblioteca?\",\n  invalidEncryptionKey: \"Chave de encripta\\xE7\\xE3o deve ter 22 caracteres. A colabora\\xE7\\xE3o ao vivo est\\xE1 desativada.\",\n  collabOfflineWarning: \"Sem liga\\xE7\\xE3o \\xE0 internet dispon\\xEDvel.\\nAs suas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipo de ficheiro n\\xE3o suportado.\",\n  imageInsertError: \"N\\xE3o foi poss\\xEDvel inserir a imagem, tente novamente mais tarde...\",\n  fileTooBig: \"O ficheiro \\xE9 muito grande. O tamanho m\\xE1ximo permitido \\xE9 {{maxSize}}.\",\n  svgImageInsertError: \"N\\xE3o foi poss\\xEDvel inserir a imagem SVG. A marca\\xE7\\xE3o SVG parece inv\\xE1lida.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG inv\\xE1lido.\",\n  cannotResolveCollabServer: \"N\\xE3o foi poss\\xEDvel fazer a liga\\xE7\\xE3o ao servidor colaborativo. Por favor, volte a carregar a p\\xE1gina e tente novamente.\",\n  importLibraryError: \"N\\xE3o foi poss\\xEDvel carregar a biblioteca\",\n  collabSaveFailed: \"N\\xE3o foi poss\\xEDvel guardar na base de dados de backend. Se os problemas persistirem, guarde o ficheiro localmente para garantir que n\\xE3o perde o seu trabalho.\",\n  collabSaveFailed_sizeExceeded: \"N\\xE3o foi poss\\xEDvel guardar na base de dados de backend, o ecr\\xE3 parece estar muito grande. Deve guardar o ficheiro localmente para garantir que n\\xE3o perde o seu trabalho.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Sele\\xE7\\xE3o\",\n  image: \"Inserir imagem\",\n  rectangle: \"Ret\\xE2ngulo\",\n  diamond: \"Losango\",\n  ellipse: \"Elipse\",\n  arrow: \"Flecha\",\n  line: \"Linha\",\n  freedraw: \"Desenhar\",\n  text: \"Texto\",\n  library: \"Biblioteca\",\n  lock: \"Manter a ferramenta selecionada ativa ap\\xF3s desenhar\",\n  penMode: \"Modo caneta - impedir toque\",\n  link: \"Acrescentar/ Adicionar liga\\xE7\\xE3o para uma forma seleccionada\",\n  eraser: \"Borracha\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"M\\xE3o (ferramenta de movimento da tela)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"A\\xE7\\xF5es da \\xE1rea de desenho\",\n  selectedShapeActions: \"A\\xE7\\xF5es das formas selecionadas\",\n  shapes: \"Formas\"\n};\nvar hints = {\n  canvasPanning: \"Para mover a tela, carregue na roda do rato ou na barra de espa\\xE7o enquanto arrasta, ou use a ferramenta da m\\xE3o\",\n  linearElement: \"Clique para iniciar v\\xE1rios pontos, arraste para uma \\xFAnica linha\",\n  freeDraw: \"Clique e arraste, large quando terminar\",\n  text: \"Dica: tamb\\xE9m pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de sele\\xE7\\xE3o\",\n  embeddable: \"\",\n  text_selected: \"Clique duas vezes ou pressione a tecla Enter para editar o texto\",\n  text_editing: \"Pressione a tecla Escape ou CtrlOrCmd+ENTER para terminar a edi\\xE7\\xE3o\",\n  linearElementMulti: \"Clique no \\xFAltimo ponto ou pressione Escape ou Enter para terminar\",\n  lockAngle: \"Pode restringir o \\xE2ngulo mantendo premida a tecla SHIFT\",\n  resize: \"Pode restringir as propor\\xE7\\xF5es mantendo a tecla SHIFT premida enquanto redimensiona,\\nmantenha a tecla ALT premida para redimensionar a partir do centro\",\n  resizeImage: \"Pode redimensionar livremente mantendo pressionada a tecla SHIFT,\\nmantenha pressionada a tecla ALT para redimensionar do centro\",\n  rotate: \"Pode restringir os \\xE2ngulos mantendo a tecla SHIFT premida enquanto roda\",\n  lineEditor_info: \"Pressione CtrlOrCmd e fa\\xE7a um duplo-clique ou pressione CtrlOrCmd + Enter para editar pontos\",\n  lineEditor_pointSelected: \"Carregue na tecla Delete para remover o(s) ponto(s), CtrlOuCmd+D para duplicar, ou arraste para mover\",\n  lineEditor_nothingSelected: \"Seleccione um ponto para editar (carregue em SHIFT para seleccionar v\\xE1rios),\\nou carregue em Alt e clique para acrescentar novos pontos\",\n  placeImage: \"Clique para colocar a imagem ou clique e arraste para definir o seu tamanho manualmente\",\n  publishLibrary: \"Publique a sua pr\\xF3pria biblioteca\",\n  bindTextToElement: \"Carregue Enter para acrescentar texto\",\n  deepBoxSelect: \"Mantenha a tecla CtrlOrCmd carregada para selec\\xE7\\xE3o profunda, impedindo o arrastamento\",\n  eraserRevert: \"Carregue tamb\\xE9m em Alt para reverter os elementos marcados para serem apagados\",\n  firefox_clipboard_write: 'Esta fun\\xE7\\xE3o pode provavelmente ser ativada definindo a op\\xE7\\xE3o \"dom.events.asyncClipboard.clipboardItem\" como \"true\". Para alterar os sinalizadores do navegador no Firefox, visite a p\\xE1gina \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"N\\xE3o \\xE9 poss\\xEDvel mostrar uma pr\\xE9-visualiza\\xE7\\xE3o\",\n  canvasTooBig: \"A \\xE1rea de desenho pode ser muito grande.\",\n  canvasTooBigTip: \"Dica: tente aproximar um pouco os elementos mais distantes.\"\n};\nvar errorSplash = {\n  headingMain: \"Foi encontrado um erro. Tente <button>recarregar a p\\xE1gina.</button>\",\n  clearCanvasMessage: \"Se a recarga n\\xE3o funcionar, tente <button>a limpar a \\xE1rea de desenho.</button>\",\n  clearCanvasCaveat: \" Isso resultar\\xE1 em perda de trabalho \",\n  trackedToSentry: \"O erro com o identificador {{eventId}} foi rastreado no nosso sistema.\",\n  openIssueMessage: \"Fomos muito cautelosos para n\\xE3o incluir suas informa\\xE7\\xF5es de cena no erro. Se sua cena n\\xE3o for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informa\\xE7\\xF5es abaixo, copiando e colando no relat\\xF3rio de erros no GitHub.\",\n  sceneContent: \"Conte\\xFAdo da cena:\"\n};\nvar roomDialog = {\n  desc_intro: \"Pode convidar pessoas para colaborarem na sua cena atual.\",\n  desc_privacy: \"N\\xE3o se preocupe, a sess\\xE3o usa criptografia de ponta-a-ponta, por isso o que desenhar permanecer\\xE1 privado. Nem mesmo o nosso servidor poder\\xE1 ver o que cria.\",\n  button_startSession: \"Iniciar sess\\xE3o\",\n  button_stopSession: \"Parar sess\\xE3o\",\n  desc_inProgressIntro: \"A sess\\xE3o de colabora\\xE7\\xE3o ao vivo est\\xE1 agora em andamento.\",\n  desc_shareLink: \"Partilhe este link com qualquer pessoa com quem queira colaborar:\",\n  desc_exitSession: \"Interrompendo a sess\\xE3o ir\\xE1 desconectar-se da sala, mas poder\\xE1 continuar a trabalhar com a cena localmente. Note que isso n\\xE3o afetar\\xE1 outras pessoas e elas ainda poder\\xE3o colaborar nas vers\\xF5es deles.\",\n  shareTitle: \"Participe numa sess\\xE3o de colabora\\xE7\\xE3o ao vivo no Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Erro\"\n};\nvar exportDialog = {\n  disk_title: \"Guardar no disco\",\n  disk_details: \"Exportar os dados da cena para um ficheiro do qual poder\\xE1 importar mais tarde.\",\n  disk_button: \"Guardar num ficheiro\",\n  link_title: \"Link partilh\\xE1vel\",\n  link_details: \"Exportar como um link de apenas leitura.\",\n  link_button: \"Exportar para link\",\n  excalidrawplus_description: \"Guardar a cena no seu espa\\xE7o de trabalho Excalidraw+\",\n  excalidrawplus_button: \"Exportar\",\n  excalidrawplus_exportError: \"N\\xE3o foi poss\\xEDvel exportar para o Excalidraw+ neste momento...\"\n};\nvar helpDialog = {\n  blog: \"Leia o nosso blogue\",\n  click: \"clicar\",\n  deepSelect: \"Selec\\xE7\\xE3o profunda\",\n  deepBoxSelect: \"Selec\\xE7\\xE3o profunda dentro da caixa, impedindo que seja arrastada\",\n  curvedArrow: \"Seta curva\",\n  curvedLine: \"Linha curva\",\n  documentation: \"Documenta\\xE7\\xE3o\",\n  doubleClick: \"clique duplo\",\n  drag: \"arrastar\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Editar pontos de linha/seta\",\n  editText: \"Editar texto / adicionar etiqueta\",\n  github: \"Encontrou algum problema? Informe-nos\",\n  howto: \"Siga os nossos guias\",\n  or: \"ou\",\n  preventBinding: \"Prevenir fixa\\xE7\\xE3o de seta\",\n  tools: \"Ferramentas\",\n  shortcuts: \"Atalhos de teclado\",\n  textFinish: \"Finalizar edi\\xE7\\xE3o (editor texto)\",\n  textNewLine: \"Adicionar nova linha (editor de texto)\",\n  title: \"Ajuda\",\n  view: \"Visualizar\",\n  zoomToFit: \"Ajustar para todos os elementos caberem\",\n  zoomToSelection: \"Ampliar a sele\\xE7\\xE3o\",\n  toggleElementLock: \"Trancar/destrancar selec\\xE7\\xE3o\",\n  movePageUpDown: \"Mover p\\xE1gina para cima / baixo\",\n  movePageLeftRight: \"Mover p\\xE1gina para esquerda / direita\"\n};\nvar clearCanvasDialog = {\n  title: \"Apagar tela\"\n};\nvar publishDialog = {\n  title: \"Publicar biblioteca\",\n  itemName: \"Nome do item\",\n  authorName: \"Nome do autor\",\n  githubUsername: \"Nome de utilizador do GitHub\",\n  twitterUsername: \"Nome de utilizador no Twitter\",\n  libraryName: \"Nome da biblioteca\",\n  libraryDesc: \"Descri\\xE7\\xE3o da biblioteca\",\n  website: \"P\\xE1gina web\",\n  placeholder: {\n    authorName: \"Introduza o seu nome ou nome de utilizador\",\n    libraryName: \"Nome da sua biblioteca\",\n    libraryDesc: \"Descri\\xE7\\xE3o da sua biblioteca para ajudar as pessoas a entender a utiliza\\xE7\\xE3o dela\",\n    githubHandle: \"Identificador do GitHub (opcional), para que possa editar a biblioteca depois desta ser enviada para revis\\xE3o\",\n    twitterHandle: \"Nome do Twitter (opcional), para que saibamos quem merece os cr\\xE9ditos na promo\\xE7\\xE3o via Twitter\",\n    website: \"Liga\\xE7\\xE3o para a sua p\\xE1gina pessoal ou qualquer outra (opcional)\"\n  },\n  errors: {\n    required: \"Obrigat\\xF3rio\",\n    website: \"Introduza um URL v\\xE1lido\"\n  },\n  noteDescription: \"Envie a sua biblioteca para ser inclu\\xEDda no <link>reposit\\xF3rio de bibliotecas p\\xFAblicas</link>para outras pessoas a poderem usar nos seus pr\\xF3prios desenhos.\",\n  noteGuidelines: \"A biblioteca precisa ser aprovada manualmente primeiro. Por favor, leia <link>orienta\\xE7\\xF5es</link> antes de enviar. Vai precisar de uma conta no GitHub para comunicar e fazer altera\\xE7\\xF5es se solicitado, mas n\\xE3o \\xE9 estritamente necess\\xE1ria.\",\n  noteLicense: \"Ao enviar, concorda que a biblioteca ser\\xE1 publicada sob a <link>Licen\\xE7a MIT, </link>o que significa, de forma resumida, que qualquer pessoa pode utiliz\\xE1-la sem restri\\xE7\\xF5es.\",\n  noteItems: \"Cada item da biblioteca deve ter o seu pr\\xF3prio nome para que este seja pesquis\\xE1vel com filtros. Os seguintes itens da biblioteca ser\\xE3o inclu\\xEDdos:\",\n  atleastOneLibItem: \"Por favor, seleccione pelo menos um item da biblioteca para come\\xE7ar\",\n  republishWarning: \"Nota: alguns dos itens seleccionados est\\xE3o marcados como j\\xE1 publicados/enviados. S\\xF3 deve reenviar itens ao actualizar uma biblioteca existente ou submiss\\xE3o.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteca enviada\",\n  content: \"Obrigado {{authorName}}. A sua biblioteca foi enviada para an\\xE1lise. Pode acompanhar o status<link>aqui</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Repor a biblioteca\",\n  removeItemsFromLib: \"Remover os itens seleccionados da biblioteca\"\n};\nvar imageExportDialog = {\n  header: \"Exportar imagem\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"Cena embutida\",\n    scale: \"\",\n    padding: \"Espa\\xE7amento\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"Exportar em PNG\",\n    exportToSvg: \"Exportar em SVG\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Os seus desenhos s\\xE3o encriptados de ponta-a-ponta, por isso os servidores do Excalidraw nunca os ver\\xE3o.\",\n  link: \"Publica\\xE7\\xE3o de blogue na encripta\\xE7\\xE3o ponta-a-ponta no Excalidraw\"\n};\nvar stats = {\n  angle: \"\\xC2ngulo\",\n  element: \"Elemento\",\n  elements: \"Elementos\",\n  height: \"Altura\",\n  scene: \"Cena\",\n  selected: \"Selecionado\",\n  storage: \"Armazenamento\",\n  title: \"Estat\\xEDsticas para nerds\",\n  total: \"Total\",\n  version: \"Vers\\xE3o\",\n  versionCopy: \"Clique para copiar\",\n  versionNotAvailable: \"Vers\\xE3o n\\xE3o dispon\\xEDvel\",\n  width: \"Largura\"\n};\nvar toast = {\n  addedToLibrary: \"Acrescentado \\xE0 biblioteca\",\n  copyStyles: \"Estilos copiados.\",\n  copyToClipboard: \"Copiado para a \\xE1rea de transfer\\xEAncia.\",\n  copyToClipboardAsPng: \"{{exportSelection}} copiado para a \\xE1rea de transfer\\xEAncia como PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Ficheiro guardado.\",\n  fileSavedToFilename: \"Guardado como {filename}\",\n  canvas: \"\\xE1rea de desenho\",\n  selection: \"sele\\xE7\\xE3o\",\n  pasteAsSingleElement: \"Usar {{shortcut}} para colar como um \\xFAnico elemento,\\nou colar num editor de texto existente\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transparente\",\n  black: \"Preto\",\n  white: \"Branco\",\n  red: \"Vermelho\",\n  pink: \"Rosa\",\n  grape: \"Uva\",\n  violet: \"Violeta\",\n  gray: \"Cinza\",\n  blue: \"Azul\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"Verde\",\n  yellow: \"Amarelo\",\n  orange: \"Laranja\",\n  bronze: \"Bronze\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Todos os dados s\\xE3o guardados no seu navegador local.\",\n    center_heading_plus: \"Queria antes ir para o Excalidraw+?\",\n    menuHint: \"Exportar, prefer\\xEAncias, idiomas...\"\n  },\n  defaults: {\n    menuHint: \"Exportar, prefer\\xEAncias e outros...\",\n    center_heading: \"Diagramas. Feito. Simples.\",\n    toolbarHint: \"Escolha uma ferramenta e comece a desenhar!\",\n    helpHint: \"Atalhos e ajuda\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"Cores\",\n  shades: \"Tons\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"Guardar no disco\",\n      button: \"Guardar no disco\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Carregar a partir de ficheiro\",\n      button: \"Carregar a partir de ficheiro\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar pt_PT_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=pt-PT-W56WCN7P.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/pt-PT-W56WCN7P.js\n"));

/***/ })

}]);