"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_nb-NO-BMB73KRH_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/nb-NO-BMB73KRH.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/nb-NO-BMB73KRH.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ nb_NO_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/nb-NO.json\nvar labels = {\n  paste: \"Lim inn\",\n  pasteAsPlaintext: \"Lim inn som klartekst\",\n  pasteCharts: \"Lim inn diagrammer\",\n  selectAll: \"Velg alt\",\n  multiSelect: \"Legg til element i utvalg\",\n  moveCanvas: \"Flytt lerretet\",\n  cut: \"Klipp ut\",\n  copy: \"Kopier\",\n  copyAsPng: \"Kopier til PNG\",\n  copyAsSvg: \"Kopier til utklippstavlen som SVG\",\n  copyText: \"Kopier til utklippstavlen som tekst\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Flytt framover\",\n  sendToBack: \"Send bakerst\",\n  bringToFront: \"Flytt forrest\",\n  sendBackward: \"Send bakover\",\n  delete: \"Slett\",\n  copyStyles: \"Kopier stiler\",\n  pasteStyles: \"Lim inn stiler\",\n  stroke: \"Strek\",\n  background: \"Bakgrunn\",\n  fill: \"Fyll\",\n  strokeWidth: \"Strektykkelse\",\n  strokeStyle: \"Strekstil\",\n  strokeStyle_solid: \"Heltrukket\",\n  strokeStyle_dashed: \"Stiplet\",\n  strokeStyle_dotted: \"Prikket\",\n  sloppiness: \"Ujevnhet\",\n  opacity: \"Synlighet\",\n  textAlign: \"Tekstjustering\",\n  edges: \"Kanter\",\n  sharp: \"Skarp\",\n  round: \"Rund\",\n  arrowheads: \"Pilspisser\",\n  arrowhead_none: \"Ingen\",\n  arrowhead_arrow: \"Pil\",\n  arrowhead_bar: \"S\\xF8yle\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Trekant\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Skriftst\\xF8rrelse\",\n  fontFamily: \"Fontfamilie\",\n  addWatermark: 'Legg til \"Laget med Excalidraw\"',\n  handDrawn: \"H\\xE5ndtegnet\",\n  normal: \"Normal\",\n  code: \"Kode\",\n  small: \"Liten\",\n  medium: \"Medium\",\n  large: \"Stor\",\n  veryLarge: \"Sv\\xE6rt stor\",\n  solid: \"Helfarge\",\n  hachure: \"Skravert\",\n  zigzag: \"Sikk-sakk\",\n  crossHatch: \"Krysskravert\",\n  thin: \"Tynn\",\n  bold: \"Tykk\",\n  left: \"Venstre\",\n  center: \"Midtstill\",\n  right: \"H\\xF8yre\",\n  extraBold: \"Ekstra tykk\",\n  architect: \"Arkitekt\",\n  artist: \"Kunstner\",\n  cartoonist: \"Tegner\",\n  fileTitle: \"Filnavn\",\n  colorPicker: \"Fargevelger\",\n  canvasColors: \"Brukes p\\xE5 lerretet\",\n  canvasBackground: \"Lerretsbakgrunn\",\n  drawingCanvas: \"Lerret\",\n  layers: \"Lag\",\n  actions: \"Handlinger\",\n  language: \"Spr\\xE5k\",\n  liveCollaboration: \"Sanntids-samarbeid...\",\n  duplicateSelection: \"Dupliser\",\n  untitled: \"Uten navn\",\n  name: \"Navn\",\n  yourName: \"Ditt navn\",\n  madeWithExcalidraw: \"Laget med Excalidraw\",\n  group: \"Grupp\\xE9r utvalg\",\n  ungroup: \"Avgrupp\\xE9r utvalg\",\n  collaborators: \"Samarbeidspartnere\",\n  showGrid: \"Vis rutenett\",\n  addToLibrary: \"Legg til i bibliotek\",\n  removeFromLibrary: \"Fjern fra bibliotek\",\n  libraryLoadingMessage: \"Laster bibliotek\\u2026\",\n  libraries: \"Bla gjennom biblioteker\",\n  loadingScene: \"Laster inn scene\\u2026\",\n  align: \"Juster\",\n  alignTop: \"Juster \\xF8verst\",\n  alignBottom: \"Juster nederst\",\n  alignLeft: \"Juster venstre\",\n  alignRight: \"Juster h\\xF8yre\",\n  centerVertically: \"Midtstill vertikalt\",\n  centerHorizontally: \"Midtstill horisontalt\",\n  distributeHorizontally: \"Distribuer horisontalt\",\n  distributeVertically: \"Distribuer vertikalt\",\n  flipHorizontal: \"Snu horisontalt\",\n  flipVertical: \"Snu vertikalt\",\n  viewMode: \"Visningsmodus\",\n  share: \"Del\",\n  showStroke: \"Vis fargevelger for kantfarge\",\n  showBackground: \"Vis fargevelger for bakgrunnsfarge\",\n  toggleTheme: \"Veksle tema\",\n  personalLib: \"Personlig bibliotek\",\n  excalidrawLib: \"Excalidraw-bibliotek\",\n  decreaseFontSize: \"Reduser skriftst\\xF8rrelse\",\n  increaseFontSize: \"\\xD8k skriftst\\xF8rrelse\",\n  unbindText: \"Avbind tekst\",\n  bindText: \"Bind tekst til beholderen\",\n  createContainerFromText: \"La tekst flyte i en beholder\",\n  link: {\n    edit: \"Rediger lenke\",\n    editEmbed: \"Rediger lenke og bygg inn\",\n    create: \"Opprett lenke\",\n    createEmbed: \"Opprett lenke og bygg inn\",\n    label: \"Lenke\",\n    labelEmbed: \"Lenk & bygg inn\",\n    empty: \"Ingen lenke er valgt\"\n  },\n  lineEditor: {\n    edit: \"Rediger linje\",\n    exit: \"Avslutt linjeredigering\"\n  },\n  elementLock: {\n    lock: \"L\\xE5s\",\n    unlock: \"L\\xE5s opp\",\n    lockAll: \"L\\xE5s alle\",\n    unlockAll: \"L\\xE5s opp alle\"\n  },\n  statusPublished: \"Publisert\",\n  sidebarLock: \"Holde sidemenyen \\xE5pen\",\n  selectAllElementsInFrame: \"Velg alle elementene i rammen\",\n  removeAllElementsFromFrame: \"Fjern alle elementer fra rammen\",\n  eyeDropper: \"Velg farge fra lerretet\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Ingen elementer lagt til enn\\xE5...\",\n  hint_emptyLibrary: \"Velg et objekt p\\xE5 lerretet for \\xE5 legge det til her, eller installer et bibliotek fra den offentlige samlingen under.\",\n  hint_emptyPrivateLibrary: \"Velg et objekt p\\xE5 lerretet for \\xE5 legge det til her.\"\n};\nvar buttons = {\n  clearReset: \"T\\xF8m lerretet og tilbakestill bakgrunnsfargen\",\n  exportJSON: \"Eksporter til fil\",\n  exportImage: \"Eksporter bilde...\",\n  export: \"Lagre som...\",\n  copyToClipboard: \"Kopier til utklippstavle\",\n  save: \"Lagre til aktiv fil\",\n  saveAs: \"Lagre som\",\n  load: \"\\xC5pne\",\n  getShareableLink: \"F\\xE5 delingslenke\",\n  close: \"Lukk\",\n  selectLanguage: \"Velg spr\\xE5k\",\n  scrollBackToContent: \"Skroll tilbake til innhold\",\n  zoomIn: \"Zoom inn\",\n  zoomOut: \"Zoom ut\",\n  resetZoom: \"Nullstill zoom\",\n  menu: \"Meny\",\n  done: \"Ferdig\",\n  edit: \"Rediger\",\n  undo: \"Angre\",\n  redo: \"Gj\\xF8r om\",\n  resetLibrary: \"Nullstill bibliotek\",\n  createNewRoom: \"Opprett et nytt rom\",\n  fullScreen: \"Fullskjerm\",\n  darkMode: \"M\\xF8rk modus\",\n  lightMode: \"Lys modus\",\n  zenMode: \"Zen-modus\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Avslutt zen-modus\",\n  cancel: \"Avbryt\",\n  clear: \"T\\xF8m\",\n  remove: \"Fjern\",\n  embed: \"Sl\\xE5 av/p\\xE5 innebygging\",\n  publishLibrary: \"Publiser\",\n  submit: \"Send inn\",\n  confirm: \"Bekreft\",\n  embeddableInteractionButton: \"Klikk for \\xE5 samhandle\"\n};\nvar alerts = {\n  clearReset: \"Dette vil t\\xF8mme lerretet. Er du sikker?\",\n  couldNotCreateShareableLink: \"Kunne ikke lage delbar lenke.\",\n  couldNotCreateShareableLinkTooBig: \"Kunne ikke opprette lenke til deling: scenen er for stor\",\n  couldNotLoadInvalidFile: \"Kunne ikke laste inn ugyldig fil\",\n  importBackendFailed: \"Importering av backend feilet.\",\n  cannotExportEmptyCanvas: \"Kan ikke eksportere et tomt lerret.\",\n  couldNotCopyToClipboard: \"Kunne ikke kopiere til utklippstavlen.\",\n  decryptFailed: \"Kunne ikke dekryptere data.\",\n  uploadedSecurly: \"Opplastingen er kryptert og kan ikke leses av Excalidraw-serveren eller tredjeparter.\",\n  loadSceneOverridePrompt: \"\\xC5 laste inn ekstern tegning vil erstatte det eksisterende innholdet. \\xD8nsker du \\xE5 fortsette?\",\n  collabStopOverridePrompt: \"Hvis du slutter \\xF8kten, overskrives din forrige, lokalt lagrede tegning. Er du sikker?\\n\\n(Hvis du \\xF8nsker \\xE5 beholde din lokale tegning, bare lukk nettleserfanen i stedet.)\",\n  errorAddingToLibrary: \"Kunne ikke legge element i biblioteket\",\n  errorRemovingFromLibrary: \"Kunne ikke fjerne element fra biblioteket\",\n  confirmAddLibrary: \"Dette vil legge til {{numShapes}} figur(er) i biblioteket ditt. Er du sikker?\",\n  imageDoesNotContainScene: \"Det ser ikke ut til at dette bildet inneholder noen scenedata. Har du aktivert innebygging av scene under eksporten?\",\n  cannotRestoreFromImage: \"Scenen kunne ikke gjenopprettes fra denne bildefilen\",\n  invalidSceneUrl: \"Kunne ikke importere scene fra den oppgitte URL-en. Den er enten \\xF8delagt, eller inneholder ikke gyldig Excalidraw JSON-data.\",\n  resetLibrary: \"Dette vil t\\xF8mme biblioteket ditt. Er du sikker?\",\n  removeItemsFromsLibrary: \"Slett {{count}} element(er) fra biblioteket?\",\n  invalidEncryptionKey: \"Krypteringsn\\xF8kkel m\\xE5 ha 22 tegn. Live-samarbeid er deaktivert.\",\n  collabOfflineWarning: \"Ingen Internett-tilkobling tilgjengelig.\\nEndringer dine vil ikke bli lagret!\"\n};\nvar errors = {\n  unsupportedFileType: \"Filtypen st\\xF8ttes ikke.\",\n  imageInsertError: \"Kunne ikke sette inn bildet. Pr\\xF8v igjen senere...\",\n  fileTooBig: \"Filen er for stor. Maksimal tillatt st\\xF8rrelse er {{maxSize}}.\",\n  svgImageInsertError: \"Kunne ikke sette inn SVG-bilde. SVG-koden ser ugyldig ut.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Ugyldig SVG.\",\n  cannotResolveCollabServer: \"Kunne ikke koble til samarbeidsserveren. Vennligst oppdater siden og pr\\xF8v p\\xE5 nytt.\",\n  importLibraryError: \"Kunne ikke laste bibliotek\",\n  collabSaveFailed: \"Kan ikke lagre i backend-databasen. Hvis problemer vedvarer, b\\xF8r du lagre filen lokalt for \\xE5 sikre at du ikke mister arbeidet.\",\n  collabSaveFailed_sizeExceeded: \"Kunne ikke lagre til backend-databasen, lerretet ser ut til \\xE5 v\\xE6re for stort. Du b\\xF8r lagre filen lokalt for \\xE5 sikre at du ikke mister arbeidet ditt.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"Ser ut som om du bruker Brave nettleser med <bold>Aggressivt Block Finger</bold> -innstillingen aktivert.\",\n    line2: \"Dette kan resultere i \\xE5 bryte <bold>tekst-elementene</bold> i tegningene.\",\n    line3: \"Vi anbefaler p\\xE5 det sterkeste \\xE5 deaktivere denne innstillingen. Du kan f\\xF8lge <link>disse trinnene</link> om hvordan du gj\\xF8r det.\",\n    line4: \"Hvis deaktivering av denne innstillingen ikke fikser visningen av tekstelementer, vennligst \\xE5pne en <issueLink>sak</issueLink> p\\xE5 v\\xE5r GitHub, eller skriv oss p\\xE5 <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Innebygde elementer kan ikke legges til i biblioteket.\",\n    iframe: \"\",\n    image: \"St\\xF8tte for \\xE5 legge til bilder i biblioteket kommer snart!\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Velg\",\n  image: \"Sett inn bilde\",\n  rectangle: \"Rektangel\",\n  diamond: \"Diamant\",\n  ellipse: \"Ellipse\",\n  arrow: \"Pil\",\n  line: \"Linje\",\n  freedraw: \"Tegn\",\n  text: \"Tekst\",\n  library: \"Bibliotek\",\n  lock: \"Behold merket verkt\\xF8y som aktivt\",\n  penMode: \"Pennemodus - forhindre ber\\xF8ring\",\n  link: \"Legg til / oppdater link for en valgt figur\",\n  eraser: \"Viskel\\xE6r\",\n  frame: \"Rammeverkt\\xF8y\",\n  magicframe: \"\",\n  embeddable: \"Nettinnbygging\",\n  laser: \"\",\n  hand: \"H\\xE5nd (panoreringsverkt\\xF8y)\",\n  extraTools: \"Flere verkt\\xF8y\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Handlinger: lerret\",\n  selectedShapeActions: \"Handlinger: valgt objekt\",\n  shapes: \"Former\"\n};\nvar hints = {\n  canvasPanning: \"For \\xE5 flytte lerretet, hold musehjulet eller mellomromstasten mens du drar, eller bruk h\\xE5nd-verkt\\xF8yet\",\n  linearElement: \"Klikk for \\xE5 starte linje med flere punkter, eller dra for en enkel linje\",\n  freeDraw: \"Klikk og dra, slipp n\\xE5r du er ferdig\",\n  text: \"Tips: du kan ogs\\xE5 legge til tekst ved \\xE5 dobbeltklikke hvor som helst med utvalgsverkt\\xF8yet\",\n  embeddable: \"Klikk og dra for \\xE5 opprette en nettside innebygd\",\n  text_selected: \"Dobbeltklikk eller trykk ENTER for \\xE5 redigere tekst\",\n  text_editing: \"Trykk Escape eller Ctrl/Cmd+Enter for \\xE5 fullf\\xF8re redigering\",\n  linearElementMulti: \"Klikk p\\xE5 siste punkt eller trykk Escape eller Enter for \\xE5 fullf\\xF8re\",\n  lockAngle: \"Du kan l\\xE5se vinkelen ved \\xE5 holde nede SHIFT\",\n  resize: \"Du kan beholde forholdet ved \\xE5 trykke SHIFT mens du endrer st\\xF8rrelse,\\ntrykk ALT for \\xE5 endre st\\xF8rrelsen fra midten\",\n  resizeImage: \"Du kan endre st\\xF8rrelse fritt ved \\xE5 holde SHIFT,\\nhold ALT for \\xE5 endre st\\xF8rrelse fra midten\",\n  rotate: \"Du kan l\\xE5se vinklene ved \\xE5 holde SHIFT mens du roterer\",\n  lineEditor_info: \"Hold Ctrl/Cmd og dobbelklikk eller trykk Ctrl/Cmd + Enter for \\xE5 endre punkter\",\n  lineEditor_pointSelected: \"Trykk p\\xE5 Slett for \\xE5 fjerne punktet, Ctrl / Cmd+D for \\xE5 duplisere, eller dra for \\xE5 flytte\",\n  lineEditor_nothingSelected: \"Velg et punkt \\xE5 redigere (hold SHIFT for \\xE5 velge flere),\\neller hold Alt og klikk for \\xE5 legge til nye punkter\",\n  placeImage: \"Klikk for \\xE5 plassere bildet, eller klikk og dra for \\xE5 angi st\\xF8rrelsen manuelt\",\n  publishLibrary: \"Publiser ditt eget bibliotek\",\n  bindTextToElement: \"Trykk Enter for \\xE5 legge til tekst\",\n  deepBoxSelect: \"Hold CTRL/CMD for \\xE5 markere dypt og forhindre flytting\",\n  eraserRevert: \"Hold Alt for \\xE5 reversere elementene merket for sletting\",\n  firefox_clipboard_write: 'Denne funksjonen kan sannsynligvis aktiveres ved \\xE5 sette \"dom.events.asyncClipboard.clipboardItem\" flagget til \"true\". For \\xE5 endre nettleserens flagg i Firefox, bes\\xF8k \"about:config\"-siden.',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Kan ikke vise forh\\xE5ndsvisning\",\n  canvasTooBig: \"Lerretet kan v\\xE6re for stort.\",\n  canvasTooBigTip: \"Tips: Pr\\xF8v \\xE5 flytte de ytterste elementene litt tettere sammen.\"\n};\nvar errorSplash = {\n  headingMain: \"En feil oppsto. Pr\\xF8v <button>\\xE5 laste siden p\\xE5 nytt.</button>\",\n  clearCanvasMessage: \"Om ny sidelasting ikke fungerer, pr\\xF8v <button>\\xE5 t\\xF8mme lerretet.</button>\",\n  clearCanvasCaveat: \" Dette vil f\\xF8re til tap av arbeid \",\n  trackedToSentry: \"Feilen med identifikator {{eventId}} ble logget i v\\xE5rt system.\",\n  openIssueMessage: \"Vi er veldig n\\xF8ye med \\xE5 ikke inkludere dine scene-opplysninger i feilen. Hvis din scene ikke er privat, vurder \\xE5 f\\xF8lge opp i v\\xE5rt <button>feilrapporteringssystem.</button> Ta med opplysningene nedenfor ved \\xE5 kopiere og lime inn i GitHub-saken.\",\n  sceneContent: \"Scene-innhold:\"\n};\nvar roomDialog = {\n  desc_intro: \"Du kan invitere personer til scenen din for \\xE5 samarbeide med deg.\",\n  desc_privacy: \"Ta det med ro, sesjonen bruker ende-til-ende-kryptering, s\\xE5 alt du tegner forblir privat. Ikke en gang serveren v\\xE5r kan se hva du lager.\",\n  button_startSession: \"Start \\xF8kt\",\n  button_stopSession: \"Stopp sesjon\",\n  desc_inProgressIntro: \"Sanntids-samarbeids\\xF8kt er n\\xE5 i gang.\",\n  desc_shareLink: \"Del denne linken med de du vil samarbeide med:\",\n  desc_exitSession: \"Dersom du avslutter sesjonen blir du frakoblet rommet, men du kan fortsette \\xE5 arbeide med scenen lokalt. V\\xE6r oppmerksom p\\xE5 at dette ikke vil p\\xE5virke andre personer, og de vil fortsatt ha mulighet til \\xE5 samarbeide p\\xE5 deres versjon.\",\n  shareTitle: \"Bli med i en live samarbeids\\xF8kt p\\xE5 Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Feil\"\n};\nvar exportDialog = {\n  disk_title: \"Lagre til disk\",\n  disk_details: \"Eksporter scene-dataene til en fil som du kan importere fra senere.\",\n  disk_button: \"Lagre til fil\",\n  link_title: \"Delbar lenke\",\n  link_details: \"Eksporter som en skrivebeskyttet lenke.\",\n  link_button: \"Eksporter til lenke\",\n  excalidrawplus_description: \"Lagre scenen til ditt Excalidraw+ arbeidsomr\\xE5de.\",\n  excalidrawplus_button: \"Eksporter\",\n  excalidrawplus_exportError: \"Kunne ikke eksportere til Excalidraw+ for \\xF8yeblikket...\"\n};\nvar helpDialog = {\n  blog: \"Les bloggen v\\xE5r\",\n  click: \"klikk\",\n  deepSelect: \"Marker dypt\",\n  deepBoxSelect: \"Marker dypt innad i boks og forhindre flytting\",\n  curvedArrow: \"Buet pil\",\n  curvedLine: \"Buet linje\",\n  documentation: \"Dokumentasjon\",\n  doubleClick: \"dobbeltklikk\",\n  drag: \"dra\",\n  editor: \"Redigeringsvisning\",\n  editLineArrowPoints: \"Rediger linje/pilpunkter\",\n  editText: \"Rediger tekst / legg til etikett\",\n  github: \"Funnet et problem? Send inn\",\n  howto: \"F\\xF8lg v\\xE5re veiledninger\",\n  or: \"eller\",\n  preventBinding: \"Forhindre pilbinding\",\n  tools: \"Verkt\\xF8y\",\n  shortcuts: \"Tastatursnarveier\",\n  textFinish: \"Fullf\\xF8r redigering (teksteditor)\",\n  textNewLine: \"Legg til ny linje (teksteditor)\",\n  title: \"Hjelp\",\n  view: \"Vis\",\n  zoomToFit: \"Zoom for \\xE5 se alle elementer\",\n  zoomToSelection: \"Zoom til utvalg\",\n  toggleElementLock: \"L\\xE5s/l\\xE5s opp utvalg\",\n  movePageUpDown: \"Flytt side opp/ned\",\n  movePageLeftRight: \"Flytt siden til venstre/h\\xF8yre\"\n};\nvar clearCanvasDialog = {\n  title: \"T\\xF8m lerret\"\n};\nvar publishDialog = {\n  title: \"Publiser bibliotek\",\n  itemName: \"Elementnavn\",\n  authorName: \"Forfatterens navn\",\n  githubUsername: \"GitHub-brukernavnet\",\n  twitterUsername: \"Twitter-brukernavn\",\n  libraryName: \"Biblioteknavn\",\n  libraryDesc: \"Beskrivelse av bibliotek\",\n  website: \"Nettsted\",\n  placeholder: {\n    authorName: \"Ditt navn eller brukernavn\",\n    libraryName: \"Navnet p\\xE5 biblioteket ditt\",\n    libraryDesc: \"Beskrivelse av biblioteket ditt for \\xE5 hjelpe folk med \\xE5 forst\\xE5 bruken\",\n    githubHandle: \"Github-brukernavn (valgfritt), slik at du kan redigere biblioteket n\\xE5r du har sendt inn for gjennomgang\",\n    twitterHandle: \"Twitter-brukernavn (valgfritt), slik at vi vet hvem vi skal kreditere n\\xE5r promotert p\\xE5 Twitter\",\n    website: \"Lenke til din personlige nettside eller et annet sted (valgfritt)\"\n  },\n  errors: {\n    required: \"P\\xE5krevd\",\n    website: \"Angi en gyldig nettadresse\"\n  },\n  noteDescription: \"Send inn biblioteket ditt som skal inkluderes i <link>kildekode for offentlig bibliotek</link>for andre \\xE5 bruke dem i tegninger.\",\n  noteGuidelines: \"Biblioteket m\\xE5 godkjennes manuelt f\\xF8rst. Les <link>retningslinjene</link> f\\xF8r innsending. Du vil trenge en GitHub-konto for \\xE5 kommunisere og gj\\xF8re endringer hvis \\xF8nsket, men det er ikke p\\xE5krevd.\",\n  noteLicense: \"Ved \\xE5 sende inn godtar du at biblioteket blir publisert under <link>MIT-lisens, </link>som kortfattet betyr at andre kan bruke dem uten begrensninger.\",\n  noteItems: \"Hvert bibliotek m\\xE5 ha sitt eget navn, s\\xE5 det er filtrerbart. F\\xF8lgende bibliotekselementer vil bli inkludert:\",\n  atleastOneLibItem: \"Vennligst velg minst ett bibliotek for \\xE5 komme i gang\",\n  republishWarning: \"Merk: noen av de valgte elementene er merket som allerede publisert/sendt. Du b\\xF8r kun sende inn elementer p\\xE5 nytt n\\xE5r du oppdaterer et eksisterende bibliotek eller innlevering.\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliotek innsendt\",\n  content: \"Takk {{authorName}}. Ditt bibliotek har blitt sendt inn for gjennomgang. Du kan spore statusen<link>her</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Nullstill bibliotek\",\n  removeItemsFromLib: \"Fjern valgte elementer fra bibliotek\"\n};\nvar imageExportDialog = {\n  header: \"Eksporter bilde\",\n  label: {\n    withBackground: \"Bakgrunn\",\n    onlySelected: \"Kun valgte\",\n    darkMode: \"M\\xF8rk modus\",\n    embedScene: \"Bygg inn scene\",\n    scale: \"Skalering\",\n    padding: \"Avstand\"\n  },\n  tooltip: {\n    embedScene: \"Scenedata vil bli lagret i den eksporterte PNG/SVG-filen, slik at scenen kan gjenopprettes fra den.\\nDet vil \\xF8ke den eksporterte filst\\xF8rrelsen.\"\n  },\n  title: {\n    exportToPng: \"Eksporter til PNG\",\n    exportToSvg: \"Eksporter til SVG\",\n    copyPngToClipboard: \"Kopier PNG til utklippstavlen\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Kopier til utklippstavle\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Dine tegninger er ende-til-ende-krypterte slik at Excalidraw sine servere aldri vil se dem.\",\n  link: \"Blogginnlegg om ende-til-ende-kryptering i Excalidraw\"\n};\nvar stats = {\n  angle: \"Vinkel\",\n  element: \"Element\",\n  elements: \"Elementer\",\n  height: \"H\\xF8yde\",\n  scene: \"Scene\",\n  selected: \"Valgt\",\n  storage: \"Lagring\",\n  title: \"Statistikk for nerder\",\n  total: \"Totalt\",\n  version: \"Versjon\",\n  versionCopy: \"Klikk for \\xE5 kopiere\",\n  versionNotAvailable: \"Versjon ikke tilgjengelig\",\n  width: \"Bredde\"\n};\nvar toast = {\n  addedToLibrary: \"Lagt til i biblioteket\",\n  copyStyles: \"Kopierte stiler.\",\n  copyToClipboard: \"Kopiert til utklippstavlen.\",\n  copyToClipboardAsPng: \"Kopierte {{exportSelection}} til utklippstavlen som PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Fil lagret.\",\n  fileSavedToFilename: \"Lagret til {filename}\",\n  canvas: \"lerret\",\n  selection: \"utvalg\",\n  pasteAsSingleElement: \"Bruk {{shortcut}} for \\xE5 lime inn som ett enkelt element,\\neller lim inn i en eksisterende tekstbehandler\",\n  unableToEmbed: \"Innbygging av denne nettadressen er ikke tillatt. Oppret en sak p\\xE5 GitHub for \\xE5 be om url-hvitelisting\",\n  unrecognizedLinkFormat: 'Linken du bygget inn samsvarer ikke med det forventede formatet. Pr\\xF8v \\xE5 lime inn \"bygg inn\"-strengen fra kildesiden'\n};\nvar colors = {\n  transparent: \"Gjennomsiktig\",\n  black: \"Svart\",\n  white: \"Hvit\",\n  red: \"R\\xF8d\",\n  pink: \"Rosa\",\n  grape: \"Drue\",\n  violet: \"Fiolett\",\n  gray: \"Gr\\xE5\",\n  blue: \"Bl\\xE5\",\n  cyan: \"Turkis\",\n  teal: \"Bl\\xE5gr\\xF8nn\",\n  green: \"Gr\\xF8nn\",\n  yellow: \"Gul\",\n  orange: \"Oransje\",\n  bronze: \"Bronse\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Alle dine data lagres lokalt i din nettleser.\",\n    center_heading_plus: \"\\xD8nsker du \\xE5 g\\xE5 til Excalidraw+ i stedet?\",\n    menuHint: \"Eksporter, innstillinger, spr\\xE5k, ...\"\n  },\n  defaults: {\n    menuHint: \"Eksporter, innstillinger og mer...\",\n    center_heading: \"Diagrammer. Gjort. Enkelt.\",\n    toolbarHint: \"Velg et verkt\\xF8y og start \\xE5 tegne!\",\n    helpHint: \"Snarveier & hjelp\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Mest brukte egendefinerte farger\",\n  colors: \"Farger\",\n  shades: \"Toner\",\n  hexCode: \"Heksadesimal kode\",\n  noShades: \"Ingen toner tilgjengelig for denne fargen\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Eksporter som bilde\",\n      button: \"Eksporter som bilde\",\n      description: \"Eksporter scene-dataene til en fil som du kan importere fra senere.\"\n    },\n    saveToDisk: {\n      title: \"Lagre til disk\",\n      button: \"Lagre til disk\",\n      description: \"Eksporter scene-dataene til en fil som du kan importere fra senere.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Eksporter til Excalidraw+\",\n      description: \"Lagre scenen til ditt Excalidraw+-arbeidsomr\\xE5de.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Last inn fra fil\",\n      button: \"Last inn fra fil\",\n      description: \"\\xC5 laste fra en fil vil <bold>erstatte ditt eksisterende innhold</bold>.<br></br>Du kan sikkerhetskopiere tegningen din f\\xF8rst ved \\xE5 bruke en av valgene under.\"\n    },\n    shareableLink: {\n      title: \"Last inn fra lenke\",\n      button: \"Erstatt innholdet mitt\",\n      description: \"Lasting av ekstern tegning vil <bold>erstatte ditt eksisterende innhold</bold>.<br></br>Du kan sikkerhetskopiere tegningen din f\\xF8rst ved \\xE5 bruke en av valgene nedenfor.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar nb_NO_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=nb-NO-BMB73KRH.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/nb-NO-BMB73KRH.js\n"));

/***/ })

}]);