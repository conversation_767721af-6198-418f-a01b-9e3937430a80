"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_nl-NL-F2257BLQ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/nl-NL-F2257BLQ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/nl-NL-F2257BLQ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ nl_NL_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/nl-NL.json\nvar labels = {\n  paste: \"Plakken\",\n  pasteAsPlaintext: \"Plakken als platte tekst\",\n  pasteCharts: \"Plak grafieken\",\n  selectAll: \"Alles selecteren\",\n  multiSelect: \"Voeg element toe aan selectie\",\n  moveCanvas: \"Canvas verplaatsen\",\n  cut: \"Knip\",\n  copy: \"Kopi\\xEBren\",\n  copyAsPng: \"Kopieer als PNG\",\n  copyAsSvg: \"Kopieer naar klembord als SVG\",\n  copyText: \"Kopieer naar klembord als tekst\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Breng naar voren\",\n  sendToBack: \"Stuur naar achtergrond\",\n  bringToFront: \"Breng naar voorgrond\",\n  sendBackward: \"Breng naar achter\",\n  delete: \"Verwijderen\",\n  copyStyles: \"Kopieer opmaak\",\n  pasteStyles: \"Plak opmaak\",\n  stroke: \"Lijn\",\n  background: \"Achtergrond\",\n  fill: \"Invulling\",\n  strokeWidth: \"Lijnbreedte\",\n  strokeStyle: \"Lijnstijl\",\n  strokeStyle_solid: \"Ononderbroken\",\n  strokeStyle_dashed: \"Gestreept\",\n  strokeStyle_dotted: \"Gestippeld\",\n  sloppiness: \"Slordigheid\",\n  opacity: \"Doorzichtigheid\",\n  textAlign: \"Uitlijning\",\n  edges: \"Randen\",\n  sharp: \"Hoekig\",\n  round: \"Rond\",\n  arrowheads: \"Pijlpunten\",\n  arrowhead_none: \"Geen\",\n  arrowhead_arrow: \"Pijl\",\n  arrowhead_bar: \"Balk\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Driehoek\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Tekstgrootte\",\n  fontFamily: \"Lettertype\",\n  addWatermark: 'Voeg \"Gemaakt met Excalidraw\" toe',\n  handDrawn: \"Handgetekend\",\n  normal: \"Normaal\",\n  code: \"Code\",\n  small: \"Klein\",\n  medium: \"Medium\",\n  large: \"Groot\",\n  veryLarge: \"Zeer groot\",\n  solid: \"Ingekleurd\",\n  hachure: \"Arcering\",\n  zigzag: \"\",\n  crossHatch: \"Tweemaal gearceerd\",\n  thin: \"Dun\",\n  bold: \"Vet\",\n  left: \"Links\",\n  center: \"Midden\",\n  right: \"Rechts\",\n  extraBold: \"Zwaar\",\n  architect: \"Architect\",\n  artist: \"Artiest\",\n  cartoonist: \"Cartoonist\",\n  fileTitle: \"Bestandsnaam\",\n  colorPicker: \"Kleurenkiezer\",\n  canvasColors: \"Gebruikt op canvas\",\n  canvasBackground: \"Canvas achtergrond\",\n  drawingCanvas: \"Canvas\",\n  layers: \"Lagen\",\n  actions: \"Acties\",\n  language: \"Taal\",\n  liveCollaboration: \"Live Samenwerking...\",\n  duplicateSelection: \"Dupliceer\",\n  untitled: \"Naamloos\",\n  name: \"Naam\",\n  yourName: \"Jouw naam\",\n  madeWithExcalidraw: \"Gemaakt met Excalidraw\",\n  group: \"Groeperen\",\n  ungroup: \"Groep opheffen\",\n  collaborators: \"Deelnemers\",\n  showGrid: \"Raster weergeven\",\n  addToLibrary: \"Voeg toe aan bibliotheek\",\n  removeFromLibrary: \"Verwijder uit bibliotheek\",\n  libraryLoadingMessage: \"Bibliotheek laden\\u2026\",\n  libraries: \"Blader door bibliotheken\",\n  loadingScene: \"Sc\\xE8ne laden\\u2026\",\n  align: \"Uitlijnen\",\n  alignTop: \"Boven uitlijnen\",\n  alignBottom: \"Onder uitlijnen\",\n  alignLeft: \"Links uitlijnen\",\n  alignRight: \"Rechts uitlijnen\",\n  centerVertically: \"Verticaal Centreren\",\n  centerHorizontally: \"Horizontaal Centreren\",\n  distributeHorizontally: \"Horizontaal verspreiden\",\n  distributeVertically: \"Verticaal distribueren\",\n  flipHorizontal: \"Horizontaal spiegelen\",\n  flipVertical: \"Verticaal spiegelen\",\n  viewMode: \"Weergavemodus\",\n  share: \"Deel\",\n  showStroke: \"Toon lijn kleur kiezer\",\n  showBackground: \"Toon achtergrondkleur kiezer\",\n  toggleTheme: \"Thema aan/uit\",\n  personalLib: \"Persoonlijke bibliotheek\",\n  excalidrawLib: \"Excalidraw bibliotheek\",\n  decreaseFontSize: \"Letters verkleinen\",\n  increaseFontSize: \"Letters vergroten\",\n  unbindText: \"Ontkoppel tekst\",\n  bindText: \"Koppel tekst aan de container\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"Wijzig link\",\n    editEmbed: \"Link bewerken & insluiten\",\n    create: \"Maak link\",\n    createEmbed: \"Link maken en insluiten\",\n    label: \"Link\",\n    labelEmbed: \"Link toevoegen & insluiten\",\n    empty: \"Er is geen link ingesteld\"\n  },\n  lineEditor: {\n    edit: \"Bewerk regel\",\n    exit: \"Verlaat regel-editor\"\n  },\n  elementLock: {\n    lock: \"Vergrendel\",\n    unlock: \"Ontgrendel\",\n    lockAll: \"Vergrendel alles\",\n    unlockAll: \"Ontgrendel alles\"\n  },\n  statusPublished: \"Gepubliceerd\",\n  sidebarLock: \"Zijbalk open houden\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Nog geen items toegevoegd...\",\n  hint_emptyLibrary: \"Selecteer een item op het canvas om het hier toe te voegen of installeer een bibliotheek uit de openbare repository, hieronder.\",\n  hint_emptyPrivateLibrary: \"Selecteer een item op het canvas om het hier toe te voegen.\"\n};\nvar buttons = {\n  clearReset: \"Canvas opnieuw instellen\",\n  exportJSON: \"Exporteren naar bestand\",\n  exportImage: \"Exporteer afbeelding...\",\n  export: \"Sla op...\",\n  copyToClipboard: \"Kopieer\",\n  save: \"Opslaan naar huidige bestand\",\n  saveAs: \"Opslaan als\",\n  load: \"Open\",\n  getShareableLink: \"Maak een deelbare link\",\n  close: \"Sluiten\",\n  selectLanguage: \"Taal selecteren\",\n  scrollBackToContent: \"Scroll terug naar inhoud\",\n  zoomIn: \"Inzoomen\",\n  zoomOut: \"Uitzoomen\",\n  resetZoom: \"Zoom terugzetten\",\n  menu: \"Menu\",\n  done: \"Klaar\",\n  edit: \"Bewerken\",\n  undo: \"Ongedaan maken\",\n  redo: \"Herstel ongedaan maken\",\n  resetLibrary: \"Bibliotheek Resetten\",\n  createNewRoom: \"Cre\\xEBer live-samenwerkingssessie\",\n  fullScreen: \"Volledig scherm\",\n  darkMode: \"Donkere modus\",\n  lightMode: \"Lichte modus\",\n  zenMode: \"Zen modus\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Verlaat zen modus\",\n  cancel: \"Annuleren\",\n  clear: \"Wissen\",\n  remove: \"Verwijderen\",\n  embed: \"Insluiten in-/uitschakelen\",\n  publishLibrary: \"Publiceren\",\n  submit: \"Versturen\",\n  confirm: \"Bevestigen\",\n  embeddableInteractionButton: \"Klik voor interactie\"\n};\nvar alerts = {\n  clearReset: \"Dit zal het hele canvas verwijderen. Weet je het zeker?\",\n  couldNotCreateShareableLink: \"Kon geen deelbare link aanmaken.\",\n  couldNotCreateShareableLinkTooBig: \"Kan geen deelbare link aanmaken: de sc\\xE8ne is te groot\",\n  couldNotLoadInvalidFile: \"Kan ongeldig bestand niet laden\",\n  importBackendFailed: \"Importeren vanuit backend mislukt.\",\n  cannotExportEmptyCanvas: \"Kan geen leeg canvas exporteren.\",\n  couldNotCopyToClipboard: \"Kon niet naar klembord kopi\\xEBren.\",\n  decryptFailed: \"Kan gegevens niet decoderen.\",\n  uploadedSecurly: \"De upload is beveiligd met end-to-end encryptie, wat betekent dat de Excalidraw server en derden de inhoud niet kunnen lezen.\",\n  loadSceneOverridePrompt: \"Het inladen van een externe tekening zal je bestaande inhoud vervangen. Wil je verdergaan?\",\n  collabStopOverridePrompt: \"Wanneer de sessie wordt gestopt, overschrijft u de eerdere, lokaal opgeslagen tekening. Weet je het zeker?\\n\\n(Als je de lokale tekening wilt behouden, sluit je in plaats daarvan het browsertabblad)\",\n  errorAddingToLibrary: \"Kan item niet toevoegen aan de bibliotheek\",\n  errorRemovingFromLibrary: \"Kan item niet uit de bibliotheek verwijderen\",\n  confirmAddLibrary: \"Dit zal {{numShapes}} vorm(en) toevoegen aan je bibliotheek. Weet je het zeker?\",\n  imageDoesNotContainScene: \"Deze afbeelding lijkt geen sc\\xE8ne gegevens te bevatten. Heb je sc\\xE8ne embedding tijdens het exporteren ingeschakeld?\",\n  cannotRestoreFromImage: \"Sc\\xE8ne kan niet worden hersteld vanuit dit afbeeldingsbestand\",\n  invalidSceneUrl: \"Kan sc\\xE8ne niet importeren vanuit de opgegeven URL. Het is onjuist of bevat geen geldige Excalidraw JSON-gegevens.\",\n  resetLibrary: \"Dit zal je bibliotheek wissen. Weet je het zeker?\",\n  removeItemsFromsLibrary: \"Verwijder {{count}} item(s) uit bibliotheek?\",\n  invalidEncryptionKey: \"Encryptiesleutel moet 22 tekens zijn. Live samenwerking is uitgeschakeld.\",\n  collabOfflineWarning: \"Geen internetverbinding beschikbaar.\\nJe wijzigingen worden niet opgeslagen!\"\n};\nvar errors = {\n  unsupportedFileType: \"Niet-ondersteund bestandstype.\",\n  imageInsertError: \"Afbeelding invoegen mislukt. Probeer het later opnieuw...\",\n  fileTooBig: \"Bestand is te groot. Maximale grootte is {{maxSize}}.\",\n  svgImageInsertError: \"Kon geen SVG-afbeelding invoegen. De SVG-opmaak ziet er niet geldig uit.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Ongeldige SVG.\",\n  cannotResolveCollabServer: \"Kan geen verbinding maken met de collab server. Herlaad de pagina en probeer het opnieuw.\",\n  importLibraryError: \"Kon bibliotheek niet laden\",\n  collabSaveFailed: \"Kan niet opslaan in de backend database. Als de problemen blijven bestaan, moet u het bestand lokaal opslaan om ervoor te zorgen dat u uw werk niet verliest.\",\n  collabSaveFailed_sizeExceeded: \"Kan de backend database niet opslaan, het canvas lijkt te groot te zijn. U moet het bestand lokaal opslaan om ervoor te zorgen dat u uw werk niet verliest.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Ingesloten elementen kunnen niet worden toegevoegd aan de bibliotheek.\",\n    iframe: \"\",\n    image: \"Ondersteuning voor het toevoegen van afbeeldingen aan de bibliotheek komt binnenkort!\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Selectie\",\n  image: \"Voeg afbeelding in\",\n  rectangle: \"Rechthoek\",\n  diamond: \"Ruit\",\n  ellipse: \"Ovaal\",\n  arrow: \"Pijl\",\n  line: \"Lijn\",\n  freedraw: \"Teken\",\n  text: \"Tekst\",\n  library: \"Bibliotheek\",\n  lock: \"Geselecteerde tool actief houden na tekenen\",\n  penMode: \"Pen modus - Blokkeer aanraken\",\n  link: \"Link toevoegen / bijwerken voor een geselecteerde vorm\",\n  eraser: \"Gum\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"Web insluiten\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Canvasacties\",\n  selectedShapeActions: \"Acties van geselecteerde vorm\",\n  shapes: \"Vormen\"\n};\nvar hints = {\n  canvasPanning: \"Om de canvas te verplaatsen, houd muiswiel of spatiebalk ingedrukt tijdens slepen, of gebruik het handgereedschap\",\n  linearElement: \"Klik om meerdere punten te starten, sleep voor \\xE9\\xE9n lijn\",\n  freeDraw: \"Klik en sleep, laat los als je klaar bent\",\n  text: \"Tip: je kunt tekst toevoegen door ergens dubbel te klikken met de selectietool\",\n  embeddable: \"Klink-sleep om een website-insluiting te maken\",\n  text_selected: \"Dubbelklik of druk op ENTER om tekst te bewerken\",\n  text_editing: \"Druk op Escape of CtrlOrCmd+ENTER om het bewerken te voltooien\",\n  linearElementMulti: \"Klik op het laatste punt of druk op Escape of Enter om te stoppen\",\n  lockAngle: \"Je kunt de hoek beperken door SHIFT ingedrukt te houden\",\n  resize: \"Houd tijdens het vergroten SHIFT ingedrukt om verhoudingen te behouden,\\ngebruik ALT om vanuit het midden te vergroten/verkleinen\",\n  resizeImage: \"\",\n  rotate: \"Je kan hoeken beperken door SHIFT ingedrukt te houden wanneer je draait\",\n  lineEditor_info: \"Houd CtrlOrCmd en Dubbelklik of druk op CtrlOrCmd + Enter om punten te bewerken\",\n  lineEditor_pointSelected: \"\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"Publiceer je eigen bibliotheek\",\n  bindTextToElement: \"Druk op enter om tekst toe te voegen\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Kan voorbeeld niet tonen\",\n  canvasTooBig: \"Het canvas is mogelijk te groot.\",\n  canvasTooBigTip: \"Tip: beweeg de verste elementen iets dichter bij elkaar.\"\n};\nvar errorSplash = {\n  headingMain: \"Fout opgetreden. Probeer <button>de pagina opnieuw laden.</button>\",\n  clearCanvasMessage: \"Als herladen niet werkt, probeer <button>het canvas te wissen.</button>\",\n  clearCanvasCaveat: \" Dit zal leiden tot verlies van je werk \",\n  trackedToSentry: \"De fout met ID {{eventId}} was gevolgd op ons systeem.\",\n  openIssueMessage: \"We waren voorzichtig om je sc\\xE8ne-informatie niet in de fout toe te voegen. Als je sc\\xE8ne niet priv\\xE9 is, overweeg dan alstublieft het opvolgen op onze <button>bugtracker.</button> Kopieer de informatie hieronder naar de GitHub issue.\",\n  sceneContent: \"Sc\\xE8ne-inhoud:\"\n};\nvar roomDialog = {\n  desc_intro: \"Je kunt mensen uitnodigen om met je samen te werken.\",\n  desc_privacy: \"Geen zorgen, de sessie gebruikt end-to-end encryptie, dus wat je tekent blijft priv\\xE9. Zelfs onze server zal niet kunnen zien wat je tekent.\",\n  button_startSession: \"Start sessie\",\n  button_stopSession: \"Stop sessie\",\n  desc_inProgressIntro: \"De live-samenwerkingssessie is nu gestart.\",\n  desc_shareLink: \"Deel deze link met iedereen waarmee je wil samenwerken:\",\n  desc_exitSession: \"Het stoppen van de sessie zal je loskoppelen van de kamer, maar je kunt lokaal doorwerken met de sc\\xE8ne.\\nPas op: dit heeft geen invloed op andere mensen en dat zij nog steeds in staat zullen zijn om samen te werken aan hun versie.\",\n  shareTitle: \"Neem deel aan een live samenwerkingssessie op Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Fout\"\n};\nvar exportDialog = {\n  disk_title: \"Opslaan op schijf\",\n  disk_details: \"De sc\\xE8negegevens exporteren naar een bestand waaruit u later kunt importeren.\",\n  disk_button: \"Opslaan naar bestand\",\n  link_title: \"Deelbare link\",\n  link_details: \"Exporteren als een alleen-lezen link.\",\n  link_button: \"Exporteer naar link\",\n  excalidrawplus_description: \"Sla de sc\\xE8ne op in je Excalidraw+ werkruimte.\",\n  excalidrawplus_button: \"Exporteer\",\n  excalidrawplus_exportError: \"Kan op dit moment niet exporteren naar Excalidraw+...\"\n};\nvar helpDialog = {\n  blog: \"Lees onze blog\",\n  click: \"klik\",\n  deepSelect: \"Deep selecteer\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"Gebogen pijl\",\n  curvedLine: \"Kromme lijn\",\n  documentation: \"Documentatie\",\n  doubleClick: \"dubbelklikken\",\n  drag: \"slepen\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"Probleem gevonden? Verzenden\",\n  howto: \"Volg onze handleidingen\",\n  or: \"of\",\n  preventBinding: \"Pijlbinding voorkomen\",\n  tools: \"Tools\",\n  shortcuts: \"Sneltoetsen\",\n  textFinish: \"Voltooi het bewerken (teksteditor)\",\n  textNewLine: \"Nieuwe regel toevoegen (teksteditor)\",\n  title: \"Help\",\n  view: \"Weergave\",\n  zoomToFit: \"Zoom in op alle elementen\",\n  zoomToSelection: \"Inzoomen op selectie\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"Pagina omhoog/omlaag\",\n  movePageLeftRight: \"Verplaats pagina links/rechts\"\n};\nvar clearCanvasDialog = {\n  title: \"Wis canvas\"\n};\nvar publishDialog = {\n  title: \"Publiceer bibliotheek\",\n  itemName: \"Itemnaam\",\n  authorName: \"Naam auteur\",\n  githubUsername: \"GitHub gebruikersnaam\",\n  twitterUsername: \"Twitter gebruikersnaam\",\n  libraryName: \"Naam bibliotheek\",\n  libraryDesc: \"Beschrijving van de bibliotheek\",\n  website: \"Website\",\n  placeholder: {\n    authorName: \"Je naam of gebruikersnaam\",\n    libraryName: \"Naam van je bibliotheek\",\n    libraryDesc: \"Beschrijving van je bibliotheek om mensen te helpen het gebruik ervan te begrijpen\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"Link naar je persoonlijke website of elders (optioneel)\"\n  },\n  errors: {\n    required: \"Vereist\",\n    website: \"Vul een geldige URL in\"\n  },\n  noteDescription: \"<link>openbare repository</link>\",\n  noteGuidelines: \"<link>richtlijnen</link>\",\n  noteLicense: \"<link>MIT-licentie, </link>\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliotheek ingediend\",\n  content: \"<link>Hier</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Reset bibliotheek\",\n  removeItemsFromLib: \"Verwijder geselecteerde items uit bibliotheek\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Je tekeningen zijn beveiligd met end-to-end encryptie, dus Excalidraw's servers zullen nooit zien wat je tekent.\",\n  link: \"Blog post over end-to-end versleuteling in Excalidraw\"\n};\nvar stats = {\n  angle: \"Hoek\",\n  element: \"Element\",\n  elements: \"Elementen\",\n  height: \"Hoogte\",\n  scene: \"Scene\",\n  selected: \"Geselecteerd\",\n  storage: \"Opslag\",\n  title: \"Statistieken voor nerds\",\n  total: \"Totaal\",\n  version: \"Versie\",\n  versionCopy: \"Klik om te kopi\\xEBren\",\n  versionNotAvailable: \"Versie niet beschikbaar\",\n  width: \"Breedte\"\n};\nvar toast = {\n  addedToLibrary: \"Toegevoegd aan bibliotheek\",\n  copyStyles: \"Stijlen gekopieerd.\",\n  copyToClipboard: \"Gekopieerd naar het klembord.\",\n  copyToClipboardAsPng: \"{{exportSelection}} naar klembord gekopieerd als PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Bestand opgeslagen.\",\n  fileSavedToFilename: \"Opgeslagen als {filename}\",\n  canvas: \"canvas\",\n  selection: \"selectie\",\n  pasteAsSingleElement: \"Gebruik {{shortcut}} om te plakken als een enkel element,\\nof plak in een bestaande teksteditor\",\n  unableToEmbed: \"Het insluiten van deze url is momenteel niet toegestaan. Zet een probleem op GitHub om de URL op de whitelist te zetten\",\n  unrecognizedLinkFormat: \"De link die u hebt ingesloten komt niet overeen met het verwachte formaat. Probeer de 'embed' string van de bronsite te plakken\"\n};\nvar colors = {\n  transparent: \"Transparant\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"Exporteren, voorkeuren en meer...\",\n    center_heading: \"Diagrammen. Eenvoudig. Gemaakt.\",\n    toolbarHint: \"Kies een tool & begin met tekenen!\",\n    helpHint: \"Snelkoppelingen en hulp\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar nl_NL_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=nl-NL-F2257BLQ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/nl-NL-F2257BLQ.js\n"));

/***/ })

}]);