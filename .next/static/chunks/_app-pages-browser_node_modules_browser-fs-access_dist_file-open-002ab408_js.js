"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_browser-fs-access_dist_file-open-002ab408_js"],{

/***/ "(app-pages-browser)/./node_modules/browser-fs-access/dist/file-open-002ab408.js":
/*!*******************************************************************!*\
  !*** ./node_modules/browser-fs-access/dist/file-open-002ab408.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\nconst e=async e=>{const t=await e.getFile();return t.handle=e,t};var t=async(t=[{}])=>{Array.isArray(t)||(t=[t]);const i=[];t.forEach((e,t)=>{i[t]={description:e.description||\"\",accept:{}},e.mimeTypes?e.mimeTypes.map(a=>{i[t].accept[a]=e.extensions||[]}):i[t].accept[\"*/*\"]=e.extensions||[]});const a=await window.showOpenFilePicker({id:t[0].id,startIn:t[0].startIn,types:i,multiple:t[0].multiple||!1,excludeAcceptAllOption:t[0].excludeAcceptAllOption||!1}),c=await Promise.all(a.map(e));return t[0].multiple?c:c[0]};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2ZpbGUtb3Blbi0wMDJhYjQwOC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsa0JBQWtCLDBCQUEwQixxQkFBcUIsaUJBQWlCLEtBQUssMEJBQTBCLFdBQVcsa0JBQWtCLE1BQU0sd0NBQXdDLGlDQUFpQyxnQ0FBZ0Msc0NBQXNDLEVBQUUseUNBQXlDLDBIQUEwSCxnQ0FBZ0MsNkJBQWtEIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vbm9kZV9tb2R1bGVzL2Jyb3dzZXItZnMtYWNjZXNzL2Rpc3QvZmlsZS1vcGVuLTAwMmFiNDA4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGU9YXN5bmMgZT0+e2NvbnN0IHQ9YXdhaXQgZS5nZXRGaWxlKCk7cmV0dXJuIHQuaGFuZGxlPWUsdH07dmFyIHQ9YXN5bmModD1be31dKT0+e0FycmF5LmlzQXJyYXkodCl8fCh0PVt0XSk7Y29uc3QgaT1bXTt0LmZvckVhY2goKGUsdCk9PntpW3RdPXtkZXNjcmlwdGlvbjplLmRlc2NyaXB0aW9ufHxcIlwiLGFjY2VwdDp7fX0sZS5taW1lVHlwZXM/ZS5taW1lVHlwZXMubWFwKGE9PntpW3RdLmFjY2VwdFthXT1lLmV4dGVuc2lvbnN8fFtdfSk6aVt0XS5hY2NlcHRbXCIqLypcIl09ZS5leHRlbnNpb25zfHxbXX0pO2NvbnN0IGE9YXdhaXQgd2luZG93LnNob3dPcGVuRmlsZVBpY2tlcih7aWQ6dFswXS5pZCxzdGFydEluOnRbMF0uc3RhcnRJbix0eXBlczppLG11bHRpcGxlOnRbMF0ubXVsdGlwbGV8fCExLGV4Y2x1ZGVBY2NlcHRBbGxPcHRpb246dFswXS5leGNsdWRlQWNjZXB0QWxsT3B0aW9ufHwhMX0pLGM9YXdhaXQgUHJvbWlzZS5hbGwoYS5tYXAoZSkpO3JldHVybiB0WzBdLm11bHRpcGxlP2M6Y1swXX07ZXhwb3J0e3QgYXMgZGVmYXVsdH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/browser-fs-access/dist/file-open-002ab408.js\n"));

/***/ })

}]);