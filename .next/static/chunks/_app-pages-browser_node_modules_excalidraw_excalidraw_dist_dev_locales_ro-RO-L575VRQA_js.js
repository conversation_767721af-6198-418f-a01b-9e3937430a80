"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_ro-RO-L575VRQA_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/ro-RO-L575VRQA.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/ro-RO-L575VRQA.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ ro_RO_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/ro-RO.json\nvar labels = {\n  paste: \"Lipire\",\n  pasteAsPlaintext: \"Inserare ca text simplu\",\n  pasteCharts: \"Lipire diagrame\",\n  selectAll: \"Selectare total\\u0103\",\n  multiSelect: \"Adaug\\u0103 element la selec\\u021Bie\",\n  moveCanvas: \"Mutare p\\xE2nz\\u0103\",\n  cut: \"Decupare\",\n  copy: \"Copiere\",\n  copyAsPng: \"Copiere \\xEEn memoria temporar\\u0103 ca PNG\",\n  copyAsSvg: \"Copiere \\xEEn memoria temporar\\u0103 ca SVG\",\n  copyText: \"Copiere \\xEEn memoria temporar\\u0103 ca text\",\n  copySource: \"Copiere surs\\u0103 \\xEEn memoria temporar\\u0103\",\n  convertToCode: \"Convertire \\xEEn cod\",\n  bringForward: \"Aducere \\xEEn plan apropiat\",\n  sendToBack: \"Trimitere \\xEEn ultimul plan\",\n  bringToFront: \"Aducere \\xEEn prim plan\",\n  sendBackward: \"Trimitere \\xEEn plan secundar\",\n  delete: \"\\u0218tergere\",\n  copyStyles: \"Copiere stiluri\",\n  pasteStyles: \"Lipire stiluri\",\n  stroke: \"Contur\",\n  background: \"Fundal\",\n  fill: \"Umplere\",\n  strokeWidth: \"L\\u0103\\u021Bimea conturului\",\n  strokeStyle: \"Stilul conturului\",\n  strokeStyle_solid: \"Ne\\xEEntrerupt\",\n  strokeStyle_dashed: \"Liniu\\u021Be\",\n  strokeStyle_dotted: \"Punctat\",\n  sloppiness: \"Aspectul tras\\u0103rii\",\n  opacity: \"Opacitate\",\n  textAlign: \"Alinierea textului\",\n  edges: \"Margini\",\n  sharp: \"Ascu\\u021Bite\",\n  round: \"Rotunde\",\n  arrowheads: \"V\\xE2rfuri de s\\u0103geat\\u0103\",\n  arrowhead_none: \"Niciunul\",\n  arrowhead_arrow: \"S\\u0103geat\\u0103\",\n  arrowhead_bar: \"Bar\\u0103\",\n  arrowhead_circle: \"Cerc\",\n  arrowhead_circle_outline: \"Cerc (contur)\",\n  arrowhead_triangle: \"Triunghi\",\n  arrowhead_triangle_outline: \"Triunghi (contur)\",\n  arrowhead_diamond: \"Romb\",\n  arrowhead_diamond_outline: \"Romb (contur)\",\n  fontSize: \"Dimensiune font\",\n  fontFamily: \"Familia de fonturi\",\n  addWatermark: \"Adaug\\u0103 \\u201ERealizat cu Excalidraw\\u201D\",\n  handDrawn: \"Scris de m\\xE2n\\u0103\",\n  normal: \"Normal\",\n  code: \"Cod\",\n  small: \"Mic\\u0103\",\n  medium: \"Medie\",\n  large: \"Mare\",\n  veryLarge: \"Foarte mare\",\n  solid: \"Plin\\u0103\",\n  hachure: \"Ha\\u0219ur\\u0103\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Ha\\u0219ur\\u0103 transversal\\u0103\",\n  thin: \"Sub\\u021Bire\",\n  bold: \"\\xCEngro\\u0219at\\u0103\",\n  left: \"St\\xE2nga\",\n  center: \"Centru\",\n  right: \"Dreapta\",\n  extraBold: \"Extra \\xEEngro\\u0219at\\u0103\",\n  architect: \"Arhitect\",\n  artist: \"Artist\",\n  cartoonist: \"Caricaturist\",\n  fileTitle: \"Nume de fi\\u0219ier\",\n  colorPicker: \"Selector de culoare\",\n  canvasColors: \"Folosite pe p\\xE2nz\\u0103\",\n  canvasBackground: \"Fundalul p\\xE2nzei\",\n  drawingCanvas: \"P\\xE2nz\\u0103 pentru desenat\",\n  layers: \"Straturi\",\n  actions: \"Ac\\u021Biuni\",\n  language: \"Limb\\u0103\",\n  liveCollaboration: \"Colaborare \\xEEn direct...\",\n  duplicateSelection: \"Duplicare\",\n  untitled: \"Nedenumit\",\n  name: \"Nume\",\n  yourName: \"Numele t\\u0103u\",\n  madeWithExcalidraw: \"Realizat cu Excalidraw\",\n  group: \"Grupare selec\\u021Bie\",\n  ungroup: \"Degrupare selec\\u021Bie\",\n  collaborators: \"Colaboratori\",\n  showGrid: \"Afi\\u0219are gril\\u0103\",\n  addToLibrary: \"Ad\\u0103ugare la bibliotec\\u0103\",\n  removeFromLibrary: \"Eliminare din bibliotec\\u0103\",\n  libraryLoadingMessage: \"Se \\xEEncarc\\u0103 biblioteca\\u2026\",\n  libraries: \"R\\u0103sfoie\\u0219te bibliotecile\",\n  loadingScene: \"Se \\xEEncarc\\u0103 scena\\u2026\",\n  align: \"Aliniere\",\n  alignTop: \"Aliniere sus\",\n  alignBottom: \"Aliniere jos\",\n  alignLeft: \"Aliniere la st\\xE2nga\",\n  alignRight: \"Aliniere la dreapta\",\n  centerVertically: \"Centrare vertical\\u0103\",\n  centerHorizontally: \"Centrare orizontal\\u0103\",\n  distributeHorizontally: \"Distribuie orizontal\",\n  distributeVertically: \"Distribuie vertical\",\n  flipHorizontal: \"R\\u0103sturnare orizontal\\u0103\",\n  flipVertical: \"R\\u0103sturnare vertical\\u0103\",\n  viewMode: \"Mod de vizualizare\",\n  share: \"Distribuie\",\n  showStroke: \"Afi\\u0219are selector culoare contur\",\n  showBackground: \"Afi\\u0219are selector culoare fundal\",\n  toggleTheme: \"Comutare tem\\u0103\",\n  personalLib: \"Biblioteca personal\\u0103\",\n  excalidrawLib: \"Biblioteca Excalidraw\",\n  decreaseFontSize: \"Mic\\u0219oreaz\\u0103 dimensiunea fontului\",\n  increaseFontSize: \"M\\u0103re\\u0219te dimensiunea fontului\",\n  unbindText: \"Deconectare text\",\n  bindText: \"Legare text de container\",\n  createContainerFromText: \"\\xCEncadrare text \\xEEntr-un container\",\n  link: {\n    edit: \"Editare URL\",\n    editEmbed: \"Editare URL \\u0219i \\xEEncorporare\",\n    create: \"Creare URL\",\n    createEmbed: \"Creare URL \\u0219i \\xEEncorporare\",\n    label: \"URL\",\n    labelEmbed: \"URL \\u0219i \\xEEncorporare\",\n    empty: \"Nu este setat niciun URL\"\n  },\n  lineEditor: {\n    edit: \"Editare linie\",\n    exit: \"P\\u0103r\\u0103sire editor de linii\"\n  },\n  elementLock: {\n    lock: \"Blocare\",\n    unlock: \"Deblocare\",\n    lockAll: \"Blocare toate\",\n    unlockAll: \"Deblocare toate\"\n  },\n  statusPublished: \"Publicat\",\n  sidebarLock: \"P\\u0103streaz\\u0103 deschis\\u0103 bara lateral\\u0103\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"Alegere culoare din p\\xE2nz\\u0103\",\n  textToDiagram: \"Text la diagram\\u0103\",\n  prompt: \"Solicitare\"\n};\nvar library = {\n  noItems: \"Niciun element ad\\u0103ugat \\xEEnc\\u0103...\",\n  hint_emptyLibrary: \"Selecteaz\\u0103 un element de pe p\\xE2nz\\u0103 pentru a-l ad\\u0103uga aici sau instaleaz\\u0103 o bibliotec\\u0103 din depozitul public, de mai jos.\",\n  hint_emptyPrivateLibrary: \"Selecteaz\\u0103 un element de pe p\\xE2nz\\u0103 pentru a-l ad\\u0103uga aici.\"\n};\nvar buttons = {\n  clearReset: \"Resetare p\\xE2nz\\u0103\",\n  exportJSON: \"Exportare la fi\\u0219iere\",\n  exportImage: \"Exportare imagine...\",\n  export: \"Salvare \\xEEn...\",\n  copyToClipboard: \"Copiere \\xEEn memoria temporar\\u0103\",\n  save: \"Salvare \\xEEn fi\\u0219ierul curent\",\n  saveAs: \"Salvare ca\",\n  load: \"Deschidere\",\n  getShareableLink: \"Ob\\u021Bine URL partajabil\",\n  close: \"\\xCEnchidere\",\n  selectLanguage: \"Selectare limb\\u0103\",\n  scrollBackToContent: \"Derulare \\xEEnapoi la con\\u021Binut\",\n  zoomIn: \"Apropiere\",\n  zoomOut: \"Dep\\u0103rtare\",\n  resetZoom: \"Resetare transfocare\",\n  menu: \"Meniu\",\n  done: \"Efectuat\",\n  edit: \"Edit\",\n  undo: \"Anulare\",\n  redo: \"Refacere\",\n  resetLibrary: \"Resetare bibliotec\\u0103\",\n  createNewRoom: \"Creare camer\\u0103 nou\\u0103\",\n  fullScreen: \"Ecran complet\",\n  darkMode: \"Mod \\xEEntunecat\",\n  lightMode: \"Mod luminos\",\n  zenMode: \"Mod zen\",\n  objectsSnapMode: \"Ancorare la obiecte\",\n  exitZenMode: \"Ie\\u0219ire din modul zen\",\n  cancel: \"Anulare\",\n  clear: \"\\u0218tergere\",\n  remove: \"Eliminare\",\n  embed: \"Comutare \\xEEncorporare\",\n  publishLibrary: \"Publicare\",\n  submit: \"Trimitere\",\n  confirm: \"Confirmare\",\n  embeddableInteractionButton: \"Clic pentru interac\\u021Bionare\"\n};\nvar alerts = {\n  clearReset: \"Aceast\\u0103 op\\u021Biune va \\u0219terge \\xEEntreaga p\\xE2nz\\u0103. Confirmi?\",\n  couldNotCreateShareableLink: \"Nu s-a putut crea un URL partajabil.\",\n  couldNotCreateShareableLinkTooBig: \"Nu s-a putut crea un URL partajabil: scena este prea mare\",\n  couldNotLoadInvalidFile: \"Fi\\u0219ierul invalid nu a putut fi \\xEEnc\\u0103rcat\",\n  importBackendFailed: \"Importarea de la nivel de server a e\\u0219uat.\",\n  cannotExportEmptyCanvas: \"Nu se poate exporta p\\xE2nza goal\\u0103.\",\n  couldNotCopyToClipboard: \"Nu s-a putut copia \\xEEn memoria temporar\\u0103.\",\n  decryptFailed: \"Datele nu au putut fi decriptate.\",\n  uploadedSecurly: \"\\xCEnc\\u0103rcarea a fost securizat\\u0103 prin criptare integral\\u0103, \\xEEnsemn\\xE2nd c\\u0103 serverul Excalidraw \\u0219i ter\\u021Bii nu pot citi con\\u021Binutul.\",\n  loadSceneOverridePrompt: \"\\xCEnc\\u0103rcarea desenului extern va \\xEEnlocui con\\u021Binutul existent. Dore\\u0219ti s\\u0103 continui?\",\n  collabStopOverridePrompt: \"Oprirea sesiunii va suprascrie desenul anterior stocat local. Confirmi alegerea?\\n\\n(Dac\\u0103 vrei s\\u0103 p\\u0103strezi desenul local, pur \\u0219i simplu \\xEEnchide fila navigatorului \\xEEn schimb.)\",\n  errorAddingToLibrary: \"Elementul nu a putut fi ad\\u0103ugat \\xEEn bibliotec\\u0103\",\n  errorRemovingFromLibrary: \"Elementul nu a putut fi eliminat din bibliotec\\u0103\",\n  confirmAddLibrary: \"Aceast\\u0103 ac\\u021Biune va ad\\u0103uga {{numShapes}} form\\u0103(e) la biblioteca ta. Confirmi?\",\n  imageDoesNotContainScene: \"Aceast\\u0103 imagine nu pare s\\u0103 con\\u021Bin\\u0103 date de scen\\u0103. Ai activat \\xEEncorporarea scenei \\xEEn timpul exportului?\",\n  cannotRestoreFromImage: \"Scena nu a putut fi restaurat\\u0103 din acest fi\\u0219ier de imagine\",\n  invalidSceneUrl: \"Scena nu a putut fi importat\\u0103 din URL-ul furnizat. Este fie incorect format\\u0103, fie nu con\\u021Bine date JSON Excalidraw valide.\",\n  resetLibrary: \"Aceast\\u0103 op\\u021Biune va elimina con\\u021Binutul din bibliotec\\u0103. Confirmi?\",\n  removeItemsFromsLibrary: \"\\u0218tergi {{count}} element(e) din bibliotec\\u0103?\",\n  invalidEncryptionKey: \"Cheia de criptare trebuie s\\u0103 aib\\u0103 22 de caractere. Colaborarea \\xEEn direct este dezactivat\\u0103.\",\n  collabOfflineWarning: \"Nu este disponibil\\u0103 nicio conexiune la internet.\\nModific\\u0103rile nu vor fi salvate!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tip de fi\\u0219ier neacceptat.\",\n  imageInsertError: \"Imaginea nu a putut fi introdus\\u0103. Re\\xEEncearc\\u0103 mai t\\xE2rziu...\",\n  fileTooBig: \"Fi\\u0219ierul este prea mare. Dimensiunea maxim\\u0103 permis\\u0103 este de {{maxSize}}.\",\n  svgImageInsertError: \"Imaginea SVG nu a putut fi introdus. Marcajul SVG pare invalid.\",\n  failedToFetchImage: \"Preluarea imaginii a e\\u0219uat.\",\n  invalidSVGString: \"SVG invalid.\",\n  cannotResolveCollabServer: \"Nu a putut fi realizat\\u0103 conexiunea la serverul de colaborare. Re\\xEEncarc\\u0103 pagina \\u0219i \\xEEncearc\\u0103 din nou.\",\n  importLibraryError: \"Biblioteca nu a putut fi \\xEEnc\\u0103rcat\\u0103\",\n  collabSaveFailed: \"Nu s-a putut salva \\xEEn baza de date la nivel de server. Dac\\u0103 problemele persist\\u0103, ar trebui s\\u0103 salvezi fi\\u0219ierul la nivel local pentru a te asigura c\\u0103 nu \\xEE\\u021Bi pierzi munca.\",\n  collabSaveFailed_sizeExceeded: \"Nu s-a putut salva \\xEEn baza de date la nivel de server, \\xEEntruc\\xE2t se pare c\\u0103 p\\xE2nza este prea mare. Ar trebui s\\u0103 salvezi fi\\u0219ierul la nivel local pentru a te asigura c\\u0103 nu \\xEE\\u021Bi pierzi munca.\",\n  imageToolNotSupported: \"Imaginile sunt dezactivate.\",\n  brave_measure_text_error: {\n    line1: \"Se pare c\\u0103 folose\\u0219ti navigatorul Brave cu op\\u021Biunea <bold>strict\\u0103 pentru blocarea amprent\\u0103rii</bold>.\",\n    line2: \"Acest lucru poate duce la \\xEEntreruperea <bold>elementelor text</bold> din desene.\",\n    line3: \"\\xCE\\u021Bi recomand\\u0103m ferm s\\u0103 dezactivezi aceast\\u0103 setare. Po\\u021Bi urma <link>ace\\u0219ti pa\\u0219i</link> pentru a face acest lucru.\",\n    line4: \"Dac\\u0103 dezactivarea acestei set\\u0103ri nu duce la remedierea afi\\u0219\\u0103rii elementelor text, deschide un tichet de <issueLink>problem\\u0103</issueLink> pe pagina noastr\\u0103 de GitHub sau scrie-ne pe <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Elementele \\xEEncorporabile nu pot fi ad\\u0103ugate la bibliotec\\u0103.\",\n    iframe: \"Elementele iFrame nu pot fi ad\\u0103ugate la bibliotec\\u0103.\",\n    image: \"\\xCEn cur\\xE2nd vor putea fi ad\\u0103ugate imagini \\xEEn bibliotec\\u0103!\"\n  },\n  asyncPasteFailedOnRead: \"Lipirea nu a putut fi efectuat\\u0103 (nu s-a putut citit din memoria temporar\\u0103 a sistemului).\",\n  asyncPasteFailedOnParse: \"Lipirea nu a putut fi efectuat\\u0103.\",\n  copyToSystemClipboardFailed: \"Nu s-a putut copia \\xEEn memoria temporar\\u0103.\"\n};\nvar toolBar = {\n  selection: \"Selec\\u021Bie\",\n  image: \"Introducere imagine\",\n  rectangle: \"Dreptunghi\",\n  diamond: \"Romb\",\n  ellipse: \"Elips\\u0103\",\n  arrow: \"S\\u0103geat\\u0103\",\n  line: \"Linie\",\n  freedraw: \"Desenare\",\n  text: \"Text\",\n  library: \"Bibliotec\\u0103\",\n  lock: \"Men\\u021Bine activ instrumentul selectat dup\\u0103 desenare\",\n  penMode: \"Mod stilou \\u2013 \\xEEmpiedic\\u0103 atingerea\",\n  link: \"Ad\\u0103ugare/actualizare URL pentru forma selectat\\u0103\",\n  eraser: \"Radier\\u0103\",\n  frame: \"\",\n  magicframe: \"Structur\\u0103-de-fire la cod\",\n  embeddable: \"\\xCEncorporare web\",\n  laser: \"Indicator laser\",\n  hand: \"M\\xE2n\\u0103 (instrument de panoramare)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"Mermaid la Excalidraw\",\n  magicSettings: \"Set\\u0103ri IA\"\n};\nvar headings = {\n  canvasActions: \"Ac\\u021Biuni pentru p\\xE2nz\\u0103\",\n  selectedShapeActions: \"Ac\\u021Biuni pentru forma selectat\\u0103\",\n  shapes: \"Forme\"\n};\nvar hints = {\n  canvasPanning: \"Pentru a muta p\\xE2nz\\u0103, \\u021Bine ap\\u0103sat\\u0103 roti\\u021Ba mausului sau bara de spa\\u021Biu sau folose\\u0219te instrumentul \\xEEn form\\u0103 de m\\xE2n\\u0103\",\n  linearElement: \"D\\u0103 clic pentru a crea mai multe puncte, gliseaz\\u0103 pentru a forma o singur\\u0103 linie\",\n  freeDraw: \"D\\u0103 clic pe p\\xE2nz\\u0103 \\u0219i gliseaz\\u0103 cursorul, apoi elibereaz\\u0103-l c\\xE2nd ai terminat\",\n  text: \"Sfat: po\\u021Bi ad\\u0103uga text \\u0219i d\\xE2nd dublu clic oriunde cu instrumentul de selec\\u021Bie\",\n  embeddable: \"D\\u0103 clic \\u0219i trage pentru a crea un cod de \\xEEncorporare de pagin\\u0103 web\",\n  text_selected: \"D\\u0103 dublu clic sau apas\\u0103 tasta Enter pentru a edita textul\",\n  text_editing: \"Apas\\u0103 tasta Escape sau Ctrl sau Cmd + Enter pentru a finaliza editarea\",\n  linearElementMulti: \"D\\u0103 clic pe ultimul punct sau apas\\u0103 tasta Escape sau tasta Enter pentru a termina\",\n  lockAngle: \"Po\\u021Bi constr\\xE2nge unghiul prin \\u021Binerea ap\\u0103sat\\u0103 a tastei SHIFT\",\n  resize: \"Po\\u021Bi constr\\xE2nge propor\\u021Biile, \\u021Bin\\xE2nd ap\\u0103sat\\u0103 tasta SHIFT \\xEEn timp ce redimensionezi,\\n\\u021Bine ap\\u0103sat\\u0103 tasta ALT pentru a redimensiona de la centru\",\n  resizeImage: \"Po\\u021Bi redimensiona liber \\u021Bin\\xE2nd ap\\u0103sat\\u0103 tasta SHIFT,\\n\\u021Bine ap\\u0103sat\\u0103 tasta ALT pentru a redimensiona din centru\",\n  rotate: \"Po\\u021Bi constr\\xE2nge unghiurile, \\u021Bin\\xE2nd ap\\u0103sat\\u0103 tasta SHIFT \\xEEn timp ce rote\\u0219ti\",\n  lineEditor_info: \"\\u021Aine ap\\u0103sat\\u0103 tasta Ctrl sau Cmd \\u0219i d\\u0103 dublu clic sau apas\\u0103 tasta Ctrl sau Cmd + Enter pentru a edita puncte\",\n  lineEditor_pointSelected: \"Apas\\u0103 tasta Delete pentru a elimina punctele,\\ncombina\\u021Bia de taste Ctrl sau Cmd + D pentru a le duplica sau gliseaz\\u0103-le pentru a le schimba pozi\\u021Bia\",\n  lineEditor_nothingSelected: \"Selecteaz\\u0103 un punct pentru a-l edita (\\u021Bine ap\\u0103sat\\u0103 tasta SHIFT pentru a selecta mai multe),\\nsau \\u021Bine ap\\u0103sat\\u0103 tasta Alt \\u0219i d\\u0103 clic pentru a ad\\u0103uga puncte noi\",\n  placeImage: \"D\\u0103 clic pentru a pozi\\u021Biona imaginea sau d\\u0103 clic \\u0219i gliseaz\\u0103 pentru a seta manual dimensiunea imaginii\",\n  publishLibrary: \"Public\\u0103 propria bibliotec\\u0103\",\n  bindTextToElement: \"Apas\\u0103 tasta Enter pentru a ad\\u0103uga text\",\n  deepBoxSelect: \"\\u021Aine ap\\u0103sat\\u0103 tasta Ctrl sau Cmd pentru a efectua selectarea de ad\\xE2ncime \\u0219i pentru a preveni glisarea\",\n  eraserRevert: \"\\u021Aine ap\\u0103sat\\u0103 tasta Alt pentru a anula elementele marcate pentru \\u0219tergere\",\n  firefox_clipboard_write: \"Aceast\\u0103 caracteristic\\u0103 poate fi probabil activat\\u0103 prin setarea preferin\\u021Bei \\u201Edom.events.asyncClipboard.clipboardItem\\u201D ca \\u201Etrue\\u201D. Pentru a schimba preferin\\u021Bele navigatorului \\xEEn Firefox, acceseaz\\u0103 pagina \\u201Eabout:config\\u201D.\",\n  disableSnapping: \"\\u021Aine ap\\u0103sat CtrlOrCmd pentru a dezactiva ancorarea\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Nu se poate afi\\u0219a previzualizarea\",\n  canvasTooBig: \"P\\xE2nza poate fi prea mare.\",\n  canvasTooBigTip: \"Sfat: \\xEEncearc\\u0103 s\\u0103 apropii pu\\u021Bin mai mult elementele cele mai \\xEEndep\\u0103rtate.\"\n};\nvar errorSplash = {\n  headingMain: \"A ap\\u0103rut o eroare. \\xCEncearc\\u0103 <button>s\\u0103 re\\xEEncarci pagina</button>.\",\n  clearCanvasMessage: \"Dac\\u0103 re\\xEEnc\\u0103rcarea nu func\\u021Bioneaz\\u0103, \\xEEncearc\\u0103 <button>s\\u0103 \\u0219tergi p\\xE2nza</button>.\",\n  clearCanvasCaveat: \" Acest lucru va duce la pierderea progresului \",\n  trackedToSentry: \"Eroarea cu identificatorul {{eventId}} a fost urm\\u0103rit\\u0103 \\xEEn sistemul nostru.\",\n  openIssueMessage: \"Am luat m\\u0103suri de precau\\u021Bie pentru a nu include informa\\u021Bii despre scen\\u0103 \\xEEn eroare. Dac\\u0103 scena nu este privat\\u0103, ofer\\u0103-ne mai multe informa\\u021Bii \\xEEn <button>monitorul nostru pentru erori</button>. Include informa\\u021Biile de mai jos copiindu-le \\u0219i lipindu-le \\xEEn tichetul cu problem\\u0103 de pe GitHub.\",\n  sceneContent: \"Con\\u021Binutul scenei:\"\n};\nvar roomDialog = {\n  desc_intro: \"Po\\u021Bi invita alte persoane pentru a colabora la scena actual\\u0103.\",\n  desc_privacy: \"Nu te \\xEEngrijora. Sesiunea utilizeaz\\u0103 criptarea integral\\u0103, astfel \\xEEnc\\xE2t orice desenezi va r\\u0103m\\xE2ne privat. Nici m\\u0103car serverul nostru nu va putea vedea pe ce ai lucrat.\",\n  button_startSession: \"Pornire sesiune\",\n  button_stopSession: \"Oprire sesiune\",\n  desc_inProgressIntro: \"Sesiunea de colaborare \\xEEn direct este \\xEEn curs de desf\\u0103\\u0219urare.\",\n  desc_shareLink: \"Distribuie acest URL persoanelor cu care dore\\u0219ti s\\u0103 colaborezi:\",\n  desc_exitSession: \"Oprirea sesiunii te va deconecta de la sal\\u0103, \\xEEns\\u0103 vei putea lucra \\xEEn continuare, pe plan local, cu scena. Re\\u021Bine c\\u0103 aceast\\u0103 op\\u021Biune nu va afecta alte persoane, iar acestea vor putea s\\u0103 colaboreze \\xEEn continuare pe versiunea lor.\",\n  shareTitle: \"Al\\u0103tur\\u0103-te unei sesiuni de colaborare \\xEEn direct pe Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Eroare\"\n};\nvar exportDialog = {\n  disk_title: \"Salvare pe disc\",\n  disk_details: \"Export\\u0103 datele scenei pe un fi\\u0219ier din care po\\u021Bi importa mai t\\xE2rziu.\",\n  disk_button: \"Salvare \\xEEn fi\\u0219ier\",\n  link_title: \"URL partajabil\",\n  link_details: \"Export\\u0103 ca URL doar \\xEEn citire.\",\n  link_button: \"Exportare \\xEEn URL\",\n  excalidrawplus_description: \"Salveaz\\u0103 scena \\xEEn spa\\u021Biul de lucru Excalidraw+.\",\n  excalidrawplus_button: \"Exportare\",\n  excalidrawplus_exportError: \"Excalidraw+ nu a putut fi exportat \\xEEn acest moment...\"\n};\nvar helpDialog = {\n  blog: \"Cite\\u0219te blogul nostru\",\n  click: \"clic\",\n  deepSelect: \"Selectare de ad\\xE2ncime\",\n  deepBoxSelect: \"Selectare de ad\\xE2ncime \\xEEn caset\\u0103 \\u0219i prevenire glisare\",\n  curvedArrow: \"S\\u0103geat\\u0103 curbat\\u0103\",\n  curvedLine: \"Linie curbat\\u0103\",\n  documentation: \"Documenta\\u021Bie\",\n  doubleClick: \"dublu clic\",\n  drag: \"glisare\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Editare puncte de s\\u0103geat\\u0103/r\\xE2nd\",\n  editText: \"Editare text/ad\\u0103ugare etichet\\u0103\",\n  github: \"Ai \\xEEnt\\xE2mpinat o problem\\u0103? Trimite un raport\",\n  howto: \"Urm\\u0103re\\u0219te ghidurile noastre\",\n  or: \"sau\",\n  preventBinding: \"\\xCEmpiedic\\u0103 legarea s\\u0103ge\\u021Bii\",\n  tools: \"Instrumente\",\n  shortcuts: \"Comenzi rapide de la tastatur\\u0103\",\n  textFinish: \"Finalizeaz\\u0103 editarea (editor de text)\",\n  textNewLine: \"Adaug\\u0103 o linie nou\\u0103 (editor de text)\",\n  title: \"Ajutor\",\n  view: \"Vizualizare\",\n  zoomToFit: \"Transfocare pentru a cuprinde totul\",\n  zoomToSelection: \"Transfocare la selec\\u021Bie\",\n  toggleElementLock: \"Blocare/deblocare selec\\u021Bie\",\n  movePageUpDown: \"Deplasare pagin\\u0103 sus/jos\",\n  movePageLeftRight: \"Deplasare pagin\\u0103 st\\xE2nga/dreapta\"\n};\nvar clearCanvasDialog = {\n  title: \"\\u0218tergere p\\xE2nz\\u0103\"\n};\nvar publishDialog = {\n  title: \"Publicare bibliotec\\u0103\",\n  itemName: \"Denumirea elementului\",\n  authorName: \"Numele autorului\",\n  githubUsername: \"Numele de utilizator GitHub\",\n  twitterUsername: \"Numele de utilizator Twitter\",\n  libraryName: \"Denumirea bibliotecii\",\n  libraryDesc: \"Descrierea bibliotecii\",\n  website: \"Pagin\\u0103 de internet\",\n  placeholder: {\n    authorName: \"Numele sau numele t\\u0103u de utilizator\",\n    libraryName: \"Numele bibliotecii tale\",\n    libraryDesc: \"Descrierea bibliotecii tale pentru a ajuta oamenii s\\u0103 \\xEEn\\u021Beleag\\u0103 utilizarea acesteia\",\n    githubHandle: \"Numele de utilizator GitHub (op\\u021Bional), pentru a putea edita biblioteca odat\\u0103 ce este trimis\\u0103 spre revizuire\",\n    twitterHandle: \"Numele de utilizator Twitter (op\\u021Bional), pentru a indica sursa la promovarea pe Twitter\",\n    website: \"Trimitere c\\u0103tre pagina ta personal\\u0103 de internet sau altundeva (op\\u021Bional)\"\n  },\n  errors: {\n    required: \"Obligatoriu\",\n    website: \"Introdu un URL valid\"\n  },\n  noteDescription: \"Trimite-\\u021Bi biblioteca pentru a fi inclus\\u0103 \\xEEn <link>depozitul de biblioteci publice</link> \\xEEn vederea utiliz\\u0103rii de c\\u0103tre alte persoane \\xEEn desenele lor.\",\n  noteGuidelines: \"Biblioteca trebuie aprobat\\u0103 manual mai \\xEEnt\\xE2i. Cite\\u0219te <link>orient\\u0103rile</link> \\xEEnainte de trimitere. Vei avea nevoie de un cont GitHub pentru a comunica \\u0219i efectua modific\\u0103ri, dac\\u0103 este cazul, \\xEEns\\u0103 nu este strict necesar.\",\n  noteLicense: \"Prin trimiterea bibliotecii, e\\u0219ti de acord c\\u0103 aceasta va fi publicat\\u0103 sub <link>Licen\\u021Ba MIT, </link>care, pe scurt, \\xEEnseamn\\u0103 c\\u0103 oricine o poate folosi f\\u0103r\\u0103 restric\\u021Bii.\",\n  noteItems: \"Fiecare element din bibliotec\\u0103 trebuie s\\u0103 aib\\u0103 propriul nume astfel \\xEEnc\\xE2t s\\u0103 fie filtrabil. Urm\\u0103toarele elemente din bibliotec\\u0103 vor fi incluse:\",\n  atleastOneLibItem: \"Selecteaz\\u0103 cel pu\\u021Bin un element din bibliotec\\u0103 pentru a \\xEEncepe\",\n  republishWarning: \"Observa\\u021Bie: unele dintre elementele selectate sunt marcate ca fiind deja publicate/trimise. Ar trebui s\\u0103 retrimi\\u021Bi elemente numai atunci c\\xE2nd actualizezi o trimitere sau o bibliotec\\u0103 existent\\u0103.\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliotec\\u0103 trimis\\u0103\",\n  content: \"\\xCE\\u021Bi mul\\u021Bumim, {{authorName}}. Biblioteca a fost trimis\\u0103 spre revizuire. Po\\u021Bi urm\\u0103ri starea <link>aici</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Resetare bibliotec\\u0103\",\n  removeItemsFromLib: \"Elimin\\u0103 elementele selectate din bibliotec\\u0103\"\n};\nvar imageExportDialog = {\n  header: \"Exportare imagine\",\n  label: {\n    withBackground: \"Fundal\",\n    onlySelected: \"Numai selec\\u021Bia\",\n    darkMode: \"Mod \\xEEntunecat\",\n    embedScene: \"\\xCEncorporare scen\\u0103\",\n    scale: \"Scal\\u0103\",\n    padding: \"Spa\\u021Biere\"\n  },\n  tooltip: {\n    embedScene: \"Datele scenei vor fi salvate \\xEEn fi\\u0219ierul PNG/SVG exportat, astfel c\\u0103 scena va putea fi restaurat\\u0103 din acesta.\\nVa cre\\u0219te dimensiunea fi\\u0219ierului exportat.\"\n  },\n  title: {\n    exportToPng: \"Exportare ca PNG\",\n    exportToSvg: \"Exportare ca SVG\",\n    copyPngToClipboard: \"Copiere PNG \\xEEn memoria temporar\\u0103\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copiere \\xEEn memoria temporar\\u0103\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Desenele tale sunt criptate integral, astfel c\\u0103 serverele Excalidraw nu le vor vedea niciodat\\u0103.\",\n  link: \"Articol de blog pe criptarea integral\\u0103 din Excalidraw\"\n};\nvar stats = {\n  angle: \"Unghi\",\n  element: \"Element\",\n  elements: \"Elemente\",\n  height: \"\\xCEn\\u0103l\\u021Bime\",\n  scene: \"Scen\\u0103\",\n  selected: \"Selectate\",\n  storage: \"Stocare\",\n  title: \"Statistici pentru pasiona\\u021Bi\",\n  total: \"Total\",\n  version: \"Versiune\",\n  versionCopy: \"Clic pentru copiere\",\n  versionNotAvailable: \"Versiune indisponibil\\u0103\",\n  width: \"L\\u0103\\u021Bime\"\n};\nvar toast = {\n  addedToLibrary: \"Ad\\u0103ugat \\xEEn bibliotec\\u0103\",\n  copyStyles: \"Stiluri copiate.\",\n  copyToClipboard: \"Copiat \\xEEn memoria temporar\\u0103.\",\n  copyToClipboardAsPng: \"S-a copiat {{exportSelection}} \\xEEn memoria temporar\\u0103 sub form\\u0103 de PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Fi\\u0219ier salvat.\",\n  fileSavedToFilename: \"Salvat \\xEEn {filename}\",\n  canvas: \"p\\xE2nza\",\n  selection: \"selec\\u021Bia\",\n  pasteAsSingleElement: \"Folose\\u0219te {{shortcut}} pentru a insera ca un singur element\\nsau insera \\xEEntr-un editor de text existent\",\n  unableToEmbed: \"\\xCEncorporarea acestui URL nu este permis\\u0103 momentan. Deschide\\u021Bi un tichet cu probleme pe GitHub pentru a solicita ad\\u0103ugarea acestui URL \\xEEn lista alb\\u0103\",\n  unrecognizedLinkFormat: \"URL-ul pe care l-ai \\xEEncorporat nu coincide cu formatul a\\u0219teptat. \\xCEncearc\\u0103 s\\u0103 lipe\\u0219ti \\u0219irul \\u201Ede \\xEEncorporat\\u201D furnizat de pagina surs\\u0103\"\n};\nvar colors = {\n  transparent: \"Transparent\",\n  black: \"Negru\",\n  white: \"Alb\",\n  red: \"Ro\\u0219u\",\n  pink: \"Roz\",\n  grape: \"Struguriu\",\n  violet: \"Violet\",\n  gray: \"Gri\",\n  blue: \"Albastru\",\n  cyan: \"Cyan\",\n  teal: \"Cyan-verde\",\n  green: \"Verde\",\n  yellow: \"Galben\",\n  orange: \"Portocaliu\",\n  bronze: \"Bronz\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Toate datele tale sunt salvate local \\xEEn navigatorul t\\u0103u.\",\n    center_heading_plus: \"Ai vrut s\\u0103 mergi \\xEEn schimb la Excalidraw+?\",\n    menuHint: \"Exportare, preferin\\u021Be, limbi, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportare, preferin\\u021Be \\u0219i mai multe...\",\n    center_heading: \"Diagrame. F\\u0103cute. Simple.\",\n    toolbarHint: \"Alege un instrument \\u0219i \\xEEncepe s\\u0103 desenezi!\",\n    helpHint: \"Comenzi rapide \\u0219i ajutor\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Cele mai utilizate culori personalizate\",\n  colors: \"Culori\",\n  shades: \"Nuan\\u021Be\",\n  hexCode: \"Cod hexa\",\n  noShades: \"Nu este disponibil\\u0103 nicio nuan\\u021B\\u0103 pentru aceast\\u0103 culoare\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportare ca imagine\",\n      button: \"Exportare ca imagine\",\n      description: \"Export\\u0103 datele scenei ca fi\\u0219ier din care po\\u021Bi importa mai t\\xE2rziu.\"\n    },\n    saveToDisk: {\n      title: \"Salvare pe disc\",\n      button: \"Salvare pe disc\",\n      description: \"Export\\u0103 datele scenei pe un fi\\u0219ier din care po\\u021Bi importa mai t\\xE2rziu.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exportare \\xEEn Excalidraw+\",\n      description: \"Salveaz\\u0103 scena \\xEEn spa\\u021Biul de lucru Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\\xCEnc\\u0103rcare din fi\\u0219ier\",\n      button: \"\\xCEnc\\u0103rcare din fi\\u0219ier\",\n      description: \"\\xCEnc\\u0103rcarea dintr-un fi\\u0219ier va <bold>\\xEEnlocui con\\u021Binutul existent</bold>.<br></br>Po\\u021Bi face mai \\xEEnt\\xE2i o copie de rezerv\\u0103 a desenului folosind una dintre op\\u021Biunile de mai jos.\"\n    },\n    shareableLink: {\n      title: \"\\xCEnc\\u0103rcare din lnk\",\n      button: \"\\xCEnlocuie\\u0219te con\\u021Binutul meu\",\n      description: \"\\xCEnc\\u0103rcarea unui desen extern va <bold>\\xEEnlocui con\\u021Binutul existent</bold>.<br></br>Po\\u021Bi face mai \\xEEnt\\xE2i o copie de rezerv\\u0103 a desenului folosind una dintre op\\u021Biunile de mai jos.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid la Excalidraw\",\n  button: \"Introducere\",\n  description: \"\\xCEn prezent, numai <flowchartLink>Organigramele</flowchartLink>, <sequenceLink>Diagramele de secven\\u021B\\u0103</sequenceLink> \\u0219i <classLink>Diagramele de clas\\u0103</classLink> sunt acceptate. Celelalte tipuri vor fi redate ca imagine \\xEEn Excalidraw.\",\n  syntax: \"Sintax\\u0103 Mermaid\",\n  preview: \"Previzualizare\"\n};\nvar ro_RO_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=ro-RO-L575VRQA.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvcm8tUk8tTDU3NVZSUUEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLFlBQVk7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsUUFBUTtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RkFBNEYsU0FBUztBQUNyRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsVUFBVTtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlDQUF5QyxZQUFZO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLGtCQUFrQix1REFBdUQsbUJBQW1CO0FBQ2xJO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0M7QUFDQTtBQUNBLDBDQUEwQyxXQUFXO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBOEJFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvQGV4Y2FsaWRyYXcvZXhjYWxpZHJhdy9kaXN0L2Rldi9sb2NhbGVzL3JvLVJPLUw1NzVWUlFBLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4uL2NodW5rLVhERkNVVVQ2LmpzXCI7XG5cbi8vIGxvY2FsZXMvcm8tUk8uanNvblxudmFyIGxhYmVscyA9IHtcbiAgcGFzdGU6IFwiTGlwaXJlXCIsXG4gIHBhc3RlQXNQbGFpbnRleHQ6IFwiSW5zZXJhcmUgY2EgdGV4dCBzaW1wbHVcIixcbiAgcGFzdGVDaGFydHM6IFwiTGlwaXJlIGRpYWdyYW1lXCIsXG4gIHNlbGVjdEFsbDogXCJTZWxlY3RhcmUgdG90YWxcXHUwMTAzXCIsXG4gIG11bHRpU2VsZWN0OiBcIkFkYXVnXFx1MDEwMyBlbGVtZW50IGxhIHNlbGVjXFx1MDIxQmllXCIsXG4gIG1vdmVDYW52YXM6IFwiTXV0YXJlIHBcXHhFMm56XFx1MDEwM1wiLFxuICBjdXQ6IFwiRGVjdXBhcmVcIixcbiAgY29weTogXCJDb3BpZXJlXCIsXG4gIGNvcHlBc1BuZzogXCJDb3BpZXJlIFxceEVFbiBtZW1vcmlhIHRlbXBvcmFyXFx1MDEwMyBjYSBQTkdcIixcbiAgY29weUFzU3ZnOiBcIkNvcGllcmUgXFx4RUVuIG1lbW9yaWEgdGVtcG9yYXJcXHUwMTAzIGNhIFNWR1wiLFxuICBjb3B5VGV4dDogXCJDb3BpZXJlIFxceEVFbiBtZW1vcmlhIHRlbXBvcmFyXFx1MDEwMyBjYSB0ZXh0XCIsXG4gIGNvcHlTb3VyY2U6IFwiQ29waWVyZSBzdXJzXFx1MDEwMyBcXHhFRW4gbWVtb3JpYSB0ZW1wb3JhclxcdTAxMDNcIixcbiAgY29udmVydFRvQ29kZTogXCJDb252ZXJ0aXJlIFxceEVFbiBjb2RcIixcbiAgYnJpbmdGb3J3YXJkOiBcIkFkdWNlcmUgXFx4RUVuIHBsYW4gYXByb3BpYXRcIixcbiAgc2VuZFRvQmFjazogXCJUcmltaXRlcmUgXFx4RUVuIHVsdGltdWwgcGxhblwiLFxuICBicmluZ1RvRnJvbnQ6IFwiQWR1Y2VyZSBcXHhFRW4gcHJpbSBwbGFuXCIsXG4gIHNlbmRCYWNrd2FyZDogXCJUcmltaXRlcmUgXFx4RUVuIHBsYW4gc2VjdW5kYXJcIixcbiAgZGVsZXRlOiBcIlxcdTAyMTh0ZXJnZXJlXCIsXG4gIGNvcHlTdHlsZXM6IFwiQ29waWVyZSBzdGlsdXJpXCIsXG4gIHBhc3RlU3R5bGVzOiBcIkxpcGlyZSBzdGlsdXJpXCIsXG4gIHN0cm9rZTogXCJDb250dXJcIixcbiAgYmFja2dyb3VuZDogXCJGdW5kYWxcIixcbiAgZmlsbDogXCJVbXBsZXJlXCIsXG4gIHN0cm9rZVdpZHRoOiBcIkxcXHUwMTAzXFx1MDIxQmltZWEgY29udHVydWx1aVwiLFxuICBzdHJva2VTdHlsZTogXCJTdGlsdWwgY29udHVydWx1aVwiLFxuICBzdHJva2VTdHlsZV9zb2xpZDogXCJOZVxceEVFbnRyZXJ1cHRcIixcbiAgc3Ryb2tlU3R5bGVfZGFzaGVkOiBcIkxpbml1XFx1MDIxQmVcIixcbiAgc3Ryb2tlU3R5bGVfZG90dGVkOiBcIlB1bmN0YXRcIixcbiAgc2xvcHBpbmVzczogXCJBc3BlY3R1bCB0cmFzXFx1MDEwM3JpaVwiLFxuICBvcGFjaXR5OiBcIk9wYWNpdGF0ZVwiLFxuICB0ZXh0QWxpZ246IFwiQWxpbmllcmVhIHRleHR1bHVpXCIsXG4gIGVkZ2VzOiBcIk1hcmdpbmlcIixcbiAgc2hhcnA6IFwiQXNjdVxcdTAyMUJpdGVcIixcbiAgcm91bmQ6IFwiUm90dW5kZVwiLFxuICBhcnJvd2hlYWRzOiBcIlZcXHhFMnJmdXJpIGRlIHNcXHUwMTAzZ2VhdFxcdTAxMDNcIixcbiAgYXJyb3doZWFkX25vbmU6IFwiTmljaXVudWxcIixcbiAgYXJyb3doZWFkX2Fycm93OiBcIlNcXHUwMTAzZ2VhdFxcdTAxMDNcIixcbiAgYXJyb3doZWFkX2JhcjogXCJCYXJcXHUwMTAzXCIsXG4gIGFycm93aGVhZF9jaXJjbGU6IFwiQ2VyY1wiLFxuICBhcnJvd2hlYWRfY2lyY2xlX291dGxpbmU6IFwiQ2VyYyAoY29udHVyKVwiLFxuICBhcnJvd2hlYWRfdHJpYW5nbGU6IFwiVHJpdW5naGlcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlX291dGxpbmU6IFwiVHJpdW5naGkgKGNvbnR1cilcIixcbiAgYXJyb3doZWFkX2RpYW1vbmQ6IFwiUm9tYlwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZF9vdXRsaW5lOiBcIlJvbWIgKGNvbnR1cilcIixcbiAgZm9udFNpemU6IFwiRGltZW5zaXVuZSBmb250XCIsXG4gIGZvbnRGYW1pbHk6IFwiRmFtaWxpYSBkZSBmb250dXJpXCIsXG4gIGFkZFdhdGVybWFyazogXCJBZGF1Z1xcdTAxMDMgXFx1MjAxRVJlYWxpemF0IGN1IEV4Y2FsaWRyYXdcXHUyMDFEXCIsXG4gIGhhbmREcmF3bjogXCJTY3JpcyBkZSBtXFx4RTJuXFx1MDEwM1wiLFxuICBub3JtYWw6IFwiTm9ybWFsXCIsXG4gIGNvZGU6IFwiQ29kXCIsXG4gIHNtYWxsOiBcIk1pY1xcdTAxMDNcIixcbiAgbWVkaXVtOiBcIk1lZGllXCIsXG4gIGxhcmdlOiBcIk1hcmVcIixcbiAgdmVyeUxhcmdlOiBcIkZvYXJ0ZSBtYXJlXCIsXG4gIHNvbGlkOiBcIlBsaW5cXHUwMTAzXCIsXG4gIGhhY2h1cmU6IFwiSGFcXHUwMjE5dXJcXHUwMTAzXCIsXG4gIHppZ3phZzogXCJaaWd6YWdcIixcbiAgY3Jvc3NIYXRjaDogXCJIYVxcdTAyMTl1clxcdTAxMDMgdHJhbnN2ZXJzYWxcXHUwMTAzXCIsXG4gIHRoaW46IFwiU3ViXFx1MDIxQmlyZVwiLFxuICBib2xkOiBcIlxceENFbmdyb1xcdTAyMTlhdFxcdTAxMDNcIixcbiAgbGVmdDogXCJTdFxceEUybmdhXCIsXG4gIGNlbnRlcjogXCJDZW50cnVcIixcbiAgcmlnaHQ6IFwiRHJlYXB0YVwiLFxuICBleHRyYUJvbGQ6IFwiRXh0cmEgXFx4RUVuZ3JvXFx1MDIxOWF0XFx1MDEwM1wiLFxuICBhcmNoaXRlY3Q6IFwiQXJoaXRlY3RcIixcbiAgYXJ0aXN0OiBcIkFydGlzdFwiLFxuICBjYXJ0b29uaXN0OiBcIkNhcmljYXR1cmlzdFwiLFxuICBmaWxlVGl0bGU6IFwiTnVtZSBkZSBmaVxcdTAyMTlpZXJcIixcbiAgY29sb3JQaWNrZXI6IFwiU2VsZWN0b3IgZGUgY3Vsb2FyZVwiLFxuICBjYW52YXNDb2xvcnM6IFwiRm9sb3NpdGUgcGUgcFxceEUybnpcXHUwMTAzXCIsXG4gIGNhbnZhc0JhY2tncm91bmQ6IFwiRnVuZGFsdWwgcFxceEUybnplaVwiLFxuICBkcmF3aW5nQ2FudmFzOiBcIlBcXHhFMm56XFx1MDEwMyBwZW50cnUgZGVzZW5hdFwiLFxuICBsYXllcnM6IFwiU3RyYXR1cmlcIixcbiAgYWN0aW9uczogXCJBY1xcdTAyMUJpdW5pXCIsXG4gIGxhbmd1YWdlOiBcIkxpbWJcXHUwMTAzXCIsXG4gIGxpdmVDb2xsYWJvcmF0aW9uOiBcIkNvbGFib3JhcmUgXFx4RUVuIGRpcmVjdC4uLlwiLFxuICBkdXBsaWNhdGVTZWxlY3Rpb246IFwiRHVwbGljYXJlXCIsXG4gIHVudGl0bGVkOiBcIk5lZGVudW1pdFwiLFxuICBuYW1lOiBcIk51bWVcIixcbiAgeW91ck5hbWU6IFwiTnVtZWxlIHRcXHUwMTAzdVwiLFxuICBtYWRlV2l0aEV4Y2FsaWRyYXc6IFwiUmVhbGl6YXQgY3UgRXhjYWxpZHJhd1wiLFxuICBncm91cDogXCJHcnVwYXJlIHNlbGVjXFx1MDIxQmllXCIsXG4gIHVuZ3JvdXA6IFwiRGVncnVwYXJlIHNlbGVjXFx1MDIxQmllXCIsXG4gIGNvbGxhYm9yYXRvcnM6IFwiQ29sYWJvcmF0b3JpXCIsXG4gIHNob3dHcmlkOiBcIkFmaVxcdTAyMTlhcmUgZ3JpbFxcdTAxMDNcIixcbiAgYWRkVG9MaWJyYXJ5OiBcIkFkXFx1MDEwM3VnYXJlIGxhIGJpYmxpb3RlY1xcdTAxMDNcIixcbiAgcmVtb3ZlRnJvbUxpYnJhcnk6IFwiRWxpbWluYXJlIGRpbiBiaWJsaW90ZWNcXHUwMTAzXCIsXG4gIGxpYnJhcnlMb2FkaW5nTWVzc2FnZTogXCJTZSBcXHhFRW5jYXJjXFx1MDEwMyBiaWJsaW90ZWNhXFx1MjAyNlwiLFxuICBsaWJyYXJpZXM6IFwiUlxcdTAxMDNzZm9pZVxcdTAyMTl0ZSBiaWJsaW90ZWNpbGVcIixcbiAgbG9hZGluZ1NjZW5lOiBcIlNlIFxceEVFbmNhcmNcXHUwMTAzIHNjZW5hXFx1MjAyNlwiLFxuICBhbGlnbjogXCJBbGluaWVyZVwiLFxuICBhbGlnblRvcDogXCJBbGluaWVyZSBzdXNcIixcbiAgYWxpZ25Cb3R0b206IFwiQWxpbmllcmUgam9zXCIsXG4gIGFsaWduTGVmdDogXCJBbGluaWVyZSBsYSBzdFxceEUybmdhXCIsXG4gIGFsaWduUmlnaHQ6IFwiQWxpbmllcmUgbGEgZHJlYXB0YVwiLFxuICBjZW50ZXJWZXJ0aWNhbGx5OiBcIkNlbnRyYXJlIHZlcnRpY2FsXFx1MDEwM1wiLFxuICBjZW50ZXJIb3Jpem9udGFsbHk6IFwiQ2VudHJhcmUgb3Jpem9udGFsXFx1MDEwM1wiLFxuICBkaXN0cmlidXRlSG9yaXpvbnRhbGx5OiBcIkRpc3RyaWJ1aWUgb3Jpem9udGFsXCIsXG4gIGRpc3RyaWJ1dGVWZXJ0aWNhbGx5OiBcIkRpc3RyaWJ1aWUgdmVydGljYWxcIixcbiAgZmxpcEhvcml6b250YWw6IFwiUlxcdTAxMDNzdHVybmFyZSBvcml6b250YWxcXHUwMTAzXCIsXG4gIGZsaXBWZXJ0aWNhbDogXCJSXFx1MDEwM3N0dXJuYXJlIHZlcnRpY2FsXFx1MDEwM1wiLFxuICB2aWV3TW9kZTogXCJNb2QgZGUgdml6dWFsaXphcmVcIixcbiAgc2hhcmU6IFwiRGlzdHJpYnVpZVwiLFxuICBzaG93U3Ryb2tlOiBcIkFmaVxcdTAyMTlhcmUgc2VsZWN0b3IgY3Vsb2FyZSBjb250dXJcIixcbiAgc2hvd0JhY2tncm91bmQ6IFwiQWZpXFx1MDIxOWFyZSBzZWxlY3RvciBjdWxvYXJlIGZ1bmRhbFwiLFxuICB0b2dnbGVUaGVtZTogXCJDb211dGFyZSB0ZW1cXHUwMTAzXCIsXG4gIHBlcnNvbmFsTGliOiBcIkJpYmxpb3RlY2EgcGVyc29uYWxcXHUwMTAzXCIsXG4gIGV4Y2FsaWRyYXdMaWI6IFwiQmlibGlvdGVjYSBFeGNhbGlkcmF3XCIsXG4gIGRlY3JlYXNlRm9udFNpemU6IFwiTWljXFx1MDIxOW9yZWF6XFx1MDEwMyBkaW1lbnNpdW5lYSBmb250dWx1aVwiLFxuICBpbmNyZWFzZUZvbnRTaXplOiBcIk1cXHUwMTAzcmVcXHUwMjE5dGUgZGltZW5zaXVuZWEgZm9udHVsdWlcIixcbiAgdW5iaW5kVGV4dDogXCJEZWNvbmVjdGFyZSB0ZXh0XCIsXG4gIGJpbmRUZXh0OiBcIkxlZ2FyZSB0ZXh0IGRlIGNvbnRhaW5lclwiLFxuICBjcmVhdGVDb250YWluZXJGcm9tVGV4dDogXCJcXHhDRW5jYWRyYXJlIHRleHQgXFx4RUVudHItdW4gY29udGFpbmVyXCIsXG4gIGxpbms6IHtcbiAgICBlZGl0OiBcIkVkaXRhcmUgVVJMXCIsXG4gICAgZWRpdEVtYmVkOiBcIkVkaXRhcmUgVVJMIFxcdTAyMTlpIFxceEVFbmNvcnBvcmFyZVwiLFxuICAgIGNyZWF0ZTogXCJDcmVhcmUgVVJMXCIsXG4gICAgY3JlYXRlRW1iZWQ6IFwiQ3JlYXJlIFVSTCBcXHUwMjE5aSBcXHhFRW5jb3Jwb3JhcmVcIixcbiAgICBsYWJlbDogXCJVUkxcIixcbiAgICBsYWJlbEVtYmVkOiBcIlVSTCBcXHUwMjE5aSBcXHhFRW5jb3Jwb3JhcmVcIixcbiAgICBlbXB0eTogXCJOdSBlc3RlIHNldGF0IG5pY2l1biBVUkxcIlxuICB9LFxuICBsaW5lRWRpdG9yOiB7XG4gICAgZWRpdDogXCJFZGl0YXJlIGxpbmllXCIsXG4gICAgZXhpdDogXCJQXFx1MDEwM3JcXHUwMTAzc2lyZSBlZGl0b3IgZGUgbGluaWlcIlxuICB9LFxuICBlbGVtZW50TG9jazoge1xuICAgIGxvY2s6IFwiQmxvY2FyZVwiLFxuICAgIHVubG9jazogXCJEZWJsb2NhcmVcIixcbiAgICBsb2NrQWxsOiBcIkJsb2NhcmUgdG9hdGVcIixcbiAgICB1bmxvY2tBbGw6IFwiRGVibG9jYXJlIHRvYXRlXCJcbiAgfSxcbiAgc3RhdHVzUHVibGlzaGVkOiBcIlB1YmxpY2F0XCIsXG4gIHNpZGViYXJMb2NrOiBcIlBcXHUwMTAzc3RyZWF6XFx1MDEwMyBkZXNjaGlzXFx1MDEwMyBiYXJhIGxhdGVyYWxcXHUwMTAzXCIsXG4gIHNlbGVjdEFsbEVsZW1lbnRzSW5GcmFtZTogXCJcIixcbiAgcmVtb3ZlQWxsRWxlbWVudHNGcm9tRnJhbWU6IFwiXCIsXG4gIGV5ZURyb3BwZXI6IFwiQWxlZ2VyZSBjdWxvYXJlIGRpbiBwXFx4RTJuelxcdTAxMDNcIixcbiAgdGV4dFRvRGlhZ3JhbTogXCJUZXh0IGxhIGRpYWdyYW1cXHUwMTAzXCIsXG4gIHByb21wdDogXCJTb2xpY2l0YXJlXCJcbn07XG52YXIgbGlicmFyeSA9IHtcbiAgbm9JdGVtczogXCJOaWNpdW4gZWxlbWVudCBhZFxcdTAxMDN1Z2F0IFxceEVFbmNcXHUwMTAzLi4uXCIsXG4gIGhpbnRfZW1wdHlMaWJyYXJ5OiBcIlNlbGVjdGVhelxcdTAxMDMgdW4gZWxlbWVudCBkZSBwZSBwXFx4RTJuelxcdTAxMDMgcGVudHJ1IGEtbCBhZFxcdTAxMDN1Z2EgYWljaSBzYXUgaW5zdGFsZWF6XFx1MDEwMyBvIGJpYmxpb3RlY1xcdTAxMDMgZGluIGRlcG96aXR1bCBwdWJsaWMsIGRlIG1haSBqb3MuXCIsXG4gIGhpbnRfZW1wdHlQcml2YXRlTGlicmFyeTogXCJTZWxlY3RlYXpcXHUwMTAzIHVuIGVsZW1lbnQgZGUgcGUgcFxceEUybnpcXHUwMTAzIHBlbnRydSBhLWwgYWRcXHUwMTAzdWdhIGFpY2kuXCJcbn07XG52YXIgYnV0dG9ucyA9IHtcbiAgY2xlYXJSZXNldDogXCJSZXNldGFyZSBwXFx4RTJuelxcdTAxMDNcIixcbiAgZXhwb3J0SlNPTjogXCJFeHBvcnRhcmUgbGEgZmlcXHUwMjE5aWVyZVwiLFxuICBleHBvcnRJbWFnZTogXCJFeHBvcnRhcmUgaW1hZ2luZS4uLlwiLFxuICBleHBvcnQ6IFwiU2FsdmFyZSBcXHhFRW4uLi5cIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIkNvcGllcmUgXFx4RUVuIG1lbW9yaWEgdGVtcG9yYXJcXHUwMTAzXCIsXG4gIHNhdmU6IFwiU2FsdmFyZSBcXHhFRW4gZmlcXHUwMjE5aWVydWwgY3VyZW50XCIsXG4gIHNhdmVBczogXCJTYWx2YXJlIGNhXCIsXG4gIGxvYWQ6IFwiRGVzY2hpZGVyZVwiLFxuICBnZXRTaGFyZWFibGVMaW5rOiBcIk9iXFx1MDIxQmluZSBVUkwgcGFydGFqYWJpbFwiLFxuICBjbG9zZTogXCJcXHhDRW5jaGlkZXJlXCIsXG4gIHNlbGVjdExhbmd1YWdlOiBcIlNlbGVjdGFyZSBsaW1iXFx1MDEwM1wiLFxuICBzY3JvbGxCYWNrVG9Db250ZW50OiBcIkRlcnVsYXJlIFxceEVFbmFwb2kgbGEgY29uXFx1MDIxQmludXRcIixcbiAgem9vbUluOiBcIkFwcm9waWVyZVwiLFxuICB6b29tT3V0OiBcIkRlcFxcdTAxMDNydGFyZVwiLFxuICByZXNldFpvb206IFwiUmVzZXRhcmUgdHJhbnNmb2NhcmVcIixcbiAgbWVudTogXCJNZW5pdVwiLFxuICBkb25lOiBcIkVmZWN0dWF0XCIsXG4gIGVkaXQ6IFwiRWRpdFwiLFxuICB1bmRvOiBcIkFudWxhcmVcIixcbiAgcmVkbzogXCJSZWZhY2VyZVwiLFxuICByZXNldExpYnJhcnk6IFwiUmVzZXRhcmUgYmlibGlvdGVjXFx1MDEwM1wiLFxuICBjcmVhdGVOZXdSb29tOiBcIkNyZWFyZSBjYW1lclxcdTAxMDMgbm91XFx1MDEwM1wiLFxuICBmdWxsU2NyZWVuOiBcIkVjcmFuIGNvbXBsZXRcIixcbiAgZGFya01vZGU6IFwiTW9kIFxceEVFbnR1bmVjYXRcIixcbiAgbGlnaHRNb2RlOiBcIk1vZCBsdW1pbm9zXCIsXG4gIHplbk1vZGU6IFwiTW9kIHplblwiLFxuICBvYmplY3RzU25hcE1vZGU6IFwiQW5jb3JhcmUgbGEgb2JpZWN0ZVwiLFxuICBleGl0WmVuTW9kZTogXCJJZVxcdTAyMTlpcmUgZGluIG1vZHVsIHplblwiLFxuICBjYW5jZWw6IFwiQW51bGFyZVwiLFxuICBjbGVhcjogXCJcXHUwMjE4dGVyZ2VyZVwiLFxuICByZW1vdmU6IFwiRWxpbWluYXJlXCIsXG4gIGVtYmVkOiBcIkNvbXV0YXJlIFxceEVFbmNvcnBvcmFyZVwiLFxuICBwdWJsaXNoTGlicmFyeTogXCJQdWJsaWNhcmVcIixcbiAgc3VibWl0OiBcIlRyaW1pdGVyZVwiLFxuICBjb25maXJtOiBcIkNvbmZpcm1hcmVcIixcbiAgZW1iZWRkYWJsZUludGVyYWN0aW9uQnV0dG9uOiBcIkNsaWMgcGVudHJ1IGludGVyYWNcXHUwMjFCaW9uYXJlXCJcbn07XG52YXIgYWxlcnRzID0ge1xuICBjbGVhclJlc2V0OiBcIkFjZWFzdFxcdTAxMDMgb3BcXHUwMjFCaXVuZSB2YSBcXHUwMjE5dGVyZ2UgXFx4RUVudHJlYWdhIHBcXHhFMm56XFx1MDEwMy4gQ29uZmlybWk/XCIsXG4gIGNvdWxkTm90Q3JlYXRlU2hhcmVhYmxlTGluazogXCJOdSBzLWEgcHV0dXQgY3JlYSB1biBVUkwgcGFydGFqYWJpbC5cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rVG9vQmlnOiBcIk51IHMtYSBwdXR1dCBjcmVhIHVuIFVSTCBwYXJ0YWphYmlsOiBzY2VuYSBlc3RlIHByZWEgbWFyZVwiLFxuICBjb3VsZE5vdExvYWRJbnZhbGlkRmlsZTogXCJGaVxcdTAyMTlpZXJ1bCBpbnZhbGlkIG51IGEgcHV0dXQgZmkgXFx4RUVuY1xcdTAxMDNyY2F0XCIsXG4gIGltcG9ydEJhY2tlbmRGYWlsZWQ6IFwiSW1wb3J0YXJlYSBkZSBsYSBuaXZlbCBkZSBzZXJ2ZXIgYSBlXFx1MDIxOXVhdC5cIixcbiAgY2Fubm90RXhwb3J0RW1wdHlDYW52YXM6IFwiTnUgc2UgcG9hdGUgZXhwb3J0YSBwXFx4RTJuemEgZ29hbFxcdTAxMDMuXCIsXG4gIGNvdWxkTm90Q29weVRvQ2xpcGJvYXJkOiBcIk51IHMtYSBwdXR1dCBjb3BpYSBcXHhFRW4gbWVtb3JpYSB0ZW1wb3JhclxcdTAxMDMuXCIsXG4gIGRlY3J5cHRGYWlsZWQ6IFwiRGF0ZWxlIG51IGF1IHB1dHV0IGZpIGRlY3JpcHRhdGUuXCIsXG4gIHVwbG9hZGVkU2VjdXJseTogXCJcXHhDRW5jXFx1MDEwM3JjYXJlYSBhIGZvc3Qgc2VjdXJpemF0XFx1MDEwMyBwcmluIGNyaXB0YXJlIGludGVncmFsXFx1MDEwMywgXFx4RUVuc2VtblxceEUybmQgY1xcdTAxMDMgc2VydmVydWwgRXhjYWxpZHJhdyBcXHUwMjE5aSB0ZXJcXHUwMjFCaWkgbnUgcG90IGNpdGkgY29uXFx1MDIxQmludXR1bC5cIixcbiAgbG9hZFNjZW5lT3ZlcnJpZGVQcm9tcHQ6IFwiXFx4Q0VuY1xcdTAxMDNyY2FyZWEgZGVzZW51bHVpIGV4dGVybiB2YSBcXHhFRW5sb2N1aSBjb25cXHUwMjFCaW51dHVsIGV4aXN0ZW50LiBEb3JlXFx1MDIxOXRpIHNcXHUwMTAzIGNvbnRpbnVpP1wiLFxuICBjb2xsYWJTdG9wT3ZlcnJpZGVQcm9tcHQ6IFwiT3ByaXJlYSBzZXNpdW5paSB2YSBzdXByYXNjcmllIGRlc2VudWwgYW50ZXJpb3Igc3RvY2F0IGxvY2FsLiBDb25maXJtaSBhbGVnZXJlYT9cXG5cXG4oRGFjXFx1MDEwMyB2cmVpIHNcXHUwMTAzIHBcXHUwMTAzc3RyZXppIGRlc2VudWwgbG9jYWwsIHB1ciBcXHUwMjE5aSBzaW1wbHUgXFx4RUVuY2hpZGUgZmlsYSBuYXZpZ2F0b3J1bHVpIFxceEVFbiBzY2hpbWIuKVwiLFxuICBlcnJvckFkZGluZ1RvTGlicmFyeTogXCJFbGVtZW50dWwgbnUgYSBwdXR1dCBmaSBhZFxcdTAxMDN1Z2F0IFxceEVFbiBiaWJsaW90ZWNcXHUwMTAzXCIsXG4gIGVycm9yUmVtb3ZpbmdGcm9tTGlicmFyeTogXCJFbGVtZW50dWwgbnUgYSBwdXR1dCBmaSBlbGltaW5hdCBkaW4gYmlibGlvdGVjXFx1MDEwM1wiLFxuICBjb25maXJtQWRkTGlicmFyeTogXCJBY2Vhc3RcXHUwMTAzIGFjXFx1MDIxQml1bmUgdmEgYWRcXHUwMTAzdWdhIHt7bnVtU2hhcGVzfX0gZm9ybVxcdTAxMDMoZSkgbGEgYmlibGlvdGVjYSB0YS4gQ29uZmlybWk/XCIsXG4gIGltYWdlRG9lc05vdENvbnRhaW5TY2VuZTogXCJBY2Vhc3RcXHUwMTAzIGltYWdpbmUgbnUgcGFyZSBzXFx1MDEwMyBjb25cXHUwMjFCaW5cXHUwMTAzIGRhdGUgZGUgc2NlblxcdTAxMDMuIEFpIGFjdGl2YXQgXFx4RUVuY29ycG9yYXJlYSBzY2VuZWkgXFx4RUVuIHRpbXB1bCBleHBvcnR1bHVpP1wiLFxuICBjYW5ub3RSZXN0b3JlRnJvbUltYWdlOiBcIlNjZW5hIG51IGEgcHV0dXQgZmkgcmVzdGF1cmF0XFx1MDEwMyBkaW4gYWNlc3QgZmlcXHUwMjE5aWVyIGRlIGltYWdpbmVcIixcbiAgaW52YWxpZFNjZW5lVXJsOiBcIlNjZW5hIG51IGEgcHV0dXQgZmkgaW1wb3J0YXRcXHUwMTAzIGRpbiBVUkwtdWwgZnVybml6YXQuIEVzdGUgZmllIGluY29yZWN0IGZvcm1hdFxcdTAxMDMsIGZpZSBudSBjb25cXHUwMjFCaW5lIGRhdGUgSlNPTiBFeGNhbGlkcmF3IHZhbGlkZS5cIixcbiAgcmVzZXRMaWJyYXJ5OiBcIkFjZWFzdFxcdTAxMDMgb3BcXHUwMjFCaXVuZSB2YSBlbGltaW5hIGNvblxcdTAyMUJpbnV0dWwgZGluIGJpYmxpb3RlY1xcdTAxMDMuIENvbmZpcm1pP1wiLFxuICByZW1vdmVJdGVtc0Zyb21zTGlicmFyeTogXCJcXHUwMjE4dGVyZ2kge3tjb3VudH19IGVsZW1lbnQoZSkgZGluIGJpYmxpb3RlY1xcdTAxMDM/XCIsXG4gIGludmFsaWRFbmNyeXB0aW9uS2V5OiBcIkNoZWlhIGRlIGNyaXB0YXJlIHRyZWJ1aWUgc1xcdTAxMDMgYWliXFx1MDEwMyAyMiBkZSBjYXJhY3RlcmUuIENvbGFib3JhcmVhIFxceEVFbiBkaXJlY3QgZXN0ZSBkZXphY3RpdmF0XFx1MDEwMy5cIixcbiAgY29sbGFiT2ZmbGluZVdhcm5pbmc6IFwiTnUgZXN0ZSBkaXNwb25pYmlsXFx1MDEwMyBuaWNpbyBjb25leGl1bmUgbGEgaW50ZXJuZXQuXFxuTW9kaWZpY1xcdTAxMDNyaWxlIG51IHZvciBmaSBzYWx2YXRlIVwiXG59O1xudmFyIGVycm9ycyA9IHtcbiAgdW5zdXBwb3J0ZWRGaWxlVHlwZTogXCJUaXAgZGUgZmlcXHUwMjE5aWVyIG5lYWNjZXB0YXQuXCIsXG4gIGltYWdlSW5zZXJ0RXJyb3I6IFwiSW1hZ2luZWEgbnUgYSBwdXR1dCBmaSBpbnRyb2R1c1xcdTAxMDMuIFJlXFx4RUVuY2VhcmNcXHUwMTAzIG1haSB0XFx4RTJyeml1Li4uXCIsXG4gIGZpbGVUb29CaWc6IFwiRmlcXHUwMjE5aWVydWwgZXN0ZSBwcmVhIG1hcmUuIERpbWVuc2l1bmVhIG1heGltXFx1MDEwMyBwZXJtaXNcXHUwMTAzIGVzdGUgZGUge3ttYXhTaXplfX0uXCIsXG4gIHN2Z0ltYWdlSW5zZXJ0RXJyb3I6IFwiSW1hZ2luZWEgU1ZHIG51IGEgcHV0dXQgZmkgaW50cm9kdXMuIE1hcmNhanVsIFNWRyBwYXJlIGludmFsaWQuXCIsXG4gIGZhaWxlZFRvRmV0Y2hJbWFnZTogXCJQcmVsdWFyZWEgaW1hZ2luaWkgYSBlXFx1MDIxOXVhdC5cIixcbiAgaW52YWxpZFNWR1N0cmluZzogXCJTVkcgaW52YWxpZC5cIixcbiAgY2Fubm90UmVzb2x2ZUNvbGxhYlNlcnZlcjogXCJOdSBhIHB1dHV0IGZpIHJlYWxpemF0XFx1MDEwMyBjb25leGl1bmVhIGxhIHNlcnZlcnVsIGRlIGNvbGFib3JhcmUuIFJlXFx4RUVuY2FyY1xcdTAxMDMgcGFnaW5hIFxcdTAyMTlpIFxceEVFbmNlYXJjXFx1MDEwMyBkaW4gbm91LlwiLFxuICBpbXBvcnRMaWJyYXJ5RXJyb3I6IFwiQmlibGlvdGVjYSBudSBhIHB1dHV0IGZpIFxceEVFbmNcXHUwMTAzcmNhdFxcdTAxMDNcIixcbiAgY29sbGFiU2F2ZUZhaWxlZDogXCJOdSBzLWEgcHV0dXQgc2FsdmEgXFx4RUVuIGJhemEgZGUgZGF0ZSBsYSBuaXZlbCBkZSBzZXJ2ZXIuIERhY1xcdTAxMDMgcHJvYmxlbWVsZSBwZXJzaXN0XFx1MDEwMywgYXIgdHJlYnVpIHNcXHUwMTAzIHNhbHZlemkgZmlcXHUwMjE5aWVydWwgbGEgbml2ZWwgbG9jYWwgcGVudHJ1IGEgdGUgYXNpZ3VyYSBjXFx1MDEwMyBudSBcXHhFRVxcdTAyMUJpIHBpZXJ6aSBtdW5jYS5cIixcbiAgY29sbGFiU2F2ZUZhaWxlZF9zaXplRXhjZWVkZWQ6IFwiTnUgcy1hIHB1dHV0IHNhbHZhIFxceEVFbiBiYXphIGRlIGRhdGUgbGEgbml2ZWwgZGUgc2VydmVyLCBcXHhFRW50cnVjXFx4RTJ0IHNlIHBhcmUgY1xcdTAxMDMgcFxceEUybnphIGVzdGUgcHJlYSBtYXJlLiBBciB0cmVidWkgc1xcdTAxMDMgc2FsdmV6aSBmaVxcdTAyMTlpZXJ1bCBsYSBuaXZlbCBsb2NhbCBwZW50cnUgYSB0ZSBhc2lndXJhIGNcXHUwMTAzIG51IFxceEVFXFx1MDIxQmkgcGllcnppIG11bmNhLlwiLFxuICBpbWFnZVRvb2xOb3RTdXBwb3J0ZWQ6IFwiSW1hZ2luaWxlIHN1bnQgZGV6YWN0aXZhdGUuXCIsXG4gIGJyYXZlX21lYXN1cmVfdGV4dF9lcnJvcjoge1xuICAgIGxpbmUxOiBcIlNlIHBhcmUgY1xcdTAxMDMgZm9sb3NlXFx1MDIxOXRpIG5hdmlnYXRvcnVsIEJyYXZlIGN1IG9wXFx1MDIxQml1bmVhIDxib2xkPnN0cmljdFxcdTAxMDMgcGVudHJ1IGJsb2NhcmVhIGFtcHJlbnRcXHUwMTAzcmlpPC9ib2xkPi5cIixcbiAgICBsaW5lMjogXCJBY2VzdCBsdWNydSBwb2F0ZSBkdWNlIGxhIFxceEVFbnRyZXJ1cGVyZWEgPGJvbGQ+ZWxlbWVudGVsb3IgdGV4dDwvYm9sZD4gZGluIGRlc2VuZS5cIixcbiAgICBsaW5lMzogXCJcXHhDRVxcdTAyMUJpIHJlY29tYW5kXFx1MDEwM20gZmVybSBzXFx1MDEwMyBkZXphY3RpdmV6aSBhY2Vhc3RcXHUwMTAzIHNldGFyZS4gUG9cXHUwMjFCaSB1cm1hIDxsaW5rPmFjZVxcdTAyMTl0aSBwYVxcdTAyMTlpPC9saW5rPiBwZW50cnUgYSBmYWNlIGFjZXN0IGx1Y3J1LlwiLFxuICAgIGxpbmU0OiBcIkRhY1xcdTAxMDMgZGV6YWN0aXZhcmVhIGFjZXN0ZWkgc2V0XFx1MDEwM3JpIG51IGR1Y2UgbGEgcmVtZWRpZXJlYSBhZmlcXHUwMjE5XFx1MDEwM3JpaSBlbGVtZW50ZWxvciB0ZXh0LCBkZXNjaGlkZSB1biB0aWNoZXQgZGUgPGlzc3VlTGluaz5wcm9ibGVtXFx1MDEwMzwvaXNzdWVMaW5rPiBwZSBwYWdpbmEgbm9hc3RyXFx1MDEwMyBkZSBHaXRIdWIgc2F1IHNjcmllLW5lIHBlIDxkaXNjb3JkTGluaz5EaXNjb3JkPC9kaXNjb3JkTGluaz5cIlxuICB9LFxuICBsaWJyYXJ5RWxlbWVudFR5cGVFcnJvcjoge1xuICAgIGVtYmVkZGFibGU6IFwiRWxlbWVudGVsZSBcXHhFRW5jb3Jwb3JhYmlsZSBudSBwb3QgZmkgYWRcXHUwMTAzdWdhdGUgbGEgYmlibGlvdGVjXFx1MDEwMy5cIixcbiAgICBpZnJhbWU6IFwiRWxlbWVudGVsZSBpRnJhbWUgbnUgcG90IGZpIGFkXFx1MDEwM3VnYXRlIGxhIGJpYmxpb3RlY1xcdTAxMDMuXCIsXG4gICAgaW1hZ2U6IFwiXFx4Q0VuIGN1clxceEUybmQgdm9yIHB1dGVhIGZpIGFkXFx1MDEwM3VnYXRlIGltYWdpbmkgXFx4RUVuIGJpYmxpb3RlY1xcdTAxMDMhXCJcbiAgfSxcbiAgYXN5bmNQYXN0ZUZhaWxlZE9uUmVhZDogXCJMaXBpcmVhIG51IGEgcHV0dXQgZmkgZWZlY3R1YXRcXHUwMTAzIChudSBzLWEgcHV0dXQgY2l0aXQgZGluIG1lbW9yaWEgdGVtcG9yYXJcXHUwMTAzIGEgc2lzdGVtdWx1aSkuXCIsXG4gIGFzeW5jUGFzdGVGYWlsZWRPblBhcnNlOiBcIkxpcGlyZWEgbnUgYSBwdXR1dCBmaSBlZmVjdHVhdFxcdTAxMDMuXCIsXG4gIGNvcHlUb1N5c3RlbUNsaXBib2FyZEZhaWxlZDogXCJOdSBzLWEgcHV0dXQgY29waWEgXFx4RUVuIG1lbW9yaWEgdGVtcG9yYXJcXHUwMTAzLlwiXG59O1xudmFyIHRvb2xCYXIgPSB7XG4gIHNlbGVjdGlvbjogXCJTZWxlY1xcdTAyMUJpZVwiLFxuICBpbWFnZTogXCJJbnRyb2R1Y2VyZSBpbWFnaW5lXCIsXG4gIHJlY3RhbmdsZTogXCJEcmVwdHVuZ2hpXCIsXG4gIGRpYW1vbmQ6IFwiUm9tYlwiLFxuICBlbGxpcHNlOiBcIkVsaXBzXFx1MDEwM1wiLFxuICBhcnJvdzogXCJTXFx1MDEwM2dlYXRcXHUwMTAzXCIsXG4gIGxpbmU6IFwiTGluaWVcIixcbiAgZnJlZWRyYXc6IFwiRGVzZW5hcmVcIixcbiAgdGV4dDogXCJUZXh0XCIsXG4gIGxpYnJhcnk6IFwiQmlibGlvdGVjXFx1MDEwM1wiLFxuICBsb2NrOiBcIk1lblxcdTAyMUJpbmUgYWN0aXYgaW5zdHJ1bWVudHVsIHNlbGVjdGF0IGR1cFxcdTAxMDMgZGVzZW5hcmVcIixcbiAgcGVuTW9kZTogXCJNb2Qgc3RpbG91IFxcdTIwMTMgXFx4RUVtcGllZGljXFx1MDEwMyBhdGluZ2VyZWFcIixcbiAgbGluazogXCJBZFxcdTAxMDN1Z2FyZS9hY3R1YWxpemFyZSBVUkwgcGVudHJ1IGZvcm1hIHNlbGVjdGF0XFx1MDEwM1wiLFxuICBlcmFzZXI6IFwiUmFkaWVyXFx1MDEwM1wiLFxuICBmcmFtZTogXCJcIixcbiAgbWFnaWNmcmFtZTogXCJTdHJ1Y3R1clxcdTAxMDMtZGUtZmlyZSBsYSBjb2RcIixcbiAgZW1iZWRkYWJsZTogXCJcXHhDRW5jb3Jwb3JhcmUgd2ViXCIsXG4gIGxhc2VyOiBcIkluZGljYXRvciBsYXNlclwiLFxuICBoYW5kOiBcIk1cXHhFMm5cXHUwMTAzIChpbnN0cnVtZW50IGRlIHBhbm9yYW1hcmUpXCIsXG4gIGV4dHJhVG9vbHM6IFwiXCIsXG4gIG1lcm1haWRUb0V4Y2FsaWRyYXc6IFwiTWVybWFpZCBsYSBFeGNhbGlkcmF3XCIsXG4gIG1hZ2ljU2V0dGluZ3M6IFwiU2V0XFx1MDEwM3JpIElBXCJcbn07XG52YXIgaGVhZGluZ3MgPSB7XG4gIGNhbnZhc0FjdGlvbnM6IFwiQWNcXHUwMjFCaXVuaSBwZW50cnUgcFxceEUybnpcXHUwMTAzXCIsXG4gIHNlbGVjdGVkU2hhcGVBY3Rpb25zOiBcIkFjXFx1MDIxQml1bmkgcGVudHJ1IGZvcm1hIHNlbGVjdGF0XFx1MDEwM1wiLFxuICBzaGFwZXM6IFwiRm9ybWVcIlxufTtcbnZhciBoaW50cyA9IHtcbiAgY2FudmFzUGFubmluZzogXCJQZW50cnUgYSBtdXRhIHBcXHhFMm56XFx1MDEwMywgXFx1MDIxQmluZSBhcFxcdTAxMDNzYXRcXHUwMTAzIHJvdGlcXHUwMjFCYSBtYXVzdWx1aSBzYXUgYmFyYSBkZSBzcGFcXHUwMjFCaXUgc2F1IGZvbG9zZVxcdTAyMTl0ZSBpbnN0cnVtZW50dWwgXFx4RUVuIGZvcm1cXHUwMTAzIGRlIG1cXHhFMm5cXHUwMTAzXCIsXG4gIGxpbmVhckVsZW1lbnQ6IFwiRFxcdTAxMDMgY2xpYyBwZW50cnUgYSBjcmVhIG1haSBtdWx0ZSBwdW5jdGUsIGdsaXNlYXpcXHUwMTAzIHBlbnRydSBhIGZvcm1hIG8gc2luZ3VyXFx1MDEwMyBsaW5pZVwiLFxuICBmcmVlRHJhdzogXCJEXFx1MDEwMyBjbGljIHBlIHBcXHhFMm56XFx1MDEwMyBcXHUwMjE5aSBnbGlzZWF6XFx1MDEwMyBjdXJzb3J1bCwgYXBvaSBlbGliZXJlYXpcXHUwMTAzLWwgY1xceEUybmQgYWkgdGVybWluYXRcIixcbiAgdGV4dDogXCJTZmF0OiBwb1xcdTAyMUJpIGFkXFx1MDEwM3VnYSB0ZXh0IFxcdTAyMTlpIGRcXHhFMm5kIGR1Ymx1IGNsaWMgb3JpdW5kZSBjdSBpbnN0cnVtZW50dWwgZGUgc2VsZWNcXHUwMjFCaWVcIixcbiAgZW1iZWRkYWJsZTogXCJEXFx1MDEwMyBjbGljIFxcdTAyMTlpIHRyYWdlIHBlbnRydSBhIGNyZWEgdW4gY29kIGRlIFxceEVFbmNvcnBvcmFyZSBkZSBwYWdpblxcdTAxMDMgd2ViXCIsXG4gIHRleHRfc2VsZWN0ZWQ6IFwiRFxcdTAxMDMgZHVibHUgY2xpYyBzYXUgYXBhc1xcdTAxMDMgdGFzdGEgRW50ZXIgcGVudHJ1IGEgZWRpdGEgdGV4dHVsXCIsXG4gIHRleHRfZWRpdGluZzogXCJBcGFzXFx1MDEwMyB0YXN0YSBFc2NhcGUgc2F1IEN0cmwgc2F1IENtZCArIEVudGVyIHBlbnRydSBhIGZpbmFsaXphIGVkaXRhcmVhXCIsXG4gIGxpbmVhckVsZW1lbnRNdWx0aTogXCJEXFx1MDEwMyBjbGljIHBlIHVsdGltdWwgcHVuY3Qgc2F1IGFwYXNcXHUwMTAzIHRhc3RhIEVzY2FwZSBzYXUgdGFzdGEgRW50ZXIgcGVudHJ1IGEgdGVybWluYVwiLFxuICBsb2NrQW5nbGU6IFwiUG9cXHUwMjFCaSBjb25zdHJcXHhFMm5nZSB1bmdoaXVsIHByaW4gXFx1MDIxQmluZXJlYSBhcFxcdTAxMDNzYXRcXHUwMTAzIGEgdGFzdGVpIFNISUZUXCIsXG4gIHJlc2l6ZTogXCJQb1xcdTAyMUJpIGNvbnN0clxceEUybmdlIHByb3BvclxcdTAyMUJpaWxlLCBcXHUwMjFCaW5cXHhFMm5kIGFwXFx1MDEwM3NhdFxcdTAxMDMgdGFzdGEgU0hJRlQgXFx4RUVuIHRpbXAgY2UgcmVkaW1lbnNpb25lemksXFxuXFx1MDIxQmluZSBhcFxcdTAxMDNzYXRcXHUwMTAzIHRhc3RhIEFMVCBwZW50cnUgYSByZWRpbWVuc2lvbmEgZGUgbGEgY2VudHJ1XCIsXG4gIHJlc2l6ZUltYWdlOiBcIlBvXFx1MDIxQmkgcmVkaW1lbnNpb25hIGxpYmVyIFxcdTAyMUJpblxceEUybmQgYXBcXHUwMTAzc2F0XFx1MDEwMyB0YXN0YSBTSElGVCxcXG5cXHUwMjFCaW5lIGFwXFx1MDEwM3NhdFxcdTAxMDMgdGFzdGEgQUxUIHBlbnRydSBhIHJlZGltZW5zaW9uYSBkaW4gY2VudHJ1XCIsXG4gIHJvdGF0ZTogXCJQb1xcdTAyMUJpIGNvbnN0clxceEUybmdlIHVuZ2hpdXJpbGUsIFxcdTAyMUJpblxceEUybmQgYXBcXHUwMTAzc2F0XFx1MDEwMyB0YXN0YSBTSElGVCBcXHhFRW4gdGltcCBjZSByb3RlXFx1MDIxOXRpXCIsXG4gIGxpbmVFZGl0b3JfaW5mbzogXCJcXHUwMjFBaW5lIGFwXFx1MDEwM3NhdFxcdTAxMDMgdGFzdGEgQ3RybCBzYXUgQ21kIFxcdTAyMTlpIGRcXHUwMTAzIGR1Ymx1IGNsaWMgc2F1IGFwYXNcXHUwMTAzIHRhc3RhIEN0cmwgc2F1IENtZCArIEVudGVyIHBlbnRydSBhIGVkaXRhIHB1bmN0ZVwiLFxuICBsaW5lRWRpdG9yX3BvaW50U2VsZWN0ZWQ6IFwiQXBhc1xcdTAxMDMgdGFzdGEgRGVsZXRlIHBlbnRydSBhIGVsaW1pbmEgcHVuY3RlbGUsXFxuY29tYmluYVxcdTAyMUJpYSBkZSB0YXN0ZSBDdHJsIHNhdSBDbWQgKyBEIHBlbnRydSBhIGxlIGR1cGxpY2Egc2F1IGdsaXNlYXpcXHUwMTAzLWxlIHBlbnRydSBhIGxlIHNjaGltYmEgcG96aVxcdTAyMUJpYVwiLFxuICBsaW5lRWRpdG9yX25vdGhpbmdTZWxlY3RlZDogXCJTZWxlY3RlYXpcXHUwMTAzIHVuIHB1bmN0IHBlbnRydSBhLWwgZWRpdGEgKFxcdTAyMUJpbmUgYXBcXHUwMTAzc2F0XFx1MDEwMyB0YXN0YSBTSElGVCBwZW50cnUgYSBzZWxlY3RhIG1haSBtdWx0ZSksXFxuc2F1IFxcdTAyMUJpbmUgYXBcXHUwMTAzc2F0XFx1MDEwMyB0YXN0YSBBbHQgXFx1MDIxOWkgZFxcdTAxMDMgY2xpYyBwZW50cnUgYSBhZFxcdTAxMDN1Z2EgcHVuY3RlIG5vaVwiLFxuICBwbGFjZUltYWdlOiBcIkRcXHUwMTAzIGNsaWMgcGVudHJ1IGEgcG96aVxcdTAyMUJpb25hIGltYWdpbmVhIHNhdSBkXFx1MDEwMyBjbGljIFxcdTAyMTlpIGdsaXNlYXpcXHUwMTAzIHBlbnRydSBhIHNldGEgbWFudWFsIGRpbWVuc2l1bmVhIGltYWdpbmlpXCIsXG4gIHB1Ymxpc2hMaWJyYXJ5OiBcIlB1YmxpY1xcdTAxMDMgcHJvcHJpYSBiaWJsaW90ZWNcXHUwMTAzXCIsXG4gIGJpbmRUZXh0VG9FbGVtZW50OiBcIkFwYXNcXHUwMTAzIHRhc3RhIEVudGVyIHBlbnRydSBhIGFkXFx1MDEwM3VnYSB0ZXh0XCIsXG4gIGRlZXBCb3hTZWxlY3Q6IFwiXFx1MDIxQWluZSBhcFxcdTAxMDNzYXRcXHUwMTAzIHRhc3RhIEN0cmwgc2F1IENtZCBwZW50cnUgYSBlZmVjdHVhIHNlbGVjdGFyZWEgZGUgYWRcXHhFMm5jaW1lIFxcdTAyMTlpIHBlbnRydSBhIHByZXZlbmkgZ2xpc2FyZWFcIixcbiAgZXJhc2VyUmV2ZXJ0OiBcIlxcdTAyMUFpbmUgYXBcXHUwMTAzc2F0XFx1MDEwMyB0YXN0YSBBbHQgcGVudHJ1IGEgYW51bGEgZWxlbWVudGVsZSBtYXJjYXRlIHBlbnRydSBcXHUwMjE5dGVyZ2VyZVwiLFxuICBmaXJlZm94X2NsaXBib2FyZF93cml0ZTogXCJBY2Vhc3RcXHUwMTAzIGNhcmFjdGVyaXN0aWNcXHUwMTAzIHBvYXRlIGZpIHByb2JhYmlsIGFjdGl2YXRcXHUwMTAzIHByaW4gc2V0YXJlYSBwcmVmZXJpblxcdTAyMUJlaSBcXHUyMDFFZG9tLmV2ZW50cy5hc3luY0NsaXBib2FyZC5jbGlwYm9hcmRJdGVtXFx1MjAxRCBjYSBcXHUyMDFFdHJ1ZVxcdTIwMUQuIFBlbnRydSBhIHNjaGltYmEgcHJlZmVyaW5cXHUwMjFCZWxlIG5hdmlnYXRvcnVsdWkgXFx4RUVuIEZpcmVmb3gsIGFjY2VzZWF6XFx1MDEwMyBwYWdpbmEgXFx1MjAxRWFib3V0OmNvbmZpZ1xcdTIwMUQuXCIsXG4gIGRpc2FibGVTbmFwcGluZzogXCJcXHUwMjFBaW5lIGFwXFx1MDEwM3NhdCBDdHJsT3JDbWQgcGVudHJ1IGEgZGV6YWN0aXZhIGFuY29yYXJlYVwiXG59O1xudmFyIGNhbnZhc0Vycm9yID0ge1xuICBjYW5ub3RTaG93UHJldmlldzogXCJOdSBzZSBwb2F0ZSBhZmlcXHUwMjE5YSBwcmV2aXp1YWxpemFyZWFcIixcbiAgY2FudmFzVG9vQmlnOiBcIlBcXHhFMm56YSBwb2F0ZSBmaSBwcmVhIG1hcmUuXCIsXG4gIGNhbnZhc1Rvb0JpZ1RpcDogXCJTZmF0OiBcXHhFRW5jZWFyY1xcdTAxMDMgc1xcdTAxMDMgYXByb3BpaSBwdVxcdTAyMUJpbiBtYWkgbXVsdCBlbGVtZW50ZWxlIGNlbGUgbWFpIFxceEVFbmRlcFxcdTAxMDNydGF0ZS5cIlxufTtcbnZhciBlcnJvclNwbGFzaCA9IHtcbiAgaGVhZGluZ01haW46IFwiQSBhcFxcdTAxMDNydXQgbyBlcm9hcmUuIFxceENFbmNlYXJjXFx1MDEwMyA8YnV0dG9uPnNcXHUwMTAzIHJlXFx4RUVuY2FyY2kgcGFnaW5hPC9idXR0b24+LlwiLFxuICBjbGVhckNhbnZhc01lc3NhZ2U6IFwiRGFjXFx1MDEwMyByZVxceEVFbmNcXHUwMTAzcmNhcmVhIG51IGZ1bmNcXHUwMjFCaW9uZWF6XFx1MDEwMywgXFx4RUVuY2VhcmNcXHUwMTAzIDxidXR0b24+c1xcdTAxMDMgXFx1MDIxOXRlcmdpIHBcXHhFMm56YTwvYnV0dG9uPi5cIixcbiAgY2xlYXJDYW52YXNDYXZlYXQ6IFwiIEFjZXN0IGx1Y3J1IHZhIGR1Y2UgbGEgcGllcmRlcmVhIHByb2dyZXN1bHVpIFwiLFxuICB0cmFja2VkVG9TZW50cnk6IFwiRXJvYXJlYSBjdSBpZGVudGlmaWNhdG9ydWwge3tldmVudElkfX0gYSBmb3N0IHVybVxcdTAxMDNyaXRcXHUwMTAzIFxceEVFbiBzaXN0ZW11bCBub3N0cnUuXCIsXG4gIG9wZW5Jc3N1ZU1lc3NhZ2U6IFwiQW0gbHVhdCBtXFx1MDEwM3N1cmkgZGUgcHJlY2F1XFx1MDIxQmllIHBlbnRydSBhIG51IGluY2x1ZGUgaW5mb3JtYVxcdTAyMUJpaSBkZXNwcmUgc2NlblxcdTAxMDMgXFx4RUVuIGVyb2FyZS4gRGFjXFx1MDEwMyBzY2VuYSBudSBlc3RlIHByaXZhdFxcdTAxMDMsIG9mZXJcXHUwMTAzLW5lIG1haSBtdWx0ZSBpbmZvcm1hXFx1MDIxQmlpIFxceEVFbiA8YnV0dG9uPm1vbml0b3J1bCBub3N0cnUgcGVudHJ1IGVyb3JpPC9idXR0b24+LiBJbmNsdWRlIGluZm9ybWFcXHUwMjFCaWlsZSBkZSBtYWkgam9zIGNvcGlpbmR1LWxlIFxcdTAyMTlpIGxpcGluZHUtbGUgXFx4RUVuIHRpY2hldHVsIGN1IHByb2JsZW1cXHUwMTAzIGRlIHBlIEdpdEh1Yi5cIixcbiAgc2NlbmVDb250ZW50OiBcIkNvblxcdTAyMUJpbnV0dWwgc2NlbmVpOlwiXG59O1xudmFyIHJvb21EaWFsb2cgPSB7XG4gIGRlc2NfaW50cm86IFwiUG9cXHUwMjFCaSBpbnZpdGEgYWx0ZSBwZXJzb2FuZSBwZW50cnUgYSBjb2xhYm9yYSBsYSBzY2VuYSBhY3R1YWxcXHUwMTAzLlwiLFxuICBkZXNjX3ByaXZhY3k6IFwiTnUgdGUgXFx4RUVuZ3Jpam9yYS4gU2VzaXVuZWEgdXRpbGl6ZWF6XFx1MDEwMyBjcmlwdGFyZWEgaW50ZWdyYWxcXHUwMTAzLCBhc3RmZWwgXFx4RUVuY1xceEUydCBvcmljZSBkZXNlbmV6aSB2YSByXFx1MDEwM21cXHhFMm5lIHByaXZhdC4gTmljaSBtXFx1MDEwM2NhciBzZXJ2ZXJ1bCBub3N0cnUgbnUgdmEgcHV0ZWEgdmVkZWEgcGUgY2UgYWkgbHVjcmF0LlwiLFxuICBidXR0b25fc3RhcnRTZXNzaW9uOiBcIlBvcm5pcmUgc2VzaXVuZVwiLFxuICBidXR0b25fc3RvcFNlc3Npb246IFwiT3ByaXJlIHNlc2l1bmVcIixcbiAgZGVzY19pblByb2dyZXNzSW50cm86IFwiU2VzaXVuZWEgZGUgY29sYWJvcmFyZSBcXHhFRW4gZGlyZWN0IGVzdGUgXFx4RUVuIGN1cnMgZGUgZGVzZlxcdTAxMDNcXHUwMjE5dXJhcmUuXCIsXG4gIGRlc2Nfc2hhcmVMaW5rOiBcIkRpc3RyaWJ1aWUgYWNlc3QgVVJMIHBlcnNvYW5lbG9yIGN1IGNhcmUgZG9yZVxcdTAyMTl0aSBzXFx1MDEwMyBjb2xhYm9yZXppOlwiLFxuICBkZXNjX2V4aXRTZXNzaW9uOiBcIk9wcmlyZWEgc2VzaXVuaWkgdGUgdmEgZGVjb25lY3RhIGRlIGxhIHNhbFxcdTAxMDMsIFxceEVFbnNcXHUwMTAzIHZlaSBwdXRlYSBsdWNyYSBcXHhFRW4gY29udGludWFyZSwgcGUgcGxhbiBsb2NhbCwgY3Ugc2NlbmEuIFJlXFx1MDIxQmluZSBjXFx1MDEwMyBhY2Vhc3RcXHUwMTAzIG9wXFx1MDIxQml1bmUgbnUgdmEgYWZlY3RhIGFsdGUgcGVyc29hbmUsIGlhciBhY2VzdGVhIHZvciBwdXRlYSBzXFx1MDEwMyBjb2xhYm9yZXplIFxceEVFbiBjb250aW51YXJlIHBlIHZlcnNpdW5lYSBsb3IuXCIsXG4gIHNoYXJlVGl0bGU6IFwiQWxcXHUwMTAzdHVyXFx1MDEwMy10ZSB1bmVpIHNlc2l1bmkgZGUgY29sYWJvcmFyZSBcXHhFRW4gZGlyZWN0IHBlIEV4Y2FsaWRyYXdcIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiRXJvYXJlXCJcbn07XG52YXIgZXhwb3J0RGlhbG9nID0ge1xuICBkaXNrX3RpdGxlOiBcIlNhbHZhcmUgcGUgZGlzY1wiLFxuICBkaXNrX2RldGFpbHM6IFwiRXhwb3J0XFx1MDEwMyBkYXRlbGUgc2NlbmVpIHBlIHVuIGZpXFx1MDIxOWllciBkaW4gY2FyZSBwb1xcdTAyMUJpIGltcG9ydGEgbWFpIHRcXHhFMnJ6aXUuXCIsXG4gIGRpc2tfYnV0dG9uOiBcIlNhbHZhcmUgXFx4RUVuIGZpXFx1MDIxOWllclwiLFxuICBsaW5rX3RpdGxlOiBcIlVSTCBwYXJ0YWphYmlsXCIsXG4gIGxpbmtfZGV0YWlsczogXCJFeHBvcnRcXHUwMTAzIGNhIFVSTCBkb2FyIFxceEVFbiBjaXRpcmUuXCIsXG4gIGxpbmtfYnV0dG9uOiBcIkV4cG9ydGFyZSBcXHhFRW4gVVJMXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2Rlc2NyaXB0aW9uOiBcIlNhbHZlYXpcXHUwMTAzIHNjZW5hIFxceEVFbiBzcGFcXHUwMjFCaXVsIGRlIGx1Y3J1IEV4Y2FsaWRyYXcrLlwiLFxuICBleGNhbGlkcmF3cGx1c19idXR0b246IFwiRXhwb3J0YXJlXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2V4cG9ydEVycm9yOiBcIkV4Y2FsaWRyYXcrIG51IGEgcHV0dXQgZmkgZXhwb3J0YXQgXFx4RUVuIGFjZXN0IG1vbWVudC4uLlwiXG59O1xudmFyIGhlbHBEaWFsb2cgPSB7XG4gIGJsb2c6IFwiQ2l0ZVxcdTAyMTl0ZSBibG9ndWwgbm9zdHJ1XCIsXG4gIGNsaWNrOiBcImNsaWNcIixcbiAgZGVlcFNlbGVjdDogXCJTZWxlY3RhcmUgZGUgYWRcXHhFMm5jaW1lXCIsXG4gIGRlZXBCb3hTZWxlY3Q6IFwiU2VsZWN0YXJlIGRlIGFkXFx4RTJuY2ltZSBcXHhFRW4gY2FzZXRcXHUwMTAzIFxcdTAyMTlpIHByZXZlbmlyZSBnbGlzYXJlXCIsXG4gIGN1cnZlZEFycm93OiBcIlNcXHUwMTAzZ2VhdFxcdTAxMDMgY3VyYmF0XFx1MDEwM1wiLFxuICBjdXJ2ZWRMaW5lOiBcIkxpbmllIGN1cmJhdFxcdTAxMDNcIixcbiAgZG9jdW1lbnRhdGlvbjogXCJEb2N1bWVudGFcXHUwMjFCaWVcIixcbiAgZG91YmxlQ2xpY2s6IFwiZHVibHUgY2xpY1wiLFxuICBkcmFnOiBcImdsaXNhcmVcIixcbiAgZWRpdG9yOiBcIkVkaXRvclwiLFxuICBlZGl0TGluZUFycm93UG9pbnRzOiBcIkVkaXRhcmUgcHVuY3RlIGRlIHNcXHUwMTAzZ2VhdFxcdTAxMDMvclxceEUybmRcIixcbiAgZWRpdFRleHQ6IFwiRWRpdGFyZSB0ZXh0L2FkXFx1MDEwM3VnYXJlIGV0aWNoZXRcXHUwMTAzXCIsXG4gIGdpdGh1YjogXCJBaSBcXHhFRW50XFx4RTJtcGluYXQgbyBwcm9ibGVtXFx1MDEwMz8gVHJpbWl0ZSB1biByYXBvcnRcIixcbiAgaG93dG86IFwiVXJtXFx1MDEwM3JlXFx1MDIxOXRlIGdoaWR1cmlsZSBub2FzdHJlXCIsXG4gIG9yOiBcInNhdVwiLFxuICBwcmV2ZW50QmluZGluZzogXCJcXHhDRW1waWVkaWNcXHUwMTAzIGxlZ2FyZWEgc1xcdTAxMDNnZVxcdTAyMUJpaVwiLFxuICB0b29sczogXCJJbnN0cnVtZW50ZVwiLFxuICBzaG9ydGN1dHM6IFwiQ29tZW56aSByYXBpZGUgZGUgbGEgdGFzdGF0dXJcXHUwMTAzXCIsXG4gIHRleHRGaW5pc2g6IFwiRmluYWxpemVhelxcdTAxMDMgZWRpdGFyZWEgKGVkaXRvciBkZSB0ZXh0KVwiLFxuICB0ZXh0TmV3TGluZTogXCJBZGF1Z1xcdTAxMDMgbyBsaW5pZSBub3VcXHUwMTAzIChlZGl0b3IgZGUgdGV4dClcIixcbiAgdGl0bGU6IFwiQWp1dG9yXCIsXG4gIHZpZXc6IFwiVml6dWFsaXphcmVcIixcbiAgem9vbVRvRml0OiBcIlRyYW5zZm9jYXJlIHBlbnRydSBhIGN1cHJpbmRlIHRvdHVsXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJUcmFuc2ZvY2FyZSBsYSBzZWxlY1xcdTAyMUJpZVwiLFxuICB0b2dnbGVFbGVtZW50TG9jazogXCJCbG9jYXJlL2RlYmxvY2FyZSBzZWxlY1xcdTAyMUJpZVwiLFxuICBtb3ZlUGFnZVVwRG93bjogXCJEZXBsYXNhcmUgcGFnaW5cXHUwMTAzIHN1cy9qb3NcIixcbiAgbW92ZVBhZ2VMZWZ0UmlnaHQ6IFwiRGVwbGFzYXJlIHBhZ2luXFx1MDEwMyBzdFxceEUybmdhL2RyZWFwdGFcIlxufTtcbnZhciBjbGVhckNhbnZhc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiXFx1MDIxOHRlcmdlcmUgcFxceEUybnpcXHUwMTAzXCJcbn07XG52YXIgcHVibGlzaERpYWxvZyA9IHtcbiAgdGl0bGU6IFwiUHVibGljYXJlIGJpYmxpb3RlY1xcdTAxMDNcIixcbiAgaXRlbU5hbWU6IFwiRGVudW1pcmVhIGVsZW1lbnR1bHVpXCIsXG4gIGF1dGhvck5hbWU6IFwiTnVtZWxlIGF1dG9ydWx1aVwiLFxuICBnaXRodWJVc2VybmFtZTogXCJOdW1lbGUgZGUgdXRpbGl6YXRvciBHaXRIdWJcIixcbiAgdHdpdHRlclVzZXJuYW1lOiBcIk51bWVsZSBkZSB1dGlsaXphdG9yIFR3aXR0ZXJcIixcbiAgbGlicmFyeU5hbWU6IFwiRGVudW1pcmVhIGJpYmxpb3RlY2lpXCIsXG4gIGxpYnJhcnlEZXNjOiBcIkRlc2NyaWVyZWEgYmlibGlvdGVjaWlcIixcbiAgd2Vic2l0ZTogXCJQYWdpblxcdTAxMDMgZGUgaW50ZXJuZXRcIixcbiAgcGxhY2Vob2xkZXI6IHtcbiAgICBhdXRob3JOYW1lOiBcIk51bWVsZSBzYXUgbnVtZWxlIHRcXHUwMTAzdSBkZSB1dGlsaXphdG9yXCIsXG4gICAgbGlicmFyeU5hbWU6IFwiTnVtZWxlIGJpYmxpb3RlY2lpIHRhbGVcIixcbiAgICBsaWJyYXJ5RGVzYzogXCJEZXNjcmllcmVhIGJpYmxpb3RlY2lpIHRhbGUgcGVudHJ1IGEgYWp1dGEgb2FtZW5paSBzXFx1MDEwMyBcXHhFRW5cXHUwMjFCZWxlYWdcXHUwMTAzIHV0aWxpemFyZWEgYWNlc3RlaWFcIixcbiAgICBnaXRodWJIYW5kbGU6IFwiTnVtZWxlIGRlIHV0aWxpemF0b3IgR2l0SHViIChvcFxcdTAyMUJpb25hbCksIHBlbnRydSBhIHB1dGVhIGVkaXRhIGJpYmxpb3RlY2Egb2RhdFxcdTAxMDMgY2UgZXN0ZSB0cmltaXNcXHUwMTAzIHNwcmUgcmV2aXp1aXJlXCIsXG4gICAgdHdpdHRlckhhbmRsZTogXCJOdW1lbGUgZGUgdXRpbGl6YXRvciBUd2l0dGVyIChvcFxcdTAyMUJpb25hbCksIHBlbnRydSBhIGluZGljYSBzdXJzYSBsYSBwcm9tb3ZhcmVhIHBlIFR3aXR0ZXJcIixcbiAgICB3ZWJzaXRlOiBcIlRyaW1pdGVyZSBjXFx1MDEwM3RyZSBwYWdpbmEgdGEgcGVyc29uYWxcXHUwMTAzIGRlIGludGVybmV0IHNhdSBhbHR1bmRldmEgKG9wXFx1MDIxQmlvbmFsKVwiXG4gIH0sXG4gIGVycm9yczoge1xuICAgIHJlcXVpcmVkOiBcIk9ibGlnYXRvcml1XCIsXG4gICAgd2Vic2l0ZTogXCJJbnRyb2R1IHVuIFVSTCB2YWxpZFwiXG4gIH0sXG4gIG5vdGVEZXNjcmlwdGlvbjogXCJUcmltaXRlLVxcdTAyMUJpIGJpYmxpb3RlY2EgcGVudHJ1IGEgZmkgaW5jbHVzXFx1MDEwMyBcXHhFRW4gPGxpbms+ZGVwb3ppdHVsIGRlIGJpYmxpb3RlY2kgcHVibGljZTwvbGluaz4gXFx4RUVuIHZlZGVyZWEgdXRpbGl6XFx1MDEwM3JpaSBkZSBjXFx1MDEwM3RyZSBhbHRlIHBlcnNvYW5lIFxceEVFbiBkZXNlbmVsZSBsb3IuXCIsXG4gIG5vdGVHdWlkZWxpbmVzOiBcIkJpYmxpb3RlY2EgdHJlYnVpZSBhcHJvYmF0XFx1MDEwMyBtYW51YWwgbWFpIFxceEVFbnRcXHhFMmkuIENpdGVcXHUwMjE5dGUgPGxpbms+b3JpZW50XFx1MDEwM3JpbGU8L2xpbms+IFxceEVFbmFpbnRlIGRlIHRyaW1pdGVyZS4gVmVpIGF2ZWEgbmV2b2llIGRlIHVuIGNvbnQgR2l0SHViIHBlbnRydSBhIGNvbXVuaWNhIFxcdTAyMTlpIGVmZWN0dWEgbW9kaWZpY1xcdTAxMDNyaSwgZGFjXFx1MDEwMyBlc3RlIGNhenVsLCBcXHhFRW5zXFx1MDEwMyBudSBlc3RlIHN0cmljdCBuZWNlc2FyLlwiLFxuICBub3RlTGljZW5zZTogXCJQcmluIHRyaW1pdGVyZWEgYmlibGlvdGVjaWksIGVcXHUwMjE5dGkgZGUgYWNvcmQgY1xcdTAxMDMgYWNlYXN0YSB2YSBmaSBwdWJsaWNhdFxcdTAxMDMgc3ViIDxsaW5rPkxpY2VuXFx1MDIxQmEgTUlULCA8L2xpbms+Y2FyZSwgcGUgc2N1cnQsIFxceEVFbnNlYW1uXFx1MDEwMyBjXFx1MDEwMyBvcmljaW5lIG8gcG9hdGUgZm9sb3NpIGZcXHUwMTAzclxcdTAxMDMgcmVzdHJpY1xcdTAyMUJpaS5cIixcbiAgbm90ZUl0ZW1zOiBcIkZpZWNhcmUgZWxlbWVudCBkaW4gYmlibGlvdGVjXFx1MDEwMyB0cmVidWllIHNcXHUwMTAzIGFpYlxcdTAxMDMgcHJvcHJpdWwgbnVtZSBhc3RmZWwgXFx4RUVuY1xceEUydCBzXFx1MDEwMyBmaWUgZmlsdHJhYmlsLiBVcm1cXHUwMTAzdG9hcmVsZSBlbGVtZW50ZSBkaW4gYmlibGlvdGVjXFx1MDEwMyB2b3IgZmkgaW5jbHVzZTpcIixcbiAgYXRsZWFzdE9uZUxpYkl0ZW06IFwiU2VsZWN0ZWF6XFx1MDEwMyBjZWwgcHVcXHUwMjFCaW4gdW4gZWxlbWVudCBkaW4gYmlibGlvdGVjXFx1MDEwMyBwZW50cnUgYSBcXHhFRW5jZXBlXCIsXG4gIHJlcHVibGlzaFdhcm5pbmc6IFwiT2JzZXJ2YVxcdTAyMUJpZTogdW5lbGUgZGludHJlIGVsZW1lbnRlbGUgc2VsZWN0YXRlIHN1bnQgbWFyY2F0ZSBjYSBmaWluZCBkZWphIHB1YmxpY2F0ZS90cmltaXNlLiBBciB0cmVidWkgc1xcdTAxMDMgcmV0cmltaVxcdTAyMUJpIGVsZW1lbnRlIG51bWFpIGF0dW5jaSBjXFx4RTJuZCBhY3R1YWxpemV6aSBvIHRyaW1pdGVyZSBzYXUgbyBiaWJsaW90ZWNcXHUwMTAzIGV4aXN0ZW50XFx1MDEwMy5cIlxufTtcbnZhciBwdWJsaXNoU3VjY2Vzc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiQmlibGlvdGVjXFx1MDEwMyB0cmltaXNcXHUwMTAzXCIsXG4gIGNvbnRlbnQ6IFwiXFx4Q0VcXHUwMjFCaSBtdWxcXHUwMjFCdW1pbSwge3thdXRob3JOYW1lfX0uIEJpYmxpb3RlY2EgYSBmb3N0IHRyaW1pc1xcdTAxMDMgc3ByZSByZXZpenVpcmUuIFBvXFx1MDIxQmkgdXJtXFx1MDEwM3JpIHN0YXJlYSA8bGluaz5haWNpPC9saW5rPlwiXG59O1xudmFyIGNvbmZpcm1EaWFsb2cgPSB7XG4gIHJlc2V0TGlicmFyeTogXCJSZXNldGFyZSBiaWJsaW90ZWNcXHUwMTAzXCIsXG4gIHJlbW92ZUl0ZW1zRnJvbUxpYjogXCJFbGltaW5cXHUwMTAzIGVsZW1lbnRlbGUgc2VsZWN0YXRlIGRpbiBiaWJsaW90ZWNcXHUwMTAzXCJcbn07XG52YXIgaW1hZ2VFeHBvcnREaWFsb2cgPSB7XG4gIGhlYWRlcjogXCJFeHBvcnRhcmUgaW1hZ2luZVwiLFxuICBsYWJlbDoge1xuICAgIHdpdGhCYWNrZ3JvdW5kOiBcIkZ1bmRhbFwiLFxuICAgIG9ubHlTZWxlY3RlZDogXCJOdW1haSBzZWxlY1xcdTAyMUJpYVwiLFxuICAgIGRhcmtNb2RlOiBcIk1vZCBcXHhFRW50dW5lY2F0XCIsXG4gICAgZW1iZWRTY2VuZTogXCJcXHhDRW5jb3Jwb3JhcmUgc2NlblxcdTAxMDNcIixcbiAgICBzY2FsZTogXCJTY2FsXFx1MDEwM1wiLFxuICAgIHBhZGRpbmc6IFwiU3BhXFx1MDIxQmllcmVcIlxuICB9LFxuICB0b29sdGlwOiB7XG4gICAgZW1iZWRTY2VuZTogXCJEYXRlbGUgc2NlbmVpIHZvciBmaSBzYWx2YXRlIFxceEVFbiBmaVxcdTAyMTlpZXJ1bCBQTkcvU1ZHIGV4cG9ydGF0LCBhc3RmZWwgY1xcdTAxMDMgc2NlbmEgdmEgcHV0ZWEgZmkgcmVzdGF1cmF0XFx1MDEwMyBkaW4gYWNlc3RhLlxcblZhIGNyZVxcdTAyMTl0ZSBkaW1lbnNpdW5lYSBmaVxcdTAyMTlpZXJ1bHVpIGV4cG9ydGF0LlwiXG4gIH0sXG4gIHRpdGxlOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiRXhwb3J0YXJlIGNhIFBOR1wiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIkV4cG9ydGFyZSBjYSBTVkdcIixcbiAgICBjb3B5UG5nVG9DbGlwYm9hcmQ6IFwiQ29waWVyZSBQTkcgXFx4RUVuIG1lbW9yaWEgdGVtcG9yYXJcXHUwMTAzXCJcbiAgfSxcbiAgYnV0dG9uOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiUE5HXCIsXG4gICAgZXhwb3J0VG9Tdmc6IFwiU1ZHXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIkNvcGllcmUgXFx4RUVuIG1lbW9yaWEgdGVtcG9yYXJcXHUwMTAzXCJcbiAgfVxufTtcbnZhciBlbmNyeXB0ZWQgPSB7XG4gIHRvb2x0aXA6IFwiRGVzZW5lbGUgdGFsZSBzdW50IGNyaXB0YXRlIGludGVncmFsLCBhc3RmZWwgY1xcdTAxMDMgc2VydmVyZWxlIEV4Y2FsaWRyYXcgbnUgbGUgdm9yIHZlZGVhIG5pY2lvZGF0XFx1MDEwMy5cIixcbiAgbGluazogXCJBcnRpY29sIGRlIGJsb2cgcGUgY3JpcHRhcmVhIGludGVncmFsXFx1MDEwMyBkaW4gRXhjYWxpZHJhd1wiXG59O1xudmFyIHN0YXRzID0ge1xuICBhbmdsZTogXCJVbmdoaVwiLFxuICBlbGVtZW50OiBcIkVsZW1lbnRcIixcbiAgZWxlbWVudHM6IFwiRWxlbWVudGVcIixcbiAgaGVpZ2h0OiBcIlxceENFblxcdTAxMDNsXFx1MDIxQmltZVwiLFxuICBzY2VuZTogXCJTY2VuXFx1MDEwM1wiLFxuICBzZWxlY3RlZDogXCJTZWxlY3RhdGVcIixcbiAgc3RvcmFnZTogXCJTdG9jYXJlXCIsXG4gIHRpdGxlOiBcIlN0YXRpc3RpY2kgcGVudHJ1IHBhc2lvbmFcXHUwMjFCaVwiLFxuICB0b3RhbDogXCJUb3RhbFwiLFxuICB2ZXJzaW9uOiBcIlZlcnNpdW5lXCIsXG4gIHZlcnNpb25Db3B5OiBcIkNsaWMgcGVudHJ1IGNvcGllcmVcIixcbiAgdmVyc2lvbk5vdEF2YWlsYWJsZTogXCJWZXJzaXVuZSBpbmRpc3BvbmliaWxcXHUwMTAzXCIsXG4gIHdpZHRoOiBcIkxcXHUwMTAzXFx1MDIxQmltZVwiXG59O1xudmFyIHRvYXN0ID0ge1xuICBhZGRlZFRvTGlicmFyeTogXCJBZFxcdTAxMDN1Z2F0IFxceEVFbiBiaWJsaW90ZWNcXHUwMTAzXCIsXG4gIGNvcHlTdHlsZXM6IFwiU3RpbHVyaSBjb3BpYXRlLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiQ29waWF0IFxceEVFbiBtZW1vcmlhIHRlbXBvcmFyXFx1MDEwMy5cIixcbiAgY29weVRvQ2xpcGJvYXJkQXNQbmc6IFwiUy1hIGNvcGlhdCB7e2V4cG9ydFNlbGVjdGlvbn19IFxceEVFbiBtZW1vcmlhIHRlbXBvcmFyXFx1MDEwMyBzdWIgZm9ybVxcdTAxMDMgZGUgUE5HXFxuKHt7ZXhwb3J0Q29sb3JTY2hlbWV9fSlcIixcbiAgZmlsZVNhdmVkOiBcIkZpXFx1MDIxOWllciBzYWx2YXQuXCIsXG4gIGZpbGVTYXZlZFRvRmlsZW5hbWU6IFwiU2FsdmF0IFxceEVFbiB7ZmlsZW5hbWV9XCIsXG4gIGNhbnZhczogXCJwXFx4RTJuemFcIixcbiAgc2VsZWN0aW9uOiBcInNlbGVjXFx1MDIxQmlhXCIsXG4gIHBhc3RlQXNTaW5nbGVFbGVtZW50OiBcIkZvbG9zZVxcdTAyMTl0ZSB7e3Nob3J0Y3V0fX0gcGVudHJ1IGEgaW5zZXJhIGNhIHVuIHNpbmd1ciBlbGVtZW50XFxuc2F1IGluc2VyYSBcXHhFRW50ci11biBlZGl0b3IgZGUgdGV4dCBleGlzdGVudFwiLFxuICB1bmFibGVUb0VtYmVkOiBcIlxceENFbmNvcnBvcmFyZWEgYWNlc3R1aSBVUkwgbnUgZXN0ZSBwZXJtaXNcXHUwMTAzIG1vbWVudGFuLiBEZXNjaGlkZVxcdTAyMUJpIHVuIHRpY2hldCBjdSBwcm9ibGVtZSBwZSBHaXRIdWIgcGVudHJ1IGEgc29saWNpdGEgYWRcXHUwMTAzdWdhcmVhIGFjZXN0dWkgVVJMIFxceEVFbiBsaXN0YSBhbGJcXHUwMTAzXCIsXG4gIHVucmVjb2duaXplZExpbmtGb3JtYXQ6IFwiVVJMLXVsIHBlIGNhcmUgbC1haSBcXHhFRW5jb3Jwb3JhdCBudSBjb2luY2lkZSBjdSBmb3JtYXR1bCBhXFx1MDIxOXRlcHRhdC4gXFx4Q0VuY2VhcmNcXHUwMTAzIHNcXHUwMTAzIGxpcGVcXHUwMjE5dGkgXFx1MDIxOWlydWwgXFx1MjAxRWRlIFxceEVFbmNvcnBvcmF0XFx1MjAxRCBmdXJuaXphdCBkZSBwYWdpbmEgc3Vyc1xcdTAxMDNcIlxufTtcbnZhciBjb2xvcnMgPSB7XG4gIHRyYW5zcGFyZW50OiBcIlRyYW5zcGFyZW50XCIsXG4gIGJsYWNrOiBcIk5lZ3J1XCIsXG4gIHdoaXRlOiBcIkFsYlwiLFxuICByZWQ6IFwiUm9cXHUwMjE5dVwiLFxuICBwaW5rOiBcIlJvelwiLFxuICBncmFwZTogXCJTdHJ1Z3VyaXVcIixcbiAgdmlvbGV0OiBcIlZpb2xldFwiLFxuICBncmF5OiBcIkdyaVwiLFxuICBibHVlOiBcIkFsYmFzdHJ1XCIsXG4gIGN5YW46IFwiQ3lhblwiLFxuICB0ZWFsOiBcIkN5YW4tdmVyZGVcIixcbiAgZ3JlZW46IFwiVmVyZGVcIixcbiAgeWVsbG93OiBcIkdhbGJlblwiLFxuICBvcmFuZ2U6IFwiUG9ydG9jYWxpdVwiLFxuICBicm9uemU6IFwiQnJvbnpcIlxufTtcbnZhciB3ZWxjb21lU2NyZWVuID0ge1xuICBhcHA6IHtcbiAgICBjZW50ZXJfaGVhZGluZzogXCJUb2F0ZSBkYXRlbGUgdGFsZSBzdW50IHNhbHZhdGUgbG9jYWwgXFx4RUVuIG5hdmlnYXRvcnVsIHRcXHUwMTAzdS5cIixcbiAgICBjZW50ZXJfaGVhZGluZ19wbHVzOiBcIkFpIHZydXQgc1xcdTAxMDMgbWVyZ2kgXFx4RUVuIHNjaGltYiBsYSBFeGNhbGlkcmF3Kz9cIixcbiAgICBtZW51SGludDogXCJFeHBvcnRhcmUsIHByZWZlcmluXFx1MDIxQmUsIGxpbWJpLCAuLi5cIlxuICB9LFxuICBkZWZhdWx0czoge1xuICAgIG1lbnVIaW50OiBcIkV4cG9ydGFyZSwgcHJlZmVyaW5cXHUwMjFCZSBcXHUwMjE5aSBtYWkgbXVsdGUuLi5cIixcbiAgICBjZW50ZXJfaGVhZGluZzogXCJEaWFncmFtZS4gRlxcdTAxMDNjdXRlLiBTaW1wbGUuXCIsXG4gICAgdG9vbGJhckhpbnQ6IFwiQWxlZ2UgdW4gaW5zdHJ1bWVudCBcXHUwMjE5aSBcXHhFRW5jZXBlIHNcXHUwMTAzIGRlc2VuZXppIVwiLFxuICAgIGhlbHBIaW50OiBcIkNvbWVuemkgcmFwaWRlIFxcdTAyMTlpIGFqdXRvclwiXG4gIH1cbn07XG52YXIgY29sb3JQaWNrZXIgPSB7XG4gIG1vc3RVc2VkQ3VzdG9tQ29sb3JzOiBcIkNlbGUgbWFpIHV0aWxpemF0ZSBjdWxvcmkgcGVyc29uYWxpemF0ZVwiLFxuICBjb2xvcnM6IFwiQ3Vsb3JpXCIsXG4gIHNoYWRlczogXCJOdWFuXFx1MDIxQmVcIixcbiAgaGV4Q29kZTogXCJDb2QgaGV4YVwiLFxuICBub1NoYWRlczogXCJOdSBlc3RlIGRpc3BvbmliaWxcXHUwMTAzIG5pY2lvIG51YW5cXHUwMjFCXFx1MDEwMyBwZW50cnUgYWNlYXN0XFx1MDEwMyBjdWxvYXJlXCJcbn07XG52YXIgb3ZlcndyaXRlQ29uZmlybSA9IHtcbiAgYWN0aW9uOiB7XG4gICAgZXhwb3J0VG9JbWFnZToge1xuICAgICAgdGl0bGU6IFwiRXhwb3J0YXJlIGNhIGltYWdpbmVcIixcbiAgICAgIGJ1dHRvbjogXCJFeHBvcnRhcmUgY2EgaW1hZ2luZVwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRXhwb3J0XFx1MDEwMyBkYXRlbGUgc2NlbmVpIGNhIGZpXFx1MDIxOWllciBkaW4gY2FyZSBwb1xcdTAyMUJpIGltcG9ydGEgbWFpIHRcXHhFMnJ6aXUuXCJcbiAgICB9LFxuICAgIHNhdmVUb0Rpc2s6IHtcbiAgICAgIHRpdGxlOiBcIlNhbHZhcmUgcGUgZGlzY1wiLFxuICAgICAgYnV0dG9uOiBcIlNhbHZhcmUgcGUgZGlzY1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRXhwb3J0XFx1MDEwMyBkYXRlbGUgc2NlbmVpIHBlIHVuIGZpXFx1MDIxOWllciBkaW4gY2FyZSBwb1xcdTAyMUJpIGltcG9ydGEgbWFpIHRcXHhFMnJ6aXUuXCJcbiAgICB9LFxuICAgIGV4Y2FsaWRyYXdQbHVzOiB7XG4gICAgICB0aXRsZTogXCJFeGNhbGlkcmF3K1wiLFxuICAgICAgYnV0dG9uOiBcIkV4cG9ydGFyZSBcXHhFRW4gRXhjYWxpZHJhdytcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlNhbHZlYXpcXHUwMTAzIHNjZW5hIFxceEVFbiBzcGFcXHUwMjFCaXVsIGRlIGx1Y3J1IEV4Y2FsaWRyYXcrLlwiXG4gICAgfVxuICB9LFxuICBtb2RhbDoge1xuICAgIGxvYWRGcm9tRmlsZToge1xuICAgICAgdGl0bGU6IFwiXFx4Q0VuY1xcdTAxMDNyY2FyZSBkaW4gZmlcXHUwMjE5aWVyXCIsXG4gICAgICBidXR0b246IFwiXFx4Q0VuY1xcdTAxMDNyY2FyZSBkaW4gZmlcXHUwMjE5aWVyXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcXHhDRW5jXFx1MDEwM3JjYXJlYSBkaW50ci11biBmaVxcdTAyMTlpZXIgdmEgPGJvbGQ+XFx4RUVubG9jdWkgY29uXFx1MDIxQmludXR1bCBleGlzdGVudDwvYm9sZD4uPGJyPjwvYnI+UG9cXHUwMjFCaSBmYWNlIG1haSBcXHhFRW50XFx4RTJpIG8gY29waWUgZGUgcmV6ZXJ2XFx1MDEwMyBhIGRlc2VudWx1aSBmb2xvc2luZCB1bmEgZGludHJlIG9wXFx1MDIxQml1bmlsZSBkZSBtYWkgam9zLlwiXG4gICAgfSxcbiAgICBzaGFyZWFibGVMaW5rOiB7XG4gICAgICB0aXRsZTogXCJcXHhDRW5jXFx1MDEwM3JjYXJlIGRpbiBsbmtcIixcbiAgICAgIGJ1dHRvbjogXCJcXHhDRW5sb2N1aWVcXHUwMjE5dGUgY29uXFx1MDIxQmludXR1bCBtZXVcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlxceENFbmNcXHUwMTAzcmNhcmVhIHVudWkgZGVzZW4gZXh0ZXJuIHZhIDxib2xkPlxceEVFbmxvY3VpIGNvblxcdTAyMUJpbnV0dWwgZXhpc3RlbnQ8L2JvbGQ+Ljxicj48L2JyPlBvXFx1MDIxQmkgZmFjZSBtYWkgXFx4RUVudFxceEUyaSBvIGNvcGllIGRlIHJlemVydlxcdTAxMDMgYSBkZXNlbnVsdWkgZm9sb3NpbmQgdW5hIGRpbnRyZSBvcFxcdTAyMUJpdW5pbGUgZGUgbWFpIGpvcy5cIlxuICAgIH1cbiAgfVxufTtcbnZhciBtZXJtYWlkID0ge1xuICB0aXRsZTogXCJNZXJtYWlkIGxhIEV4Y2FsaWRyYXdcIixcbiAgYnV0dG9uOiBcIkludHJvZHVjZXJlXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlxceENFbiBwcmV6ZW50LCBudW1haSA8Zmxvd2NoYXJ0TGluaz5PcmdhbmlncmFtZWxlPC9mbG93Y2hhcnRMaW5rPiwgPHNlcXVlbmNlTGluaz5EaWFncmFtZWxlIGRlIHNlY3ZlblxcdTAyMUJcXHUwMTAzPC9zZXF1ZW5jZUxpbms+IFxcdTAyMTlpIDxjbGFzc0xpbms+RGlhZ3JhbWVsZSBkZSBjbGFzXFx1MDEwMzwvY2xhc3NMaW5rPiBzdW50IGFjY2VwdGF0ZS4gQ2VsZWxhbHRlIHRpcHVyaSB2b3IgZmkgcmVkYXRlIGNhIGltYWdpbmUgXFx4RUVuIEV4Y2FsaWRyYXcuXCIsXG4gIHN5bnRheDogXCJTaW50YXhcXHUwMTAzIE1lcm1haWRcIixcbiAgcHJldmlldzogXCJQcmV2aXp1YWxpemFyZVwiXG59O1xudmFyIHJvX1JPX2RlZmF1bHQgPSB7XG4gIGxhYmVscyxcbiAgbGlicmFyeSxcbiAgYnV0dG9ucyxcbiAgYWxlcnRzLFxuICBlcnJvcnMsXG4gIHRvb2xCYXIsXG4gIGhlYWRpbmdzLFxuICBoaW50cyxcbiAgY2FudmFzRXJyb3IsXG4gIGVycm9yU3BsYXNoLFxuICByb29tRGlhbG9nLFxuICBlcnJvckRpYWxvZyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWxwRGlhbG9nLFxuICBjbGVhckNhbnZhc0RpYWxvZyxcbiAgcHVibGlzaERpYWxvZyxcbiAgcHVibGlzaFN1Y2Nlc3NEaWFsb2csXG4gIGNvbmZpcm1EaWFsb2csXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBlbmNyeXB0ZWQsXG4gIHN0YXRzLFxuICB0b2FzdCxcbiAgY29sb3JzLFxuICB3ZWxjb21lU2NyZWVuLFxuICBjb2xvclBpY2tlcixcbiAgb3ZlcndyaXRlQ29uZmlybSxcbiAgbWVybWFpZFxufTtcbmV4cG9ydCB7XG4gIGFsZXJ0cyxcbiAgYnV0dG9ucyxcbiAgY2FudmFzRXJyb3IsXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBjb2xvclBpY2tlcixcbiAgY29sb3JzLFxuICBjb25maXJtRGlhbG9nLFxuICByb19ST19kZWZhdWx0IGFzIGRlZmF1bHQsXG4gIGVuY3J5cHRlZCxcbiAgZXJyb3JEaWFsb2csXG4gIGVycm9yU3BsYXNoLFxuICBlcnJvcnMsXG4gIGV4cG9ydERpYWxvZyxcbiAgaGVhZGluZ3MsXG4gIGhlbHBEaWFsb2csXG4gIGhpbnRzLFxuICBpbWFnZUV4cG9ydERpYWxvZyxcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBtZXJtYWlkLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgcm9vbURpYWxvZyxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICB0b29sQmFyLFxuICB3ZWxjb21lU2NyZWVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cm8tUk8tTDU3NVZSUUEuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/ro-RO-L575VRQA.js\n"));

/***/ })

}]);