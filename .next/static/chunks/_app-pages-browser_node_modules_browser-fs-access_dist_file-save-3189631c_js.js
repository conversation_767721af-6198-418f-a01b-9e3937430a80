"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_browser-fs-access_dist_file-save-3189631c_js"],{

/***/ "(app-pages-browser)/./node_modules/browser-fs-access/dist/file-save-3189631c.js":
/*!*******************************************************************!*\
  !*** ./node_modules/browser-fs-access/dist/file-save-3189631c.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\nvar e=async(e,t={})=>{Array.isArray(t)&&(t=t[0]);const n=document.createElement(\"a\");let a=e;\"body\"in e&&(a=await async function(e,t){const n=e.getReader(),a=new ReadableStream({start:e=>async function t(){return n.read().then(({done:n,value:a})=>{if(!n)return e.enqueue(a),t();e.close()})}()}),r=new Response(a),c=await r.blob();return n.releaseLock(),new Blob([c],{type:t})}(e.body,e.headers.get(\"content-type\"))),n.download=t.fileName||\"Untitled\",n.href=URL.createObjectURL(await a);const r=()=>{\"function\"==typeof c&&c()},c=t.legacySetup&&t.legacySetup(r,()=>c(reject),n);return n.addEventListener(\"click\",()=>{setTimeout(()=>URL.revokeObjectURL(n.href),3e4),r()}),n.click(),null};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2ZpbGUtc2F2ZS0zMTg5NjMxYy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsa0JBQWtCLElBQUksMkJBQTJCLG9DQUFvQyxRQUFRLHlDQUF5Qyw0Q0FBNEMsNEJBQTRCLHVCQUF1QixlQUFlLElBQUksOEJBQThCLFVBQVUsRUFBRSxHQUFHLHFDQUFxQyxxQ0FBcUMsT0FBTyxFQUFFLDhHQUE4RyxhQUFhLDBCQUEwQixtREFBbUQsdUNBQXVDLG9EQUFvRCxrQkFBdUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvYnJvd3Nlci1mcy1hY2Nlc3MvZGlzdC9maWxlLXNhdmUtMzE4OTYzMWMuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGU9YXN5bmMoZSx0PXt9KT0+e0FycmF5LmlzQXJyYXkodCkmJih0PXRbMF0pO2NvbnN0IG49ZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImFcIik7bGV0IGE9ZTtcImJvZHlcImluIGUmJihhPWF3YWl0IGFzeW5jIGZ1bmN0aW9uKGUsdCl7Y29uc3Qgbj1lLmdldFJlYWRlcigpLGE9bmV3IFJlYWRhYmxlU3RyZWFtKHtzdGFydDplPT5hc3luYyBmdW5jdGlvbiB0KCl7cmV0dXJuIG4ucmVhZCgpLnRoZW4oKHtkb25lOm4sdmFsdWU6YX0pPT57aWYoIW4pcmV0dXJuIGUuZW5xdWV1ZShhKSx0KCk7ZS5jbG9zZSgpfSl9KCl9KSxyPW5ldyBSZXNwb25zZShhKSxjPWF3YWl0IHIuYmxvYigpO3JldHVybiBuLnJlbGVhc2VMb2NrKCksbmV3IEJsb2IoW2NdLHt0eXBlOnR9KX0oZS5ib2R5LGUuaGVhZGVycy5nZXQoXCJjb250ZW50LXR5cGVcIikpKSxuLmRvd25sb2FkPXQuZmlsZU5hbWV8fFwiVW50aXRsZWRcIixuLmhyZWY9VVJMLmNyZWF0ZU9iamVjdFVSTChhd2FpdCBhKTtjb25zdCByPSgpPT57XCJmdW5jdGlvblwiPT10eXBlb2YgYyYmYygpfSxjPXQubGVnYWN5U2V0dXAmJnQubGVnYWN5U2V0dXAociwoKT0+YyhyZWplY3QpLG4pO3JldHVybiBuLmFkZEV2ZW50TGlzdGVuZXIoXCJjbGlja1wiLCgpPT57c2V0VGltZW91dCgoKT0+VVJMLnJldm9rZU9iamVjdFVSTChuLmhyZWYpLDNlNCkscigpfSksbi5jbGljaygpLG51bGx9O2V4cG9ydHtlIGFzIGRlZmF1bHR9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/browser-fs-access/dist/file-save-3189631c.js\n"));

/***/ })

}]);