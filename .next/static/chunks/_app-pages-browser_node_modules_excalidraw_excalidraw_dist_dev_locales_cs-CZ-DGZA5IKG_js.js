"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_cs-CZ-DGZA5IKG_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/cs-CZ-DGZA5IKG.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/cs-CZ-DGZA5IKG.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ cs_CZ_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/cs-CZ.json\nvar labels = {\n  paste: \"Vlo\\u017Eit\",\n  pasteAsPlaintext: \"Vlo\\u017Eit jako prost\\xFD text\",\n  pasteCharts: \"Vlo\\u017Eit grafy\",\n  selectAll: \"Vybrat v\\u0161e\",\n  multiSelect: \"P\\u0159idat prvek do v\\xFDb\\u011Bru\",\n  moveCanvas: \"Posunout pl\\xE1tno\",\n  cut: \"Vyjmout\",\n  copy: \"Kop\\xEDrovat\",\n  copyAsPng: \"Zkop\\xEDrovat do schr\\xE1nky jako PNG\",\n  copyAsSvg: \"Zkop\\xEDrovat do schr\\xE1nky jako SVG\",\n  copyText: \"Zkop\\xEDrovat do schr\\xE1nky jako text\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"P\\u0159en\\xE9st bl\\xED\\u017E\",\n  sendToBack: \"P\\u0159en\\xE9st do pozad\\xED\",\n  bringToFront: \"P\\u0159en\\xE9st do pop\\u0159ed\\xED\",\n  sendBackward: \"P\\u0159en\\xE9st d\\xE1l\",\n  delete: \"Smazat\",\n  copyStyles: \"Kop\\xEDrovat styly\",\n  pasteStyles: \"Vlo\\u017Eit styly\",\n  stroke: \"Obrys\",\n  background: \"Pozad\\xED\",\n  fill: \"V\\xFDpl\\u0148\",\n  strokeWidth: \"Tlou\\u0161\\u0165ka tahu\",\n  strokeStyle: \"Styl tahu\",\n  strokeStyle_solid: \"Pln\\xFD\",\n  strokeStyle_dashed: \"\\u010C\\xE1rkovan\\xFD\",\n  strokeStyle_dotted: \"Te\\u010Dkovan\\xFD\",\n  sloppiness: \"Stylizace\",\n  opacity: \"Pr\\u016Fhlednost\",\n  textAlign: \"Zarovn\\xE1n\\xED textu\",\n  edges: \"Hrany\",\n  sharp: \"Ostr\\xE9\",\n  round: \"Zaoblen\\xE9\",\n  arrowheads: \"Styl \\u0161ipky\",\n  arrowhead_none: \"\\u017D\\xE1dn\\xFD\",\n  arrowhead_arrow: \"\\u0160ipka\",\n  arrowhead_bar: \"K\\xF3ta\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Troj\\xFAheln\\xEDk\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Velikost p\\xEDsma\",\n  fontFamily: \"P\\xEDsmo\",\n  addWatermark: 'P\\u0159idat \"Vyrobeno s Excalidraw\"',\n  handDrawn: \"Od ruky\",\n  normal: \"Norm\\xE1ln\\xED\",\n  code: \"K\\xF3d\",\n  small: \"Mal\\xE9\",\n  medium: \"St\\u0159edn\\xED\",\n  large: \"Velk\\xE9\",\n  veryLarge: \"Velmi velk\\xE9\",\n  solid: \"Pln\\xFD\",\n  hachure: \"Hachure\",\n  zigzag: \"Klikat\\u011B\",\n  crossHatch: \"K\\u0159\\xED\\u017Eov\\xFD \\u0161rafov\\xE1n\\xED\",\n  thin: \"Tenk\\xFD\",\n  bold: \"Tlust\\xFD\",\n  left: \"Vlevo\",\n  center: \"Na st\\u0159ed\",\n  right: \"Vpravo\",\n  extraBold: \"Extra tlust\\xFD\",\n  architect: \"Architekt\",\n  artist: \"Um\\u011Blec\",\n  cartoonist: \"Kartoonista\",\n  fileTitle: \"N\\xE1zev souboru\",\n  colorPicker: \"V\\xFDb\\u011Br barvy\",\n  canvasColors: \"Pou\\u017Eito na pl\\xE1tn\\u011B\",\n  canvasBackground: \"Pozad\\xED pl\\xE1tna\",\n  drawingCanvas: \"Kreslic\\xED pl\\xE1tno\",\n  layers: \"Vrstvy\",\n  actions: \"Akce\",\n  language: \"Jazyk\",\n  liveCollaboration: \"\\u017Div\\xE1 spolupr\\xE1ce...\",\n  duplicateSelection: \"Duplikovat\",\n  untitled: \"Bez n\\xE1zvu\",\n  name: \"N\\xE1zev\",\n  yourName: \"Va\\u0161e jm\\xE9no\",\n  madeWithExcalidraw: \"Vytvo\\u0159eno v Excalidraw\",\n  group: \"Slou\\u010Dit v\\xFDb\\u011Br do skupiny\",\n  ungroup: \"Zru\\u0161it slou\\u010Den\\xED skupiny\",\n  collaborators: \"Spolupracovn\\xEDci\",\n  showGrid: \"Zobrazit m\\u0159\\xED\\u017Eku\",\n  addToLibrary: \"P\\u0159idat do knihovny\",\n  removeFromLibrary: \"Odebrat z knihovny\",\n  libraryLoadingMessage: \"Na\\u010D\\xEDt\\xE1n\\xED knihovny\\u2026\",\n  libraries: \"Proch\\xE1zet knihovny\",\n  loadingScene: \"Na\\u010D\\xEDt\\xE1n\\xED sc\\xE9ny\\u2026\",\n  align: \"Zarovn\\xE1n\\xED\",\n  alignTop: \"Zarovnat nahoru\",\n  alignBottom: \"Zarovnat dol\\u016F\",\n  alignLeft: \"Zarovnat vlevo\",\n  alignRight: \"Zarovnejte vpravo\",\n  centerVertically: \"Vycentrovat svisle\",\n  centerHorizontally: \"Vycentrovat vodorovn\\u011B\",\n  distributeHorizontally: \"Rozlo\\u017Eit horizont\\xE1ln\\u011B\",\n  distributeVertically: \"Rozlo\\u017Eit svisle\",\n  flipHorizontal: \"P\\u0159evr\\xE1tit vodorovn\\u011B\",\n  flipVertical: \"P\\u0159evr\\xE1tit svisle\",\n  viewMode: \"N\\xE1hled\",\n  share: \"Sd\\xEDlet\",\n  showStroke: \"Zobrazit v\\xFDb\\u011Br barvy\",\n  showBackground: \"Zobrazit v\\xFDb\\u011Br barev pozad\\xED\",\n  toggleTheme: \"P\\u0159epnout tmav\\xFD \\u0159e\\u017Eim\",\n  personalLib: \"Osobn\\xED knihovna\",\n  excalidrawLib: \"Exkalidraw knihovna\",\n  decreaseFontSize: \"Zmen\\u0161it p\\xEDsmo\",\n  increaseFontSize: \"Zv\\u011Bt\\u0161it p\\xEDsmo\",\n  unbindText: \"Zru\\u0161it vazbu textu\",\n  bindText: \"V\\xE1zat text s kontejnerem\",\n  createContainerFromText: \"Zabalit text do kontejneru\",\n  link: {\n    edit: \"Upravit odkaz\",\n    editEmbed: \"\",\n    create: \"Vytvo\\u0159it odkaz\",\n    createEmbed: \"\",\n    label: \"Odkaz\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Upravit \\u010D\\xE1ru\",\n    exit: \"Ukon\\u010Dit editor \\u0159\\xE1dk\\u016F\"\n  },\n  elementLock: {\n    lock: \"Uzamknout\",\n    unlock: \"Odemknout\",\n    lockAll: \"Uzamknout v\\u0161e\",\n    unlockAll: \"Odemknout v\\u0161e\"\n  },\n  statusPublished: \"Zve\\u0159ejn\\u011Bno\",\n  sidebarLock: \"Ponechat postrann\\xED panel otev\\u0159en\\xFD\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"Vyberte barvu z pl\\xE1tna\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Dosud neexistuj\\xED \\u017E\\xE1dn\\xE9 polo\\u017Eky...\",\n  hint_emptyLibrary: \"Vyberte polo\\u017Eku na pl\\xE1tn\\u011B a p\\u0159idejte ji sem nebo nainstalujte knihovnu z ve\\u0159ejn\\xE9ho \\xFAlo\\u017Ei\\u0161t\\u011B n\\xED\\u017Ee.\",\n  hint_emptyPrivateLibrary: \"Vyberte polo\\u017Eku na pl\\xE1tn\\u011B a p\\u0159idejte ji sem.\"\n};\nvar buttons = {\n  clearReset: \"Resetovat pl\\xE1tno\",\n  exportJSON: \"Exportovat do souboru\",\n  exportImage: \"Exportovat obr\\xE1zek...\",\n  export: \"Ulo\\u017Eit jako...\",\n  copyToClipboard: \"Kop\\xEDrovat do schr\\xE1nky\",\n  save: \"Ulo\\u017Eit do aktu\\xE1ln\\xEDho souboru\",\n  saveAs: \"Ulo\\u017Eit jako\",\n  load: \"Otev\\u0159\\xEDt\",\n  getShareableLink: \"Z\\xEDskat odkaz pro sd\\xEDlen\\xED\",\n  close: \"Zav\\u0159\\xEDt\",\n  selectLanguage: \"Zvolit jazyk\",\n  scrollBackToContent: \"P\\u0159ej\\xEDt zp\\u011Bt na obsah\",\n  zoomIn: \"P\\u0159ibl\\xED\\u017Eit\",\n  zoomOut: \"Odd\\xE1lit\",\n  resetZoom: \"Resetovat p\\u0159ibl\\xED\\u017Een\\xED\",\n  menu: \"Menu\",\n  done: \"Hotovo\",\n  edit: \"Upravit\",\n  undo: \"Zp\\u011Bt\",\n  redo: \"Znovu\",\n  resetLibrary: \"Obnovit knihovnu\",\n  createNewRoom: \"Vytvo\\u0159it novou m\\xEDstnost\",\n  fullScreen: \"Cel\\xE1 obrazovka\",\n  darkMode: \"Tmav\\xFD re\\u017Eim\",\n  lightMode: \"Sv\\u011Btl\\xFD re\\u017Eim\",\n  zenMode: \"Zen m\\xF3d\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Opustit zen m\\xF3d\",\n  cancel: \"Zru\\u0161it\",\n  clear: \"Vy\\u010Distit\",\n  remove: \"Odstranit\",\n  embed: \"\",\n  publishLibrary: \"Zve\\u0159ejnit\",\n  submit: \"Odeslat\",\n  confirm: \"Potvrdit\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Toto vyma\\u017Ee cel\\xE9 pl\\xE1tno. Jste si jisti?\",\n  couldNotCreateShareableLink: \"Nepoda\\u0159ilo se vytvo\\u0159it sd\\xEDliteln\\xFD odkaz.\",\n  couldNotCreateShareableLinkTooBig: \"Nepoda\\u0159ilo se vytvo\\u0159it sd\\xEDlen\\xFD odkaz: sc\\xE9na je p\\u0159\\xEDli\\u0161 velk\\xE1\",\n  couldNotLoadInvalidFile: \"Nepoda\\u0159ilo se na\\u010D\\xEDst neplatn\\xFD soubor\",\n  importBackendFailed: \"Import z backendu se nezda\\u0159il.\",\n  cannotExportEmptyCanvas: \"Nelze exportovat pr\\xE1zdn\\xE9 pl\\xE1tno.\",\n  couldNotCopyToClipboard: \"Nelze zkop\\xEDrovat do schr\\xE1nky.\",\n  decryptFailed: \"Nelze de\\u0161ifrovat data.\",\n  uploadedSecurly: \"Nahr\\xE1v\\xE1n\\xED je zabezpe\\u010Deno koncov\\xFDm \\u0161ifrov\\xE1n\\xEDm, co\\u017E znamen\\xE1, \\u017Ee server Excalidraw ani t\\u0159et\\xED strany nemohou obsah p\\u0159e\\u010D\\xEDst.\",\n  loadSceneOverridePrompt: \"Na\\u010D\\xEDt\\xE1n\\xED extern\\xEDho v\\xFDkresu nahrad\\xED v\\xE1\\u0161 existuj\\xEDc\\xED obsah. P\\u0159ejete si pokra\\u010Dovat?\",\n  collabStopOverridePrompt: \"Zastaven\\xED relace p\\u0159ep\\xED\\u0161e va\\u0161e p\\u0159edchoz\\xED, lok\\xE1ln\\u011B ulo\\u017Een\\xE9 kresby. Jste si jisti?\\n\\n(Pokud chcete zachovat m\\xEDstn\\xED kresbu, jednodu\\u0161e zav\\u0159ete kartu prohl\\xED\\u017Ee\\u010De)\",\n  errorAddingToLibrary: \"Polo\\u017Eku nelze p\\u0159idat do knihovny\",\n  errorRemovingFromLibrary: \"Polo\\u017Eku nelze odstranit z knihovny\",\n  confirmAddLibrary: \"T\\xEDmto p\\u0159id\\xE1te {{numShapes}} tvar\\u016F do tv\\xE9 knihovny. Jste si jisti?\",\n  imageDoesNotContainScene: \"Zd\\xE1 se, \\u017Ee tento obr\\xE1zek neobsahuje \\u017E\\xE1dn\\xE1 data o sc\\xE9n\\u011B. Zapnuli jste p\\u0159i exportu vkl\\xE1d\\xE1n\\xED sc\\xE9ny?\",\n  cannotRestoreFromImage: \"Sc\\xE9nu nelze obnovit z tohoto souboru obr\\xE1zku\",\n  invalidSceneUrl: \"Nelze importovat sc\\xE9nu z zadan\\xE9 URL. Je bu\\u010F po\\u0161kozen\\xE1, nebo neobsahuje platn\\xE1 JSON data Excalidraw.\",\n  resetLibrary: \"T\\xEDmto vyma\\u017Eete va\\u0161i knihovnu. Jste si jisti?\",\n  removeItemsFromsLibrary: \"Smazat {{count}} polo\\u017Eek z knihovny?\",\n  invalidEncryptionKey: \"\\u0160ifrovac\\xED kl\\xED\\u010D mus\\xED m\\xEDt 22 znak\\u016F. Live spolupr\\xE1ce je zak\\xE1z\\xE1na.\",\n  collabOfflineWarning: \"Nen\\xED k dispozici \\u017E\\xE1dn\\xE9 internetov\\xE9 p\\u0159ipojen\\xED.\\nVa\\u0161e zm\\u011Bny nebudou ulo\\u017Eeny!\"\n};\nvar errors = {\n  unsupportedFileType: \"Nepodporovan\\xFD typ souboru.\",\n  imageInsertError: \"Nelze vlo\\u017Eit obr\\xE1zek. Zkuste to pozd\\u011Bji...\",\n  fileTooBig: \"Soubor je p\\u0159\\xEDli\\u0161 velk\\xFD. Maxim\\xE1ln\\xED povolen\\xE1 velikost je {{maxSize}}.\",\n  svgImageInsertError: \"Nelze vlo\\u017Eit SVG obr\\xE1zek. Zna\\u010Den\\xED SVG je neplatn\\xE9.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Neplatn\\xFD SVG.\",\n  cannotResolveCollabServer: \"Nelze se p\\u0159ipojit ke sd\\xEDlen\\xE9mu serveru. Pros\\xEDm obnovte str\\xE1nku a zkuste to znovu.\",\n  importLibraryError: \"Nelze na\\u010D\\xEDst knihovnu\",\n  collabSaveFailed: \"Nelze ulo\\u017Eit do datab\\xE1ze na serveru. Pokud probl\\xE9my p\\u0159etrv\\xE1vaj\\xED, m\\u011Bli byste ulo\\u017Eit soubor lok\\xE1ln\\u011B, abyste se ujistili, \\u017Ee neztrat\\xEDte svou pr\\xE1ci.\",\n  collabSaveFailed_sizeExceeded: \"Nelze ulo\\u017Eit do datab\\xE1ze na serveru, pl\\xE1tno se zd\\xE1 b\\xFDt p\\u0159\\xEDli\\u0161 velk\\xE9. M\\u011Bli byste ulo\\u017Eit soubor lok\\xE1ln\\u011B, abyste se ujistili, \\u017Ee neztrat\\xEDte svou pr\\xE1ci.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"Vypad\\xE1 to, \\u017Ee pou\\u017E\\xEDv\\xE1te Brave prohl\\xED\\u017Ee\\u010D s povolen\\xFDm nastaven\\xEDm <bold>Aggressively Block Fingerprinting</bold>.\",\n    line2: \"To by mohlo v\\xE9st k naru\\u0161en\\xED <bold>Textov\\xFDch element\\u016F</bold> ve va\\u0161ich v\\xFDkresech.\",\n    line3: \"D\\u016Frazn\\u011B doporu\\u010Dujeme zak\\xE1zat toto nastaven\\xED. M\\u016F\\u017Eete sledovat <link>tyto kroky</link> jak to ud\\u011Blat.\",\n    line4: \"Pokud vypnut\\xED tohoto nastaven\\xED neopravuje zobrazen\\xED textov\\xFDch prvk\\u016F, pros\\xEDm, otev\\u0159ete <issueLink>probl\\xE9m</issueLink> na na\\u0161em GitHubu, nebo n\\xE1m napi\\u0161te na <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"V\\xFDb\\u011Br\",\n  image: \"Vlo\\u017Eit obr\\xE1zek\",\n  rectangle: \"Obd\\xE9ln\\xEDk\",\n  diamond: \"Diamant\",\n  ellipse: \"Elipsa\",\n  arrow: \"\\u0160ipka\",\n  line: \"\\u010C\\xE1ra\",\n  freedraw: \"Kreslen\\xED\",\n  text: \"Text\",\n  library: \"Knihovna\",\n  lock: \"Po kreslen\\xED ponechat vybran\\xFD n\\xE1stroj aktivn\\xED\",\n  penMode: \"Re\\u017Eim Pera - zabr\\xE1nit dotyku\",\n  link: \"P\\u0159idat/aktualizovat odkaz pro vybran\\xFD tvar\",\n  eraser: \"Guma\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"Ruka (n\\xE1stroj pro posouv\\xE1n\\xED)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Akce pl\\xE1tna\",\n  selectedShapeActions: \"Akce vybran\\xE9ho tvaru\",\n  shapes: \"Tvary\"\n};\nvar hints = {\n  canvasPanning: \"Chcete-li p\\u0159esunout pl\\xE1tno, podr\\u017Ete kole\\u010Dko nebo mezern\\xEDk p\\u0159i ta\\u017Een\\xED nebo pou\\u017Eijte n\\xE1stroj Ruka\",\n  linearElement: \"Kliknut\\xEDm pro v\\xEDce bod\\u016F, t\\xE1hnut\\xEDm pro jednu \\u010D\\xE1ru\",\n  freeDraw: \"Klikn\\u011Bte a t\\xE1hn\\u011Bte, pro ukon\\u010Den\\xED pus\\u0165te\",\n  text: \"Tip: Text m\\u016F\\u017Eete tak\\xE9 p\\u0159idat dvojit\\xFDm kliknut\\xEDm kdekoli pomoc\\xED n\\xE1stroje pro v\\xFDb\\u011Br\",\n  embeddable: \"\",\n  text_selected: \"Dvojklikem nebo stisknut\\xEDm kl\\xE1vesy ENTER uprav\\xEDte text\",\n  text_editing: \"Stiskn\\u011Bte Escape nebo Ctrl/Cmd+ENTER pro dokon\\u010Den\\xED \\xFAprav\",\n  linearElementMulti: \"Klikn\\u011Bte na posledn\\xED bod nebo stiskn\\u011Bte Escape anebo Enter pro dokon\\u010Den\\xED\",\n  lockAngle: \"\\xDAhel m\\u016F\\u017Eete omezit podr\\u017Een\\xEDm SHIFT\",\n  resize: \"M\\u016F\\u017Eete omezit proporce podr\\u017Een\\xEDm SHIFT p\\u0159i zm\\u011Bn\\u011B velikosti,\\npodr\\u017Ete ALT pro zm\\u011Bnu velikosti od st\\u0159edu\",\n  resizeImage: \"M\\u016F\\u017Eete voln\\u011B zm\\u011Bnit velikost podr\\u017Een\\xEDm SHIFT,\\npodr\\u017Een\\xEDm kl\\xE1vesy ALT zm\\u011Bn\\xEDte velikosti od st\\u0159edu\",\n  rotate: \"\\xDAhly m\\u016F\\u017Eete omezit podr\\u017Een\\xEDm SHIFT p\\u0159i ot\\xE1\\u010Den\\xED\",\n  lineEditor_info: \"Podr\\u017Ete Ctrl/Cmd a dvakr\\xE1t klikn\\u011Bte nebo stiskn\\u011Bte Ctrl/Cmd + Enter pro \\xFApravu bod\\u016F\",\n  lineEditor_pointSelected: \"Stisknut\\xEDm tla\\u010D\\xEDtka Delete odstra\\u0148te bod(y),\\nCtrl/Cmd+D pro duplicitu nebo t\\xE1hnut\\xEDm pro p\\u0159esun\",\n  lineEditor_nothingSelected: \"Vyberte bod, kter\\xFD chcete upravit (podr\\u017Een\\xEDm kl\\xE1vesy SHIFT vyberete v\\xEDce polo\\u017Eek),\\nnebo podr\\u017Een\\xEDm kl\\xE1vesy Alt a kliknut\\xEDm p\\u0159id\\xE1te nov\\xE9 body\",\n  placeImage: \"Kliknut\\xEDm um\\xEDst\\u011Bte obr\\xE1zek, nebo klepnut\\xEDm a p\\u0159eta\\u017Een\\xEDm ru\\u010Dn\\u011B nastav\\xEDte jeho velikost\",\n  publishLibrary: \"Publikovat vlastn\\xED knihovnu\",\n  bindTextToElement: \"Stiskn\\u011Bte Enter pro p\\u0159id\\xE1n\\xED textu\",\n  deepBoxSelect: \"Podr\\u017Ete Ctrl/Cmd pro hlubok\\xFD v\\xFDb\\u011Br a pro zabr\\xE1n\\u011Bn\\xED t\\xE1hnut\\xED\",\n  eraserRevert: \"Podr\\u017Een\\xEDm kl\\xE1vesy Alt vr\\xE1t\\xEDte prvky ozna\\u010Den\\xE9 pro smaz\\xE1n\\xED\",\n  firefox_clipboard_write: 'Tato funkce m\\u016F\\u017Ee b\\xFDt povolena nastaven\\xEDm vlajky \"dom.events.asyncClipboard.clipboardItem\" na \"true\". Chcete-li zm\\u011Bnit vlajky prohl\\xED\\u017Ee\\u010De ve Firefoxu, nav\\u0161tivte str\\xE1nku \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"N\\xE1hled nelze zobrazit\",\n  canvasTooBig: \"Pl\\xE1tno je mo\\u017En\\xE1 p\\u0159\\xEDli\\u0161 velk\\xE9.\",\n  canvasTooBigTip: \"Tip: zkus posunout nejvzd\\xE1len\\u011Bj\\u0161\\xED prvky trochu bl\\xED\\u017Ee k sob\\u011B.\"\n};\nvar errorSplash = {\n  headingMain: \"Chyba. Zkuste <button>znovu na\\u010D\\xEDst str\\xE1nku</button>.\",\n  clearCanvasMessage: \"Pokud op\\u011Btovn\\xE9 na\\u010Dten\\xED nefunguje, zkuste <button>vymazat pl\\xE1tno</button>.\",\n  clearCanvasCaveat: \" To povede ke ztr\\xE1t\\u011B dat \",\n  trackedToSentry: \"Chyba identifik\\xE1toru {{eventId}} byl zaznamen\\xE1n v na\\u0161em syst\\xE9mu.\",\n  openIssueMessage: \"Byli jsme velmi opatrn\\xED, abychom neuv\\xE1d\\u011Bli informace o Va\\u0161\\xED sc\\xE9n\\u011B. Pokud va\\u0161e sc\\xE9na nen\\xED soukrom\\xE1, zva\\u017Ete pros\\xEDm sledov\\xE1n\\xED na na\\u0161em <button>bug trackeru</button>. Uve\\u010Fte pros\\xEDm n\\xED\\u017Ee uveden\\xE9 informace kop\\xEDrov\\xE1n\\xEDm a vlo\\u017Een\\xEDm do probl\\xE9mu na GitHubu.\",\n  sceneContent: \"Obsah sc\\xE9ny:\"\n};\nvar roomDialog = {\n  desc_intro: \"M\\u016F\\u017Eete pozvat lidi na va\\u0161i aktu\\xE1ln\\xED sc\\xE9nu ke spolupr\\xE1ci s v\\xE1mi.\",\n  desc_privacy: \"Nebojte se, relace pou\\u017E\\xEDv\\xE1 end-to-end \\u0161ifrov\\xE1n\\xED, tak\\u017Ee cokoliv nakresl\\xEDte z\\u016Fstane soukrom\\xE9. Ani n\\xE1\\u0161 server nebude schopen vid\\u011Bt, s \\u010D\\xEDm budete pracovat.\",\n  button_startSession: \"Zah\\xE1jit relaci\",\n  button_stopSession: \"Ukon\\u010Dit relaci\",\n  desc_inProgressIntro: \"\\u017Div\\xE1 spolupr\\xE1ce pr\\xE1v\\u011B prob\\xEDh\\xE1.\",\n  desc_shareLink: \"Sd\\xEDlejte tento odkaz s ka\\u017Ed\\xFDm, s k\\xFDm chcete spolupracovat:\",\n  desc_exitSession: \"Zastaven\\xEDm relace se odpoj\\xEDte od m\\xEDstnosti, ale budete moci pokra\\u010Dovat v pr\\xE1ci s touto sc\\xE9nou lok\\xE1ln\\u011B. V\\u0161imn\\u011Bte si, \\u017Ee to nebude m\\xEDt vliv na ostatn\\xED lidi a budou st\\xE1le moci spolupracovat na jejich verzi.\",\n  shareTitle: \"P\\u0159ipojte se k aktivn\\xED spolupr\\xE1ci na Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Chyba\"\n};\nvar exportDialog = {\n  disk_title: \"Ulo\\u017Eit na disk\",\n  disk_details: \"Exportovat data sc\\xE9ny do souboru, ze kter\\xE9ho m\\u016F\\u017Eete importovat pozd\\u011Bji.\",\n  disk_button: \"Ulo\\u017Eit do souboru\",\n  link_title: \"Odkaz pro sd\\xEDlen\\xED\",\n  link_details: \"Exportovat jako odkaz pouze pro \\u010Dten\\xED.\",\n  link_button: \"Exportovat do odkazu\",\n  excalidrawplus_description: \"Ulo\\u017Eit sc\\xE9nu do va\\u0161eho pracovn\\xEDho prostoru Excalidraw+.\",\n  excalidrawplus_button: \"Exportovat\",\n  excalidrawplus_exportError: \"Export do Excalidraw+ se v tuto chv\\xEDli nezda\\u0159il...\"\n};\nvar helpDialog = {\n  blog: \"P\\u0159e\\u010Dt\\u011Bte si n\\xE1\\u0161 blog\",\n  click: \"kliknut\\xED\",\n  deepSelect: \"Hlubok\\xFD v\\xFDb\\u011Br\",\n  deepBoxSelect: \"Hlubok\\xFD v\\xFDb\\u011Br uvnit\\u0159 boxu a zabr\\xE1n\\u011Bn\\xED t\\xE1hnnut\\xED\",\n  curvedArrow: \"Zak\\u0159iven\\xE1 \\u0161ipka\",\n  curvedLine: \"Zak\\u0159iven\\xE1 \\u010D\\xE1ra\",\n  documentation: \"Dokumentace\",\n  doubleClick: \"dvojklik\",\n  drag: \"ta\\u017Een\\xED\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Upravit body lini\\xED/\\u0161ipek\",\n  editText: \"Upravit text / p\\u0159idat popis\",\n  github: \"Na\\u0161el jsi probl\\xE9m? Nahla\\u0161 ho\",\n  howto: \"Sledujte na\\u0161e n\\xE1vody\",\n  or: \"nebo\",\n  preventBinding: \"Zabr\\xE1nit v\\xE1z\\xE1n\\xED \\u0161ipky\",\n  tools: \"N\\xE1stroje\",\n  shortcuts: \"Kl\\xE1vesov\\xE9 zkratky\",\n  textFinish: \"Dokon\\u010Dit \\xFApravy (textov\\xFD editor)\",\n  textNewLine: \"P\\u0159idat nov\\xFD \\u0159\\xE1dek (textov\\xFD editor)\",\n  title: \"N\\xE1pov\\u011Bda\",\n  view: \"Zobrazen\\xED\",\n  zoomToFit: \"P\\u0159ibl\\xED\\u017Eit na zobrazen\\xED v\\u0161ech prvk\\u016F\",\n  zoomToSelection: \"P\\u0159ibl\\xED\\u017Eit na v\\xFDb\\u011Br\",\n  toggleElementLock: \"Zamknout/odemknout v\\xFDb\\u011Br\",\n  movePageUpDown: \"Posunout str\\xE1nku nahoru/dol\\u016F\",\n  movePageLeftRight: \"P\\u0159esunout str\\xE1nku doleva/doprava\"\n};\nvar clearCanvasDialog = {\n  title: \"Vymazat pl\\xE1tno\"\n};\nvar publishDialog = {\n  title: \"Publikovat knihovnu\",\n  itemName: \"N\\xE1zev polo\\u017Eky\",\n  authorName: \"Jm\\xE9no autora\",\n  githubUsername: \"GitHub u\\u017Eivatelsk\\xE9 jm\\xE9no\",\n  twitterUsername: \"Twitter u\\u017Eivatelsk\\xE9 jm\\xE9no\",\n  libraryName: \"N\\xE1zev knihovny\",\n  libraryDesc: \"Popis knihovny\",\n  website: \"Webov\\xE1 str\\xE1nka\",\n  placeholder: {\n    authorName: \"Jm\\xE9no nebo u\\u017Eivatelsk\\xE9 jm\\xE9no\",\n    libraryName: \"N\\xE1zev va\\u0161\\xED knihovny\",\n    libraryDesc: \"Popis Va\\u0161\\xED knihovny, kter\\xFD pom\\u016F\\u017Ee lidem pochopit jej\\xED vyu\\u017Eit\\xED\",\n    githubHandle: \"Github u\\u017Eivatelsk\\xE9 jm\\xE9no (nepovinn\\xE9), abyste mohli upravovat knihovnu pot\\xE9 co je odesl\\xE1na ke kontrole\",\n    twitterHandle: \"Twitter u\\u017Eivatelsk\\xE9 jm\\xE9no (nepovinn\\xE9), abychom v\\u011Bd\\u011Bli koho ozna\\u010Dit p\\u0159i propagaci na Twitteru\",\n    website: \"Odkaz na Va\\u0161i osobn\\xED webovou str\\xE1nku nebo jinam (nepovinn\\xE9)\"\n  },\n  errors: {\n    required: \"Povinn\\xE9\",\n    website: \"Zadejte platnou URL adresu\"\n  },\n  noteDescription: \"Ode\\u0161lete svou knihovnu, pro za\\u0159azen\\xED do <link>ve\\u0159ejn\\xE9ho \\xFAlo\\u017Ei\\u0161t\\u011B knihoven</link>, odkud ji budou moci p\\u0159i kreslen\\xED vyu\\u017E\\xEDt i ostatn\\xED u\\u017Eivatel\\xE9.\",\n  noteGuidelines: \"Knihovna mus\\xED b\\xFDt nejd\\u0159\\xEDve ru\\u010Dn\\u011B schv\\xE1lena. P\\u0159e\\u010Dt\\u011Bte si pros\\xEDm <link>pokyny</link>\",\n  noteLicense: \"Odesl\\xE1n\\xEDm souhlas\\xEDte s t\\xEDm, \\u017Ee knihovna bude zve\\u0159ejn\\u011Bna pod <link>MIT licenc\\xED</link>, stru\\u010Dn\\u011B \\u0159e\\u010Deno, kdokoli ji m\\u016F\\u017Ee pou\\u017E\\xEDvat bez omezen\\xED.\",\n  noteItems: \"Ka\\u017Ed\\xE1 polo\\u017Eka knihovny mus\\xED m\\xEDt sv\\u016Fj vlastn\\xED n\\xE1zev, aby byla filtrovateln\\xE1. N\\xE1sleduj\\xEDc\\xED polo\\u017Eky knihovny budou zahrnuty:\",\n  atleastOneLibItem: \"Vyberte alespo\\u0148 jednu polo\\u017Eku knihovny, kterou chcete za\\u010D\\xEDt\",\n  republishWarning: \"Pozn\\xE1mka: n\\u011Bkter\\xE9 z vybran\\xFDch polo\\u017Eek jsou ozna\\u010Deny jako ji\\u017E zve\\u0159ejn\\u011Bn\\xE9/odeslan\\xE9. Polo\\u017Eky byste m\\u011Bli znovu odeslat pouze p\\u0159i aktualizaci existuj\\xEDc\\xED knihovny nebo pod\\xE1n\\xED.\"\n};\nvar publishSuccessDialog = {\n  title: \"Knihovna byla odesl\\xE1na\",\n  content: \"D\\u011Bkujeme v\\xE1m {{authorName}}. Va\\u0161e knihovna byla odesl\\xE1na k posouzen\\xED. Stav m\\u016F\\u017Eete sledovat <link>zde</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Resetovat knihovnu\",\n  removeItemsFromLib: \"Odstranit vybran\\xE9 polo\\u017Eky z knihovny\"\n};\nvar imageExportDialog = {\n  header: \"Exportovat obr\\xE1zek\",\n  label: {\n    withBackground: \"Pozad\\xED\",\n    onlySelected: \"Pouze vybran\\xE9\",\n    darkMode: \"Tmav\\xFD re\\u017Eim\",\n    embedScene: \"Vlo\\u017Eit sc\\xE9nu\",\n    scale: \"M\\u011B\\u0159\\xEDtko\",\n    padding: \"Odsazen\\xED\"\n  },\n  tooltip: {\n    embedScene: \"Data sc\\xE9ny budou ulo\\u017Eena do exportovan\\xE9ho souboru PNG/SVG tak, aby z n\\u011Bj mohla b\\xFDt sc\\xE9na obnovena.\\nZv\\xFD\\u0161\\xED se velikost exportovan\\xE9ho souboru.\"\n  },\n  title: {\n    exportToPng: \"Exportovat do PNG\",\n    exportToSvg: \"Exportovat do SVG\",\n    copyPngToClipboard: \"Kop\\xEDrovat PNG do schr\\xE1nky\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Kop\\xEDrovat do schr\\xE1nky\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Va\\u0161e kresby jsou end-to-end \\u0161ifrovan\\xE9, tak\\u017Ee servery Excalidraw je nikdy neuvid\\xED.\",\n  link: \"Blog p\\u0159\\xEDsp\\u011Bvek na end-to-end \\u0161ifrov\\xE1n\\xED v Excalidraw\"\n};\nvar stats = {\n  angle: \"\\xDAhel\",\n  element: \"Prvek\",\n  elements: \"Prvky\",\n  height: \"V\\xFD\\u0161ka\",\n  scene: \"Sc\\xE9na\",\n  selected: \"Vybr\\xE1no\",\n  storage: \"\\xDAlo\\u017Ei\\u0161t\\u011B\",\n  title: \"Statistika pro nerdy\",\n  total: \"Celkem\",\n  version: \"Verze\",\n  versionCopy: \"Kliknut\\xEDm zkop\\xEDrujete\",\n  versionNotAvailable: \"Verze nen\\xED k dispozici\",\n  width: \"\\u0160\\xED\\u0159ka\"\n};\nvar toast = {\n  addedToLibrary: \"P\\u0159id\\xE1no do knihovny\",\n  copyStyles: \"Styly byly zkop\\xEDrov\\xE1ny.\",\n  copyToClipboard: \"Zkop\\xEDrov\\xE1no do schr\\xE1nky.\",\n  copyToClipboardAsPng: \"{{exportSelection}} zkop\\xEDrov\\xE1n do schr\\xE1nky jako PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Soubor byl ulo\\u017Een.\",\n  fileSavedToFilename: \"Ulo\\u017Eeno do {filename}\",\n  canvas: \"pl\\xE1tno\",\n  selection: \"v\\xFDb\\u011Br\",\n  pasteAsSingleElement: \"Pomoc\\xED {{shortcut}} vlo\\u017Ete jako jeden prvek,\\nnebo vlo\\u017Ete do existuj\\xEDc\\xEDho textov\\xE9ho editoru\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Pr\\u016Fhledn\\xE1\",\n  black: \"\\u010Cern\\xE1\",\n  white: \"B\\xEDl\\xE1\",\n  red: \"\\u010Cerven\\xE1\",\n  pink: \"R\\u016F\\u017Eov\\xE1\",\n  grape: \"V\\xEDnov\\xE1\",\n  violet: \"Fialov\\xE1\",\n  gray: \"\\u0160ed\\xE1\",\n  blue: \"Modr\\xE1\",\n  cyan: \"Azurov\\xE1\",\n  teal: \"Modrozelen\\xE1\",\n  green: \"Zelen\\xE1\",\n  yellow: \"\\u017Dlut\\xE1\",\n  orange: \"Oran\\u017Eov\\xE1\",\n  bronze: \"Bronzov\\xE1\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"V\\u0161echna va\\u0161e data jsou ulo\\u017Eena lok\\xE1ln\\u011B ve va\\u0161em prohl\\xED\\u017Ee\\u010Di.\",\n    center_heading_plus: \"Chcete m\\xEDsto toho p\\u0159ej\\xEDt na Excalidraw+?\",\n    menuHint: \"Export, nastaven\\xED, jazyky, ...\"\n  },\n  defaults: {\n    menuHint: \"Export, nastaven\\xED a dal\\u0161\\xED...\",\n    center_heading: \"Diagramy. Vytvo\\u0159eny. Jednodu\\u0161e.\",\n    toolbarHint: \"Vyberte n\\xE1stroj a za\\u010Dn\\u011Bte kreslit!\",\n    helpHint: \"Zkratky a pomoc\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Nejpou\\u017E\\xEDvan\\u011Bj\\u0161\\xED vlastn\\xED barvy\",\n  colors: \"Barvy\",\n  shades: \"St\\xEDny\",\n  hexCode: \"Hex k\\xF3d\",\n  noShades: \"Pro tuto barvu nejsou k dispozici \\u017E\\xE1dn\\xE9 odst\\xEDny\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar cs_CZ_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=cs-CZ-DGZA5IKG.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/cs-CZ-DGZA5IKG.js\n"));

/***/ })

}]);