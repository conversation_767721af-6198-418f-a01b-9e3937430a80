"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_th-TH-55ACRHDJ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/th-TH-55ACRHDJ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/th-TH-55ACRHDJ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ th_TH_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/th-TH.json\nvar labels = {\n  paste: \"\\u0E27\\u0E32\\u0E07\",\n  pasteAsPlaintext: \"\\u0E27\\u0E32\\u0E07\\u0E42\\u0E14\\u0E22\\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E01\\u0E32\\u0E23\\u0E08\\u0E31\\u0E14\\u0E23\\u0E39\\u0E1B\\u0E41\\u0E1A\\u0E1A\",\n  pasteCharts: \"\\u0E27\\u0E32\\u0E07\\u0E41\\u0E1C\\u0E19\\u0E20\\u0E39\\u0E21\\u0E34\",\n  selectAll: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\",\n  multiSelect: \"\",\n  moveCanvas: \"\",\n  cut: \"\\u0E15\\u0E31\\u0E14\",\n  copy: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E01\",\n  copyAsPng: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E07\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\\u0E40\\u0E1B\\u0E47\\u0E19 PNG\",\n  copyAsSvg: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E07\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\\u0E40\\u0E1B\\u0E47\\u0E19 SVG\",\n  copyText: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E07\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\\u0E40\\u0E1B\\u0E47\\u0E19\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"\\u0E19\\u0E33\\u0E02\\u0E36\\u0E49\\u0E19\\u0E02\\u0E49\\u0E32\\u0E07\\u0E1A\\u0E19\",\n  sendToBack: \"\\u0E22\\u0E49\\u0E32\\u0E22\\u0E44\\u0E1B\\u0E02\\u0E49\\u0E32\\u0E07\\u0E25\\u0E48\\u0E32\\u0E07\",\n  bringToFront: \"\\u0E19\\u0E33\\u0E02\\u0E36\\u0E49\\u0E19\\u0E02\\u0E49\\u0E32\\u0E07\\u0E2B\\u0E19\\u0E49\\u0E32\",\n  sendBackward: \"\\u0E22\\u0E49\\u0E32\\u0E22\\u0E44\\u0E1B\\u0E02\\u0E49\\u0E32\\u0E07\\u0E2B\\u0E25\\u0E31\\u0E07\",\n  delete: \"\\u0E25\\u0E1A\",\n  copyStyles: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E01\\u0E23\\u0E39\\u0E1B\\u0E41\\u0E1A\\u0E1A\",\n  pasteStyles: \"\\u0E27\\u0E32\\u0E07\\u0E23\\u0E39\\u0E1B\\u0E41\\u0E1A\\u0E1A\",\n  stroke: \"\\u0E40\\u0E2A\\u0E49\\u0E19\\u0E02\\u0E2D\\u0E1A\",\n  background: \"\\u0E1E\\u0E37\\u0E49\\u0E19\\u0E2B\\u0E25\\u0E31\\u0E07\",\n  fill: \"\\u0E40\\u0E15\\u0E34\\u0E21\\u0E2A\\u0E35\",\n  strokeWidth: \"\\u0E19\\u0E49\\u0E33\\u0E2B\\u0E19\\u0E31\\u0E01\\u0E40\\u0E2A\\u0E49\\u0E19\\u0E02\\u0E2D\\u0E1A\",\n  strokeStyle: \"\\u0E23\\u0E39\\u0E1B\\u0E41\\u0E1A\\u0E1A\\u0E40\\u0E2A\\u0E49\\u0E19\",\n  strokeStyle_solid: \"\\u0E40\\u0E2A\\u0E49\\u0E19\\u0E17\\u0E36\\u0E1A\",\n  strokeStyle_dashed: \"\\u0E40\\u0E2A\\u0E49\\u0E19\\u0E1B\\u0E23\\u0E30\",\n  strokeStyle_dotted: \"\\u0E08\\u0E38\\u0E14\",\n  sloppiness: \"\\u0E04\\u0E27\\u0E32\\u0E21\\u0E40\\u0E25\\u0E2D\\u0E30\\u0E40\\u0E17\\u0E2D\\u0E30\",\n  opacity: \"\\u0E04\\u0E27\\u0E32\\u0E21\\u0E17\\u0E36\\u0E1A\\u0E41\\u0E2A\\u0E07\",\n  textAlign: \"\\u0E08\\u0E31\\u0E14\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21\",\n  edges: \"\\u0E02\\u0E2D\\u0E1A\",\n  sharp: \"\",\n  round: \"\",\n  arrowheads: \"\\u0E2B\\u0E31\\u0E27\\u0E25\\u0E39\\u0E01\\u0E28\\u0E23\",\n  arrowhead_none: \"\\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\",\n  arrowhead_arrow: \"\\u0E25\\u0E39\\u0E01\\u0E28\\u0E23\",\n  arrowhead_bar: \"\\u0E41\\u0E16\\u0E1A\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"\\u0E2A\\u0E32\\u0E21\\u0E40\\u0E2B\\u0E25\\u0E35\\u0E48\\u0E22\\u0E21\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"\\u0E02\\u0E19\\u0E32\\u0E14\\u0E15\\u0E31\\u0E27\\u0E2D\\u0E31\\u0E01\\u0E29\\u0E23\",\n  fontFamily: \"\\u0E41\\u0E1A\\u0E1A\\u0E15\\u0E31\\u0E27\\u0E2D\\u0E31\\u0E01\\u0E29\\u0E23\",\n  addWatermark: '\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E25\\u0E32\\u0E22\\u0E19\\u0E49\\u0E33 \"\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E14\\u0E49\\u0E27\\u0E22 Excalidraw\"',\n  handDrawn: \"\\u0E25\\u0E32\\u0E22\\u0E21\\u0E37\\u0E2D\",\n  normal: \"\\u0E1B\\u0E01\\u0E15\\u0E34\",\n  code: \"\\u0E42\\u0E04\\u0E49\\u0E14\",\n  small: \"\\u0E40\\u0E25\\u0E47\\u0E01\",\n  medium: \"\\u0E01\\u0E25\\u0E32\\u0E07\",\n  large: \"\\u0E43\\u0E2B\\u0E0D\\u0E48\",\n  veryLarge: \"\\u0E43\\u0E2B\\u0E0D\\u0E48\\u0E21\\u0E32\\u0E01\",\n  solid: \"\",\n  hachure: \"\",\n  zigzag: \"\",\n  crossHatch: \"\",\n  thin: \"\\u0E1A\\u0E32\\u0E07\",\n  bold: \"\\u0E2B\\u0E19\\u0E32\",\n  left: \"\\u0E0B\\u0E49\\u0E32\\u0E22\",\n  center: \"\\u0E01\\u0E25\\u0E32\\u0E07\",\n  right: \"\\u0E02\\u0E27\\u0E32\",\n  extraBold: \"\\u0E2B\\u0E19\\u0E32\\u0E1E\\u0E34\\u0E40\\u0E28\\u0E29\",\n  architect: \"\",\n  artist: \"\\u0E28\\u0E34\\u0E25\\u0E1B\\u0E34\\u0E19\",\n  cartoonist: \"\",\n  fileTitle: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E44\\u0E1F\\u0E25\\u0E4C\",\n  colorPicker: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E2A\\u0E35\\u0E17\\u0E35\\u0E48\\u0E01\\u0E33\\u0E2B\\u0E19\\u0E14\\u0E40\\u0E2D\\u0E07\",\n  canvasColors: \"\",\n  canvasBackground: \"\",\n  drawingCanvas: \"\",\n  layers: \"\",\n  actions: \"\\u0E01\\u0E32\\u0E23\\u0E01\\u0E23\\u0E30\\u0E17\\u0E33\",\n  language: \"\\u0E20\\u0E32\\u0E29\\u0E32\",\n  liveCollaboration: \"\",\n  duplicateSelection: \"\\u0E17\\u0E33\\u0E2A\\u0E33\\u0E40\\u0E19\\u0E32\",\n  untitled: \"\\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E0A\\u0E37\\u0E48\\u0E2D\",\n  name: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\",\n  yourName: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E02\\u0E2D\\u0E07\\u0E04\\u0E38\\u0E13\",\n  madeWithExcalidraw: \"\",\n  group: \"\\u0E08\\u0E31\\u0E14\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\",\n  ungroup: \"\\u0E22\\u0E01\\u0E40\\u0E25\\u0E34\\u0E01\\u0E01\\u0E32\\u0E23\\u0E08\\u0E31\\u0E14\\u0E01\\u0E25\\u0E38\\u0E48\\u0E21\",\n  collaborators: \"\",\n  showGrid: \"\\u0E41\\u0E2A\\u0E14\\u0E07\\u0E40\\u0E2A\\u0E49\\u0E19\\u0E15\\u0E32\\u0E23\\u0E32\\u0E07\",\n  addToLibrary: \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E44\\u0E1B\\u0E43\\u0E19\\u0E04\\u0E25\\u0E31\\u0E07\",\n  removeFromLibrary: \"\\u0E19\\u0E33\\u0E2D\\u0E2D\\u0E01\\u0E08\\u0E32\\u0E01\\u0E04\\u0E25\\u0E31\\u0E07\",\n  libraryLoadingMessage: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E04\\u0E25\\u0E31\\u0E07...\",\n  libraries: \"\",\n  loadingScene: \"\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E09\\u0E32\\u0E01\",\n  align: \"\\u0E08\\u0E31\\u0E14\\u0E15\\u0E33\\u0E41\\u0E2B\\u0E19\\u0E48\\u0E07\",\n  alignTop: \"\\u0E08\\u0E31\\u0E14\\u0E0A\\u0E34\\u0E14\\u0E14\\u0E49\\u0E32\\u0E19\\u0E1A\\u0E19\",\n  alignBottom: \"\\u0E08\\u0E31\\u0E14\\u0E0A\\u0E34\\u0E14\\u0E14\\u0E49\\u0E32\\u0E19\\u0E25\\u0E48\\u0E32\\u0E07\",\n  alignLeft: \"\\u0E08\\u0E31\\u0E14\\u0E0A\\u0E34\\u0E14\\u0E0B\\u0E49\\u0E32\\u0E22\",\n  alignRight: \"\\u0E08\\u0E31\\u0E14\\u0E0A\\u0E34\\u0E14\\u0E02\\u0E27\\u0E32\",\n  centerVertically: \"\\u0E01\\u0E36\\u0E48\\u0E07\\u0E01\\u0E25\\u0E32\\u0E07\\u0E41\\u0E19\\u0E27\\u0E15\\u0E31\\u0E49\\u0E07\",\n  centerHorizontally: \"\\u0E01\\u0E36\\u0E48\\u0E07\\u0E01\\u0E25\\u0E32\\u0E07\\u0E41\\u0E19\\u0E27\\u0E19\\u0E2D\\u0E19\",\n  distributeHorizontally: \"\\u0E01\\u0E23\\u0E30\\u0E08\\u0E32\\u0E22\\u0E41\\u0E19\\u0E27\\u0E19\\u0E2D\\u0E19\",\n  distributeVertically: \"\\u0E01\\u0E23\\u0E30\\u0E08\\u0E32\\u0E22\\u0E41\\u0E19\\u0E27\\u0E15\\u0E31\\u0E49\\u0E07\",\n  flipHorizontal: \"\\u0E1E\\u0E25\\u0E34\\u0E01\\u0E41\\u0E19\\u0E27\\u0E19\\u0E2D\\u0E19\",\n  flipVertical: \"\\u0E1E\\u0E25\\u0E34\\u0E01\\u0E41\\u0E19\\u0E27\\u0E15\\u0E31\\u0E49\\u0E07\",\n  viewMode: \"\\u0E42\\u0E2B\\u0E21\\u0E14\\u0E21\\u0E38\\u0E21\\u0E21\\u0E2D\\u0E07\",\n  share: \"\\u0E41\\u0E0A\\u0E23\\u0E4C\",\n  showStroke: \"\",\n  showBackground: \"\",\n  toggleTheme: \"\\u0E2A\\u0E25\\u0E31\\u0E1A\\u0E18\\u0E35\\u0E21\",\n  personalLib: \"\\u0E04\\u0E25\\u0E31\\u0E07\\u0E02\\u0E2D\\u0E07\\u0E09\\u0E31\\u0E19\",\n  excalidrawLib: \"\\u0E04\\u0E25\\u0E31\\u0E07\\u0E02\\u0E2D\\u0E07 Excalidraw\",\n  decreaseFontSize: \"\\u0E25\\u0E14\\u0E02\\u0E19\\u0E32\\u0E14\\u0E15\\u0E31\\u0E27\\u0E2D\\u0E31\\u0E01\\u0E29\\u0E23\",\n  increaseFontSize: \"\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E02\\u0E19\\u0E32\\u0E14\\u0E15\\u0E31\\u0E27\\u0E2D\\u0E31\\u0E01\\u0E29\\u0E23\",\n  unbindText: \"\\u0E22\\u0E01\\u0E40\\u0E25\\u0E34\\u0E01\\u0E01\\u0E32\\u0E23\\u0E1C\\u0E39\\u0E01\\u0E15\\u0E34\\u0E14\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E25\\u0E34\\u0E07\\u0E01\\u0E4C\",\n    editEmbed: \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\\u0E41\\u0E25\\u0E30\\u0E01\\u0E32\\u0E23\\u0E1D\\u0E31\\u0E07\",\n    create: \"\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\",\n    createEmbed: \"\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\\u0E41\\u0E25\\u0E30\\u0E01\\u0E32\\u0E23\\u0E1D\\u0E31\\u0E07\",\n    label: \"\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\",\n    labelEmbed: \"\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\\u0E41\\u0E25\\u0E30\\u0E01\\u0E32\\u0E23\\u0E1D\\u0E31\\u0E07\",\n    empty: \"\\u0E44\\u0E21\\u0E48\\u0E44\\u0E14\\u0E49\\u0E43\\u0E2A\\u0E48\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\"\n  },\n  lineEditor: {\n    edit: \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E40\\u0E2A\\u0E49\\u0E19\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"\\u0E25\\u0E47\\u0E2D\\u0E01\",\n    unlock: \"\\u0E1B\\u0E25\\u0E14\\u0E25\\u0E47\\u0E2D\\u0E01\",\n    lockAll: \"\\u0E25\\u0E47\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\",\n    unlockAll: \"\\u0E1B\\u0E25\\u0E14\\u0E25\\u0E47\\u0E2D\\u0E01\\u0E17\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E21\\u0E14\"\n  },\n  statusPublished: \"\\u0E40\\u0E1C\\u0E22\\u0E41\\u0E1E\\u0E23\\u0E48\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\\u0E22\\u0E31\\u0E07\\u0E44\\u0E21\\u0E48\\u0E21\\u0E35\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E17\\u0E35\\u0E48\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E40\\u0E02\\u0E49\\u0E32\\u0E44\\u0E1B\\u0E44\\u0E14\\u0E49\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"\\u0E23\\u0E35\\u0E40\\u0E0B\\u0E47\\u0E17\\u0E1C\\u0E37\\u0E19\\u0E1C\\u0E49\\u0E32\\u0E43\\u0E1A\",\n  exportJSON: \"\\u0E2A\\u0E48\\u0E07\\u0E2D\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E44\\u0E1F\\u0E25\\u0E4C\",\n  exportImage: \"\\u0E2A\\u0E48\\u0E07\\u0E2D\\u0E2D\\u0E01\\u0E40\\u0E1B\\u0E47\\u0E19\\u0E23\\u0E39\\u0E1B\\u0E20\\u0E32\\u0E1E\",\n  export: \"\\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\",\n  copyToClipboard: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\",\n  save: \"\\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\\u0E40\\u0E1B\\u0E47\\u0E19\\u0E44\\u0E1F\\u0E25\\u0E4C\\u0E1B\\u0E31\\u0E08\\u0E08\\u0E38\\u0E1A\\u0E31\\u0E19\",\n  saveAs: \"\\u0E1A\\u0E31\\u0E19\\u0E17\\u0E36\\u0E01\\u0E40\\u0E1B\\u0E47\\u0E19\",\n  load: \"\\u0E40\\u0E1B\\u0E34\\u0E14\",\n  getShareableLink: \"\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\\u0E17\\u0E35\\u0E48\\u0E41\\u0E0A\\u0E23\\u0E4C\\u0E44\\u0E14\\u0E49\",\n  close: \"\\u0E1B\\u0E34\\u0E14\",\n  selectLanguage: \"\\u0E40\\u0E25\\u0E37\\u0E2D\\u0E01\\u0E20\\u0E32\\u0E29\\u0E32\",\n  scrollBackToContent: \"\\u0E40\\u0E25\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E01\\u0E25\\u0E31\\u0E1A\\u0E44\\u0E1B\\u0E14\\u0E49\\u0E32\\u0E19\\u0E1A\\u0E19\",\n  zoomIn: \"\\u0E0B\\u0E39\\u0E21\\u0E40\\u0E02\\u0E49\\u0E32\",\n  zoomOut: \"\\u0E0B\\u0E39\\u0E21\\u0E2D\\u0E2D\\u0E01\",\n  resetZoom: \"\\u0E23\\u0E35\\u0E40\\u0E0B\\u0E47\\u0E15\\u0E01\\u0E32\\u0E23\\u0E0B\\u0E39\\u0E21\",\n  menu: \"\\u0E40\\u0E21\\u0E19\\u0E39\",\n  done: \"\\u0E40\\u0E2A\\u0E23\\u0E47\\u0E08\\u0E2A\\u0E34\\u0E49\\u0E19\",\n  edit: \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\",\n  undo: \"\\u0E40\\u0E25\\u0E34\\u0E01\\u0E17\\u0E33\",\n  redo: \"\\u0E17\\u0E33\\u0E0B\\u0E49\\u0E33\",\n  resetLibrary: \"\\u0E23\\u0E35\\u0E40\\u0E0B\\u0E47\\u0E15\\u0E04\\u0E25\\u0E31\\u0E07\",\n  createNewRoom: \"\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E2B\\u0E49\\u0E2D\\u0E07\\u0E43\\u0E2B\\u0E21\\u0E48\",\n  fullScreen: \"\\u0E40\\u0E15\\u0E47\\u0E21\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E08\\u0E2D\",\n  darkMode: \"\\u0E42\\u0E2B\\u0E21\\u0E14\\u0E01\\u0E25\\u0E32\\u0E07\\u0E04\\u0E37\\u0E19\",\n  lightMode: \"\\u0E42\\u0E2B\\u0E21\\u0E14\\u0E01\\u0E25\\u0E32\\u0E07\\u0E27\\u0E31\\u0E19\",\n  zenMode: \"\\u0E42\\u0E2B\\u0E21\\u0E14 Zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"\\u0E2D\\u0E2D\\u0E01\\u0E08\\u0E32\\u0E01\\u0E42\\u0E2B\\u0E21\\u0E14 Zen\",\n  cancel: \"\\u0E22\\u0E01\\u0E40\\u0E25\\u0E34\\u0E01\",\n  clear: \"\\u0E40\\u0E04\\u0E25\\u0E35\\u0E22\\u0E23\\u0E4C\",\n  remove: \"\\u0E25\\u0E1A\",\n  embed: \"\\u0E2A\\u0E25\\u0E31\\u0E1A\\u0E01\\u0E32\\u0E23\\u0E1D\\u0E31\\u0E07\",\n  publishLibrary: \"\\u0E40\\u0E1C\\u0E22\\u0E41\\u0E1E\\u0E23\\u0E48\",\n  submit: \"\\u0E15\\u0E01\\u0E25\\u0E07\",\n  confirm: \"\\u0E22\\u0E37\\u0E19\\u0E22\\u0E31\\u0E19\",\n  embeddableInteractionButton: \"\\u0E04\\u0E25\\u0E34\\u0E01\\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E1B\\u0E0F\\u0E34\\u0E2A\\u0E31\\u0E21\\u0E1E\\u0E31\\u0E19\\u0E18\\u0E4C\"\n};\nvar alerts = {\n  clearReset: \"\",\n  couldNotCreateShareableLink: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E25\\u0E34\\u0E07\\u0E04\\u0E4C\\u0E44\\u0E14\\u0E49\",\n  couldNotCreateShareableLinkTooBig: \"\",\n  couldNotLoadInvalidFile: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E44\\u0E1F\\u0E25\\u0E4C\\u0E17\\u0E35\\u0E48\\u0E1C\\u0E34\\u0E14\\u0E1E\\u0E25\\u0E32\\u0E14\\u0E44\\u0E14\\u0E49\",\n  importBackendFailed: \"\\u0E40\\u0E01\\u0E34\\u0E14\\u0E02\\u0E49\\u0E2D\\u0E1C\\u0E34\\u0E14\\u0E1E\\u0E25\\u0E32\\u0E14\\u0E08\\u0E32\\u0E01\\u0E01\\u0E32\\u0E23\\u0E19\\u0E33\\u0E40\\u0E02\\u0E49\\u0E32\\u0E08\\u0E32\\u0E01\\u0E23\\u0E30\\u0E1A\\u0E1A\\u0E2B\\u0E25\\u0E31\\u0E07\\u0E1A\\u0E49\\u0E32\\u0E19\",\n  cannotExportEmptyCanvas: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E19\\u0E33\\u0E2D\\u0E2D\\u0E01\\u0E08\\u0E32\\u0E01\\u0E1C\\u0E37\\u0E19\\u0E1C\\u0E49\\u0E32\\u0E43\\u0E1A\\u0E17\\u0E35\\u0E48\\u0E27\\u0E48\\u0E32\\u0E07\\u0E40\\u0E1B\\u0E25\\u0E48\\u0E32\\u0E44\\u0E14\\u0E49\",\n  couldNotCopyToClipboard: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\\u0E44\\u0E14\\u0E49\",\n  decryptFailed: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E16\\u0E2D\\u0E14\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E44\\u0E14\\u0E49\",\n  uploadedSecurly: \"\\u0E01\\u0E32\\u0E23\\u0E2D\\u0E31\\u0E1E\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E44\\u0E14\\u0E49\\u0E16\\u0E39\\u0E01\\u0E40\\u0E02\\u0E49\\u0E32\\u0E23\\u0E2B\\u0E31\\u0E2A\\u0E41\\u0E1A\\u0E1A end-to-end \\u0E2B\\u0E21\\u0E32\\u0E22\\u0E04\\u0E27\\u0E32\\u0E21\\u0E27\\u0E48\\u0E32\\u0E40\\u0E0B\\u0E34\\u0E23\\u0E4C\\u0E1F\\u0E40\\u0E27\\u0E2D\\u0E23\\u0E4C\\u0E02\\u0E2D\\u0E07 Excalidraw \\u0E41\\u0E25\\u0E30\\u0E1A\\u0E38\\u0E04\\u0E04\\u0E25\\u0E2D\\u0E37\\u0E48\\u0E19\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E2D\\u0E48\\u0E32\\u0E19\\u0E02\\u0E49\\u0E2D\\u0E21\\u0E39\\u0E25\\u0E44\\u0E14\\u0E49\",\n  loadSceneOverridePrompt: \"\",\n  collabStopOverridePrompt: \"\",\n  errorAddingToLibrary: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E40\\u0E02\\u0E49\\u0E32\\u0E44\\u0E1B\\u0E43\\u0E19\\u0E04\\u0E25\\u0E31\\u0E07\\u0E44\\u0E14\\u0E49\",\n  errorRemovingFromLibrary: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E25\\u0E1A\\u0E23\\u0E32\\u0E22\\u0E01\\u0E32\\u0E23\\u0E19\\u0E35\\u0E49\\u0E2D\\u0E2D\\u0E01\\u0E08\\u0E32\\u0E01\\u0E04\\u0E25\\u0E31\\u0E07\\u0E44\\u0E14\\u0E49\",\n  confirmAddLibrary: \"\",\n  imageDoesNotContainScene: \"\",\n  cannotRestoreFromImage: \"\",\n  invalidSceneUrl: \"\",\n  resetLibrary: \"\",\n  removeItemsFromsLibrary: \"\",\n  invalidEncryptionKey: \"\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"\\u0E44\\u0E21\\u0E48\\u0E23\\u0E2D\\u0E07\\u0E23\\u0E31\\u0E1A\\u0E0A\\u0E19\\u0E34\\u0E14\\u0E02\\u0E2D\\u0E07\\u0E44\\u0E1F\\u0E25\\u0E4C\\u0E19\\u0E35\\u0E49\",\n  imageInsertError: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E39\\u0E1B\\u0E20\\u0E32\\u0E1E\\u0E44\\u0E14\\u0E49 \\u0E25\\u0E2D\\u0E07\\u0E2D\\u0E35\\u0E01\\u0E04\\u0E23\\u0E31\\u0E49\\u0E07\\u0E43\\u0E19\\u0E20\\u0E32\\u0E22\\u0E2B\\u0E25\\u0E31\\u0E07\",\n  fileTooBig: \"\",\n  svgImageInsertError: \"\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"\\u0E44\\u0E1F\\u0E25\\u0E4C SVG \\u0E1C\\u0E34\\u0E14\\u0E1E\\u0E25\\u0E32\\u0E14\",\n  cannotResolveCollabServer: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E40\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E15\\u0E48\\u0E2D\\u0E01\\u0E31\\u0E1A collab \\u0E40\\u0E0B\\u0E34\\u0E23\\u0E4C\\u0E1F\\u0E40\\u0E27\\u0E2D\\u0E23\\u0E4C\\u0E44\\u0E14\\u0E49 \\u0E42\\u0E1B\\u0E23\\u0E14\\u0E25\\u0E2D\\u0E07\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E19\\u0E35\\u0E49\\u0E43\\u0E2B\\u0E21\\u0E48\\u0E41\\u0E25\\u0E30\\u0E25\\u0E2D\\u0E07\\u0E2D\\u0E35\\u0E01\\u0E04\\u0E23\\u0E31\\u0E49\\u0E07\",\n  importLibraryError: \"\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\\u0E01\\u0E32\\u0E23\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E2D\\u0E07\\u0E04\\u0E4C\\u0E1B\\u0E23\\u0E30\\u0E01\\u0E2D\\u0E1A\\u0E17\\u0E35\\u0E48\\u0E1D\\u0E31\\u0E07\\u0E22\\u0E31\\u0E07\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E40\\u0E02\\u0E49\\u0E32\\u0E44\\u0E1B\\u0E43\\u0E19\\u0E44\\u0E25\\u0E1A\\u0E25\\u0E32\\u0E23\\u0E35\\u0E44\\u0E14\\u0E49\",\n    iframe: \"\",\n    image: \"\\u0E01\\u0E32\\u0E23\\u0E2A\\u0E19\\u0E31\\u0E1A\\u0E2A\\u0E19\\u0E38\\u0E19\\u0E2A\\u0E33\\u0E2B\\u0E23\\u0E31\\u0E1A\\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E23\\u0E39\\u0E1B\\u0E20\\u0E32\\u0E1E\\u0E25\\u0E07\\u0E43\\u0E19\\u0E44\\u0E25\\u0E1A\\u0E25\\u0E32\\u0E23\\u0E35\\u0E08\\u0E30\\u0E21\\u0E32\\u0E43\\u0E19\\u0E40\\u0E23\\u0E47\\u0E27 \\u0E46 \\u0E19\\u0E35\\u0E49\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"\",\n  image: \"\",\n  rectangle: \"\\u0E2A\\u0E35\\u0E48\\u0E40\\u0E2B\\u0E25\\u0E35\\u0E48\\u0E22\\u0E21\\u0E1C\\u0E37\\u0E19\\u0E1C\\u0E49\\u0E32\",\n  diamond: \"\",\n  ellipse: \"\\u0E27\\u0E07\\u0E23\\u0E35\",\n  arrow: \"\\u0E25\\u0E39\\u0E01\\u0E28\\u0E23\",\n  line: \"\",\n  freedraw: \"\",\n  text: \"\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21\",\n  library: \"\\u0E04\\u0E25\\u0E31\\u0E07\",\n  lock: \"\",\n  penMode: \"\",\n  link: \"\",\n  eraser: \"\\u0E22\\u0E32\\u0E07\\u0E25\\u0E1A\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\\u0E1D\\u0E31\\u0E07\\u0E40\\u0E27\\u0E47\\u0E1A\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\\u0E40\\u0E04\\u0E23\\u0E37\\u0E48\\u0E2D\\u0E07\\u0E21\\u0E37\\u0E2D\\u0E2D\\u0E37\\u0E48\\u0E19\\u0E46\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"\",\n  selectedShapeActions: \"\",\n  shapes: \"\\u0E23\\u0E39\\u0E1B\\u0E23\\u0E48\\u0E32\\u0E07\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"\",\n  freeDraw: \"\",\n  text: \"\",\n  embeddable: \"\\u0E04\\u0E25\\u0E34\\u0E01\\u0E41\\u0E25\\u0E30\\u0E25\\u0E32\\u0E01\\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E2A\\u0E23\\u0E49\\u0E32\\u0E07\\u0E01\\u0E32\\u0E23\\u0E1D\\u0E31\\u0E07\\u0E2A\\u0E33\\u0E2B\\u0E23\\u0E31\\u0E1A\\u0E40\\u0E27\\u0E47\\u0E1A\\u0E44\\u0E0B\\u0E15\\u0E4C\",\n  text_selected: \"\\u0E04\\u0E25\\u0E34\\u0E01\\u0E2A\\u0E2D\\u0E07\\u0E04\\u0E23\\u0E31\\u0E49\\u0E07\\u0E2B\\u0E23\\u0E37\\u0E2D\\u0E01\\u0E14 ENTER \\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21\",\n  text_editing: \"\\u0E01\\u0E14\\u0E1B\\u0E38\\u0E48\\u0E21 Esc \\u0E2B\\u0E23\\u0E37\\u0E2D\\u0E01\\u0E14 Ctrl, Cmd + Enter \\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E40\\u0E2A\\u0E23\\u0E47\\u0E08\\u0E01\\u0E32\\u0E23\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\",\n  linearElementMulti: \"\\u0E04\\u0E25\\u0E34\\u0E01\\u0E17\\u0E35\\u0E48\\u0E08\\u0E38\\u0E14\\u0E2A\\u0E38\\u0E14\\u0E17\\u0E49\\u0E32\\u0E22\\u0E2B\\u0E23\\u0E37\\u0E2D\\u0E01\\u0E14 Escape \\u0E2B\\u0E23\\u0E37\\u0E2D Enter \\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E40\\u0E2A\\u0E23\\u0E47\\u0E08\\u0E2A\\u0E34\\u0E49\\u0E19\",\n  lockAngle: \"\",\n  resize: \"\",\n  resizeImage: \"\",\n  rotate: \"\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\\u0E01\\u0E14\\u0E1B\\u0E38\\u0E48\\u0E21 Delete \\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E25\\u0E1A\\u0E08\\u0E38\\u0E14\\n\\u0E01\\u0E14 Ctrl \\u0E2B\\u0E23\\u0E37\\u0E2D Cmd + D \\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E17\\u0E33\\u0E0B\\u0E49\\u0E33\\u0E2B\\u0E23\\u0E37\\u0E2D\\u0E25\\u0E32\\u0E01\\u0E40\\u0E1E\\u0E37\\u0E48\\u0E2D\\u0E40\\u0E04\\u0E25\\u0E37\\u0E48\\u0E2D\\u0E19\\u0E22\\u0E49\\u0E32\\u0E22\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\",\n  canvasTooBig: \"\",\n  canvasTooBigTip: \"\"\n};\nvar errorSplash = {\n  headingMain: \"<button>\\u0E01\\u0E33\\u0E25\\u0E31\\u0E07\\u0E23\\u0E35\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E2B\\u0E19\\u0E49\\u0E32</button>\",\n  clearCanvasMessage: \"\\u0E16\\u0E49\\u0E32\\u0E42\\u0E2B\\u0E25\\u0E14\\u0E44\\u0E21\\u0E48\\u0E44\\u0E14\\u0E49 \\u0E43\\u0E2B\\u0E49\\u0E25\\u0E2D\\u0E07 <button>\\u0E40\\u0E04\\u0E25\\u0E35\\u0E22\\u0E23\\u0E4C\\u0E1C\\u0E37\\u0E19\\u0E1C\\u0E49\\u0E32\\u0E43\\u0E1A</button>\",\n  clearCanvasCaveat: \"\",\n  trackedToSentry: \"\",\n  openIssueMessage: \"\",\n  sceneContent: \"\"\n};\nvar roomDialog = {\n  desc_intro: \"\",\n  desc_privacy: \"\",\n  button_startSession: \"\\u0E40\\u0E23\\u0E34\\u0E48\\u0E21\\u0E40\\u0E0B\\u0E2A\\u0E0A\\u0E31\\u0E19\",\n  button_stopSession: \"\\u0E2B\\u0E22\\u0E38\\u0E14\\u0E40\\u0E0B\\u0E2A\\u0E0A\\u0E31\\u0E19\",\n  desc_inProgressIntro: \"\",\n  desc_shareLink: \"\",\n  desc_exitSession: \"\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"\"\n};\nvar exportDialog = {\n  disk_title: \"\",\n  disk_details: \"\",\n  disk_button: \"\",\n  link_title: \"\",\n  link_details: \"\",\n  link_button: \"\",\n  excalidrawplus_description: \"\",\n  excalidrawplus_button: \"\",\n  excalidrawplus_exportError: \"\\u0E44\\u0E21\\u0E48\\u0E2A\\u0E32\\u0E21\\u0E32\\u0E23\\u0E16\\u0E2A\\u0E48\\u0E07\\u0E2D\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E17\\u0E35\\u0E48 Excalidraw+ \\u0E44\\u0E14\\u0E49\\u0E43\\u0E19\\u0E02\\u0E13\\u0E30\\u0E19\\u0E35\\u0E49\"\n};\nvar helpDialog = {\n  blog: \"\\u0E2D\\u0E48\\u0E32\\u0E19\\u0E1A\\u0E25\\u0E47\\u0E2D\\u0E01\\u0E02\\u0E2D\\u0E07\\u0E1E\\u0E27\\u0E01\\u0E40\\u0E23\\u0E32\",\n  click: \"\\u0E04\\u0E25\\u0E34\\u0E01\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"\",\n  curvedLine: \"\",\n  documentation: \"\",\n  doubleClick: \"\\u0E14\\u0E31\\u0E1A\\u0E40\\u0E1A\\u0E34\\u0E25\\u0E04\\u0E25\\u0E34\\u0E01\",\n  drag: \"\\u0E25\\u0E32\\u0E01\",\n  editor: \"\",\n  editLineArrowPoints: \"\",\n  editText: \"\\u0E41\\u0E01\\u0E49\\u0E44\\u0E02\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21 / \\u0E40\\u0E1E\\u0E34\\u0E48\\u0E21\\u0E02\\u0E49\\u0E2D\\u0E04\\u0E27\\u0E32\\u0E21\",\n  github: \"\",\n  howto: \"\",\n  or: \"\",\n  preventBinding: \"\",\n  tools: \"\",\n  shortcuts: \"\",\n  textFinish: \"\",\n  textNewLine: \"\",\n  title: \"\\u0E0A\\u0E48\\u0E27\\u0E22\\u0E40\\u0E2B\\u0E25\\u0E37\\u0E2D\",\n  view: \"\\u0E14\\u0E39\",\n  zoomToFit: \"\",\n  zoomToSelection: \"\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\\u0E22\\u0E49\\u0E32\\u0E22\\u0E2B\\u0E19\\u0E49\\u0E32\\u0E44\\u0E1B\\u0E14\\u0E49\\u0E32\\u0E19 \\u0E0B\\u0E49\\u0E32\\u0E22/\\u0E02\\u0E27\\u0E32\"\n};\nvar clearCanvasDialog = {\n  title: \"\"\n};\nvar publishDialog = {\n  title: \"\",\n  itemName: \"\",\n  authorName: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E40\\u0E08\\u0E49\\u0E32\\u0E02\\u0E2D\\u0E07\",\n  githubUsername: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E43\\u0E0A\\u0E49 GitHub\",\n  twitterUsername: \"\\u0E0A\\u0E37\\u0E48\\u0E2D\\u0E1C\\u0E39\\u0E49\\u0E43\\u0E0A\\u0E49 Twitter\",\n  libraryName: \"\",\n  libraryDesc: \"\",\n  website: \"\",\n  placeholder: {\n    authorName: \"\",\n    libraryName: \"\",\n    libraryDesc: \"\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"\",\n    website: \"\"\n  },\n  noteDescription: \"\",\n  noteGuidelines: \"\",\n  noteLicense: \"\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"\",\n  content: \"\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\",\n  removeItemsFromLib: \"\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\\u0E42\\u0E2B\\u0E21\\u0E14\\u0E01\\u0E25\\u0E32\\u0E07\\u0E04\\u0E37\\u0E19\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\\u0E2A\\u0E48\\u0E07\\u0E2D\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E40\\u0E1B\\u0E47\\u0E19 SVG\",\n    exportToSvg: \"\\u0E2A\\u0E48\\u0E07\\u0E2D\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E40\\u0E1B\\u0E47\\u0E19 SVG\",\n    copyPngToClipboard: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E01 PNG \\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"\\u0E04\\u0E31\\u0E14\\u0E25\\u0E2D\\u0E01\\u0E44\\u0E1B\\u0E22\\u0E31\\u0E07\\u0E04\\u0E25\\u0E34\\u0E1B\\u0E1A\\u0E2D\\u0E23\\u0E4C\\u0E14\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\",\n  link: \"\"\n};\nvar stats = {\n  angle: \"\",\n  element: \"\",\n  elements: \"\",\n  height: \"\",\n  scene: \"\",\n  selected: \"\",\n  storage: \"\",\n  title: \"\",\n  total: \"\",\n  version: \"\",\n  versionCopy: \"\",\n  versionNotAvailable: \"\",\n  width: \"\"\n};\nvar toast = {\n  addedToLibrary: \"\",\n  copyStyles: \"\",\n  copyToClipboard: \"\",\n  copyToClipboardAsPng: \"\",\n  fileSaved: \"\",\n  fileSavedToFilename: \"\",\n  canvas: \"\",\n  selection: \"\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\\u0E1F\\u0E49\\u0E32\\u0E19\\u0E49\\u0E33\\u0E17\\u0E30\\u0E40\\u0E25\",\n  green: \"\\u0E40\\u0E02\\u0E35\\u0E22\\u0E27\",\n  yellow: \"\\u0E40\\u0E2B\\u0E25\\u0E37\\u0E2D\\u0E07\",\n  orange: \"\\u0E2A\\u0E49\\u0E21\",\n  bronze: \"\\u0E17\\u0E2D\\u0E07\\u0E41\\u0E14\\u0E07\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar th_TH_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=th-TH-55ACRHDJ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/th-TH-55ACRHDJ.js\n"));

/***/ })

}]);