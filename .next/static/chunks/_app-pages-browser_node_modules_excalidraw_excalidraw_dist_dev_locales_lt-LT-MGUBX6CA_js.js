"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_lt-LT-MGUBX6CA_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/lt-LT-MGUBX6CA.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/lt-LT-MGUBX6CA.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ lt_LT_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/lt-LT.json\nvar labels = {\n  paste: \"\\u012Eklijuoti\",\n  pasteAsPlaintext: \"\\u012Eklijuoti kaip paprast\\u0105 tekst\\u0105\",\n  pasteCharts: \"\\u012Eklijuoti diagramas\",\n  selectAll: \"Pa\\u017Eym\\u0117ti visk\\u0105\",\n  multiSelect: \"Prid\\u0117kite element\\u0105 prie pasirinkt\\u0173\",\n  moveCanvas: \"Judinti drob\\u0119\",\n  cut: \"I\\u0161kirpti\",\n  copy: \"Kopijuoti\",\n  copyAsPng: \"Kopijuoti \\u012F i\\u0161karpin\\u0119 kaip PNG\",\n  copyAsSvg: \"Kopijuoti \\u012F i\\u0161karpin\\u0119 kaip SVG\",\n  copyText: \"Kopijuoti \\u012F i\\u0161karpin\\u0119 kaip tekst\\u0105\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Kelti priekio link\",\n  sendToBack: \"Nustumti \\u012F u\\u017Enugar\\u012F\",\n  bringToFront: \"I\\u0161kelti \\u012F priek\\u012F\",\n  sendBackward: \"Nustumti link u\\u017Enugario\",\n  delete: \"I\\u0161trinti\",\n  copyStyles: \"Kopijuoti stilius\",\n  pasteStyles: \"\\u012Eklijuoti stilius\",\n  stroke: \"Linija\",\n  background: \"Fonas\",\n  fill: \"U\\u017Epildymas\",\n  strokeWidth: \"Linijos storis\",\n  strokeStyle: \"Linijos stilius\",\n  strokeStyle_solid: \"I\\u0161tisin\\u0117\",\n  strokeStyle_dashed: \"Br\\u016Bk\\u0161niuota\",\n  strokeStyle_dotted: \"Ta\\u0161kuota\",\n  sloppiness: \"Netvarkingumas\",\n  opacity: \"Nepermatomumas\",\n  textAlign: \"Teksto lygiavimas\",\n  edges: \"Kra\\u0161tai\",\n  sharp: \"A\\u0161trus\",\n  round: \"U\\u017Eapvalintas\",\n  arrowheads: \"Rodykl\\u0117s vir\\u0161\\u016Bn\\u0117s\",\n  arrowhead_none: \"Jokios\",\n  arrowhead_arrow: \"Rodykl\\u0117\",\n  arrowhead_bar: \"Bruk\\u0161nys\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Trikampis\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"\\u0160rifto dydis\",\n  fontFamily: \"\\u0160riftas\",\n  addWatermark: \"Sukurta su Excalidraw\",\n  handDrawn: \"Ranka ra\\u0161ytas\",\n  normal: \"Normalus\",\n  code: \"Kodas\",\n  small: \"Ma\\u017Eas\",\n  medium: \"Vidutinis\",\n  large: \"Didelis\",\n  veryLarge: \"Labai didelis\",\n  solid: \"\",\n  hachure: \"\",\n  zigzag: \"\",\n  crossHatch: \"\",\n  thin: \"Plonas\",\n  bold: \"Pastorintas\",\n  left: \"Kair\\u0117je\",\n  center: \"Centre\",\n  right: \"De\\u0161in\\u0117je\",\n  extraBold: \"Labiau pastorintas\",\n  architect: \"Architektas\",\n  artist: \"Menininkas\",\n  cartoonist: \"Karikat\\u016Bristas\",\n  fileTitle: \"Failo pavadinimas\",\n  colorPicker: \"Spalvos parinkiklis\",\n  canvasColors: \"\",\n  canvasBackground: \"Drob\\u0117s fonas\",\n  drawingCanvas: \"\",\n  layers: \"Sluoksniai\",\n  actions: \"Veiksmai\",\n  language: \"Kalba\",\n  liveCollaboration: \"Bendradarbiavimas gyvai...\",\n  duplicateSelection: \"\",\n  untitled: \"\",\n  name: \"\",\n  yourName: \"J\\u016Bs\\u0173 vardas\",\n  madeWithExcalidraw: \"Sukurta su Excalidraw\",\n  group: \"Grupuoti pasirinkim\\u0105\",\n  ungroup: \"I\\u0161grupuoti pasirinkim\\u0105\",\n  collaborators: \"Bendradarbiautojai\",\n  showGrid: \"Rodyti tinklel\\u012F\",\n  addToLibrary: \"Prid\\u0117ti \\u012F bibliotek\\u0105\",\n  removeFromLibrary: \"Pa\\u0161alinti i\\u0161 bibliotekos\",\n  libraryLoadingMessage: \"\",\n  libraries: \"Nar\\u0161yti bibliotekas\",\n  loadingScene: \"\",\n  align: \"Lygiuoti\",\n  alignTop: \"Lygiuoti vir\\u0161uje\",\n  alignBottom: \"Lygiuoti apa\\u010Dioje\",\n  alignLeft: \"Lygiuoti kair\\u0117je\",\n  alignRight: \"Lygiuoti de\\u0161in\\u0117je\",\n  centerVertically: \"Centruoti vertikaliai\",\n  centerHorizontally: \"Centruoti horizontaliai\",\n  distributeHorizontally: \"\",\n  distributeVertically: \"\",\n  flipHorizontal: \"Apversti horizontaliai\",\n  flipVertical: \"Apversti vertikaliai\",\n  viewMode: \"\",\n  share: \"Dalintis\",\n  showStroke: \"\",\n  showBackground: \"\",\n  toggleTheme: \"\",\n  personalLib: \"Asmenin\\u0117 biblioteka\",\n  excalidrawLib: \"Exaclidraw biblioteka\",\n  decreaseFontSize: \"\",\n  increaseFontSize: \"\",\n  unbindText: \"\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"Redeguoti nuorod\\u0105\",\n    editEmbed: \"\",\n    create: \"Sukurti nuorod\\u0105\",\n    createEmbed: \"\",\n    label: \"Nuoroda\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"U\\u017Erakinti\",\n    unlock: \"Atrakinti\",\n    lockAll: \"\",\n    unlockAll: \"\"\n  },\n  statusPublished: \"\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"\",\n  exportJSON: \"Eksportuoti \\u012F fail\\u0105\",\n  exportImage: \"\",\n  export: \"\",\n  copyToClipboard: \"Kopijuoti \\u012F i\\u0161karpin\\u0119\",\n  save: \"\",\n  saveAs: \"I\\u0161saugoti kaip\",\n  load: \"\",\n  getShareableLink: \"Gauti nuorod\\u0105 dalinimuisi\",\n  close: \"U\\u017Edaryti\",\n  selectLanguage: \"Pasirinkite kalb\\u0105\",\n  scrollBackToContent: \"\",\n  zoomIn: \"Priartinti\",\n  zoomOut: \"Nutolinti\",\n  resetZoom: \"\",\n  menu: \"Meniu\",\n  done: \"\",\n  edit: \"Redaguoti\",\n  undo: \"Anuliuoti\",\n  redo: \"\",\n  resetLibrary: \"Atstatyti bibliotek\\u0105\",\n  createNewRoom: \"Sukurti nauj\\u0105 kambar\\u012F\",\n  fullScreen: \"Visas ekranas\",\n  darkMode: \"Tamsus re\\u017Eimas\",\n  lightMode: \"\\u0160viesus re\\u017Eimas\",\n  zenMode: \"\\u201EZen\\u201C re\\u017Eimas\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"I\\u0161eiti i\\u0161 \\u201EZen\\u201C re\\u017Eimo\",\n  cancel: \"At\\u0161aukti\",\n  clear: \"I\\u0161valyti\",\n  remove: \"Pa\\u0161alinti\",\n  embed: \"\",\n  publishLibrary: \"Paskelbti\",\n  submit: \"Pateikti\",\n  confirm: \"Patvirtinti\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"\",\n  couldNotCreateShareableLink: \"\",\n  couldNotCreateShareableLinkTooBig: \"\",\n  couldNotLoadInvalidFile: \"\",\n  importBackendFailed: \"\",\n  cannotExportEmptyCanvas: \"\",\n  couldNotCopyToClipboard: \"\",\n  decryptFailed: \"\",\n  uploadedSecurly: \"\",\n  loadSceneOverridePrompt: \"\",\n  collabStopOverridePrompt: \"Sesijos nutraukimas perra\\u0161ys ankstesn\\u012F, lokaliai i\\u0161saugot\\u0105 pie\\u0161in\\u012F. Ar tikrai to nori?\\n\\n(Jei nori i\\u0161laikyti lokal\\u0173 pie\\u0161in\\u012F, tiesiog u\\u017Edaryk nar\\u0161ykl\\u0117s skirtuk\\u0105.)\",\n  errorAddingToLibrary: \"Nepavyko \\u012Ftraukti elemento \\u012F bibliotek\\u0105\",\n  errorRemovingFromLibrary: \"Nepavyko pa\\u0161alinti elemento i\\u0161 bibliotekos\",\n  confirmAddLibrary: \"Tai \\u012Ftrauks {{numShapes}} fig\\u016Br\\u0105/-as \\u012F tavo bibliotek\\u0105. Ar tikrai to nori?\",\n  imageDoesNotContainScene: \"Pana\\u0161u, jog \\u0161is paveiksliukas neturi scenos duomen\\u0173. Ar yra \\u012Fjuntas scenos \\u012Ftraukimas ekportavimo metu?\",\n  cannotRestoreFromImage: \"Nepavyko atstatyti scenos i\\u0161 \\u0161io nuotraukos failo\",\n  invalidSceneUrl: \"Nepavyko suimportuoti scenos i\\u0161 pateiktos nuorodos (URL). Ji arba blogai suformatuota, arba savyje neturi teising\\u0173 Excalidraw JSON duomen\\u0173.\",\n  resetLibrary: \"Tai i\\u0161valys tavo bibliotek\\u0105. Ar tikrai to nori?\",\n  removeItemsFromsLibrary: \"I\\u0161trinti {{count}} element\\u0105/-us i\\u0161 bibliotekos?\",\n  invalidEncryptionKey: \"\\u0160ifravimo raktas turi b\\u016Bti i\\u0161 22 simboli\\u0173. Redagavimas gyvai yra i\\u0161jungtas.\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"Nepalaikomas failo tipas.\",\n  imageInsertError: \"Nepyko \\u012Fkelti paveiksliuko. Pabandyk v\\u0117liau...\",\n  fileTooBig: \"Per didelis failas. Did\\u017Eiausias leid\\u017Eiamas dydis yra {{maxSize}}.\",\n  svgImageInsertError: \"Nepavyko \\u012Ftraukti SVG paveiksliuko. Pana\\u0161u, jog SVG yra nevalidus.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Nevalidus SVG.\",\n  cannotResolveCollabServer: \"Nepavyko prisijungti prie serverio bendradarbiavimui. Perkrauk puslap\\u012F ir pabandyk prisijungti dar kart\\u0105.\",\n  importLibraryError: \"Nepavyko \\u012Fkelti bibliotekos\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"\\u017Dym\\u0117jimas\",\n  image: \"\\u012Ekelti paveiksliuk\\u0105\",\n  rectangle: \"Sta\\u010Diakampis\",\n  diamond: \"Deimantas\",\n  ellipse: \"Elips\\u0117\",\n  arrow: \"Rodykl\\u0117\",\n  line: \"Linija\",\n  freedraw: \"Pie\\u0161ti\",\n  text: \"Tekstas\",\n  library: \"Biblioteka\",\n  lock: \"Baigus pie\\u0161ti, i\\u0161laikyti pasirinkt\\u0105 \\u012Frank\\u012F\",\n  penMode: \"Ra\\u0161yklio re\\u017Eimas - neleisti prisilietim\\u0173\",\n  link: \"Prid\\u0117ti / Atnaujinti pasirinktos fig\\u016Bros nuorod\\u0105\",\n  eraser: \"Trintukas\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Veiksmai su drobe\",\n  selectedShapeActions: \"Veiksmai su pasirinkta fig\\u016Bra\",\n  shapes: \"Fig\\u016Bros\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"Paspaudimai sukurs papildomus ta\\u0161kus, nepertraukiamas tempimas sukurs linij\\u0105\",\n  freeDraw: \"Spausk ir tempk, paleisk kai nor\\u0117si pabaigti\",\n  text: \"U\\u017Euomina: tekst\\u0105 taip pat galima prid\\u0117ti bet kur su dvigubu pel\\u0117s paspaudimu, kol parinkas \\u017Eym\\u0117jimo \\u012Frankis\",\n  embeddable: \"\",\n  text_selected: \"\",\n  text_editing: \"\",\n  linearElementMulti: \"\",\n  lockAngle: \"\",\n  resize: \"\",\n  resizeImage: \"\",\n  rotate: \"\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\",\n  canvasTooBig: \"\",\n  canvasTooBigTip: \"\"\n};\nvar errorSplash = {\n  headingMain: \"\",\n  clearCanvasMessage: \"\",\n  clearCanvasCaveat: \"\",\n  trackedToSentry: \"\",\n  openIssueMessage: \"\",\n  sceneContent: \"\"\n};\nvar roomDialog = {\n  desc_intro: \"\",\n  desc_privacy: \"\",\n  button_startSession: \"Prad\\u0117ti seans\\u0105\",\n  button_stopSession: \"Sustabdyti seans\\u0105\",\n  desc_inProgressIntro: \"\",\n  desc_shareLink: \"\",\n  desc_exitSession: \"\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"Klaida\"\n};\nvar exportDialog = {\n  disk_title: \"\\u012Era\\u0161yti \\u012F disk\\u0105\",\n  disk_details: \"\",\n  disk_button: \"\\u012Era\\u0161yti \\u012F fail\\u0105\",\n  link_title: \"Nuoroda dalinimuisi\",\n  link_details: \"\",\n  link_button: \"\",\n  excalidrawplus_description: \"\",\n  excalidrawplus_button: \"Eksportuoti\",\n  excalidrawplus_exportError: \"\"\n};\nvar helpDialog = {\n  blog: \"\",\n  click: \"paspaudimas\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"Banguota rodykl\\u0117\",\n  curvedLine: \"Banguota linija\",\n  documentation: \"Dokumentacija\",\n  doubleClick: \"dvigubas paspaudimas\",\n  drag: \"vilkti\",\n  editor: \"Redaktorius\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"Radai klaid\\u0105? Pateik\",\n  howto: \"Vadovaukis m\\u016Bs\\u0173 gidu\",\n  or: \"arba\",\n  preventBinding: \"\",\n  tools: \"\\u012Erankiai\",\n  shortcuts: \"Spartieji klavi\\u0161ai\",\n  textFinish: \"Baigti redagavim\\u0105 (teksto redaktoriuje)\",\n  textNewLine: \"Prid\\u0117ti nauj\\u0105 eilut\\u0119 (tekto redaktoriuje)\",\n  title: \"Pagalba\",\n  view: \"\",\n  zoomToFit: \"\",\n  zoomToSelection: \"Priartinti iki pa\\u017Eym\\u0117tos vietos\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"Pajudinti puslap\\u012F auk\\u0161tyn/\\u017Eemyn\",\n  movePageLeftRight: \"Pajudinti puslap\\u012F kair\\u0117n/de\\u0161in\\u0117n\"\n};\nvar clearCanvasDialog = {\n  title: \"I\\u0161valyti drob\\u0119\"\n};\nvar publishDialog = {\n  title: \"Pavie\\u0161inti bibliotek\\u0105\",\n  itemName: \"Elemento pavadinimas\",\n  authorName: \"Autoriaus vardas\",\n  githubUsername: \"Github spalyvardis\",\n  twitterUsername: \"Twitter slapyvardis\",\n  libraryName: \"Bibliotekos pavadinimas\",\n  libraryDesc: \"Bibliotekos apra\\u0161as\",\n  website: \"Tinklalapis\",\n  placeholder: {\n    authorName: \"Tavo vardas arba spalyvardis\",\n    libraryName: \"Tavo bibliotekos pavadinimas\",\n    libraryDesc: \"Tavo bibliotekos apra\\u0161as, pad\\u0117ti \\u017Emon\\u0117ms geriau suprasti jos paskirt\\u012F\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"Privalomas\",\n    website: \"\\u012Eveskite teising\\u0105 nuorod\\u0105 (URL)\"\n  },\n  noteDescription: \"Pateik savo bibliotek\\u0105, jog ji gal\\u0117t\\u0173 b\\u016Bti \\u012Ftraukta \\u012F <link></link>jog kiti \\u017Emon\\u0117s gal\\u0117t\\u0173 tai naudoti savo pie\\u0161iniuose.\",\n  noteGuidelines: \"Vis\\u0173 pirma, biblioteka turi b\\u016Bti rankiniu b\\u016Bdu patvirtinta. Pra\\u0161ome paskaityti <link>gair\\u0117s</link>\",\n  noteLicense: \"<link>MIT licencija, </link>\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteka pateikta\",\n  content: \"A\\u010Di\\u016B {{authorName}}. Tavo biblioteka buvo pateikta per\\u017Ei\\u016Brai. Gali sekti b\\u016Bsen\\u0105<link>\\u010Dia</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Atstatyti bibliotek\\u0105\",\n  removeItemsFromLib: \"Pa\\u0161alinti pasirinktus elementus i\\u0161 bibliotekos\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\",\n  link: \"\"\n};\nvar stats = {\n  angle: \"\",\n  element: \"Elementas\",\n  elements: \"Elementai\",\n  height: \"Auk\\u0161tis\",\n  scene: \"Scena\",\n  selected: \"Pasirinkta\",\n  storage: \"Saugykla\",\n  title: \"Informacija moksliukams\",\n  total: \"\",\n  version: \"\",\n  versionCopy: \"\",\n  versionNotAvailable: \"\",\n  width: \"Plotis\"\n};\nvar toast = {\n  addedToLibrary: \"Prid\\u0117ta \\u012F bibliotek\\u0105\",\n  copyStyles: \"\",\n  copyToClipboard: \"Nukopijuota \\u012F i\\u0161karpin\\u0119.\",\n  copyToClipboardAsPng: \"\",\n  fileSaved: \"Failas i\\u0161saugotas.\",\n  fileSavedToFilename: \"I\\u0161saugota \\u012F {filename}\",\n  canvas: \"drob\\u0117\",\n  selection: \"\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Permatoma\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar lt_LT_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=lt-LT-MGUBX6CA.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvbHQtTFQtTUdVQlg2Q0EuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLFlBQVk7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsUUFBUTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRkFBZ0YsU0FBUztBQUN6RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLFlBQVk7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLFNBQVM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUE4QkU7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvbHQtTFQtTUdVQlg2Q0EuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiLi4vY2h1bmstWERGQ1VVVDYuanNcIjtcblxuLy8gbG9jYWxlcy9sdC1MVC5qc29uXG52YXIgbGFiZWxzID0ge1xuICBwYXN0ZTogXCJcXHUwMTJFa2xpanVvdGlcIixcbiAgcGFzdGVBc1BsYWludGV4dDogXCJcXHUwMTJFa2xpanVvdGkga2FpcCBwYXByYXN0XFx1MDEwNSB0ZWtzdFxcdTAxMDVcIixcbiAgcGFzdGVDaGFydHM6IFwiXFx1MDEyRWtsaWp1b3RpIGRpYWdyYW1hc1wiLFxuICBzZWxlY3RBbGw6IFwiUGFcXHUwMTdFeW1cXHUwMTE3dGkgdmlza1xcdTAxMDVcIixcbiAgbXVsdGlTZWxlY3Q6IFwiUHJpZFxcdTAxMTdraXRlIGVsZW1lbnRcXHUwMTA1IHByaWUgcGFzaXJpbmt0XFx1MDE3M1wiLFxuICBtb3ZlQ2FudmFzOiBcIkp1ZGludGkgZHJvYlxcdTAxMTlcIixcbiAgY3V0OiBcIklcXHUwMTYxa2lycHRpXCIsXG4gIGNvcHk6IFwiS29waWp1b3RpXCIsXG4gIGNvcHlBc1BuZzogXCJLb3BpanVvdGkgXFx1MDEyRiBpXFx1MDE2MWthcnBpblxcdTAxMTkga2FpcCBQTkdcIixcbiAgY29weUFzU3ZnOiBcIktvcGlqdW90aSBcXHUwMTJGIGlcXHUwMTYxa2FycGluXFx1MDExOSBrYWlwIFNWR1wiLFxuICBjb3B5VGV4dDogXCJLb3BpanVvdGkgXFx1MDEyRiBpXFx1MDE2MWthcnBpblxcdTAxMTkga2FpcCB0ZWtzdFxcdTAxMDVcIixcbiAgY29weVNvdXJjZTogXCJcIixcbiAgY29udmVydFRvQ29kZTogXCJcIixcbiAgYnJpbmdGb3J3YXJkOiBcIktlbHRpIHByaWVraW8gbGlua1wiLFxuICBzZW5kVG9CYWNrOiBcIk51c3R1bXRpIFxcdTAxMkYgdVxcdTAxN0VudWdhclxcdTAxMkZcIixcbiAgYnJpbmdUb0Zyb250OiBcIklcXHUwMTYxa2VsdGkgXFx1MDEyRiBwcmlla1xcdTAxMkZcIixcbiAgc2VuZEJhY2t3YXJkOiBcIk51c3R1bXRpIGxpbmsgdVxcdTAxN0VudWdhcmlvXCIsXG4gIGRlbGV0ZTogXCJJXFx1MDE2MXRyaW50aVwiLFxuICBjb3B5U3R5bGVzOiBcIktvcGlqdW90aSBzdGlsaXVzXCIsXG4gIHBhc3RlU3R5bGVzOiBcIlxcdTAxMkVrbGlqdW90aSBzdGlsaXVzXCIsXG4gIHN0cm9rZTogXCJMaW5pamFcIixcbiAgYmFja2dyb3VuZDogXCJGb25hc1wiLFxuICBmaWxsOiBcIlVcXHUwMTdFcGlsZHltYXNcIixcbiAgc3Ryb2tlV2lkdGg6IFwiTGluaWpvcyBzdG9yaXNcIixcbiAgc3Ryb2tlU3R5bGU6IFwiTGluaWpvcyBzdGlsaXVzXCIsXG4gIHN0cm9rZVN0eWxlX3NvbGlkOiBcIklcXHUwMTYxdGlzaW5cXHUwMTE3XCIsXG4gIHN0cm9rZVN0eWxlX2Rhc2hlZDogXCJCclxcdTAxNkJrXFx1MDE2MW5pdW90YVwiLFxuICBzdHJva2VTdHlsZV9kb3R0ZWQ6IFwiVGFcXHUwMTYxa3VvdGFcIixcbiAgc2xvcHBpbmVzczogXCJOZXR2YXJraW5ndW1hc1wiLFxuICBvcGFjaXR5OiBcIk5lcGVybWF0b211bWFzXCIsXG4gIHRleHRBbGlnbjogXCJUZWtzdG8gbHlnaWF2aW1hc1wiLFxuICBlZGdlczogXCJLcmFcXHUwMTYxdGFpXCIsXG4gIHNoYXJwOiBcIkFcXHUwMTYxdHJ1c1wiLFxuICByb3VuZDogXCJVXFx1MDE3RWFwdmFsaW50YXNcIixcbiAgYXJyb3doZWFkczogXCJSb2R5a2xcXHUwMTE3cyB2aXJcXHUwMTYxXFx1MDE2Qm5cXHUwMTE3c1wiLFxuICBhcnJvd2hlYWRfbm9uZTogXCJKb2tpb3NcIixcbiAgYXJyb3doZWFkX2Fycm93OiBcIlJvZHlrbFxcdTAxMTdcIixcbiAgYXJyb3doZWFkX2JhcjogXCJCcnVrXFx1MDE2MW55c1wiLFxuICBhcnJvd2hlYWRfY2lyY2xlOiBcIlwiLFxuICBhcnJvd2hlYWRfY2lyY2xlX291dGxpbmU6IFwiXCIsXG4gIGFycm93aGVhZF90cmlhbmdsZTogXCJUcmlrYW1waXNcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlX291dGxpbmU6IFwiXCIsXG4gIGFycm93aGVhZF9kaWFtb25kOiBcIlwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZF9vdXRsaW5lOiBcIlwiLFxuICBmb250U2l6ZTogXCJcXHUwMTYwcmlmdG8gZHlkaXNcIixcbiAgZm9udEZhbWlseTogXCJcXHUwMTYwcmlmdGFzXCIsXG4gIGFkZFdhdGVybWFyazogXCJTdWt1cnRhIHN1IEV4Y2FsaWRyYXdcIixcbiAgaGFuZERyYXduOiBcIlJhbmthIHJhXFx1MDE2MXl0YXNcIixcbiAgbm9ybWFsOiBcIk5vcm1hbHVzXCIsXG4gIGNvZGU6IFwiS29kYXNcIixcbiAgc21hbGw6IFwiTWFcXHUwMTdFYXNcIixcbiAgbWVkaXVtOiBcIlZpZHV0aW5pc1wiLFxuICBsYXJnZTogXCJEaWRlbGlzXCIsXG4gIHZlcnlMYXJnZTogXCJMYWJhaSBkaWRlbGlzXCIsXG4gIHNvbGlkOiBcIlwiLFxuICBoYWNodXJlOiBcIlwiLFxuICB6aWd6YWc6IFwiXCIsXG4gIGNyb3NzSGF0Y2g6IFwiXCIsXG4gIHRoaW46IFwiUGxvbmFzXCIsXG4gIGJvbGQ6IFwiUGFzdG9yaW50YXNcIixcbiAgbGVmdDogXCJLYWlyXFx1MDExN2plXCIsXG4gIGNlbnRlcjogXCJDZW50cmVcIixcbiAgcmlnaHQ6IFwiRGVcXHUwMTYxaW5cXHUwMTE3amVcIixcbiAgZXh0cmFCb2xkOiBcIkxhYmlhdSBwYXN0b3JpbnRhc1wiLFxuICBhcmNoaXRlY3Q6IFwiQXJjaGl0ZWt0YXNcIixcbiAgYXJ0aXN0OiBcIk1lbmluaW5rYXNcIixcbiAgY2FydG9vbmlzdDogXCJLYXJpa2F0XFx1MDE2QnJpc3Rhc1wiLFxuICBmaWxlVGl0bGU6IFwiRmFpbG8gcGF2YWRpbmltYXNcIixcbiAgY29sb3JQaWNrZXI6IFwiU3BhbHZvcyBwYXJpbmtpa2xpc1wiLFxuICBjYW52YXNDb2xvcnM6IFwiXCIsXG4gIGNhbnZhc0JhY2tncm91bmQ6IFwiRHJvYlxcdTAxMTdzIGZvbmFzXCIsXG4gIGRyYXdpbmdDYW52YXM6IFwiXCIsXG4gIGxheWVyczogXCJTbHVva3NuaWFpXCIsXG4gIGFjdGlvbnM6IFwiVmVpa3NtYWlcIixcbiAgbGFuZ3VhZ2U6IFwiS2FsYmFcIixcbiAgbGl2ZUNvbGxhYm9yYXRpb246IFwiQmVuZHJhZGFyYmlhdmltYXMgZ3l2YWkuLi5cIixcbiAgZHVwbGljYXRlU2VsZWN0aW9uOiBcIlwiLFxuICB1bnRpdGxlZDogXCJcIixcbiAgbmFtZTogXCJcIixcbiAgeW91ck5hbWU6IFwiSlxcdTAxNkJzXFx1MDE3MyB2YXJkYXNcIixcbiAgbWFkZVdpdGhFeGNhbGlkcmF3OiBcIlN1a3VydGEgc3UgRXhjYWxpZHJhd1wiLFxuICBncm91cDogXCJHcnVwdW90aSBwYXNpcmlua2ltXFx1MDEwNVwiLFxuICB1bmdyb3VwOiBcIklcXHUwMTYxZ3J1cHVvdGkgcGFzaXJpbmtpbVxcdTAxMDVcIixcbiAgY29sbGFib3JhdG9yczogXCJCZW5kcmFkYXJiaWF1dG9qYWlcIixcbiAgc2hvd0dyaWQ6IFwiUm9keXRpIHRpbmtsZWxcXHUwMTJGXCIsXG4gIGFkZFRvTGlicmFyeTogXCJQcmlkXFx1MDExN3RpIFxcdTAxMkYgYmlibGlvdGVrXFx1MDEwNVwiLFxuICByZW1vdmVGcm9tTGlicmFyeTogXCJQYVxcdTAxNjFhbGludGkgaVxcdTAxNjEgYmlibGlvdGVrb3NcIixcbiAgbGlicmFyeUxvYWRpbmdNZXNzYWdlOiBcIlwiLFxuICBsaWJyYXJpZXM6IFwiTmFyXFx1MDE2MXl0aSBiaWJsaW90ZWthc1wiLFxuICBsb2FkaW5nU2NlbmU6IFwiXCIsXG4gIGFsaWduOiBcIkx5Z2l1b3RpXCIsXG4gIGFsaWduVG9wOiBcIkx5Z2l1b3RpIHZpclxcdTAxNjF1amVcIixcbiAgYWxpZ25Cb3R0b206IFwiTHlnaXVvdGkgYXBhXFx1MDEwRGlvamVcIixcbiAgYWxpZ25MZWZ0OiBcIkx5Z2l1b3RpIGthaXJcXHUwMTE3amVcIixcbiAgYWxpZ25SaWdodDogXCJMeWdpdW90aSBkZVxcdTAxNjFpblxcdTAxMTdqZVwiLFxuICBjZW50ZXJWZXJ0aWNhbGx5OiBcIkNlbnRydW90aSB2ZXJ0aWthbGlhaVwiLFxuICBjZW50ZXJIb3Jpem9udGFsbHk6IFwiQ2VudHJ1b3RpIGhvcml6b250YWxpYWlcIixcbiAgZGlzdHJpYnV0ZUhvcml6b250YWxseTogXCJcIixcbiAgZGlzdHJpYnV0ZVZlcnRpY2FsbHk6IFwiXCIsXG4gIGZsaXBIb3Jpem9udGFsOiBcIkFwdmVyc3RpIGhvcml6b250YWxpYWlcIixcbiAgZmxpcFZlcnRpY2FsOiBcIkFwdmVyc3RpIHZlcnRpa2FsaWFpXCIsXG4gIHZpZXdNb2RlOiBcIlwiLFxuICBzaGFyZTogXCJEYWxpbnRpc1wiLFxuICBzaG93U3Ryb2tlOiBcIlwiLFxuICBzaG93QmFja2dyb3VuZDogXCJcIixcbiAgdG9nZ2xlVGhlbWU6IFwiXCIsXG4gIHBlcnNvbmFsTGliOiBcIkFzbWVuaW5cXHUwMTE3IGJpYmxpb3Rla2FcIixcbiAgZXhjYWxpZHJhd0xpYjogXCJFeGFjbGlkcmF3IGJpYmxpb3Rla2FcIixcbiAgZGVjcmVhc2VGb250U2l6ZTogXCJcIixcbiAgaW5jcmVhc2VGb250U2l6ZTogXCJcIixcbiAgdW5iaW5kVGV4dDogXCJcIixcbiAgYmluZFRleHQ6IFwiXCIsXG4gIGNyZWF0ZUNvbnRhaW5lckZyb21UZXh0OiBcIlwiLFxuICBsaW5rOiB7XG4gICAgZWRpdDogXCJSZWRlZ3VvdGkgbnVvcm9kXFx1MDEwNVwiLFxuICAgIGVkaXRFbWJlZDogXCJcIixcbiAgICBjcmVhdGU6IFwiU3VrdXJ0aSBudW9yb2RcXHUwMTA1XCIsXG4gICAgY3JlYXRlRW1iZWQ6IFwiXCIsXG4gICAgbGFiZWw6IFwiTnVvcm9kYVwiLFxuICAgIGxhYmVsRW1iZWQ6IFwiXCIsXG4gICAgZW1wdHk6IFwiXCJcbiAgfSxcbiAgbGluZUVkaXRvcjoge1xuICAgIGVkaXQ6IFwiXCIsXG4gICAgZXhpdDogXCJcIlxuICB9LFxuICBlbGVtZW50TG9jazoge1xuICAgIGxvY2s6IFwiVVxcdTAxN0VyYWtpbnRpXCIsXG4gICAgdW5sb2NrOiBcIkF0cmFraW50aVwiLFxuICAgIGxvY2tBbGw6IFwiXCIsXG4gICAgdW5sb2NrQWxsOiBcIlwiXG4gIH0sXG4gIHN0YXR1c1B1Ymxpc2hlZDogXCJcIixcbiAgc2lkZWJhckxvY2s6IFwiXCIsXG4gIHNlbGVjdEFsbEVsZW1lbnRzSW5GcmFtZTogXCJcIixcbiAgcmVtb3ZlQWxsRWxlbWVudHNGcm9tRnJhbWU6IFwiXCIsXG4gIGV5ZURyb3BwZXI6IFwiXCIsXG4gIHRleHRUb0RpYWdyYW06IFwiXCIsXG4gIHByb21wdDogXCJcIlxufTtcbnZhciBsaWJyYXJ5ID0ge1xuICBub0l0ZW1zOiBcIlwiLFxuICBoaW50X2VtcHR5TGlicmFyeTogXCJcIixcbiAgaGludF9lbXB0eVByaXZhdGVMaWJyYXJ5OiBcIlwiXG59O1xudmFyIGJ1dHRvbnMgPSB7XG4gIGNsZWFyUmVzZXQ6IFwiXCIsXG4gIGV4cG9ydEpTT046IFwiRWtzcG9ydHVvdGkgXFx1MDEyRiBmYWlsXFx1MDEwNVwiLFxuICBleHBvcnRJbWFnZTogXCJcIixcbiAgZXhwb3J0OiBcIlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiS29waWp1b3RpIFxcdTAxMkYgaVxcdTAxNjFrYXJwaW5cXHUwMTE5XCIsXG4gIHNhdmU6IFwiXCIsXG4gIHNhdmVBczogXCJJXFx1MDE2MXNhdWdvdGkga2FpcFwiLFxuICBsb2FkOiBcIlwiLFxuICBnZXRTaGFyZWFibGVMaW5rOiBcIkdhdXRpIG51b3JvZFxcdTAxMDUgZGFsaW5pbXVpc2lcIixcbiAgY2xvc2U6IFwiVVxcdTAxN0VkYXJ5dGlcIixcbiAgc2VsZWN0TGFuZ3VhZ2U6IFwiUGFzaXJpbmtpdGUga2FsYlxcdTAxMDVcIixcbiAgc2Nyb2xsQmFja1RvQ29udGVudDogXCJcIixcbiAgem9vbUluOiBcIlByaWFydGludGlcIixcbiAgem9vbU91dDogXCJOdXRvbGludGlcIixcbiAgcmVzZXRab29tOiBcIlwiLFxuICBtZW51OiBcIk1lbml1XCIsXG4gIGRvbmU6IFwiXCIsXG4gIGVkaXQ6IFwiUmVkYWd1b3RpXCIsXG4gIHVuZG86IFwiQW51bGl1b3RpXCIsXG4gIHJlZG86IFwiXCIsXG4gIHJlc2V0TGlicmFyeTogXCJBdHN0YXR5dGkgYmlibGlvdGVrXFx1MDEwNVwiLFxuICBjcmVhdGVOZXdSb29tOiBcIlN1a3VydGkgbmF1alxcdTAxMDUga2FtYmFyXFx1MDEyRlwiLFxuICBmdWxsU2NyZWVuOiBcIlZpc2FzIGVrcmFuYXNcIixcbiAgZGFya01vZGU6IFwiVGFtc3VzIHJlXFx1MDE3RWltYXNcIixcbiAgbGlnaHRNb2RlOiBcIlxcdTAxNjB2aWVzdXMgcmVcXHUwMTdFaW1hc1wiLFxuICB6ZW5Nb2RlOiBcIlxcdTIwMUVaZW5cXHUyMDFDIHJlXFx1MDE3RWltYXNcIixcbiAgb2JqZWN0c1NuYXBNb2RlOiBcIlwiLFxuICBleGl0WmVuTW9kZTogXCJJXFx1MDE2MWVpdGkgaVxcdTAxNjEgXFx1MjAxRVplblxcdTIwMUMgcmVcXHUwMTdFaW1vXCIsXG4gIGNhbmNlbDogXCJBdFxcdTAxNjFhdWt0aVwiLFxuICBjbGVhcjogXCJJXFx1MDE2MXZhbHl0aVwiLFxuICByZW1vdmU6IFwiUGFcXHUwMTYxYWxpbnRpXCIsXG4gIGVtYmVkOiBcIlwiLFxuICBwdWJsaXNoTGlicmFyeTogXCJQYXNrZWxidGlcIixcbiAgc3VibWl0OiBcIlBhdGVpa3RpXCIsXG4gIGNvbmZpcm06IFwiUGF0dmlydGludGlcIixcbiAgZW1iZWRkYWJsZUludGVyYWN0aW9uQnV0dG9uOiBcIlwiXG59O1xudmFyIGFsZXJ0cyA9IHtcbiAgY2xlYXJSZXNldDogXCJcIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rOiBcIlwiLFxuICBjb3VsZE5vdENyZWF0ZVNoYXJlYWJsZUxpbmtUb29CaWc6IFwiXCIsXG4gIGNvdWxkTm90TG9hZEludmFsaWRGaWxlOiBcIlwiLFxuICBpbXBvcnRCYWNrZW5kRmFpbGVkOiBcIlwiLFxuICBjYW5ub3RFeHBvcnRFbXB0eUNhbnZhczogXCJcIixcbiAgY291bGROb3RDb3B5VG9DbGlwYm9hcmQ6IFwiXCIsXG4gIGRlY3J5cHRGYWlsZWQ6IFwiXCIsXG4gIHVwbG9hZGVkU2VjdXJseTogXCJcIixcbiAgbG9hZFNjZW5lT3ZlcnJpZGVQcm9tcHQ6IFwiXCIsXG4gIGNvbGxhYlN0b3BPdmVycmlkZVByb21wdDogXCJTZXNpam9zIG51dHJhdWtpbWFzIHBlcnJhXFx1MDE2MXlzIGFua3N0ZXNuXFx1MDEyRiwgbG9rYWxpYWkgaVxcdTAxNjFzYXVnb3RcXHUwMTA1IHBpZVxcdTAxNjFpblxcdTAxMkYuIEFyIHRpa3JhaSB0byBub3JpP1xcblxcbihKZWkgbm9yaSBpXFx1MDE2MWxhaWt5dGkgbG9rYWxcXHUwMTczIHBpZVxcdTAxNjFpblxcdTAxMkYsIHRpZXNpb2cgdVxcdTAxN0VkYXJ5ayBuYXJcXHUwMTYxeWtsXFx1MDExN3Mgc2tpcnR1a1xcdTAxMDUuKVwiLFxuICBlcnJvckFkZGluZ1RvTGlicmFyeTogXCJOZXBhdnlrbyBcXHUwMTJGdHJhdWt0aSBlbGVtZW50byBcXHUwMTJGIGJpYmxpb3Rla1xcdTAxMDVcIixcbiAgZXJyb3JSZW1vdmluZ0Zyb21MaWJyYXJ5OiBcIk5lcGF2eWtvIHBhXFx1MDE2MWFsaW50aSBlbGVtZW50byBpXFx1MDE2MSBiaWJsaW90ZWtvc1wiLFxuICBjb25maXJtQWRkTGlicmFyeTogXCJUYWkgXFx1MDEyRnRyYXVrcyB7e251bVNoYXBlc319IGZpZ1xcdTAxNkJyXFx1MDEwNS8tYXMgXFx1MDEyRiB0YXZvIGJpYmxpb3Rla1xcdTAxMDUuIEFyIHRpa3JhaSB0byBub3JpP1wiLFxuICBpbWFnZURvZXNOb3RDb250YWluU2NlbmU6IFwiUGFuYVxcdTAxNjF1LCBqb2cgXFx1MDE2MWlzIHBhdmVpa3NsaXVrYXMgbmV0dXJpIHNjZW5vcyBkdW9tZW5cXHUwMTczLiBBciB5cmEgXFx1MDEyRmp1bnRhcyBzY2Vub3MgXFx1MDEyRnRyYXVraW1hcyBla3BvcnRhdmltbyBtZXR1P1wiLFxuICBjYW5ub3RSZXN0b3JlRnJvbUltYWdlOiBcIk5lcGF2eWtvIGF0c3RhdHl0aSBzY2Vub3MgaVxcdTAxNjEgXFx1MDE2MWlvIG51b3RyYXVrb3MgZmFpbG9cIixcbiAgaW52YWxpZFNjZW5lVXJsOiBcIk5lcGF2eWtvIHN1aW1wb3J0dW90aSBzY2Vub3MgaVxcdTAxNjEgcGF0ZWlrdG9zIG51b3JvZG9zIChVUkwpLiBKaSBhcmJhIGJsb2dhaSBzdWZvcm1hdHVvdGEsIGFyYmEgc2F2eWplIG5ldHVyaSB0ZWlzaW5nXFx1MDE3MyBFeGNhbGlkcmF3IEpTT04gZHVvbWVuXFx1MDE3My5cIixcbiAgcmVzZXRMaWJyYXJ5OiBcIlRhaSBpXFx1MDE2MXZhbHlzIHRhdm8gYmlibGlvdGVrXFx1MDEwNS4gQXIgdGlrcmFpIHRvIG5vcmk/XCIsXG4gIHJlbW92ZUl0ZW1zRnJvbXNMaWJyYXJ5OiBcIklcXHUwMTYxdHJpbnRpIHt7Y291bnR9fSBlbGVtZW50XFx1MDEwNS8tdXMgaVxcdTAxNjEgYmlibGlvdGVrb3M/XCIsXG4gIGludmFsaWRFbmNyeXB0aW9uS2V5OiBcIlxcdTAxNjBpZnJhdmltbyByYWt0YXMgdHVyaSBiXFx1MDE2QnRpIGlcXHUwMTYxIDIyIHNpbWJvbGlcXHUwMTczLiBSZWRhZ2F2aW1hcyBneXZhaSB5cmEgaVxcdTAxNjFqdW5ndGFzLlwiLFxuICBjb2xsYWJPZmZsaW5lV2FybmluZzogXCJcIlxufTtcbnZhciBlcnJvcnMgPSB7XG4gIHVuc3VwcG9ydGVkRmlsZVR5cGU6IFwiTmVwYWxhaWtvbWFzIGZhaWxvIHRpcGFzLlwiLFxuICBpbWFnZUluc2VydEVycm9yOiBcIk5lcHlrbyBcXHUwMTJGa2VsdGkgcGF2ZWlrc2xpdWtvLiBQYWJhbmR5ayB2XFx1MDExN2xpYXUuLi5cIixcbiAgZmlsZVRvb0JpZzogXCJQZXIgZGlkZWxpcyBmYWlsYXMuIERpZFxcdTAxN0VpYXVzaWFzIGxlaWRcXHUwMTdFaWFtYXMgZHlkaXMgeXJhIHt7bWF4U2l6ZX19LlwiLFxuICBzdmdJbWFnZUluc2VydEVycm9yOiBcIk5lcGF2eWtvIFxcdTAxMkZ0cmF1a3RpIFNWRyBwYXZlaWtzbGl1a28uIFBhbmFcXHUwMTYxdSwgam9nIFNWRyB5cmEgbmV2YWxpZHVzLlwiLFxuICBmYWlsZWRUb0ZldGNoSW1hZ2U6IFwiXCIsXG4gIGludmFsaWRTVkdTdHJpbmc6IFwiTmV2YWxpZHVzIFNWRy5cIixcbiAgY2Fubm90UmVzb2x2ZUNvbGxhYlNlcnZlcjogXCJOZXBhdnlrbyBwcmlzaWp1bmd0aSBwcmllIHNlcnZlcmlvIGJlbmRyYWRhcmJpYXZpbXVpLiBQZXJrcmF1ayBwdXNsYXBcXHUwMTJGIGlyIHBhYmFuZHlrIHByaXNpanVuZ3RpIGRhciBrYXJ0XFx1MDEwNS5cIixcbiAgaW1wb3J0TGlicmFyeUVycm9yOiBcIk5lcGF2eWtvIFxcdTAxMkZrZWx0aSBiaWJsaW90ZWtvc1wiLFxuICBjb2xsYWJTYXZlRmFpbGVkOiBcIlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkX3NpemVFeGNlZWRlZDogXCJcIixcbiAgaW1hZ2VUb29sTm90U3VwcG9ydGVkOiBcIlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJcIixcbiAgICBsaW5lMjogXCJcIixcbiAgICBsaW5lMzogXCJcIixcbiAgICBsaW5lNDogXCJcIlxuICB9LFxuICBsaWJyYXJ5RWxlbWVudFR5cGVFcnJvcjoge1xuICAgIGVtYmVkZGFibGU6IFwiXCIsXG4gICAgaWZyYW1lOiBcIlwiLFxuICAgIGltYWdlOiBcIlwiXG4gIH0sXG4gIGFzeW5jUGFzdGVGYWlsZWRPblJlYWQ6IFwiXCIsXG4gIGFzeW5jUGFzdGVGYWlsZWRPblBhcnNlOiBcIlwiLFxuICBjb3B5VG9TeXN0ZW1DbGlwYm9hcmRGYWlsZWQ6IFwiXCJcbn07XG52YXIgdG9vbEJhciA9IHtcbiAgc2VsZWN0aW9uOiBcIlxcdTAxN0R5bVxcdTAxMTdqaW1hc1wiLFxuICBpbWFnZTogXCJcXHUwMTJFa2VsdGkgcGF2ZWlrc2xpdWtcXHUwMTA1XCIsXG4gIHJlY3RhbmdsZTogXCJTdGFcXHUwMTBEaWFrYW1waXNcIixcbiAgZGlhbW9uZDogXCJEZWltYW50YXNcIixcbiAgZWxsaXBzZTogXCJFbGlwc1xcdTAxMTdcIixcbiAgYXJyb3c6IFwiUm9keWtsXFx1MDExN1wiLFxuICBsaW5lOiBcIkxpbmlqYVwiLFxuICBmcmVlZHJhdzogXCJQaWVcXHUwMTYxdGlcIixcbiAgdGV4dDogXCJUZWtzdGFzXCIsXG4gIGxpYnJhcnk6IFwiQmlibGlvdGVrYVwiLFxuICBsb2NrOiBcIkJhaWd1cyBwaWVcXHUwMTYxdGksIGlcXHUwMTYxbGFpa3l0aSBwYXNpcmlua3RcXHUwMTA1IFxcdTAxMkZyYW5rXFx1MDEyRlwiLFxuICBwZW5Nb2RlOiBcIlJhXFx1MDE2MXlrbGlvIHJlXFx1MDE3RWltYXMgLSBuZWxlaXN0aSBwcmlzaWxpZXRpbVxcdTAxNzNcIixcbiAgbGluazogXCJQcmlkXFx1MDExN3RpIC8gQXRuYXVqaW50aSBwYXNpcmlua3RvcyBmaWdcXHUwMTZCcm9zIG51b3JvZFxcdTAxMDVcIixcbiAgZXJhc2VyOiBcIlRyaW50dWthc1wiLFxuICBmcmFtZTogXCJcIixcbiAgbWFnaWNmcmFtZTogXCJcIixcbiAgZW1iZWRkYWJsZTogXCJcIixcbiAgbGFzZXI6IFwiXCIsXG4gIGhhbmQ6IFwiXCIsXG4gIGV4dHJhVG9vbHM6IFwiXCIsXG4gIG1lcm1haWRUb0V4Y2FsaWRyYXc6IFwiXCIsXG4gIG1hZ2ljU2V0dGluZ3M6IFwiXCJcbn07XG52YXIgaGVhZGluZ3MgPSB7XG4gIGNhbnZhc0FjdGlvbnM6IFwiVmVpa3NtYWkgc3UgZHJvYmVcIixcbiAgc2VsZWN0ZWRTaGFwZUFjdGlvbnM6IFwiVmVpa3NtYWkgc3UgcGFzaXJpbmt0YSBmaWdcXHUwMTZCcmFcIixcbiAgc2hhcGVzOiBcIkZpZ1xcdTAxNkJyb3NcIlxufTtcbnZhciBoaW50cyA9IHtcbiAgY2FudmFzUGFubmluZzogXCJcIixcbiAgbGluZWFyRWxlbWVudDogXCJQYXNwYXVkaW1haSBzdWt1cnMgcGFwaWxkb211cyB0YVxcdTAxNjFrdXMsIG5lcGVydHJhdWtpYW1hcyB0ZW1waW1hcyBzdWt1cnMgbGluaWpcXHUwMTA1XCIsXG4gIGZyZWVEcmF3OiBcIlNwYXVzayBpciB0ZW1waywgcGFsZWlzayBrYWkgbm9yXFx1MDExN3NpIHBhYmFpZ3RpXCIsXG4gIHRleHQ6IFwiVVxcdTAxN0V1b21pbmE6IHRla3N0XFx1MDEwNSB0YWlwIHBhdCBnYWxpbWEgcHJpZFxcdTAxMTd0aSBiZXQga3VyIHN1IGR2aWd1YnUgcGVsXFx1MDExN3MgcGFzcGF1ZGltdSwga29sIHBhcmlua2FzIFxcdTAxN0V5bVxcdTAxMTdqaW1vIFxcdTAxMkZyYW5raXNcIixcbiAgZW1iZWRkYWJsZTogXCJcIixcbiAgdGV4dF9zZWxlY3RlZDogXCJcIixcbiAgdGV4dF9lZGl0aW5nOiBcIlwiLFxuICBsaW5lYXJFbGVtZW50TXVsdGk6IFwiXCIsXG4gIGxvY2tBbmdsZTogXCJcIixcbiAgcmVzaXplOiBcIlwiLFxuICByZXNpemVJbWFnZTogXCJcIixcbiAgcm90YXRlOiBcIlwiLFxuICBsaW5lRWRpdG9yX2luZm86IFwiXCIsXG4gIGxpbmVFZGl0b3JfcG9pbnRTZWxlY3RlZDogXCJcIixcbiAgbGluZUVkaXRvcl9ub3RoaW5nU2VsZWN0ZWQ6IFwiXCIsXG4gIHBsYWNlSW1hZ2U6IFwiXCIsXG4gIHB1Ymxpc2hMaWJyYXJ5OiBcIlwiLFxuICBiaW5kVGV4dFRvRWxlbWVudDogXCJcIixcbiAgZGVlcEJveFNlbGVjdDogXCJcIixcbiAgZXJhc2VyUmV2ZXJ0OiBcIlwiLFxuICBmaXJlZm94X2NsaXBib2FyZF93cml0ZTogXCJcIixcbiAgZGlzYWJsZVNuYXBwaW5nOiBcIlwiXG59O1xudmFyIGNhbnZhc0Vycm9yID0ge1xuICBjYW5ub3RTaG93UHJldmlldzogXCJcIixcbiAgY2FudmFzVG9vQmlnOiBcIlwiLFxuICBjYW52YXNUb29CaWdUaXA6IFwiXCJcbn07XG52YXIgZXJyb3JTcGxhc2ggPSB7XG4gIGhlYWRpbmdNYWluOiBcIlwiLFxuICBjbGVhckNhbnZhc01lc3NhZ2U6IFwiXCIsXG4gIGNsZWFyQ2FudmFzQ2F2ZWF0OiBcIlwiLFxuICB0cmFja2VkVG9TZW50cnk6IFwiXCIsXG4gIG9wZW5Jc3N1ZU1lc3NhZ2U6IFwiXCIsXG4gIHNjZW5lQ29udGVudDogXCJcIlxufTtcbnZhciByb29tRGlhbG9nID0ge1xuICBkZXNjX2ludHJvOiBcIlwiLFxuICBkZXNjX3ByaXZhY3k6IFwiXCIsXG4gIGJ1dHRvbl9zdGFydFNlc3Npb246IFwiUHJhZFxcdTAxMTd0aSBzZWFuc1xcdTAxMDVcIixcbiAgYnV0dG9uX3N0b3BTZXNzaW9uOiBcIlN1c3RhYmR5dGkgc2VhbnNcXHUwMTA1XCIsXG4gIGRlc2NfaW5Qcm9ncmVzc0ludHJvOiBcIlwiLFxuICBkZXNjX3NoYXJlTGluazogXCJcIixcbiAgZGVzY19leGl0U2Vzc2lvbjogXCJcIixcbiAgc2hhcmVUaXRsZTogXCJcIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiS2xhaWRhXCJcbn07XG52YXIgZXhwb3J0RGlhbG9nID0ge1xuICBkaXNrX3RpdGxlOiBcIlxcdTAxMkVyYVxcdTAxNjF5dGkgXFx1MDEyRiBkaXNrXFx1MDEwNVwiLFxuICBkaXNrX2RldGFpbHM6IFwiXCIsXG4gIGRpc2tfYnV0dG9uOiBcIlxcdTAxMkVyYVxcdTAxNjF5dGkgXFx1MDEyRiBmYWlsXFx1MDEwNVwiLFxuICBsaW5rX3RpdGxlOiBcIk51b3JvZGEgZGFsaW5pbXVpc2lcIixcbiAgbGlua19kZXRhaWxzOiBcIlwiLFxuICBsaW5rX2J1dHRvbjogXCJcIixcbiAgZXhjYWxpZHJhd3BsdXNfZGVzY3JpcHRpb246IFwiXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2J1dHRvbjogXCJFa3Nwb3J0dW90aVwiLFxuICBleGNhbGlkcmF3cGx1c19leHBvcnRFcnJvcjogXCJcIlxufTtcbnZhciBoZWxwRGlhbG9nID0ge1xuICBibG9nOiBcIlwiLFxuICBjbGljazogXCJwYXNwYXVkaW1hc1wiLFxuICBkZWVwU2VsZWN0OiBcIlwiLFxuICBkZWVwQm94U2VsZWN0OiBcIlwiLFxuICBjdXJ2ZWRBcnJvdzogXCJCYW5ndW90YSByb2R5a2xcXHUwMTE3XCIsXG4gIGN1cnZlZExpbmU6IFwiQmFuZ3VvdGEgbGluaWphXCIsXG4gIGRvY3VtZW50YXRpb246IFwiRG9rdW1lbnRhY2lqYVwiLFxuICBkb3VibGVDbGljazogXCJkdmlndWJhcyBwYXNwYXVkaW1hc1wiLFxuICBkcmFnOiBcInZpbGt0aVwiLFxuICBlZGl0b3I6IFwiUmVkYWt0b3JpdXNcIixcbiAgZWRpdExpbmVBcnJvd1BvaW50czogXCJcIixcbiAgZWRpdFRleHQ6IFwiXCIsXG4gIGdpdGh1YjogXCJSYWRhaSBrbGFpZFxcdTAxMDU/IFBhdGVpa1wiLFxuICBob3d0bzogXCJWYWRvdmF1a2lzIG1cXHUwMTZCc1xcdTAxNzMgZ2lkdVwiLFxuICBvcjogXCJhcmJhXCIsXG4gIHByZXZlbnRCaW5kaW5nOiBcIlwiLFxuICB0b29sczogXCJcXHUwMTJFcmFua2lhaVwiLFxuICBzaG9ydGN1dHM6IFwiU3BhcnRpZWppIGtsYXZpXFx1MDE2MWFpXCIsXG4gIHRleHRGaW5pc2g6IFwiQmFpZ3RpIHJlZGFnYXZpbVxcdTAxMDUgKHRla3N0byByZWRha3Rvcml1amUpXCIsXG4gIHRleHROZXdMaW5lOiBcIlByaWRcXHUwMTE3dGkgbmF1alxcdTAxMDUgZWlsdXRcXHUwMTE5ICh0ZWt0byByZWRha3Rvcml1amUpXCIsXG4gIHRpdGxlOiBcIlBhZ2FsYmFcIixcbiAgdmlldzogXCJcIixcbiAgem9vbVRvRml0OiBcIlwiLFxuICB6b29tVG9TZWxlY3Rpb246IFwiUHJpYXJ0aW50aSBpa2kgcGFcXHUwMTdFeW1cXHUwMTE3dG9zIHZpZXRvc1wiLFxuICB0b2dnbGVFbGVtZW50TG9jazogXCJcIixcbiAgbW92ZVBhZ2VVcERvd246IFwiUGFqdWRpbnRpIHB1c2xhcFxcdTAxMkYgYXVrXFx1MDE2MXR5bi9cXHUwMTdFZW15blwiLFxuICBtb3ZlUGFnZUxlZnRSaWdodDogXCJQYWp1ZGludGkgcHVzbGFwXFx1MDEyRiBrYWlyXFx1MDExN24vZGVcXHUwMTYxaW5cXHUwMTE3blwiXG59O1xudmFyIGNsZWFyQ2FudmFzRGlhbG9nID0ge1xuICB0aXRsZTogXCJJXFx1MDE2MXZhbHl0aSBkcm9iXFx1MDExOVwiXG59O1xudmFyIHB1Ymxpc2hEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIlBhdmllXFx1MDE2MWludGkgYmlibGlvdGVrXFx1MDEwNVwiLFxuICBpdGVtTmFtZTogXCJFbGVtZW50byBwYXZhZGluaW1hc1wiLFxuICBhdXRob3JOYW1lOiBcIkF1dG9yaWF1cyB2YXJkYXNcIixcbiAgZ2l0aHViVXNlcm5hbWU6IFwiR2l0aHViIHNwYWx5dmFyZGlzXCIsXG4gIHR3aXR0ZXJVc2VybmFtZTogXCJUd2l0dGVyIHNsYXB5dmFyZGlzXCIsXG4gIGxpYnJhcnlOYW1lOiBcIkJpYmxpb3Rla29zIHBhdmFkaW5pbWFzXCIsXG4gIGxpYnJhcnlEZXNjOiBcIkJpYmxpb3Rla29zIGFwcmFcXHUwMTYxYXNcIixcbiAgd2Vic2l0ZTogXCJUaW5rbGFsYXBpc1wiLFxuICBwbGFjZWhvbGRlcjoge1xuICAgIGF1dGhvck5hbWU6IFwiVGF2byB2YXJkYXMgYXJiYSBzcGFseXZhcmRpc1wiLFxuICAgIGxpYnJhcnlOYW1lOiBcIlRhdm8gYmlibGlvdGVrb3MgcGF2YWRpbmltYXNcIixcbiAgICBsaWJyYXJ5RGVzYzogXCJUYXZvIGJpYmxpb3Rla29zIGFwcmFcXHUwMTYxYXMsIHBhZFxcdTAxMTd0aSBcXHUwMTdFbW9uXFx1MDExN21zIGdlcmlhdSBzdXByYXN0aSBqb3MgcGFza2lydFxcdTAxMkZcIixcbiAgICBnaXRodWJIYW5kbGU6IFwiXCIsXG4gICAgdHdpdHRlckhhbmRsZTogXCJcIixcbiAgICB3ZWJzaXRlOiBcIlwiXG4gIH0sXG4gIGVycm9yczoge1xuICAgIHJlcXVpcmVkOiBcIlByaXZhbG9tYXNcIixcbiAgICB3ZWJzaXRlOiBcIlxcdTAxMkV2ZXNraXRlIHRlaXNpbmdcXHUwMTA1IG51b3JvZFxcdTAxMDUgKFVSTClcIlxuICB9LFxuICBub3RlRGVzY3JpcHRpb246IFwiUGF0ZWlrIHNhdm8gYmlibGlvdGVrXFx1MDEwNSwgam9nIGppIGdhbFxcdTAxMTd0XFx1MDE3MyBiXFx1MDE2QnRpIFxcdTAxMkZ0cmF1a3RhIFxcdTAxMkYgPGxpbms+PC9saW5rPmpvZyBraXRpIFxcdTAxN0Vtb25cXHUwMTE3cyBnYWxcXHUwMTE3dFxcdTAxNzMgdGFpIG5hdWRvdGkgc2F2byBwaWVcXHUwMTYxaW5pdW9zZS5cIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiVmlzXFx1MDE3MyBwaXJtYSwgYmlibGlvdGVrYSB0dXJpIGJcXHUwMTZCdGkgcmFua2luaXUgYlxcdTAxNkJkdSBwYXR2aXJ0aW50YS4gUHJhXFx1MDE2MW9tZSBwYXNrYWl0eXRpIDxsaW5rPmdhaXJcXHUwMTE3czwvbGluaz5cIixcbiAgbm90ZUxpY2Vuc2U6IFwiPGxpbms+TUlUIGxpY2VuY2lqYSwgPC9saW5rPlwiLFxuICBub3RlSXRlbXM6IFwiXCIsXG4gIGF0bGVhc3RPbmVMaWJJdGVtOiBcIlwiLFxuICByZXB1Ymxpc2hXYXJuaW5nOiBcIlwiXG59O1xudmFyIHB1Ymxpc2hTdWNjZXNzRGlhbG9nID0ge1xuICB0aXRsZTogXCJCaWJsaW90ZWthIHBhdGVpa3RhXCIsXG4gIGNvbnRlbnQ6IFwiQVxcdTAxMERpXFx1MDE2QiB7e2F1dGhvck5hbWV9fS4gVGF2byBiaWJsaW90ZWthIGJ1dm8gcGF0ZWlrdGEgcGVyXFx1MDE3RWlcXHUwMTZCcmFpLiBHYWxpIHNla3RpIGJcXHUwMTZCc2VuXFx1MDEwNTxsaW5rPlxcdTAxMERpYTwvbGluaz5cIlxufTtcbnZhciBjb25maXJtRGlhbG9nID0ge1xuICByZXNldExpYnJhcnk6IFwiQXRzdGF0eXRpIGJpYmxpb3Rla1xcdTAxMDVcIixcbiAgcmVtb3ZlSXRlbXNGcm9tTGliOiBcIlBhXFx1MDE2MWFsaW50aSBwYXNpcmlua3R1cyBlbGVtZW50dXMgaVxcdTAxNjEgYmlibGlvdGVrb3NcIlxufTtcbnZhciBpbWFnZUV4cG9ydERpYWxvZyA9IHtcbiAgaGVhZGVyOiBcIlwiLFxuICBsYWJlbDoge1xuICAgIHdpdGhCYWNrZ3JvdW5kOiBcIlwiLFxuICAgIG9ubHlTZWxlY3RlZDogXCJcIixcbiAgICBkYXJrTW9kZTogXCJcIixcbiAgICBlbWJlZFNjZW5lOiBcIlwiLFxuICAgIHNjYWxlOiBcIlwiLFxuICAgIHBhZGRpbmc6IFwiXCJcbiAgfSxcbiAgdG9vbHRpcDoge1xuICAgIGVtYmVkU2NlbmU6IFwiXCJcbiAgfSxcbiAgdGl0bGU6IHtcbiAgICBleHBvcnRUb1BuZzogXCJcIixcbiAgICBleHBvcnRUb1N2ZzogXCJcIixcbiAgICBjb3B5UG5nVG9DbGlwYm9hcmQ6IFwiXCJcbiAgfSxcbiAgYnV0dG9uOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiXCIsXG4gICAgZXhwb3J0VG9Tdmc6IFwiXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIlwiXG4gIH1cbn07XG52YXIgZW5jcnlwdGVkID0ge1xuICB0b29sdGlwOiBcIlwiLFxuICBsaW5rOiBcIlwiXG59O1xudmFyIHN0YXRzID0ge1xuICBhbmdsZTogXCJcIixcbiAgZWxlbWVudDogXCJFbGVtZW50YXNcIixcbiAgZWxlbWVudHM6IFwiRWxlbWVudGFpXCIsXG4gIGhlaWdodDogXCJBdWtcXHUwMTYxdGlzXCIsXG4gIHNjZW5lOiBcIlNjZW5hXCIsXG4gIHNlbGVjdGVkOiBcIlBhc2lyaW5rdGFcIixcbiAgc3RvcmFnZTogXCJTYXVneWtsYVwiLFxuICB0aXRsZTogXCJJbmZvcm1hY2lqYSBtb2tzbGl1a2Ftc1wiLFxuICB0b3RhbDogXCJcIixcbiAgdmVyc2lvbjogXCJcIixcbiAgdmVyc2lvbkNvcHk6IFwiXCIsXG4gIHZlcnNpb25Ob3RBdmFpbGFibGU6IFwiXCIsXG4gIHdpZHRoOiBcIlBsb3Rpc1wiXG59O1xudmFyIHRvYXN0ID0ge1xuICBhZGRlZFRvTGlicmFyeTogXCJQcmlkXFx1MDExN3RhIFxcdTAxMkYgYmlibGlvdGVrXFx1MDEwNVwiLFxuICBjb3B5U3R5bGVzOiBcIlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiTnVrb3BpanVvdGEgXFx1MDEyRiBpXFx1MDE2MWthcnBpblxcdTAxMTkuXCIsXG4gIGNvcHlUb0NsaXBib2FyZEFzUG5nOiBcIlwiLFxuICBmaWxlU2F2ZWQ6IFwiRmFpbGFzIGlcXHUwMTYxc2F1Z290YXMuXCIsXG4gIGZpbGVTYXZlZFRvRmlsZW5hbWU6IFwiSVxcdTAxNjFzYXVnb3RhIFxcdTAxMkYge2ZpbGVuYW1lfVwiLFxuICBjYW52YXM6IFwiZHJvYlxcdTAxMTdcIixcbiAgc2VsZWN0aW9uOiBcIlwiLFxuICBwYXN0ZUFzU2luZ2xlRWxlbWVudDogXCJcIixcbiAgdW5hYmxlVG9FbWJlZDogXCJcIixcbiAgdW5yZWNvZ25pemVkTGlua0Zvcm1hdDogXCJcIlxufTtcbnZhciBjb2xvcnMgPSB7XG4gIHRyYW5zcGFyZW50OiBcIlBlcm1hdG9tYVwiLFxuICBibGFjazogXCJcIixcbiAgd2hpdGU6IFwiXCIsXG4gIHJlZDogXCJcIixcbiAgcGluazogXCJcIixcbiAgZ3JhcGU6IFwiXCIsXG4gIHZpb2xldDogXCJcIixcbiAgZ3JheTogXCJcIixcbiAgYmx1ZTogXCJcIixcbiAgY3lhbjogXCJcIixcbiAgdGVhbDogXCJcIixcbiAgZ3JlZW46IFwiXCIsXG4gIHllbGxvdzogXCJcIixcbiAgb3JhbmdlOiBcIlwiLFxuICBicm9uemU6IFwiXCJcbn07XG52YXIgd2VsY29tZVNjcmVlbiA9IHtcbiAgYXBwOiB7XG4gICAgY2VudGVyX2hlYWRpbmc6IFwiXCIsXG4gICAgY2VudGVyX2hlYWRpbmdfcGx1czogXCJcIixcbiAgICBtZW51SGludDogXCJcIlxuICB9LFxuICBkZWZhdWx0czoge1xuICAgIG1lbnVIaW50OiBcIlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nOiBcIlwiLFxuICAgIHRvb2xiYXJIaW50OiBcIlwiLFxuICAgIGhlbHBIaW50OiBcIlwiXG4gIH1cbn07XG52YXIgY29sb3JQaWNrZXIgPSB7XG4gIG1vc3RVc2VkQ3VzdG9tQ29sb3JzOiBcIlwiLFxuICBjb2xvcnM6IFwiXCIsXG4gIHNoYWRlczogXCJcIixcbiAgaGV4Q29kZTogXCJcIixcbiAgbm9TaGFkZXM6IFwiXCJcbn07XG52YXIgb3ZlcndyaXRlQ29uZmlybSA9IHtcbiAgYWN0aW9uOiB7XG4gICAgZXhwb3J0VG9JbWFnZToge1xuICAgICAgdGl0bGU6IFwiXCIsXG4gICAgICBidXR0b246IFwiXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcIlxuICAgIH0sXG4gICAgc2F2ZVRvRGlzazoge1xuICAgICAgdGl0bGU6IFwiXCIsXG4gICAgICBidXR0b246IFwiXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcIlxuICAgIH0sXG4gICAgZXhjYWxpZHJhd1BsdXM6IHtcbiAgICAgIHRpdGxlOiBcIlwiLFxuICAgICAgYnV0dG9uOiBcIlwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9XG4gIH0sXG4gIG1vZGFsOiB7XG4gICAgbG9hZEZyb21GaWxlOiB7XG4gICAgICB0aXRsZTogXCJcIixcbiAgICAgIGJ1dHRvbjogXCJcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfSxcbiAgICBzaGFyZWFibGVMaW5rOiB7XG4gICAgICB0aXRsZTogXCJcIixcbiAgICAgIGJ1dHRvbjogXCJcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfVxuICB9XG59O1xudmFyIG1lcm1haWQgPSB7XG4gIHRpdGxlOiBcIlwiLFxuICBidXR0b246IFwiXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlwiLFxuICBzeW50YXg6IFwiXCIsXG4gIHByZXZpZXc6IFwiXCJcbn07XG52YXIgbHRfTFRfZGVmYXVsdCA9IHtcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBidXR0b25zLFxuICBhbGVydHMsXG4gIGVycm9ycyxcbiAgdG9vbEJhcixcbiAgaGVhZGluZ3MsXG4gIGhpbnRzLFxuICBjYW52YXNFcnJvcixcbiAgZXJyb3JTcGxhc2gsXG4gIHJvb21EaWFsb2csXG4gIGVycm9yRGlhbG9nLFxuICBleHBvcnREaWFsb2csXG4gIGhlbHBEaWFsb2csXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgY29uZmlybURpYWxvZyxcbiAgaW1hZ2VFeHBvcnREaWFsb2csXG4gIGVuY3J5cHRlZCxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICBjb2xvcnMsXG4gIHdlbGNvbWVTY3JlZW4sXG4gIGNvbG9yUGlja2VyLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBtZXJtYWlkXG59O1xuZXhwb3J0IHtcbiAgYWxlcnRzLFxuICBidXR0b25zLFxuICBjYW52YXNFcnJvcixcbiAgY2xlYXJDYW52YXNEaWFsb2csXG4gIGNvbG9yUGlja2VyLFxuICBjb2xvcnMsXG4gIGNvbmZpcm1EaWFsb2csXG4gIGx0X0xUX2RlZmF1bHQgYXMgZGVmYXVsdCxcbiAgZW5jcnlwdGVkLFxuICBlcnJvckRpYWxvZyxcbiAgZXJyb3JTcGxhc2gsXG4gIGVycm9ycyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWFkaW5ncyxcbiAgaGVscERpYWxvZyxcbiAgaGludHMsXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBsYWJlbHMsXG4gIGxpYnJhcnksXG4gIG1lcm1haWQsXG4gIG92ZXJ3cml0ZUNvbmZpcm0sXG4gIHB1Ymxpc2hEaWFsb2csXG4gIHB1Ymxpc2hTdWNjZXNzRGlhbG9nLFxuICByb29tRGlhbG9nLFxuICBzdGF0cyxcbiAgdG9hc3QsXG4gIHRvb2xCYXIsXG4gIHdlbGNvbWVTY3JlZW5cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1sdC1MVC1NR1VCWDZDQS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/lt-LT-MGUBX6CA.js\n"));

/***/ })

}]);