"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_fr-FR-YE4VDJFI_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/fr-FR-YE4VDJFI.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/fr-FR-YE4VDJFI.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ fr_FR_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/fr-FR.json\nvar labels = {\n  paste: \"Coller\",\n  pasteAsPlaintext: \"Coller comme texte brut\",\n  pasteCharts: \"Coller les graphiques\",\n  selectAll: \"Tout s\\xE9lectionner\",\n  multiSelect: \"Ajouter l'\\xE9l\\xE9ment \\xE0 la s\\xE9lection\",\n  moveCanvas: \"D\\xE9placer le canevas\",\n  cut: \"Couper\",\n  copy: \"Copier\",\n  copyAsPng: \"Copier dans le presse-papier en PNG\",\n  copyAsSvg: \"Copier dans le presse-papier en SVG\",\n  copyText: \"Copier dans le presse-papier en tant que texte\",\n  copySource: \"Copier la source dans le presse-papiers\",\n  convertToCode: \"Convertir en code\",\n  bringForward: \"Envoyer vers l'avant\",\n  sendToBack: \"D\\xE9placer \\xE0 l'arri\\xE8re-plan\",\n  bringToFront: \"Mettre au premier plan\",\n  sendBackward: \"Reculer d'un plan\",\n  delete: \"Supprimer\",\n  copyStyles: \"Copier les styles\",\n  pasteStyles: \"Coller les styles\",\n  stroke: \"Trait\",\n  background: \"Arri\\xE8re-plan\",\n  fill: \"Remplissage\",\n  strokeWidth: \"Largeur du contour\",\n  strokeStyle: \"Style du trait\",\n  strokeStyle_solid: \"Continu\",\n  strokeStyle_dashed: \"Tirets\",\n  strokeStyle_dotted: \"Pointill\\xE9s\",\n  sloppiness: \"Style de trac\\xE9\",\n  opacity: \"Transparence\",\n  textAlign: \"Alignement du texte\",\n  edges: \"Angles\",\n  sharp: \"Pointus\",\n  round: \"Arrondis\",\n  arrowheads: \"Extr\\xE9mit\\xE9s\",\n  arrowhead_none: \"Sans\",\n  arrowhead_arrow: \"Fl\\xE8che\",\n  arrowhead_bar: \"Barre\",\n  arrowhead_circle: \"Cercle\",\n  arrowhead_circle_outline: \"Contour du cercle\",\n  arrowhead_triangle: \"Triangle\",\n  arrowhead_triangle_outline: \"Triangle (contour)\",\n  arrowhead_diamond: \"Losange\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Taille de la police\",\n  fontFamily: \"Police\",\n  addWatermark: 'Ajouter \"R\\xE9alis\\xE9 avec Excalidraw\"',\n  handDrawn: \"\\xC0 main lev\\xE9e\",\n  normal: \"Normale\",\n  code: \"Code\",\n  small: \"Petite\",\n  medium: \"Moyenne\",\n  large: \"Grande\",\n  veryLarge: \"Tr\\xE8s grande\",\n  solid: \"Solide\",\n  hachure: \"Hachures\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Hachures crois\\xE9es\",\n  thin: \"Fine\",\n  bold: \"\\xC9paisse\",\n  left: \"\\xC0 gauche\",\n  center: \"Au centre\",\n  right: \"\\xC0 droite\",\n  extraBold: \"Tr\\xE8s \\xE9paisse\",\n  architect: \"Architecte\",\n  artist: \"Artiste\",\n  cartoonist: \"Caricaturiste\",\n  fileTitle: \"Nom du fichier\",\n  colorPicker: \"S\\xE9lecteur de couleur\",\n  canvasColors: \"Utilis\\xE9 sur la zone de dessin\",\n  canvasBackground: \"Arri\\xE8re-plan du canevas\",\n  drawingCanvas: \"Zone de dessin\",\n  layers: \"Disposition\",\n  actions: \"Actions\",\n  language: \"Langue\",\n  liveCollaboration: \"Collaboration en direct...\",\n  duplicateSelection: \"Dupliquer\",\n  untitled: \"Sans-titre\",\n  name: \"Nom\",\n  yourName: \"Votre nom\",\n  madeWithExcalidraw: \"Fait avec Excalidraw\",\n  group: \"Grouper la s\\xE9lection\",\n  ungroup: \"D\\xE9grouper la s\\xE9lection\",\n  collaborators: \"Collaborateurs\",\n  showGrid: \"Afficher la grille\",\n  addToLibrary: \"Ajouter \\xE0 la biblioth\\xE8que\",\n  removeFromLibrary: \"Supprimer de la biblioth\\xE8que\",\n  libraryLoadingMessage: \"Chargement de la biblioth\\xE8que\\u2026\",\n  libraries: \"Parcourir les biblioth\\xE8ques\",\n  loadingScene: \"Chargement de la sc\\xE8ne\\u2026\",\n  align: \"Alignement\",\n  alignTop: \"Aligner en haut\",\n  alignBottom: \"Aligner en bas\",\n  alignLeft: \"Aligner \\xE0 gauche\",\n  alignRight: \"Aligner \\xE0 droite\",\n  centerVertically: \"Centrer verticalement\",\n  centerHorizontally: \"Centrer horizontalement\",\n  distributeHorizontally: \"R\\xE9partir horizontalement\",\n  distributeVertically: \"R\\xE9partir verticalement\",\n  flipHorizontal: \"Retourner horizontalement\",\n  flipVertical: \"Retourner verticalement\",\n  viewMode: \"Mode pr\\xE9sentation\",\n  share: \"Partager\",\n  showStroke: \"Afficher le s\\xE9lecteur de couleur de trait\",\n  showBackground: \"Afficher le s\\xE9lecteur de couleur de fond\",\n  toggleTheme: \"Changer le th\\xE8me\",\n  personalLib: \"Biblioth\\xE8que personnelle\",\n  excalidrawLib: \"Biblioth\\xE8que Excalidraw\",\n  decreaseFontSize: \"Diminuer la taille de police\",\n  increaseFontSize: \"Augmenter la taille de la police\",\n  unbindText: \"Dissocier le texte\",\n  bindText: \"Associer le texte au conteneur\",\n  createContainerFromText: \"Encadrer le texte dans un conteneur\",\n  link: {\n    edit: \"Modifier le lien\",\n    editEmbed: \"\\xC9diter le lien & int\\xE9grer\",\n    create: \"Ajouter un lien\",\n    createEmbed: \"Cr\\xE9er un lien & int\\xE9grer\",\n    label: \"Lien\",\n    labelEmbed: \"Lier & int\\xE9grer\",\n    empty: \"Aucun lien d\\xE9fini\"\n  },\n  lineEditor: {\n    edit: \"Modifier la ligne\",\n    exit: \"Quitter l'\\xE9diteur de ligne\"\n  },\n  elementLock: {\n    lock: \"Verrouiller\",\n    unlock: \"D\\xE9verrouiller\",\n    lockAll: \"Tout verrouiller\",\n    unlockAll: \"Tout d\\xE9verrouiller\"\n  },\n  statusPublished: \"Publi\\xE9\",\n  sidebarLock: \"Maintenir la barre lat\\xE9rale ouverte\",\n  selectAllElementsInFrame: \"S\\xE9lectionner tous les \\xE9l\\xE9ments du cadre\",\n  removeAllElementsFromFrame: \"Supprimer tous les \\xE9l\\xE9ments du cadre\",\n  eyeDropper: \"Choisir la couleur depuis la toile\",\n  textToDiagram: \"Texte vers Diagramme\",\n  prompt: \"Consignes\"\n};\nvar library = {\n  noItems: \"Aucun \\xE9l\\xE9ment n'a encore \\xE9t\\xE9 ajout\\xE9 ...\",\n  hint_emptyLibrary: \"S\\xE9lectionnez un \\xE9l\\xE9ment sur le canevas pour l'ajouter ici ou installez une biblioth\\xE8que depuis le d\\xE9p\\xF4t public, ci-dessous.\",\n  hint_emptyPrivateLibrary: \"S\\xE9lectionnez un \\xE9l\\xE9ment sur le canevas pour l'ajouter ici.\"\n};\nvar buttons = {\n  clearReset: \"R\\xE9initialiser le canevas\",\n  exportJSON: \"Exporter comme fichier\",\n  exportImage: \"Exporter l'image...\",\n  export: \"Enregistrer sous...\",\n  copyToClipboard: \"Copier dans le presse-papier\",\n  save: \"Enregistrer dans le fichier actuel\",\n  saveAs: \"Enregistrer sous\",\n  load: \"Ouvrir\",\n  getShareableLink: \"Obtenir un lien de partage\",\n  close: \"Fermer\",\n  selectLanguage: \"Choisir une langue\",\n  scrollBackToContent: \"Revenir au contenu\",\n  zoomIn: \"Zoomer\",\n  zoomOut: \"D\\xE9zoomer\",\n  resetZoom: \"R\\xE9initialiser le zoom\",\n  menu: \"Menu\",\n  done: \"Termin\\xE9\",\n  edit: \"Modifier\",\n  undo: \"Annuler\",\n  redo: \"R\\xE9tablir\",\n  resetLibrary: \"R\\xE9initialiser la biblioth\\xE8que\",\n  createNewRoom: \"Cr\\xE9er une nouvelle salle\",\n  fullScreen: \"Plein \\xE9cran\",\n  darkMode: \"Mode sombre\",\n  lightMode: \"Mode clair\",\n  zenMode: \"Mode zen\",\n  objectsSnapMode: \"Aimanter aux objets\",\n  exitZenMode: \"Quitter le mode zen\",\n  cancel: \"Annuler\",\n  clear: \"Effacer\",\n  remove: \"Supprimer\",\n  embed: \"Activer/D\\xE9sactiver l'int\\xE9gration\",\n  publishLibrary: \"Publier\",\n  submit: \"Envoyer\",\n  confirm: \"Confirmer\",\n  embeddableInteractionButton: \"Cliquez pour interagir\"\n};\nvar alerts = {\n  clearReset: \"L'int\\xE9gralit\\xE9 du canevas va \\xEAtre effac\\xE9e. \\xCAtes-vous s\\xFBr ?\",\n  couldNotCreateShareableLink: \"Impossible de cr\\xE9er un lien de partage.\",\n  couldNotCreateShareableLinkTooBig: \"Impossible de cr\\xE9er un lien de partage : la sc\\xE8ne est trop volumineuse\",\n  couldNotLoadInvalidFile: \"Impossible de charger un fichier invalide\",\n  importBackendFailed: \"L'importation depuis le serveur a \\xE9chou\\xE9.\",\n  cannotExportEmptyCanvas: \"Impossible d'exporter un canevas vide.\",\n  couldNotCopyToClipboard: \"Impossible de copier dans le presse-papiers.\",\n  decryptFailed: \"Les donn\\xE9es n'ont pas pu \\xEAtre d\\xE9chiffr\\xE9es.\",\n  uploadedSecurly: \"Le t\\xE9l\\xE9chargement a \\xE9t\\xE9 s\\xE9curis\\xE9 avec un chiffrement de bout en bout, ce qui signifie que ni Excalidraw ni personne d'autre ne peut en lire le contenu.\",\n  loadSceneOverridePrompt: \"Le chargement d'un dessin externe remplacera votre contenu actuel. Souhaitez-vous continuer ?\",\n  collabStopOverridePrompt: \"Arr\\xEAter la session \\xE9crasera votre pr\\xE9c\\xE9dent dessin stock\\xE9 localement. \\xCAtes-vous s\\xFBr\\xB7e ?\\n\\n(Si vous voulez garder votre dessin local, fermez simplement l'onglet du navigateur \\xE0 la place.)\",\n  errorAddingToLibrary: \"Impossible d'ajouter l'\\xE9l\\xE9ment \\xE0 la biblioth\\xE8que\",\n  errorRemovingFromLibrary: \"Impossible de retirer l'\\xE9l\\xE9ment de la biblioth\\xE8que\",\n  confirmAddLibrary: \"Cela va ajouter {{numShapes}} forme(s) \\xE0 votre biblioth\\xE8que. \\xCAtes-vous s\\xFBr\\xB7e ?\",\n  imageDoesNotContainScene: \"Cette image ne semble pas contenir de donn\\xE9es de sc\\xE8ne. Avez-vous activ\\xE9 l'int\\xE9gration de sc\\xE8ne lors de l'exportation ?\",\n  cannotRestoreFromImage: \"Impossible de restaurer la sc\\xE8ne depuis ce fichier image\",\n  invalidSceneUrl: \"Impossible d'importer la sc\\xE8ne depuis l'URL fournie. Elle est soit incorrecte, soit ne contient pas de donn\\xE9es JSON Excalidraw valides.\",\n  resetLibrary: \"Cela va effacer votre biblioth\\xE8que. \\xCAtes-vous s\\xFBr\\xB7e ?\",\n  removeItemsFromsLibrary: \"Supprimer {{count}} \\xE9l\\xE9ment(s) de la biblioth\\xE8que\\xA0?\",\n  invalidEncryptionKey: \"La cl\\xE9 de chiffrement doit comporter 22 caract\\xE8res. La collaboration en direct est d\\xE9sactiv\\xE9e.\",\n  collabOfflineWarning: \"Aucune connexion internet disponible.\\nVos modifications ne seront pas enregistr\\xE9es !\"\n};\nvar errors = {\n  unsupportedFileType: \"Type de fichier non support\\xE9.\",\n  imageInsertError: \"Impossible d'ins\\xE9rer l'image. R\\xE9essayez plus tard...\",\n  fileTooBig: \"Le fichier est trop volumineux. La taille maximale autoris\\xE9e est de {{maxSize}}.\",\n  svgImageInsertError: \"Impossible d'ins\\xE9rer l'image SVG. Le balisage SVG semble invalide.\",\n  failedToFetchImage: \"\\xC9chec de r\\xE9cup\\xE9ration de l'image.\",\n  invalidSVGString: \"SVG invalide.\",\n  cannotResolveCollabServer: \"Impossible de se connecter au serveur collaboratif. Veuillez recharger la page et r\\xE9essayer.\",\n  importLibraryError: \"Impossible de charger la biblioth\\xE8que\",\n  collabSaveFailed: \"Impossible d'enregistrer dans la base de donn\\xE9es en arri\\xE8re-plan. Si des probl\\xE8mes persistent, vous devriez enregistrer votre fichier localement pour vous assurer de ne pas perdre votre travail.\",\n  collabSaveFailed_sizeExceeded: \"Impossible d'enregistrer dans la base de donn\\xE9es en arri\\xE8re-plan, le tableau semble trop grand. Vous devriez enregistrer le fichier localement pour vous assurer de ne pas perdre votre travail.\",\n  imageToolNotSupported: \"Les images sont d\\xE9sactiv\\xE9es.\",\n  brave_measure_text_error: {\n    line1: \"On dirait que vous utilisez le navigateur Brave avec l'option <bold>Bloquer agressivement le fichage</bold> activ\\xE9e.\",\n    line2: \"Cela pourrait entra\\xEEner des probl\\xE8mes avec les <bold>\\xC9l\\xE9ments Textuels</bold> dans vos dessins.\",\n    line3: \"Nous recommandons fortement de d\\xE9sactiver cette option. Vous pouvez suivre <link>ces instructions</link> pour savoir comment faire.\",\n    line4: \"Si d\\xE9sactiver cette option de r\\xE9sout pas le probl\\xE8me d'affichage des \\xE9l\\xE9ments textuels, veuillez ouvrir un <issueLink>ticket</issueLink> sur notre GitHub, ou \\xE9crivez-nous sur notre <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Les \\xE9l\\xE9ments int\\xE9gr\\xE9s ne peuvent pas \\xEAtre ajout\\xE9s \\xE0 la librairie.\",\n    iframe: \"\",\n    image: \"Le support pour l'ajout d'images \\xE0 la librairie arrive bient\\xF4t\\xA0!\"\n  },\n  asyncPasteFailedOnRead: \"Impossible de coller (impossible de lire le presse-papiers syst\\xE8me).\",\n  asyncPasteFailedOnParse: \"Impossible de coller.\",\n  copyToSystemClipboardFailed: \"\\xC9chec de la copie dans le presse-papiers.\"\n};\nvar toolBar = {\n  selection: \"S\\xE9lection\",\n  image: \"Ins\\xE9rer une image\",\n  rectangle: \"Rectangle\",\n  diamond: \"Losange\",\n  ellipse: \"Ellipse\",\n  arrow: \"Fl\\xE8che\",\n  line: \"Ligne\",\n  freedraw: \"Dessiner\",\n  text: \"Texte\",\n  library: \"Biblioth\\xE8que\",\n  lock: \"Garder l'outil s\\xE9lectionn\\xE9 actif apr\\xE8s le dessin\",\n  penMode: \"Mode stylo - \\xE9vite le toucher\",\n  link: \"Ajouter/mettre \\xE0 jour le lien pour une forme s\\xE9lectionn\\xE9e\",\n  eraser: \"Gomme\",\n  frame: \"Outil de cadre\",\n  magicframe: \"\",\n  embeddable: \"Int\\xE9gration Web\",\n  laser: \"Pointeur laser\",\n  hand: \"Mains (outil de d\\xE9placement de la vue)\",\n  extraTools: \"Plus d'outils\",\n  mermaidToExcalidraw: \"De Mermaid \\xE0 Excalidraw\",\n  magicSettings: \"Param\\xE8tres IA\"\n};\nvar headings = {\n  canvasActions: \"Actions du canevas\",\n  selectedShapeActions: \"Actions pour la forme s\\xE9lectionn\\xE9e\",\n  shapes: \"Formes\"\n};\nvar hints = {\n  canvasPanning: \"Pour d\\xE9placer la zone de dessin, maintenez la molette de la souris enfonc\\xE9e ou la barre d'espace tout en faisant glisser, ou utiliser l'outil main.\",\n  linearElement: \"Cliquez pour d\\xE9marrer plusieurs points, faites glisser pour une seule ligne\",\n  freeDraw: \"Cliquez et faites glissez, rel\\xE2chez quand vous avez termin\\xE9\",\n  text: \"Astuce : vous pouvez aussi ajouter du texte en double-cliquant n'importe o\\xF9 avec l'outil de s\\xE9lection\",\n  embeddable: \"Cliquez et glissez pour cr\\xE9er une int\\xE9gration de site web\",\n  text_selected: \"Double-cliquez ou appuyez sur ENTR\\xC9E pour modifier le texte\",\n  text_editing: \"Appuyez sur \\xC9CHAP ou Ctrl/Cmd+ENTR\\xC9E pour terminer l'\\xE9dition\",\n  linearElementMulti: \"Cliquez sur le dernier point ou appuyez sur \\xC9chap ou Entr\\xE9e pour terminer\",\n  lockAngle: \"Vous pouvez restreindre l'angle en maintenant MAJ\",\n  resize: \"Vous pouvez conserver les proportions en maintenant la touche MAJ pendant le redimensionnement, maintenez la touche ALT pour redimensionner par rapport au centre\",\n  resizeImage: \"Vous pouvez redimensionner librement en maintenant SHIFT,\\nmaintenez ALT pour redimensionner depuis le centre\",\n  rotate: \"Vous pouvez restreindre les angles en maintenant MAJ pendant la rotation\",\n  lineEditor_info: \"Maintenez CtrlOrCmd et Double-cliquez ou appuyez sur CtrlOrCmd + Entr\\xE9e pour modifier les points\",\n  lineEditor_pointSelected: \"Appuyer sur Suppr. pour supprimer des points, Ctrl ou Cmd+D pour dupliquer, ou faire glisser pour d\\xE9placer\",\n  lineEditor_nothingSelected: \"S\\xE9lectionner un point pour \\xE9diter (maintenir la touche MAJ pour en s\\xE9lectionner plusieurs),\\nou maintenir la touche Alt enfonc\\xE9e et cliquer pour ajouter de nouveaux points\",\n  placeImage: \"Cliquez pour placer l'image, ou cliquez et faites glisser pour d\\xE9finir sa taille manuellement\",\n  publishLibrary: \"Publier votre propre biblioth\\xE8que\",\n  bindTextToElement: \"Appuyer sur Entr\\xE9e pour ajouter du texte\",\n  deepBoxSelect: \"Maintenir Ctrl ou Cmd pour s\\xE9lectionner dans les groupes et emp\\xEAcher le d\\xE9placement\",\n  eraserRevert: \"Maintenez Alt enfonc\\xE9 pour annuler les \\xE9l\\xE9ments marqu\\xE9s pour suppression\",\n  firefox_clipboard_write: `Cette fonctionnalit\\xE9 devrait pouvoir \\xEAtre activ\\xE9e en d\\xE9finissant l'option \"dom.events.asyncClipboard.clipboard.clipboardItem\" \\xE0 \"true\". Pour modifier les param\\xE8tres du navigateur dans Firefox, visitez la page \"about:config\".`,\n  disableSnapping: \"Maintenez CtrlOuCmd pour d\\xE9sactiver l'aimantation\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Impossible d\\u2019afficher l\\u2019aper\\xE7u\",\n  canvasTooBig: \"Le canevas est peut-\\xEAtre trop grand.\",\n  canvasTooBigTip: \"Astuce : essayez de rapprocher un peu les \\xE9l\\xE9ments les plus \\xE9loign\\xE9s.\"\n};\nvar errorSplash = {\n  headingMain: \"Une erreur est survenue. Essayez <button>de recharger la page.</button>\",\n  clearCanvasMessage: \"Si le rechargement ne r\\xE9sout pas l'erreur, essayez <button>effacement du canevas.</button>\",\n  clearCanvasCaveat: \" Cela entra\\xEEnera une perte du travail \",\n  trackedToSentry: \"L'erreur avec l'identifiant {{eventId}} a \\xE9t\\xE9 enregistr\\xE9e dans notre syst\\xE8me.\",\n  openIssueMessage: \"Nous avons fait tr\\xE8s attention \\xE0 ne pas inclure les informations de votre sc\\xE8ne dans l'erreur. Si votre sc\\xE8ne n'est pas priv\\xE9e, veuillez envisager de poursuivre sur notre <button>outil de suivi des bugs.</button> Veuillez inclure les informations ci-dessous en les copiant-collant dans le ticket GitHub.\",\n  sceneContent: \"Contenu de la sc\\xE8ne :\"\n};\nvar roomDialog = {\n  desc_intro: \"Vous pouvez inviter des personnes \\xE0 collaborer avec vous sur votre sc\\xE8ne actuelle.\",\n  desc_privacy: \"Pas d'inqui\\xE9tude, la session utilise le chiffrement de bout en bout, donc tout ce que vous dessinez restera priv\\xE9. M\\xEAme notre serveur ne pourra voir ce que vous faites.\",\n  button_startSession: \"D\\xE9marrer la session\",\n  button_stopSession: \"Arr\\xEAter la session\",\n  desc_inProgressIntro: \"La session de collaboration en direct est maintenant en cours.\",\n  desc_shareLink: \"Partagez ce lien avec les personnes avec lesquelles vous souhaitez collaborer :\",\n  desc_exitSession: \"Arr\\xEAter la session vous d\\xE9connectera de la salle, mais vous pourrez continuer \\xE0 travailler avec la sc\\xE8ne, localement. Notez que cela n'affectera pas les autres personnes, et ils pourront toujours collaborer sur leur version.\",\n  shareTitle: \"Rejoindre une session de collaboration en direct sur Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Erreur\"\n};\nvar exportDialog = {\n  disk_title: \"Enregistrer sur le disque\",\n  disk_details: \"Exporter les donn\\xE9es de la sc\\xE8ne comme un fichier que vous pourrez importer ult\\xE9rieurement.\",\n  disk_button: \"Enregistrer comme fichier\",\n  link_title: \"Lien partageable\",\n  link_details: \"Exporter comme un lien en lecture seule.\",\n  link_button: \"Exporter comme lien\",\n  excalidrawplus_description: \"Enregistrer la sc\\xE8ne dans votre espace de travail Excalidraw+.\",\n  excalidrawplus_button: \"Exporter\",\n  excalidrawplus_exportError: \"Impossible d'exporter vers Excalidraw+ pour le moment...\"\n};\nvar helpDialog = {\n  blog: \"Lire notre blog\",\n  click: \"clic\",\n  deepSelect: \"S\\xE9lection dans les groupes\",\n  deepBoxSelect: \"S\\xE9lectionner dans les groupes, et emp\\xEAcher le d\\xE9placement\",\n  curvedArrow: \"Fl\\xE8che courb\\xE9e\",\n  curvedLine: \"Ligne courb\\xE9e\",\n  documentation: \"Documentation\",\n  doubleClick: \"double-clic\",\n  drag: \"glisser\",\n  editor: \"\\xC9diteur\",\n  editLineArrowPoints: \"Modifier les points de ligne/fl\\xE8che\",\n  editText: \"Modifier le texte / ajouter un libell\\xE9\",\n  github: \"Probl\\xE8me trouv\\xE9 ? Soumettre\",\n  howto: \"Suivez nos guides\",\n  or: \"ou\",\n  preventBinding: \"Emp\\xEAcher la liaison de fl\\xE8che\",\n  tools: \"Outils\",\n  shortcuts: \"Raccourcis clavier\",\n  textFinish: \"Terminer l'\\xE9dition (\\xE9diteur de texte)\",\n  textNewLine: \"Ajouter une nouvelle ligne (\\xE9diteur de texte)\",\n  title: \"Aide\",\n  view: \"Affichage\",\n  zoomToFit: \"Zoomer pour voir tous les \\xE9l\\xE9ments\",\n  zoomToSelection: \"Zoomer sur la s\\xE9lection\",\n  toggleElementLock: \"Verrouiller/d\\xE9verrouiller la s\\xE9lection\",\n  movePageUpDown: \"D\\xE9placer la page vers le haut/bas\",\n  movePageLeftRight: \"D\\xE9placer la page vers la gauche/droite\"\n};\nvar clearCanvasDialog = {\n  title: \"Effacer la zone de dessin\"\n};\nvar publishDialog = {\n  title: \"Publier la biblioth\\xE8que\",\n  itemName: \"Nom de l\\u2019\\xE9l\\xE9ment\",\n  authorName: \"Nom de l'auteur\",\n  githubUsername: \"Nom d'utilisateur GitHub\",\n  twitterUsername: \"Nom d'utilisateur Twitter\",\n  libraryName: \"Nom de la biblioth\\xE8que\",\n  libraryDesc: \"Description de la biblioth\\xE8que\",\n  website: \"Site web\",\n  placeholder: {\n    authorName: \"Votre nom ou nom d'utilisateur\",\n    libraryName: \"Nom de votre biblioth\\xE8que\",\n    libraryDesc: \"Description de votre biblioth\\xE8que pour aider les gens \\xE0 comprendre son usage\",\n    githubHandle: \"Nom d'utilisateur GitHub (optionnel), pour que tu puisses modifier la biblioth\\xE8que une fois soumise pour v\\xE9rification\",\n    twitterHandle: \"Nom d'utilisateur Twitter (optionnel), pour savoir qui cr\\xE9diter lors de la promotion sur Twitter\",\n    website: \"Lien vers votre site web personnel ou autre (optionnel)\"\n  },\n  errors: {\n    required: \"Requis\",\n    website: \"Entrer une URL valide\"\n  },\n  noteDescription: \"Soumets ta biblioth\\xE8que pour l'inclure au <link>d\\xE9p\\xF4t de biblioth\\xE8que publique</link>pour permettre son utilisation par autrui dans leurs dessins.\",\n  noteGuidelines: \"La biblioth\\xE8que doit d'abord \\xEAtre approuv\\xE9e manuellement. Veuillez lire les <link>lignes directrices</link> avant de la soumettre. Vous aurez besoin d'un compte GitHub pour communiquer et apporter des modifications si demand\\xE9, mais ce n'est pas obligatoire.\",\n  noteLicense: \"En soumettant, vous acceptez que la biblioth\\xE8que soit publi\\xE9e sous la <link>Licence MIT, </link>ce qui en gros signifie que tout le monde peut l'utiliser sans restrictions.\",\n  noteItems: \"Chaque \\xE9l\\xE9ment de la biblioth\\xE8que doit avoir son propre nom afin qu'il soit filtrable. Les \\xE9l\\xE9ments de biblioth\\xE8que suivants seront inclus :\",\n  atleastOneLibItem: \"Veuillez s\\xE9lectionner au moins un \\xE9l\\xE9ment de biblioth\\xE8que pour commencer\",\n  republishWarning: \"Remarque : certains des \\xE9l\\xE9ments s\\xE9lectionn\\xE9s sont marqu\\xE9s comme \\xE9tant d\\xE9j\\xE0 publi\\xE9s/soumis. Vous devez uniquement resoumettre des \\xE9l\\xE9ments lors de la mise \\xE0 jour d'une biblioth\\xE8que ou d'une soumission existante.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioth\\xE8que soumise\",\n  content: \"Merci {{authorName}}. Votre biblioth\\xE8que a \\xE9t\\xE9 soumise pour examen. Vous pouvez suivre le statut<link>ici</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"R\\xE9initialiser la biblioth\\xE8que\",\n  removeItemsFromLib: \"Enlever les \\xE9l\\xE9ments s\\xE9lectionn\\xE9s de la biblioth\\xE8que\"\n};\nvar imageExportDialog = {\n  header: \"Exporter l'image\",\n  label: {\n    withBackground: \"Fond\",\n    onlySelected: \"Uniquement la s\\xE9lection\",\n    darkMode: \"Mode sombre\",\n    embedScene: \"Int\\xE9grer la sc\\xE8ne\",\n    scale: \"\\xC9chelle\",\n    padding: \"Marge interne\"\n  },\n  tooltip: {\n    embedScene: \"Les donn\\xE9es de la sc\\xE8ne seront sauvegard\\xE9es dans le fichier PNG/SVG export\\xE9 afin que la sc\\xE8ne puisse \\xEAtre restaur\\xE9e depuis celui-ci.\\nCela augmentera la taille du fichier export\\xE9.\"\n  },\n  title: {\n    exportToPng: \"Exporter en PNG\",\n    exportToSvg: \"Exporter en SVG\",\n    copyPngToClipboard: \"Copier le PNG dans le presse-papier\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copier dans le presse-papier\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Vos dessins sont chiffr\\xE9s de bout en bout, les serveurs d'Excalidraw ne les verront jamais.\",\n  link: \"Article de blog sur le chiffrement de bout en bout dans Excalidraw\"\n};\nvar stats = {\n  angle: \"Angle\",\n  element: \"\\xC9l\\xE9ment\",\n  elements: \"\\xC9l\\xE9ments\",\n  height: \"Hauteur\",\n  scene: \"Sc\\xE8ne\",\n  selected: \"S\\xE9lection\",\n  storage: \"Stockage\",\n  title: \"Stats pour les nerds\",\n  total: \"Total\",\n  version: \"Version\",\n  versionCopy: \"Cliquer pour copier\",\n  versionNotAvailable: \"Version non disponible\",\n  width: \"Largeur\"\n};\nvar toast = {\n  addedToLibrary: \"Ajout\\xE9 \\xE0 la biblioth\\xE8que\",\n  copyStyles: \"Styles copi\\xE9s.\",\n  copyToClipboard: \"Copi\\xE9 dans le presse-papier.\",\n  copyToClipboardAsPng: \"{{exportSelection}} copi\\xE9 dans le presse-papier en PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Fichier enregistr\\xE9.\",\n  fileSavedToFilename: \"Enregistr\\xE9 sous {filename}\",\n  canvas: \"canevas\",\n  selection: \"s\\xE9lection\",\n  pasteAsSingleElement: \"Utiliser {{shortcut}} pour coller comme un seul \\xE9l\\xE9ment,\\nou coller dans un \\xE9diteur de texte existant\",\n  unableToEmbed: \"Int\\xE9grer cet URL n'est actuellement pas autoris\\xE9. Ouvrez un ticket sur GitHub pour demander son ajout \\xE0 la liste blanche\",\n  unrecognizedLinkFormat: \"Le lien que vous avez int\\xE9gr\\xE9 ne correspond pas au format attendu. Veuillez essayer de coller la cha\\xEEne d'int\\xE9gration fournie par le site source\"\n};\nvar colors = {\n  transparent: \"Transparent\",\n  black: \"Noir\",\n  white: \"Blanc\",\n  red: \"Rouge\",\n  pink: \"Rose\",\n  grape: \"Mauve\",\n  violet: \"Violet\",\n  gray: \"Gris\",\n  blue: \"Bleu\",\n  cyan: \"Cyan\",\n  teal: \"Turquoise\",\n  green: \"Vert\",\n  yellow: \"Jaune\",\n  orange: \"Orange\",\n  bronze: \"Bronze\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Toutes vos donn\\xE9es sont sauvegard\\xE9es en local dans votre navigateur.\",\n    center_heading_plus: \"Vouliez-vous plut\\xF4t aller \\xE0 Excalidraw+\\xA0\\xE0 la place ?\",\n    menuHint: \"Exportation, pr\\xE9f\\xE9rences, langues, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportation, pr\\xE9f\\xE9rences et plus...\",\n    center_heading: \"Diagrammes. Rendus. Simples.\",\n    toolbarHint: \"Choisissez un outil et commencez \\xE0 dessiner\\xA0!\",\n    helpHint: \"Raccourcis et aide\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Couleurs personnalis\\xE9es les plus fr\\xE9quemment utilis\\xE9es\",\n  colors: \"Couleurs\",\n  shades: \"Nuances\",\n  hexCode: \"Code hex\",\n  noShades: \"Aucune nuance disponible pour cette couleur\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exporter en image\",\n      button: \"Exporter en image\",\n      description: \"Exporter les donn\\xE9es de la sc\\xE8ne comme une image que vous pourrez importer ult\\xE9rieurement.\"\n    },\n    saveToDisk: {\n      title: \"Sauvegarder sur le disque\",\n      button: \"Sauvegarder sur le disque\",\n      description: \"Exporter les donn\\xE9es de la sc\\xE8ne comme un fichier que vous pourrez importer ult\\xE9rieurement.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exporter vers Excalidraw+\",\n      description: \"Enregistrer la sc\\xE8ne dans votre espace de travail Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Charger depuis un fichier\",\n      button: \"Charger depuis un fichier\",\n      description: \"Charger depuis un fichier va <bold>remplacer votre contenu existant</bold>.<br></br>Vous pouvez d'abord sauvegarder votre dessin en utilisant l'une des options ci-dessous.\"\n    },\n    shareableLink: {\n      title: \"Charger depuis un lien\",\n      button: \"Remplacer mon contenu\",\n      description: \"Charger un dessin externe va <bold>remplacer votre contenu existant</bold>.<br></br>Vous pouvez d'abord sauvegarder votre dessin en utilisant l'une des options ci-dessous.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"De Mermaid \\xE0 Excalidraw\",\n  button: \"Ins\\xE9rer\",\n  description: \"\",\n  syntax: \"Syntaxe Mermaid\",\n  preview: \"Pr\\xE9visualisation\"\n};\nvar fr_FR_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=fr-FR-YE4VDJFI.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/fr-FR-YE4VDJFI.js\n"));

/***/ })

}]);