"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_browser-fs-access_dist_directory-open-01563666_js"],{

/***/ "(app-pages-browser)/./node_modules/browser-fs-access/dist/directory-open-01563666.js":
/*!************************************************************************!*\
  !*** ./node_modules/browser-fs-access/dist/directory-open-01563666.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\nvar e=async(e=[{}])=>(Array.isArray(e)||(e=[e]),e[0].recursive=e[0].recursive||!1,new Promise((t,r)=>{const i=document.createElement(\"input\");i.type=\"file\",i.webkitdirectory=!0;const c=e=>{\"function\"==typeof a&&a(),t(e)},a=e[0].legacySetup&&e[0].legacySetup(c,()=>a(r),i);i.addEventListener(\"change\",()=>{let t=Array.from(i.files);e[0].recursive?e[0].recursive&&e[0].skipDirectory&&(t=t.filter(t=>t.webkitRelativePath.split(\"/\").every(t=>!e[0].skipDirectory({name:t,kind:\"directory\"})))):t=t.filter(e=>2===e.webkitRelativePath.split(\"/\").length),c(t)}),i.click()}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2RpcmVjdG9yeS1vcGVuLTAxNTYzNjY2LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpQkFBaUIscUZBQXFGLHdDQUF3QyxtQ0FBbUMsWUFBWSwrQkFBK0Isb0RBQW9ELGlDQUFpQywwQkFBMEIsZ0lBQWdJLHdCQUF3QixvRUFBb0UsWUFBWSxHQUF3QiIsInNvdXJjZXMiOlsiL1VzZXJzL2p1c3Rpbm1lbmVzdHJpbmEvd29ya3NwYWNlL3N5c3RlbS1kZXNpZ24tbGxtL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2RpcmVjdG9yeS1vcGVuLTAxNTYzNjY2LmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBlPWFzeW5jKGU9W3t9XSk9PihBcnJheS5pc0FycmF5KGUpfHwoZT1bZV0pLGVbMF0ucmVjdXJzaXZlPWVbMF0ucmVjdXJzaXZlfHwhMSxuZXcgUHJvbWlzZSgodCxyKT0+e2NvbnN0IGk9ZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcImlucHV0XCIpO2kudHlwZT1cImZpbGVcIixpLndlYmtpdGRpcmVjdG9yeT0hMDtjb25zdCBjPWU9PntcImZ1bmN0aW9uXCI9PXR5cGVvZiBhJiZhKCksdChlKX0sYT1lWzBdLmxlZ2FjeVNldHVwJiZlWzBdLmxlZ2FjeVNldHVwKGMsKCk9PmEociksaSk7aS5hZGRFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsKCk9PntsZXQgdD1BcnJheS5mcm9tKGkuZmlsZXMpO2VbMF0ucmVjdXJzaXZlP2VbMF0ucmVjdXJzaXZlJiZlWzBdLnNraXBEaXJlY3RvcnkmJih0PXQuZmlsdGVyKHQ9PnQud2Via2l0UmVsYXRpdmVQYXRoLnNwbGl0KFwiL1wiKS5ldmVyeSh0PT4hZVswXS5za2lwRGlyZWN0b3J5KHtuYW1lOnQsa2luZDpcImRpcmVjdG9yeVwifSkpKSk6dD10LmZpbHRlcihlPT4yPT09ZS53ZWJraXRSZWxhdGl2ZVBhdGguc3BsaXQoXCIvXCIpLmxlbmd0aCksYyh0KX0pLGkuY2xpY2soKX0pKTtleHBvcnR7ZSBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/browser-fs-access/dist/directory-open-01563666.js\n"));

/***/ })

}]);