"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_da-DK-N76F4QAJ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/da-DK-N76F4QAJ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/da-DK-N76F4QAJ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ da_DK_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/da-DK.json\nvar labels = {\n  paste: \"Inds\\xE6t\",\n  pasteAsPlaintext: \"Inds\\xE6t som klartekst\",\n  pasteCharts: \"Inds\\xE6t diagrammer\",\n  selectAll: \"Marker alle\",\n  multiSelect: \"Tilf\\xF8j element til markering\",\n  moveCanvas: \"Flyt l\\xE6rred\",\n  cut: \"Klip\",\n  copy: \"Kopier\",\n  copyAsPng: \"Kopier til klippebord som PNG\",\n  copyAsSvg: \"Kopier til klippebord som SVG\",\n  copyText: \"Kopi\\xE9r til udklipsholder som tekst\",\n  copySource: \"Kopi\\xE9r kilde til udklipsholder\",\n  convertToCode: \"Konvert\\xE9r til kode\",\n  bringForward: \"Flyt fremad\",\n  sendToBack: \"Placer bagest\",\n  bringToFront: \"Placer forrest\",\n  sendBackward: \"Send bagud\",\n  delete: \"Fjern\",\n  copyStyles: \"Kopier stil\",\n  pasteStyles: \"Inds\\xE6t stil\",\n  stroke: \"Linje\",\n  background: \"Baggrund\",\n  fill: \"Udfyld\",\n  strokeWidth: \"Linjebredde\",\n  strokeStyle: \"Linjeform\",\n  strokeStyle_solid: \"Solid\",\n  strokeStyle_dashed: \"Stiplet\",\n  strokeStyle_dotted: \"Prikket\",\n  sloppiness: \"Sjuskethed\",\n  opacity: \"Gennemsigtighed\",\n  textAlign: \"Tekstjustering\",\n  edges: \"Kanter\",\n  sharp: \"Skarp\",\n  round: \"Rund\",\n  arrowheads: \"Pilehoveder\",\n  arrowhead_none: \"Ingen\",\n  arrowhead_arrow: \"Pil\",\n  arrowhead_bar: \"Bj\\xE6lke\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Trekant\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Skriftst\\xF8rrelse\",\n  fontFamily: \"Skrifttypefamilie\",\n  addWatermark: 'Tilf\\xF8j \"Lavet med Excalidraw\"',\n  handDrawn: \"H\\xE5nd-tegnet\",\n  normal: \"Normal\",\n  code: \"Kode\",\n  small: \"Lille\",\n  medium: \"Mellem\",\n  large: \"Stor\",\n  veryLarge: \"Meget stor\",\n  solid: \"Solid\",\n  hachure: \"Skravering\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Krydsskravering\",\n  thin: \"Tynd\",\n  bold: \"Fed\",\n  left: \"Venstre\",\n  center: \"Centrere\",\n  right: \"H\\xF8jre\",\n  extraBold: \"Extra fed\",\n  architect: \"Arkitekt\",\n  artist: \"Kunstner\",\n  cartoonist: \"Tegneserietegner\",\n  fileTitle: \"Filnavn\",\n  colorPicker: \"Farvev\\xE6lger\",\n  canvasColors: \"Brugt p\\xE5 l\\xE6rred\",\n  canvasBackground: \"L\\xE6rredsbaggrund\",\n  drawingCanvas: \"Tegnel\\xE6rred\",\n  layers: \"Lag\",\n  actions: \"Handlinger\",\n  language: \"Sprog\",\n  liveCollaboration: \"Live samarbejde...\",\n  duplicateSelection: \"Duplik\\xE9r\",\n  untitled: \"Unavngivet\",\n  name: \"Navn\",\n  yourName: \"Dit navn\",\n  madeWithExcalidraw: \"Fremstillet med Excalidraw\",\n  group: \"Grupper valgte\",\n  ungroup: \"Opl\\xF8s gruppe\",\n  collaborators: \"Deltagere\",\n  showGrid: \"Vis gitter\",\n  addToLibrary: \"F\\xF8j til Bibliotek\",\n  removeFromLibrary: \"Fjern fra biblioteket\",\n  libraryLoadingMessage: \"Indl\\xE6ser bibliotek\\u2026\",\n  libraries: \"Gennemse biblioteker\",\n  loadingScene: \"Indl\\xE6ser scene\\u2026\",\n  align: \"Just\\xE9r\",\n  alignTop: \"Juster til top\",\n  alignBottom: \"Juster til bund\",\n  alignLeft: \"Venstrejusteret\",\n  alignRight: \"Juster h\\xF8jre\",\n  centerVertically: \"Center vertikalt\",\n  centerHorizontally: \"Vandret centreret\",\n  distributeHorizontally: \"Distribuer vandret\",\n  distributeVertically: \"Distribuer lodret\",\n  flipHorizontal: \"Spejlvend horisontalt\",\n  flipVertical: \"Vend lodret\",\n  viewMode: \"Visningstilstand\",\n  share: \"Del\",\n  showStroke: \"Vis stregfarve-v\\xE6lger\",\n  showBackground: \"Vis baggrundsfarve-v\\xE6lger\",\n  toggleTheme: \"Skift tema\",\n  personalLib: \"Personligt bibliotek\",\n  excalidrawLib: \"Excalidraw Bibliotek\",\n  decreaseFontSize: \"G\\xF8r skriften mindre\",\n  increaseFontSize: \"G\\xF8r skriften st\\xF8rre\",\n  unbindText: \"Frig\\xF8r tekst\",\n  bindText: \"Bind tekst til beholderen\",\n  createContainerFromText: \"Ombryd tekst i en beholder\",\n  link: {\n    edit: \"Redig\\xE9r link\",\n    editEmbed: \"Redig\\xE9r link & indlejret\",\n    create: \"Link oprettet\",\n    createEmbed: \"Opret link & indlejret\",\n    label: \"Links\",\n    labelEmbed: \"Link & indlejret\",\n    empty: \"Intet link angivet\"\n  },\n  lineEditor: {\n    edit: \"Rediger Linje\",\n    exit: \"Afslut linjeeditor\"\n  },\n  elementLock: {\n    lock: \"L\\xE5s\",\n    unlock: \"L\\xE5s op\",\n    lockAll: \"L\\xE5s alle\",\n    unlockAll: \"L\\xE5s alle op\"\n  },\n  statusPublished: \"Udgiver\",\n  sidebarLock: \"Hold sidepanel \\xE5ben\",\n  selectAllElementsInFrame: \"V\\xE6lg alle elementer i rammen\",\n  removeAllElementsFromFrame: \"Fjern alle elementer fra ramme\",\n  eyeDropper: \"V\\xE6lg farve fra l\\xE6rred\",\n  textToDiagram: \"Tekst til diagram\",\n  prompt: \"Prompt\"\n};\nvar library = {\n  noItems: \"Ingen varer tilf\\xF8jet endnu...\",\n  hint_emptyLibrary: \"V\\xE6lg et element p\\xE5 l\\xE6rred for at tilf\\xF8je det her, eller installer et bibliotek fra det offentlige arkiv, nedenfor.\",\n  hint_emptyPrivateLibrary: \"V\\xE6lg et element p\\xE5 l\\xE6rred for at tilf\\xF8je det her.\"\n};\nvar buttons = {\n  clearReset: \"Nulstil l\\xE6rredet\",\n  exportJSON: \"Eksport\\xE9r til fil\",\n  exportImage: \"Eksporter billede...\",\n  export: \"Gem til...\",\n  copyToClipboard: \"Kopier til klippebord\",\n  save: \"Gem til nuv\\xE6rende fil\",\n  saveAs: \"Gem som\",\n  load: \"\\xC5bn\",\n  getShareableLink: \"Lav et delbart link\",\n  close: \"Luk\",\n  selectLanguage: \"V\\xE6lg sprog\",\n  scrollBackToContent: \"Scroll tilbage til indhold\",\n  zoomIn: \"Zoom ind\",\n  zoomOut: \"Zoom ud\",\n  resetZoom: \"Nulstil zoom\",\n  menu: \"Menu\",\n  done: \"F\\xE6rdig\",\n  edit: \"Rediger\",\n  undo: \"Fortryd\",\n  redo: \"Gendan\",\n  resetLibrary: \"Nulstil bibliotek\",\n  createNewRoom: \"Opret nyt rum\",\n  fullScreen: \"Fuld sk\\xE6rm\",\n  darkMode: \"M\\xF8rk tilstand\",\n  lightMode: \"Lys baggrund\",\n  zenMode: \"Zentilstand\",\n  objectsSnapMode: \"Fastg\\xF8r til objekter\",\n  exitZenMode: \"Stop zentilstand\",\n  cancel: \"Annuller\",\n  clear: \"Ryd\",\n  remove: \"Fjern\",\n  embed: \"Sl\\xE5 indlejring til/fra\",\n  publishLibrary: \"Public\\xE9r\",\n  submit: \"Gem\",\n  confirm: \"Bekr\\xE6ft\",\n  embeddableInteractionButton: \"Klik for at interagere\"\n};\nvar alerts = {\n  clearReset: \"Dette vil rydde hele l\\xE6rredet. Er du sikker?\",\n  couldNotCreateShareableLink: \"Kunne ikke oprette delbart link.\",\n  couldNotCreateShareableLinkTooBig: \"Kunne ikke oprette delbart link: scenen er for stor\",\n  couldNotLoadInvalidFile: \"Kunne ikke indl\\xE6se ugyldig fil\",\n  importBackendFailed: \"Import fra backend mislykkedes.\",\n  cannotExportEmptyCanvas: \"Kan ikke eksportere tomt l\\xE6rred.\",\n  couldNotCopyToClipboard: \"Kunne ikke kopiere til udklipsholderen.\",\n  decryptFailed: \"Kunne ikke dekryptere data.\",\n  uploadedSecurly: \"Upload er blevet sikret med ende-til-ende kryptering, hvilket betyder, at Excalidraw server og tredjeparter ikke kan l\\xE6se indholdet.\",\n  loadSceneOverridePrompt: \"Indl\\xE6sning af ekstern tegning erstatter dit eksisterende indhold. \\xD8nsker du at forts\\xE6tte?\",\n  collabStopOverridePrompt: \"Stopper sessionen vil overskrive din tidligere, lokalt gemte tegning. Er du sikker?\\n\\n(Hvis du \\xF8nsker at beholde din lokale tegning, skal du blot lukke browserfanen i stedet.)\",\n  errorAddingToLibrary: \"Kunne ikke tilf\\xF8je element til biblioteket\",\n  errorRemovingFromLibrary: \"Kunne ikke fjerne element fra biblioteket\",\n  confirmAddLibrary: \"Dette vil tilf\\xF8je {{numShapes}} form(er) til dit bibliotek. Er du sikker?\",\n  imageDoesNotContainScene: \"Dette billede synes ikke at indeholde scene data. Har du aktiveret scene indlejring under eksport?\",\n  cannotRestoreFromImage: \"Scene kunne ikke gendannes fra denne billedfil\",\n  invalidSceneUrl: \"Kunne ikke importere scene fra den angivne URL. Det er enten misdannet eller indeholder ikke gyldige Excalidraw JSON data.\",\n  resetLibrary: \"Dette vil rydde hele l\\xE6rredet. Er du sikker?\",\n  removeItemsFromsLibrary: \"Slet {{count}} vare(r) fra biblioteket?\",\n  invalidEncryptionKey: \"Krypteringsn\\xF8glen skal v\\xE6re p\\xE5 22 tegn. Live-samarbejde er deaktiveret.\",\n  collabOfflineWarning: \"Ingen internetforbindelse tilg\\xE6ngelig.\\nDine \\xE6ndringer vil ikke blive gemt!\"\n};\nvar errors = {\n  unsupportedFileType: \"Filtypen er ikke underst\\xF8ttet.\",\n  imageInsertError: \"Billedet kunne ikke inds\\xE6ttes. Pr\\xF8v igen senere...\",\n  fileTooBig: \"Filen er for stor. Maksimal tilladt st\\xF8rrelse er {{maxSize}}.\",\n  svgImageInsertError: \"Kunne ikke inds\\xE6tte SVG-billede. SVG-markup'en ser ugyldig ud.\",\n  failedToFetchImage: \"Dataene blev ikke hentet.\",\n  invalidSVGString: \"Ugyldig SVG.\",\n  cannotResolveCollabServer: \"Kunne ikke oprette forbindelse til samarbejdsserveren. Genindl\\xE6s siden og pr\\xF8v igen.\",\n  importLibraryError: \"Biblioteket kunne ikke indl\\xE6ses\",\n  collabSaveFailed: \"Kunne ikke gemme i databasen. Hvis problemerne forts\\xE6tter, b\\xF8r du gemme din fil lokalt for at sikre, at du ikke mister dit arbejde.\",\n  collabSaveFailed_sizeExceeded: \"Kunne ikke gemme i databasen, l\\xE6rredet lader til at v\\xE6re for stort. Du b\\xF8r gemme filen lokalt for at sikre, at du ikke mister dit arbejde.\",\n  imageToolNotSupported: \"Billeder er deaktiveret.\",\n  brave_measure_text_error: {\n    line1: \"Det ser ud til, at du bruger Brave browser med indstillingen <bold>Aggressively Block Fingerprinting</bold> aktiveret.\",\n    line2: \"Dette kan resultere i brud p\\xE5 <bold>tekstelementerne</bold> i dine tegninger.\",\n    line3: \"Vi anbefaler kraftigt at deaktivere denne indstilling. Du kan f\\xF8lge <link>disse trin</link> om, hvordan du g\\xF8r det.\",\n    line4: \"Hvis deaktivering af denne indstilling ikke l\\xF8ser visning af tekstelementer, \\xE5bn venligst et <issueLink>issue</issueLink> p\\xE5 vores GitHub, eller skriv os p\\xE5 <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Indlejringselementer kan ikke tilf\\xF8jes til biblioteket.\",\n    iframe: \"IFrame elementer kan ikke tilf\\xF8jes til biblioteket.\",\n    image: \"Underst\\xF8ttelse af at tilf\\xF8je billeder til biblioteket kommer snart!\"\n  },\n  asyncPasteFailedOnRead: \"Kunne ikke inds\\xE6tte (kan ikke l\\xE6se fra systemets udklipsholder).\",\n  asyncPasteFailedOnParse: \"Kunne ikke inds\\xE6tte.\",\n  copyToSystemClipboardFailed: \"Kunne ikke kopiere til udklipsholderen.\"\n};\nvar toolBar = {\n  selection: \"&Udvalg\",\n  image: \"Inds\\xE6t billeder\",\n  rectangle: \"Rektangler\",\n  diamond: \"Diamanter\",\n  ellipse: \"Ellipser\",\n  arrow: \"Pile\",\n  line: \"Linje\",\n  freedraw: \"Tegn\",\n  text: \"Tekster\",\n  library: \"~Bibliotek\",\n  lock: \"Behold valgte v\\xE6rkt\\xF8j aktiv efter tegning\",\n  penMode: \"Pen-tilstand - forhindrer ber\\xF8ring\",\n  link: \"Tilf\\xF8j/ Opdater link for en valgt form\",\n  eraser: \"Slet\",\n  frame: \"Rammev\\xE6rkt\\xF8j\",\n  magicframe: \"Wireframe til kode\",\n  embeddable: \"Web-indlejring\",\n  laser: \"Lasermark\\xF8r\",\n  hand: \"H\\xE5nd (panorering v\\xE6rkt\\xF8j)\",\n  extraTools: \"Flere v\\xE6rkt\\xF8jer\",\n  mermaidToExcalidraw: \"Mermaid til Excalidraw\",\n  magicSettings: \"AI indstillinger\"\n};\nvar headings = {\n  canvasActions: \"L\\xE6rred handlinger\",\n  selectedShapeActions: \"Valgte figurhandlinger\",\n  shapes: \"Former\"\n};\nvar hints = {\n  canvasPanning: \"For at flytte l\\xE6rred, hold musehjulet eller mellemrumstasten mens du tr\\xE6kker, eller brug h\\xE5ndv\\xE6rkt\\xF8jet\",\n  linearElement: \"Klik for at starte flere punkter, tr\\xE6k for enkelt linje\",\n  freeDraw: \"Klik og tr\\xE6k, slip n\\xE5r du er f\\xE6rdig\",\n  text: \"Tip: du kan ogs\\xE5 tilf\\xF8je tekst ved at dobbeltklikke hvor som helst med det valgte v\\xE6rkt\\xF8j\",\n  embeddable: \"Klik p\\xE5 tr\\xE6k for at oprette en hjemmeside indlejret\",\n  text_selected: \"\",\n  text_editing: \"\",\n  linearElementMulti: \"\",\n  lockAngle: \"\",\n  resize: \"\",\n  resizeImage: \"\",\n  rotate: \"\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Kan ikke vise forh\\xE5ndsvisning\",\n  canvasTooBig: \"L\\xE6rredet kan v\\xE6re for stort.\",\n  canvasTooBigTip: \"Tip: Pr\\xF8v at flytte de fjerneste elementer lidt t\\xE6ttere sammen.\"\n};\nvar errorSplash = {\n  headingMain: \"Der opstod en fejl. Pr\\xF8v <button>at genindl\\xE6se siden</button>.\",\n  clearCanvasMessage: \"\",\n  clearCanvasCaveat: \"\",\n  trackedToSentry: \"\",\n  openIssueMessage: \"<button></button> Kopiere og inds\\xE6t venligst oplysningerne nedenfor i et GitHub problem.\",\n  sceneContent: \"Scene indhold:\"\n};\nvar roomDialog = {\n  desc_intro: \"Du kan invitere folk til din nuv\\xE6rende scene, s\\xE5 de kan samarbejde med dig.\",\n  desc_privacy: \"Bare rolig, sessionen bruger end-to-end kryptering, s\\xE5 uanset hvad du tegner vil det forblive privat. Ikke engang vores server vil kunne se, hvad du kommer op med.\",\n  button_startSession: \"Start session\",\n  button_stopSession: \"Stop session\",\n  desc_inProgressIntro: \"Live-samarbejde session er nu begyndt.\",\n  desc_shareLink: \"Del dette link med enhver, du \\xF8nsker at samarbejde med:\",\n  desc_exitSession: \"\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"Fejl\"\n};\nvar exportDialog = {\n  disk_title: \"Gem til disk\",\n  disk_details: \"\",\n  disk_button: \"\",\n  link_title: \"\",\n  link_details: \"\",\n  link_button: \"\",\n  excalidrawplus_description: \"\",\n  excalidrawplus_button: \"\",\n  excalidrawplus_exportError: \"\"\n};\nvar helpDialog = {\n  blog: \"L\\xE6s vores blog\",\n  click: \"\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"\",\n  curvedLine: \"\",\n  documentation: \"\",\n  doubleClick: \"\",\n  drag: \"\",\n  editor: \"\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"\",\n  howto: \"\",\n  or: \"\",\n  preventBinding: \"\",\n  tools: \"\",\n  shortcuts: \"\",\n  textFinish: \"\",\n  textNewLine: \"\",\n  title: \"\",\n  view: \"\",\n  zoomToFit: \"\",\n  zoomToSelection: \"\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\"\n};\nvar clearCanvasDialog = {\n  title: \"\"\n};\nvar publishDialog = {\n  title: \"\",\n  itemName: \"\",\n  authorName: \"\",\n  githubUsername: \"\",\n  twitterUsername: \"\",\n  libraryName: \"\",\n  libraryDesc: \"\",\n  website: \"\",\n  placeholder: {\n    authorName: \"\",\n    libraryName: \"\",\n    libraryDesc: \"\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"\",\n    website: \"\"\n  },\n  noteDescription: \"\",\n  noteGuidelines: \"\",\n  noteLicense: \"\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"\",\n  content: \"\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\",\n  removeItemsFromLib: \"\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\",\n  link: \"\"\n};\nvar stats = {\n  angle: \"\",\n  element: \"\",\n  elements: \"\",\n  height: \"\",\n  scene: \"\",\n  selected: \"\",\n  storage: \"\",\n  title: \"Statistik for n\\xF8rder\",\n  total: \"\",\n  version: \"\",\n  versionCopy: \"Klik for at kopiere\",\n  versionNotAvailable: \"\",\n  width: \"Bredde\"\n};\nvar toast = {\n  addedToLibrary: \"\",\n  copyStyles: \"Kopieret stilarter.\",\n  copyToClipboard: \"Kopieret til klippebord.\",\n  copyToClipboardAsPng: \"Kopieret {{exportSelection}} til klippebord som PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Fil gemt.\",\n  fileSavedToFilename: \"Gemt som {filename}\",\n  canvas: \"canvas\",\n  selection: \"markering\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar da_DK_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=da-DK-N76F4QAJ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/da-DK-N76F4QAJ.js\n"));

/***/ })

}]);