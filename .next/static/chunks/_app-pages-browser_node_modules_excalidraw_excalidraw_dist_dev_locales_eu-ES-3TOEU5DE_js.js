"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_eu-ES-3TOEU5DE_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/eu-ES-3TOEU5DE.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/eu-ES-3TOEU5DE.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ eu_ES_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/eu-ES.json\nvar labels = {\n  paste: \"Itsatsi\",\n  pasteAsPlaintext: \"Itsatsi testu arrunt gisa\",\n  pasteCharts: \"Itsatsi grafikoak\",\n  selectAll: \"Hautatu dena\",\n  multiSelect: \"Gehitu elementua hautapenera\",\n  moveCanvas: \"Mugitu oihala\",\n  cut: \"Ebaki\",\n  copy: \"Kopiatu\",\n  copyAsPng: \"Kopiatu arbelera PNG gisa\",\n  copyAsSvg: \"Kopiatu arbelera SVG gisa\",\n  copyText: \"Kopiatu arbelera testu gisa\",\n  copySource: \"Kopiatu iturria arbelean\",\n  convertToCode: \"Bihurtu kodea\",\n  bringForward: \"Ekarri aurrerago\",\n  sendToBack: \"Eraman atzera\",\n  bringToFront: \"Ekarri aurrera\",\n  sendBackward: \"Eraman atzerago\",\n  delete: \"Ezabatu\",\n  copyStyles: \"Kopiatu estiloak\",\n  pasteStyles: \"Itsatsi estiloak\",\n  stroke: \"Marra\",\n  background: \"Atzeko planoa\",\n  fill: \"Bete\",\n  strokeWidth: \"Marraren zabalera\",\n  strokeStyle: \"Marraren estiloa\",\n  strokeStyle_solid: \"Solidoa\",\n  strokeStyle_dashed: \"Marratua\",\n  strokeStyle_dotted: \"Puntukatua\",\n  sloppiness: \"Marraren trazoa\",\n  opacity: \"Opakotasuna\",\n  textAlign: \"Testuaren lerrokapena\",\n  edges: \"Ertzak\",\n  sharp: \"Ertz bizia\",\n  round: \"Borobildua\",\n  arrowheads: \"Gezi-puntak\",\n  arrowhead_none: \"Bat ere ez\",\n  arrowhead_arrow: \"Gezia\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Hirukia\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Letra-tamaina\",\n  fontFamily: \"Letra-tipoa\",\n  addWatermark: 'Gehitu \"Excalidraw bidez egina\"',\n  handDrawn: \"Eskuz marraztua\",\n  normal: \"Normala\",\n  code: \"Kodea\",\n  small: \"Txikia\",\n  medium: \"Ertaina\",\n  large: \"Handia\",\n  veryLarge: \"Oso handia\",\n  solid: \"Solidoa\",\n  hachure: \"Itzalduna\",\n  zigzag: \"Sigi-saga\",\n  crossHatch: \"Marraduna\",\n  thin: \"Mehea\",\n  bold: \"Lodia\",\n  left: \"Ezkerrean\",\n  center: \"Erdian\",\n  right: \"Eskuinean\",\n  extraBold: \"Oso lodia\",\n  architect: \"Arkitektoa\",\n  artist: \"Artista\",\n  cartoonist: \"Marrazkilaria\",\n  fileTitle: \"Fitxategi izena\",\n  colorPicker: \"Kolore-hautatzailea\",\n  canvasColors: \"Oihalean erabilita\",\n  canvasBackground: \"Oihalaren atzeko planoa\",\n  drawingCanvas: \"Marrazteko oihala\",\n  layers: \"Geruzak\",\n  actions: \"Ekintzak\",\n  language: \"Hizkuntza\",\n  liveCollaboration: \"Zuzeneko elkarlana...\",\n  duplicateSelection: \"Bikoiztu\",\n  untitled: \"Izengabea\",\n  name: \"Izena\",\n  yourName: \"Zure izena\",\n  madeWithExcalidraw: \"Excalidraw bidez egina\",\n  group: \"Hautapena taldea bihurtu\",\n  ungroup: \"Desegin hautapenaren taldea\",\n  collaborators: \"Kolaboratzaileak\",\n  showGrid: \"Erakutsi sareta\",\n  addToLibrary: \"Gehitu liburutegira\",\n  removeFromLibrary: \"Kendu liburutegitik\",\n  libraryLoadingMessage: \"Liburutegia kargatzen\\u2026\",\n  libraries: \"Arakatu liburutegiak\",\n  loadingScene: \"Eszena kargatzen\\u2026\",\n  align: \"Lerrokatu\",\n  alignTop: \"Lerrokatu goian\",\n  alignBottom: \"Lerrokatu behean\",\n  alignLeft: \"Lerrokatu ezkerrean\",\n  alignRight: \"Lerrokatu eskuinean\",\n  centerVertically: \"Erdiratu bertikalki\",\n  centerHorizontally: \"Erdiratu horizontalki\",\n  distributeHorizontally: \"Banandu horizontalki\",\n  distributeVertically: \"Banandu bertikalki\",\n  flipHorizontal: \"Irauli horizontalki\",\n  flipVertical: \"Irauli bertikalki\",\n  viewMode: \"Ikuspegia\",\n  share: \"Partekatu\",\n  showStroke: \"Erakutsi marraren kolore-hautatzailea\",\n  showBackground: \"Erakutsi atzeko planoaren kolore-hautatzailea\",\n  toggleTheme: \"Aldatu gaia\",\n  personalLib: \"Liburutegi pertsonala\",\n  excalidrawLib: \"Excalidraw liburutegia\",\n  decreaseFontSize: \"Txikitu letra tamaina\",\n  increaseFontSize: \"Handitu letra tamaina\",\n  unbindText: \"Askatu testua\",\n  bindText: \"Lotu testua edukiontziari\",\n  createContainerFromText: \"Bilatu testua edukiontzi batean\",\n  link: {\n    edit: \"Editatu esteka\",\n    editEmbed: \"Editatu esteka eta kapsulatu\",\n    create: \"Sortu esteka\",\n    createEmbed: \"Sortu esteka eta kapsulatu\",\n    label: \"Esteka\",\n    labelEmbed: \"Esteka eta kapsula\",\n    empty: \"Ez da estekarik ezarri\"\n  },\n  lineEditor: {\n    edit: \"Editatu lerroa\",\n    exit: \"Irten lerro-editoretik\"\n  },\n  elementLock: {\n    lock: \"Blokeatu\",\n    unlock: \"Desblokeatu\",\n    lockAll: \"Blokeatu guztiak\",\n    unlockAll: \"Desblokeatu guztiak\"\n  },\n  statusPublished: \"Argitaratua\",\n  sidebarLock: \"Mantendu alboko barra irekita\",\n  selectAllElementsInFrame: \"Hautatu markoko elementu guztiak\",\n  removeAllElementsFromFrame: \"Kendu markoko elementu guztiak\",\n  eyeDropper: \"Aukeratu kolorea oihaletik\",\n  textToDiagram: \"Testutik diagramara\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Oraindik ez da elementurik gehitu...\",\n  hint_emptyLibrary: \"Hautatu oihaleko elementu bat hemen gehitzeko, edo instalatu liburutegi bat beheko biltegi publikotik.\",\n  hint_emptyPrivateLibrary: \"Hautatu oihaleko elementu bat hemen gehitzeko.\"\n};\nvar buttons = {\n  clearReset: \"Garbitu oihala\",\n  exportJSON: \"Esportatu fitxategira\",\n  exportImage: \"Esportatu irudia...\",\n  export: \"Gorde hemen...\",\n  copyToClipboard: \"Kopiatu arbelera\",\n  save: \"Gorde uneko fitxategian\",\n  saveAs: \"Gorde honela\",\n  load: \"Ireki\",\n  getShareableLink: \"Lortu partekatzeko esteka\",\n  close: \"Itxi\",\n  selectLanguage: \"Hautatu hizkuntza\",\n  scrollBackToContent: \"Joan atzera edukira\",\n  zoomIn: \"Handiagotu\",\n  zoomOut: \"Txikiagotu\",\n  resetZoom: \"Leheneratu zooma\",\n  menu: \"Menua\",\n  done: \"Egina\",\n  edit: \"Editatu\",\n  undo: \"Desegin\",\n  redo: \"Berregin\",\n  resetLibrary: \"Leheneratu liburutegia\",\n  createNewRoom: \"Sortu gela berria\",\n  fullScreen: \"Pantaila osoa\",\n  darkMode: \"Modu iluna\",\n  lightMode: \"Modu argia\",\n  zenMode: \"Zen modua\",\n  objectsSnapMode: \"Atxiki objektuei\",\n  exitZenMode: \"Irten Zen modutik\",\n  cancel: \"Utzi\",\n  clear: \"Garbitu\",\n  remove: \"Kendu\",\n  embed: \"Aldatu kapsulatzea\",\n  publishLibrary: \"Argitaratu\",\n  submit: \"Bidali\",\n  confirm: \"Bai\",\n  embeddableInteractionButton: \"Egin klik elkar eragiteko\"\n};\nvar alerts = {\n  clearReset: \"Honek oihal osoa garbituko du. Ziur zaude?\",\n  couldNotCreateShareableLink: \"Ezin izan da partekatzeko estekarik sortu.\",\n  couldNotCreateShareableLinkTooBig: \"Ezin izan da partekatzeko estekarik sortu: eszena handiegia da\",\n  couldNotLoadInvalidFile: \"Ezin izan da kargatu, fitxategiak ez du balio\",\n  importBackendFailed: \"Inportazioak huts egin du.\",\n  cannotExportEmptyCanvas: \"Ezin izan da oihal hutsa esportatu.\",\n  couldNotCopyToClipboard: \"Ezin izan da arbelean kopiatu.\",\n  decryptFailed: \"Ezin izan da deszifratu.\",\n  uploadedSecurly: \"Kargatzea muturretik muturrerako zifratze bidez ziurtatu da, hau da, Excalidraw zerbitzariak eta hirugarrenek ezin dutela edukia irakurri.\",\n  loadSceneOverridePrompt: \"Kanpoko marrazkia kargatzeak lehendik duzun edukia ordezkatuko du. Jarraitu nahi duzu?\",\n  collabStopOverridePrompt: \"Saioa gelditzeak lokalean gordetako zure aurreko marrazkia gainidatziko du. Ziur zaude?\\n\\n(Zure marrazki lokala mantendu nahi baduzu, itxi arakatzailearen fitxa.)\",\n  errorAddingToLibrary: \"Ezin izan da elementua liburutegian gehitu\",\n  errorRemovingFromLibrary: \"Ezin izan da elementua liburutegitik kendu\",\n  confirmAddLibrary: \"Honek {{numShapes}} forma gehituko ditu zure liburutegian. Ziur zaude?\",\n  imageDoesNotContainScene: \"Irudi honek ez dirudi eszena daturik duenik. Eszena kapsulatzea gaitu al duzu esportazioan?\",\n  cannotRestoreFromImage: \"Ezin izan da eszena leheneratu irudi fitxategi honetatik\",\n  invalidSceneUrl: \"Ezin izan da eszena inportatu emandako URLtik. Gaizki eratuta dago edo ez du baliozko Excalidraw JSON daturik.\",\n  resetLibrary: \"Honek zure liburutegia garbituko du. Ziur zaude?\",\n  removeItemsFromsLibrary: \"Liburutegitik {{count}} elementu ezabatu?\",\n  invalidEncryptionKey: \"Enkriptazio-gakoak 22 karaktere izan behar ditu. Zuzeneko lankidetza desgaituta dago.\",\n  collabOfflineWarning: \"Ez dago Interneteko konexiorik.\\nZure aldaketak ez dira gordeko!\"\n};\nvar errors = {\n  unsupportedFileType: \"Onartu gabeko fitxategi mota.\",\n  imageInsertError: \"Ezin izan da irudia txertatu. Saiatu berriro geroago...\",\n  fileTooBig: \"Fitxategia handiegia da. Onartutako gehienezko tamaina {{maxSize}} da.\",\n  svgImageInsertError: \"Ezin izan da SVG irudia txertatu. SVG markak baliogabea dirudi.\",\n  failedToFetchImage: \"Ezin izan da irudia eskuratu.\",\n  invalidSVGString: \"SVG baliogabea.\",\n  cannotResolveCollabServer: \"Ezin izan da elkarlaneko zerbitzarira konektatu. Mesedez, berriro kargatu orria eta saiatu berriro.\",\n  importLibraryError: \"Ezin izan da liburutegia kargatu\",\n  collabSaveFailed: \"Ezin izan da backend datu-basean gorde. Arazoak jarraitzen badu, zure fitxategia lokalean gorde beharko zenuke zure lana ez duzula galtzen ziurtatzeko.\",\n  collabSaveFailed_sizeExceeded: \"Ezin izan da backend datu-basean gorde, ohiala handiegia dela dirudi. Fitxategia lokalean gorde beharko zenuke zure lana galtzen ez duzula ziurtatzeko.\",\n  imageToolNotSupported: \"Irudiak desgaituta daude.\",\n  brave_measure_text_error: {\n    line1: \"Brave arakatzailea erabiltzen ari zarela dirudi <bold>Blokeatu hatz-markak erasokorki</bold> ezarpena gaituta.\",\n    line2: \"Honek zure marrazkietako <bold>Testu-elementuak</bold> hautsi ditzake.\",\n    line3: \"Ezarpen hau desgaitzea gomendatzen dugu. <link>urrats hauek</link> jarrai ditzakezu hori nola egin jakiteko.\",\n    line4: \"Ezarpen hau desgaituz gero, testu-elementuen bistaratzea konpontzen ez bada, ireki <issueLink>arazo</issueLink> gure GitHub-en edo idatzi iezaguzu <discordLink>Discord</discordLink> helbidera\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Kapsulatutako elementuak ezin dira liburutegira gehitu.\",\n    iframe: \"IFrame elementuak ezin dira liburutegira gehitu.\",\n    image: \"Laster egongo da irudiak liburutegian gehitzeko laguntza!\"\n  },\n  asyncPasteFailedOnRead: \"Ezin izan da itsatsi (ezin izan da sistemaren arbeletik irakurri).\",\n  asyncPasteFailedOnParse: \"Ezin izan da itsatsi.\",\n  copyToSystemClipboardFailed: \"Ezin izan da arbelean kopiatu.\"\n};\nvar toolBar = {\n  selection: \"Hautapena\",\n  image: \"Txertatu irudia\",\n  rectangle: \"Laukizuzena\",\n  diamond: \"Diamantea\",\n  ellipse: \"Elipsea\",\n  arrow: \"Gezia\",\n  line: \"Lerroa\",\n  freedraw: \"Marraztu\",\n  text: \"Testua\",\n  library: \"Liburutegia\",\n  lock: \"Mantendu aktibo hautatutako tresna marraztu ondoren\",\n  penMode: \"Luma modua - ukipena saihestu\",\n  link: \"Gehitu / Eguneratu esteka hautatutako forma baterako\",\n  eraser: \"Borragoma\",\n  frame: \"Marko tresna\",\n  magicframe: \"Wireframe kodetzeko\",\n  embeddable: \"Web kapsulatzea\",\n  laser: \"Laser punteroa\",\n  hand: \"Eskua (panoratze tresna)\",\n  extraTools: \"Tresna gehiago\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"AI ezarpenak\"\n};\nvar headings = {\n  canvasActions: \"Canvas ekintzak\",\n  selectedShapeActions: \"Hautatutako formaren ekintzak\",\n  shapes: \"Formak\"\n};\nvar hints = {\n  canvasPanning: \"Oihala mugitzeko, eutsi saguaren gurpila edo zuriune-barra arrastatzean, edo erabili esku tresna\",\n  linearElement: \"Egin klik hainbat puntu hasteko, arrastatu lerro bakarrerako\",\n  freeDraw: \"Egin klik eta arrastatu, askatu amaitutakoan\",\n  text: \"Aholkua: testua gehitu dezakezu edozein lekutan klik bikoitza eginez hautapen tresnarekin\",\n  embeddable: \"Egin klik eta arrastatu webgunea kapsulatzeko\",\n  text_selected: \"Egin klik bikoitza edo sakatu SARTU testua editatzeko\",\n  text_editing: \"Sakatu Esc edo Ctrl+SARTU editatzen amaitzeko\",\n  linearElementMulti: \"Egin klik azken puntuan edo sakatu Esc edo Sartu amaitzeko\",\n  lockAngle: \"SHIFT sakatuta angelua mantendu dezakezu\",\n  resize: \"Proportzioak mantendu ditzakezu SHIFT sakatuta tamaina aldatzen duzun bitartean.\\nsakatu ALT erditik tamaina aldatzeko\",\n  resizeImage: \"Tamaina libreki alda dezakezu SHIFT sakatuta,\\nsakatu ALT erditik tamaina aldatzeko\",\n  rotate: \"Angeluak mantendu ditzakezu SHIFT sakatuta biratzen duzun bitartean\",\n  lineEditor_info: \"Eutsi sakatuta Ctrl edo Cmd eta egin klik bikoitza edo sakatu Ctrl edo Cmd + Sartu puntuak editatzeko\",\n  lineEditor_pointSelected: \"Sakatu Ezabatu puntuak kentzeko,\\nKtrl+D bikoizteko, edo arrastatu mugitzeko\",\n  lineEditor_nothingSelected: \"Hautatu editatzeko puntu bat (SHIFT sakatuta anitz hautatzeko),\\nedo eduki Alt sakatuta eta egin klik puntu berriak gehitzeko\",\n  placeImage: \"Egin klik irudia kokatzeko, edo egin klik eta arrastatu bere tamaina eskuz ezartzeko\",\n  publishLibrary: \"Argitaratu zure liburutegia\",\n  bindTextToElement: \"Sakatu Sartu testua gehitzeko\",\n  deepBoxSelect: \"Eutsi Ctrl edo Cmd sakatuta aukeraketa sakona egiteko eta arrastatzea saihesteko\",\n  eraserRevert: \"Eduki Alt sakatuta ezabatzeko markatutako elementuak leheneratzeko\",\n  firefox_clipboard_write: 'Ezaugarri hau \"dom.events.asyncClipboard.clipboardItem\" marka \"true\" gisa ezarrita gaitu daiteke. Firefox-en arakatzailearen banderak aldatzeko, bisitatu \"about:config\" orrialdera.',\n  disableSnapping: \"Eduki sakatuta Ctrl edo Cmd tekla atxikipena desgaitzeko\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Ezin da oihala aurreikusi\",\n  canvasTooBig: \"Agian oihala handiegia da.\",\n  canvasTooBigTip: \"Aholkua: saiatu urrunen dauden elementuak pixka bat hurbiltzen.\"\n};\nvar errorSplash = {\n  headingMain: \"Errore bat aurkitu da. Saiatu <button>orria birkargatzen.</button>\",\n  clearCanvasMessage: \"Birkargatzea ez bada burutzen, saiatu <button>oihala garbitzen.</button>\",\n  clearCanvasCaveat: \" Honen ondorioz lana galduko da \",\n  trackedToSentry: \"Identifikatzailearen errorea {{eventId}} gure sistemak behatu du.\",\n  openIssueMessage: \"Oso kontuz ibili gara zure eszenaren informazioa errorean ez sartzeko. Zure eszena pribatua ez bada, kontuan hartu gure <button>erroreen jarraipena egitea.</button> Sartu beheko informazioa kopiatu eta itsatsi bidez GitHub issue-n.\",\n  sceneContent: \"Eszenaren edukia:\"\n};\nvar roomDialog = {\n  desc_intro: \"Jendea zure uneko eszenara gonbida dezakezu zurekin elkarlanean aritzeko.\",\n  desc_privacy: \"Ez kezkatu, saioak muturretik muturrerako enkriptatzea erabiltzen du, beraz, marrazten duzuna pribatua izango da. Gure zerbitzariak ere ezingo du ikusi zer egiten duzun.\",\n  button_startSession: \"Hasi saioa\",\n  button_stopSession: \"Itxi saioa\",\n  desc_inProgressIntro: \"Zuzeneko lankidetza saioa abian da.\",\n  desc_shareLink: \"Partekatu esteka hau elkarlanean aritu nahi duzun edonorekin:\",\n  desc_exitSession: \"Saioa ixteak aretotik deskonektatuko zaitu, baina eszenarekin lanean jarraitu ahal izango duzu lokalean. Kontuan izan honek ez diela beste pertsonei eragingo, eta euren bertsioan elkarlanean aritu ahal izango dira.\",\n  shareTitle: \"Sartu Excalidraw-en zuzeneko lankidetza-saio batean\"\n};\nvar errorDialog = {\n  title: \"Errorea\"\n};\nvar exportDialog = {\n  disk_title: \"Gorde diskoan\",\n  disk_details: \"Esportatu eszenaren datuak geroago inportatu ahal izango duzun fitxategi batan.\",\n  disk_button: \"Gorde fitxategian\",\n  link_title: \"Partekatzeko esteka\",\n  link_details: \"Esportatu irakurtzeko soilik moduko esteka.\",\n  link_button: \"Esportatu esteka\",\n  excalidrawplus_description: \"Gorde eszena zure Excalidraw+ laneko areara.\",\n  excalidrawplus_button: \"Esportatu\",\n  excalidrawplus_exportError: \"Une honetan ezin izan da esportatu Excalidraw+era...\"\n};\nvar helpDialog = {\n  blog: \"Irakurri gure bloga\",\n  click: \"sakatu\",\n  deepSelect: \"Hautapen sakona\",\n  deepBoxSelect: \"Hautapen sakona egin laukizuzen bidez, eta saihestu arrastatzea\",\n  curvedArrow: \"Gezi kurbatua\",\n  curvedLine: \"Lerro kurbatua\",\n  documentation: \"Dokumentazioa\",\n  doubleClick: \"klik bikoitza\",\n  drag: \"arrastatu\",\n  editor: \"Editorea\",\n  editLineArrowPoints: \"Editatu lerroak/gezi-puntuak\",\n  editText: \"Editatu testua / gehitu etiketa\",\n  github: \"Arazorik izan al duzu? Eman horren berri\",\n  howto: \"Jarraitu gure gidak\",\n  or: \"edo\",\n  preventBinding: \"Saihestu gezien gainjartzea\",\n  tools: \"Tresnak\",\n  shortcuts: \"Laster-teklak\",\n  textFinish: \"Bukatu edizioa (testu editorea)\",\n  textNewLine: \"Gehitu lerro berri bat (testu editorea)\",\n  title: \"Laguntza\",\n  view: \"Bistaratu\",\n  zoomToFit: \"Egin zoom elementu guztiak ikusteko\",\n  zoomToSelection: \"Zooma hautapenera\",\n  toggleElementLock: \"Blokeatu/desbloketatu hautapena\",\n  movePageUpDown: \"Mugitu orria gora/behera\",\n  movePageLeftRight: \"Mugitu orria ezker/eskuin\"\n};\nvar clearCanvasDialog = {\n  title: \"Garbitu oihala\"\n};\nvar publishDialog = {\n  title: \"Argitaratu liburutegia\",\n  itemName: \"Elementuaren izena\",\n  authorName: \"Egilearen izena\",\n  githubUsername: \"GitHub-eko erabiltzaile-izena\",\n  twitterUsername: \"Twitter-eko erabiltzaile-izena\",\n  libraryName: \"Liburutegiaren izena\",\n  libraryDesc: \"Liburutegiaren deskripzioa\",\n  website: \"Webgunea\",\n  placeholder: {\n    authorName: \"Zure izena edo erabiltzaile-izena\",\n    libraryName: \"Zure liburutegiaren izena\",\n    libraryDesc: \"Zure liburutegiaren deskripzioa laguntzeko jendeari ulertzen haren erabilpena\",\n    githubHandle: \"GitHub heldulekua (aukerakoa), liburutegia editatu ahal izateko berrikustera bidalitakoan\",\n    twitterHandle: \"Twitter-eko erabiltzaile-izena (aukerakoa), badakigu nori kreditatu behar dugun Twitter bidez sustatzeko\",\n    website: \"Estekatu zure webgunera edo nahi duzun tokira (aukerakoa)\"\n  },\n  errors: {\n    required: \"Beharrezkoa\",\n    website: \"Sartu baliozko URL bat\"\n  },\n  noteDescription: \"Bidali zure liburutegira sartu ahal izateko <link>zure liburutegiko biltegian</link>beste jendeak bere marrazkietan erabili ahal izateko.\",\n  noteGuidelines: \"Liburutegia eskuz onartu behar da. Irakurri <link>gidalerroak</link> bidali aurretik. GitHub kontu bat edukitzea komeni da komunikatzeko eta aldaketak egin ahal izateko, baina ez da guztiz beharrezkoa.\",\n  noteLicense: \"Bidaltzen baduzu, onartzen duzu liburutegia <link>MIT lizentziarekin argitaratuko dela, </link>zeinak, laburbilduz, esan nahi du edozeinek erabiltzen ahal duela murrizketarik gabe.\",\n  noteItems: \"Liburutegiko elementu bakoitzak bere izena eduki behar du iragazi ahal izateko. Liburutegiko hurrengo elementuak barne daude:\",\n  atleastOneLibItem: \"Hautatu gutxienez liburutegiko elementu bat gutxienez hasi ahal izateko\",\n  republishWarning: \"Oharra: hautatutako elementu batzuk dagoeneko argitaratuta/bidalita bezala markatuta daude. Elementuak berriro bidali behar dituzu lehendik dagoen liburutegi edo bidalketa eguneratzen duzunean.\"\n};\nvar publishSuccessDialog = {\n  title: \"Liburutegia bidali da\",\n  content: \"Eskerrik asko {{authorName}}. Zure liburutegia bidali da berrikustera. Jarraitu dezakezu haren egoera<link>hemen</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Leheneratu liburutegia\",\n  removeItemsFromLib: \"Kendu hautatutako elementuak liburutegitik\"\n};\nvar imageExportDialog = {\n  header: \"Esportatu irudia\",\n  label: {\n    withBackground: \"Atzeko planoa\",\n    onlySelected: \"Hautapena soilik\",\n    darkMode: \"Modu iluna\",\n    embedScene: \"Txertatu eszena\",\n    scale: \"Eskala\",\n    padding: \"Betegarria\"\n  },\n  tooltip: {\n    embedScene: \"Eszenaren datuak esportatutako PNG/SVG fitxategian gordeko dira, eszena bertatik berrezartzeko.\\nEsportatutako fitxategien tamaina handituko da.\"\n  },\n  title: {\n    exportToPng: \"Esportatu PNG gisa\",\n    exportToSvg: \"Esportatu SVG gisa\",\n    copyPngToClipboard: \"Kopiatu PNG arbelera\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Kopiatu arbelean\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Zure marrazkiak muturretik muturrera enkriptatu dira, beraz Excalidraw-ren zerbitzariek ezingo dituzte ikusi.\",\n  link: \"Excalidraw-ren muturretik muturrerako enkriptatzearen gaineko mezua blogean\"\n};\nvar stats = {\n  angle: \"Angelua\",\n  element: \"Elementua\",\n  elements: \"Elementuak\",\n  height: \"Altuera\",\n  scene: \"Eszena\",\n  selected: \"Hautatua\",\n  storage: \"Biltegia\",\n  title: \"Datuak\",\n  total: \"Guztira\",\n  version: \"Bertsioa\",\n  versionCopy: \"Klikatu kopiatzeko\",\n  versionNotAvailable: \"Bertsio ez eskuragarria\",\n  width: \"Zabalera\"\n};\nvar toast = {\n  addedToLibrary: \"Liburutegira gehitu da\",\n  copyStyles: \"Estiloak kopiatu dira.\",\n  copyToClipboard: \"Arbelean kopiatu da.\",\n  copyToClipboardAsPng: \"{{exportSelection}} kopiatu da arbelean PNG gisa\\n({{exportColorScheme}})\",\n  fileSaved: \"Fitxategia gorde da.\",\n  fileSavedToFilename: \"{filename}-n gorde da\",\n  canvas: \"oihala\",\n  selection: \"hautapena\",\n  pasteAsSingleElement: \"Erabili {{shortcut}} elementu bakar gisa itsasteko,\\nedo itsatsi lehendik dagoen testu-editore batean\",\n  unableToEmbed: \"Url hau txertatzea ez da une honetan onartzen. Sortu issue bat GitHub-en Urla zerrenda zurian sartzea eskatzeko\",\n  unrecognizedLinkFormat: \"Kapsulatu duzun esteka ez dator bat espero den formatuarekin. Mesedez, saiatu iturburu-guneak emandako 'kapsulatu' katea itsasten\"\n};\nvar colors = {\n  transparent: \"Gardena\",\n  black: \"Beltza\",\n  white: \"Zuria\",\n  red: \"Gorria\",\n  pink: \"Arrosa\",\n  grape: \"Mahats kolorea\",\n  violet: \"Bioleta\",\n  gray: \"Grisa\",\n  blue: \"Urdina\",\n  cyan: \"Ziana\",\n  teal: \"Berde urdinxka\",\n  green: \"Berdea\",\n  yellow: \"Horia\",\n  orange: \"Laranja\",\n  bronze: \"Brontzea\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Zure datu guztiak lokalean gordetzen dira zure nabigatzailean.\",\n    center_heading_plus: \"Horren ordez Excalidraw+-era joan nahi al zenuen?\",\n    menuHint: \"Esportatu, hobespenak, hizkuntzak...\"\n  },\n  defaults: {\n    menuHint: \"Esportatu, hobespenak eta gehiago...\",\n    center_heading: \"Diagramak. Egina. Sinplea.\",\n    toolbarHint: \"Aukeratu tresna bat eta hasi marrazten!\",\n    helpHint: \"Lasterbideak eta laguntza\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Gehien erabilitako kolore pertsonalizatuak\",\n  colors: \"Koloreak\",\n  shades: \"\\xD1abardurak\",\n  hexCode: \"Hez kodea\",\n  noShades: \"Kolore honetarako ez dago \\xF1abardurarik eskuragarri\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Esportatu irudi gisa\",\n      button: \"Esportatu irudi gisa\",\n      description: \"Esportatu eszenaren datuak geroago inportatu ahal izango duzun irudi gisa.\"\n    },\n    saveToDisk: {\n      title: \"Gorde diskoan\",\n      button: \"Gorde diskoan\",\n      description: \"Esportatu eszenaren datuak geroago inportatu ahal izango duzun fitxategi batan.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Esportatu Excalidraw+ra\",\n      description: \"Gorde eszena zure Excalidraw+ laneko areara.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Fitxategitik kargatu\",\n      button: \"Fitxategitik kargatu\",\n      description: \"Fitxategi batetik kargatzeak <bold>lehendik duzun edukia ordezkatuko du</bold>.<br></br>Lehenengo marrazkiaren babeskopia egin dezakezu beheko aukeretako bat erabiliz.\"\n    },\n    shareableLink: {\n      title: \"Estekatik kargatu\",\n      button: \"Ordeztu nire edukia\",\n      description: \"Kanpoko irudi bat kargatzeak <bold>lehendik duzun edukia ordezkatuko du</bold>.<br></br>. Zure marrazkiaren babeskopia egin dezakezu lehenik beheko aukeretako bat erabiliz.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"Txertatu\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"Aurrebista\"\n};\nvar eu_ES_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=eu-ES-3TOEU5DE.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvZXUtRVMtM1RPRVU1REUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLFlBQVk7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsUUFBUTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3RUFBd0UsVUFBVTtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtREFBbUQsVUFBVTtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixZQUFZO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGtCQUFrQixpQ0FBaUMsbUJBQW1CO0FBQ2pHO0FBQ0EseUJBQXlCLFNBQVM7QUFDbEM7QUFDQTtBQUNBLG1DQUFtQyxXQUFXO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBOEJFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvQGV4Y2FsaWRyYXcvZXhjYWxpZHJhdy9kaXN0L2Rldi9sb2NhbGVzL2V1LUVTLTNUT0VVNURFLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4uL2NodW5rLVhERkNVVVQ2LmpzXCI7XG5cbi8vIGxvY2FsZXMvZXUtRVMuanNvblxudmFyIGxhYmVscyA9IHtcbiAgcGFzdGU6IFwiSXRzYXRzaVwiLFxuICBwYXN0ZUFzUGxhaW50ZXh0OiBcIkl0c2F0c2kgdGVzdHUgYXJydW50IGdpc2FcIixcbiAgcGFzdGVDaGFydHM6IFwiSXRzYXRzaSBncmFmaWtvYWtcIixcbiAgc2VsZWN0QWxsOiBcIkhhdXRhdHUgZGVuYVwiLFxuICBtdWx0aVNlbGVjdDogXCJHZWhpdHUgZWxlbWVudHVhIGhhdXRhcGVuZXJhXCIsXG4gIG1vdmVDYW52YXM6IFwiTXVnaXR1IG9paGFsYVwiLFxuICBjdXQ6IFwiRWJha2lcIixcbiAgY29weTogXCJLb3BpYXR1XCIsXG4gIGNvcHlBc1BuZzogXCJLb3BpYXR1IGFyYmVsZXJhIFBORyBnaXNhXCIsXG4gIGNvcHlBc1N2ZzogXCJLb3BpYXR1IGFyYmVsZXJhIFNWRyBnaXNhXCIsXG4gIGNvcHlUZXh0OiBcIktvcGlhdHUgYXJiZWxlcmEgdGVzdHUgZ2lzYVwiLFxuICBjb3B5U291cmNlOiBcIktvcGlhdHUgaXR1cnJpYSBhcmJlbGVhblwiLFxuICBjb252ZXJ0VG9Db2RlOiBcIkJpaHVydHUga29kZWFcIixcbiAgYnJpbmdGb3J3YXJkOiBcIkVrYXJyaSBhdXJyZXJhZ29cIixcbiAgc2VuZFRvQmFjazogXCJFcmFtYW4gYXR6ZXJhXCIsXG4gIGJyaW5nVG9Gcm9udDogXCJFa2FycmkgYXVycmVyYVwiLFxuICBzZW5kQmFja3dhcmQ6IFwiRXJhbWFuIGF0emVyYWdvXCIsXG4gIGRlbGV0ZTogXCJFemFiYXR1XCIsXG4gIGNvcHlTdHlsZXM6IFwiS29waWF0dSBlc3RpbG9ha1wiLFxuICBwYXN0ZVN0eWxlczogXCJJdHNhdHNpIGVzdGlsb2FrXCIsXG4gIHN0cm9rZTogXCJNYXJyYVwiLFxuICBiYWNrZ3JvdW5kOiBcIkF0emVrbyBwbGFub2FcIixcbiAgZmlsbDogXCJCZXRlXCIsXG4gIHN0cm9rZVdpZHRoOiBcIk1hcnJhcmVuIHphYmFsZXJhXCIsXG4gIHN0cm9rZVN0eWxlOiBcIk1hcnJhcmVuIGVzdGlsb2FcIixcbiAgc3Ryb2tlU3R5bGVfc29saWQ6IFwiU29saWRvYVwiLFxuICBzdHJva2VTdHlsZV9kYXNoZWQ6IFwiTWFycmF0dWFcIixcbiAgc3Ryb2tlU3R5bGVfZG90dGVkOiBcIlB1bnR1a2F0dWFcIixcbiAgc2xvcHBpbmVzczogXCJNYXJyYXJlbiB0cmF6b2FcIixcbiAgb3BhY2l0eTogXCJPcGFrb3Rhc3VuYVwiLFxuICB0ZXh0QWxpZ246IFwiVGVzdHVhcmVuIGxlcnJva2FwZW5hXCIsXG4gIGVkZ2VzOiBcIkVydHpha1wiLFxuICBzaGFycDogXCJFcnR6IGJpemlhXCIsXG4gIHJvdW5kOiBcIkJvcm9iaWxkdWFcIixcbiAgYXJyb3doZWFkczogXCJHZXppLXB1bnRha1wiLFxuICBhcnJvd2hlYWRfbm9uZTogXCJCYXQgZXJlIGV6XCIsXG4gIGFycm93aGVhZF9hcnJvdzogXCJHZXppYVwiLFxuICBhcnJvd2hlYWRfYmFyOiBcIkJhcnJhXCIsXG4gIGFycm93aGVhZF9jaXJjbGU6IFwiXCIsXG4gIGFycm93aGVhZF9jaXJjbGVfb3V0bGluZTogXCJcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlOiBcIkhpcnVraWFcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlX291dGxpbmU6IFwiXCIsXG4gIGFycm93aGVhZF9kaWFtb25kOiBcIlwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZF9vdXRsaW5lOiBcIlwiLFxuICBmb250U2l6ZTogXCJMZXRyYS10YW1haW5hXCIsXG4gIGZvbnRGYW1pbHk6IFwiTGV0cmEtdGlwb2FcIixcbiAgYWRkV2F0ZXJtYXJrOiAnR2VoaXR1IFwiRXhjYWxpZHJhdyBiaWRleiBlZ2luYVwiJyxcbiAgaGFuZERyYXduOiBcIkVza3V6IG1hcnJhenR1YVwiLFxuICBub3JtYWw6IFwiTm9ybWFsYVwiLFxuICBjb2RlOiBcIktvZGVhXCIsXG4gIHNtYWxsOiBcIlR4aWtpYVwiLFxuICBtZWRpdW06IFwiRXJ0YWluYVwiLFxuICBsYXJnZTogXCJIYW5kaWFcIixcbiAgdmVyeUxhcmdlOiBcIk9zbyBoYW5kaWFcIixcbiAgc29saWQ6IFwiU29saWRvYVwiLFxuICBoYWNodXJlOiBcIkl0emFsZHVuYVwiLFxuICB6aWd6YWc6IFwiU2lnaS1zYWdhXCIsXG4gIGNyb3NzSGF0Y2g6IFwiTWFycmFkdW5hXCIsXG4gIHRoaW46IFwiTWVoZWFcIixcbiAgYm9sZDogXCJMb2RpYVwiLFxuICBsZWZ0OiBcIkV6a2VycmVhblwiLFxuICBjZW50ZXI6IFwiRXJkaWFuXCIsXG4gIHJpZ2h0OiBcIkVza3VpbmVhblwiLFxuICBleHRyYUJvbGQ6IFwiT3NvIGxvZGlhXCIsXG4gIGFyY2hpdGVjdDogXCJBcmtpdGVrdG9hXCIsXG4gIGFydGlzdDogXCJBcnRpc3RhXCIsXG4gIGNhcnRvb25pc3Q6IFwiTWFycmF6a2lsYXJpYVwiLFxuICBmaWxlVGl0bGU6IFwiRml0eGF0ZWdpIGl6ZW5hXCIsXG4gIGNvbG9yUGlja2VyOiBcIktvbG9yZS1oYXV0YXR6YWlsZWFcIixcbiAgY2FudmFzQ29sb3JzOiBcIk9paGFsZWFuIGVyYWJpbGl0YVwiLFxuICBjYW52YXNCYWNrZ3JvdW5kOiBcIk9paGFsYXJlbiBhdHpla28gcGxhbm9hXCIsXG4gIGRyYXdpbmdDYW52YXM6IFwiTWFycmF6dGVrbyBvaWhhbGFcIixcbiAgbGF5ZXJzOiBcIkdlcnV6YWtcIixcbiAgYWN0aW9uczogXCJFa2ludHpha1wiLFxuICBsYW5ndWFnZTogXCJIaXprdW50emFcIixcbiAgbGl2ZUNvbGxhYm9yYXRpb246IFwiWnV6ZW5la28gZWxrYXJsYW5hLi4uXCIsXG4gIGR1cGxpY2F0ZVNlbGVjdGlvbjogXCJCaWtvaXp0dVwiLFxuICB1bnRpdGxlZDogXCJJemVuZ2FiZWFcIixcbiAgbmFtZTogXCJJemVuYVwiLFxuICB5b3VyTmFtZTogXCJadXJlIGl6ZW5hXCIsXG4gIG1hZGVXaXRoRXhjYWxpZHJhdzogXCJFeGNhbGlkcmF3IGJpZGV6IGVnaW5hXCIsXG4gIGdyb3VwOiBcIkhhdXRhcGVuYSB0YWxkZWEgYmlodXJ0dVwiLFxuICB1bmdyb3VwOiBcIkRlc2VnaW4gaGF1dGFwZW5hcmVuIHRhbGRlYVwiLFxuICBjb2xsYWJvcmF0b3JzOiBcIktvbGFib3JhdHphaWxlYWtcIixcbiAgc2hvd0dyaWQ6IFwiRXJha3V0c2kgc2FyZXRhXCIsXG4gIGFkZFRvTGlicmFyeTogXCJHZWhpdHUgbGlidXJ1dGVnaXJhXCIsXG4gIHJlbW92ZUZyb21MaWJyYXJ5OiBcIktlbmR1IGxpYnVydXRlZ2l0aWtcIixcbiAgbGlicmFyeUxvYWRpbmdNZXNzYWdlOiBcIkxpYnVydXRlZ2lhIGthcmdhdHplblxcdTIwMjZcIixcbiAgbGlicmFyaWVzOiBcIkFyYWthdHUgbGlidXJ1dGVnaWFrXCIsXG4gIGxvYWRpbmdTY2VuZTogXCJFc3plbmEga2FyZ2F0emVuXFx1MjAyNlwiLFxuICBhbGlnbjogXCJMZXJyb2thdHVcIixcbiAgYWxpZ25Ub3A6IFwiTGVycm9rYXR1IGdvaWFuXCIsXG4gIGFsaWduQm90dG9tOiBcIkxlcnJva2F0dSBiZWhlYW5cIixcbiAgYWxpZ25MZWZ0OiBcIkxlcnJva2F0dSBlemtlcnJlYW5cIixcbiAgYWxpZ25SaWdodDogXCJMZXJyb2thdHUgZXNrdWluZWFuXCIsXG4gIGNlbnRlclZlcnRpY2FsbHk6IFwiRXJkaXJhdHUgYmVydGlrYWxraVwiLFxuICBjZW50ZXJIb3Jpem9udGFsbHk6IFwiRXJkaXJhdHUgaG9yaXpvbnRhbGtpXCIsXG4gIGRpc3RyaWJ1dGVIb3Jpem9udGFsbHk6IFwiQmFuYW5kdSBob3Jpem9udGFsa2lcIixcbiAgZGlzdHJpYnV0ZVZlcnRpY2FsbHk6IFwiQmFuYW5kdSBiZXJ0aWthbGtpXCIsXG4gIGZsaXBIb3Jpem9udGFsOiBcIklyYXVsaSBob3Jpem9udGFsa2lcIixcbiAgZmxpcFZlcnRpY2FsOiBcIklyYXVsaSBiZXJ0aWthbGtpXCIsXG4gIHZpZXdNb2RlOiBcIklrdXNwZWdpYVwiLFxuICBzaGFyZTogXCJQYXJ0ZWthdHVcIixcbiAgc2hvd1N0cm9rZTogXCJFcmFrdXRzaSBtYXJyYXJlbiBrb2xvcmUtaGF1dGF0emFpbGVhXCIsXG4gIHNob3dCYWNrZ3JvdW5kOiBcIkVyYWt1dHNpIGF0emVrbyBwbGFub2FyZW4ga29sb3JlLWhhdXRhdHphaWxlYVwiLFxuICB0b2dnbGVUaGVtZTogXCJBbGRhdHUgZ2FpYVwiLFxuICBwZXJzb25hbExpYjogXCJMaWJ1cnV0ZWdpIHBlcnRzb25hbGFcIixcbiAgZXhjYWxpZHJhd0xpYjogXCJFeGNhbGlkcmF3IGxpYnVydXRlZ2lhXCIsXG4gIGRlY3JlYXNlRm9udFNpemU6IFwiVHhpa2l0dSBsZXRyYSB0YW1haW5hXCIsXG4gIGluY3JlYXNlRm9udFNpemU6IFwiSGFuZGl0dSBsZXRyYSB0YW1haW5hXCIsXG4gIHVuYmluZFRleHQ6IFwiQXNrYXR1IHRlc3R1YVwiLFxuICBiaW5kVGV4dDogXCJMb3R1IHRlc3R1YSBlZHVraW9udHppYXJpXCIsXG4gIGNyZWF0ZUNvbnRhaW5lckZyb21UZXh0OiBcIkJpbGF0dSB0ZXN0dWEgZWR1a2lvbnR6aSBiYXRlYW5cIixcbiAgbGluazoge1xuICAgIGVkaXQ6IFwiRWRpdGF0dSBlc3Rla2FcIixcbiAgICBlZGl0RW1iZWQ6IFwiRWRpdGF0dSBlc3Rla2EgZXRhIGthcHN1bGF0dVwiLFxuICAgIGNyZWF0ZTogXCJTb3J0dSBlc3Rla2FcIixcbiAgICBjcmVhdGVFbWJlZDogXCJTb3J0dSBlc3Rla2EgZXRhIGthcHN1bGF0dVwiLFxuICAgIGxhYmVsOiBcIkVzdGVrYVwiLFxuICAgIGxhYmVsRW1iZWQ6IFwiRXN0ZWthIGV0YSBrYXBzdWxhXCIsXG4gICAgZW1wdHk6IFwiRXogZGEgZXN0ZWthcmlrIGV6YXJyaVwiXG4gIH0sXG4gIGxpbmVFZGl0b3I6IHtcbiAgICBlZGl0OiBcIkVkaXRhdHUgbGVycm9hXCIsXG4gICAgZXhpdDogXCJJcnRlbiBsZXJyby1lZGl0b3JldGlrXCJcbiAgfSxcbiAgZWxlbWVudExvY2s6IHtcbiAgICBsb2NrOiBcIkJsb2tlYXR1XCIsXG4gICAgdW5sb2NrOiBcIkRlc2Jsb2tlYXR1XCIsXG4gICAgbG9ja0FsbDogXCJCbG9rZWF0dSBndXp0aWFrXCIsXG4gICAgdW5sb2NrQWxsOiBcIkRlc2Jsb2tlYXR1IGd1enRpYWtcIlxuICB9LFxuICBzdGF0dXNQdWJsaXNoZWQ6IFwiQXJnaXRhcmF0dWFcIixcbiAgc2lkZWJhckxvY2s6IFwiTWFudGVuZHUgYWxib2tvIGJhcnJhIGlyZWtpdGFcIixcbiAgc2VsZWN0QWxsRWxlbWVudHNJbkZyYW1lOiBcIkhhdXRhdHUgbWFya29rbyBlbGVtZW50dSBndXp0aWFrXCIsXG4gIHJlbW92ZUFsbEVsZW1lbnRzRnJvbUZyYW1lOiBcIktlbmR1IG1hcmtva28gZWxlbWVudHUgZ3V6dGlha1wiLFxuICBleWVEcm9wcGVyOiBcIkF1a2VyYXR1IGtvbG9yZWEgb2loYWxldGlrXCIsXG4gIHRleHRUb0RpYWdyYW06IFwiVGVzdHV0aWsgZGlhZ3JhbWFyYVwiLFxuICBwcm9tcHQ6IFwiXCJcbn07XG52YXIgbGlicmFyeSA9IHtcbiAgbm9JdGVtczogXCJPcmFpbmRpayBleiBkYSBlbGVtZW50dXJpayBnZWhpdHUuLi5cIixcbiAgaGludF9lbXB0eUxpYnJhcnk6IFwiSGF1dGF0dSBvaWhhbGVrbyBlbGVtZW50dSBiYXQgaGVtZW4gZ2VoaXR6ZWtvLCBlZG8gaW5zdGFsYXR1IGxpYnVydXRlZ2kgYmF0IGJlaGVrbyBiaWx0ZWdpIHB1Ymxpa290aWsuXCIsXG4gIGhpbnRfZW1wdHlQcml2YXRlTGlicmFyeTogXCJIYXV0YXR1IG9paGFsZWtvIGVsZW1lbnR1IGJhdCBoZW1lbiBnZWhpdHpla28uXCJcbn07XG52YXIgYnV0dG9ucyA9IHtcbiAgY2xlYXJSZXNldDogXCJHYXJiaXR1IG9paGFsYVwiLFxuICBleHBvcnRKU09OOiBcIkVzcG9ydGF0dSBmaXR4YXRlZ2lyYVwiLFxuICBleHBvcnRJbWFnZTogXCJFc3BvcnRhdHUgaXJ1ZGlhLi4uXCIsXG4gIGV4cG9ydDogXCJHb3JkZSBoZW1lbi4uLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiS29waWF0dSBhcmJlbGVyYVwiLFxuICBzYXZlOiBcIkdvcmRlIHVuZWtvIGZpdHhhdGVnaWFuXCIsXG4gIHNhdmVBczogXCJHb3JkZSBob25lbGFcIixcbiAgbG9hZDogXCJJcmVraVwiLFxuICBnZXRTaGFyZWFibGVMaW5rOiBcIkxvcnR1IHBhcnRla2F0emVrbyBlc3Rla2FcIixcbiAgY2xvc2U6IFwiSXR4aVwiLFxuICBzZWxlY3RMYW5ndWFnZTogXCJIYXV0YXR1IGhpemt1bnR6YVwiLFxuICBzY3JvbGxCYWNrVG9Db250ZW50OiBcIkpvYW4gYXR6ZXJhIGVkdWtpcmFcIixcbiAgem9vbUluOiBcIkhhbmRpYWdvdHVcIixcbiAgem9vbU91dDogXCJUeGlraWFnb3R1XCIsXG4gIHJlc2V0Wm9vbTogXCJMZWhlbmVyYXR1IHpvb21hXCIsXG4gIG1lbnU6IFwiTWVudWFcIixcbiAgZG9uZTogXCJFZ2luYVwiLFxuICBlZGl0OiBcIkVkaXRhdHVcIixcbiAgdW5kbzogXCJEZXNlZ2luXCIsXG4gIHJlZG86IFwiQmVycmVnaW5cIixcbiAgcmVzZXRMaWJyYXJ5OiBcIkxlaGVuZXJhdHUgbGlidXJ1dGVnaWFcIixcbiAgY3JlYXRlTmV3Um9vbTogXCJTb3J0dSBnZWxhIGJlcnJpYVwiLFxuICBmdWxsU2NyZWVuOiBcIlBhbnRhaWxhIG9zb2FcIixcbiAgZGFya01vZGU6IFwiTW9kdSBpbHVuYVwiLFxuICBsaWdodE1vZGU6IFwiTW9kdSBhcmdpYVwiLFxuICB6ZW5Nb2RlOiBcIlplbiBtb2R1YVwiLFxuICBvYmplY3RzU25hcE1vZGU6IFwiQXR4aWtpIG9iamVrdHVlaVwiLFxuICBleGl0WmVuTW9kZTogXCJJcnRlbiBaZW4gbW9kdXRpa1wiLFxuICBjYW5jZWw6IFwiVXR6aVwiLFxuICBjbGVhcjogXCJHYXJiaXR1XCIsXG4gIHJlbW92ZTogXCJLZW5kdVwiLFxuICBlbWJlZDogXCJBbGRhdHUga2Fwc3VsYXR6ZWFcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiQXJnaXRhcmF0dVwiLFxuICBzdWJtaXQ6IFwiQmlkYWxpXCIsXG4gIGNvbmZpcm06IFwiQmFpXCIsXG4gIGVtYmVkZGFibGVJbnRlcmFjdGlvbkJ1dHRvbjogXCJFZ2luIGtsaWsgZWxrYXIgZXJhZ2l0ZWtvXCJcbn07XG52YXIgYWxlcnRzID0ge1xuICBjbGVhclJlc2V0OiBcIkhvbmVrIG9paGFsIG9zb2EgZ2FyYml0dWtvIGR1LiBaaXVyIHphdWRlP1wiLFxuICBjb3VsZE5vdENyZWF0ZVNoYXJlYWJsZUxpbms6IFwiRXppbiBpemFuIGRhIHBhcnRla2F0emVrbyBlc3Rla2FyaWsgc29ydHUuXCIsXG4gIGNvdWxkTm90Q3JlYXRlU2hhcmVhYmxlTGlua1Rvb0JpZzogXCJFemluIGl6YW4gZGEgcGFydGVrYXR6ZWtvIGVzdGVrYXJpayBzb3J0dTogZXN6ZW5hIGhhbmRpZWdpYSBkYVwiLFxuICBjb3VsZE5vdExvYWRJbnZhbGlkRmlsZTogXCJFemluIGl6YW4gZGEga2FyZ2F0dSwgZml0eGF0ZWdpYWsgZXogZHUgYmFsaW9cIixcbiAgaW1wb3J0QmFja2VuZEZhaWxlZDogXCJJbnBvcnRhemlvYWsgaHV0cyBlZ2luIGR1LlwiLFxuICBjYW5ub3RFeHBvcnRFbXB0eUNhbnZhczogXCJFemluIGl6YW4gZGEgb2loYWwgaHV0c2EgZXNwb3J0YXR1LlwiLFxuICBjb3VsZE5vdENvcHlUb0NsaXBib2FyZDogXCJFemluIGl6YW4gZGEgYXJiZWxlYW4ga29waWF0dS5cIixcbiAgZGVjcnlwdEZhaWxlZDogXCJFemluIGl6YW4gZGEgZGVzemlmcmF0dS5cIixcbiAgdXBsb2FkZWRTZWN1cmx5OiBcIkthcmdhdHplYSBtdXR1cnJldGlrIG11dHVycmVyYWtvIHppZnJhdHplIGJpZGV6IHppdXJ0YXR1IGRhLCBoYXUgZGEsIEV4Y2FsaWRyYXcgemVyYml0emFyaWFrIGV0YSBoaXJ1Z2FycmVuZWsgZXppbiBkdXRlbGEgZWR1a2lhIGlyYWt1cnJpLlwiLFxuICBsb2FkU2NlbmVPdmVycmlkZVByb21wdDogXCJLYW5wb2tvIG1hcnJhemtpYSBrYXJnYXR6ZWFrIGxlaGVuZGlrIGR1enVuIGVkdWtpYSBvcmRlemthdHVrbyBkdS4gSmFycmFpdHUgbmFoaSBkdXp1P1wiLFxuICBjb2xsYWJTdG9wT3ZlcnJpZGVQcm9tcHQ6IFwiU2Fpb2EgZ2VsZGl0emVhayBsb2thbGVhbiBnb3JkZXRha28genVyZSBhdXJyZWtvIG1hcnJhemtpYSBnYWluaWRhdHppa28gZHUuIFppdXIgemF1ZGU/XFxuXFxuKFp1cmUgbWFycmF6a2kgbG9rYWxhIG1hbnRlbmR1IG5haGkgYmFkdXp1LCBpdHhpIGFyYWthdHphaWxlYXJlbiBmaXR4YS4pXCIsXG4gIGVycm9yQWRkaW5nVG9MaWJyYXJ5OiBcIkV6aW4gaXphbiBkYSBlbGVtZW50dWEgbGlidXJ1dGVnaWFuIGdlaGl0dVwiLFxuICBlcnJvclJlbW92aW5nRnJvbUxpYnJhcnk6IFwiRXppbiBpemFuIGRhIGVsZW1lbnR1YSBsaWJ1cnV0ZWdpdGlrIGtlbmR1XCIsXG4gIGNvbmZpcm1BZGRMaWJyYXJ5OiBcIkhvbmVrIHt7bnVtU2hhcGVzfX0gZm9ybWEgZ2VoaXR1a28gZGl0dSB6dXJlIGxpYnVydXRlZ2lhbi4gWml1ciB6YXVkZT9cIixcbiAgaW1hZ2VEb2VzTm90Q29udGFpblNjZW5lOiBcIklydWRpIGhvbmVrIGV6IGRpcnVkaSBlc3plbmEgZGF0dXJpayBkdWVuaWsuIEVzemVuYSBrYXBzdWxhdHplYSBnYWl0dSBhbCBkdXp1IGVzcG9ydGF6aW9hbj9cIixcbiAgY2Fubm90UmVzdG9yZUZyb21JbWFnZTogXCJFemluIGl6YW4gZGEgZXN6ZW5hIGxlaGVuZXJhdHUgaXJ1ZGkgZml0eGF0ZWdpIGhvbmV0YXRpa1wiLFxuICBpbnZhbGlkU2NlbmVVcmw6IFwiRXppbiBpemFuIGRhIGVzemVuYSBpbnBvcnRhdHUgZW1hbmRha28gVVJMdGlrLiBHYWl6a2kgZXJhdHV0YSBkYWdvIGVkbyBleiBkdSBiYWxpb3prbyBFeGNhbGlkcmF3IEpTT04gZGF0dXJpay5cIixcbiAgcmVzZXRMaWJyYXJ5OiBcIkhvbmVrIHp1cmUgbGlidXJ1dGVnaWEgZ2FyYml0dWtvIGR1LiBaaXVyIHphdWRlP1wiLFxuICByZW1vdmVJdGVtc0Zyb21zTGlicmFyeTogXCJMaWJ1cnV0ZWdpdGlrIHt7Y291bnR9fSBlbGVtZW50dSBlemFiYXR1P1wiLFxuICBpbnZhbGlkRW5jcnlwdGlvbktleTogXCJFbmtyaXB0YXppby1nYWtvYWsgMjIga2FyYWt0ZXJlIGl6YW4gYmVoYXIgZGl0dS4gWnV6ZW5la28gbGFua2lkZXR6YSBkZXNnYWl0dXRhIGRhZ28uXCIsXG4gIGNvbGxhYk9mZmxpbmVXYXJuaW5nOiBcIkV6IGRhZ28gSW50ZXJuZXRla28ga29uZXhpb3Jpay5cXG5adXJlIGFsZGFrZXRhayBleiBkaXJhIGdvcmRla28hXCJcbn07XG52YXIgZXJyb3JzID0ge1xuICB1bnN1cHBvcnRlZEZpbGVUeXBlOiBcIk9uYXJ0dSBnYWJla28gZml0eGF0ZWdpIG1vdGEuXCIsXG4gIGltYWdlSW5zZXJ0RXJyb3I6IFwiRXppbiBpemFuIGRhIGlydWRpYSB0eGVydGF0dS4gU2FpYXR1IGJlcnJpcm8gZ2Vyb2Fnby4uLlwiLFxuICBmaWxlVG9vQmlnOiBcIkZpdHhhdGVnaWEgaGFuZGllZ2lhIGRhLiBPbmFydHV0YWtvIGdlaGllbmV6a28gdGFtYWluYSB7e21heFNpemV9fSBkYS5cIixcbiAgc3ZnSW1hZ2VJbnNlcnRFcnJvcjogXCJFemluIGl6YW4gZGEgU1ZHIGlydWRpYSB0eGVydGF0dS4gU1ZHIG1hcmthayBiYWxpb2dhYmVhIGRpcnVkaS5cIixcbiAgZmFpbGVkVG9GZXRjaEltYWdlOiBcIkV6aW4gaXphbiBkYSBpcnVkaWEgZXNrdXJhdHUuXCIsXG4gIGludmFsaWRTVkdTdHJpbmc6IFwiU1ZHIGJhbGlvZ2FiZWEuXCIsXG4gIGNhbm5vdFJlc29sdmVDb2xsYWJTZXJ2ZXI6IFwiRXppbiBpemFuIGRhIGVsa2FybGFuZWtvIHplcmJpdHphcmlyYSBrb25la3RhdHUuIE1lc2VkZXosIGJlcnJpcm8ga2FyZ2F0dSBvcnJpYSBldGEgc2FpYXR1IGJlcnJpcm8uXCIsXG4gIGltcG9ydExpYnJhcnlFcnJvcjogXCJFemluIGl6YW4gZGEgbGlidXJ1dGVnaWEga2FyZ2F0dVwiLFxuICBjb2xsYWJTYXZlRmFpbGVkOiBcIkV6aW4gaXphbiBkYSBiYWNrZW5kIGRhdHUtYmFzZWFuIGdvcmRlLiBBcmF6b2FrIGphcnJhaXR6ZW4gYmFkdSwgenVyZSBmaXR4YXRlZ2lhIGxva2FsZWFuIGdvcmRlIGJlaGFya28gemVudWtlIHp1cmUgbGFuYSBleiBkdXp1bGEgZ2FsdHplbiB6aXVydGF0emVrby5cIixcbiAgY29sbGFiU2F2ZUZhaWxlZF9zaXplRXhjZWVkZWQ6IFwiRXppbiBpemFuIGRhIGJhY2tlbmQgZGF0dS1iYXNlYW4gZ29yZGUsIG9oaWFsYSBoYW5kaWVnaWEgZGVsYSBkaXJ1ZGkuIEZpdHhhdGVnaWEgbG9rYWxlYW4gZ29yZGUgYmVoYXJrbyB6ZW51a2UgenVyZSBsYW5hIGdhbHR6ZW4gZXogZHV6dWxhIHppdXJ0YXR6ZWtvLlwiLFxuICBpbWFnZVRvb2xOb3RTdXBwb3J0ZWQ6IFwiSXJ1ZGlhayBkZXNnYWl0dXRhIGRhdWRlLlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJCcmF2ZSBhcmFrYXR6YWlsZWEgZXJhYmlsdHplbiBhcmkgemFyZWxhIGRpcnVkaSA8Ym9sZD5CbG9rZWF0dSBoYXR6LW1hcmthayBlcmFzb2tvcmtpPC9ib2xkPiBlemFycGVuYSBnYWl0dXRhLlwiLFxuICAgIGxpbmUyOiBcIkhvbmVrIHp1cmUgbWFycmF6a2lldGFrbyA8Ym9sZD5UZXN0dS1lbGVtZW50dWFrPC9ib2xkPiBoYXV0c2kgZGl0emFrZS5cIixcbiAgICBsaW5lMzogXCJFemFycGVuIGhhdSBkZXNnYWl0emVhIGdvbWVuZGF0emVuIGR1Z3UuIDxsaW5rPnVycmF0cyBoYXVlazwvbGluaz4gamFycmFpIGRpdHpha2V6dSBob3JpIG5vbGEgZWdpbiBqYWtpdGVrby5cIixcbiAgICBsaW5lNDogXCJFemFycGVuIGhhdSBkZXNnYWl0dXogZ2VybywgdGVzdHUtZWxlbWVudHVlbiBiaXN0YXJhdHplYSBrb25wb250emVuIGV6IGJhZGEsIGlyZWtpIDxpc3N1ZUxpbms+YXJhem88L2lzc3VlTGluaz4gZ3VyZSBHaXRIdWItZW4gZWRvIGlkYXR6aSBpZXphZ3V6dSA8ZGlzY29yZExpbms+RGlzY29yZDwvZGlzY29yZExpbms+IGhlbGJpZGVyYVwiXG4gIH0sXG4gIGxpYnJhcnlFbGVtZW50VHlwZUVycm9yOiB7XG4gICAgZW1iZWRkYWJsZTogXCJLYXBzdWxhdHV0YWtvIGVsZW1lbnR1YWsgZXppbiBkaXJhIGxpYnVydXRlZ2lyYSBnZWhpdHUuXCIsXG4gICAgaWZyYW1lOiBcIklGcmFtZSBlbGVtZW50dWFrIGV6aW4gZGlyYSBsaWJ1cnV0ZWdpcmEgZ2VoaXR1LlwiLFxuICAgIGltYWdlOiBcIkxhc3RlciBlZ29uZ28gZGEgaXJ1ZGlhayBsaWJ1cnV0ZWdpYW4gZ2VoaXR6ZWtvIGxhZ3VudHphIVwiXG4gIH0sXG4gIGFzeW5jUGFzdGVGYWlsZWRPblJlYWQ6IFwiRXppbiBpemFuIGRhIGl0c2F0c2kgKGV6aW4gaXphbiBkYSBzaXN0ZW1hcmVuIGFyYmVsZXRpayBpcmFrdXJyaSkuXCIsXG4gIGFzeW5jUGFzdGVGYWlsZWRPblBhcnNlOiBcIkV6aW4gaXphbiBkYSBpdHNhdHNpLlwiLFxuICBjb3B5VG9TeXN0ZW1DbGlwYm9hcmRGYWlsZWQ6IFwiRXppbiBpemFuIGRhIGFyYmVsZWFuIGtvcGlhdHUuXCJcbn07XG52YXIgdG9vbEJhciA9IHtcbiAgc2VsZWN0aW9uOiBcIkhhdXRhcGVuYVwiLFxuICBpbWFnZTogXCJUeGVydGF0dSBpcnVkaWFcIixcbiAgcmVjdGFuZ2xlOiBcIkxhdWtpenV6ZW5hXCIsXG4gIGRpYW1vbmQ6IFwiRGlhbWFudGVhXCIsXG4gIGVsbGlwc2U6IFwiRWxpcHNlYVwiLFxuICBhcnJvdzogXCJHZXppYVwiLFxuICBsaW5lOiBcIkxlcnJvYVwiLFxuICBmcmVlZHJhdzogXCJNYXJyYXp0dVwiLFxuICB0ZXh0OiBcIlRlc3R1YVwiLFxuICBsaWJyYXJ5OiBcIkxpYnVydXRlZ2lhXCIsXG4gIGxvY2s6IFwiTWFudGVuZHUgYWt0aWJvIGhhdXRhdHV0YWtvIHRyZXNuYSBtYXJyYXp0dSBvbmRvcmVuXCIsXG4gIHBlbk1vZGU6IFwiTHVtYSBtb2R1YSAtIHVraXBlbmEgc2FpaGVzdHVcIixcbiAgbGluazogXCJHZWhpdHUgLyBFZ3VuZXJhdHUgZXN0ZWthIGhhdXRhdHV0YWtvIGZvcm1hIGJhdGVyYWtvXCIsXG4gIGVyYXNlcjogXCJCb3JyYWdvbWFcIixcbiAgZnJhbWU6IFwiTWFya28gdHJlc25hXCIsXG4gIG1hZ2ljZnJhbWU6IFwiV2lyZWZyYW1lIGtvZGV0emVrb1wiLFxuICBlbWJlZGRhYmxlOiBcIldlYiBrYXBzdWxhdHplYVwiLFxuICBsYXNlcjogXCJMYXNlciBwdW50ZXJvYVwiLFxuICBoYW5kOiBcIkVza3VhIChwYW5vcmF0emUgdHJlc25hKVwiLFxuICBleHRyYVRvb2xzOiBcIlRyZXNuYSBnZWhpYWdvXCIsXG4gIG1lcm1haWRUb0V4Y2FsaWRyYXc6IFwiXCIsXG4gIG1hZ2ljU2V0dGluZ3M6IFwiQUkgZXphcnBlbmFrXCJcbn07XG52YXIgaGVhZGluZ3MgPSB7XG4gIGNhbnZhc0FjdGlvbnM6IFwiQ2FudmFzIGVraW50emFrXCIsXG4gIHNlbGVjdGVkU2hhcGVBY3Rpb25zOiBcIkhhdXRhdHV0YWtvIGZvcm1hcmVuIGVraW50emFrXCIsXG4gIHNoYXBlczogXCJGb3JtYWtcIlxufTtcbnZhciBoaW50cyA9IHtcbiAgY2FudmFzUGFubmluZzogXCJPaWhhbGEgbXVnaXR6ZWtvLCBldXRzaSBzYWd1YXJlbiBndXJwaWxhIGVkbyB6dXJpdW5lLWJhcnJhIGFycmFzdGF0emVhbiwgZWRvIGVyYWJpbGkgZXNrdSB0cmVzbmFcIixcbiAgbGluZWFyRWxlbWVudDogXCJFZ2luIGtsaWsgaGFpbmJhdCBwdW50dSBoYXN0ZWtvLCBhcnJhc3RhdHUgbGVycm8gYmFrYXJyZXJha29cIixcbiAgZnJlZURyYXc6IFwiRWdpbiBrbGlrIGV0YSBhcnJhc3RhdHUsIGFza2F0dSBhbWFpdHV0YWtvYW5cIixcbiAgdGV4dDogXCJBaG9sa3VhOiB0ZXN0dWEgZ2VoaXR1IGRlemFrZXp1IGVkb3plaW4gbGVrdXRhbiBrbGlrIGJpa29pdHphIGVnaW5leiBoYXV0YXBlbiB0cmVzbmFyZWtpblwiLFxuICBlbWJlZGRhYmxlOiBcIkVnaW4ga2xpayBldGEgYXJyYXN0YXR1IHdlYmd1bmVhIGthcHN1bGF0emVrb1wiLFxuICB0ZXh0X3NlbGVjdGVkOiBcIkVnaW4ga2xpayBiaWtvaXR6YSBlZG8gc2FrYXR1IFNBUlRVIHRlc3R1YSBlZGl0YXR6ZWtvXCIsXG4gIHRleHRfZWRpdGluZzogXCJTYWthdHUgRXNjIGVkbyBDdHJsK1NBUlRVIGVkaXRhdHplbiBhbWFpdHpla29cIixcbiAgbGluZWFyRWxlbWVudE11bHRpOiBcIkVnaW4ga2xpayBhemtlbiBwdW50dWFuIGVkbyBzYWthdHUgRXNjIGVkbyBTYXJ0dSBhbWFpdHpla29cIixcbiAgbG9ja0FuZ2xlOiBcIlNISUZUIHNha2F0dXRhIGFuZ2VsdWEgbWFudGVuZHUgZGV6YWtlenVcIixcbiAgcmVzaXplOiBcIlByb3BvcnR6aW9hayBtYW50ZW5kdSBkaXR6YWtlenUgU0hJRlQgc2FrYXR1dGEgdGFtYWluYSBhbGRhdHplbiBkdXp1biBiaXRhcnRlYW4uXFxuc2FrYXR1IEFMVCBlcmRpdGlrIHRhbWFpbmEgYWxkYXR6ZWtvXCIsXG4gIHJlc2l6ZUltYWdlOiBcIlRhbWFpbmEgbGlicmVraSBhbGRhIGRlemFrZXp1IFNISUZUIHNha2F0dXRhLFxcbnNha2F0dSBBTFQgZXJkaXRpayB0YW1haW5hIGFsZGF0emVrb1wiLFxuICByb3RhdGU6IFwiQW5nZWx1YWsgbWFudGVuZHUgZGl0emFrZXp1IFNISUZUIHNha2F0dXRhIGJpcmF0emVuIGR1enVuIGJpdGFydGVhblwiLFxuICBsaW5lRWRpdG9yX2luZm86IFwiRXV0c2kgc2FrYXR1dGEgQ3RybCBlZG8gQ21kIGV0YSBlZ2luIGtsaWsgYmlrb2l0emEgZWRvIHNha2F0dSBDdHJsIGVkbyBDbWQgKyBTYXJ0dSBwdW50dWFrIGVkaXRhdHpla29cIixcbiAgbGluZUVkaXRvcl9wb2ludFNlbGVjdGVkOiBcIlNha2F0dSBFemFiYXR1IHB1bnR1YWsga2VudHpla28sXFxuS3RybCtEIGJpa29penRla28sIGVkbyBhcnJhc3RhdHUgbXVnaXR6ZWtvXCIsXG4gIGxpbmVFZGl0b3Jfbm90aGluZ1NlbGVjdGVkOiBcIkhhdXRhdHUgZWRpdGF0emVrbyBwdW50dSBiYXQgKFNISUZUIHNha2F0dXRhIGFuaXR6IGhhdXRhdHpla28pLFxcbmVkbyBlZHVraSBBbHQgc2FrYXR1dGEgZXRhIGVnaW4ga2xpayBwdW50dSBiZXJyaWFrIGdlaGl0emVrb1wiLFxuICBwbGFjZUltYWdlOiBcIkVnaW4ga2xpayBpcnVkaWEga29rYXR6ZWtvLCBlZG8gZWdpbiBrbGlrIGV0YSBhcnJhc3RhdHUgYmVyZSB0YW1haW5hIGVza3V6IGV6YXJ0emVrb1wiLFxuICBwdWJsaXNoTGlicmFyeTogXCJBcmdpdGFyYXR1IHp1cmUgbGlidXJ1dGVnaWFcIixcbiAgYmluZFRleHRUb0VsZW1lbnQ6IFwiU2FrYXR1IFNhcnR1IHRlc3R1YSBnZWhpdHpla29cIixcbiAgZGVlcEJveFNlbGVjdDogXCJFdXRzaSBDdHJsIGVkbyBDbWQgc2FrYXR1dGEgYXVrZXJha2V0YSBzYWtvbmEgZWdpdGVrbyBldGEgYXJyYXN0YXR6ZWEgc2FpaGVzdGVrb1wiLFxuICBlcmFzZXJSZXZlcnQ6IFwiRWR1a2kgQWx0IHNha2F0dXRhIGV6YWJhdHpla28gbWFya2F0dXRha28gZWxlbWVudHVhayBsZWhlbmVyYXR6ZWtvXCIsXG4gIGZpcmVmb3hfY2xpcGJvYXJkX3dyaXRlOiAnRXphdWdhcnJpIGhhdSBcImRvbS5ldmVudHMuYXN5bmNDbGlwYm9hcmQuY2xpcGJvYXJkSXRlbVwiIG1hcmthIFwidHJ1ZVwiIGdpc2EgZXphcnJpdGEgZ2FpdHUgZGFpdGVrZS4gRmlyZWZveC1lbiBhcmFrYXR6YWlsZWFyZW4gYmFuZGVyYWsgYWxkYXR6ZWtvLCBiaXNpdGF0dSBcImFib3V0OmNvbmZpZ1wiIG9ycmlhbGRlcmEuJyxcbiAgZGlzYWJsZVNuYXBwaW5nOiBcIkVkdWtpIHNha2F0dXRhIEN0cmwgZWRvIENtZCB0ZWtsYSBhdHhpa2lwZW5hIGRlc2dhaXR6ZWtvXCJcbn07XG52YXIgY2FudmFzRXJyb3IgPSB7XG4gIGNhbm5vdFNob3dQcmV2aWV3OiBcIkV6aW4gZGEgb2loYWxhIGF1cnJlaWt1c2lcIixcbiAgY2FudmFzVG9vQmlnOiBcIkFnaWFuIG9paGFsYSBoYW5kaWVnaWEgZGEuXCIsXG4gIGNhbnZhc1Rvb0JpZ1RpcDogXCJBaG9sa3VhOiBzYWlhdHUgdXJydW5lbiBkYXVkZW4gZWxlbWVudHVhayBwaXhrYSBiYXQgaHVyYmlsdHplbi5cIlxufTtcbnZhciBlcnJvclNwbGFzaCA9IHtcbiAgaGVhZGluZ01haW46IFwiRXJyb3JlIGJhdCBhdXJraXR1IGRhLiBTYWlhdHUgPGJ1dHRvbj5vcnJpYSBiaXJrYXJnYXR6ZW4uPC9idXR0b24+XCIsXG4gIGNsZWFyQ2FudmFzTWVzc2FnZTogXCJCaXJrYXJnYXR6ZWEgZXogYmFkYSBidXJ1dHplbiwgc2FpYXR1IDxidXR0b24+b2loYWxhIGdhcmJpdHplbi48L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNDYXZlYXQ6IFwiIEhvbmVuIG9uZG9yaW96IGxhbmEgZ2FsZHVrbyBkYSBcIixcbiAgdHJhY2tlZFRvU2VudHJ5OiBcIklkZW50aWZpa2F0emFpbGVhcmVuIGVycm9yZWEge3tldmVudElkfX0gZ3VyZSBzaXN0ZW1hayBiZWhhdHUgZHUuXCIsXG4gIG9wZW5Jc3N1ZU1lc3NhZ2U6IFwiT3NvIGtvbnR1eiBpYmlsaSBnYXJhIHp1cmUgZXN6ZW5hcmVuIGluZm9ybWF6aW9hIGVycm9yZWFuIGV6IHNhcnR6ZWtvLiBadXJlIGVzemVuYSBwcmliYXR1YSBleiBiYWRhLCBrb250dWFuIGhhcnR1IGd1cmUgPGJ1dHRvbj5lcnJvcmVlbiBqYXJyYWlwZW5hIGVnaXRlYS48L2J1dHRvbj4gU2FydHUgYmVoZWtvIGluZm9ybWF6aW9hIGtvcGlhdHUgZXRhIGl0c2F0c2kgYmlkZXogR2l0SHViIGlzc3VlLW4uXCIsXG4gIHNjZW5lQ29udGVudDogXCJFc3plbmFyZW4gZWR1a2lhOlwiXG59O1xudmFyIHJvb21EaWFsb2cgPSB7XG4gIGRlc2NfaW50cm86IFwiSmVuZGVhIHp1cmUgdW5la28gZXN6ZW5hcmEgZ29uYmlkYSBkZXpha2V6dSB6dXJla2luIGVsa2FybGFuZWFuIGFyaXR6ZWtvLlwiLFxuICBkZXNjX3ByaXZhY3k6IFwiRXoga2V6a2F0dSwgc2Fpb2FrIG11dHVycmV0aWsgbXV0dXJyZXJha28gZW5rcmlwdGF0emVhIGVyYWJpbHR6ZW4gZHUsIGJlcmF6LCBtYXJyYXp0ZW4gZHV6dW5hIHByaWJhdHVhIGl6YW5nbyBkYS4gR3VyZSB6ZXJiaXR6YXJpYWsgZXJlIGV6aW5nbyBkdSBpa3VzaSB6ZXIgZWdpdGVuIGR1enVuLlwiLFxuICBidXR0b25fc3RhcnRTZXNzaW9uOiBcIkhhc2kgc2Fpb2FcIixcbiAgYnV0dG9uX3N0b3BTZXNzaW9uOiBcIkl0eGkgc2Fpb2FcIixcbiAgZGVzY19pblByb2dyZXNzSW50cm86IFwiWnV6ZW5la28gbGFua2lkZXR6YSBzYWlvYSBhYmlhbiBkYS5cIixcbiAgZGVzY19zaGFyZUxpbms6IFwiUGFydGVrYXR1IGVzdGVrYSBoYXUgZWxrYXJsYW5lYW4gYXJpdHUgbmFoaSBkdXp1biBlZG9ub3Jla2luOlwiLFxuICBkZXNjX2V4aXRTZXNzaW9uOiBcIlNhaW9hIGl4dGVhayBhcmV0b3RpayBkZXNrb25la3RhdHVrbyB6YWl0dSwgYmFpbmEgZXN6ZW5hcmVraW4gbGFuZWFuIGphcnJhaXR1IGFoYWwgaXphbmdvIGR1enUgbG9rYWxlYW4uIEtvbnR1YW4gaXphbiBob25layBleiBkaWVsYSBiZXN0ZSBwZXJ0c29uZWkgZXJhZ2luZ28sIGV0YSBldXJlbiBiZXJ0c2lvYW4gZWxrYXJsYW5lYW4gYXJpdHUgYWhhbCBpemFuZ28gZGlyYS5cIixcbiAgc2hhcmVUaXRsZTogXCJTYXJ0dSBFeGNhbGlkcmF3LWVuIHp1emVuZWtvIGxhbmtpZGV0emEtc2FpbyBiYXRlYW5cIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiRXJyb3JlYVwiXG59O1xudmFyIGV4cG9ydERpYWxvZyA9IHtcbiAgZGlza190aXRsZTogXCJHb3JkZSBkaXNrb2FuXCIsXG4gIGRpc2tfZGV0YWlsczogXCJFc3BvcnRhdHUgZXN6ZW5hcmVuIGRhdHVhayBnZXJvYWdvIGlucG9ydGF0dSBhaGFsIGl6YW5nbyBkdXp1biBmaXR4YXRlZ2kgYmF0YW4uXCIsXG4gIGRpc2tfYnV0dG9uOiBcIkdvcmRlIGZpdHhhdGVnaWFuXCIsXG4gIGxpbmtfdGl0bGU6IFwiUGFydGVrYXR6ZWtvIGVzdGVrYVwiLFxuICBsaW5rX2RldGFpbHM6IFwiRXNwb3J0YXR1IGlyYWt1cnR6ZWtvIHNvaWxpayBtb2R1a28gZXN0ZWthLlwiLFxuICBsaW5rX2J1dHRvbjogXCJFc3BvcnRhdHUgZXN0ZWthXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2Rlc2NyaXB0aW9uOiBcIkdvcmRlIGVzemVuYSB6dXJlIEV4Y2FsaWRyYXcrIGxhbmVrbyBhcmVhcmEuXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2J1dHRvbjogXCJFc3BvcnRhdHVcIixcbiAgZXhjYWxpZHJhd3BsdXNfZXhwb3J0RXJyb3I6IFwiVW5lIGhvbmV0YW4gZXppbiBpemFuIGRhIGVzcG9ydGF0dSBFeGNhbGlkcmF3K2VyYS4uLlwiXG59O1xudmFyIGhlbHBEaWFsb2cgPSB7XG4gIGJsb2c6IFwiSXJha3VycmkgZ3VyZSBibG9nYVwiLFxuICBjbGljazogXCJzYWthdHVcIixcbiAgZGVlcFNlbGVjdDogXCJIYXV0YXBlbiBzYWtvbmFcIixcbiAgZGVlcEJveFNlbGVjdDogXCJIYXV0YXBlbiBzYWtvbmEgZWdpbiBsYXVraXp1emVuIGJpZGV6LCBldGEgc2FpaGVzdHUgYXJyYXN0YXR6ZWFcIixcbiAgY3VydmVkQXJyb3c6IFwiR2V6aSBrdXJiYXR1YVwiLFxuICBjdXJ2ZWRMaW5lOiBcIkxlcnJvIGt1cmJhdHVhXCIsXG4gIGRvY3VtZW50YXRpb246IFwiRG9rdW1lbnRhemlvYVwiLFxuICBkb3VibGVDbGljazogXCJrbGlrIGJpa29pdHphXCIsXG4gIGRyYWc6IFwiYXJyYXN0YXR1XCIsXG4gIGVkaXRvcjogXCJFZGl0b3JlYVwiLFxuICBlZGl0TGluZUFycm93UG9pbnRzOiBcIkVkaXRhdHUgbGVycm9hay9nZXppLXB1bnR1YWtcIixcbiAgZWRpdFRleHQ6IFwiRWRpdGF0dSB0ZXN0dWEgLyBnZWhpdHUgZXRpa2V0YVwiLFxuICBnaXRodWI6IFwiQXJhem9yaWsgaXphbiBhbCBkdXp1PyBFbWFuIGhvcnJlbiBiZXJyaVwiLFxuICBob3d0bzogXCJKYXJyYWl0dSBndXJlIGdpZGFrXCIsXG4gIG9yOiBcImVkb1wiLFxuICBwcmV2ZW50QmluZGluZzogXCJTYWloZXN0dSBnZXppZW4gZ2FpbmphcnR6ZWFcIixcbiAgdG9vbHM6IFwiVHJlc25ha1wiLFxuICBzaG9ydGN1dHM6IFwiTGFzdGVyLXRla2xha1wiLFxuICB0ZXh0RmluaXNoOiBcIkJ1a2F0dSBlZGl6aW9hICh0ZXN0dSBlZGl0b3JlYSlcIixcbiAgdGV4dE5ld0xpbmU6IFwiR2VoaXR1IGxlcnJvIGJlcnJpIGJhdCAodGVzdHUgZWRpdG9yZWEpXCIsXG4gIHRpdGxlOiBcIkxhZ3VudHphXCIsXG4gIHZpZXc6IFwiQmlzdGFyYXR1XCIsXG4gIHpvb21Ub0ZpdDogXCJFZ2luIHpvb20gZWxlbWVudHUgZ3V6dGlhayBpa3VzdGVrb1wiLFxuICB6b29tVG9TZWxlY3Rpb246IFwiWm9vbWEgaGF1dGFwZW5lcmFcIixcbiAgdG9nZ2xlRWxlbWVudExvY2s6IFwiQmxva2VhdHUvZGVzYmxva2V0YXR1IGhhdXRhcGVuYVwiLFxuICBtb3ZlUGFnZVVwRG93bjogXCJNdWdpdHUgb3JyaWEgZ29yYS9iZWhlcmFcIixcbiAgbW92ZVBhZ2VMZWZ0UmlnaHQ6IFwiTXVnaXR1IG9ycmlhIGV6a2VyL2Vza3VpblwiXG59O1xudmFyIGNsZWFyQ2FudmFzRGlhbG9nID0ge1xuICB0aXRsZTogXCJHYXJiaXR1IG9paGFsYVwiXG59O1xudmFyIHB1Ymxpc2hEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIkFyZ2l0YXJhdHUgbGlidXJ1dGVnaWFcIixcbiAgaXRlbU5hbWU6IFwiRWxlbWVudHVhcmVuIGl6ZW5hXCIsXG4gIGF1dGhvck5hbWU6IFwiRWdpbGVhcmVuIGl6ZW5hXCIsXG4gIGdpdGh1YlVzZXJuYW1lOiBcIkdpdEh1Yi1la28gZXJhYmlsdHphaWxlLWl6ZW5hXCIsXG4gIHR3aXR0ZXJVc2VybmFtZTogXCJUd2l0dGVyLWVrbyBlcmFiaWx0emFpbGUtaXplbmFcIixcbiAgbGlicmFyeU5hbWU6IFwiTGlidXJ1dGVnaWFyZW4gaXplbmFcIixcbiAgbGlicmFyeURlc2M6IFwiTGlidXJ1dGVnaWFyZW4gZGVza3JpcHppb2FcIixcbiAgd2Vic2l0ZTogXCJXZWJndW5lYVwiLFxuICBwbGFjZWhvbGRlcjoge1xuICAgIGF1dGhvck5hbWU6IFwiWnVyZSBpemVuYSBlZG8gZXJhYmlsdHphaWxlLWl6ZW5hXCIsXG4gICAgbGlicmFyeU5hbWU6IFwiWnVyZSBsaWJ1cnV0ZWdpYXJlbiBpemVuYVwiLFxuICAgIGxpYnJhcnlEZXNjOiBcIlp1cmUgbGlidXJ1dGVnaWFyZW4gZGVza3JpcHppb2EgbGFndW50emVrbyBqZW5kZWFyaSB1bGVydHplbiBoYXJlbiBlcmFiaWxwZW5hXCIsXG4gICAgZ2l0aHViSGFuZGxlOiBcIkdpdEh1YiBoZWxkdWxla3VhIChhdWtlcmFrb2EpLCBsaWJ1cnV0ZWdpYSBlZGl0YXR1IGFoYWwgaXphdGVrbyBiZXJyaWt1c3RlcmEgYmlkYWxpdGFrb2FuXCIsXG4gICAgdHdpdHRlckhhbmRsZTogXCJUd2l0dGVyLWVrbyBlcmFiaWx0emFpbGUtaXplbmEgKGF1a2VyYWtvYSksIGJhZGFraWd1IG5vcmkga3JlZGl0YXR1IGJlaGFyIGR1Z3VuIFR3aXR0ZXIgYmlkZXogc3VzdGF0emVrb1wiLFxuICAgIHdlYnNpdGU6IFwiRXN0ZWthdHUgenVyZSB3ZWJndW5lcmEgZWRvIG5haGkgZHV6dW4gdG9raXJhIChhdWtlcmFrb2EpXCJcbiAgfSxcbiAgZXJyb3JzOiB7XG4gICAgcmVxdWlyZWQ6IFwiQmVoYXJyZXprb2FcIixcbiAgICB3ZWJzaXRlOiBcIlNhcnR1IGJhbGlvemtvIFVSTCBiYXRcIlxuICB9LFxuICBub3RlRGVzY3JpcHRpb246IFwiQmlkYWxpIHp1cmUgbGlidXJ1dGVnaXJhIHNhcnR1IGFoYWwgaXphdGVrbyA8bGluaz56dXJlIGxpYnVydXRlZ2lrbyBiaWx0ZWdpYW48L2xpbms+YmVzdGUgamVuZGVhayBiZXJlIG1hcnJhemtpZXRhbiBlcmFiaWxpIGFoYWwgaXphdGVrby5cIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiTGlidXJ1dGVnaWEgZXNrdXogb25hcnR1IGJlaGFyIGRhLiBJcmFrdXJyaSA8bGluaz5naWRhbGVycm9hazwvbGluaz4gYmlkYWxpIGF1cnJldGlrLiBHaXRIdWIga29udHUgYmF0IGVkdWtpdHplYSBrb21lbmkgZGEga29tdW5pa2F0emVrbyBldGEgYWxkYWtldGFrIGVnaW4gYWhhbCBpemF0ZWtvLCBiYWluYSBleiBkYSBndXp0aXogYmVoYXJyZXprb2EuXCIsXG4gIG5vdGVMaWNlbnNlOiBcIkJpZGFsdHplbiBiYWR1enUsIG9uYXJ0emVuIGR1enUgbGlidXJ1dGVnaWEgPGxpbms+TUlUIGxpemVudHppYXJla2luIGFyZ2l0YXJhdHVrbyBkZWxhLCA8L2xpbms+emVpbmFrLCBsYWJ1cmJpbGR1eiwgZXNhbiBuYWhpIGR1IGVkb3plaW5layBlcmFiaWx0emVuIGFoYWwgZHVlbGEgbXVycml6a2V0YXJpayBnYWJlLlwiLFxuICBub3RlSXRlbXM6IFwiTGlidXJ1dGVnaWtvIGVsZW1lbnR1IGJha29pdHphayBiZXJlIGl6ZW5hIGVkdWtpIGJlaGFyIGR1IGlyYWdhemkgYWhhbCBpemF0ZWtvLiBMaWJ1cnV0ZWdpa28gaHVycmVuZ28gZWxlbWVudHVhayBiYXJuZSBkYXVkZTpcIixcbiAgYXRsZWFzdE9uZUxpYkl0ZW06IFwiSGF1dGF0dSBndXR4aWVuZXogbGlidXJ1dGVnaWtvIGVsZW1lbnR1IGJhdCBndXR4aWVuZXogaGFzaSBhaGFsIGl6YXRla29cIixcbiAgcmVwdWJsaXNoV2FybmluZzogXCJPaGFycmE6IGhhdXRhdHV0YWtvIGVsZW1lbnR1IGJhdHp1ayBkYWdvZW5la28gYXJnaXRhcmF0dXRhL2JpZGFsaXRhIGJlemFsYSBtYXJrYXR1dGEgZGF1ZGUuIEVsZW1lbnR1YWsgYmVycmlybyBiaWRhbGkgYmVoYXIgZGl0dXp1IGxlaGVuZGlrIGRhZ29lbiBsaWJ1cnV0ZWdpIGVkbyBiaWRhbGtldGEgZWd1bmVyYXR6ZW4gZHV6dW5lYW4uXCJcbn07XG52YXIgcHVibGlzaFN1Y2Nlc3NEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIkxpYnVydXRlZ2lhIGJpZGFsaSBkYVwiLFxuICBjb250ZW50OiBcIkVza2VycmlrIGFza28ge3thdXRob3JOYW1lfX0uIFp1cmUgbGlidXJ1dGVnaWEgYmlkYWxpIGRhIGJlcnJpa3VzdGVyYS4gSmFycmFpdHUgZGV6YWtlenUgaGFyZW4gZWdvZXJhPGxpbms+aGVtZW48L2xpbms+XCJcbn07XG52YXIgY29uZmlybURpYWxvZyA9IHtcbiAgcmVzZXRMaWJyYXJ5OiBcIkxlaGVuZXJhdHUgbGlidXJ1dGVnaWFcIixcbiAgcmVtb3ZlSXRlbXNGcm9tTGliOiBcIktlbmR1IGhhdXRhdHV0YWtvIGVsZW1lbnR1YWsgbGlidXJ1dGVnaXRpa1wiXG59O1xudmFyIGltYWdlRXhwb3J0RGlhbG9nID0ge1xuICBoZWFkZXI6IFwiRXNwb3J0YXR1IGlydWRpYVwiLFxuICBsYWJlbDoge1xuICAgIHdpdGhCYWNrZ3JvdW5kOiBcIkF0emVrbyBwbGFub2FcIixcbiAgICBvbmx5U2VsZWN0ZWQ6IFwiSGF1dGFwZW5hIHNvaWxpa1wiLFxuICAgIGRhcmtNb2RlOiBcIk1vZHUgaWx1bmFcIixcbiAgICBlbWJlZFNjZW5lOiBcIlR4ZXJ0YXR1IGVzemVuYVwiLFxuICAgIHNjYWxlOiBcIkVza2FsYVwiLFxuICAgIHBhZGRpbmc6IFwiQmV0ZWdhcnJpYVwiXG4gIH0sXG4gIHRvb2x0aXA6IHtcbiAgICBlbWJlZFNjZW5lOiBcIkVzemVuYXJlbiBkYXR1YWsgZXNwb3J0YXR1dGFrbyBQTkcvU1ZHIGZpdHhhdGVnaWFuIGdvcmRla28gZGlyYSwgZXN6ZW5hIGJlcnRhdGlrIGJlcnJlemFydHpla28uXFxuRXNwb3J0YXR1dGFrbyBmaXR4YXRlZ2llbiB0YW1haW5hIGhhbmRpdHVrbyBkYS5cIlxuICB9LFxuICB0aXRsZToge1xuICAgIGV4cG9ydFRvUG5nOiBcIkVzcG9ydGF0dSBQTkcgZ2lzYVwiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIkVzcG9ydGF0dSBTVkcgZ2lzYVwiLFxuICAgIGNvcHlQbmdUb0NsaXBib2FyZDogXCJLb3BpYXR1IFBORyBhcmJlbGVyYVwiXG4gIH0sXG4gIGJ1dHRvbjoge1xuICAgIGV4cG9ydFRvUG5nOiBcIlBOR1wiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIlNWR1wiLFxuICAgIGNvcHlQbmdUb0NsaXBib2FyZDogXCJLb3BpYXR1IGFyYmVsZWFuXCJcbiAgfVxufTtcbnZhciBlbmNyeXB0ZWQgPSB7XG4gIHRvb2x0aXA6IFwiWnVyZSBtYXJyYXpraWFrIG11dHVycmV0aWsgbXV0dXJyZXJhIGVua3JpcHRhdHUgZGlyYSwgYmVyYXogRXhjYWxpZHJhdy1yZW4gemVyYml0emFyaWVrIGV6aW5nbyBkaXR1enRlIGlrdXNpLlwiLFxuICBsaW5rOiBcIkV4Y2FsaWRyYXctcmVuIG11dHVycmV0aWsgbXV0dXJyZXJha28gZW5rcmlwdGF0emVhcmVuIGdhaW5la28gbWV6dWEgYmxvZ2VhblwiXG59O1xudmFyIHN0YXRzID0ge1xuICBhbmdsZTogXCJBbmdlbHVhXCIsXG4gIGVsZW1lbnQ6IFwiRWxlbWVudHVhXCIsXG4gIGVsZW1lbnRzOiBcIkVsZW1lbnR1YWtcIixcbiAgaGVpZ2h0OiBcIkFsdHVlcmFcIixcbiAgc2NlbmU6IFwiRXN6ZW5hXCIsXG4gIHNlbGVjdGVkOiBcIkhhdXRhdHVhXCIsXG4gIHN0b3JhZ2U6IFwiQmlsdGVnaWFcIixcbiAgdGl0bGU6IFwiRGF0dWFrXCIsXG4gIHRvdGFsOiBcIkd1enRpcmFcIixcbiAgdmVyc2lvbjogXCJCZXJ0c2lvYVwiLFxuICB2ZXJzaW9uQ29weTogXCJLbGlrYXR1IGtvcGlhdHpla29cIixcbiAgdmVyc2lvbk5vdEF2YWlsYWJsZTogXCJCZXJ0c2lvIGV6IGVza3VyYWdhcnJpYVwiLFxuICB3aWR0aDogXCJaYWJhbGVyYVwiXG59O1xudmFyIHRvYXN0ID0ge1xuICBhZGRlZFRvTGlicmFyeTogXCJMaWJ1cnV0ZWdpcmEgZ2VoaXR1IGRhXCIsXG4gIGNvcHlTdHlsZXM6IFwiRXN0aWxvYWsga29waWF0dSBkaXJhLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiQXJiZWxlYW4ga29waWF0dSBkYS5cIixcbiAgY29weVRvQ2xpcGJvYXJkQXNQbmc6IFwie3tleHBvcnRTZWxlY3Rpb259fSBrb3BpYXR1IGRhIGFyYmVsZWFuIFBORyBnaXNhXFxuKHt7ZXhwb3J0Q29sb3JTY2hlbWV9fSlcIixcbiAgZmlsZVNhdmVkOiBcIkZpdHhhdGVnaWEgZ29yZGUgZGEuXCIsXG4gIGZpbGVTYXZlZFRvRmlsZW5hbWU6IFwie2ZpbGVuYW1lfS1uIGdvcmRlIGRhXCIsXG4gIGNhbnZhczogXCJvaWhhbGFcIixcbiAgc2VsZWN0aW9uOiBcImhhdXRhcGVuYVwiLFxuICBwYXN0ZUFzU2luZ2xlRWxlbWVudDogXCJFcmFiaWxpIHt7c2hvcnRjdXR9fSBlbGVtZW50dSBiYWthciBnaXNhIGl0c2FzdGVrbyxcXG5lZG8gaXRzYXRzaSBsZWhlbmRpayBkYWdvZW4gdGVzdHUtZWRpdG9yZSBiYXRlYW5cIixcbiAgdW5hYmxlVG9FbWJlZDogXCJVcmwgaGF1IHR4ZXJ0YXR6ZWEgZXogZGEgdW5lIGhvbmV0YW4gb25hcnR6ZW4uIFNvcnR1IGlzc3VlIGJhdCBHaXRIdWItZW4gVXJsYSB6ZXJyZW5kYSB6dXJpYW4gc2FydHplYSBlc2thdHpla29cIixcbiAgdW5yZWNvZ25pemVkTGlua0Zvcm1hdDogXCJLYXBzdWxhdHUgZHV6dW4gZXN0ZWthIGV6IGRhdG9yIGJhdCBlc3Blcm8gZGVuIGZvcm1hdHVhcmVraW4uIE1lc2VkZXosIHNhaWF0dSBpdHVyYnVydS1ndW5lYWsgZW1hbmRha28gJ2thcHN1bGF0dScga2F0ZWEgaXRzYXN0ZW5cIlxufTtcbnZhciBjb2xvcnMgPSB7XG4gIHRyYW5zcGFyZW50OiBcIkdhcmRlbmFcIixcbiAgYmxhY2s6IFwiQmVsdHphXCIsXG4gIHdoaXRlOiBcIlp1cmlhXCIsXG4gIHJlZDogXCJHb3JyaWFcIixcbiAgcGluazogXCJBcnJvc2FcIixcbiAgZ3JhcGU6IFwiTWFoYXRzIGtvbG9yZWFcIixcbiAgdmlvbGV0OiBcIkJpb2xldGFcIixcbiAgZ3JheTogXCJHcmlzYVwiLFxuICBibHVlOiBcIlVyZGluYVwiLFxuICBjeWFuOiBcIlppYW5hXCIsXG4gIHRlYWw6IFwiQmVyZGUgdXJkaW54a2FcIixcbiAgZ3JlZW46IFwiQmVyZGVhXCIsXG4gIHllbGxvdzogXCJIb3JpYVwiLFxuICBvcmFuZ2U6IFwiTGFyYW5qYVwiLFxuICBicm9uemU6IFwiQnJvbnR6ZWFcIlxufTtcbnZhciB3ZWxjb21lU2NyZWVuID0ge1xuICBhcHA6IHtcbiAgICBjZW50ZXJfaGVhZGluZzogXCJadXJlIGRhdHUgZ3V6dGlhayBsb2thbGVhbiBnb3JkZXR6ZW4gZGlyYSB6dXJlIG5hYmlnYXR6YWlsZWFuLlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nX3BsdXM6IFwiSG9ycmVuIG9yZGV6IEV4Y2FsaWRyYXcrLWVyYSBqb2FuIG5haGkgYWwgemVudWVuP1wiLFxuICAgIG1lbnVIaW50OiBcIkVzcG9ydGF0dSwgaG9iZXNwZW5haywgaGl6a3VudHphay4uLlwiXG4gIH0sXG4gIGRlZmF1bHRzOiB7XG4gICAgbWVudUhpbnQ6IFwiRXNwb3J0YXR1LCBob2Jlc3BlbmFrIGV0YSBnZWhpYWdvLi4uXCIsXG4gICAgY2VudGVyX2hlYWRpbmc6IFwiRGlhZ3JhbWFrLiBFZ2luYS4gU2lucGxlYS5cIixcbiAgICB0b29sYmFySGludDogXCJBdWtlcmF0dSB0cmVzbmEgYmF0IGV0YSBoYXNpIG1hcnJhenRlbiFcIixcbiAgICBoZWxwSGludDogXCJMYXN0ZXJiaWRlYWsgZXRhIGxhZ3VudHphXCJcbiAgfVxufTtcbnZhciBjb2xvclBpY2tlciA9IHtcbiAgbW9zdFVzZWRDdXN0b21Db2xvcnM6IFwiR2VoaWVuIGVyYWJpbGl0YWtvIGtvbG9yZSBwZXJ0c29uYWxpemF0dWFrXCIsXG4gIGNvbG9yczogXCJLb2xvcmVha1wiLFxuICBzaGFkZXM6IFwiXFx4RDFhYmFyZHVyYWtcIixcbiAgaGV4Q29kZTogXCJIZXoga29kZWFcIixcbiAgbm9TaGFkZXM6IFwiS29sb3JlIGhvbmV0YXJha28gZXogZGFnbyBcXHhGMWFiYXJkdXJhcmlrIGVza3VyYWdhcnJpXCJcbn07XG52YXIgb3ZlcndyaXRlQ29uZmlybSA9IHtcbiAgYWN0aW9uOiB7XG4gICAgZXhwb3J0VG9JbWFnZToge1xuICAgICAgdGl0bGU6IFwiRXNwb3J0YXR1IGlydWRpIGdpc2FcIixcbiAgICAgIGJ1dHRvbjogXCJFc3BvcnRhdHUgaXJ1ZGkgZ2lzYVwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRXNwb3J0YXR1IGVzemVuYXJlbiBkYXR1YWsgZ2Vyb2FnbyBpbnBvcnRhdHUgYWhhbCBpemFuZ28gZHV6dW4gaXJ1ZGkgZ2lzYS5cIlxuICAgIH0sXG4gICAgc2F2ZVRvRGlzazoge1xuICAgICAgdGl0bGU6IFwiR29yZGUgZGlza29hblwiLFxuICAgICAgYnV0dG9uOiBcIkdvcmRlIGRpc2tvYW5cIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkVzcG9ydGF0dSBlc3plbmFyZW4gZGF0dWFrIGdlcm9hZ28gaW5wb3J0YXR1IGFoYWwgaXphbmdvIGR1enVuIGZpdHhhdGVnaSBiYXRhbi5cIlxuICAgIH0sXG4gICAgZXhjYWxpZHJhd1BsdXM6IHtcbiAgICAgIHRpdGxlOiBcIkV4Y2FsaWRyYXcrXCIsXG4gICAgICBidXR0b246IFwiRXNwb3J0YXR1IEV4Y2FsaWRyYXcrcmFcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkdvcmRlIGVzemVuYSB6dXJlIEV4Y2FsaWRyYXcrIGxhbmVrbyBhcmVhcmEuXCJcbiAgICB9XG4gIH0sXG4gIG1vZGFsOiB7XG4gICAgbG9hZEZyb21GaWxlOiB7XG4gICAgICB0aXRsZTogXCJGaXR4YXRlZ2l0aWsga2FyZ2F0dVwiLFxuICAgICAgYnV0dG9uOiBcIkZpdHhhdGVnaXRpayBrYXJnYXR1XCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJGaXR4YXRlZ2kgYmF0ZXRpayBrYXJnYXR6ZWFrIDxib2xkPmxlaGVuZGlrIGR1enVuIGVkdWtpYSBvcmRlemthdHVrbyBkdTwvYm9sZD4uPGJyPjwvYnI+TGVoZW5lbmdvIG1hcnJhemtpYXJlbiBiYWJlc2tvcGlhIGVnaW4gZGV6YWtlenUgYmVoZWtvIGF1a2VyZXRha28gYmF0IGVyYWJpbGl6LlwiXG4gICAgfSxcbiAgICBzaGFyZWFibGVMaW5rOiB7XG4gICAgICB0aXRsZTogXCJFc3Rla2F0aWsga2FyZ2F0dVwiLFxuICAgICAgYnV0dG9uOiBcIk9yZGV6dHUgbmlyZSBlZHVraWFcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIkthbnBva28gaXJ1ZGkgYmF0IGthcmdhdHplYWsgPGJvbGQ+bGVoZW5kaWsgZHV6dW4gZWR1a2lhIG9yZGV6a2F0dWtvIGR1PC9ib2xkPi48YnI+PC9icj4uIFp1cmUgbWFycmF6a2lhcmVuIGJhYmVza29waWEgZWdpbiBkZXpha2V6dSBsZWhlbmlrIGJlaGVrbyBhdWtlcmV0YWtvIGJhdCBlcmFiaWxpei5cIlxuICAgIH1cbiAgfVxufTtcbnZhciBtZXJtYWlkID0ge1xuICB0aXRsZTogXCJcIixcbiAgYnV0dG9uOiBcIlR4ZXJ0YXR1XCIsXG4gIGRlc2NyaXB0aW9uOiBcIlwiLFxuICBzeW50YXg6IFwiXCIsXG4gIHByZXZpZXc6IFwiQXVycmViaXN0YVwiXG59O1xudmFyIGV1X0VTX2RlZmF1bHQgPSB7XG4gIGxhYmVscyxcbiAgbGlicmFyeSxcbiAgYnV0dG9ucyxcbiAgYWxlcnRzLFxuICBlcnJvcnMsXG4gIHRvb2xCYXIsXG4gIGhlYWRpbmdzLFxuICBoaW50cyxcbiAgY2FudmFzRXJyb3IsXG4gIGVycm9yU3BsYXNoLFxuICByb29tRGlhbG9nLFxuICBlcnJvckRpYWxvZyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWxwRGlhbG9nLFxuICBjbGVhckNhbnZhc0RpYWxvZyxcbiAgcHVibGlzaERpYWxvZyxcbiAgcHVibGlzaFN1Y2Nlc3NEaWFsb2csXG4gIGNvbmZpcm1EaWFsb2csXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBlbmNyeXB0ZWQsXG4gIHN0YXRzLFxuICB0b2FzdCxcbiAgY29sb3JzLFxuICB3ZWxjb21lU2NyZWVuLFxuICBjb2xvclBpY2tlcixcbiAgb3ZlcndyaXRlQ29uZmlybSxcbiAgbWVybWFpZFxufTtcbmV4cG9ydCB7XG4gIGFsZXJ0cyxcbiAgYnV0dG9ucyxcbiAgY2FudmFzRXJyb3IsXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBjb2xvclBpY2tlcixcbiAgY29sb3JzLFxuICBjb25maXJtRGlhbG9nLFxuICBldV9FU19kZWZhdWx0IGFzIGRlZmF1bHQsXG4gIGVuY3J5cHRlZCxcbiAgZXJyb3JEaWFsb2csXG4gIGVycm9yU3BsYXNoLFxuICBlcnJvcnMsXG4gIGV4cG9ydERpYWxvZyxcbiAgaGVhZGluZ3MsXG4gIGhlbHBEaWFsb2csXG4gIGhpbnRzLFxuICBpbWFnZUV4cG9ydERpYWxvZyxcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBtZXJtYWlkLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgcm9vbURpYWxvZyxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICB0b29sQmFyLFxuICB3ZWxjb21lU2NyZWVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXUtRVMtM1RPRVU1REUuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/eu-ES-3TOEU5DE.js\n"));

/***/ })

}]);