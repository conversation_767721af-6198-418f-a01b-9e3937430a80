"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_kk-KZ-UJPYGRQQ_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/kk-KZ-UJPYGRQQ.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/kk-KZ-UJPYGRQQ.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ kk_KZ_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/kk-KZ.json\nvar labels = {\n  paste: \"\\u049A\\u043E\\u044E\",\n  pasteAsPlaintext: \"\",\n  pasteCharts: \"\\u0414\\u0438\\u0430\\u0433\\u0440\\u0430\\u043C\\u043C\\u0430\\u043B\\u0430\\u0440\\u0434\\u044B \\u049B\\u043E\\u044E\",\n  selectAll: \"\\u0411\\u04D9\\u0440\\u0456\\u043D \\u0442\\u0430\\u04A3\\u0434\\u0430\\u0443\",\n  multiSelect: \"\",\n  moveCanvas: \"\",\n  cut: \"\\u049A\\u0438\\u044E\",\n  copy: \"\\u041A\\u04E9\\u0448\\u0456\\u0440\\u0443\",\n  copyAsPng: \"\",\n  copyAsSvg: \"\",\n  copyText: \"\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"\",\n  sendToBack: \"\",\n  bringToFront: \"\",\n  sendBackward: \"\",\n  delete: \"\\u0416\\u043E\\u044E\",\n  copyStyles: \"\\u0421\\u0442\\u0438\\u043B\\u044C\\u0434\\u0435\\u0440\\u0434\\u0456 \\u043A\\u04E9\\u0448\\u0456\\u0440\\u0443\",\n  pasteStyles: \"\\u0421\\u0442\\u0438\\u043B\\u044C\\u0434\\u0435\\u0440\\u0434\\u0456 \\u049B\\u043E\\u044E\",\n  stroke: \"\",\n  background: \"\",\n  fill: \"\",\n  strokeWidth: \"\",\n  strokeStyle: \"\",\n  strokeStyle_solid: \"\",\n  strokeStyle_dashed: \"\",\n  strokeStyle_dotted: \"\",\n  sloppiness: \"\",\n  opacity: \"\",\n  textAlign: \"\",\n  edges: \"\",\n  sharp: \"\",\n  round: \"\",\n  arrowheads: \"\\u041D\\u04B1\\u0441\\u049B\\u0430\\u0440 \\u04B1\\u0448\\u0442\\u0430\\u0440\\u044B\",\n  arrowhead_none: \"\\u0416\\u043E\\u049B\",\n  arrowhead_arrow: \"\\u041D\\u04B1\\u0441\\u049B\\u0430\\u0440\",\n  arrowhead_bar: \"\\u0422\\u043E\\u0441\\u049B\\u0430\\u0443\\u044B\\u043B\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"\\u049A\\u0430\\u0440\\u0456\\u043F \\u04E9\\u043B\\u0448\\u0435\\u043C\\u0456\",\n  fontFamily: \"\\u049A\\u0430\\u0440\\u0456\\u043F \\u0442\\u043E\\u0431\\u044B\",\n  addWatermark: \"\",\n  handDrawn: \"\",\n  normal: \"\\u049A\\u0430\\u043B\\u044B\\u043F\\u0442\\u044B\",\n  code: \"\",\n  small: \"\\u041A\\u0456\\u0448\\u0456\",\n  medium: \"\\u041E\\u0440\\u0442\\u0430\",\n  large: \"\\u04AE\\u043B\\u043A\\u0435\\u043D\",\n  veryLarge: \"\\u04E8\\u0442\\u0435 \\u04AF\\u043B\\u043A\\u0435\\u043D\",\n  solid: \"\",\n  hachure: \"\",\n  zigzag: \"\",\n  crossHatch: \"\",\n  thin: \"\",\n  bold: \"\",\n  left: \"\\u0421\\u043E\\u043B\\u0493\\u0430\",\n  center: \"\\u041E\\u0440\\u0442\\u0430\\u0493\\u0430\",\n  right: \"\\u041E\\u04A3\\u0493\\u0430\",\n  extraBold: \"\",\n  architect: \"\",\n  artist: \"\",\n  cartoonist: \"\",\n  fileTitle: \"\\u0424\\u0430\\u0439\\u043B \\u0430\\u0442\\u0430\\u0443\\u044B\",\n  colorPicker: \"\",\n  canvasColors: \"\",\n  canvasBackground: \"\",\n  drawingCanvas: \"\",\n  layers: \"\",\n  actions: \"\",\n  language: \"\\u0422\\u0456\\u043B\",\n  liveCollaboration: \"\",\n  duplicateSelection: \"\\u041A\\u04E9\\u0448\\u0456\\u0440\\u043C\\u0435\",\n  untitled: \"\\u0410\\u0442\\u0430\\u0443\\u044B\\u0441\\u044B\\u0437\",\n  name: \"\",\n  yourName: \"\",\n  madeWithExcalidraw: \"\",\n  group: \"\",\n  ungroup: \"\",\n  collaborators: \"\",\n  showGrid: \"\",\n  addToLibrary: \"\",\n  removeFromLibrary: \"\",\n  libraryLoadingMessage: \"\",\n  libraries: \"\",\n  loadingScene: \"\",\n  align: \"\",\n  alignTop: \"\",\n  alignBottom: \"\",\n  alignLeft: \"\",\n  alignRight: \"\",\n  centerVertically: \"\",\n  centerHorizontally: \"\",\n  distributeHorizontally: \"\",\n  distributeVertically: \"\",\n  flipHorizontal: \"\",\n  flipVertical: \"\",\n  viewMode: \"\",\n  share: \"\",\n  showStroke: \"\",\n  showBackground: \"\",\n  toggleTheme: \"\",\n  personalLib: \"\",\n  excalidrawLib: \"\",\n  decreaseFontSize: \"\",\n  increaseFontSize: \"\",\n  unbindText: \"\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"\",\n    editEmbed: \"\",\n    create: \"\",\n    createEmbed: \"\",\n    label: \"\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"\",\n    unlock: \"\",\n    lockAll: \"\",\n    unlockAll: \"\"\n  },\n  statusPublished: \"\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"\",\n  exportJSON: \"\",\n  exportImage: \"\",\n  export: \"\",\n  copyToClipboard: \"\",\n  save: \"\",\n  saveAs: \"\",\n  load: \"\",\n  getShareableLink: \"\",\n  close: \"\\u0416\\u0430\\u0431\\u0443\",\n  selectLanguage: \"\\u0422\\u0456\\u043B\\u0434\\u0456 \\u0442\\u0430\\u04A3\\u0434\\u0430\\u0443\",\n  scrollBackToContent: \"\",\n  zoomIn: \"\",\n  zoomOut: \"\",\n  resetZoom: \"\",\n  menu: \"M\\u04D9\\u0437\\u0456\\u0440\",\n  done: \"\\u0414\\u0430\\u0439\\u044B\\u043D\",\n  edit: \"\",\n  undo: \"\",\n  redo: \"\",\n  resetLibrary: \"\",\n  createNewRoom: \"\",\n  fullScreen: \"\",\n  darkMode: \"\",\n  lightMode: \"\",\n  zenMode: \"\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"\",\n  cancel: \"\",\n  clear: \"\",\n  remove: \"\",\n  embed: \"\",\n  publishLibrary: \"\",\n  submit: \"\",\n  confirm: \"\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"\",\n  couldNotCreateShareableLink: \"\",\n  couldNotCreateShareableLinkTooBig: \"\",\n  couldNotLoadInvalidFile: \"\",\n  importBackendFailed: \"\",\n  cannotExportEmptyCanvas: \"\",\n  couldNotCopyToClipboard: \"\",\n  decryptFailed: \"\",\n  uploadedSecurly: \"\",\n  loadSceneOverridePrompt: \"\",\n  collabStopOverridePrompt: \"\",\n  errorAddingToLibrary: \"\",\n  errorRemovingFromLibrary: \"\",\n  confirmAddLibrary: \"\",\n  imageDoesNotContainScene: \"\",\n  cannotRestoreFromImage: \"\",\n  invalidSceneUrl: \"\",\n  resetLibrary: \"\",\n  removeItemsFromsLibrary: \"\",\n  invalidEncryptionKey: \"\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"\",\n  imageInsertError: \"\\u0421\\u0443\\u0440\\u0435\\u0442\\u0442\\u0456 \\u0436\\u04AF\\u043A\\u0442\\u0435\\u0443 \\u043C\\u04AF\\u043C\\u043A\\u0456\\u043D \\u0431\\u043E\\u043B\\u043C\\u0430\\u0434\\u044B. \\u041A\\u0435\\u0439\\u0456\\u043D\\u0456\\u0440\\u0435\\u043A \\u049B\\u0430\\u0439\\u0442\\u0430\\u043B\\u0430\\u043F \\u043A\\u04E9\\u0440\\u0456\\u04A3\\u0456\\u0437...\",\n  fileTooBig: \"\\u0424\\u0430\\u0439\\u043B \\u04E9\\u0442\\u0435 \\u04AF\\u043B\\u043A\\u0435\\u043D. \\u041C\\u0430\\u043A\\u0441\\u0438\\u043C\\u0430\\u043B\\u0434\\u044B \\u0440\\u04B1\\u049B\\u0441\\u0430\\u0442 \\u0435\\u0442\\u0456\\u043B\\u0433\\u0435\\u043D \\u043A\\u04E9\\u043B\\u0435\\u043C {{maxSize}}.\",\n  svgImageInsertError: \"\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"\",\n  cannotResolveCollabServer: \"\",\n  importLibraryError: \"\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"\",\n  image: \"\\u0421\\u0443\\u0440\\u0435\\u0442\\u0442\\u0456 \\u049B\\u043E\\u044E\",\n  rectangle: \"\",\n  diamond: \"\",\n  ellipse: \"\",\n  arrow: \"\\u041D\\u04B1\\u0441\\u049B\\u0430\\u0440\",\n  line: \"\",\n  freedraw: \"\",\n  text: \"\\u041C\\u04D9\\u0442\\u0456\\u043D\",\n  library: \"\",\n  lock: \"\",\n  penMode: \"\",\n  link: \"\",\n  eraser: \"\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"\",\n  selectedShapeActions: \"\",\n  shapes: \"\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"\",\n  freeDraw: \"\",\n  text: \"\",\n  embeddable: \"\",\n  text_selected: \"\",\n  text_editing: \"\",\n  linearElementMulti: \"\",\n  lockAngle: \"\",\n  resize: \"\",\n  resizeImage: \"\",\n  rotate: \"\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\",\n  canvasTooBig: \"\",\n  canvasTooBigTip: \"\"\n};\nvar errorSplash = {\n  headingMain: \"\",\n  clearCanvasMessage: \"\",\n  clearCanvasCaveat: \"\",\n  trackedToSentry: \"\",\n  openIssueMessage: \"\",\n  sceneContent: \"\"\n};\nvar roomDialog = {\n  desc_intro: \"\",\n  desc_privacy: \"\",\n  button_startSession: \"\",\n  button_stopSession: \"\",\n  desc_inProgressIntro: \"\",\n  desc_shareLink: \"\",\n  desc_exitSession: \"\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"\\u049A\\u0430\\u0442\\u0435\"\n};\nvar exportDialog = {\n  disk_title: \"\",\n  disk_details: \"\\u0421\\u0430\\u0445\\u043D\\u0430 \\u0434\\u0435\\u0440\\u0435\\u043A\\u0442\\u0435\\u0440\\u0456\\u043D \\u043A\\u0435\\u0439\\u0456\\u043D \\u049B\\u0430\\u0439\\u0442\\u0430 \\u0438\\u043C\\u043F\\u043E\\u0440\\u0442\\u0442\\u0430\\u0443\\u0493\\u0430 \\u0431\\u043E\\u043B\\u0430\\u0442\\u044B\\u043D \\u0444\\u0430\\u0439\\u043B\\u0493\\u0430 \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0442\\u0430\\u04A3\\u044B\\u0437.\",\n  disk_button: \"\\u0424\\u0430\\u0439\\u043B\\u0493\\u0430 \\u0441\\u0430\\u049B\\u0442\\u0430\\u0443\",\n  link_title: \"\\u041E\\u0440\\u0442\\u0430\\u049B \\u0441\\u0456\\u043B\\u0442\\u0435\\u043C\\u0435\",\n  link_details: \"\\u0422\\u0435\\u043A \\u043E\\u049B\\u0443\\u0493\\u0430 \\u0430\\u0440\\u043D\\u0430\\u043B\\u0493\\u0430\\u043D \\u0441\\u0456\\u043B\\u0442\\u0435\\u043C\\u0435 \\u0440\\u0435\\u0442\\u0456\\u043D\\u0434\\u0435 \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0442\\u0430\\u0443.\",\n  link_button: \"\\u0421\\u0456\\u043B\\u0442\\u0435\\u043C\\u0435\\u0433\\u0435 \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0442\\u0430\\u0443\",\n  excalidrawplus_description: \"\\u0421\\u0430\\u0445\\u043D\\u0430\\u043D\\u044B \\u04E9\\u0437\\u0456\\u04A3\\u0456\\u0437\\u0434\\u0456\\u04A3 Excalidraw+ \\u0436\\u04B1\\u043C\\u044B\\u0441 \\u043A\\u0435\\u04A3\\u0456\\u0441\\u0442\\u0456\\u0433\\u0456\\u043D\\u0434\\u0435 \\u0441\\u0430\\u049B\\u0442\\u0430\\u04A3\\u044B\\u0437.\",\n  excalidrawplus_button: \"\\u042D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\",\n  excalidrawplus_exportError: \"\\u049A\\u0430\\u0437\\u0456\\u0440\\u0433\\u0456 \\u0443\\u0430\\u049B\\u044B\\u0442\\u0442\\u0430 Excalidraw+ \\u04AF\\u0448\\u0456\\u043D \\u044D\\u043A\\u0441\\u043F\\u043E\\u0440\\u0442\\u0442\\u0430\\u0443 \\u043C\\u04AF\\u043C\\u043A\\u0456\\u043D \\u0435\\u043C\\u0435\\u0441...\"\n};\nvar helpDialog = {\n  blog: \"\\u0411\\u0456\\u0437\\u0434\\u0456\\u04A3 \\u0431\\u043B\\u043E\\u0433\\u0442\\u044B \\u043E\\u049B\\u0443\",\n  click: \"\\u0448\\u0435\\u0440\\u0442\\u0443\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"\\u041C\\u0430\\u0439\\u044B\\u0441\\u049B\\u0430\\u043D \\u043D\\u04B1\\u0441\\u049B\\u0430\\u0440\",\n  curvedLine: \"\\u041C\\u0430\\u0439\\u044B\\u0441\\u049B\\u0430\\u043D \\u0441\\u044B\\u0437\\u044B\\u049B\",\n  documentation: \"\\u049A\\u04B1\\u0436\\u0430\\u0442\\u0442\\u0430\\u043C\\u0430\",\n  doubleClick: \"\\u049B\\u043E\\u0441 \\u0448\\u0435\\u0440\\u0442\\u0443\",\n  drag: \"\\u0430\\u043F\\u0430\\u0440\\u0443\",\n  editor: \"\\u04E8\\u04A3\\u0434\\u0435\\u0443\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"\\u049A\\u0430\\u0442\\u0435 \\u0442\\u0430\\u043F\\u0442\\u044B\\u04A3\\u044B\\u0437 \\u0431\\u0430? \\u0416\\u043E\\u043B\\u0434\\u0430\\u04A3\\u044B\\u0437\",\n  howto: \"\\u0411\\u0456\\u0437\\u0434\\u0456\\u04A3 \\u043D\\u04B1\\u0441\\u049B\\u0430\\u0443\\u043B\\u044B\\u049B\\u0442\\u0430\\u0440\\u0434\\u044B \\u043E\\u0440\\u044B\\u043D\\u0434\\u0430\\u04A3\\u044B\\u0437\",\n  or: \"\\u043D\\u0435\\u043C\\u0435\\u0441\\u0435\",\n  preventBinding: \"\\u041D\\u04B1\\u0441\\u049B\\u0430\\u0440\\u0434\\u044B \\u0431\\u0430\\u0439\\u043B\\u0430\\u043D\\u044B\\u0441\\u0442\\u044B\\u0440\\u0443\\u0493\\u0430 \\u0436\\u043E\\u043B \\u0431\\u0435\\u0440\\u043C\\u0435\\u0443\",\n  tools: \"\",\n  shortcuts: \"\\u041F\\u0435\\u0440\\u043D\\u0435\\u0442\\u0430\\u049B\\u0442\\u0430 \\u043F\\u04D9\\u0440\\u043C\\u0435\\u043D\\u0434\\u0435\\u0440\\u0456\",\n  textFinish: \"\\u04E8\\u04A3\\u0434\\u0435\\u0443\\u0434\\u0456 \\u0430\\u044F\\u049B\\u0442\\u0430\\u0443 (\\u043C\\u04D9\\u0442\\u0456\\u043D\\u0434\\u0456\\u043A \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u043E\\u0440)\",\n  textNewLine: \"\\u0416\\u0430\\u04A3\\u0430 \\u0436\\u043E\\u043B\\u0493\\u0430 \\u043A\\u04E9\\u0448\\u0443 (\\u043C\\u04D9\\u0442\\u0456\\u043D\\u0434\\u0456\\u043A \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u043E\\u0440)\",\n  title: \"\\u041A\\u04E9\\u043C\\u0435\\u043A\",\n  view: \"\\u041A\\u04E9\\u0440\\u0443\",\n  zoomToFit: \"\\u0411\\u0430\\u0440\\u043B\\u044B\\u049B \\u044D\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\\u0442\\u0435\\u0440\\u0434\\u0456\\u04A3 \\u043A\\u04E9\\u043B\\u0435\\u043C\\u0456\\u043D\\u0435 \\u0441\\u04D9\\u0439\\u043A\\u0435\\u0441 \\u04AF\\u043B\\u043A\\u0435\\u0439\\u0442\\u0443\",\n  zoomToSelection: \"\\u0422\\u0430\\u04A3\\u0434\\u0430\\u043B\\u0493\\u0430\\u043D\\u0434\\u044B \\u04AF\\u043B\\u043A\\u0435\\u0439\\u0442\\u0443\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\"\n};\nvar clearCanvasDialog = {\n  title: \"\"\n};\nvar publishDialog = {\n  title: \"\",\n  itemName: \"\",\n  authorName: \"\",\n  githubUsername: \"\",\n  twitterUsername: \"\",\n  libraryName: \"\",\n  libraryDesc: \"\",\n  website: \"\",\n  placeholder: {\n    authorName: \"\",\n    libraryName: \"\",\n    libraryDesc: \"\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"\",\n    website: \"\"\n  },\n  noteDescription: \"\",\n  noteGuidelines: \"\",\n  noteLicense: \"\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"\",\n  content: \"\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\",\n  removeItemsFromLib: \"\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\\u0421\\u0456\\u0437\\u0434\\u0456\\u04A3 \\u0441\\u044B\\u0437\\u0431\\u0430\\u043B\\u0430\\u0440\\u044B\\u04A3\\u044B\\u0437 \\u04E9\\u0442\\u043F\\u0435\\u043B\\u0456 \\u0448\\u0438\\u0444\\u0440\\u043B\\u0435\\u0443 \\u0430\\u0440\\u049B\\u044B\\u043B\\u044B \\u0448\\u0438\\u0444\\u0440\\u043B\\u0430\\u043D\\u0493\\u0430\\u043D, \\u0441\\u043E\\u043D\\u0434\\u044B\\u049B\\u0442\\u0430\\u043D Excalidraw \\u0441\\u0435\\u0440\\u0432\\u0435\\u0440\\u043B\\u0435\\u0440\\u0456 \\u043E\\u043B\\u0430\\u0440\\u0434\\u044B \\u0435\\u0448\\u049B\\u0430\\u0448\\u0430\\u043D \\u043A\\u04E9\\u0440\\u043C\\u0435\\u0439\\u0434\\u0456.\",\n  link: \"Excalidraw \\u049B\\u043E\\u043B\\u0434\\u0430\\u043D\\u0430\\u0442\\u044B\\u043D \\u04E9\\u0442\\u043F\\u0435\\u043B\\u0456 \\u0448\\u0438\\u0444\\u0440\\u043B\\u0435\\u0443 \\u0442\\u0443\\u0440\\u0430\\u043B\\u044B \\u0431\\u043B\\u043E\\u0433 \\u0436\\u0430\\u0437\\u0431\\u0430\\u0441\\u044B\"\n};\nvar stats = {\n  angle: \"\\u0411\\u04B1\\u0440\\u044B\\u0448\",\n  element: \"\\u042D\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\",\n  elements: \"\\u042D\\u043B\\u0435\\u043C\\u0435\\u043D\\u0442\\u0442\\u0435\\u0440\",\n  height: \"\\u0411\\u0438\\u0456\\u043A\\u0442\\u0456\\u0433\\u0456\",\n  scene: \"\\u0421\\u0430\\u0445\\u043D\\u0430\",\n  selected: \"\\u0422\\u0430\\u04A3\\u0434\\u0430\\u043B\\u0434\\u044B\",\n  storage: \"\\u0421\\u0430\\u049B\\u0442\\u0430\\u0443 \\u043A\\u04E9\\u043B\\u0435\\u043C\\u0456\",\n  title: \"\",\n  total: \"\\u0411\\u0430\\u0440\\u043B\\u044B\\u0493\\u044B\",\n  version: \"\\u041D\\u04B1\\u0441\\u049B\\u0430\",\n  versionCopy: \"\\u041A\\u04E9\\u0448\\u0456\\u0440\\u0443 \\u04AF\\u0448\\u0456\\u043D \\u0431\\u0430\\u0441\\u044B\\u04A3\\u044B\\u0437\",\n  versionNotAvailable: \"\\u0411\\u04B1\\u043B \\u043D\\u04B1\\u0441\\u049B\\u0430 \\u049B\\u043E\\u043B\\u0436\\u0435\\u0442\\u0456\\u043C\\u0441\\u0456\\u0437\",\n  width: \"\\u0415\\u043D\\u0456\"\n};\nvar toast = {\n  addedToLibrary: \"\",\n  copyStyles: \"\\u0421\\u0442\\u0438\\u043B\\u044C\\u0434\\u0435\\u0440 \\u043A\\u04E9\\u0448\\u0456\\u0440\\u0456\\u043B\\u0434\\u0456.\",\n  copyToClipboard: \"\",\n  copyToClipboardAsPng: \"\",\n  fileSaved: \"\\u0424\\u0430\\u0439\\u043B \\u0441\\u0430\\u049B\\u0442\\u0430\\u043B\\u0434\\u044B.\",\n  fileSavedToFilename: \"{filename} \\u0441\\u0430\\u049B\\u0442\\u0430\\u043B\\u0434\\u044B\",\n  canvas: \"\",\n  selection: \"\\u0442\\u0430\\u04A3\\u0434\\u0430\\u0443\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar kk_KZ_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=kk-KZ-UJPYGRQQ.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/kk-KZ-UJPYGRQQ.js\n"));

/***/ })

}]);