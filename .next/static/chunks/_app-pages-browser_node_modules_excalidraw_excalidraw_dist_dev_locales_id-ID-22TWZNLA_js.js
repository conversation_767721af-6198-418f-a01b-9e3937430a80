"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_id-ID-22TWZNLA_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/id-ID-22TWZNLA.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/id-ID-22TWZNLA.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ id_ID_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/id-ID.json\nvar labels = {\n  paste: \"Tempel\",\n  pasteAsPlaintext: \"Tempel sebagai teks biasa\",\n  pasteCharts: \"Tempel diagram\",\n  selectAll: \"Pilih semua\",\n  multiSelect: \"Tambahkan elemen ke pilihan\",\n  moveCanvas: \"Pindahkan kanvas\",\n  cut: \"Potong\",\n  copy: \"Salin\",\n  copyAsPng: \"Salin ke papan klip sebagai PNG\",\n  copyAsSvg: \"Salin ke papan klip sebagai SVG\",\n  copyText: \"Salin ke papan klip sebagai teks\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Bawa maju\",\n  sendToBack: \"Kirim ke belakang\",\n  bringToFront: \"Bawa ke depan\",\n  sendBackward: \"Kirim mundur\",\n  delete: \"Hapus\",\n  copyStyles: \"Salin gaya\",\n  pasteStyles: \"Tempelkan gaya\",\n  stroke: \"Guratan\",\n  background: \"Latar\",\n  fill: \"Isian\",\n  strokeWidth: \"Lebar guratan\",\n  strokeStyle: \"Gaya guratan\",\n  strokeStyle_solid: \"Padat\",\n  strokeStyle_dashed: \"Putus-putus\",\n  strokeStyle_dotted: \"Titik-titik\",\n  sloppiness: \"Kecerobohan\",\n  opacity: \"Keburaman\",\n  textAlign: \"Perataan teks\",\n  edges: \"Tepi\",\n  sharp: \"Tajam\",\n  round: \"Bulat\",\n  arrowheads: \"Mata panah\",\n  arrowhead_none: \"Tidak ada\",\n  arrowhead_arrow: \"Panah\",\n  arrowhead_bar: \"Batang\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Segitiga\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Ukuran font\",\n  fontFamily: \"Jenis font\",\n  addWatermark: 'Tambahkan \"Dibuat dengan Excalidraw\"',\n  handDrawn: \"Tulisan tangan\",\n  normal: \"Normal\",\n  code: \"Kode\",\n  small: \"Kecil\",\n  medium: \"Sedang\",\n  large: \"Besar\",\n  veryLarge: \"Sangat besar\",\n  solid: \"Padat\",\n  hachure: \"Garis-garis\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Asiran silang\",\n  thin: \"Lembut\",\n  bold: \"Tebal\",\n  left: \"Kiri\",\n  center: \"Tengah\",\n  right: \"Kanan\",\n  extraBold: \"Sangat tebal\",\n  architect: \"Arsitek\",\n  artist: \"Artis\",\n  cartoonist: \"Kartunis\",\n  fileTitle: \"Nama file\",\n  colorPicker: \"Pilihan Warna\",\n  canvasColors: \"Digunakan di kanvas\",\n  canvasBackground: \"Latar Kanvas\",\n  drawingCanvas: \"Kanvas\",\n  layers: \"Lapisan\",\n  actions: \"Aksi\",\n  language: \"Bahasa\",\n  liveCollaboration: \"Kolaborasi langsung...\",\n  duplicateSelection: \"Duplikat\",\n  untitled: \"Tanpa judul\",\n  name: \"Nama\",\n  yourName: \"Nama Anda\",\n  madeWithExcalidraw: \"Dibuat dengan Excalidraw\",\n  group: \"Kelompokan pilihan\",\n  ungroup: \"Pisahkan pilihan\",\n  collaborators: \"Kolaborator\",\n  showGrid: \"Tampilkan grid\",\n  addToLibrary: \"Tambahkan ke pustaka\",\n  removeFromLibrary: \"Hapus dari pustaka\",\n  libraryLoadingMessage: \"Memuat pustaka\\u2026\",\n  libraries: \"Telusur pustaka\",\n  loadingScene: \"Memuat pemandangan\\u2026\",\n  align: \"Perataan\",\n  alignTop: \"Rata atas\",\n  alignBottom: \"Rata bawah\",\n  alignLeft: \"Rata kiri\",\n  alignRight: \"Rata kanan\",\n  centerVertically: \"Pusatkan secara vertikal\",\n  centerHorizontally: \"Pusatkan secara horizontal\",\n  distributeHorizontally: \"Distribusikan horizontal\",\n  distributeVertically: \"Distribusikan vertikal\",\n  flipHorizontal: \"Balikkan horizontal\",\n  flipVertical: \"Balikkan vertikal\",\n  viewMode: \"Mode tampilan\",\n  share: \"Bagikan\",\n  showStroke: \"Tampilkan garis pengambil warna\",\n  showBackground: \"Tampilkan latar pengambil warna\",\n  toggleTheme: \"Ubah tema\",\n  personalLib: \"Pustaka Pribadi\",\n  excalidrawLib: \"Pustaka Excalidraw\",\n  decreaseFontSize: \"Kecilkan ukuran font\",\n  increaseFontSize: \"Besarkan ukuran font\",\n  unbindText: \"Lepas teks\",\n  bindText: \"Kunci teks ke kontainer\",\n  createContainerFromText: \"Bungkus teks dalam kontainer\",\n  link: {\n    edit: \"Edit tautan\",\n    editEmbed: \"\",\n    create: \"Buat tautan\",\n    createEmbed: \"\",\n    label: \"Tautan\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Edit tautan\",\n    exit: \"Keluar editor garis\"\n  },\n  elementLock: {\n    lock: \"Kunci\",\n    unlock: \"Lepas\",\n    lockAll: \"Kunci semua\",\n    unlockAll: \"Lepas semua\"\n  },\n  statusPublished: \"Telah terbit\",\n  sidebarLock: \"Biarkan sidebar tetap terbuka\",\n  selectAllElementsInFrame: \"Pilih semua elemen di bingkai\",\n  removeAllElementsFromFrame: \"Hapus semua elemen dari bingkai\",\n  eyeDropper: \"Ambil warna dari kanvas\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Belum ada item yang ditambahkan...\",\n  hint_emptyLibrary: \"Pilih item pada kanvas untuk menambahkan nya di sini, atau pasang pustaka dari gudang di bawah ini.\",\n  hint_emptyPrivateLibrary: \"Pilih item pada kanvas untuk menambahkan nya di sini.\"\n};\nvar buttons = {\n  clearReset: \"Setel Ulang Kanvas\",\n  exportJSON: \"Ekspor ke file\",\n  exportImage: \"Ekspor gambar...\",\n  export: \"Simpan ke...\",\n  copyToClipboard: \"Salin ke Papan Klip\",\n  save: \"Simpan ke file sekarang\",\n  saveAs: \"Simpan sebagai\",\n  load: \"Buka\",\n  getShareableLink: \"Buat Tautan yang Bisa Dibagian\",\n  close: \"Tutup\",\n  selectLanguage: \"Pilih bahasa\",\n  scrollBackToContent: \"Gulir kembali ke konten\",\n  zoomIn: \"Besarkan\",\n  zoomOut: \"Kecilkan\",\n  resetZoom: \"Reset Pembesaran\",\n  menu: \"Menu\",\n  done: \"Selesai\",\n  edit: \"Edit\",\n  undo: \"Urungkan\",\n  redo: \"Ulangi\",\n  resetLibrary: \"Reset pustaka\",\n  createNewRoom: \"Buat ruang baru\",\n  fullScreen: \"Layar penuh\",\n  darkMode: \"Mode gelap\",\n  lightMode: \"Mode terang\",\n  zenMode: \"Mode zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Keluar dari mode zen\",\n  cancel: \"Batal\",\n  clear: \"Hapus\",\n  remove: \"Hapus\",\n  embed: \"\",\n  publishLibrary: \"Terbitkan\",\n  submit: \"Kirimkan\",\n  confirm: \"Konfirmasi\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Ini akan menghapus semua yang ada dikanvas. Apakah kamu yakin ?\",\n  couldNotCreateShareableLink: \"Tidak bisa membuat tautan yang bisa dibagikan\",\n  couldNotCreateShareableLinkTooBig: \"Tidak dapat membuat tautan yang dapat dibagikan: pemandangan terlalu besar\",\n  couldNotLoadInvalidFile: \"Tidak dapat memuat berkas yang tidak valid\",\n  importBackendFailed: \"Gagal mengimpor dari backend\",\n  cannotExportEmptyCanvas: \"Tidak bisa mengekspor kanvas kosong\",\n  couldNotCopyToClipboard: \"Tidak bisa menyalin ke papan klip.\",\n  decryptFailed: \"Tidak dapat mengdekripsi data.\",\n  uploadedSecurly: \"Pengunggahan ini telah diamankan menggunakan enkripsi end-to-end, artinya server Excalidraw dan pihak ketiga tidak data membaca nya\",\n  loadSceneOverridePrompt: \"Memuat gambar external akan mengganti konten Anda yang ada. Apakah Anda ingin melanjutkan?\",\n  collabStopOverridePrompt: \"Menghentikan sesi akan menimpa gambar Anda yang tersimpan secara lokal. Anda yakin?\\n\\n(Jika Anda ingin menyimpan gambar lokal Anda, gantinya cukup tutup tab browser.)\",\n  errorAddingToLibrary: \"Tidak dapat menambahkan item ke pustaka\",\n  errorRemovingFromLibrary: \"Tidak dapat membuang item dari pustaka\",\n  confirmAddLibrary: \"Ini akan menambahkan {{numShapes}} bentuk ke pustaka Anda. Anda yakin?\",\n  imageDoesNotContainScene: \"Gambar ini sepertinya tidak terdapat data pemandangan. Sudahkah Anda mengaktifkan penyematan pemandangan ketika ekspor?\",\n  cannotRestoreFromImage: \"Pemandangan tidak dapat dipulihkan dari file gambar ini\",\n  invalidSceneUrl: \"Tidak dapat impor pemandangan dari URL. Kemungkinan URL itu rusak atau tidak berisi data JSON Excalidraw yang valid.\",\n  resetLibrary: \"Ini akan menghapus pustaka Anda. Anda yakin?\",\n  removeItemsFromsLibrary: \"Hapus {{count}} item dari pustaka?\",\n  invalidEncryptionKey: \"Sandi enkripsi harus 22 karakter. Kolaborasi langsung dinonaktifkan.\",\n  collabOfflineWarning: \"Tidak ada koneksi internet.\\nPerubahan tidak akan disimpan!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipe file tidak didukung.\",\n  imageInsertError: \"Tidak dapat menyisipkan gambar. Coba lagi nanti...\",\n  fileTooBig: \"File terlalu besar. Ukuran maksimum yang dibolehkan {{maxSize}}.\",\n  svgImageInsertError: \"Tidak dapat menyisipkan gambar SVG. Markup SVG sepertinya tidak valid.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG tidak valid.\",\n  cannotResolveCollabServer: \"Tidak dapat terhubung ke server kolab. Muat ulang laman dan coba lagi.\",\n  importLibraryError: \"Tidak dapat memuat pustaka\",\n  collabSaveFailed: \"Tidak dapat menyimpan ke dalam basis data server. Jika masih berlanjut, Anda sebaiknya simpan berkas Anda secara lokal untuk memastikan pekerjaan Anda tidak hilang.\",\n  collabSaveFailed_sizeExceeded: \"Tidak dapat menyimpan ke dalam basis data server, tampaknya ukuran kanvas terlalu besar. Anda sebaiknya simpan berkas Anda secara lokal untuk memastikan pekerjaan Anda tidak hilang.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"Sepertinya Anda menggunkan peramban Brave dengan pengaturan <bold>Blokir Fingerprinting yang Agresif</bold> diaktifkan.\",\n    line2: \"Ini dapat membuat <bold>Elemen Teks</bold> dalam gambar mu.\",\n    line3: \"Kami sangat menyarankan mematikan pengaturan ini. Anda dapat mengikuti <link>langkah-langkah ini</link> untuk melakukannya.\",\n    line4: \"Jika mematikan pengaturan ini tidak membenarkan tampilan elemen teks, mohon buka\\n<issueLink>isu</issueLink> di GitHub kami, atau chat kami di <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Pilihan\",\n  image: \"Sisipkan gambar\",\n  rectangle: \"Persegi\",\n  diamond: \"Berlian\",\n  ellipse: \"Elips\",\n  arrow: \"Panah\",\n  line: \"Garis\",\n  freedraw: \"Gambar\",\n  text: \"Teks\",\n  library: \"Pustaka\",\n  lock: \"Biarkan alat yang dipilih aktif setelah menggambar\",\n  penMode: \"Mode pena - mencegah sentuhan\",\n  link: \"Tambah/Perbarui tautan untuk bentuk yang dipilih\",\n  eraser: \"Penghapus\",\n  frame: \"Alat bingkai\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"Tangan (alat panning)\",\n  extraTools: \"Alat-alat lain\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Opsi Kanvas\",\n  selectedShapeActions: \"Opsi bentuk yang dipilih\",\n  shapes: \"Bentuk\"\n};\nvar hints = {\n  canvasPanning: \"Untuk memindahkan kanvas, tekan roda mouse atau spacebar sambil menyeret, atau menggunakan alat tangan\",\n  linearElement: \"Klik untuk memulai banyak poin, seret untuk satu baris\",\n  freeDraw: \"Klik dan seret, lepaskan jika Anda selesai\",\n  text: \"Tip: Anda juga dapat menambahkan teks dengan klik ganda di mana saja dengan alat pemilihan\",\n  embeddable: \"\",\n  text_selected: \"Klik ganda atau tekan ENTER untuk edit teks\",\n  text_editing: \"Tekan Escape atau CtrlAtauCmd+ENTER untuk selesai mengedit\",\n  linearElementMulti: \"Klik pada titik akhir atau tekan Escape atau Enter untuk menyelesaikan\",\n  lockAngle: \"Anda dapat menjaga sudut dengan menahan SHIFT\",\n  resize: \"Anda dapat menjaga proposi dengan menekan SHIFT sambil mengubah ukuran,\\ntekan AlT untuk mengubah ukuran dari tengah\",\n  resizeImage: \"Anda dapat mengubah secara bebas dengan menekan SHIFT,\\nTekan ALT untuk mengubah dari tengah\",\n  rotate: \"Anda dapat menjaga sudut dengan menahan SHIFT sambil memutar\",\n  lineEditor_info: \"Tekan Ctrl/Cmd dan Dobel-klik atau tekan Ctrl/Cmd +Enter untuk mengedit poin\",\n  lineEditor_pointSelected: \"Tekan Delete untuk menghapus titik, Ctrl/Cmd + D untuk menduplikasi, atau seret untuk memindahkan\",\n  lineEditor_nothingSelected: \"Pilih titik untuk mengedit (tekan SHIFT untuk pilih banyak), atau tekan Alt dan klik untuk tambahkan titik baru\",\n  placeImage: \"Klik untuk tempatkan gambar, atau klik dan jatuhkan untuk tetapkan ukuran secara manual\",\n  publishLibrary: \"Terbitkan pustaka Anda\",\n  bindTextToElement: \"Tekan enter untuk tambahkan teks\",\n  deepBoxSelect: \"Tekan Ctrl atau Cmd untuk memilih yang di dalam, dan mencegah penggeseran\",\n  eraserRevert: \"Tahan Alt untuk mengembalikan elemen yang ditandai untuk dihapus\",\n  firefox_clipboard_write: 'Fitur ini dapat diaktifkan melalui pengaturan flag \"dom.events.asyncClipboard.clipboardItem\" ke \"true\". Untuk mengganti flag di Firefox, pergi ke laman \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Tidak dapat menampilkan pratinjau\",\n  canvasTooBig: \"Kanvas mungkin terlalu besar.\",\n  canvasTooBigTip: \"Tip: coba pindahkan elemen-terjauh lebih dekat bersama.\"\n};\nvar errorSplash = {\n  headingMain: \"Mengalami sebuah kesalahan. Cobalah <button>muat ulang halaman.</button>\",\n  clearCanvasMessage: \"Jika memuat ulang tidak bekerja, cobalah <button>bersihkan canvas.</button>\",\n  clearCanvasCaveat: \" Ini akan menghasilkan hilangnya pekerjaan \",\n  trackedToSentry: \"Kesalahan dengan pengidentifikasi {{eventId}} dilacak di sistem kami.\",\n  openIssueMessage: \"Kami sangat berhati-hati untuk tidak menyertakan informasi pemandangan Anda pada kesalahan. Jika pemandangan Anda tidak bersifat pribadi, mohon pertimbangkan menindak lanjut pada <button>pelacak bug.</button> Mohon sertakan informasi dibawah ini dengan menyalin dan menempelkan di Github issue.\",\n  sceneContent: \"Pemandangan konten:\"\n};\nvar roomDialog = {\n  desc_intro: \"Anda dapat mengundang orang ke pemandangan Anda saat ini untuk berkolaborasi dengan Anda.\",\n  desc_privacy: \"Jangan khawatir, sesi menggunakan enkripsi end-to-end, sehingga apa pun yang Anda gambar akan tetap bersifat pribadi. Bahkan server kami tidak dapat melihat apa yang Anda lakukan.\",\n  button_startSession: \"Mulai sesi\",\n  button_stopSession: \"Hentikan sesi\",\n  desc_inProgressIntro: \"Sesi kolaborasi sedang berlangsung sekarang.\",\n  desc_shareLink: \"Bagikan tautan ini dengan siapa pun yang Anda inginkan untuk kolaborasi bersama:\",\n  desc_exitSession: \"Menghentikan sesi akan memutuskan hubungan Anda dari ruangan, tetapi Anda dapat melanjutkan bekerja dengan pemandangan Anda secara lokal. Perhatikan bahwa ini tidak memengaruhi orang lain, dan mereka masih dapat berkolaborasi pada versi mereka.\",\n  shareTitle: \"Gabung sesi kolaborasi langsung di Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Kesalahan\"\n};\nvar exportDialog = {\n  disk_title: \"Simpan ke disk\",\n  disk_details: \"Ekspor data pemandangan ke file yang mana Anda dapat impor nanti.\",\n  disk_button: \"Simpan ke file\",\n  link_title: \"Tautan\",\n  link_details: \"Ekspor sebagai tautan yang hanya dibaca.\",\n  link_button: \"Ekspor ke tautan\",\n  excalidrawplus_description: \"Simpan pemandangan ke ruang kerja Excalidraw+ Anda.\",\n  excalidrawplus_button: \"Ekspor\",\n  excalidrawplus_exportError: \"Tidak dapat ekspor ke Excalidraw+ saat ini...\"\n};\nvar helpDialog = {\n  blog: \"Baca blog kami\",\n  click: \"klik\",\n  deepSelect: \"Pilih dalam\",\n  deepBoxSelect: \"Pilih dalam kotak, dan cegah penggeseran\",\n  curvedArrow: \"Panah lengkung\",\n  curvedLine: \"Garis lengkung\",\n  documentation: \"Dokumentasi\",\n  doubleClick: \"klik-ganda\",\n  drag: \"seret\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Edit titik garis/panah\",\n  editText: \"Edit teks / tambah label\",\n  github: \"Menemukan masalah? Kirimkan\",\n  howto: \"Ikuti panduan kami\",\n  or: \"atau\",\n  preventBinding: \"Cegah pengikatan panah\",\n  tools: \"Alat\",\n  shortcuts: \"Pintasan keyboard\",\n  textFinish: \"Selesai mengedit (editor teks)\",\n  textNewLine: \"Tambahkan garis baru (editor teks)\",\n  title: \"Bantuan\",\n  view: \"Tampilan\",\n  zoomToFit: \"Perbesar agar sesuai dengan semua elemen\",\n  zoomToSelection: \"Perbesar ke seleksi\",\n  toggleElementLock: \"Kunci/lepas seleksi\",\n  movePageUpDown: \"Pindah halaman keatas/kebawah\",\n  movePageLeftRight: \"Pindah halaman kebawah/keatas\"\n};\nvar clearCanvasDialog = {\n  title: \"Hapus kanvas\"\n};\nvar publishDialog = {\n  title: \"Terbitkan pustaka\",\n  itemName: \"Nama item\",\n  authorName: \"Nama pembuat\",\n  githubUsername: \"Nama pengguna github\",\n  twitterUsername: \"Nama pengguna Twitter\",\n  libraryName: \"Nama Pustaka\",\n  libraryDesc: \"Deskripsi pustaka\",\n  website: \"Situs Web\",\n  placeholder: {\n    authorName: \"Nama atau nama pengguna Anda\",\n    libraryName: \"Nama dari pustaka Anda\",\n    libraryDesc: \"Deskripsi pustaka Anda untuk membantu orang mengerti penggunaannya\",\n    githubHandle: \"Akun GitHub (opsional), jadi Anda dapat mengubah pustaka ketika diserahkan untuk review\",\n    twitterHandle: \"Nama pengguna Twitter (opsional), jadi kami tahu siapa dipuji ketika mempromosikannya melalui Twitter\",\n    website: \"Hubungkan ke situs personal Anda atau lainnya (opsional)\"\n  },\n  errors: {\n    required: \"Dibutuhkan\",\n    website: \"Masukkan URL valid\"\n  },\n  noteDescription: \"Kirimkan pustaka Anda untuk disertakan di <link>repositori pustaka publik</link>untuk orang lain menggunakannya dalam gambar mereka.\",\n  noteGuidelines: \"Pustaka butuh disetujui secara manual terlebih dahulu. Baca <link>pedoman</link> sebelum mengirim. Anda butuh akun GitHub untuk berkomunikasi dan membuat perubahan jika dibutuhkan, tetapi tidak wajib dibutukan.\",\n  noteLicense: \"Dengan mengkirimkannya, Anda setuju pustaka akan diterbitkan dibawah <link>Lisensi MIT, </link>yang artinya siapa pun dapat menggunakannya tanpa batasan.\",\n  noteItems: \"Setiap item pustaka harus memiliki nama, sehingga bisa disortir. Item pustaka di bawah ini akan dimasukan:\",\n  atleastOneLibItem: \"Pilih setidaknya satu item pustaka untuk mulai\",\n  republishWarning: \"Catatan: beberapa item yang dipilih telah ditandai sebagai sudah dipublikasikan/diserahkan. Anda hanya dapat menyerahkan kembali item-item ketika memperbarui pustaka atau pengumpulan.\"\n};\nvar publishSuccessDialog = {\n  title: \"Pustaka telah dikirm\",\n  content: \"Terima kasih {{authorName}}. pustaka Anda telah diserahkan untuk ditinjau ulang. Anda dapat cek statusnya<link>di sini</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Reset pustaka\",\n  removeItemsFromLib: \"Hapus item yang dipilih dari pustaka\"\n};\nvar imageExportDialog = {\n  header: \"Ekspor gambar\",\n  label: {\n    withBackground: \"Latar\",\n    onlySelected: \"Hanya yang dipilih\",\n    darkMode: \"Mode gelap\",\n    embedScene: \"Sematkan pemandangan\",\n    scale: \"Skala\",\n    padding: \"Lapisan\"\n  },\n  tooltip: {\n    embedScene: \"Data pemandangan akan disimpan dalam file PNG/SVG yang diekspor sehingga pemandangan itu dapat dipulihkan darinya.\\nAkan membesarkan ukuran file yang diekspor.\"\n  },\n  title: {\n    exportToPng: \"Ekspor ke PNG\",\n    exportToSvg: \"Ekspor ke SVG\",\n    copyPngToClipboard: \"Salin PNG ke papan klip\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Salin ke papan klip\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Gambar anda terenkripsi end-to-end sehingga server Excalidraw tidak akan pernah dapat melihatnya.\",\n  link: \"Pos blog tentang enkripsi ujung ke ujung di Excalidraw\"\n};\nvar stats = {\n  angle: \"Sudut\",\n  element: \"Elemen\",\n  elements: \"Elemen\",\n  height: \"Tinggi\",\n  scene: \"Pemandangan\",\n  selected: \"Terpilih\",\n  storage: \"Penyimpanan\",\n  title: \"Statistik untuk nerd\",\n  total: \"Total\",\n  version: \"Versi\",\n  versionCopy: \"Klik untuk salin\",\n  versionNotAvailable: \"Versi tidak tersedia\",\n  width: \"Lebar\"\n};\nvar toast = {\n  addedToLibrary: \"Tambahkan ke pustaka\",\n  copyStyles: \"Gaya tersalin.\",\n  copyToClipboard: \"Tersalin ke papan klip.\",\n  copyToClipboardAsPng: \"Tersalin {{exportSelection}} ke clipboard sebagai PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"File tersimpan.\",\n  fileSavedToFilename: \"Disimpan ke {filename}\",\n  canvas: \"kanvas\",\n  selection: \"pilihan\",\n  pasteAsSingleElement: \"Gunakan {{shortcut}} untuk menempelkan sebagai satu elemen,\\natau tempelkan ke teks editor yang ada\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transparan\",\n  black: \"Hitam\",\n  white: \"Putih\",\n  red: \"Merah\",\n  pink: \"Pink\",\n  grape: \"Ungu\",\n  violet: \"Violet\",\n  gray: \"Abu-abu\",\n  blue: \"Biru\",\n  cyan: \"Cyan\",\n  teal: \"Teal\",\n  green: \"Hijau\",\n  yellow: \"Kuning\",\n  orange: \"Jingga\",\n  bronze: \"Tembaga\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Semua data Anda disimpan secara lokal di peramban Anda.\",\n    center_heading_plus: \"Apa Anda ingin berpindah ke Excalidraw+?\",\n    menuHint: \"Ekspor, preferensi, bahasa, ...\"\n  },\n  defaults: {\n    menuHint: \"Ekspor, preferensi, dan selebihnya...\",\n    center_heading: \"Diagram. Menjadi. Mudah.\",\n    toolbarHint: \"Pilih alat & mulai menggambar!\",\n    helpHint: \"Pintasan & bantuan\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Warna yang sering dipakai\",\n  colors: \"Warna\",\n  shades: \"Nuansa\",\n  hexCode: \"Kode hexa\",\n  noShades: \"Tidak ada nuansa untuk warna ini\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Ekspor sebagai gambar\",\n      button: \"Ekspor sebagai gambar\",\n      description: \"Ekspor data pemandangan sebagai gambar yang dapat anda impor nanti.\"\n    },\n    saveToDisk: {\n      title: \"Simpan ke disk\",\n      button: \"Simpan ke disk\",\n      description: \"Ekspor data pemandangan ke file yang dapat Anda dapat impor nanti.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Ekspor ke Excalidraw+\",\n      description: \"Simpan pemandangan ke ruang kerja Excalidraw+ Anda.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Muat dari file\",\n      button: \"Muat dari file\",\n      description: \"Memuat dari file yang akan <bold>menggantikan konten Anda sekarang</bold>.<br></br>Anda dapat mencadangkan gambar anda dulu menggunakan opsi-opsi ini.\"\n    },\n    shareableLink: {\n      title: \"Muat dari link\",\n      button: \"Ganti konten saya\",\n      description: \"Memuat dari file yang akan <bold>menggantikan konten Anda sekarang</bold>.<br></br>Anda dapat mencadangkan gambar anda dulu menggunakan opsi-opsi ini.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar id_ID_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=id-ID-22TWZNLA.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvaWQtSUQtMjJUV1pOTEEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLFlBQVk7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsUUFBUTtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRUFBcUUsU0FBUztBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3REFBd0QsVUFBVTtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixZQUFZO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLGtCQUFrQiw2QkFBNkIsbUJBQW1CO0FBQ3RHO0FBQ0EscUNBQXFDLFNBQVM7QUFDOUM7QUFDQTtBQUNBLG1DQUFtQyxXQUFXO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBOEJFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvQGV4Y2FsaWRyYXcvZXhjYWxpZHJhdy9kaXN0L2Rldi9sb2NhbGVzL2lkLUlELTIyVFdaTkxBLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4uL2NodW5rLVhERkNVVVQ2LmpzXCI7XG5cbi8vIGxvY2FsZXMvaWQtSUQuanNvblxudmFyIGxhYmVscyA9IHtcbiAgcGFzdGU6IFwiVGVtcGVsXCIsXG4gIHBhc3RlQXNQbGFpbnRleHQ6IFwiVGVtcGVsIHNlYmFnYWkgdGVrcyBiaWFzYVwiLFxuICBwYXN0ZUNoYXJ0czogXCJUZW1wZWwgZGlhZ3JhbVwiLFxuICBzZWxlY3RBbGw6IFwiUGlsaWggc2VtdWFcIixcbiAgbXVsdGlTZWxlY3Q6IFwiVGFtYmFoa2FuIGVsZW1lbiBrZSBwaWxpaGFuXCIsXG4gIG1vdmVDYW52YXM6IFwiUGluZGFoa2FuIGthbnZhc1wiLFxuICBjdXQ6IFwiUG90b25nXCIsXG4gIGNvcHk6IFwiU2FsaW5cIixcbiAgY29weUFzUG5nOiBcIlNhbGluIGtlIHBhcGFuIGtsaXAgc2ViYWdhaSBQTkdcIixcbiAgY29weUFzU3ZnOiBcIlNhbGluIGtlIHBhcGFuIGtsaXAgc2ViYWdhaSBTVkdcIixcbiAgY29weVRleHQ6IFwiU2FsaW4ga2UgcGFwYW4ga2xpcCBzZWJhZ2FpIHRla3NcIixcbiAgY29weVNvdXJjZTogXCJcIixcbiAgY29udmVydFRvQ29kZTogXCJcIixcbiAgYnJpbmdGb3J3YXJkOiBcIkJhd2EgbWFqdVwiLFxuICBzZW5kVG9CYWNrOiBcIktpcmltIGtlIGJlbGFrYW5nXCIsXG4gIGJyaW5nVG9Gcm9udDogXCJCYXdhIGtlIGRlcGFuXCIsXG4gIHNlbmRCYWNrd2FyZDogXCJLaXJpbSBtdW5kdXJcIixcbiAgZGVsZXRlOiBcIkhhcHVzXCIsXG4gIGNvcHlTdHlsZXM6IFwiU2FsaW4gZ2F5YVwiLFxuICBwYXN0ZVN0eWxlczogXCJUZW1wZWxrYW4gZ2F5YVwiLFxuICBzdHJva2U6IFwiR3VyYXRhblwiLFxuICBiYWNrZ3JvdW5kOiBcIkxhdGFyXCIsXG4gIGZpbGw6IFwiSXNpYW5cIixcbiAgc3Ryb2tlV2lkdGg6IFwiTGViYXIgZ3VyYXRhblwiLFxuICBzdHJva2VTdHlsZTogXCJHYXlhIGd1cmF0YW5cIixcbiAgc3Ryb2tlU3R5bGVfc29saWQ6IFwiUGFkYXRcIixcbiAgc3Ryb2tlU3R5bGVfZGFzaGVkOiBcIlB1dHVzLXB1dHVzXCIsXG4gIHN0cm9rZVN0eWxlX2RvdHRlZDogXCJUaXRpay10aXRpa1wiLFxuICBzbG9wcGluZXNzOiBcIktlY2Vyb2JvaGFuXCIsXG4gIG9wYWNpdHk6IFwiS2VidXJhbWFuXCIsXG4gIHRleHRBbGlnbjogXCJQZXJhdGFhbiB0ZWtzXCIsXG4gIGVkZ2VzOiBcIlRlcGlcIixcbiAgc2hhcnA6IFwiVGFqYW1cIixcbiAgcm91bmQ6IFwiQnVsYXRcIixcbiAgYXJyb3doZWFkczogXCJNYXRhIHBhbmFoXCIsXG4gIGFycm93aGVhZF9ub25lOiBcIlRpZGFrIGFkYVwiLFxuICBhcnJvd2hlYWRfYXJyb3c6IFwiUGFuYWhcIixcbiAgYXJyb3doZWFkX2JhcjogXCJCYXRhbmdcIixcbiAgYXJyb3doZWFkX2NpcmNsZTogXCJcIixcbiAgYXJyb3doZWFkX2NpcmNsZV9vdXRsaW5lOiBcIlwiLFxuICBhcnJvd2hlYWRfdHJpYW5nbGU6IFwiU2VnaXRpZ2FcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlX291dGxpbmU6IFwiXCIsXG4gIGFycm93aGVhZF9kaWFtb25kOiBcIlwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZF9vdXRsaW5lOiBcIlwiLFxuICBmb250U2l6ZTogXCJVa3VyYW4gZm9udFwiLFxuICBmb250RmFtaWx5OiBcIkplbmlzIGZvbnRcIixcbiAgYWRkV2F0ZXJtYXJrOiAnVGFtYmFoa2FuIFwiRGlidWF0IGRlbmdhbiBFeGNhbGlkcmF3XCInLFxuICBoYW5kRHJhd246IFwiVHVsaXNhbiB0YW5nYW5cIixcbiAgbm9ybWFsOiBcIk5vcm1hbFwiLFxuICBjb2RlOiBcIktvZGVcIixcbiAgc21hbGw6IFwiS2VjaWxcIixcbiAgbWVkaXVtOiBcIlNlZGFuZ1wiLFxuICBsYXJnZTogXCJCZXNhclwiLFxuICB2ZXJ5TGFyZ2U6IFwiU2FuZ2F0IGJlc2FyXCIsXG4gIHNvbGlkOiBcIlBhZGF0XCIsXG4gIGhhY2h1cmU6IFwiR2FyaXMtZ2FyaXNcIixcbiAgemlnemFnOiBcIlppZ3phZ1wiLFxuICBjcm9zc0hhdGNoOiBcIkFzaXJhbiBzaWxhbmdcIixcbiAgdGhpbjogXCJMZW1idXRcIixcbiAgYm9sZDogXCJUZWJhbFwiLFxuICBsZWZ0OiBcIktpcmlcIixcbiAgY2VudGVyOiBcIlRlbmdhaFwiLFxuICByaWdodDogXCJLYW5hblwiLFxuICBleHRyYUJvbGQ6IFwiU2FuZ2F0IHRlYmFsXCIsXG4gIGFyY2hpdGVjdDogXCJBcnNpdGVrXCIsXG4gIGFydGlzdDogXCJBcnRpc1wiLFxuICBjYXJ0b29uaXN0OiBcIkthcnR1bmlzXCIsXG4gIGZpbGVUaXRsZTogXCJOYW1hIGZpbGVcIixcbiAgY29sb3JQaWNrZXI6IFwiUGlsaWhhbiBXYXJuYVwiLFxuICBjYW52YXNDb2xvcnM6IFwiRGlndW5ha2FuIGRpIGthbnZhc1wiLFxuICBjYW52YXNCYWNrZ3JvdW5kOiBcIkxhdGFyIEthbnZhc1wiLFxuICBkcmF3aW5nQ2FudmFzOiBcIkthbnZhc1wiLFxuICBsYXllcnM6IFwiTGFwaXNhblwiLFxuICBhY3Rpb25zOiBcIkFrc2lcIixcbiAgbGFuZ3VhZ2U6IFwiQmFoYXNhXCIsXG4gIGxpdmVDb2xsYWJvcmF0aW9uOiBcIktvbGFib3Jhc2kgbGFuZ3N1bmcuLi5cIixcbiAgZHVwbGljYXRlU2VsZWN0aW9uOiBcIkR1cGxpa2F0XCIsXG4gIHVudGl0bGVkOiBcIlRhbnBhIGp1ZHVsXCIsXG4gIG5hbWU6IFwiTmFtYVwiLFxuICB5b3VyTmFtZTogXCJOYW1hIEFuZGFcIixcbiAgbWFkZVdpdGhFeGNhbGlkcmF3OiBcIkRpYnVhdCBkZW5nYW4gRXhjYWxpZHJhd1wiLFxuICBncm91cDogXCJLZWxvbXBva2FuIHBpbGloYW5cIixcbiAgdW5ncm91cDogXCJQaXNhaGthbiBwaWxpaGFuXCIsXG4gIGNvbGxhYm9yYXRvcnM6IFwiS29sYWJvcmF0b3JcIixcbiAgc2hvd0dyaWQ6IFwiVGFtcGlsa2FuIGdyaWRcIixcbiAgYWRkVG9MaWJyYXJ5OiBcIlRhbWJhaGthbiBrZSBwdXN0YWthXCIsXG4gIHJlbW92ZUZyb21MaWJyYXJ5OiBcIkhhcHVzIGRhcmkgcHVzdGFrYVwiLFxuICBsaWJyYXJ5TG9hZGluZ01lc3NhZ2U6IFwiTWVtdWF0IHB1c3Rha2FcXHUyMDI2XCIsXG4gIGxpYnJhcmllczogXCJUZWx1c3VyIHB1c3Rha2FcIixcbiAgbG9hZGluZ1NjZW5lOiBcIk1lbXVhdCBwZW1hbmRhbmdhblxcdTIwMjZcIixcbiAgYWxpZ246IFwiUGVyYXRhYW5cIixcbiAgYWxpZ25Ub3A6IFwiUmF0YSBhdGFzXCIsXG4gIGFsaWduQm90dG9tOiBcIlJhdGEgYmF3YWhcIixcbiAgYWxpZ25MZWZ0OiBcIlJhdGEga2lyaVwiLFxuICBhbGlnblJpZ2h0OiBcIlJhdGEga2FuYW5cIixcbiAgY2VudGVyVmVydGljYWxseTogXCJQdXNhdGthbiBzZWNhcmEgdmVydGlrYWxcIixcbiAgY2VudGVySG9yaXpvbnRhbGx5OiBcIlB1c2F0a2FuIHNlY2FyYSBob3Jpem9udGFsXCIsXG4gIGRpc3RyaWJ1dGVIb3Jpem9udGFsbHk6IFwiRGlzdHJpYnVzaWthbiBob3Jpem9udGFsXCIsXG4gIGRpc3RyaWJ1dGVWZXJ0aWNhbGx5OiBcIkRpc3RyaWJ1c2lrYW4gdmVydGlrYWxcIixcbiAgZmxpcEhvcml6b250YWw6IFwiQmFsaWtrYW4gaG9yaXpvbnRhbFwiLFxuICBmbGlwVmVydGljYWw6IFwiQmFsaWtrYW4gdmVydGlrYWxcIixcbiAgdmlld01vZGU6IFwiTW9kZSB0YW1waWxhblwiLFxuICBzaGFyZTogXCJCYWdpa2FuXCIsXG4gIHNob3dTdHJva2U6IFwiVGFtcGlsa2FuIGdhcmlzIHBlbmdhbWJpbCB3YXJuYVwiLFxuICBzaG93QmFja2dyb3VuZDogXCJUYW1waWxrYW4gbGF0YXIgcGVuZ2FtYmlsIHdhcm5hXCIsXG4gIHRvZ2dsZVRoZW1lOiBcIlViYWggdGVtYVwiLFxuICBwZXJzb25hbExpYjogXCJQdXN0YWthIFByaWJhZGlcIixcbiAgZXhjYWxpZHJhd0xpYjogXCJQdXN0YWthIEV4Y2FsaWRyYXdcIixcbiAgZGVjcmVhc2VGb250U2l6ZTogXCJLZWNpbGthbiB1a3VyYW4gZm9udFwiLFxuICBpbmNyZWFzZUZvbnRTaXplOiBcIkJlc2Fya2FuIHVrdXJhbiBmb250XCIsXG4gIHVuYmluZFRleHQ6IFwiTGVwYXMgdGVrc1wiLFxuICBiaW5kVGV4dDogXCJLdW5jaSB0ZWtzIGtlIGtvbnRhaW5lclwiLFxuICBjcmVhdGVDb250YWluZXJGcm9tVGV4dDogXCJCdW5na3VzIHRla3MgZGFsYW0ga29udGFpbmVyXCIsXG4gIGxpbms6IHtcbiAgICBlZGl0OiBcIkVkaXQgdGF1dGFuXCIsXG4gICAgZWRpdEVtYmVkOiBcIlwiLFxuICAgIGNyZWF0ZTogXCJCdWF0IHRhdXRhblwiLFxuICAgIGNyZWF0ZUVtYmVkOiBcIlwiLFxuICAgIGxhYmVsOiBcIlRhdXRhblwiLFxuICAgIGxhYmVsRW1iZWQ6IFwiXCIsXG4gICAgZW1wdHk6IFwiXCJcbiAgfSxcbiAgbGluZUVkaXRvcjoge1xuICAgIGVkaXQ6IFwiRWRpdCB0YXV0YW5cIixcbiAgICBleGl0OiBcIktlbHVhciBlZGl0b3IgZ2FyaXNcIlxuICB9LFxuICBlbGVtZW50TG9jazoge1xuICAgIGxvY2s6IFwiS3VuY2lcIixcbiAgICB1bmxvY2s6IFwiTGVwYXNcIixcbiAgICBsb2NrQWxsOiBcIkt1bmNpIHNlbXVhXCIsXG4gICAgdW5sb2NrQWxsOiBcIkxlcGFzIHNlbXVhXCJcbiAgfSxcbiAgc3RhdHVzUHVibGlzaGVkOiBcIlRlbGFoIHRlcmJpdFwiLFxuICBzaWRlYmFyTG9jazogXCJCaWFya2FuIHNpZGViYXIgdGV0YXAgdGVyYnVrYVwiLFxuICBzZWxlY3RBbGxFbGVtZW50c0luRnJhbWU6IFwiUGlsaWggc2VtdWEgZWxlbWVuIGRpIGJpbmdrYWlcIixcbiAgcmVtb3ZlQWxsRWxlbWVudHNGcm9tRnJhbWU6IFwiSGFwdXMgc2VtdWEgZWxlbWVuIGRhcmkgYmluZ2thaVwiLFxuICBleWVEcm9wcGVyOiBcIkFtYmlsIHdhcm5hIGRhcmkga2FudmFzXCIsXG4gIHRleHRUb0RpYWdyYW06IFwiXCIsXG4gIHByb21wdDogXCJcIlxufTtcbnZhciBsaWJyYXJ5ID0ge1xuICBub0l0ZW1zOiBcIkJlbHVtIGFkYSBpdGVtIHlhbmcgZGl0YW1iYWhrYW4uLi5cIixcbiAgaGludF9lbXB0eUxpYnJhcnk6IFwiUGlsaWggaXRlbSBwYWRhIGthbnZhcyB1bnR1ayBtZW5hbWJhaGthbiBueWEgZGkgc2luaSwgYXRhdSBwYXNhbmcgcHVzdGFrYSBkYXJpIGd1ZGFuZyBkaSBiYXdhaCBpbmkuXCIsXG4gIGhpbnRfZW1wdHlQcml2YXRlTGlicmFyeTogXCJQaWxpaCBpdGVtIHBhZGEga2FudmFzIHVudHVrIG1lbmFtYmFoa2FuIG55YSBkaSBzaW5pLlwiXG59O1xudmFyIGJ1dHRvbnMgPSB7XG4gIGNsZWFyUmVzZXQ6IFwiU2V0ZWwgVWxhbmcgS2FudmFzXCIsXG4gIGV4cG9ydEpTT046IFwiRWtzcG9yIGtlIGZpbGVcIixcbiAgZXhwb3J0SW1hZ2U6IFwiRWtzcG9yIGdhbWJhci4uLlwiLFxuICBleHBvcnQ6IFwiU2ltcGFuIGtlLi4uXCIsXG4gIGNvcHlUb0NsaXBib2FyZDogXCJTYWxpbiBrZSBQYXBhbiBLbGlwXCIsXG4gIHNhdmU6IFwiU2ltcGFuIGtlIGZpbGUgc2VrYXJhbmdcIixcbiAgc2F2ZUFzOiBcIlNpbXBhbiBzZWJhZ2FpXCIsXG4gIGxvYWQ6IFwiQnVrYVwiLFxuICBnZXRTaGFyZWFibGVMaW5rOiBcIkJ1YXQgVGF1dGFuIHlhbmcgQmlzYSBEaWJhZ2lhblwiLFxuICBjbG9zZTogXCJUdXR1cFwiLFxuICBzZWxlY3RMYW5ndWFnZTogXCJQaWxpaCBiYWhhc2FcIixcbiAgc2Nyb2xsQmFja1RvQ29udGVudDogXCJHdWxpciBrZW1iYWxpIGtlIGtvbnRlblwiLFxuICB6b29tSW46IFwiQmVzYXJrYW5cIixcbiAgem9vbU91dDogXCJLZWNpbGthblwiLFxuICByZXNldFpvb206IFwiUmVzZXQgUGVtYmVzYXJhblwiLFxuICBtZW51OiBcIk1lbnVcIixcbiAgZG9uZTogXCJTZWxlc2FpXCIsXG4gIGVkaXQ6IFwiRWRpdFwiLFxuICB1bmRvOiBcIlVydW5na2FuXCIsXG4gIHJlZG86IFwiVWxhbmdpXCIsXG4gIHJlc2V0TGlicmFyeTogXCJSZXNldCBwdXN0YWthXCIsXG4gIGNyZWF0ZU5ld1Jvb206IFwiQnVhdCBydWFuZyBiYXJ1XCIsXG4gIGZ1bGxTY3JlZW46IFwiTGF5YXIgcGVudWhcIixcbiAgZGFya01vZGU6IFwiTW9kZSBnZWxhcFwiLFxuICBsaWdodE1vZGU6IFwiTW9kZSB0ZXJhbmdcIixcbiAgemVuTW9kZTogXCJNb2RlIHplblwiLFxuICBvYmplY3RzU25hcE1vZGU6IFwiXCIsXG4gIGV4aXRaZW5Nb2RlOiBcIktlbHVhciBkYXJpIG1vZGUgemVuXCIsXG4gIGNhbmNlbDogXCJCYXRhbFwiLFxuICBjbGVhcjogXCJIYXB1c1wiLFxuICByZW1vdmU6IFwiSGFwdXNcIixcbiAgZW1iZWQ6IFwiXCIsXG4gIHB1Ymxpc2hMaWJyYXJ5OiBcIlRlcmJpdGthblwiLFxuICBzdWJtaXQ6IFwiS2lyaW1rYW5cIixcbiAgY29uZmlybTogXCJLb25maXJtYXNpXCIsXG4gIGVtYmVkZGFibGVJbnRlcmFjdGlvbkJ1dHRvbjogXCJcIlxufTtcbnZhciBhbGVydHMgPSB7XG4gIGNsZWFyUmVzZXQ6IFwiSW5pIGFrYW4gbWVuZ2hhcHVzIHNlbXVhIHlhbmcgYWRhIGRpa2FudmFzLiBBcGFrYWgga2FtdSB5YWtpbiA/XCIsXG4gIGNvdWxkTm90Q3JlYXRlU2hhcmVhYmxlTGluazogXCJUaWRhayBiaXNhIG1lbWJ1YXQgdGF1dGFuIHlhbmcgYmlzYSBkaWJhZ2lrYW5cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rVG9vQmlnOiBcIlRpZGFrIGRhcGF0IG1lbWJ1YXQgdGF1dGFuIHlhbmcgZGFwYXQgZGliYWdpa2FuOiBwZW1hbmRhbmdhbiB0ZXJsYWx1IGJlc2FyXCIsXG4gIGNvdWxkTm90TG9hZEludmFsaWRGaWxlOiBcIlRpZGFrIGRhcGF0IG1lbXVhdCBiZXJrYXMgeWFuZyB0aWRhayB2YWxpZFwiLFxuICBpbXBvcnRCYWNrZW5kRmFpbGVkOiBcIkdhZ2FsIG1lbmdpbXBvciBkYXJpIGJhY2tlbmRcIixcbiAgY2Fubm90RXhwb3J0RW1wdHlDYW52YXM6IFwiVGlkYWsgYmlzYSBtZW5nZWtzcG9yIGthbnZhcyBrb3NvbmdcIixcbiAgY291bGROb3RDb3B5VG9DbGlwYm9hcmQ6IFwiVGlkYWsgYmlzYSBtZW55YWxpbiBrZSBwYXBhbiBrbGlwLlwiLFxuICBkZWNyeXB0RmFpbGVkOiBcIlRpZGFrIGRhcGF0IG1lbmdkZWtyaXBzaSBkYXRhLlwiLFxuICB1cGxvYWRlZFNlY3VybHk6IFwiUGVuZ3VuZ2dhaGFuIGluaSB0ZWxhaCBkaWFtYW5rYW4gbWVuZ2d1bmFrYW4gZW5rcmlwc2kgZW5kLXRvLWVuZCwgYXJ0aW55YSBzZXJ2ZXIgRXhjYWxpZHJhdyBkYW4gcGloYWsga2V0aWdhIHRpZGFrIGRhdGEgbWVtYmFjYSBueWFcIixcbiAgbG9hZFNjZW5lT3ZlcnJpZGVQcm9tcHQ6IFwiTWVtdWF0IGdhbWJhciBleHRlcm5hbCBha2FuIG1lbmdnYW50aSBrb250ZW4gQW5kYSB5YW5nIGFkYS4gQXBha2FoIEFuZGEgaW5naW4gbWVsYW5qdXRrYW4/XCIsXG4gIGNvbGxhYlN0b3BPdmVycmlkZVByb21wdDogXCJNZW5naGVudGlrYW4gc2VzaSBha2FuIG1lbmltcGEgZ2FtYmFyIEFuZGEgeWFuZyB0ZXJzaW1wYW4gc2VjYXJhIGxva2FsLiBBbmRhIHlha2luP1xcblxcbihKaWthIEFuZGEgaW5naW4gbWVueWltcGFuIGdhbWJhciBsb2thbCBBbmRhLCBnYW50aW55YSBjdWt1cCB0dXR1cCB0YWIgYnJvd3Nlci4pXCIsXG4gIGVycm9yQWRkaW5nVG9MaWJyYXJ5OiBcIlRpZGFrIGRhcGF0IG1lbmFtYmFoa2FuIGl0ZW0ga2UgcHVzdGFrYVwiLFxuICBlcnJvclJlbW92aW5nRnJvbUxpYnJhcnk6IFwiVGlkYWsgZGFwYXQgbWVtYnVhbmcgaXRlbSBkYXJpIHB1c3Rha2FcIixcbiAgY29uZmlybUFkZExpYnJhcnk6IFwiSW5pIGFrYW4gbWVuYW1iYWhrYW4ge3tudW1TaGFwZXN9fSBiZW50dWsga2UgcHVzdGFrYSBBbmRhLiBBbmRhIHlha2luP1wiLFxuICBpbWFnZURvZXNOb3RDb250YWluU2NlbmU6IFwiR2FtYmFyIGluaSBzZXBlcnRpbnlhIHRpZGFrIHRlcmRhcGF0IGRhdGEgcGVtYW5kYW5nYW4uIFN1ZGFoa2FoIEFuZGEgbWVuZ2FrdGlma2FuIHBlbnllbWF0YW4gcGVtYW5kYW5nYW4ga2V0aWthIGVrc3Bvcj9cIixcbiAgY2Fubm90UmVzdG9yZUZyb21JbWFnZTogXCJQZW1hbmRhbmdhbiB0aWRhayBkYXBhdCBkaXB1bGloa2FuIGRhcmkgZmlsZSBnYW1iYXIgaW5pXCIsXG4gIGludmFsaWRTY2VuZVVybDogXCJUaWRhayBkYXBhdCBpbXBvciBwZW1hbmRhbmdhbiBkYXJpIFVSTC4gS2VtdW5na2luYW4gVVJMIGl0dSBydXNhayBhdGF1IHRpZGFrIGJlcmlzaSBkYXRhIEpTT04gRXhjYWxpZHJhdyB5YW5nIHZhbGlkLlwiLFxuICByZXNldExpYnJhcnk6IFwiSW5pIGFrYW4gbWVuZ2hhcHVzIHB1c3Rha2EgQW5kYS4gQW5kYSB5YWtpbj9cIixcbiAgcmVtb3ZlSXRlbXNGcm9tc0xpYnJhcnk6IFwiSGFwdXMge3tjb3VudH19IGl0ZW0gZGFyaSBwdXN0YWthP1wiLFxuICBpbnZhbGlkRW5jcnlwdGlvbktleTogXCJTYW5kaSBlbmtyaXBzaSBoYXJ1cyAyMiBrYXJha3Rlci4gS29sYWJvcmFzaSBsYW5nc3VuZyBkaW5vbmFrdGlma2FuLlwiLFxuICBjb2xsYWJPZmZsaW5lV2FybmluZzogXCJUaWRhayBhZGEga29uZWtzaSBpbnRlcm5ldC5cXG5QZXJ1YmFoYW4gdGlkYWsgYWthbiBkaXNpbXBhbiFcIlxufTtcbnZhciBlcnJvcnMgPSB7XG4gIHVuc3VwcG9ydGVkRmlsZVR5cGU6IFwiVGlwZSBmaWxlIHRpZGFrIGRpZHVrdW5nLlwiLFxuICBpbWFnZUluc2VydEVycm9yOiBcIlRpZGFrIGRhcGF0IG1lbnlpc2lwa2FuIGdhbWJhci4gQ29iYSBsYWdpIG5hbnRpLi4uXCIsXG4gIGZpbGVUb29CaWc6IFwiRmlsZSB0ZXJsYWx1IGJlc2FyLiBVa3VyYW4gbWFrc2ltdW0geWFuZyBkaWJvbGVoa2FuIHt7bWF4U2l6ZX19LlwiLFxuICBzdmdJbWFnZUluc2VydEVycm9yOiBcIlRpZGFrIGRhcGF0IG1lbnlpc2lwa2FuIGdhbWJhciBTVkcuIE1hcmt1cCBTVkcgc2VwZXJ0aW55YSB0aWRhayB2YWxpZC5cIixcbiAgZmFpbGVkVG9GZXRjaEltYWdlOiBcIlwiLFxuICBpbnZhbGlkU1ZHU3RyaW5nOiBcIlNWRyB0aWRhayB2YWxpZC5cIixcbiAgY2Fubm90UmVzb2x2ZUNvbGxhYlNlcnZlcjogXCJUaWRhayBkYXBhdCB0ZXJodWJ1bmcga2Ugc2VydmVyIGtvbGFiLiBNdWF0IHVsYW5nIGxhbWFuIGRhbiBjb2JhIGxhZ2kuXCIsXG4gIGltcG9ydExpYnJhcnlFcnJvcjogXCJUaWRhayBkYXBhdCBtZW11YXQgcHVzdGFrYVwiLFxuICBjb2xsYWJTYXZlRmFpbGVkOiBcIlRpZGFrIGRhcGF0IG1lbnlpbXBhbiBrZSBkYWxhbSBiYXNpcyBkYXRhIHNlcnZlci4gSmlrYSBtYXNpaCBiZXJsYW5qdXQsIEFuZGEgc2ViYWlrbnlhIHNpbXBhbiBiZXJrYXMgQW5kYSBzZWNhcmEgbG9rYWwgdW50dWsgbWVtYXN0aWthbiBwZWtlcmphYW4gQW5kYSB0aWRhayBoaWxhbmcuXCIsXG4gIGNvbGxhYlNhdmVGYWlsZWRfc2l6ZUV4Y2VlZGVkOiBcIlRpZGFrIGRhcGF0IG1lbnlpbXBhbiBrZSBkYWxhbSBiYXNpcyBkYXRhIHNlcnZlciwgdGFtcGFrbnlhIHVrdXJhbiBrYW52YXMgdGVybGFsdSBiZXNhci4gQW5kYSBzZWJhaWtueWEgc2ltcGFuIGJlcmthcyBBbmRhIHNlY2FyYSBsb2thbCB1bnR1ayBtZW1hc3Rpa2FuIHBla2VyamFhbiBBbmRhIHRpZGFrIGhpbGFuZy5cIixcbiAgaW1hZ2VUb29sTm90U3VwcG9ydGVkOiBcIlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJTZXBlcnRpbnlhIEFuZGEgbWVuZ2d1bmthbiBwZXJhbWJhbiBCcmF2ZSBkZW5nYW4gcGVuZ2F0dXJhbiA8Ym9sZD5CbG9raXIgRmluZ2VycHJpbnRpbmcgeWFuZyBBZ3Jlc2lmPC9ib2xkPiBkaWFrdGlma2FuLlwiLFxuICAgIGxpbmUyOiBcIkluaSBkYXBhdCBtZW1idWF0IDxib2xkPkVsZW1lbiBUZWtzPC9ib2xkPiBkYWxhbSBnYW1iYXIgbXUuXCIsXG4gICAgbGluZTM6IFwiS2FtaSBzYW5nYXQgbWVueWFyYW5rYW4gbWVtYXRpa2FuIHBlbmdhdHVyYW4gaW5pLiBBbmRhIGRhcGF0IG1lbmdpa3V0aSA8bGluaz5sYW5na2FoLWxhbmdrYWggaW5pPC9saW5rPiB1bnR1ayBtZWxha3VrYW5ueWEuXCIsXG4gICAgbGluZTQ6IFwiSmlrYSBtZW1hdGlrYW4gcGVuZ2F0dXJhbiBpbmkgdGlkYWsgbWVtYmVuYXJrYW4gdGFtcGlsYW4gZWxlbWVuIHRla3MsIG1vaG9uIGJ1a2FcXG48aXNzdWVMaW5rPmlzdTwvaXNzdWVMaW5rPiBkaSBHaXRIdWIga2FtaSwgYXRhdSBjaGF0IGthbWkgZGkgPGRpc2NvcmRMaW5rPkRpc2NvcmQ8L2Rpc2NvcmRMaW5rPlwiXG4gIH0sXG4gIGxpYnJhcnlFbGVtZW50VHlwZUVycm9yOiB7XG4gICAgZW1iZWRkYWJsZTogXCJcIixcbiAgICBpZnJhbWU6IFwiXCIsXG4gICAgaW1hZ2U6IFwiXCJcbiAgfSxcbiAgYXN5bmNQYXN0ZUZhaWxlZE9uUmVhZDogXCJcIixcbiAgYXN5bmNQYXN0ZUZhaWxlZE9uUGFyc2U6IFwiXCIsXG4gIGNvcHlUb1N5c3RlbUNsaXBib2FyZEZhaWxlZDogXCJcIlxufTtcbnZhciB0b29sQmFyID0ge1xuICBzZWxlY3Rpb246IFwiUGlsaWhhblwiLFxuICBpbWFnZTogXCJTaXNpcGthbiBnYW1iYXJcIixcbiAgcmVjdGFuZ2xlOiBcIlBlcnNlZ2lcIixcbiAgZGlhbW9uZDogXCJCZXJsaWFuXCIsXG4gIGVsbGlwc2U6IFwiRWxpcHNcIixcbiAgYXJyb3c6IFwiUGFuYWhcIixcbiAgbGluZTogXCJHYXJpc1wiLFxuICBmcmVlZHJhdzogXCJHYW1iYXJcIixcbiAgdGV4dDogXCJUZWtzXCIsXG4gIGxpYnJhcnk6IFwiUHVzdGFrYVwiLFxuICBsb2NrOiBcIkJpYXJrYW4gYWxhdCB5YW5nIGRpcGlsaWggYWt0aWYgc2V0ZWxhaCBtZW5nZ2FtYmFyXCIsXG4gIHBlbk1vZGU6IFwiTW9kZSBwZW5hIC0gbWVuY2VnYWggc2VudHVoYW5cIixcbiAgbGluazogXCJUYW1iYWgvUGVyYmFydWkgdGF1dGFuIHVudHVrIGJlbnR1ayB5YW5nIGRpcGlsaWhcIixcbiAgZXJhc2VyOiBcIlBlbmdoYXB1c1wiLFxuICBmcmFtZTogXCJBbGF0IGJpbmdrYWlcIixcbiAgbWFnaWNmcmFtZTogXCJcIixcbiAgZW1iZWRkYWJsZTogXCJcIixcbiAgbGFzZXI6IFwiXCIsXG4gIGhhbmQ6IFwiVGFuZ2FuIChhbGF0IHBhbm5pbmcpXCIsXG4gIGV4dHJhVG9vbHM6IFwiQWxhdC1hbGF0IGxhaW5cIixcbiAgbWVybWFpZFRvRXhjYWxpZHJhdzogXCJcIixcbiAgbWFnaWNTZXR0aW5nczogXCJcIlxufTtcbnZhciBoZWFkaW5ncyA9IHtcbiAgY2FudmFzQWN0aW9uczogXCJPcHNpIEthbnZhc1wiLFxuICBzZWxlY3RlZFNoYXBlQWN0aW9uczogXCJPcHNpIGJlbnR1ayB5YW5nIGRpcGlsaWhcIixcbiAgc2hhcGVzOiBcIkJlbnR1a1wiXG59O1xudmFyIGhpbnRzID0ge1xuICBjYW52YXNQYW5uaW5nOiBcIlVudHVrIG1lbWluZGFoa2FuIGthbnZhcywgdGVrYW4gcm9kYSBtb3VzZSBhdGF1IHNwYWNlYmFyIHNhbWJpbCBtZW55ZXJldCwgYXRhdSBtZW5nZ3VuYWthbiBhbGF0IHRhbmdhblwiLFxuICBsaW5lYXJFbGVtZW50OiBcIktsaWsgdW50dWsgbWVtdWxhaSBiYW55YWsgcG9pbiwgc2VyZXQgdW50dWsgc2F0dSBiYXJpc1wiLFxuICBmcmVlRHJhdzogXCJLbGlrIGRhbiBzZXJldCwgbGVwYXNrYW4gamlrYSBBbmRhIHNlbGVzYWlcIixcbiAgdGV4dDogXCJUaXA6IEFuZGEganVnYSBkYXBhdCBtZW5hbWJhaGthbiB0ZWtzIGRlbmdhbiBrbGlrIGdhbmRhIGRpIG1hbmEgc2FqYSBkZW5nYW4gYWxhdCBwZW1pbGloYW5cIixcbiAgZW1iZWRkYWJsZTogXCJcIixcbiAgdGV4dF9zZWxlY3RlZDogXCJLbGlrIGdhbmRhIGF0YXUgdGVrYW4gRU5URVIgdW50dWsgZWRpdCB0ZWtzXCIsXG4gIHRleHRfZWRpdGluZzogXCJUZWthbiBFc2NhcGUgYXRhdSBDdHJsQXRhdUNtZCtFTlRFUiB1bnR1ayBzZWxlc2FpIG1lbmdlZGl0XCIsXG4gIGxpbmVhckVsZW1lbnRNdWx0aTogXCJLbGlrIHBhZGEgdGl0aWsgYWtoaXIgYXRhdSB0ZWthbiBFc2NhcGUgYXRhdSBFbnRlciB1bnR1ayBtZW55ZWxlc2Fpa2FuXCIsXG4gIGxvY2tBbmdsZTogXCJBbmRhIGRhcGF0IG1lbmphZ2Egc3VkdXQgZGVuZ2FuIG1lbmFoYW4gU0hJRlRcIixcbiAgcmVzaXplOiBcIkFuZGEgZGFwYXQgbWVuamFnYSBwcm9wb3NpIGRlbmdhbiBtZW5la2FuIFNISUZUIHNhbWJpbCBtZW5ndWJhaCB1a3VyYW4sXFxudGVrYW4gQWxUIHVudHVrIG1lbmd1YmFoIHVrdXJhbiBkYXJpIHRlbmdhaFwiLFxuICByZXNpemVJbWFnZTogXCJBbmRhIGRhcGF0IG1lbmd1YmFoIHNlY2FyYSBiZWJhcyBkZW5nYW4gbWVuZWthbiBTSElGVCxcXG5UZWthbiBBTFQgdW50dWsgbWVuZ3ViYWggZGFyaSB0ZW5nYWhcIixcbiAgcm90YXRlOiBcIkFuZGEgZGFwYXQgbWVuamFnYSBzdWR1dCBkZW5nYW4gbWVuYWhhbiBTSElGVCBzYW1iaWwgbWVtdXRhclwiLFxuICBsaW5lRWRpdG9yX2luZm86IFwiVGVrYW4gQ3RybC9DbWQgZGFuIERvYmVsLWtsaWsgYXRhdSB0ZWthbiBDdHJsL0NtZCArRW50ZXIgdW50dWsgbWVuZ2VkaXQgcG9pblwiLFxuICBsaW5lRWRpdG9yX3BvaW50U2VsZWN0ZWQ6IFwiVGVrYW4gRGVsZXRlIHVudHVrIG1lbmdoYXB1cyB0aXRpaywgQ3RybC9DbWQgKyBEIHVudHVrIG1lbmR1cGxpa2FzaSwgYXRhdSBzZXJldCB1bnR1ayBtZW1pbmRhaGthblwiLFxuICBsaW5lRWRpdG9yX25vdGhpbmdTZWxlY3RlZDogXCJQaWxpaCB0aXRpayB1bnR1ayBtZW5nZWRpdCAodGVrYW4gU0hJRlQgdW50dWsgcGlsaWggYmFueWFrKSwgYXRhdSB0ZWthbiBBbHQgZGFuIGtsaWsgdW50dWsgdGFtYmFoa2FuIHRpdGlrIGJhcnVcIixcbiAgcGxhY2VJbWFnZTogXCJLbGlrIHVudHVrIHRlbXBhdGthbiBnYW1iYXIsIGF0YXUga2xpayBkYW4gamF0dWhrYW4gdW50dWsgdGV0YXBrYW4gdWt1cmFuIHNlY2FyYSBtYW51YWxcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiVGVyYml0a2FuIHB1c3Rha2EgQW5kYVwiLFxuICBiaW5kVGV4dFRvRWxlbWVudDogXCJUZWthbiBlbnRlciB1bnR1ayB0YW1iYWhrYW4gdGVrc1wiLFxuICBkZWVwQm94U2VsZWN0OiBcIlRla2FuIEN0cmwgYXRhdSBDbWQgdW50dWsgbWVtaWxpaCB5YW5nIGRpIGRhbGFtLCBkYW4gbWVuY2VnYWggcGVuZ2dlc2VyYW5cIixcbiAgZXJhc2VyUmV2ZXJ0OiBcIlRhaGFuIEFsdCB1bnR1ayBtZW5nZW1iYWxpa2FuIGVsZW1lbiB5YW5nIGRpdGFuZGFpIHVudHVrIGRpaGFwdXNcIixcbiAgZmlyZWZveF9jbGlwYm9hcmRfd3JpdGU6ICdGaXR1ciBpbmkgZGFwYXQgZGlha3RpZmthbiBtZWxhbHVpIHBlbmdhdHVyYW4gZmxhZyBcImRvbS5ldmVudHMuYXN5bmNDbGlwYm9hcmQuY2xpcGJvYXJkSXRlbVwiIGtlIFwidHJ1ZVwiLiBVbnR1ayBtZW5nZ2FudGkgZmxhZyBkaSBGaXJlZm94LCBwZXJnaSBrZSBsYW1hbiBcImFib3V0OmNvbmZpZ1wiLicsXG4gIGRpc2FibGVTbmFwcGluZzogXCJcIlxufTtcbnZhciBjYW52YXNFcnJvciA9IHtcbiAgY2Fubm90U2hvd1ByZXZpZXc6IFwiVGlkYWsgZGFwYXQgbWVuYW1waWxrYW4gcHJhdGluamF1XCIsXG4gIGNhbnZhc1Rvb0JpZzogXCJLYW52YXMgbXVuZ2tpbiB0ZXJsYWx1IGJlc2FyLlwiLFxuICBjYW52YXNUb29CaWdUaXA6IFwiVGlwOiBjb2JhIHBpbmRhaGthbiBlbGVtZW4tdGVyamF1aCBsZWJpaCBkZWthdCBiZXJzYW1hLlwiXG59O1xudmFyIGVycm9yU3BsYXNoID0ge1xuICBoZWFkaW5nTWFpbjogXCJNZW5nYWxhbWkgc2VidWFoIGtlc2FsYWhhbi4gQ29iYWxhaCA8YnV0dG9uPm11YXQgdWxhbmcgaGFsYW1hbi48L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNNZXNzYWdlOiBcIkppa2EgbWVtdWF0IHVsYW5nIHRpZGFrIGJla2VyamEsIGNvYmFsYWggPGJ1dHRvbj5iZXJzaWhrYW4gY2FudmFzLjwvYnV0dG9uPlwiLFxuICBjbGVhckNhbnZhc0NhdmVhdDogXCIgSW5pIGFrYW4gbWVuZ2hhc2lsa2FuIGhpbGFuZ255YSBwZWtlcmphYW4gXCIsXG4gIHRyYWNrZWRUb1NlbnRyeTogXCJLZXNhbGFoYW4gZGVuZ2FuIHBlbmdpZGVudGlmaWthc2kge3tldmVudElkfX0gZGlsYWNhayBkaSBzaXN0ZW0ga2FtaS5cIixcbiAgb3Blbklzc3VlTWVzc2FnZTogXCJLYW1pIHNhbmdhdCBiZXJoYXRpLWhhdGkgdW50dWsgdGlkYWsgbWVueWVydGFrYW4gaW5mb3JtYXNpIHBlbWFuZGFuZ2FuIEFuZGEgcGFkYSBrZXNhbGFoYW4uIEppa2EgcGVtYW5kYW5nYW4gQW5kYSB0aWRhayBiZXJzaWZhdCBwcmliYWRpLCBtb2hvbiBwZXJ0aW1iYW5na2FuIG1lbmluZGFrIGxhbmp1dCBwYWRhIDxidXR0b24+cGVsYWNhayBidWcuPC9idXR0b24+IE1vaG9uIHNlcnRha2FuIGluZm9ybWFzaSBkaWJhd2FoIGluaSBkZW5nYW4gbWVueWFsaW4gZGFuIG1lbmVtcGVsa2FuIGRpIEdpdGh1YiBpc3N1ZS5cIixcbiAgc2NlbmVDb250ZW50OiBcIlBlbWFuZGFuZ2FuIGtvbnRlbjpcIlxufTtcbnZhciByb29tRGlhbG9nID0ge1xuICBkZXNjX2ludHJvOiBcIkFuZGEgZGFwYXQgbWVuZ3VuZGFuZyBvcmFuZyBrZSBwZW1hbmRhbmdhbiBBbmRhIHNhYXQgaW5pIHVudHVrIGJlcmtvbGFib3Jhc2kgZGVuZ2FuIEFuZGEuXCIsXG4gIGRlc2NfcHJpdmFjeTogXCJKYW5nYW4ga2hhd2F0aXIsIHNlc2kgbWVuZ2d1bmFrYW4gZW5rcmlwc2kgZW5kLXRvLWVuZCwgc2VoaW5nZ2EgYXBhIHB1biB5YW5nIEFuZGEgZ2FtYmFyIGFrYW4gdGV0YXAgYmVyc2lmYXQgcHJpYmFkaS4gQmFoa2FuIHNlcnZlciBrYW1pIHRpZGFrIGRhcGF0IG1lbGloYXQgYXBhIHlhbmcgQW5kYSBsYWt1a2FuLlwiLFxuICBidXR0b25fc3RhcnRTZXNzaW9uOiBcIk11bGFpIHNlc2lcIixcbiAgYnV0dG9uX3N0b3BTZXNzaW9uOiBcIkhlbnRpa2FuIHNlc2lcIixcbiAgZGVzY19pblByb2dyZXNzSW50cm86IFwiU2VzaSBrb2xhYm9yYXNpIHNlZGFuZyBiZXJsYW5nc3VuZyBzZWthcmFuZy5cIixcbiAgZGVzY19zaGFyZUxpbms6IFwiQmFnaWthbiB0YXV0YW4gaW5pIGRlbmdhbiBzaWFwYSBwdW4geWFuZyBBbmRhIGluZ2lua2FuIHVudHVrIGtvbGFib3Jhc2kgYmVyc2FtYTpcIixcbiAgZGVzY19leGl0U2Vzc2lvbjogXCJNZW5naGVudGlrYW4gc2VzaSBha2FuIG1lbXV0dXNrYW4gaHVidW5nYW4gQW5kYSBkYXJpIHJ1YW5nYW4sIHRldGFwaSBBbmRhIGRhcGF0IG1lbGFuanV0a2FuIGJla2VyamEgZGVuZ2FuIHBlbWFuZGFuZ2FuIEFuZGEgc2VjYXJhIGxva2FsLiBQZXJoYXRpa2FuIGJhaHdhIGluaSB0aWRhayBtZW1lbmdhcnVoaSBvcmFuZyBsYWluLCBkYW4gbWVyZWthIG1hc2loIGRhcGF0IGJlcmtvbGFib3Jhc2kgcGFkYSB2ZXJzaSBtZXJla2EuXCIsXG4gIHNoYXJlVGl0bGU6IFwiR2FidW5nIHNlc2kga29sYWJvcmFzaSBsYW5nc3VuZyBkaSBFeGNhbGlkcmF3XCJcbn07XG52YXIgZXJyb3JEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIktlc2FsYWhhblwiXG59O1xudmFyIGV4cG9ydERpYWxvZyA9IHtcbiAgZGlza190aXRsZTogXCJTaW1wYW4ga2UgZGlza1wiLFxuICBkaXNrX2RldGFpbHM6IFwiRWtzcG9yIGRhdGEgcGVtYW5kYW5nYW4ga2UgZmlsZSB5YW5nIG1hbmEgQW5kYSBkYXBhdCBpbXBvciBuYW50aS5cIixcbiAgZGlza19idXR0b246IFwiU2ltcGFuIGtlIGZpbGVcIixcbiAgbGlua190aXRsZTogXCJUYXV0YW5cIixcbiAgbGlua19kZXRhaWxzOiBcIkVrc3BvciBzZWJhZ2FpIHRhdXRhbiB5YW5nIGhhbnlhIGRpYmFjYS5cIixcbiAgbGlua19idXR0b246IFwiRWtzcG9yIGtlIHRhdXRhblwiLFxuICBleGNhbGlkcmF3cGx1c19kZXNjcmlwdGlvbjogXCJTaW1wYW4gcGVtYW5kYW5nYW4ga2UgcnVhbmcga2VyamEgRXhjYWxpZHJhdysgQW5kYS5cIixcbiAgZXhjYWxpZHJhd3BsdXNfYnV0dG9uOiBcIkVrc3BvclwiLFxuICBleGNhbGlkcmF3cGx1c19leHBvcnRFcnJvcjogXCJUaWRhayBkYXBhdCBla3Nwb3Iga2UgRXhjYWxpZHJhdysgc2FhdCBpbmkuLi5cIlxufTtcbnZhciBoZWxwRGlhbG9nID0ge1xuICBibG9nOiBcIkJhY2EgYmxvZyBrYW1pXCIsXG4gIGNsaWNrOiBcImtsaWtcIixcbiAgZGVlcFNlbGVjdDogXCJQaWxpaCBkYWxhbVwiLFxuICBkZWVwQm94U2VsZWN0OiBcIlBpbGloIGRhbGFtIGtvdGFrLCBkYW4gY2VnYWggcGVuZ2dlc2VyYW5cIixcbiAgY3VydmVkQXJyb3c6IFwiUGFuYWggbGVuZ2t1bmdcIixcbiAgY3VydmVkTGluZTogXCJHYXJpcyBsZW5na3VuZ1wiLFxuICBkb2N1bWVudGF0aW9uOiBcIkRva3VtZW50YXNpXCIsXG4gIGRvdWJsZUNsaWNrOiBcImtsaWstZ2FuZGFcIixcbiAgZHJhZzogXCJzZXJldFwiLFxuICBlZGl0b3I6IFwiRWRpdG9yXCIsXG4gIGVkaXRMaW5lQXJyb3dQb2ludHM6IFwiRWRpdCB0aXRpayBnYXJpcy9wYW5haFwiLFxuICBlZGl0VGV4dDogXCJFZGl0IHRla3MgLyB0YW1iYWggbGFiZWxcIixcbiAgZ2l0aHViOiBcIk1lbmVtdWthbiBtYXNhbGFoPyBLaXJpbWthblwiLFxuICBob3d0bzogXCJJa3V0aSBwYW5kdWFuIGthbWlcIixcbiAgb3I6IFwiYXRhdVwiLFxuICBwcmV2ZW50QmluZGluZzogXCJDZWdhaCBwZW5naWthdGFuIHBhbmFoXCIsXG4gIHRvb2xzOiBcIkFsYXRcIixcbiAgc2hvcnRjdXRzOiBcIlBpbnRhc2FuIGtleWJvYXJkXCIsXG4gIHRleHRGaW5pc2g6IFwiU2VsZXNhaSBtZW5nZWRpdCAoZWRpdG9yIHRla3MpXCIsXG4gIHRleHROZXdMaW5lOiBcIlRhbWJhaGthbiBnYXJpcyBiYXJ1IChlZGl0b3IgdGVrcylcIixcbiAgdGl0bGU6IFwiQmFudHVhblwiLFxuICB2aWV3OiBcIlRhbXBpbGFuXCIsXG4gIHpvb21Ub0ZpdDogXCJQZXJiZXNhciBhZ2FyIHNlc3VhaSBkZW5nYW4gc2VtdWEgZWxlbWVuXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJQZXJiZXNhciBrZSBzZWxla3NpXCIsXG4gIHRvZ2dsZUVsZW1lbnRMb2NrOiBcIkt1bmNpL2xlcGFzIHNlbGVrc2lcIixcbiAgbW92ZVBhZ2VVcERvd246IFwiUGluZGFoIGhhbGFtYW4ga2VhdGFzL2tlYmF3YWhcIixcbiAgbW92ZVBhZ2VMZWZ0UmlnaHQ6IFwiUGluZGFoIGhhbGFtYW4ga2ViYXdhaC9rZWF0YXNcIlxufTtcbnZhciBjbGVhckNhbnZhc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiSGFwdXMga2FudmFzXCJcbn07XG52YXIgcHVibGlzaERpYWxvZyA9IHtcbiAgdGl0bGU6IFwiVGVyYml0a2FuIHB1c3Rha2FcIixcbiAgaXRlbU5hbWU6IFwiTmFtYSBpdGVtXCIsXG4gIGF1dGhvck5hbWU6IFwiTmFtYSBwZW1idWF0XCIsXG4gIGdpdGh1YlVzZXJuYW1lOiBcIk5hbWEgcGVuZ2d1bmEgZ2l0aHViXCIsXG4gIHR3aXR0ZXJVc2VybmFtZTogXCJOYW1hIHBlbmdndW5hIFR3aXR0ZXJcIixcbiAgbGlicmFyeU5hbWU6IFwiTmFtYSBQdXN0YWthXCIsXG4gIGxpYnJhcnlEZXNjOiBcIkRlc2tyaXBzaSBwdXN0YWthXCIsXG4gIHdlYnNpdGU6IFwiU2l0dXMgV2ViXCIsXG4gIHBsYWNlaG9sZGVyOiB7XG4gICAgYXV0aG9yTmFtZTogXCJOYW1hIGF0YXUgbmFtYSBwZW5nZ3VuYSBBbmRhXCIsXG4gICAgbGlicmFyeU5hbWU6IFwiTmFtYSBkYXJpIHB1c3Rha2EgQW5kYVwiLFxuICAgIGxpYnJhcnlEZXNjOiBcIkRlc2tyaXBzaSBwdXN0YWthIEFuZGEgdW50dWsgbWVtYmFudHUgb3JhbmcgbWVuZ2VydGkgcGVuZ2d1bmFhbm55YVwiLFxuICAgIGdpdGh1YkhhbmRsZTogXCJBa3VuIEdpdEh1YiAob3BzaW9uYWwpLCBqYWRpIEFuZGEgZGFwYXQgbWVuZ3ViYWggcHVzdGFrYSBrZXRpa2EgZGlzZXJhaGthbiB1bnR1ayByZXZpZXdcIixcbiAgICB0d2l0dGVySGFuZGxlOiBcIk5hbWEgcGVuZ2d1bmEgVHdpdHRlciAob3BzaW9uYWwpLCBqYWRpIGthbWkgdGFodSBzaWFwYSBkaXB1amkga2V0aWthIG1lbXByb21vc2lrYW5ueWEgbWVsYWx1aSBUd2l0dGVyXCIsXG4gICAgd2Vic2l0ZTogXCJIdWJ1bmdrYW4ga2Ugc2l0dXMgcGVyc29uYWwgQW5kYSBhdGF1IGxhaW5ueWEgKG9wc2lvbmFsKVwiXG4gIH0sXG4gIGVycm9yczoge1xuICAgIHJlcXVpcmVkOiBcIkRpYnV0dWhrYW5cIixcbiAgICB3ZWJzaXRlOiBcIk1hc3Vra2FuIFVSTCB2YWxpZFwiXG4gIH0sXG4gIG5vdGVEZXNjcmlwdGlvbjogXCJLaXJpbWthbiBwdXN0YWthIEFuZGEgdW50dWsgZGlzZXJ0YWthbiBkaSA8bGluaz5yZXBvc2l0b3JpIHB1c3Rha2EgcHVibGlrPC9saW5rPnVudHVrIG9yYW5nIGxhaW4gbWVuZ2d1bmFrYW5ueWEgZGFsYW0gZ2FtYmFyIG1lcmVrYS5cIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiUHVzdGFrYSBidXR1aCBkaXNldHVqdWkgc2VjYXJhIG1hbnVhbCB0ZXJsZWJpaCBkYWh1bHUuIEJhY2EgPGxpbms+cGVkb21hbjwvbGluaz4gc2ViZWx1bSBtZW5naXJpbS4gQW5kYSBidXR1aCBha3VuIEdpdEh1YiB1bnR1ayBiZXJrb211bmlrYXNpIGRhbiBtZW1idWF0IHBlcnViYWhhbiBqaWthIGRpYnV0dWhrYW4sIHRldGFwaSB0aWRhayB3YWppYiBkaWJ1dHVrYW4uXCIsXG4gIG5vdGVMaWNlbnNlOiBcIkRlbmdhbiBtZW5na2lyaW1rYW5ueWEsIEFuZGEgc2V0dWp1IHB1c3Rha2EgYWthbiBkaXRlcmJpdGthbiBkaWJhd2FoIDxsaW5rPkxpc2Vuc2kgTUlULCA8L2xpbms+eWFuZyBhcnRpbnlhIHNpYXBhIHB1biBkYXBhdCBtZW5nZ3VuYWthbm55YSB0YW5wYSBiYXRhc2FuLlwiLFxuICBub3RlSXRlbXM6IFwiU2V0aWFwIGl0ZW0gcHVzdGFrYSBoYXJ1cyBtZW1pbGlraSBuYW1hLCBzZWhpbmdnYSBiaXNhIGRpc29ydGlyLiBJdGVtIHB1c3Rha2EgZGkgYmF3YWggaW5pIGFrYW4gZGltYXN1a2FuOlwiLFxuICBhdGxlYXN0T25lTGliSXRlbTogXCJQaWxpaCBzZXRpZGFrbnlhIHNhdHUgaXRlbSBwdXN0YWthIHVudHVrIG11bGFpXCIsXG4gIHJlcHVibGlzaFdhcm5pbmc6IFwiQ2F0YXRhbjogYmViZXJhcGEgaXRlbSB5YW5nIGRpcGlsaWggdGVsYWggZGl0YW5kYWkgc2ViYWdhaSBzdWRhaCBkaXB1Ymxpa2FzaWthbi9kaXNlcmFoa2FuLiBBbmRhIGhhbnlhIGRhcGF0IG1lbnllcmFoa2FuIGtlbWJhbGkgaXRlbS1pdGVtIGtldGlrYSBtZW1wZXJiYXJ1aSBwdXN0YWthIGF0YXUgcGVuZ3VtcHVsYW4uXCJcbn07XG52YXIgcHVibGlzaFN1Y2Nlc3NEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIlB1c3Rha2EgdGVsYWggZGlraXJtXCIsXG4gIGNvbnRlbnQ6IFwiVGVyaW1hIGthc2loIHt7YXV0aG9yTmFtZX19LiBwdXN0YWthIEFuZGEgdGVsYWggZGlzZXJhaGthbiB1bnR1ayBkaXRpbmphdSB1bGFuZy4gQW5kYSBkYXBhdCBjZWsgc3RhdHVzbnlhPGxpbms+ZGkgc2luaTwvbGluaz5cIlxufTtcbnZhciBjb25maXJtRGlhbG9nID0ge1xuICByZXNldExpYnJhcnk6IFwiUmVzZXQgcHVzdGFrYVwiLFxuICByZW1vdmVJdGVtc0Zyb21MaWI6IFwiSGFwdXMgaXRlbSB5YW5nIGRpcGlsaWggZGFyaSBwdXN0YWthXCJcbn07XG52YXIgaW1hZ2VFeHBvcnREaWFsb2cgPSB7XG4gIGhlYWRlcjogXCJFa3Nwb3IgZ2FtYmFyXCIsXG4gIGxhYmVsOiB7XG4gICAgd2l0aEJhY2tncm91bmQ6IFwiTGF0YXJcIixcbiAgICBvbmx5U2VsZWN0ZWQ6IFwiSGFueWEgeWFuZyBkaXBpbGloXCIsXG4gICAgZGFya01vZGU6IFwiTW9kZSBnZWxhcFwiLFxuICAgIGVtYmVkU2NlbmU6IFwiU2VtYXRrYW4gcGVtYW5kYW5nYW5cIixcbiAgICBzY2FsZTogXCJTa2FsYVwiLFxuICAgIHBhZGRpbmc6IFwiTGFwaXNhblwiXG4gIH0sXG4gIHRvb2x0aXA6IHtcbiAgICBlbWJlZFNjZW5lOiBcIkRhdGEgcGVtYW5kYW5nYW4gYWthbiBkaXNpbXBhbiBkYWxhbSBmaWxlIFBORy9TVkcgeWFuZyBkaWVrc3BvciBzZWhpbmdnYSBwZW1hbmRhbmdhbiBpdHUgZGFwYXQgZGlwdWxpaGthbiBkYXJpbnlhLlxcbkFrYW4gbWVtYmVzYXJrYW4gdWt1cmFuIGZpbGUgeWFuZyBkaWVrc3Bvci5cIlxuICB9LFxuICB0aXRsZToge1xuICAgIGV4cG9ydFRvUG5nOiBcIkVrc3BvciBrZSBQTkdcIixcbiAgICBleHBvcnRUb1N2ZzogXCJFa3Nwb3Iga2UgU1ZHXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIlNhbGluIFBORyBrZSBwYXBhbiBrbGlwXCJcbiAgfSxcbiAgYnV0dG9uOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiUE5HXCIsXG4gICAgZXhwb3J0VG9Tdmc6IFwiU1ZHXCIsXG4gICAgY29weVBuZ1RvQ2xpcGJvYXJkOiBcIlNhbGluIGtlIHBhcGFuIGtsaXBcIlxuICB9XG59O1xudmFyIGVuY3J5cHRlZCA9IHtcbiAgdG9vbHRpcDogXCJHYW1iYXIgYW5kYSB0ZXJlbmtyaXBzaSBlbmQtdG8tZW5kIHNlaGluZ2dhIHNlcnZlciBFeGNhbGlkcmF3IHRpZGFrIGFrYW4gcGVybmFoIGRhcGF0IG1lbGloYXRueWEuXCIsXG4gIGxpbms6IFwiUG9zIGJsb2cgdGVudGFuZyBlbmtyaXBzaSB1anVuZyBrZSB1anVuZyBkaSBFeGNhbGlkcmF3XCJcbn07XG52YXIgc3RhdHMgPSB7XG4gIGFuZ2xlOiBcIlN1ZHV0XCIsXG4gIGVsZW1lbnQ6IFwiRWxlbWVuXCIsXG4gIGVsZW1lbnRzOiBcIkVsZW1lblwiLFxuICBoZWlnaHQ6IFwiVGluZ2dpXCIsXG4gIHNjZW5lOiBcIlBlbWFuZGFuZ2FuXCIsXG4gIHNlbGVjdGVkOiBcIlRlcnBpbGloXCIsXG4gIHN0b3JhZ2U6IFwiUGVueWltcGFuYW5cIixcbiAgdGl0bGU6IFwiU3RhdGlzdGlrIHVudHVrIG5lcmRcIixcbiAgdG90YWw6IFwiVG90YWxcIixcbiAgdmVyc2lvbjogXCJWZXJzaVwiLFxuICB2ZXJzaW9uQ29weTogXCJLbGlrIHVudHVrIHNhbGluXCIsXG4gIHZlcnNpb25Ob3RBdmFpbGFibGU6IFwiVmVyc2kgdGlkYWsgdGVyc2VkaWFcIixcbiAgd2lkdGg6IFwiTGViYXJcIlxufTtcbnZhciB0b2FzdCA9IHtcbiAgYWRkZWRUb0xpYnJhcnk6IFwiVGFtYmFoa2FuIGtlIHB1c3Rha2FcIixcbiAgY29weVN0eWxlczogXCJHYXlhIHRlcnNhbGluLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiVGVyc2FsaW4ga2UgcGFwYW4ga2xpcC5cIixcbiAgY29weVRvQ2xpcGJvYXJkQXNQbmc6IFwiVGVyc2FsaW4ge3tleHBvcnRTZWxlY3Rpb259fSBrZSBjbGlwYm9hcmQgc2ViYWdhaSBQTkdcXG4oe3tleHBvcnRDb2xvclNjaGVtZX19KVwiLFxuICBmaWxlU2F2ZWQ6IFwiRmlsZSB0ZXJzaW1wYW4uXCIsXG4gIGZpbGVTYXZlZFRvRmlsZW5hbWU6IFwiRGlzaW1wYW4ga2Uge2ZpbGVuYW1lfVwiLFxuICBjYW52YXM6IFwia2FudmFzXCIsXG4gIHNlbGVjdGlvbjogXCJwaWxpaGFuXCIsXG4gIHBhc3RlQXNTaW5nbGVFbGVtZW50OiBcIkd1bmFrYW4ge3tzaG9ydGN1dH19IHVudHVrIG1lbmVtcGVsa2FuIHNlYmFnYWkgc2F0dSBlbGVtZW4sXFxuYXRhdSB0ZW1wZWxrYW4ga2UgdGVrcyBlZGl0b3IgeWFuZyBhZGFcIixcbiAgdW5hYmxlVG9FbWJlZDogXCJcIixcbiAgdW5yZWNvZ25pemVkTGlua0Zvcm1hdDogXCJcIlxufTtcbnZhciBjb2xvcnMgPSB7XG4gIHRyYW5zcGFyZW50OiBcIlRyYW5zcGFyYW5cIixcbiAgYmxhY2s6IFwiSGl0YW1cIixcbiAgd2hpdGU6IFwiUHV0aWhcIixcbiAgcmVkOiBcIk1lcmFoXCIsXG4gIHBpbms6IFwiUGlua1wiLFxuICBncmFwZTogXCJVbmd1XCIsXG4gIHZpb2xldDogXCJWaW9sZXRcIixcbiAgZ3JheTogXCJBYnUtYWJ1XCIsXG4gIGJsdWU6IFwiQmlydVwiLFxuICBjeWFuOiBcIkN5YW5cIixcbiAgdGVhbDogXCJUZWFsXCIsXG4gIGdyZWVuOiBcIkhpamF1XCIsXG4gIHllbGxvdzogXCJLdW5pbmdcIixcbiAgb3JhbmdlOiBcIkppbmdnYVwiLFxuICBicm9uemU6IFwiVGVtYmFnYVwiXG59O1xudmFyIHdlbGNvbWVTY3JlZW4gPSB7XG4gIGFwcDoge1xuICAgIGNlbnRlcl9oZWFkaW5nOiBcIlNlbXVhIGRhdGEgQW5kYSBkaXNpbXBhbiBzZWNhcmEgbG9rYWwgZGkgcGVyYW1iYW4gQW5kYS5cIixcbiAgICBjZW50ZXJfaGVhZGluZ19wbHVzOiBcIkFwYSBBbmRhIGluZ2luIGJlcnBpbmRhaCBrZSBFeGNhbGlkcmF3Kz9cIixcbiAgICBtZW51SGludDogXCJFa3Nwb3IsIHByZWZlcmVuc2ksIGJhaGFzYSwgLi4uXCJcbiAgfSxcbiAgZGVmYXVsdHM6IHtcbiAgICBtZW51SGludDogXCJFa3Nwb3IsIHByZWZlcmVuc2ksIGRhbiBzZWxlYmlobnlhLi4uXCIsXG4gICAgY2VudGVyX2hlYWRpbmc6IFwiRGlhZ3JhbS4gTWVuamFkaS4gTXVkYWguXCIsXG4gICAgdG9vbGJhckhpbnQ6IFwiUGlsaWggYWxhdCAmIG11bGFpIG1lbmdnYW1iYXIhXCIsXG4gICAgaGVscEhpbnQ6IFwiUGludGFzYW4gJiBiYW50dWFuXCJcbiAgfVxufTtcbnZhciBjb2xvclBpY2tlciA9IHtcbiAgbW9zdFVzZWRDdXN0b21Db2xvcnM6IFwiV2FybmEgeWFuZyBzZXJpbmcgZGlwYWthaVwiLFxuICBjb2xvcnM6IFwiV2FybmFcIixcbiAgc2hhZGVzOiBcIk51YW5zYVwiLFxuICBoZXhDb2RlOiBcIktvZGUgaGV4YVwiLFxuICBub1NoYWRlczogXCJUaWRhayBhZGEgbnVhbnNhIHVudHVrIHdhcm5hIGluaVwiXG59O1xudmFyIG92ZXJ3cml0ZUNvbmZpcm0gPSB7XG4gIGFjdGlvbjoge1xuICAgIGV4cG9ydFRvSW1hZ2U6IHtcbiAgICAgIHRpdGxlOiBcIkVrc3BvciBzZWJhZ2FpIGdhbWJhclwiLFxuICAgICAgYnV0dG9uOiBcIkVrc3BvciBzZWJhZ2FpIGdhbWJhclwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRWtzcG9yIGRhdGEgcGVtYW5kYW5nYW4gc2ViYWdhaSBnYW1iYXIgeWFuZyBkYXBhdCBhbmRhIGltcG9yIG5hbnRpLlwiXG4gICAgfSxcbiAgICBzYXZlVG9EaXNrOiB7XG4gICAgICB0aXRsZTogXCJTaW1wYW4ga2UgZGlza1wiLFxuICAgICAgYnV0dG9uOiBcIlNpbXBhbiBrZSBkaXNrXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJFa3Nwb3IgZGF0YSBwZW1hbmRhbmdhbiBrZSBmaWxlIHlhbmcgZGFwYXQgQW5kYSBkYXBhdCBpbXBvciBuYW50aS5cIlxuICAgIH0sXG4gICAgZXhjYWxpZHJhd1BsdXM6IHtcbiAgICAgIHRpdGxlOiBcIkV4Y2FsaWRyYXcrXCIsXG4gICAgICBidXR0b246IFwiRWtzcG9yIGtlIEV4Y2FsaWRyYXcrXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJTaW1wYW4gcGVtYW5kYW5nYW4ga2UgcnVhbmcga2VyamEgRXhjYWxpZHJhdysgQW5kYS5cIlxuICAgIH1cbiAgfSxcbiAgbW9kYWw6IHtcbiAgICBsb2FkRnJvbUZpbGU6IHtcbiAgICAgIHRpdGxlOiBcIk11YXQgZGFyaSBmaWxlXCIsXG4gICAgICBidXR0b246IFwiTXVhdCBkYXJpIGZpbGVcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIk1lbXVhdCBkYXJpIGZpbGUgeWFuZyBha2FuIDxib2xkPm1lbmdnYW50aWthbiBrb250ZW4gQW5kYSBzZWthcmFuZzwvYm9sZD4uPGJyPjwvYnI+QW5kYSBkYXBhdCBtZW5jYWRhbmdrYW4gZ2FtYmFyIGFuZGEgZHVsdSBtZW5nZ3VuYWthbiBvcHNpLW9wc2kgaW5pLlwiXG4gICAgfSxcbiAgICBzaGFyZWFibGVMaW5rOiB7XG4gICAgICB0aXRsZTogXCJNdWF0IGRhcmkgbGlua1wiLFxuICAgICAgYnV0dG9uOiBcIkdhbnRpIGtvbnRlbiBzYXlhXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJNZW11YXQgZGFyaSBmaWxlIHlhbmcgYWthbiA8Ym9sZD5tZW5nZ2FudGlrYW4ga29udGVuIEFuZGEgc2VrYXJhbmc8L2JvbGQ+Ljxicj48L2JyPkFuZGEgZGFwYXQgbWVuY2FkYW5na2FuIGdhbWJhciBhbmRhIGR1bHUgbWVuZ2d1bmFrYW4gb3BzaS1vcHNpIGluaS5cIlxuICAgIH1cbiAgfVxufTtcbnZhciBtZXJtYWlkID0ge1xuICB0aXRsZTogXCJcIixcbiAgYnV0dG9uOiBcIlwiLFxuICBkZXNjcmlwdGlvbjogXCJcIixcbiAgc3ludGF4OiBcIlwiLFxuICBwcmV2aWV3OiBcIlwiXG59O1xudmFyIGlkX0lEX2RlZmF1bHQgPSB7XG4gIGxhYmVscyxcbiAgbGlicmFyeSxcbiAgYnV0dG9ucyxcbiAgYWxlcnRzLFxuICBlcnJvcnMsXG4gIHRvb2xCYXIsXG4gIGhlYWRpbmdzLFxuICBoaW50cyxcbiAgY2FudmFzRXJyb3IsXG4gIGVycm9yU3BsYXNoLFxuICByb29tRGlhbG9nLFxuICBlcnJvckRpYWxvZyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWxwRGlhbG9nLFxuICBjbGVhckNhbnZhc0RpYWxvZyxcbiAgcHVibGlzaERpYWxvZyxcbiAgcHVibGlzaFN1Y2Nlc3NEaWFsb2csXG4gIGNvbmZpcm1EaWFsb2csXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBlbmNyeXB0ZWQsXG4gIHN0YXRzLFxuICB0b2FzdCxcbiAgY29sb3JzLFxuICB3ZWxjb21lU2NyZWVuLFxuICBjb2xvclBpY2tlcixcbiAgb3ZlcndyaXRlQ29uZmlybSxcbiAgbWVybWFpZFxufTtcbmV4cG9ydCB7XG4gIGFsZXJ0cyxcbiAgYnV0dG9ucyxcbiAgY2FudmFzRXJyb3IsXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBjb2xvclBpY2tlcixcbiAgY29sb3JzLFxuICBjb25maXJtRGlhbG9nLFxuICBpZF9JRF9kZWZhdWx0IGFzIGRlZmF1bHQsXG4gIGVuY3J5cHRlZCxcbiAgZXJyb3JEaWFsb2csXG4gIGVycm9yU3BsYXNoLFxuICBlcnJvcnMsXG4gIGV4cG9ydERpYWxvZyxcbiAgaGVhZGluZ3MsXG4gIGhlbHBEaWFsb2csXG4gIGhpbnRzLFxuICBpbWFnZUV4cG9ydERpYWxvZyxcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBtZXJtYWlkLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgcm9vbURpYWxvZyxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICB0b29sQmFyLFxuICB3ZWxjb21lU2NyZWVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aWQtSUQtMjJUV1pOTEEuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/id-ID-22TWZNLA.js\n"));

/***/ })

}]);