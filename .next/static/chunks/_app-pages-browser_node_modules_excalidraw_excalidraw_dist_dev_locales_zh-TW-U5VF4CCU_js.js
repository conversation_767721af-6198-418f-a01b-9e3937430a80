"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_zh-TW-U5VF4CCU_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-TW-U5VF4CCU.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-TW-U5VF4CCU.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ zh_TW_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/zh-TW.json\nvar labels = {\n  paste: \"\\u8CBC\\u4E0A\",\n  pasteAsPlaintext: \"\\u4EE5\\u7D14\\u6587\\u5B57\\u8CBC\\u4E0A\",\n  pasteCharts: \"\\u8CBC\\u4E0A\\u5716\\u8868\",\n  selectAll: \"\\u5168\\u9078\",\n  multiSelect: \"\\u5C07\\u7269\\u4EF6\\u52A0\\u5165\\u9078\\u53D6\\u7BC4\\u570D\",\n  moveCanvas: \"\\u79FB\\u52D5\\u756B\\u5E03\",\n  cut: \"\\u526A\\u4E0B\",\n  copy: \"\\u8907\\u88FD\",\n  copyAsPng: \"\\u4EE5PNG\\u683C\\u5F0F\\u5132\\u5B58\\u5230\\u526A\\u8CBC\\u677F\",\n  copyAsSvg: \"\\u4EE5SVG\\u683C\\u5F0F\\u8907\\u88FD\\u5230\\u526A\\u8CBC\\u677F\",\n  copyText: \"\\u4EE5\\u6587\\u5B57\\u683C\\u5F0F\\u8907\\u88FD\\u81F3\\u526A\\u8CBC\\u7C3F\",\n  copySource: \"\\u8907\\u88FD\\u4F86\\u6E90\\u81F3\\u526A\\u8CBC\\u7C3F\",\n  convertToCode: \"\\u8F49\\u63DB\\u70BA\\u7A0B\\u5F0F\\u78BC\",\n  bringForward: \"\\u4E0A\\u79FB\\u4E00\\u5C64\",\n  sendToBack: \"\\u79FB\\u5230\\u6700\\u5E95\\u5C64\",\n  bringToFront: \"\\u7F6E\\u65BC\\u6700\\u9802\\u5C64\",\n  sendBackward: \"\\u5F80\\u5F8C\\u79FB\\u4E00\\u5C64\",\n  delete: \"\\u522A\\u9664\",\n  copyStyles: \"\\u8907\\u88FD\\u6A23\\u5F0F\",\n  pasteStyles: \"\\u8CBC\\u4E0A\\u6A23\\u5F0F\",\n  stroke: \"\\u7B46\\u756B\",\n  background: \"\\u80CC\\u666F\",\n  fill: \"\\u586B\\u6EFF\",\n  strokeWidth: \"\\u7B46\\u8DE1\\u5BEC\\u5EA6\",\n  strokeStyle: \"\\u7B46\\u756B\\u6A23\\u5F0F\",\n  strokeStyle_solid: \"\\u5BE6\\u7DDA\",\n  strokeStyle_dashed: \"\\u865B\\u7DDA\",\n  strokeStyle_dotted: \"\\u9EDE\\u7DDA\",\n  sloppiness: \"\\u7DDA\\u689D\\u98A8\\u683C\",\n  opacity: \"\\u900F\\u660E\\u5EA6\",\n  textAlign: \"\\u6587\\u5B57\\u5C0D\\u9F4A\",\n  edges: \"\\u908A\\u7DE3\",\n  sharp: \"\\u5C16\\u92B3\",\n  round: \"\\u5E73\\u6ED1\",\n  arrowheads: \"\\u7BAD\\u982D\",\n  arrowhead_none: \"\\u7121\",\n  arrowhead_arrow: \"\\u7BAD\\u982D\",\n  arrowhead_bar: \"\\u689D\\u72C0\\u7BAD\\u982D\",\n  arrowhead_circle: \"\\u5713\\u5F62\",\n  arrowhead_circle_outline: \"\\u5713\\u5F62\\uFF08\\u5916\\u6846\\uFF09\",\n  arrowhead_triangle: \"\\u4E09\\u89D2\\u5F62\",\n  arrowhead_triangle_outline: \"\\u4E09\\u89D2\\u5F62\\uFF08\\u5916\\u6846\\uFF09\",\n  arrowhead_diamond: \"\\u83F1\\u5F62\",\n  arrowhead_diamond_outline: \"\\u83F1\\u5F62\\uFF08\\u5916\\u6846\\uFF09\",\n  fontSize: \"\\u5B57\\u578B\\u5927\\u5C0F\",\n  fontFamily: \"\\u5B57\\u9AD4\\u96C6\",\n  addWatermark: '\\u52A0\\u4E0A \"Made with Excalidraw\" \\u6D6E\\u6C34\\u5370',\n  handDrawn: \"\\u624B\\u5BEB\",\n  normal: \"\\u4E00\\u822C\",\n  code: \"\\u4EE3\\u78BC\",\n  small: \"\\u5C0F\",\n  medium: \"\\u4E2D\",\n  large: \"\\u5927\",\n  veryLarge: \"\\u7279\\u5927\",\n  solid: \"\\u5BE6\\u5FC3\",\n  hachure: \"\\u659C\\u7DDA\\u7B46\\u89F8\",\n  zigzag: \"\\uFF3A\\u5B57\\u5F62\",\n  crossHatch: \"\\u4EA4\\u53C9\\u7B46\\u89F8\",\n  thin: \"\\u7D30\",\n  bold: \"\\u7C97\",\n  left: \"\\u5DE6\\u5074\",\n  center: \"\\u7F6E\\u4E2D\",\n  right: \"\\u53F3\\u5074\",\n  extraBold: \"\\u6975\\u7C97\",\n  architect: \"\\u7CBE\\u78BA\",\n  artist: \"\\u85DD\\u8853\",\n  cartoonist: \"\\u5361\\u901A\",\n  fileTitle: \"\\u6A94\\u6848\\u540D\\u7A31\",\n  colorPicker: \"\\u8272\\u5F69\\u9078\\u64C7\\u5DE5\\u5177\",\n  canvasColors: \"\\u4F7F\\u7528\\u65BC\\u756B\\u5E03\",\n  canvasBackground: \"Canvas \\u80CC\\u666F\",\n  drawingCanvas: \"\\u7E6A\\u5716 canvas\",\n  layers: \"\\u5716\\u5C64\",\n  actions: \"\\u52D5\\u4F5C\",\n  language: \"\\u8A9E\\u8A00\",\n  liveCollaboration: \"\\u5373\\u6642\\u5354\\u4F5C...\",\n  duplicateSelection: \"\\u8907\\u88FD\",\n  untitled: \"\\u7121\\u6A19\\u984C\",\n  name: \"\\u540D\\u7A31\",\n  yourName: \"\\u4F60\\u7684\\u540D\\u7A31\",\n  madeWithExcalidraw: \"\\u4EE5 Excalidraw \\u88FD\\u4F5C\",\n  group: \"\\u5EFA\\u7ACB\\u7FA4\\u7D44\",\n  ungroup: \"\\u53D6\\u6D88\\u7FA4\\u7D44\",\n  collaborators: \"\\u5354\\u4F5C\\u8005\",\n  showGrid: \"\\u986F\\u793A\\u683C\\u7DDA\",\n  addToLibrary: \"\\u52A0\\u5165\\u8CC7\\u6599\\u5EAB\",\n  removeFromLibrary: \"\\u5F9E\\u8CC7\\u6599\\u5EAB\\u4E2D\\u79FB\\u9664\",\n  libraryLoadingMessage: \"\\u8CC7\\u6599\\u5EAB\\u8B80\\u53D6\\u4E2D\\u2026\",\n  libraries: \"\\u700F\\u89BD\\u8CC7\\u6599\\u5EAB\",\n  loadingScene: \"\\u5834\\u666F\\u8B80\\u53D6\\u4E2D\\u2026\",\n  align: \"\\u5C0D\\u9F4A\",\n  alignTop: \"\\u5C0D\\u9F4A\\u9802\\u90E8\",\n  alignBottom: \"\\u5C0D\\u9F4A\\u5E95\\u90E8\",\n  alignLeft: \"\\u5C0D\\u9F4A\\u5DE6\\u5074\",\n  alignRight: \"\\u5C0D\\u9F4A\\u53F3\\u5074\",\n  centerVertically: \"\\u5782\\u76F4\\u7F6E\\u4E2D\",\n  centerHorizontally: \"\\u6C34\\u5E73\\u7F6E\\u4E2D\",\n  distributeHorizontally: \"\\u6C34\\u5E73\\u5206\\u5E03\",\n  distributeVertically: \"\\u5782\\u76F4\\u5206\\u5E03\",\n  flipHorizontal: \"\\u6C34\\u5E73\\u7FFB\\u8F49\",\n  flipVertical: \"\\u5782\\u76F4\\u7FFB\\u8F49\",\n  viewMode: \"\\u6AA2\\u8996\\u6A21\\u5F0F\",\n  share: \"\\u5171\\u4EAB\",\n  showStroke: \"\\u986F\\u793A\\u7DDA\\u689D\\u6AA2\\u8272\\u5668\",\n  showBackground: \"\\u986F\\u793A\\u80CC\\u666F\\u6AA2\\u8272\\u5668\",\n  toggleTheme: \"\\u5207\\u63DB\\u4E3B\\u984C\",\n  personalLib: \"\\u500B\\u4EBA\\u8CC7\\u6599\\u5EAB\",\n  excalidrawLib: \"Excalidraw \\u8CC7\\u6599\\u5EAB\",\n  decreaseFontSize: \"\\u7E2E\\u5C0F\\u6587\\u5B57\",\n  increaseFontSize: \"\\u653E\\u5927\\u6587\\u5B57\",\n  unbindText: \"\\u53D6\\u6D88\\u7D81\\u5B9A\\u6587\\u5B57\",\n  bindText: \"\\u7D50\\u5408\\u6587\\u5B57\\u81F3\\u5BB9\\u5668\",\n  createContainerFromText: \"\\u5C07\\u6587\\u5B57\\u5305\\u65BC\\u5BB9\\u5668\\u4E2D\",\n  link: {\n    edit: \"\\u7DE8\\u8F2F\\u9023\\u7D50\",\n    editEmbed: \"\\u7DE8\\u8F2F\\u9023\\u7D50&\\u5D4C\\u5165\",\n    create: \"\\u5EFA\\u7ACB\\u9023\\u7D50\",\n    createEmbed: \"\\u5EFA\\u7ACB\\u9023\\u7D50&\\u5D4C\\u5165\",\n    label: \"\\u9023\\u7D50\",\n    labelEmbed: \"\\u9023\\u7D50&\\u5D4C\\u5165\",\n    empty: \"\\u672A\\u8A2D\\u5B9A\\u9023\\u7D50\"\n  },\n  lineEditor: {\n    edit: \"\\u7DE8\\u8F2F\\u7DDA\\u689D\",\n    exit: \"\\u7D50\\u675F\\u7DDA\\u689D\\u7DE8\\u8F2F\"\n  },\n  elementLock: {\n    lock: \"\\u9396\\u5B9A\",\n    unlock: \"\\u89E3\\u9396\",\n    lockAll: \"\\u5168\\u90E8\\u9396\\u5B9A\",\n    unlockAll: \"\\u5168\\u90E8\\u89E3\\u9396\"\n  },\n  statusPublished: \"\\u5DF2\\u767C\\u5E03\",\n  sidebarLock: \"\\u5074\\u6B04\\u7DAD\\u6301\\u958B\\u555F\",\n  selectAllElementsInFrame: \"\\u9078\\u53D6\\u6846\\u67B6\\u5167\\u7684\\u6240\\u6709\\u5143\\u7D20\",\n  removeAllElementsFromFrame: \"\\u5F9E\\u6846\\u67B6\\u5167\\u79FB\\u9664\\u6240\\u6709\\u5143\\u7D20\",\n  eyeDropper: \"\\u5F9E\\u756B\\u5E03\\u4E2D\\u9078\\u53D6\\u984F\\u8272\",\n  textToDiagram: \"\\u6587\\u5B57\\u8F49\\u5716\\u8868\",\n  prompt: \"\\u63D0\\u793A\\u8A5E\"\n};\nvar library = {\n  noItems: \"\\u5C1A\\u672A\\u52A0\\u5165\\u4EFB\\u4F55\\u7269\\u4EF6...\",\n  hint_emptyLibrary: \"\\u9078\\u53D6\\u756B\\u5E03\\u4E0A\\u7684\\u7269\\u4EF6\\u4EE5\\u52A0\\u5165\\uFF0C\\u6216\\u5F9E\\u4E0B\\u65B9\\u7684\\u516C\\u958B repository \\u4E2D\\u5B89\\u88DD\\u8CC7\\u6599\\u5EAB\",\n  hint_emptyPrivateLibrary: \"\\u9078\\u64C7\\u756B\\u5E03\\u4E0A\\u7684\\u7269\\u4EF6\\u4EE5\\u5728\\u6B64\\u52A0\\u5165\"\n};\nvar buttons = {\n  clearReset: \"\\u91CD\\u7F6E canvas\",\n  exportJSON: \"\\u532F\\u51FA\\u81F3\\u6A94\\u6848\",\n  exportImage: \"\\u532F\\u51FA\\u5716\\u7247\",\n  export: \"\\u5132\\u5B58\\u81F3...\",\n  copyToClipboard: \"\\u8907\\u88FD\\u81F3\\u526A\\u8CBC\\u7C3F\",\n  save: \"\\u5132\\u5B58\\u76EE\\u524D\\u6A94\\u6848\",\n  saveAs: \"\\u5132\\u5B58\\u70BA\",\n  load: \"\\u958B\\u555F\",\n  getShareableLink: \"\\u53D6\\u5F97\\u5171\\u4EAB\\u9023\\u7D50\",\n  close: \"\\u95DC\\u9589\",\n  selectLanguage: \"\\u9078\\u64C7\\u8A9E\\u8A00\",\n  scrollBackToContent: \"\\u6372\\u52D5\\u56DE\\u5230\\u5167\\u5BB9\",\n  zoomIn: \"\\u653E\\u5927\",\n  zoomOut: \"\\u7E2E\\u5C0F\",\n  resetZoom: \"\\u91CD\\u8A2D\\u7E2E\\u653E\",\n  menu: \"\\u9078\\u55AE\",\n  done: \"\\u5B8C\\u6210\",\n  edit: \"\\u7DE8\\u8F2F\",\n  undo: \"\\u5FA9\\u539F\",\n  redo: \"\\u91CD\\u505A\",\n  resetLibrary: \"\\u91CD\\u8A2D\\u8CC7\\u6599\\u5EAB\",\n  createNewRoom: \"\\u5EFA\\u7ACB\\u65B0\\u5354\\u4F5C\\u6703\\u8B70\\u5BA4\",\n  fullScreen: \"\\u5168\\u87A2\\u5E55\",\n  darkMode: \"\\u6DF1\\u8272\\u6A21\\u5F0F\",\n  lightMode: \"\\u6DFA\\u8272\\u6A21\\u5F0F\",\n  zenMode: \"\\u5C08\\u6CE8\\u6A21\\u5F0F\",\n  objectsSnapMode: \"\\u5438\\u9644\\u81F3\\u7269\\u4EF6\",\n  exitZenMode: \"\\u96E2\\u958B\\u5C08\\u6CE8\\u6A21\\u5F0F\",\n  cancel: \"\\u53D6\\u6D88\",\n  clear: \"\\u6E05\\u9664\",\n  remove: \"\\u522A\\u9664\",\n  embed: \"\\u5207\\u63DB\\u5D4C\\u5165\",\n  publishLibrary: \"\\u767C\\u5E03\",\n  submit: \"\\u9001\\u51FA\",\n  confirm: \"\\u78BA\\u8A8D\",\n  embeddableInteractionButton: \"\\u9EDE\\u64CA\\u4EE5\\u4E92\\u52D5\"\n};\nvar alerts = {\n  clearReset: \"\\u9019\\u5C07\\u6703\\u6E05\\u9664\\u6574\\u500B canvas\\u3002\\u4F60\\u78BA\\u5B9A\\u55CE\\uFF1F\",\n  couldNotCreateShareableLink: \"\\u7121\\u6CD5\\u5EFA\\u7ACB\\u5171\\u4EAB\\u9023\\u7D50\\u3002\",\n  couldNotCreateShareableLinkTooBig: \"\\u7121\\u6CD5\\u5EFA\\u7ACB\\u5171\\u4EAB\\u9023\\u7D50\\uFF1A\\u5834\\u666F\\u592A\\u5927\",\n  couldNotLoadInvalidFile: \"\\u7121\\u6CD5\\u8B80\\u53D6\\u5931\\u6548\\u7684\\u6A94\\u6848\\u3002\",\n  importBackendFailed: \"\\u5F8C\\u7AEF\\u8B80\\u53D6\\u5931\\u6557\\u3002\",\n  cannotExportEmptyCanvas: \"\\u7121\\u6CD5\\u8F38\\u51FA\\u7A7A\\u767D\\u7684 canvas\\u3002\",\n  couldNotCopyToClipboard: \"\\u7121\\u6CD5\\u8907\\u88FD\\u5230\\u526A\\u8CBC\\u7C3F\",\n  decryptFailed: \"\\u7121\\u6CD5\\u89E3\\u5BC6\\u8CC7\\u6599\\u3002\",\n  uploadedSecurly: \"\\u4E0A\\u50B3\\u5DF2\\u901A\\u904E end-to-end \\u52A0\\u5BC6\\uFF0CExcalidraw \\u4F3A\\u670D\\u5668\\u548C\\u7B2C\\u4E09\\u65B9\\u7121\\u6CD5\\u7686\\u8B80\\u53D6\\u5176\\u5167\\u5BB9\\u3002\",\n  loadSceneOverridePrompt: \"\\u8B80\\u53D6\\u5916\\u90E8\\u5716\\u6A23\\u5C07\\u53D6\\u4EE3\\u76EE\\u524D\\u7684\\u5167\\u5BB9\\u3002\\u662F\\u5426\\u8981\\u7E7C\\u7E8C\\uFF1F\",\n  collabStopOverridePrompt: \"\\u505C\\u6B62\\u9023\\u7DDA\\u5C07\\u8986\\u84CB\\u60A8\\u5148\\u524D\\u65BC\\u672C\\u6A5F\\u5132\\u5B58\\u7684\\u7E6A\\u5716\\u9032\\u5EA6\\uFF0C\\u662F\\u5426\\u78BA\\u8A8D\\uFF1F\\n\\n\\uFF08\\u5982\\u8981\\u4FDD\\u7559\\u539F\\u6709\\u7684\\u672C\\u6A5F\\u7E6A\\u5716\\u9032\\u5EA6\\uFF0C\\u76F4\\u63A5\\u95DC\\u9589\\u700F\\u89BD\\u5668\\u5206\\u9801\\u5373\\u53EF\\u3002\\uFF09\",\n  errorAddingToLibrary: \"\\u7121\\u6CD5\\u65BC\\u6B64\\u8CC7\\u6599\\u5EAB\\u52A0\\u5165\\u9805\\u76EE\",\n  errorRemovingFromLibrary: \"\\u7121\\u6CD5\\u7531\\u6B64\\u8CC7\\u6599\\u5EAB\\u79FB\\u9664\\u9805\\u76EE\",\n  confirmAddLibrary: \"\\u9019\\u5C07\\u6703\\u5C07 {{numShapes}} \\u500B\\u5716\\u5F62\\u52A0\\u5165\\u4F60\\u7684\\u8CC7\\u6599\\u5EAB\\uFF0C\\u4F60\\u78BA\\u5B9A\\u55CE\\uFF1F\",\n  imageDoesNotContainScene: \"\\u6B64\\u5716\\u6A94\\u4E2D\\u672A\\u5305\\u542B\\u5834\\u666F\\u8CC7\\u6599\\u3002\\u8F38\\u51FA\\u6A94\\u6848\\u6642\\u662F\\u5426\\u6709\\u5305\\u542B\\u5834\\u666F\\u8CC7\\u6599\\uFF1F\",\n  cannotRestoreFromImage: \"\\u7121\\u6CD5\\u7531\\u6B64\\u6A94\\u6848\\u56DE\\u5FA9\\u5834\\u666F\\u3002\",\n  invalidSceneUrl: \"\\u7121\\u6CD5\\u7531\\u63D0\\u4F9B\\u7684 URL \\u532F\\u5165\\u5834\\u666F\\u3002\\u53EF\\u80FD\\u662F\\u767C\\u751F\\u7570\\u5E38\\uFF0C\\u6216\\u672A\\u5305\\u542B\\u6709\\u6548\\u7684 Excalidraw JSON \\u8CC7\\u6599\\u3002\",\n  resetLibrary: \"\\u9019\\u6703\\u6E05\\u9664\\u60A8\\u7684\\u8CC7\\u6599\\u5EAB\\uFF0C\\u662F\\u5426\\u78BA\\u5B9A\\uFF1F\",\n  removeItemsFromsLibrary: \"\\u5F9E\\u8CC7\\u6599\\u5EAB\\u522A\\u9664 {{count}} \\u9805\\uFF1F\",\n  invalidEncryptionKey: \"\\u52A0\\u5BC6\\u9375\\u5FC5\\u9808\\u70BA22\\u5B57\\u5143\\u3002\\u5373\\u6642\\u5354\\u4F5C\\u5DF2\\u505C\\u7528\\u3002\",\n  collabOfflineWarning: \"\\u6C92\\u6709\\u53EF\\u7528\\u7684\\u7DB2\\u8DEF\\u9023\\u7DDA\\u3002\\n\\u8B8A\\u66F4\\u7121\\u6CD5\\u5132\\u5B58\\uFF01\"\n};\nvar errors = {\n  unsupportedFileType: \"\\u4E0D\\u652F\\u63F4\\u7684\\u6A94\\u6848\\u985E\\u578B\\u3002\",\n  imageInsertError: \"\\u7121\\u6CD5\\u63D2\\u5165\\u5716\\u7247\\u3002\\u8ACB\\u7A0D\\u5F8C\\u518D\\u8A66\\u2026\",\n  fileTooBig: \"\\u6A94\\u6848\\u904E\\u5927\\u3002\\u53EF\\u63A5\\u53D7\\u7684\\u6700\\u5927\\u5C3A\\u5BF8\\u70BA {{maxSize}} \\u3002\",\n  svgImageInsertError: \"\\u7121\\u6CD5\\u63D2\\u5165 SVG \\u5716\\u7247\\u3002\\u6B64 SVG \\u6A94\\u6848\\u6709\\u554F\\u984C\\u3002\",\n  failedToFetchImage: \"\\u7121\\u6CD5\\u7372\\u53D6\\u5716\\u7247\\u3002\",\n  invalidSVGString: \"\\u7121\\u6548\\u7684 SVG\\u3002\",\n  cannotResolveCollabServer: \"\\u7121\\u6CD5\\u9023\\u7D50\\u81F3 collab \\u4F3A\\u670D\\u5668\\u3002\\u8ACB\\u91CD\\u65B0\\u6574\\u7406\\u5F8C\\u518D\\u8A66\\u4E00\\u6B21\\u3002\",\n  importLibraryError: \"\\u7121\\u6CD5\\u8F09\\u5165\\u8CC7\\u6599\\u5EAB\",\n  collabSaveFailed: \"\\u7121\\u6CD5\\u5132\\u5B58\\u81F3\\u5F8C\\u7AEF\\u8CC7\\u6599\\u5EAB\\u3002\\u82E5\\u6B64\\u554F\\u984C\\u6301\\u7E8C\\u767C\\u751F\\uFF0C\\u8ACB\\u5C07\\u6A94\\u6848\\u5132\\u5B58\\u65BC\\u672C\\u6A5F\\u4EE5\\u78BA\\u4FDD\\u8CC7\\u6599\\u4E0D\\u6703\\u907A\\u5931\\u3002\",\n  collabSaveFailed_sizeExceeded: \"\\u7121\\u6CD5\\u5132\\u5B58\\u81F3\\u5F8C\\u7AEF\\u8CC7\\u6599\\u5EAB\\uFF0C\\u53EF\\u80FD\\u7684\\u539F\\u56E0\\u70BA\\u756B\\u5E03\\u5C3A\\u5BF8\\u904E\\u5927\\u3002\\u8ACB\\u5C07\\u6A94\\u6848\\u5132\\u5B58\\u65BC\\u672C\\u6A5F\\u4EE5\\u78BA\\u4FDD\\u8CC7\\u6599\\u4E0D\\u6703\\u907A\\u5931\\u3002\",\n  imageToolNotSupported: \"\\u5716\\u7247\\u5DF2\\u505C\\u7528\",\n  brave_measure_text_error: {\n    line1: \"\\u770B\\u8D77\\u4F86\\u60A8\\u958B\\u555F\\u4E86 Brave \\u700F\\u89BD\\u5668\\u7684 <bold>Aggressively Block Fingerprinting</bold> \\u8A2D\\u5B9A\\u3002\",\n    line2: \"\\u9019\\u53EF\\u80FD\\u9020\\u6210\\u60A8\\u756B\\u5E03\\u4E2D <bold>\\u6587\\u5B57\\u5143\\u7D20</bold> \\u7684\\u7570\\u5E38\\u3002\",\n    line3: \"\\u6211\\u5011\\u5F37\\u70C8\\u5EFA\\u8B70\\u60A8\\u95DC\\u9589\\u6B64\\u8A2D\\u5B9A\\u3002\\u60A8\\u53EF\\u4EE5\\u4F9D\\u7167 <link>\\u9019\\u4E9B\\u6B65\\u9A5F</link> \\u4F86\\u9032\\u884C\\u3002\",\n    line4: \"\\u82E5\\u95DC\\u9589\\u6B64\\u8A2D\\u5B9A\\u4E26\\u672A\\u4FEE\\u5FA9\\u6587\\u5B57\\u5143\\u7D20\\u7684\\u986F\\u793A\\u554F\\u984C\\uFF0C\\u8ACB\\u56DE\\u5831\\u65BC\\u6211\\u5011 GitHub \\u4E0A\\u7684 <issueLink>issue</issueLink>\\uFF0C\\u6216\\u5728 <discordLink>Discord</discordLink> \\u4E0A\\u544A\\u8A34\\u6211\\u5011\\u3002\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\\u53EF\\u5D4C\\u5165\\u5143\\u7D20\\u7121\\u6CD5\\u52A0\\u5165\\u8CC7\\u6599\\u5EAB\",\n    iframe: \"IFrame \\u5143\\u7D20\\u7121\\u6CD5\\u52A0\\u5165\\u8CC7\\u6599\\u5EAB\",\n    image: \"\\u5373\\u5C07\\u652F\\u63F4\\u52A0\\u5165\\u5716\\u7247\\u81F3\\u8CC7\\u6599\\u5EAB\\uFF01\"\n  },\n  asyncPasteFailedOnRead: \"\\u7121\\u6CD5\\u8CBC\\u4E0A\\uFF08\\u7121\\u6CD5\\u7531\\u7CFB\\u7D71\\u526A\\u8CBC\\u7C3F\\u8B80\\u5165\\uFF09\",\n  asyncPasteFailedOnParse: \"\\u7121\\u6CD5\\u8CBC\\u4E0A\",\n  copyToSystemClipboardFailed: \"\\u7121\\u6CD5\\u8907\\u88FD\\u81F3\\u526A\\u8CBC\\u7C3F\"\n};\nvar toolBar = {\n  selection: \"\\u9078\\u53D6\",\n  image: \"\\u63D2\\u5165\\u5716\\u7247\",\n  rectangle: \"\\u9577\\u65B9\\u5F62\",\n  diamond: \"\\u83F1\\u5F62\",\n  ellipse: \"\\u6A62\\u5713\",\n  arrow: \"\\u7BAD\\u982D\",\n  line: \"\\u7DDA\\u689D\",\n  freedraw: \"\\u7E6A\\u5716\",\n  text: \"\\u6587\\u5B57\",\n  library: \"\\u8CC7\\u6599\\u5EAB\",\n  lock: \"\\u53EF\\u9023\\u7E8C\\u4F7F\\u7528\\u9078\\u53D6\\u7684\\u5DE5\\u5177\",\n  penMode: \"\\u7B46\\u6A21\\u5F0F - \\u907F\\u514D\\u89F8\\u6478\",\n  link: \"\\u70BA\\u6240\\u9078\\u7684\\u5F62\\u72C0\\u589E\\u52A0\\b/\\u66F4\\u65B0\\u9023\\u7D50\",\n  eraser: \"\\u6A61\\u76AE\\u64E6\",\n  frame: \"\\u6846\\u67B6\\u5DE5\\u5177\",\n  magicframe: \"\\u7DDA\\u6846\\u7A3F\\u8F49\\u70BA\\u7A0B\\u5F0F\\u78BC\",\n  embeddable: \"\\u5D4C\\u5165\\u7DB2\\u7AD9\",\n  laser: \"\\u96F7\\u5C04\\u7B46\",\n  hand: \"\\u624B\\u5F62\\uFF08\\u5E73\\u79FB\\u5DE5\\u5177\\uFF09\",\n  extraTools: \"\\u66F4\\u591A\\u5DE5\\u5177\",\n  mermaidToExcalidraw: \"Mermaid \\u81F3 Excalidraw\",\n  magicSettings: \"AI \\u8A2D\\u5B9A\"\n};\nvar headings = {\n  canvasActions: \"canvas \\u52D5\\u4F5C\",\n  selectedShapeActions: \"\\u9078\\u53D6\\u5716\\u5F62\\u52D5\\u4F5C\",\n  shapes: \"\\u5F62\\u72C0\"\n};\nvar hints = {\n  canvasPanning: \"\\u82E5\\u8981\\u79FB\\u52D5\\u756B\\u5E03\\uFF0C\\u8ACB\\u5728\\u62D6\\u66F3\\u6642\\u6309\\u4F4F\\u6ED1\\u9F20\\u6EFE\\u8F2A\\u6216\\u7A7A\\u767D\\u9375\\uFF0C\\u6216\\u4F7F\\u7528\\u624B\\u5F62\\u5DE5\\u5177\",\n  linearElement: \"\\u9EDE\\u64CA\\u4EE5\\u7E6A\\u88FD\\u591A\\u9EDE\\u66F2\\u7DDA\\uFF1B\\u6216\\u62D6\\u66F3\\u4EE5\\u7E6A\\u88FD\\u76F4\\u7DDA\",\n  freeDraw: \"\\u9EDE\\u64CA\\u4E26\\u62D6\\u66F3\\u4F86\\u7E6A\\u5716\\uFF0C\\u653E\\u958B\\u5373\\u7D50\\u675F\",\n  text: \"\\u63D0\\u793A\\uFF1A\\u4EA6\\u53EF\\u4F7F\\u7528\\u9078\\u53D6\\u5DE5\\u5177\\u5728\\u4EFB\\u4F55\\u5730\\u65B9\\u96D9\\u64CA\\u4F86\\u52A0\\u5165\\u6587\\u5B57\",\n  embeddable: \"\\u9EDE\\u64CA\\u4E26\\u62D6\\u79FB\\u4EE5\\u5EFA\\u7ACB\\u5D4C\\u5165\\u7DB2\\u7AD9\",\n  text_selected: \"\\u96D9\\u64CA\\u6ED1\\u9F20\\u6216\\u6309 Enter \\u4EE5\\u7DE8\\u8F2F\\u6587\\u5B57\",\n  text_editing: \"\\u6309\\u8DF3\\u812B\\u9375\\u6216 Ctrl \\u6216 Cmd + Enter \\u4EE5\\u7D50\\u675F\\u7DE8\\u8F2F\",\n  linearElementMulti: \"\\u6309\\u4E0B Escape \\u6216 Enter \\u4EE5\\u7D50\\u675F\\u7E6A\\u88FD\",\n  lockAngle: \"\\u6309\\u4F4F SHIFT \\u53EF\\u9650\\u5236\\u65CB\\u8F49\\u89D2\\u5EA6\",\n  resize: \"\\u7E2E\\u653E\\u6642\\u6309\\u4F4F Shift \\u53EF\\u4FDD\\u6301\\u539F\\u6BD4\\u4F8B\\u7E2E\\u653E\\uFF1B\\\\n\\u6309\\u4F4F Alt \\u53EF\\u7531\\u4E2D\\u5FC3\\u9EDE\\u9032\\u884C\\u7E2E\\u653E\",\n  resizeImage: \"\\u6309\\u4F4F SHIFT \\u53EF\\u4EFB\\u610F\\u7E2E\\u653E\\uFF0C\\u6309\\u4F4F ALT \\u53EF\\u7531\\u4E2D\\u592E\\u7E2E\\u653E\\u3002\",\n  rotate: \"\\u65CB\\u8F49\\u6642\\u6309\\u4F4F Shift \\u53EF\\u9650\\u5236\\u65CB\\u8F49\\u89D2\\u5EA6\",\n  lineEditor_info: \"\\u6309\\u4F4F Ctrl \\u6216 Cmd \\u4E26\\u96D9\\u64CA\\u6216\\u6309\\u4F4F Ctrl \\u6216 Cmd + Enter \\u4F86\\u7DE8\\u8F2F\\u63A7\\u5236\\u9EDE\",\n  lineEditor_pointSelected: \"\\u6309\\u4E0B Delete \\u53EF\\u79FB\\u9664\\u9328\\u9EDE\\uFF1BCtrl \\u6216 Cmd + D \\u53EF\\u8907\\u88FD\\uFF1B\\u6216\\u53EF\\u62D6\\u66F3\\u4F86\\u79FB\\u52D5\",\n  lineEditor_nothingSelected: \"\\u9078\\u64C7\\u8981\\u7DE8\\u8F2F\\u7684\\u9328\\u9EDE\\uFF08\\u6309\\u4F4F SHIFT \\u53EF\\u591A\\u9078\\uFF09\\uFF0C\\n\\u6216\\u6309\\u4F4F Alt \\u4E26\\u9EDE\\u64CA\\u4EE5\\u589E\\u52A0\\u65B0\\u9328\\u9EDE\\u3002\",\n  placeImage: \"\\u9EDE\\u64CA\\u4EE5\\u653E\\u7F6E\\u5716\\u7247\\uFF0C\\u6216\\u9EDE\\u64CA\\u4E26\\u62D6\\u66F3\\u4EE5\\u624B\\u52D5\\u8ABF\\u6574\\u5176\\u5C3A\\u5BF8\\u3002\",\n  publishLibrary: \"\\u767C\\u5E03\\u500B\\u4EBA\\u8CC7\\u6599\\u5EAB\",\n  bindTextToElement: \"\\u6309\\u4E0B Enter \\u4EE5\\u52A0\\u5165\\u6587\\u5B57\\u3002\",\n  deepBoxSelect: \"\\u6309\\u4F4F Ctrl \\u6216 Cmd \\u4EE5\\u6DF1\\u5EA6\\u9078\\u53D6\\u4E26\\u907F\\u514D\\u62D6\\u66F3\",\n  eraserRevert: \"\\u6309\\u4F4F Alt \\u4EE5\\u53CD\\u9078\\u53D6\\u5DF2\\u6A19\\u8A18\\u5F85\\u522A\\u9664\\u7684\\u5143\\u7D20\",\n  firefox_clipboard_write: '\\u6B64\\u529F\\u80FD\\u6709\\u6A5F\\u6703\\u900F\\u904E\\u5C07 \"dom.events.asyncClipboard.clipboardItem\" \\u8A2D\\u5B9A\\u70BA \"true\" \\u4F86\\u958B\\u555F\\u3002\\n\\u82E5\\u8981\\u8B8A\\u66F4 Firefox \\u700F\\u89BD\\u5668\\u7684\\u6B64\\u8A2D\\u5B9A\\u503C\\uFF0C\\u8ACB\\u81F3 \"about:config\" \\u9801\\u9762\\u3002',\n  disableSnapping: \"\\u6309\\u4F4F Ctrl \\u6216 Cmd \\u4EE5\\u7981\\u7528\\u5438\\u9644\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\\u7121\\u6CD5\\u986F\\u793A\\u9810\\u89BD\",\n  canvasTooBig: \"\\u756B\\u5E03\\u53EF\\u80FD\\u904E\\u5927\",\n  canvasTooBigTip: \"\\u63D0\\u793A\\uFF1A\\u53EF\\u5617\\u8A66\\u5C07\\u6700\\u9060\\u7684\\u5143\\u7D20\\u79FB\\u52D5\\u81F3\\u8F03\\u96C6\\u4E2D\\u7684\\u4F4D\\u7F6E\"\n};\nvar errorSplash = {\n  headingMain: \"\\u767C\\u751F\\u932F\\u8AA4\\uFF0C\\u5617\\u8A66<button>\\u91CD\\u65B0\\u8F09\\u5165\\u9801\\u9762\\u3002</button>\",\n  clearCanvasMessage: \"\\u82E5\\u91CD\\u65B0\\u8F09\\u5165\\u4ECD\\u7121\\u6CD5\\u89E3\\u6C7A\\u554F\\u984C\\uFF0C\\u5617\\u8A66<button>\\u6E05\\u9664 canvas\\u3002</button>\",\n  clearCanvasCaveat: \"\\u6B64\\u52D5\\u4F5C\\u5C07\\u9020\\u6210\\u76EE\\u524D\\u7684\\u4F5C\\u54C1\\u88AB\\u79FB\\u9664\\u3002\",\n  trackedToSentry: \"\\u6B64\\u932F\\u8AA4\\u8207\\u5176\\u8B58\\u5225\\u78BC{{eventId}}\\u5C07\\u7531\\u7CFB\\u7D71\\u8A18\\u9304\\u3002\",\n  openIssueMessage: \"\\u6211\\u5011\\u5C07\\u8B39\\u614E\\u8655\\u7406\\uFF0C\\u4F60\\u7684\\u4F5C\\u54C1\\u5167\\u5BB9\\u4E0D\\u6703\\u88AB\\u5305\\u542B\\u5728\\u932F\\u8AA4\\u5831\\u544A\\u4E2D\\u3002\\u82E5\\u4F60\\u7684\\u4F5C\\u54C1\\u4E0D\\u9700\\u4FDD\\u6301\\u79C1\\u5BC6\\uFF0C\\u8ACB\\u8003\\u616E\\u4F7F\\u7528\\u6211\\u5011\\u7684<button>bug tracker\\u3002</button>\\u8ACB\\u5C07\\u4E0B\\u5217\\u8CC7\\u8A0A\\u8907\\u88FD\\u8CBC\\u4E0A\\u81F3 GitHub issue \\u4E2D\\u3002\",\n  sceneContent: \"\\u4F5C\\u54C1\\u5167\\u5BB9\\uFF1A\"\n};\nvar roomDialog = {\n  desc_intro: \"\\u4F60\\u53EF\\u4EE5\\u9080\\u8ACB\\u5176\\u4ED6\\u4EBA\\u4E00\\u8D77\\u5354\\u4F5C\\u76EE\\u524D\\u7684\\u4F5C\\u54C1\\u3002\",\n  desc_privacy: \"\\u9023\\u7DDA\\u4F7F\\u7528 end-to-end \\u52A0\\u5BC6\\u6545\\u7121\\u9808\\u64D4\\u5FC3\\u4F5C\\u54C1\\u7684\\u5B89\\u5168\\u6027\\u3002\\u5373\\u4F7F\\u662F\\u6211\\u5011\\u7684\\u4F3A\\u670D\\u5668\\u4E5F\\u7121\\u6CD5\\u53D6\\u5F97\\u5176\\u5167\\u5BB9\\u3002\",\n  button_startSession: \"\\u958B\\u59CB\\u9023\\u7DDA\",\n  button_stopSession: \"\\u505C\\u6B62\\u9023\\u7DDA\",\n  desc_inProgressIntro: \"\\u5373\\u6642\\u5354\\u4F5C\\u9023\\u7DDA\\u6B63\\u5728\\u9032\\u884C\\u4E2D\\u3002\",\n  desc_shareLink: \"\\u5C07\\u6B64\\u9023\\u7D50\\u5206\\u4EAB\\u7D66\\u6B32\\u5354\\u4F5C\\u7684\\u5C0D\\u8C61\\uFF1A\",\n  desc_exitSession: \"\\u505C\\u6B62\\u9023\\u7DDA\\u5C07\\u4E2D\\u65B7\\u4F60\\u8207\\u5354\\u4F5C\\u6703\\u8B70\\u5BA4\\u7684\\u9023\\u7D50\\uFF0C\\u4F46\\u4F60\\u4ECD\\u53EF\\u65BC\\u672C\\u6A5F\\u7DE8\\u8F2F\\u6B64\\u4F5C\\u54C1\\u3002\\u610F\\u6307\\u505C\\u6B62\\u9023\\u7DDA\\u5F8C\\u4F60\\u7684\\u7DE8\\u8F2F\\u4E0D\\u6703\\u88AB\\u5148\\u524D\\u5171\\u540C\\u5354\\u4F5C\\u7684\\u4EBA\\u770B\\u898B\\uFF0C\\u4E14\\u4ED6\\u5011\\u53EF\\u7E7C\\u7E8C\\u5171\\u540C\\u5354\\u4F5C\\u53E6\\u4E00\\u500B\\u7248\\u672C\\u3002\",\n  shareTitle: \"\\u52A0\\u5165 Excalidraw \\u4E0A\\u7684\\u5373\\u6642\\u5354\\u4F5C\\u6703\\u8B70\\u5BA4\"\n};\nvar errorDialog = {\n  title: \"\\u932F\\u8AA4\"\n};\nvar exportDialog = {\n  disk_title: \"\\u5132\\u5B58\\u81F3\\u786C\\u789F\",\n  disk_details: \"\\u5C07\\u5834\\u666F\\u532F\\u51FA\\u70BA\\u53EF\\u4F9B\\u532F\\u5165\\u4E4B\\u6A94\\u6848\",\n  disk_button: \"\\u5132\\u5B58\\u81F3\\u6A94\\u6848\",\n  link_title: \"\\u53EF\\u5171\\u4EAB\\u9023\\u7D50\",\n  link_details: \"\\u532F\\u51FA\\u70BA\\u552F\\u8B80\\u9023\\u7D50\",\n  link_button: \"\\u532F\\u51FA\\u70BA\\u9023\\u7D50\",\n  excalidrawplus_description: \"\\u5C07\\u6B64\\u5834\\u666F\\u5132\\u5B58\\u81F3\\u4F60\\u7684 Excalidraw+ \\u5DE5\\u4F5C\\u5340\",\n  excalidrawplus_button: \"\\u8F38\\u51FA\",\n  excalidrawplus_exportError: \"\\u76EE\\u524D\\u7121\\u6CD5\\u8F38\\u51FA\\u81F3 Excalidraw+\"\n};\nvar helpDialog = {\n  blog: \"\\u95B1\\u8B80\\u90E8\\u843D\\u683C\",\n  click: \"\\u9EDE\\u64CA\",\n  deepSelect: \"\\u6DF1\\u5EA6\\u9078\\u53D6\",\n  deepBoxSelect: \"\\u5728\\u5BB9\\u5668\\u5167\\u6DF1\\u5EA6\\u9078\\u53D6\\u4E26\\u907F\\u514D\\u62D6\\u66F3\",\n  curvedArrow: \"\\u66F2\\u7BAD\\u982D\",\n  curvedLine: \"\\u66F2\\u7DDA\",\n  documentation: \"\\u6587\\u4EF6\",\n  doubleClick: \"\\u96D9\\u64CA\",\n  drag: \"\\u62D6\\u66F3\",\n  editor: \"\\u7DE8\\u8F2F\\u5668\",\n  editLineArrowPoints: \"\\u7DE8\\u8F2F\\u7DDA/\\u7BAD\\u982D\\u63A7\\u5236\\u9EDE\",\n  editText: \"\\u7DE8\\u8F2F\\u6587\\u5B57/\\u589E\\u52A0\\u6A19\\u7C64\",\n  github: \"\\u767C\\u73FE\\u7570\\u5E38\\uFF1F\\u56DE\\u5831\\u554F\\u984C\",\n  howto: \"\\u53C3\\u7167\\u6211\\u5011\\u7684\\u8AAA\\u660E\",\n  or: \"\\u6216\",\n  preventBinding: \"\\u907F\\u514D\\u7BAD\\u865F\\u9023\\u7D50\",\n  tools: \"\\u5DE5\\u5177\",\n  shortcuts: \"\\u9375\\u76E4\\u5FEB\\u901F\\u9375\",\n  textFinish: \"\\u5B8C\\u6210\\u7DE8\\u8F2F\\uFF08\\u6587\\u5B57\\u7DE8\\u8F2F\\u5668\\uFF09\",\n  textNewLine: \"\\u63DB\\u884C\\uFF08\\u6587\\u5B57\\u7DE8\\u8F2F\\u5668\\uFF09\",\n  title: \"\\u8AAA\\u660E\",\n  view: \"\\u6AA2\\u8996\",\n  zoomToFit: \"\\u653E\\u5927\\u81F3\\u586B\\u6EFF\\u756B\\u9762\",\n  zoomToSelection: \"\\u7E2E\\u653E\\u81F3\\u9078\\u53D6\\u5340\",\n  toggleElementLock: \"\\u9396\\u5B9A/\\u89E3\\u9396\\u5DF2\\u9078\\u7684\\u9805\\u76EE\",\n  movePageUpDown: \"\\u5411\\u4E0A/\\u4E0B\\u79FB\\u52D5\\u9801\\u9762\",\n  movePageLeftRight: \"\\u5411\\u5DE6/\\u53F3\\u79FB\\u52D5\\u9801\\u9762\"\n};\nvar clearCanvasDialog = {\n  title: \"\\u6E05\\u9664\\u756B\\u5E03\"\n};\nvar publishDialog = {\n  title: \"\\u767C\\u5E03\\u8CC7\\u6599\\u5EAB\",\n  itemName: \"\\u9805\\u76EE\\u540D\\u7A31\",\n  authorName: \"\\u4F5C\\u8005\\u540D\\u7A31\",\n  githubUsername: \"GitHub \\u5E33\\u865F\",\n  twitterUsername: \"Twitter \\u5E33\\u865F\",\n  libraryName: \"\\u8CC7\\u6599\\u5EAB\\u540D\\u7A31\",\n  libraryDesc: \"\\u8CC7\\u6599\\u5EAB\\u8AAA\\u660E\",\n  website: \"\\u7DB2\\u7AD9\",\n  placeholder: {\n    authorName: \"\\u60A8\\u7684\\u540D\\u7A31\\u6216\\u5E33\\u865F\",\n    libraryName: \"\\u60A8\\u7684\\u8CC7\\u6599\\u5EAB\\u540D\\u7A31\",\n    libraryDesc: \"\\u63D0\\u4F9B\\u60A8\\u7684\\u8CC7\\u6599\\u5EAB\\u8AAA\\u660E\\u4EE5\\u5229\\u4ED6\\u4EBA\\u7406\\u89E3\\u5176\\u7528\\u9014\",\n    githubHandle: \"Github handle\\uFF08\\u9078\\u586B\\uFF09\\uFF0C\\u586B\\u5BEB\\u5F8C\\u60A8\\u53EF\\u7DE8\\u8F2F\\u5DF2\\u9001\\u51FA\\u5F85\\u5BE9\\u67E5\\u7684\\u8CC7\\u6599\\u5EAB\",\n    twitterHandle: \"Twitter \\u5E33\\u865F\\uFF08\\u9078\\u586B\\uFF09\\uFF0C\\u586B\\u5BEB\\u5F8C\\u82E5\\u6211\\u5011\\u5728 Twitter \\u63A8\\u5EE3\\u6642\\u53EF\\u63D0\\u53CA\\u60A8\",\n    website: \"\\u60A8\\u500B\\u4EBA\\u7DB2\\u7AD9\\u6216\\u5176\\u4ED6\\u7DB2\\u7AD9\\u7684\\u9023\\u7D50\\uFF08\\u9078\\u586B\\uFF09\"\n  },\n  errors: {\n    required: \"\\u5FC5\\u586B\",\n    website: \"\\u8ACB\\u8F38\\u5165\\u6709\\u6548\\u7684 URL\"\n  },\n  noteDescription: \"\\u9001\\u51FA\\u60A8\\u7684\\u8CC7\\u6599\\u5EAB\\u5F8C\\u5C07\\u88AB\\u5305\\u542B\\u65BC<link>\\u516C\\u958B\\u8CC7\\u6599\\u5EAB repository</link>\\u4EE5\\u5229\\u4ED6\\u4EBA\\u5728\\u5176\\u7E6A\\u5716\\u4E2D\\u4F7F\\u7528\\u3002\",\n  noteGuidelines: \"\\u8CC7\\u6599\\u5EAB\\u9700\\u5148\\u7D93\\u4EBA\\u5DE5\\u5BE9\\u67E5\\u3002\\u8ACB\\u95B1\\u8B80<link>\\u8AAA\\u660E\\u6587\\u4EF6</link>\\u518D\\u9001\\u51FA\\u3002\\u82E5\\u9700\\u6E9D\\u901A\\u8207\\u4FEE\\u6539\\u6642\\u8981\\u900F\\u904E GitHub \\u5E33\\u865F\\u4F86\\u9032\\u884C\\uFF0C\\u4F46\\u4E26\\u975E\\u5F37\\u5236\\u9700\\u6C42\\u3002\",\n  noteLicense: \"\\u9001\\u51FA\\u5373\\u4EE3\\u8868\\u60A8\\u540C\\u610F\\u6B64\\u8CC7\\u6599\\u5EAB\\u5C07\\u767C\\u5E03\\u6642\\u4F7F\\u7528 <link>MIT \\u6388\\u6B0A\\uFF0C</link>\\u7C21\\u55AE\\u4F86\\u8AAA\\u662F\\u6307\\u4EFB\\u4F55\\u4EBA\\u90FD\\u80FD\\u4E0D\\u53D7\\u9650\\u5236\\u7684\\u4F7F\\u7528\\u3002\",\n  noteItems: \"\\u6BCF\\u500B\\u8CC7\\u6599\\u5EAB\\u9805\\u76EE\\u90FD\\u6709\\u7368\\u7ACB\\u7684\\u540D\\u7A31\\u6545\\u53EF\\u7BE9\\u9078\\u3002\\u6703\\u5305\\u542B\\u4E0B\\u5217\\u8CC7\\u6599\\u5EAB\\u9805\\u76EE\\uFF1A\",\n  atleastOneLibItem: \"\\u8ACB\\u9078\\u64C7\\u81F3\\u5C11\\u4E00\\u9805\\u8CC7\\u6599\\u5EAB\\u9805\\u76EE\",\n  republishWarning: \"\\u6CE8\\u610F\\uFF1A\\u90E8\\u5206\\u9078\\u53D6\\u4E2D\\u7684\\u7269\\u4EF6\\u5148\\u524D\\u5DF2\\u767C\\u5E03/\\u9001\\u51FA\\u904E\\u3002\\u5EFA\\u8B70\\u50C5\\u5728\\u8981\\u66F4\\u65B0\\u73FE\\u5B58\\u8CC7\\u6599\\u5EAB\\u6216\\u5DF2\\u9001\\u51FA\\u7684\\u7269\\u4EF6\\u6642\\u624D\\u91CD\\u65B0\\u9001\\u51FA\\u9019\\u4E9B\\u7269\\u4EF6\\u3002\"\n};\nvar publishSuccessDialog = {\n  title: \"\\u8CC7\\u6599\\u5EAB\\u5DF2\\u9001\\u51FA\",\n  content: \"\\u611F\\u8B1D {{authorName}} \\u3002\\u60A8\\u7684\\u8CC7\\u6599\\u5EAB\\u5DF2\\u9001\\u51FA\\u5F85\\u5BE9\\u67E5\\u3002\\u60A8\\u53EF\\u67E5\\u770B\\u76EE\\u524D\\u72C0\\u614B<link>\\u5728\\u6B64</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\\u91CD\\u8A2D\\u8CC7\\u6599\\u5EAB\",\n  removeItemsFromLib: \"\\u5F9E\\u8CC7\\u6599\\u5EAB\\u79FB\\u9664\\u6240\\u9078\\u7684\\u9805\\u76EE\"\n};\nvar imageExportDialog = {\n  header: \"\\u532F\\u51FA\\u5716\\u7247\",\n  label: {\n    withBackground: \"\\u80CC\\u666F\",\n    onlySelected: \"\\u50C5\\u9078\\u53D6\\u7269\\u4EF6\",\n    darkMode: \"\\u6DF1\\u8272\\u6A21\\u5F0F\",\n    embedScene: \"\\u5D4C\\u5165\\u5834\\u666F\",\n    scale: \"\\u7E2E\\u653E\\u6BD4\\u4F8B\",\n    padding: \"\\u5167\\u9593\\u8DDD\"\n  },\n  tooltip: {\n    embedScene: \"\\u7528\\u65BC\\u56DE\\u5FA9\\u5834\\u666F\\u7684\\u5834\\u666F\\u8CC7\\u6599\\u6703\\u88AB\\u5305\\u542B\\u5728\\u8F38\\u51FA\\u7684 PNG/SVG \\u6A94\\u6848\\u4E2D\\u3002\\n\\u6703\\u589E\\u52A0\\u8F38\\u51FA\\u7684\\u6A94\\u6848\\u5927\\u5C0F\\u3002\"\n  },\n  title: {\n    exportToPng: \"\\u8F38\\u51FA\\u6210 PNG\",\n    exportToSvg: \"\\u8F38\\u51FA\\u6210 SVG\",\n    copyPngToClipboard: \"\\u8907\\u88FD PNG \\u81F3\\u526A\\u8CBC\\u7C3F\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"\\u8907\\u88FD\\u81F3\\u526A\\u8CBC\\u7C3F\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\\u4F60\\u7684\\u4F5C\\u54C1\\u5DF2\\u4F7F\\u7528 end-to-end \\u65B9\\u5F0F\\u52A0\\u5BC6\\uFF0CExcalidraw \\u7684\\u4F3A\\u670D\\u5668\\u4E5F\\u7121\\u6CD5\\u53D6\\u5F97\\u5176\\u5167\\u5BB9\\u3002\",\n  link: \"Excalidraw \\u7AEF\\u5230\\u7AEF\\u52A0\\u5BC6\\u7684\\u76F8\\u95DC\\u90E8\\u843D\\u683C\\u6587\\u7AE0\"\n};\nvar stats = {\n  angle: \"\\u89D2\\u5EA6\",\n  element: \"\\u5143\\u7D20\",\n  elements: \"\\u5143\\u7D20\",\n  height: \"\\u9AD8\\u5EA6\",\n  scene: \"\\u5834\\u666F\",\n  selected: \"\\u5DF2\\u9078\",\n  storage: \"\\u5132\\u5B58\",\n  title: \"\\u8A73\\u7D30\\u7D71\\u8A08\",\n  total: \"\\u5408\\u8A08\",\n  version: \"\\u7248\\u672C\",\n  versionCopy: \"\\u9EDE\\u64CA\\u8907\\u88FD\",\n  versionNotAvailable: \"\\u7121\\u6CD5\\u53D6\\u5F97\\u7248\\u672C\",\n  width: \"\\u5BEC\\u5EA6\"\n};\nvar toast = {\n  addedToLibrary: \"\\u52A0\\u5165\\u8CC7\\u6599\\u5EAB\",\n  copyStyles: \"\\u5DF2\\u8907\\u88FD\\u6A23\\u5F0F\",\n  copyToClipboard: \"\\u8907\\u88FD\\u81F3\\u526A\\u8CBC\\u7C3F\\u3002\",\n  copyToClipboardAsPng: \"\\u4EE5 PNG \\u683C\\u5F0F\\u5C07 {{exportSelection}} \\u8907\\u88FD\\u81F3\\u526A\\u8CBC\\u7C3F\\n({{exportColorScheme}})\",\n  fileSaved: \"\\u5DF2\\u5132\\u5B58\\u6A94\\u6848\\u3002\",\n  fileSavedToFilename: \"\\u5132\\u5B58\\u70BA {filename}\",\n  canvas: \"\\u756B\\u5E03\",\n  selection: \"\\u5DF2\\u9078\\u9805\\u76EE\",\n  pasteAsSingleElement: \"\\u4F7F\\u7528 {{shortcut}} \\u4EE5\\u505A\\u70BA\\u55AE\\u4E00\\u7269\\u4EF6\\u8CBC\\u4E0A\\uFF0C\\n\\u6216\\u8CBC\\u4E0A\\u81F3\\u73FE\\u6709\\u7684\\u6587\\u5B57\\u7DE8\\u8F2F\\u5668\",\n  unableToEmbed: \"\\u76EE\\u524D\\u4E0D\\u5141\\u8A31\\u5D4C\\u5165\\u6B64\\u7DB2\\u5740\\u3002\\u60A8\\u53EF\\u81F3 GitHub \\u63D0\\u51FA issue \\u4EE5\\u8981\\u6C42\\u5C07\\u6B64\\u7DB2\\u5740\\u52A0\\u5165\\u5408\\u683C\\u540D\\u55AE\\u3002\",\n  unrecognizedLinkFormat: \"\\u60A8\\u5D4C\\u5165\\u7684\\u9023\\u7D50\\u683C\\u5F0F\\u4E0D\\u7B26\\u3002\\u8ACB\\u5617\\u8A66\\u8CBC\\u5165\\u539F\\u7DB2\\u7AD9\\u6240\\u63D0\\u4F9B\\u7684\\u300C\\u5D4C\\u5165\\u300D\\u5B57\\u4E32\\u3002\"\n};\nvar colors = {\n  transparent: \"\\u900F\\u660E\",\n  black: \"\\u9ED1\",\n  white: \"\\u767D\",\n  red: \"\\u7D05\",\n  pink: \"\\u7C89\\u7D05\",\n  grape: \"\\u6DF1\\u7D2B\",\n  violet: \"\\u85CD\\u7D2B\",\n  gray: \"\\u7070\",\n  blue: \"\\u85CD\",\n  cyan: \"\\u9752\",\n  teal: \"\\u85CD\\u7DA0\",\n  green: \"\\u7DA0\",\n  yellow: \"\\u9EC3\",\n  orange: \"\\u6A58\",\n  bronze: \"\\u9285\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\\u6240\\u6709\\u8CC7\\u6599\\u7686\\u5DF2\\u5728\\u700F\\u89BD\\u5668\\u4E2D\\u5132\\u5B58\\u65BC\\u672C\\u6A5F\",\n    center_heading_plus: \"\\u60A8\\u662F\\u5426\\u662F\\u8981\\u524D\\u5F80 Excalidraw+ \\uFF1F\",\n    menuHint: \"\\u8F38\\u51FA\\u3001\\u504F\\u597D\\u8A2D\\u5B9A\\u3001\\u8A9E\\u8A00...\"\n  },\n  defaults: {\n    menuHint: \"\\u8F38\\u51FA\\u3001\\u504F\\u597D\\u8A2D\\u5B9A\\u53CA\\u5176\\u4ED6...\",\n    center_heading: \"\\u5716\\u8868\\u3002\\u88FD\\u4F5C\\u3002\\u8D85\\u7C21\\u55AE\\u3002\",\n    toolbarHint: \"\\u9078\\u500B\\u5DE5\\u5177\\u958B\\u59CB\\u756B\\u5716\\u5427\\uFF01\",\n    helpHint: \"\\u5FEB\\u901F\\u9375\\u8207\\u8AAA\\u660E\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\\u6700\\u5E38\\u4F7F\\u7528\\u7684\\u81EA\\u8A02\\u984F\\u8272\",\n  colors: \"\\u984F\\u8272\",\n  shades: \"\\u6F38\\u8B8A\\u8272\",\n  hexCode: \"Hex \\u78BC\",\n  noShades: \"\\u6C92\\u6709\\u6B64\\u984F\\u8272\\u7684\\u6F38\\u8B8A\\u8272\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\\u532F\\u51FA\\u70BA\\u5716\\u7247\",\n      button: \"\\u532F\\u51FA\\u70BA\\u5716\\u7247\",\n      description: \"\\u5C07\\u5834\\u666F\\u532F\\u51FA\\u70BA\\u53EF\\u4F9B\\u532F\\u5165\\u7684\\u5716\\u7247\\u6A94\\u6848\"\n    },\n    saveToDisk: {\n      title: \"\\u5132\\u5B58\\u81F3\\u786C\\u789F\",\n      button: \"\\u5132\\u5B58\\u81F3\\u786C\\u789F\",\n      description: \"\\u5C07\\u5834\\u666F\\u532F\\u51FA\\u70BA\\u53EF\\u4F9B\\u532F\\u5165\\u7684\\u6A94\\u6848\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"\\u532F\\u51FA\\u81F3 Excalidraw+\",\n      description: \"\\u5C07\\u6B64\\u5834\\u666F\\u5132\\u5B58\\u81F3\\u60A8\\u7684 Excalidraw+ \\u5DE5\\u4F5C\\u5340\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\\u5F9E\\u6A94\\u6848\\u8F09\\u5165\",\n      button: \"\\u5F9E\\u6A94\\u6848\\u8F09\\u5165\",\n      description: \"\\u5F9E\\u6A94\\u6848\\u8F09\\u5165\\u5C07<bold>\\u53D6\\u4EE3\\u60A8\\u76EE\\u524D\\u7684\\u5167\\u5BB9</bold>\\u3002<br></br>\\u53EF\\u5148\\u4F7F\\u7528\\u4E0B\\u65B9\\u7684\\u9078\\u9805\\u5099\\u4EFD\\u60A8\\u7684\\u7E6A\\u5716\\u3002\"\n    },\n    shareableLink: {\n      title: \"\\u5F9E\\u9023\\u7D50\\u8F09\\u5165\",\n      button: \"\\u53D6\\u4EE3\\u6211\\u7684\\u5167\\u5BB9\",\n      description: \"\\u8F09\\u5165\\u5916\\u90E8\\u7E6A\\u5716\\u5C07<bold>\\u53D6\\u4EE3\\u60A8\\u76EE\\u524D\\u7684\\u5167\\u5BB9</bold>\\u3002<br></br>\\u53EF\\u5148\\u4F7F\\u7528\\u4E0B\\u65B9\\u7684\\u9078\\u9805\\u5099\\u4EFD\\u60A8\\u7684\\u7E6A\\u5716\\u3002\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid \\u81F3 Excalidraw\",\n  button: \"\\u63D2\\u5165\",\n  description: \"\\u76EE\\u524D\\u50C5\\u652F\\u63F4 <flowchartLink>Flowchart</flowchartLink> \\u3001 <sequenceLink>Sequence</sequenceLink> \\u53CA <classLink>Class </classLink> \\u5716\\u8868\\u3002\\u5176\\u9918\\u6A94\\u6848\\u985E\\u578B\\u5728 Excalidraw \\u5C07\\u6703\\u4EE5\\u5716\\u50CF\\u5448\\u73FE\\u3002\",\n  syntax: \"Mermaid \\u8A9E\\u6CD5\",\n  preview: \"\\u9810\\u89BD\"\n};\nvar zh_TW_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=zh-TW-U5VF4CCU.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-TW-U5VF4CCU.js\n"));

/***/ })

}]);