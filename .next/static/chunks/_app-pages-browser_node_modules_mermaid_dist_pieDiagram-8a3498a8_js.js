"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_mermaid_dist_pieDiagram-8a3498a8_js"],{

/***/ "(app-pages-browser)/./node_modules/mermaid/dist/pieDiagram-8a3498a8.js":
/*!**********************************************************!*\
  !*** ./node_modules/mermaid/dist/pieDiagram-8a3498a8.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   diagram: () => (/* binding */ diagram)\n/* harmony export */ });\n/* harmony import */ var _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mermaid-b5860b54.js */ \"(app-pages-browser)/./node_modules/mermaid/dist/mermaid-b5860b54.js\");\n/* harmony import */ var d3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3 */ \"(app-pages-browser)/./node_modules/d3/src/index.js\");\n/* harmony import */ var ts_dedent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ts-dedent */ \"(app-pages-browser)/./node_modules/ts-dedent/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var _braintree_sanitize_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @braintree/sanitize-url */ \"(app-pages-browser)/./node_modules/@braintree/sanitize-url/dist/index.js\");\n/* harmony import */ var dompurify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dompurify */ \"(app-pages-browser)/./node_modules/dompurify/dist/purify.js\");\n\n\n\n\n\n\n\n\n\n\n\nvar parser = function() {\n  var o = function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v)\n      ;\n    return o2;\n  }, $V0 = [1, 3], $V1 = [1, 4], $V2 = [1, 5], $V3 = [1, 6], $V4 = [1, 10, 12, 14, 16, 18, 19, 20, 21, 22], $V5 = [2, 4], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 20, 21, 22], $V7 = [20, 21, 22], $V8 = [2, 7], $V9 = [1, 12], $Va = [1, 13], $Vb = [1, 14], $Vc = [1, 15], $Vd = [1, 16], $Ve = [1, 17];\n  var parser2 = {\n    trace: function trace() {\n    },\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"PIE\": 5, \"document\": 6, \"showData\": 7, \"line\": 8, \"statement\": 9, \"txt\": 10, \"value\": 11, \"title\": 12, \"title_value\": 13, \"acc_title\": 14, \"acc_title_value\": 15, \"acc_descr\": 16, \"acc_descr_value\": 17, \"acc_descr_multiline_value\": 18, \"section\": 19, \"NEWLINE\": 20, \";\": 21, \"EOF\": 22, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"PIE\", 7: \"showData\", 10: \"txt\", 11: \"value\", 12: \"title\", 13: \"title_value\", 14: \"acc_title\", 15: \"acc_title_value\", 16: \"acc_descr\", 17: \"acc_descr_value\", 18: \"acc_descr_multiline_value\", 19: \"section\", 20: \"NEWLINE\", 21: \";\", 22: \"EOF\" },\n    productions_: [0, [3, 2], [3, 2], [3, 3], [6, 0], [6, 2], [8, 2], [9, 0], [9, 2], [9, 2], [9, 2], [9, 2], [9, 1], [9, 1], [4, 1], [4, 1], [4, 1]],\n    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 3:\n          yy.setShowData(true);\n          break;\n        case 6:\n          this.$ = $$[$0 - 1];\n          break;\n        case 8:\n          yy.addSection($$[$0 - 1], yy.cleanupValue($$[$0]));\n          break;\n        case 9:\n          this.$ = $$[$0].trim();\n          yy.setDiagramTitle(this.$);\n          break;\n        case 10:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 11:\n        case 12:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 13:\n          yy.addSection($$[$0].substr(8));\n          this.$ = $$[$0].substr(8);\n          break;\n      }\n    },\n    table: [{ 3: 1, 4: 2, 5: $V0, 20: $V1, 21: $V2, 22: $V3 }, { 1: [3] }, { 3: 7, 4: 2, 5: $V0, 20: $V1, 21: $V2, 22: $V3 }, o($V4, $V5, { 6: 8, 7: [1, 9] }), o($V6, [2, 14]), o($V6, [2, 15]), o($V6, [2, 16]), { 1: [2, 1] }, o($V7, $V8, { 8: 10, 9: 11, 1: [2, 2], 10: $V9, 12: $Va, 14: $Vb, 16: $Vc, 18: $Vd, 19: $Ve }), o($V4, $V5, { 6: 18 }), o($V4, [2, 5]), { 4: 19, 20: $V1, 21: $V2, 22: $V3 }, { 11: [1, 20] }, { 13: [1, 21] }, { 15: [1, 22] }, { 17: [1, 23] }, o($V7, [2, 12]), o($V7, [2, 13]), o($V7, $V8, { 8: 10, 9: 11, 1: [2, 3], 10: $V9, 12: $Va, 14: $Vb, 16: $Vc, 18: $Vd, 19: $Ve }), o($V4, [2, 6]), o($V7, [2, 8]), o($V7, [2, 9]), o($V7, [2, 10]), o($V7, [2, 11])],\n    defaultActions: { 7: [2, 1] },\n    parseError: function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    },\n    parse: function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      var symbol, state, action, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }\n  };\n  var lexer = function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      },\n      // resets the lexer, sets new input\n      setInput: function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      },\n      // consumes and returns one char from the input\n      input: function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      },\n      // unshifts one char (or a string) into the input\n      unput: function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      },\n      // When called from action, caches matched text and appends it on next action\n      more: function() {\n        this._more = true;\n        return this;\n      },\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      },\n      // retain first n characters of the match\n      less: function(n) {\n        this.unput(this.match.slice(n));\n      },\n      // displays already matched input, i.e. for error messages\n      pastInput: function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      },\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      },\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      },\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      },\n      // return next match in input\n      next: function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      },\n      // return next match that has a token\n      lex: function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      },\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: function begin(condition) {\n        this.conditionStack.push(condition);\n      },\n      // pop the previously active lexer condition state off the condition stack\n      popState: function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      },\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      },\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      },\n      // alias for begin(condition)\n      pushState: function pushState(condition) {\n        this.begin(condition);\n      },\n      // return the number of states currently on the stack\n      stateStackSize: function stateStackSize() {\n        return this.conditionStack.length;\n      },\n      options: { \"case-insensitive\": true },\n      performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            return 20;\n          case 3:\n            break;\n          case 4:\n            break;\n          case 5:\n            this.begin(\"title\");\n            return 12;\n          case 6:\n            this.popState();\n            return \"title_value\";\n          case 7:\n            this.begin(\"acc_title\");\n            return 14;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n          case 9:\n            this.begin(\"acc_descr\");\n            return 16;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n          case 11:\n            this.begin(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n          case 14:\n            this.begin(\"string\");\n            break;\n          case 15:\n            this.popState();\n            break;\n          case 16:\n            return \"txt\";\n          case 17:\n            return 5;\n          case 18:\n            return 7;\n          case 19:\n            return \"value\";\n          case 20:\n            return 22;\n        }\n      },\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:[\\s]+)/i, /^(?:title\\b)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:[\\}])/i, /^(?:[^\\}]*)/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:pie\\b)/i, /^(?:showData\\b)/i, /^(?::[\\s]*[\\d]+(?:\\.[\\d]+)?)/i, /^(?:$)/i],\n      conditions: { \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [6], \"inclusive\": false }, \"string\": { \"rules\": [15, 16], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 7, 9, 11, 14, 17, 18, 19, 20], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nconst parser$1 = parser;\nconst DEFAULT_PIE_CONFIG = _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.B.pie;\nconst DEFAULT_PIE_DB = {\n  sections: {},\n  showData: false,\n  config: DEFAULT_PIE_CONFIG\n};\nlet sections = DEFAULT_PIE_DB.sections;\nlet showData = DEFAULT_PIE_DB.showData;\nconst config = structuredClone(DEFAULT_PIE_CONFIG);\nconst getConfig = () => structuredClone(config);\nconst clear = () => {\n  sections = structuredClone(DEFAULT_PIE_DB.sections);\n  showData = DEFAULT_PIE_DB.showData;\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.v)();\n};\nconst addSection = (label, value) => {\n  label = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.d)(label, (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)());\n  if (sections[label] === void 0) {\n    sections[label] = value;\n    _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(`added new section: ${label}, with value: ${value}`);\n  }\n};\nconst getSections = () => sections;\nconst cleanupValue = (value) => {\n  if (value.substring(0, 1) === \":\") {\n    value = value.substring(1).trim();\n  }\n  return Number(value.trim());\n};\nconst setShowData = (toggle) => {\n  showData = toggle;\n};\nconst getShowData = () => showData;\nconst db = {\n  getConfig,\n  clear,\n  setDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.q,\n  getDiagramTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.t,\n  setAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.s,\n  getAccTitle: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.g,\n  setAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.b,\n  getAccDescription: _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.a,\n  addSection,\n  getSections,\n  cleanupValue,\n  setShowData,\n  getShowData\n};\nconst getStyles = (options) => `\n  .pieCircle{\n    stroke: ${options.pieStrokeColor};\n    stroke-width : ${options.pieStrokeWidth};\n    opacity : ${options.pieOpacity};\n  }\n  .pieOuterCircle{\n    stroke: ${options.pieOuterStrokeColor};\n    stroke-width: ${options.pieOuterStrokeWidth};\n    fill: none;\n  }\n  .pieTitleText {\n    text-anchor: middle;\n    font-size: ${options.pieTitleTextSize};\n    fill: ${options.pieTitleTextColor};\n    font-family: ${options.fontFamily};\n  }\n  .slice {\n    font-family: ${options.fontFamily};\n    fill: ${options.pieSectionTextColor};\n    font-size:${options.pieSectionTextSize};\n    // fill: white;\n  }\n  .legend text {\n    fill: ${options.pieLegendTextColor};\n    font-family: ${options.fontFamily};\n    font-size: ${options.pieLegendTextSize};\n  }\n`;\nconst styles = getStyles;\nconst createPieArcs = (sections2) => {\n  const pieData = Object.entries(sections2).map((element) => {\n    return {\n      label: element[0],\n      value: element[1]\n    };\n  }).sort((a, b) => {\n    return b.value - a.value;\n  });\n  const pie$1 = (0,d3__WEBPACK_IMPORTED_MODULE_0__.pie)().value(\n    (d3Section) => d3Section.value\n  );\n  return pie$1(pieData);\n};\nconst draw = (text, id, _version, diagObj) => {\n  _mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.l.debug(\"rendering pie chart\\n\" + text);\n  const db2 = diagObj.db;\n  const globalConfig = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.c)();\n  const pieConfig = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.C)(db2.getConfig(), globalConfig.pie);\n  const MARGIN = 40;\n  const LEGEND_RECT_SIZE = 18;\n  const LEGEND_SPACING = 4;\n  const height = 450;\n  const pieWidth = height;\n  const svg = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.A)(id);\n  const group = svg.append(\"g\");\n  const sections2 = db2.getSections();\n  group.attr(\"transform\", \"translate(\" + pieWidth / 2 + \",\" + height / 2 + \")\");\n  const { themeVariables } = globalConfig;\n  let [outerStrokeWidth] = (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.D)(themeVariables.pieOuterStrokeWidth);\n  outerStrokeWidth ?? (outerStrokeWidth = 2);\n  const textPosition = pieConfig.textPosition;\n  const radius = Math.min(pieWidth, height) / 2 - MARGIN;\n  const arcGenerator = (0,d3__WEBPACK_IMPORTED_MODULE_0__.arc)().innerRadius(0).outerRadius(radius);\n  const labelArcGenerator = (0,d3__WEBPACK_IMPORTED_MODULE_0__.arc)().innerRadius(radius * textPosition).outerRadius(radius * textPosition);\n  group.append(\"circle\").attr(\"cx\", 0).attr(\"cy\", 0).attr(\"r\", radius + outerStrokeWidth / 2).attr(\"class\", \"pieOuterCircle\");\n  const arcs = createPieArcs(sections2);\n  const myGeneratedColors = [\n    themeVariables.pie1,\n    themeVariables.pie2,\n    themeVariables.pie3,\n    themeVariables.pie4,\n    themeVariables.pie5,\n    themeVariables.pie6,\n    themeVariables.pie7,\n    themeVariables.pie8,\n    themeVariables.pie9,\n    themeVariables.pie10,\n    themeVariables.pie11,\n    themeVariables.pie12\n  ];\n  const color = (0,d3__WEBPACK_IMPORTED_MODULE_0__.scaleOrdinal)(myGeneratedColors);\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"path\").attr(\"d\", arcGenerator).attr(\"fill\", (datum) => {\n    return color(datum.data.label);\n  }).attr(\"class\", \"pieCircle\");\n  let sum = 0;\n  Object.keys(sections2).forEach((key) => {\n    sum += sections2[key];\n  });\n  group.selectAll(\"mySlices\").data(arcs).enter().append(\"text\").text((datum) => {\n    return (datum.data.value / sum * 100).toFixed(0) + \"%\";\n  }).attr(\"transform\", (datum) => {\n    return \"translate(\" + labelArcGenerator.centroid(datum) + \")\";\n  }).style(\"text-anchor\", \"middle\").attr(\"class\", \"slice\");\n  group.append(\"text\").text(db2.getDiagramTitle()).attr(\"x\", 0).attr(\"y\", -(height - 50) / 2).attr(\"class\", \"pieTitleText\");\n  const legend = group.selectAll(\".legend\").data(color.domain()).enter().append(\"g\").attr(\"class\", \"legend\").attr(\"transform\", (_datum, index) => {\n    const height2 = LEGEND_RECT_SIZE + LEGEND_SPACING;\n    const offset = height2 * color.domain().length / 2;\n    const horizontal = 12 * LEGEND_RECT_SIZE;\n    const vertical = index * height2 - offset;\n    return \"translate(\" + horizontal + \",\" + vertical + \")\";\n  });\n  legend.append(\"rect\").attr(\"width\", LEGEND_RECT_SIZE).attr(\"height\", LEGEND_RECT_SIZE).style(\"fill\", color).style(\"stroke\", color);\n  legend.data(arcs).append(\"text\").attr(\"x\", LEGEND_RECT_SIZE + LEGEND_SPACING).attr(\"y\", LEGEND_RECT_SIZE - LEGEND_SPACING).text((datum) => {\n    const { label, value } = datum.data;\n    if (db2.getShowData()) {\n      return `${label} [${value}]`;\n    }\n    return label;\n  });\n  const longestTextWidth = Math.max(\n    ...legend.selectAll(\"text\").nodes().map((node) => (node == null ? void 0 : node.getBoundingClientRect().width) ?? 0)\n  );\n  const totalWidth = pieWidth + MARGIN + LEGEND_RECT_SIZE + LEGEND_SPACING + longestTextWidth;\n  svg.attr(\"viewBox\", `0 0 ${totalWidth} ${height}`);\n  (0,_mermaid_b5860b54_js__WEBPACK_IMPORTED_MODULE_5__.i)(svg, height, totalWidth, pieConfig.useMaxWidth);\n};\nconst renderer = { draw };\nconst diagram = {\n  parser: parser$1,\n  db,\n  renderer,\n  styles\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/mermaid/dist/pieDiagram-8a3498a8.js\n"));

/***/ })

}]);