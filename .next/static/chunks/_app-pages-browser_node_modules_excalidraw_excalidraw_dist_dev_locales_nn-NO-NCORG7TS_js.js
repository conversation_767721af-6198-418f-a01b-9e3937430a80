"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_nn-NO-NCORG7TS_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/nn-NO-NCORG7TS.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/nn-NO-NCORG7TS.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ nn_NO_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/nn-NO.json\nvar labels = {\n  paste: \"Lim inn\",\n  pasteAsPlaintext: \"\",\n  pasteCharts: \"Lim inn diagram\",\n  selectAll: \"Vel alt\",\n  multiSelect: \"Legg til element i utval\",\n  moveCanvas: \"Flytt lerretet\",\n  cut: \"Klipp ut\",\n  copy: \"Kopier\",\n  copyAsPng: \"Kopier til utklippstavla som PNG\",\n  copyAsSvg: \"Kopier til utklippstavla som SVG\",\n  copyText: \"\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Flytt framover\",\n  sendToBack: \"Send heilt bak\",\n  bringToFront: \"Flytt heilt fram\",\n  sendBackward: \"Send bakover\",\n  delete: \"Slett\",\n  copyStyles: \"Kopier stilar\",\n  pasteStyles: \"Lim inn stilar\",\n  stroke: \"Strek\",\n  background: \"Bakgrunn\",\n  fill: \"Fyll\",\n  strokeWidth: \"Strekbreidd\",\n  strokeStyle: \"Strekstil\",\n  strokeStyle_solid: \"Solid\",\n  strokeStyle_dashed: \"Stipla\",\n  strokeStyle_dotted: \"Prikka\",\n  sloppiness: \"Ujamnheit\",\n  opacity: \"Synlegheit\",\n  textAlign: \"Tekstjustering\",\n  edges: \"Kanter\",\n  sharp: \"Skarp\",\n  round: \"Rund\",\n  arrowheads: \"Pilhovud\",\n  arrowhead_none: \"Ingen\",\n  arrowhead_arrow: \"Pil\",\n  arrowhead_bar: \"Stolpe\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Trekant\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Skriftstorleik\",\n  fontFamily: \"Skrifttype\",\n  addWatermark: \"Legg til \\xABLaga med Excalidraw\\xBB\",\n  handDrawn: \"Handteikna\",\n  normal: \"Normal\",\n  code: \"Kode\",\n  small: \"Liten\",\n  medium: \"Medium\",\n  large: \"Stor\",\n  veryLarge: \"Sv\\xE6rt stor\",\n  solid: \"Solid\",\n  hachure: \"Skravert\",\n  zigzag: \"\",\n  crossHatch: \"Krysskravert\",\n  thin: \"Tynn\",\n  bold: \"Tjukk\",\n  left: \"Venstre\",\n  center: \"Midstill\",\n  right: \"H\\xF8gre\",\n  extraBold: \"Ekstra tjukk\",\n  architect: \"Arkitekt\",\n  artist: \"Kunstnar\",\n  cartoonist: \"Teiknar\",\n  fileTitle: \"Filnamn\",\n  colorPicker: \"Fargeveljar\",\n  canvasColors: \"Brukt p\\xE5 lerretet\",\n  canvasBackground: \"Lerretsbakgrunn\",\n  drawingCanvas: \"Lerret\",\n  layers: \"Lag\",\n  actions: \"Handlingar\",\n  language: \"Spr\\xE5k\",\n  liveCollaboration: \"\",\n  duplicateSelection: \"Dupliser\",\n  untitled: \"Utan namn\",\n  name: \"Namn\",\n  yourName: \"Namnet ditt\",\n  madeWithExcalidraw: \"Laga med Excalidraw\",\n  group: \"Grupper utval\",\n  ungroup: \"Avgrupper utval\",\n  collaborators: \"Samarbeidarar\",\n  showGrid: \"Vis rutenett\",\n  addToLibrary: \"Legg til i bibliotek\",\n  removeFromLibrary: \"Fjern fr\\xE5 bibliotek\",\n  libraryLoadingMessage: \"Laster bibliotek\\u2026\",\n  libraries: \"Blad gjennom bibliotek\",\n  loadingScene: \"Laster scene\\u2026\",\n  align: \"Juster\",\n  alignTop: \"Juster til topp\",\n  alignBottom: \"Juster til botn\",\n  alignLeft: \"Juster til venstre\",\n  alignRight: \"Juster til h\\xF8gre\",\n  centerVertically: \"Midtstill vertikalt\",\n  centerHorizontally: \"Midtstill horisontalt\",\n  distributeHorizontally: \"Sprei horisontalt\",\n  distributeVertically: \"Sprei vertikalt\",\n  flipHorizontal: \"Vipp vassrett\",\n  flipVertical: \"Vipp loddrett\",\n  viewMode: \"Visningsmodus\",\n  share: \"Del\",\n  showStroke: \"Vis fargeveljar for linjer\",\n  showBackground: \"Vis fargeveljar for bakgrunn\",\n  toggleTheme: \"Veksle tema\",\n  personalLib: \"Personleg bibliotek\",\n  excalidrawLib: \"Excalidraw-bibliotek\",\n  decreaseFontSize: \"Gjer skriftstorleik mindre\",\n  increaseFontSize: \"Gjer skriftstorleik st\\xF8rre\",\n  unbindText: \"Avbind tekst\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"Rediger lenke\",\n    editEmbed: \"\",\n    create: \"Lag lenke\",\n    createEmbed: \"\",\n    label: \"Lenke\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"\",\n    unlock: \"\",\n    lockAll: \"\",\n    unlockAll: \"\"\n  },\n  statusPublished: \"\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"Tilbakestill lerretet\",\n  exportJSON: \"Eksporter til fil\",\n  exportImage: \"\",\n  export: \"\",\n  copyToClipboard: \"Kopier til utklippstavla\",\n  save: \"Lagre til noverande fil\",\n  saveAs: \"Lagre som\",\n  load: \"\",\n  getShareableLink: \"Hent delingslenke\",\n  close: \"Lukk\",\n  selectLanguage: \"Vel spr\\xE5k\",\n  scrollBackToContent: \"Skroll tilbake til innhald\",\n  zoomIn: \"Zoom inn\",\n  zoomOut: \"Zoom ut\",\n  resetZoom: \"Nullstill zoom\",\n  menu: \"Meny\",\n  done: \"Ferdig\",\n  edit: \"Rediger\",\n  undo: \"Angre\",\n  redo: \"Gjer om\",\n  resetLibrary: \"Nullstill bibliotek\",\n  createNewRoom: \"Lag nytt rom\",\n  fullScreen: \"Fullskjerm\",\n  darkMode: \"M\\xF8rk modus\",\n  lightMode: \"Lys modus\",\n  zenMode: \"Zen-modus\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Avslutt zen-modus\",\n  cancel: \"Avbryt\",\n  clear: \"T\\xF8m\",\n  remove: \"Fjern\",\n  embed: \"\",\n  publishLibrary: \"Publiser\",\n  submit: \"Send inn\",\n  confirm: \"Stadfest\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Dette vil t\\xF8mme lerretet. Er du sikker?\",\n  couldNotCreateShareableLink: \"Kunne ikkje lage delingslenke.\",\n  couldNotCreateShareableLinkTooBig: \"Kunne ikkje opprette deleleg lenke: scena er for stor\",\n  couldNotLoadInvalidFile: \"Kunne ikkje laste inn ugyldig fil\",\n  importBackendFailed: \"Importering av backend feila.\",\n  cannotExportEmptyCanvas: \"Kan ikkje eksportere eit tomt lerret.\",\n  couldNotCopyToClipboard: \"\",\n  decryptFailed: \"Kunne ikkje dekryptere data.\",\n  uploadedSecurly: \"Opplastinga er kryptert og er ikkje mogleg \\xE5 lese av Excalidraw-serveren eller tredjepartar.\",\n  loadSceneOverridePrompt: \"Innlasting av ekstern teikning erstattar ditt eksisterande innhald. Ynskjer du \\xE5 fortsette?\",\n  collabStopOverridePrompt: \"Viss du avsluttar \\xF8kta overskriv du den f\\xF8rre, lokalt lagra teikninga di. Er du sikker?\\n\\n(\\xD8nsker du \\xE5 halde fram med denne? D\\xE5 er det berre \\xE5 lukke denne fana.)\",\n  errorAddingToLibrary: \"Kunne ikkje legge elementet i biblioteket\",\n  errorRemovingFromLibrary: \"Kunne ikkje fjerne elementet fr\\xE5 biblioteket\",\n  confirmAddLibrary: \"Dette vil legge til {{numShapes}} form(er) i biblioteket ditt. Er du sikker?\",\n  imageDoesNotContainScene: \"Dette biletet ser ikkje ut til \\xE5 ha noko scenedata. Har du skrutt p\\xE5 innbygging av scene medan eksporteringa heldt p\\xE5?\",\n  cannotRestoreFromImage: \"Scena kunne ikkje gjenopprettast fr\\xE5 denne biletfila\",\n  invalidSceneUrl: \"Kunne ikkje hente noko scene fr\\xE5 den URL-en. Ho er anten \\xF8ydelagd eller inneheld ikkje gyldig Excalidraw JSON-data.\",\n  resetLibrary: \"Dette vil fjerne alt innhald fr\\xE5 biblioteket. Er du sikker?\",\n  removeItemsFromsLibrary: \"Slette {{count}}\\xA0element fr\\xE5 biblioteket?\",\n  invalidEncryptionKey: \"Krypteringsn\\xF8kkelen m\\xE5 ha 22 teikn. Sanntidssamarbeid er deaktivert.\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"Filtypen er ikkje st\\xF8tta.\",\n  imageInsertError: \"Kunne ikkje sette inn biletet. Pr\\xF8v igjen seinare...\",\n  fileTooBig: \"Fila er for stor. Maksimal tillate storleik er {{maxSize}}.\",\n  svgImageInsertError: \"Kunne ikkje sette inn SVG-biletet. SVG-koden ser ugyldig ut.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Ugyldig SVG.\",\n  cannotResolveCollabServer: \"Kunne ikkje kople til samarbeidsserveren. Ver vennleg \\xE5 oppdatere inn sida og pr\\xF8v p\\xE5 nytt.\",\n  importLibraryError: \"\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Vel\",\n  image: \"Sett in bilete\",\n  rectangle: \"Rektangel\",\n  diamond: \"Diamant\",\n  ellipse: \"Ellipse\",\n  arrow: \"Pil\",\n  line: \"Linje\",\n  freedraw: \"Teikn\",\n  text: \"Tekst\",\n  library: \"Bibliotek\",\n  lock: \"Hald fram med valt verkt\\xF8y\",\n  penMode: \"\",\n  link: \"Legg til/ oppdater lenke til valt figur\",\n  eraser: \"Viskel\\xEAr\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Handlingar: lerret\",\n  selectedShapeActions: \"Handlingar: valt objekt\",\n  shapes: \"Formar\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"Klikk for \\xE5 starte linje med fleire punkt, eller drag for ei enkel linje\",\n  freeDraw: \"Klikk og drag, slepp n\\xE5r du er ferdig\",\n  text: \"Tips: du kan \\xF2g leggje til tekst ved \\xE5 dobbeltklikke kor som helst med utvalgsverktyet\",\n  embeddable: \"\",\n  text_selected: \"Dobbelklikk eller trykk ENTER for \\xE5 redigere teksta\",\n  text_editing: \"Trykk Escape eller CtrlOrCmd+ENTER for \\xE5 fullf\\xF8re redigeringa\",\n  linearElementMulti: \"Klikk p\\xE5 siste punkt eller trykk Escape eller Enter for \\xE5 fullf\\xF8re\",\n  lockAngle: \"Du kan begrense vinkelen ved \\xE5 holde nede SKIFT\",\n  resize: \"Du kan halde fram med forholdet ved \\xE5 trykke SHIFT medan du endrar storleik,\\ntrykk ALT for \\xE5 endre storleiken fr\\xE5 midten\",\n  resizeImage: \"Du kan endre storleiken fritt ved \\xE5 halde inne SHIFT,\\nhald ALT for \\xE5 endre storleik fr\\xE5 sentrum\",\n  rotate: \"Du kan l\\xE5se vinklane ved \\xE5 halde SHIFT medan du roterer\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"Trykk p\\xE5 Slett for \\xE5 fjerne punkt(a),\\nCtrl / Cmd+D for \\xE5 duplisere, eller drag for \\xE5 flytte\",\n  lineEditor_nothingSelected: \"Vel eit punkt \\xE5 redigere (hald inne SHIFT for \\xE5 velje fleire),\\neller hald inne Alt og klikk for \\xE5 legge til nye punkt\",\n  placeImage: \"Klikk for \\xE5 plassere biletet, eller klikk og drag for \\xE5 velje storleik manuelt\",\n  publishLibrary: \"Publiser ditt eige bibliotek\",\n  bindTextToElement: \"Trykk p\\xE5 enter for \\xE5 legge til tekst\",\n  deepBoxSelect: \"Hald inne Ctrl / Cmd for \\xE5 velje djupt, og forhindre flytting\",\n  eraserRevert: \"Hald inne Alt for \\xE5 reversere markering av element for sletting\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Kan ikkje vise f\\xF8rehandsvising\",\n  canvasTooBig: \"Lerretet er mogleg for stort.\",\n  canvasTooBigTip: \"Tips: pr\\xF8v \\xE5 flytte elementa som er lengst fr\\xE5 kvarandre, litt n\\xE6rare kvarandre.\"\n};\nvar errorSplash = {\n  headingMain: \"Ein feil oppstod. Pr\\xF8v <button>\\xE5 laste sida p\\xE5 nytt.</button>\",\n  clearCanvasMessage: \"Om ny sidelasting ikkje fungerer, pr\\xF8v <button>\\xE5 t\\xF8mme lerretet.</button>\",\n  clearCanvasCaveat: \" Dette vil f\\xF8re til tap av arbeid \",\n  trackedToSentry: \"Feilen med identifikator {{eventId}} vart logga i systemet v\\xE5rt.\",\n  openIssueMessage: \"Vi er veldig n\\xF8ye med \\xE5 ikkje inkludere scene-opplysingane dine i feilmeldinga. Viss scena di ikkje er privat kan du vurdere \\xE5 f\\xF8lge opp i <button>feilrapporteringssystemet v\\xE5rt.</button> Ta med opplysingane nedanfor ved \\xE5 kopiere og lime inn i GitHub-saka.\",\n  sceneContent: \"Scene-innhald:\"\n};\nvar roomDialog = {\n  desc_intro: \"Du kan invitere personar til scena di for \\xE5 samarbeide med deg.\",\n  desc_privacy: \"Ta det med ro; \\xF8kta brukar ende-til-ende-kryptering, s\\xE5 alt du teiknar held fram med \\xE5 vere privat. Ikkje ein gong serveren v\\xE5r kan sj\\xE5 kva du lagar.\",\n  button_startSession: \"Start \\xF8kt\",\n  button_stopSession: \"Stopp \\xF8kt\",\n  desc_inProgressIntro: \"Sanntids-samarbeids\\xF8kt er no i gang.\",\n  desc_shareLink: \"Del denne lenka med dei du vil samarbeide med:\",\n  desc_exitSession: \"Dersom du avsluttar \\xF8kta blir du kopla fr\\xE5 rommet, men du kan halde fram med \\xE5 arbeide med scena lokalt. Ver merksam p\\xE5 at dette ikkje vil p\\xE5verke andre personar, og desse vil framleis ha moglegheit til \\xE5 samarbeide p\\xE5 deira eigen versjon.\",\n  shareTitle: \"Bli med p\\xE5 eit sanntidssamarbeid p\\xE5 Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Feil\"\n};\nvar exportDialog = {\n  disk_title: \"Lagre til disk\",\n  disk_details: \"Eksporter scenedataa til ei fil du kan importere seinare.\",\n  disk_button: \"Lagre til fil\",\n  link_title: \"Deleleg lenke\",\n  link_details: \"Eksporter som skrivebeskytta lenke.\",\n  link_button: \"Eksporter til lenke\",\n  excalidrawplus_description: \"Lagre scena til Excalidraw+-arbeidsomr\\xE5det ditt.\",\n  excalidrawplus_button: \"Eksporter\",\n  excalidrawplus_exportError: \"Kunne ikkje eksportere til Excalidraw+ akkurat no...\"\n};\nvar helpDialog = {\n  blog: \"Les bloggen v\\xE5r\",\n  click: \"klikk\",\n  deepSelect: \"Marker djupt\",\n  deepBoxSelect: \"Marker djupt inni boksen og forhindr flytting\",\n  curvedArrow: \"Boga pil\",\n  curvedLine: \"Boga linje\",\n  documentation: \"Dokumentasjon\",\n  doubleClick: \"dobbelklikk\",\n  drag: \"drag\",\n  editor: \"Redigering\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"Funne eit problem? Send inn\",\n  howto: \"F\\xF8lg vegleiinga v\\xE5r\",\n  or: \"eller\",\n  preventBinding: \"Hindre pilkopling\",\n  tools: \"\",\n  shortcuts: \"Tastatursnarvegar\",\n  textFinish: \"Fullf\\xF8r redigering (teksthandsamar)\",\n  textNewLine: \"Legg til ny linje (teksthandsamar)\",\n  title: \"Hjelp\",\n  view: \"Vising\",\n  zoomToFit: \"Zoom for \\xE5 sj\\xE5 alle elementa\",\n  zoomToSelection: \"Zoom til utval\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\"\n};\nvar clearCanvasDialog = {\n  title: \"T\\xF8m lerretet\"\n};\nvar publishDialog = {\n  title: \"Publiser bibliotek\",\n  itemName: \"Elementnamn\",\n  authorName: \"Eigaren sitt namn\",\n  githubUsername: \"GitHub-brukarnamn\",\n  twitterUsername: \"Twitter-brukarnamn\",\n  libraryName: \"Biblioteknamn\",\n  libraryDesc: \"Bibliotekskildring\",\n  website: \"Nettstad\",\n  placeholder: {\n    authorName: \"Namnet eller brukarnamnet ditt\",\n    libraryName: \"Namnet p\\xE5 biblioteket ditt\",\n    libraryDesc: \"Skildring av biblioteket ditt s\\xE5nn at andre forst\\xE5r bruken av det\",\n    githubHandle: \"GitHub-brukarnamn (valfritt), slik at du kan redigere bibiloteket n\\xE5r det er sendt inn til vurdering\",\n    twitterHandle: \"Twitter-brukarnamn (valfritt), s\\xE5 vi veit kven vi skal kreditere p\\xE5 Twitter\",\n    website: \"Lenke til den personlege nettstaden din eller ein anna stad (valfritt)\"\n  },\n  errors: {\n    required: \"Kravt\",\n    website: \"Fyll inn ein gyldig URL\"\n  },\n  noteDescription: \"Send inn biblioteket ditt til inkludering i <link>den offentlege bibliotek-kjeldekoda</link>slik at andre kan bruke det i teikningane deira.\",\n  noteGuidelines: \"Biblioteket m\\xE5 godkjennast manuelt fyrst. Ver vennleg \\xE5 lese <link>retningslinjene</link> f\\xF8r du sender inn. Du kjem til \\xE5 trenge ein GitHub-konto for \\xE5 kommunisere og gjere endringar dersom kravt, men det er ikkje strengt naudsynt.\",\n  noteLicense: \"Ved \\xE5 sende inn godkjenner du at biblioteket vert publisert under <link>MIT-lisensen, </link>som kort sagt betyr at kven som helst kan bruke det utan avgrensingar.\",\n  noteItems: \"Kvart bibliotekselement m\\xE5 ha eit eige namn, slik at det er mogleg \\xE5 filtrere. Dei f\\xF8lgande bibliotekselementa blir inkludert:\",\n  atleastOneLibItem: \"Ver vennleg \\xE5 markere minst eitt bibliotekselement for \\xE5 starte\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"Bibliotek innsendt\",\n  content: \"Tusen takk {{authorName}}! Biblioteket ditt har blitt sendt inn til gjennomgang. Du kan halde styr p\\xE5 status<link>her</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Tilbakestill bibliotek\",\n  removeItemsFromLib: \"Fjern valde element fr\\xE5 biblioteket\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Teikningane dine er ende-til-ende-krypterte slik at Excalidraw sine serverar aldri f\\xE5r sj\\xE5 dei.\",\n  link: \"Blogginnlegg om ende-til-ende-kryptering i Excalidraw\"\n};\nvar stats = {\n  angle: \"Vinkel\",\n  element: \"Element\",\n  elements: \"Element\",\n  height: \"H\\xF8gde\",\n  scene: \"Scene\",\n  selected: \"Valde\",\n  storage: \"Lagring\",\n  title: \"Statistikk for nerdar\",\n  total: \"Totalt\",\n  version: \"Versjon\",\n  versionCopy: \"Klikk for \\xE5 kopiere\",\n  versionNotAvailable: \"Versjonen er ikkje tilgjengeleg\",\n  width: \"Breidde\"\n};\nvar toast = {\n  addedToLibrary: \"Lagt til i bibliotek\",\n  copyStyles: \"Kopierte stilane.\",\n  copyToClipboard: \"Kopiert til utklippstavla.\",\n  copyToClipboardAsPng: \"Kopierte {{exportSelection}} til utklippstavla som PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Fila er lagra.\",\n  fileSavedToFilename: \"Lagra som {filename}\",\n  canvas: \"lerret\",\n  selection: \"val\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Gjennomsiktig\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar nn_NO_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=nn-NO-NCORG7TS.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/nn-NO-NCORG7TS.js\n"));

/***/ })

}]);