"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_pt-BR-APOPYZJ7_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/pt-BR-APOPYZJ7.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/pt-BR-APOPYZJ7.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ pt_BR_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/pt-BR.json\nvar labels = {\n  paste: \"Colar\",\n  pasteAsPlaintext: \"Colar como texto sem formata\\xE7\\xE3o\",\n  pasteCharts: \"Colar gr\\xE1ficos\",\n  selectAll: \"Selecionar tudo\",\n  multiSelect: \"Adicionar elemento \\xE0 sele\\xE7\\xE3o\",\n  moveCanvas: \"Mover tela\",\n  cut: \"Recortar\",\n  copy: \"Copiar\",\n  copyAsPng: \"Copiar para a \\xE1rea de transfer\\xEAncia como PNG\",\n  copyAsSvg: \"Copiar para a \\xE1rea de transfer\\xEAncia como SVG\",\n  copyText: \"Copiar para \\xE1rea de transfer\\xEAncia como texto\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Trazer para a frente\",\n  sendToBack: \"Enviar para o fundo\",\n  bringToFront: \"Trazer para o primeiro plano\",\n  sendBackward: \"Enviar para tr\\xE1s\",\n  delete: \"Apagar\",\n  copyStyles: \"Copiar os estilos\",\n  pasteStyles: \"Colar os estilos\",\n  stroke: \"Contorno\",\n  background: \"Fundo\",\n  fill: \"Preenchimento\",\n  strokeWidth: \"Espessura do tra\\xE7o\",\n  strokeStyle: \"Estilo de tra\\xE7o\",\n  strokeStyle_solid: \"S\\xF3lido\",\n  strokeStyle_dashed: \"Tracejado\",\n  strokeStyle_dotted: \"Pontilhado\",\n  sloppiness: \"Precis\\xE3o do tra\\xE7o\",\n  opacity: \"Opacidade\",\n  textAlign: \"Alinhamento do texto\",\n  edges: \"Arestas\",\n  sharp: \"Pontudo\",\n  round: \"Arredondado\",\n  arrowheads: \"Pontas\",\n  arrowhead_none: \"Nenhuma\",\n  arrowhead_arrow: \"Flecha\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Tri\\xE2ngulo\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Tamanho da fonte\",\n  fontFamily: \"Fam\\xEDlia da fonte\",\n  addWatermark: 'Adicionar \"Feito com Excalidraw\"',\n  handDrawn: \"Manuscrito\",\n  normal: \"Normal\",\n  code: \"C\\xF3digo\",\n  small: \"Pequeno\",\n  medium: \"M\\xE9dio\",\n  large: \"Grande\",\n  veryLarge: \"Muito grande\",\n  solid: \"S\\xF3lido\",\n  hachure: \"Hachura\",\n  zigzag: \"Zigue-zague\",\n  crossHatch: \"Hachura cruzada\",\n  thin: \"Fino\",\n  bold: \"Espesso\",\n  left: \"Esquerda\",\n  center: \"Centralizar\",\n  right: \"Direita\",\n  extraBold: \"Muito espesso\",\n  architect: \"Arquiteto\",\n  artist: \"Artista\",\n  cartoonist: \"Cartunista\",\n  fileTitle: \"Nome do arquivo\",\n  colorPicker: \"Seletor de cores\",\n  canvasColors: \"Usado na tela\",\n  canvasBackground: \"Fundo da tela\",\n  drawingCanvas: \"Tela de desenho\",\n  layers: \"Camadas\",\n  actions: \"A\\xE7\\xF5es\",\n  language: \"Idioma\",\n  liveCollaboration: \"Colabora\\xE7\\xE3o ao vivo...\",\n  duplicateSelection: \"Duplicar\",\n  untitled: \"Sem t\\xEDtulo\",\n  name: \"Nome\",\n  yourName: \"Seu nome\",\n  madeWithExcalidraw: \"Feito com Excalidraw\",\n  group: \"Agrupar sele\\xE7\\xE3o\",\n  ungroup: \"Desagrupar sele\\xE7\\xE3o\",\n  collaborators: \"Colaboradores\",\n  showGrid: \"Mostrar grade\",\n  addToLibrary: \"Adicionar \\xE0 biblioteca\",\n  removeFromLibrary: \"Remover da biblioteca\",\n  libraryLoadingMessage: \"Carregando biblioteca\\u2026\",\n  libraries: \"Procurar bibliotecas\",\n  loadingScene: \"Carregando cena\\u2026\",\n  align: \"Alinhamento\",\n  alignTop: \"Alinhar ao topo\",\n  alignBottom: \"Alinhar embaixo\",\n  alignLeft: \"Alinhar \\xE0 esquerda\",\n  alignRight: \"Alinhar \\xE0 direita\",\n  centerVertically: \"Centralizar verticalmente\",\n  centerHorizontally: \"Centralizar horizontalmente\",\n  distributeHorizontally: \"Distribuir horizontalmente\",\n  distributeVertically: \"Distribuir verticalmente\",\n  flipHorizontal: \"Inverter horizontalmente\",\n  flipVertical: \"Inverter verticalmente\",\n  viewMode: \"Modo de visualiza\\xE7\\xE3o\",\n  share: \"Compartilhar\",\n  showStroke: \"Exibir seletor de cores do tra\\xE7o\",\n  showBackground: \"Exibir seletor de cores do fundo\",\n  toggleTheme: \"Alternar tema\",\n  personalLib: \"Biblioteca Pessoal\",\n  excalidrawLib: \"Biblioteca do Excalidraw\",\n  decreaseFontSize: \"Diminuir o tamanho da fonte\",\n  increaseFontSize: \"Aumentar o tamanho da fonte\",\n  unbindText: \"Desvincular texto\",\n  bindText: \"Vincular texto ao cont\\xEAiner\",\n  createContainerFromText: \"Envolver texto em um cont\\xEAiner\",\n  link: {\n    edit: \"Editar link\",\n    editEmbed: \"\",\n    create: \"Criar link\",\n    createEmbed: \"\",\n    label: \"Link\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"Editar linha\",\n    exit: \"Sair do editor de linha\"\n  },\n  elementLock: {\n    lock: \"Bloquear\",\n    unlock: \"Desbloquear\",\n    lockAll: \"Bloquear tudo\",\n    unlockAll: \"Desbloquear tudo\"\n  },\n  statusPublished: \"Publicado\",\n  sidebarLock: \"Manter barra lateral aberta\",\n  selectAllElementsInFrame: \"Selecionar todos os elementos no quadro\",\n  removeAllElementsFromFrame: \"Remover todos os elementos do quadro\",\n  eyeDropper: \"Escolher cor da tela\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Nenhum item adicionado ainda...\",\n  hint_emptyLibrary: \"Selecione um item na tela para adicion\\xE1-lo aqui, ou instale uma biblioteca do reposit\\xF3rio p\\xFAblico, abaixo.\",\n  hint_emptyPrivateLibrary: \"Selecione um item na tela para adicion\\xE1-lo aqui.\"\n};\nvar buttons = {\n  clearReset: \"Limpar o canvas e redefinir a cor de fundo\",\n  exportJSON: \"Exportar arquivo\",\n  exportImage: \"Exportar imagem...\",\n  export: \"Salvar como...\",\n  copyToClipboard: \"Copiar para o clipboard\",\n  save: \"Salvar para o arquivo atual\",\n  saveAs: \"Salvar como\",\n  load: \"Abrir\",\n  getShareableLink: \"Obter um link de compartilhamento\",\n  close: \"Fechar\",\n  selectLanguage: \"Selecionar idioma\",\n  scrollBackToContent: \"Voltar para o conte\\xFAdo\",\n  zoomIn: \"Aumentar zoom\",\n  zoomOut: \"Diminuir zoom\",\n  resetZoom: \"Redefinir zoom\",\n  menu: \"Menu\",\n  done: \"Conclu\\xEDdo\",\n  edit: \"Editar\",\n  undo: \"Desfazer\",\n  redo: \"Refazer\",\n  resetLibrary: \"Redefinir biblioteca\",\n  createNewRoom: \"Criar nova sala\",\n  fullScreen: \"Tela cheia\",\n  darkMode: \"Modo escuro\",\n  lightMode: \"Modo claro\",\n  zenMode: \"Modo Zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Sair do modo zen\",\n  cancel: \"Cancelar\",\n  clear: \"Limpar\",\n  remove: \"Remover\",\n  embed: \"\",\n  publishLibrary: \"Publicar\",\n  submit: \"Enviar\",\n  confirm: \"Confirmar\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Isto ir\\xE1 limpar toda a tela. Voc\\xEA tem certeza?\",\n  couldNotCreateShareableLink: \"N\\xE3o foi poss\\xEDvel criar um link de compartilhamento.\",\n  couldNotCreateShareableLinkTooBig: \"N\\xE3o foi poss\\xEDvel criar um link compartilh\\xE1vel: a cena \\xE9 muito grande\",\n  couldNotLoadInvalidFile: \"N\\xE3o foi poss\\xEDvel carregar o arquivo inv\\xE1lido\",\n  importBackendFailed: \"A importa\\xE7\\xE3o do servidor falhou.\",\n  cannotExportEmptyCanvas: \"N\\xE3o \\xE9 poss\\xEDvel exportar um canvas vazio.\",\n  couldNotCopyToClipboard: \"N\\xE3o foi poss\\xEDvel copiar para a \\xE1rea de transfer\\xEAncia.\",\n  decryptFailed: \"N\\xE3o foi poss\\xEDvel descriptografar os dados.\",\n  uploadedSecurly: \"O upload foi protegido com criptografia de ponta a ponta, o que significa que o servidor do Excalidraw e terceiros n\\xE3o podem ler o conte\\xFAdo.\",\n  loadSceneOverridePrompt: \"Carregar um desenho externo substituir\\xE1 o seu conte\\xFAdo existente. Deseja continuar?\",\n  collabStopOverridePrompt: \"Ao interromper a sess\\xE3o, voc\\xEA substituir\\xE1 seu desenho anterior, armazenado localmente. Voc\\xEA tem certeza?\\n\\n(Se voc\\xEA deseja manter seu desenho local, simplesmente feche a aba do navegador.)\",\n  errorAddingToLibrary: \"N\\xE3o foi poss\\xEDvel adicionar o item \\xE0 biblioteca\",\n  errorRemovingFromLibrary: \"N\\xE3o foi poss\\xEDvel remover o item da biblioteca\",\n  confirmAddLibrary: \"Isso adicionar\\xE1 {{numShapes}} forma(s) \\xE0 sua biblioteca. Tem certeza?\",\n  imageDoesNotContainScene: \"Esta imagem parece n\\xE3o conter dados de cenas. Voc\\xEA ativou a incorpora\\xE7\\xE3o da cena durante a exporta\\xE7\\xE3o?\",\n  cannotRestoreFromImage: \"N\\xE3o foi poss\\xEDvel restaurar a cena deste arquivo de imagem\",\n  invalidSceneUrl: \"N\\xE3o foi poss\\xEDvel importar a cena da URL fornecida. Ela est\\xE1 incompleta ou n\\xE3o cont\\xE9m dados JSON v\\xE1lidos do Excalidraw.\",\n  resetLibrary: \"Isto limpar\\xE1 a sua biblioteca. Voc\\xEA tem certeza?\",\n  removeItemsFromsLibrary: \"Excluir {{count}} item(ns) da biblioteca?\",\n  invalidEncryptionKey: \"A chave de encripta\\xE7\\xE3o deve ter 22 caracteres. A colabora\\xE7\\xE3o ao vivo est\\xE1 desabilitada.\",\n  collabOfflineWarning: \"Sem conex\\xE3o com a internet dispon\\xEDvel.\\nSuas altera\\xE7\\xF5es n\\xE3o ser\\xE3o salvas!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipo de arquivo n\\xE3o suportado.\",\n  imageInsertError: \"N\\xE3o foi poss\\xEDvel inserir imagem. Tente novamente mais tarde...\",\n  fileTooBig: \"O arquivo \\xE9 muito grande. O tamanho m\\xE1ximo permitido \\xE9 {{maxSize}}.\",\n  svgImageInsertError: \"N\\xE3o foi poss\\xEDvel inserir a imagem SVG. A marca\\xE7\\xE3o SVG parece inv\\xE1lida.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG Inv\\xE1lido.\",\n  cannotResolveCollabServer: \"N\\xE3o foi poss\\xEDvel conectar-se ao servidor colaborativo. Por favor, recarregue a p\\xE1gina e tente novamente.\",\n  importLibraryError: \"N\\xE3o foi poss\\xEDvel carregar a biblioteca\",\n  collabSaveFailed: \"N\\xE3o foi poss\\xEDvel salvar no banco de dados do servidor. Se os problemas persistirem, salve o arquivo localmente para garantir que n\\xE3o perca o seu trabalho.\",\n  collabSaveFailed_sizeExceeded: \"N\\xE3o foi poss\\xEDvel salvar no banco de dados do servidor, a tela parece ser muito grande. Se os problemas persistirem, salve o arquivo localmente para garantir que n\\xE3o perca o seu trabalho.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"Parece que voc\\xEA est\\xE1 usando o navegador Brave com a configura\\xE7\\xE3o <bold>Bloquear Impress\\xF5es Digitais</bold> no modo agressivo.\",\n    line2: \"Isso pode acabar quebrando <bold>Elementos de Texto</bold> em seus desenhos.\",\n    line3: \"Recomendamos fortemente desativar essa configura\\xE7\\xE3o. Voc\\xEA pode acessar o <link>passo a passo</link> sobre como fazer isso.\",\n    line4: \"Se desativar essa configura\\xE7\\xE3o n\\xE3o corrigir a exibi\\xE7\\xE3o de elementos de texto, por favor abra uma <issueLink>issue</issueLink> em nosso GitHub, ou mande uma mensagem em nosso <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Sele\\xE7\\xE3o\",\n  image: \"Inserir imagem\",\n  rectangle: \"Ret\\xE2ngulo\",\n  diamond: \"Losango\",\n  ellipse: \"Elipse\",\n  arrow: \"Flecha\",\n  line: \"Linha\",\n  freedraw: \"Desenhar\",\n  text: \"Texto\",\n  library: \"Biblioteca\",\n  lock: \"Manter ativa a ferramenta selecionada ap\\xF3s desenhar\",\n  penMode: \"Modo caneta \\u2014 impede o toque\",\n  link: \"Adicionar/Atualizar link para uma forma selecionada\",\n  eraser: \"Borracha\",\n  frame: \"Ferramenta de quadro\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"M\\xE3o (ferramenta de rolagem)\",\n  extraTools: \"Mais ferramentas\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"A\\xE7\\xF5es da tela\",\n  selectedShapeActions: \"A\\xE7\\xF5es das formas selecionadas\",\n  shapes: \"Formas\"\n};\nvar hints = {\n  canvasPanning: \"Para mover a tela, segure a roda do mouse ou a barra de espa\\xE7o enquanto arrasta ou use a ferramenta de m\\xE3o\",\n  linearElement: \"Clique para iniciar v\\xE1rios pontos, arraste para uma \\xFAnica linha\",\n  freeDraw: \"Toque e arraste, solte quando terminar\",\n  text: \"Dica: voc\\xEA tamb\\xE9m pode adicionar texto clicando duas vezes em qualquer lugar com a ferramenta de sele\\xE7\\xE3o\",\n  embeddable: \"\",\n  text_selected: \"Clique duplo ou tecle ENTER para editar o texto\",\n  text_editing: \"Pressione Esc ou Ctrl/Cmd+ENTER para encerrar a edi\\xE7\\xE3o\",\n  linearElementMulti: \"Clique no \\xFAltimo ponto ou pressione Escape ou Enter para terminar\",\n  lockAngle: \"Voc\\xEA pode restringir o \\xE2ngulo segurando o SHIFT\",\n  resize: \"Voc\\xEA pode restringir propor\\xE7\\xF5es segurando SHIFT enquanto redimensiona,\\nsegure ALT para redimensionar do centro\",\n  resizeImage: \"Voc\\xEA pode redimensionar livremente segurando SHIFT,\\nsegure ALT para redimensionar a partir do centro\",\n  rotate: \"Voc\\xEA pode restringir os \\xE2ngulos segurando SHIFT enquanto gira\",\n  lineEditor_info: \"Pressione CtrlOuCmd e duplo-clique ou pressione CtrlOuCmd + Enter para editar pontos\",\n  lineEditor_pointSelected: \"Pressione Delete para remover o(s) ponto(s),\\nCtrl/Cmd+D para duplicar ou arraste para mover\",\n  lineEditor_nothingSelected: \"Selecione um ponto para editar (segure SHIFT para selecionar v\\xE1rios) ou segure Alt e clique para adicionar novos pontos\",\n  placeImage: \"Clique para colocar a imagem, ou clique e arraste para definir manualmente o seu tamanho\",\n  publishLibrary: \"Publicar sua pr\\xF3pria biblioteca\",\n  bindTextToElement: \"Pressione Enter para adicionar o texto\",\n  deepBoxSelect: \"Segure Ctrl/Cmd para sele\\xE7\\xE3o profunda e para evitar arrastar\",\n  eraserRevert: \"Segure a tecla Alt para inverter os elementos marcados para exclus\\xE3o\",\n  firefox_clipboard_write: 'Esse recurso pode ser ativado configurando a op\\xE7\\xE3o \"dom.events.asyncClipboard.clipboardItem\" como \"true\". Para alterar os sinalizadores do navegador no Firefox, visite a p\\xE1gina \"about:config\".',\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"N\\xE3o \\xE9 poss\\xEDvel mostrar pr\\xE9-visualiza\\xE7\\xE3o\",\n  canvasTooBig: \"A tela pode ser muito grande.\",\n  canvasTooBigTip: \"Dica: tente aproximar um pouco os elementos mais distantes.\"\n};\nvar errorSplash = {\n  headingMain: \"Foi encontrado um erro. Tente <button>recarregar a p\\xE1gina.</button>\",\n  clearCanvasMessage: \"Se recarregar a p\\xE1gina n\\xE3o funcionar, tente <button>limpando a tela.</button>\",\n  clearCanvasCaveat: \" Isso resultar\\xE1 em perda de trabalho \",\n  trackedToSentry: \"O erro com o identificador {{eventId}} foi rastreado no nosso sistema.\",\n  openIssueMessage: \"Fomos muito cautelosos para n\\xE3o incluir suas informa\\xE7\\xF5es de cena no erro. Se sua cena n\\xE3o for privada, por favor, considere seguir nosso <button>rastreador de bugs.</button> Por favor, inclua informa\\xE7\\xF5es abaixo, copiando e colando para a issue do GitHub.\",\n  sceneContent: \"Conte\\xFAdo da cena:\"\n};\nvar roomDialog = {\n  desc_intro: \"Voc\\xEA pode convidar pessoas para sua cena atual para colaborar com voc\\xEA.\",\n  desc_privacy: \"N\\xE3o se preocupe, a sess\\xE3o usa criptografia de ponta a ponta; portanto, o que voc\\xEA desenhar permanecer\\xE1 privado. Nem mesmo nosso servidor poder\\xE1 ver o que voc\\xEA cria.\",\n  button_startSession: \"Iniciar sess\\xE3o\",\n  button_stopSession: \"Parar sess\\xE3o\",\n  desc_inProgressIntro: \"A sess\\xE3o de colabora\\xE7\\xE3o ao vivo est\\xE1 agora em andamento.\",\n  desc_shareLink: \"Compartilhe este link com qualquer pessoa com quem voc\\xEA queira colaborar:\",\n  desc_exitSession: \"Interrompendo a sess\\xE3o voc\\xEA ir\\xE1 se desconectar da sala, mas voc\\xEA poder\\xE1 continuar trabalhando com a cena localmente. Observe que isso n\\xE3o afetar\\xE1 outras pessoas, e elas ainda poder\\xE3o colaborar em suas vers\\xF5es.\",\n  shareTitle: \"Participe de uma sess\\xE3o ao vivo de colabora\\xE7\\xE3o no Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Erro\"\n};\nvar exportDialog = {\n  disk_title: \"Salvar no computador\",\n  disk_details: \"Exportar os dados da cena para um arquivo que voc\\xEA poder\\xE1 importar mais tarde.\",\n  disk_button: \"Salvar em um arquivo\",\n  link_title: \"Link compartilh\\xE1vel\",\n  link_details: \"Exportar como link de apenas leitura.\",\n  link_button: \"Exportar link\",\n  excalidrawplus_description: \"Salvar a cena na sua \\xE1rea de trabalho Excalidraw+.\",\n  excalidrawplus_button: \"Exportar\",\n  excalidrawplus_exportError: \"N\\xE3o \\xE9 poss\\xEDvel exportar para o Excalidraw+ neste momento...\"\n};\nvar helpDialog = {\n  blog: \"Leia o nosso blog\",\n  click: \"clicar\",\n  deepSelect: \"Sele\\xE7\\xE3o profunda\",\n  deepBoxSelect: \"Use a sele\\xE7\\xE3o profunda dentro da caixa para previnir arrastar\",\n  curvedArrow: \"Seta curva\",\n  curvedLine: \"Linha curva\",\n  documentation: \"Documenta\\xE7\\xE3o\",\n  doubleClick: \"clique duplo\",\n  drag: \"arrastar\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Editar linha/ponta da seta\",\n  editText: \"Editar texto / adicionar etiqueta\",\n  github: \"Encontrou algum problema? Nos informe\",\n  howto: \"Siga nossos guias\",\n  or: \"ou\",\n  preventBinding: \"Evitar fixa\\xE7\\xE3o de seta\",\n  tools: \"Ferramentas\",\n  shortcuts: \"Atalhos de teclado\",\n  textFinish: \"Encerrar edi\\xE7\\xE3o (editor de texto)\",\n  textNewLine: \"Adicionar nova linha (editor de texto)\",\n  title: \"Ajudar\",\n  view: \"Visualizar\",\n  zoomToFit: \"Ampliar para encaixar todos os elementos\",\n  zoomToSelection: \"Ampliar a sele\\xE7\\xE3o\",\n  toggleElementLock: \"Bloquear/desbloquear sele\\xE7\\xE3o\",\n  movePageUpDown: \"Mover a p\\xE1gina para cima/baixo\",\n  movePageLeftRight: \"Mover a p\\xE1gina para esquerda/direita\"\n};\nvar clearCanvasDialog = {\n  title: \"Limpar a tela\"\n};\nvar publishDialog = {\n  title: \"Publicar biblioteca\",\n  itemName: \"Nome do item\",\n  authorName: \"Nome do autor\",\n  githubUsername: \"Nome de usu\\xE1rio do GitHub\",\n  twitterUsername: \"Nome de usu\\xE1rio do Twitter\",\n  libraryName: \"Nome da Biblioteca\",\n  libraryDesc: \"Descri\\xE7\\xE3o da biblioteca\",\n  website: \"Site\",\n  placeholder: {\n    authorName: \"Seu nome ou nome de usu\\xE1rio\",\n    libraryName: \"Nome da sua biblioteca\",\n    libraryDesc: \"Descri\\xE7\\xE3o para ajudar as pessoas a entenderem o uso da sua da sua biblioteca\",\n    githubHandle: \"Identificador do GitHub (opcional), para que voc\\xEA possa editar a biblioteca depois de enviar para revis\\xE3o\",\n    twitterHandle: \"Nome de usu\\xE1rio do Twitter (opcional), para que saibamos quem deve ser creditado se promovermos no Twitter\",\n    website: \"Link para o seu site pessoal ou outro lugar (opcional)\"\n  },\n  errors: {\n    required: \"Obrigat\\xF3rio\",\n    website: \"Informe uma URL v\\xE1lida\"\n  },\n  noteDescription: \"Envie sua biblioteca para ser inclu\\xEDda no <link>reposit\\xF3rio de biblioteca p\\xFAblica</link>para outras pessoas usarem em seus desenhos.\",\n  noteGuidelines: \"A biblioteca precisa ser aprovada manualmente primeiro. Por favor leia o <link>orienta\\xE7\\xF5es</link> antes de enviar. Voc\\xEA precisar\\xE1 de uma conta do GitHub para se comunicar e fazer altera\\xE7\\xF5es quando solicitado, mas n\\xE3o \\xE9 estritamente necess\\xE1rio.\",\n  noteLicense: \"Ao enviar, voc\\xEA concorda que a biblioteca ser\\xE1 publicada sob a <link>Licen\\xE7a MIT, </link>o que, em suma, significa que qualquer pessoa pode utiliz\\xE1-los sem restri\\xE7\\xF5es.\",\n  noteItems: \"Cada item da biblioteca deve ter seu pr\\xF3prio nome para que seja filtr\\xE1vel. Os seguintes itens da biblioteca ser\\xE3o inclu\\xEDdos:\",\n  atleastOneLibItem: \"Por favor, selecione pelo menos um item da biblioteca para come\\xE7ar\",\n  republishWarning: \"Nota: alguns dos itens selecionados est\\xE3o marcados como j\\xE1 publicado/enviado. Voc\\xEA s\\xF3 deve reenviar itens ao atualizar uma biblioteca existente ou submiss\\xE3o.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteca enviada\",\n  content: \"Obrigado {{authorName}}. Sua biblioteca foi enviada para an\\xE1lise. Voc\\xEA pode acompanhar o status<link>aqui</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Redefinir biblioteca\",\n  removeItemsFromLib: \"Remover itens selecionados da biblioteca\"\n};\nvar imageExportDialog = {\n  header: \"Exportar imagem\",\n  label: {\n    withBackground: \"Fundo\",\n    onlySelected: \"Somente selecionados\",\n    darkMode: \"Modo escuro\",\n    embedScene: \"Incorporar cena\",\n    scale: \"Escala\",\n    padding: \"Margem interna\"\n  },\n  tooltip: {\n    embedScene: \"Os dados da cena ser\\xE3o salvos no arquivo PNG/SVG exportado para que a cena possa ser restaurada a partir dele.\\nIsso aumentar\\xE1 o tamanho do arquivo exportado.\"\n  },\n  title: {\n    exportToPng: \"Exportar como PNG\",\n    exportToSvg: \"Exportar como SVG\",\n    copyPngToClipboard: \"Copiar PNG para \\xE1rea de transfer\\xEAncia\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copiar para a \\xE1rea de transfer\\xEAncia\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Seus desenhos s\\xE3o criptografados de ponta a ponta, ent\\xE3o os servidores do Excalidraw nunca os ver\\xE3o.\",\n  link: \"Publica\\xE7\\xE3o de blog com criptografia de ponta a ponta no Excalidraw\"\n};\nvar stats = {\n  angle: \"\\xC2ngulo\",\n  element: \"Elemento\",\n  elements: \"Elementos\",\n  height: \"Altura\",\n  scene: \"Cena\",\n  selected: \"Selecionado\",\n  storage: \"Armazenamento\",\n  title: \"Estat\\xEDsticas para nerds\",\n  total: \"Total\",\n  version: \"Vers\\xE3o\",\n  versionCopy: \"Clique para copiar\",\n  versionNotAvailable: \"Vers\\xE3o n\\xE3o dispon\\xEDvel\",\n  width: \"Largura\"\n};\nvar toast = {\n  addedToLibrary: \"Adicionado \\xE0 biblioteca\",\n  copyStyles: \"Estilos copiados.\",\n  copyToClipboard: \"Copiado para \\xE1rea de transfer\\xEAncia.\",\n  copyToClipboardAsPng: \"{{exportSelection}} copiado para a \\xE1rea de transfer\\xEAncia como PNG ({{exportColorScheme}})\",\n  fileSaved: \"Arquivo salvo.\",\n  fileSavedToFilename: \"Salvo em {filename}\",\n  canvas: \"tela\",\n  selection: \"sele\\xE7\\xE3o\",\n  pasteAsSingleElement: \"Use {{shortcut}} para colar como um \\xFAnico elemento,\\nou cole em um editor de texto j\\xE1 existente\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transparente\",\n  black: \"Preto\",\n  white: \"Branco\",\n  red: \"Vermelho\",\n  pink: \"Rosa\",\n  grape: \"Uva\",\n  violet: \"Violeta\",\n  gray: \"Cinza\",\n  blue: \"Azul\",\n  cyan: \"Ciano\",\n  teal: \"Verde-azulado\",\n  green: \"Verde\",\n  yellow: \"Amarelo\",\n  orange: \"Laranja\",\n  bronze: \"Bronze\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Todos os dados s\\xE3o salvos localmente no seu navegador.\",\n    center_heading_plus: \"Voc\\xEA queria ir para o Excalidraw+ em vez disso?\",\n    menuHint: \"Exportar, prefer\\xEAncias, idiomas...\"\n  },\n  defaults: {\n    menuHint: \"Exportar, prefer\\xEAncias e mais...\",\n    center_heading: \"Diagramas, Feito. Simples.\",\n    toolbarHint: \"Escolha uma ferramenta e comece a desenhar!\",\n    helpHint: \"Atalhos e ajuda\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Cores personalizadas mais usadas\",\n  colors: \"Cores\",\n  shades: \"Tons\",\n  hexCode: \"C\\xF3digo hexadecimal\",\n  noShades: \"Sem tons dispon\\xEDveis para essa cor\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportar como imagem\",\n      button: \"Exportar como imagem\",\n      description: \"Exportar os dados da cena para um arquivo que voc\\xEA poder\\xE1 importar mais tarde.\"\n    },\n    saveToDisk: {\n      title: \"Salvar no computador\",\n      button: \"Salvar no computador\",\n      description: \"Exportar os dados da cena para um arquivo que voc\\xEA poder\\xE1 importar mais tarde.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Exportar para Excalidraw+\",\n      description: \"Salvar a cena na sua \\xE1rea de trabalho Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Carregar de arquivo\",\n      button: \"Carregar de arquivo\",\n      description: \"Carregar de um arquivo ir\\xE1 <bold> substituir o conte\\xFAdo existente</bold>.<br></br>Voc\\xEA pode salvar seu desenho primeiro usando uma das op\\xE7\\xF5es abaixo.\"\n    },\n    shareableLink: {\n      title: \"Carregar de um link\",\n      button: \"Substituir meu conte\\xFAdo\",\n      description: \"Carregar um desenho externo ir\\xE1 <bold> substituir seu conte\\xFAdo existente</bold>.<br></br>Voc\\xEA pode salvar seu desenho antes utilizando uma das op\\xE7\\xF5es abaixo.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar pt_BR_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=pt-BR-APOPYZJ7.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/pt-BR-APOPYZJ7.js\n"));

/***/ })

}]);