"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_kab-KAB-2S7ZURK7_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/kab-KAB-2S7ZURK7.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/kab-KAB-2S7ZURK7.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ kab_KAB_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/kab-KAB.json\nvar labels = {\n  paste: \"Sen\\u1E6De\\u1E0D\",\n  pasteAsPlaintext: \"\",\n  pasteCharts: \"Sen\\u1E6De\\u1E0D udlifen\",\n  selectAll: \"Fren akk\",\n  multiSelect: \"Rnu aferdis \\u0263er tefrayt\",\n  moveCanvas: \"Smutti ta\\u0263zut n usune\\u0263\",\n  cut: \"Gzem\",\n  copy: \"N\\u0263el\",\n  copyAsPng: \"N\\u0263el \\u0263er tecfawit am PNG\",\n  copyAsSvg: \"N\\u0263el \\u0263er tecfawit am SVG\",\n  copyText: \"N\\u0263el \\u0263er tecfawit am u\\u1E0Dris\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Awi \\u0263er sdat\",\n  sendToBack: \"Awi s agilal\",\n  bringToFront: \"Err \\u0263er deffir\",\n  sendBackward: \"Awi \\u0263er deffir\",\n  delete: \"Kkes\",\n  copyStyles: \"N\\u0263el i\\u0263unab\",\n  pasteStyles: \"Sen\\u1E6De\\u1E0D i\\u0263unab\",\n  stroke: \"Azizdew\",\n  background: \"Agilal\",\n  fill: \"Ta\\u010D\\u010Dart\",\n  strokeWidth: \"Tehri n yizirig\",\n  strokeStyle: \"A\\u0263anib n tizirig\",\n  strokeStyle_solid: \"A\\u010D\\u010Duran\",\n  strokeStyle_dashed: \"S tjerri\\u1E0Din\",\n  strokeStyle_dotted: \"S tenqi\\u1E0Din\",\n  sloppiness: \"Astehzi\",\n  opacity: \"Ti\\u1E0Dullest\",\n  textAlign: \"Areyyec n u\\u1E0Dris\",\n  edges: \"Leryuf\",\n  sharp: \"Yemsed\",\n  round: \"Imdewer\",\n  arrowheads: \"Ixfawen n tenccabt\",\n  arrowhead_none: \"Ulac\",\n  arrowhead_arrow: \"Taneccabt\",\n  arrowhead_bar: \"Afeggag\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"Akerdis\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Tiddi n tsefsit\",\n  fontFamily: \"Tawacult n tsefsiyin\",\n  addWatermark: 'Seddu \"Yettwaxdem s Excalidraw\"',\n  handDrawn: \"Asune\\u0263 s ufus\",\n  normal: \"Amagnu\",\n  code: \"Tangalt\",\n  small: \"Me\\u1E93\\u1E93i\",\n  medium: \"Alemmas\",\n  large: \"Ameqran\",\n  veryLarge: \"Meqqer a\\u1E6Das\",\n  solid: \"A\\u010D\\u010Duran\",\n  hachure: \"Azerreg\",\n  zigzag: \"\",\n  crossHatch: \"Azerreg anmidag\",\n  thin: \"Arqaq\",\n  bold: \"Azuran\",\n  left: \"Azelma\\u1E0D\",\n  center: \"Talemmast\",\n  right: \"Ayfus\",\n  extraBold: \"Azuran a\\u1E6Das\",\n  architect: \"Amasdag\",\n  artist: \"Ana\\u1E93ur\",\n  cartoonist: \"Amef\\u0263ul\",\n  fileTitle: \"Isem n ufaylu\",\n  colorPicker: \"Amafran n yini\",\n  canvasColors: \"Yettwaseqdec di te\\u0263zut n usune\\u0263\",\n  canvasBackground: \"Agilal n te\\u0263zut n usune\\u0263\",\n  drawingCanvas: \"Ta\\u0263zut n usune\\u0263\",\n  layers: \"Tissiyin\",\n  actions: \"Tigawin\",\n  language: \"Tutlayt\",\n  liveCollaboration: \"Am\\u025Biwen s srid...\",\n  duplicateSelection: \"Sisleg\",\n  untitled: \"War azwel\",\n  name: \"Isem\",\n  yourName: \"Isem-ik (im)\",\n  madeWithExcalidraw: \"Yettwaxdem s Excalidraw\",\n  group: \"Segrew tafrayt\",\n  ungroup: \"Kkess asegrew i tefrayt\",\n  collaborators: \"Im\\u025Biwnen\",\n  showGrid: \"Beqqe\\u1E0D aferrug\",\n  addToLibrary: \"Rnu \\u0263er temkar\\u1E0Dit\",\n  removeFromLibrary: \"Kkes si temkar\\u1E0Dit\",\n  libraryLoadingMessage: \"Asali n temkar\\u1E0Dit\\u2026\",\n  libraries: \"Snirem timkar\\u1E0Diyin\",\n  loadingScene: \"Asali n usayes\\u2026\",\n  align: \"Reyyec\",\n  alignTop: \"Areyyec uksawen\",\n  alignBottom: \"Areyyec ukessar\",\n  alignLeft: \"Reyyec s azelma\\u1E0D\",\n  alignRight: \"Areyyec s ayfus\",\n  centerVertically: \"Di tlemmast s ibeddi\",\n  centerHorizontally: \"Di tlemmast s uglawi\",\n  distributeHorizontally: \"Freq s uglawi\",\n  distributeVertically: \"Freq s yibeddi\",\n  flipHorizontal: \"Tuttya taglawant\",\n  flipVertical: \"Tuttya tubdidt\",\n  viewMode: \"Askar n tmu\\u0263li\",\n  share: \"B\\u1E0Du\",\n  showStroke: \"Beqqe\\u1E0D amelqa\\u1E0D n yini n yizirig\",\n  showBackground: \"Beqqe\\u1E0D amelqa\\u1E0D n yini n ugilal\",\n  toggleTheme: \"Snifel asentel\",\n  personalLib: \"Tamkar\\u1E0Dit tudmawant\",\n  excalidrawLib: \"Tamkar\\u1E0Dit n Excalidraw\",\n  decreaseFontSize: \"Senqes tiddi n tsefsit\",\n  increaseFontSize: \"Sali tiddi n tsefsit\",\n  unbindText: \"Serre\\u1E25 iwe\\u1E0Dris\",\n  bindText: \"Arez a\\u1E0Dris s anagbar\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"\\u1E92reg ase\\u0263wen\",\n    editEmbed: \"\",\n    create: \"Snulfu-d ase\\u0263wen\",\n    createEmbed: \"\",\n    label: \"Ase\\u0263wen\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\\u1E92reg izirig\",\n    exit: \"Ffe\\u0263 seg uma\\u1E93rag n yizirig\"\n  },\n  elementLock: {\n    lock: \"Sekke\\u1E5B\",\n    unlock: \"Serre\\u1E25\",\n    lockAll: \"Sekke\\u1E5B akk\",\n    unlockAll: \"Serre\\u1E25 akk\"\n  },\n  statusPublished: \"Yeffe\\u0263-d\",\n  sidebarLock: \"E\\u01E7\\u01E7 afeggag n yidis yeldi\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"Ulac iferdisen yettwarnan yakan...\",\n  hint_emptyLibrary: \"Fren aferdis di te\\u0263zut nusune\\u0263 akken at-ternu\\u1E0D dagi, ne\\u0263 sbedd tamkar\\u1E0Dit seg usarsay azayez, ukessar-agi.\",\n  hint_emptyPrivateLibrary: \"Fren aferdis di te\\u0263zut nusune\\u0263 akken at-ternu\\u1E0D dagi.\"\n};\nvar buttons = {\n  clearReset: \"Ales awennez n te\\u0263zut n usune\\u0263\",\n  exportJSON: \"Sife\\u1E0D afaylu\",\n  exportImage: \"Sife\\u1E0D tugna...\",\n  export: \"Sekles di...\",\n  copyToClipboard: \"N\\u0263el \\u0263er tecfawit\",\n  save: \"Sekles deg ufaylu amiran\",\n  saveAs: \"Sekles am\",\n  load: \"Ldi\",\n  getShareableLink: \"Awi-d ase\\u0263wen n be\\u1E6D\\u1E6Du\",\n  close: \"Mdel\",\n  selectLanguage: \"Fren tutlayt\",\n  scrollBackToContent: \"U\\u0263al s agbur\",\n  zoomIn: \"Sim\\u0263ur\",\n  zoomOut: \"Sim\\u1E93i\",\n  resetZoom: \"Ales awennez n usem\\u0263er\",\n  menu: \"Umu\\u0263\",\n  done: \"Ifukk\",\n  edit: \"\\u1E92reg\",\n  undo: \"Sefsex\",\n  redo: \"Err-d\",\n  resetLibrary: \"Ales awennez n temkar\\u1E0Dit\",\n  createNewRoom: \"Snulfu-d taxxamt tamaynutt\",\n  fullScreen: \"Agdil a\\u010D\\u010Duran\",\n  darkMode: \"Askar imsulles\",\n  lightMode: \"Askar afaw\",\n  zenMode: \"Askar Zen\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"Ffe\\u0263 seg uskar Zen\",\n  cancel: \"Sefsex\",\n  clear: \"Sfe\\u1E0D\",\n  remove: \"Kkes\",\n  embed: \"\",\n  publishLibrary: \"\\u1E92reg\",\n  submit: \"Azen\",\n  confirm: \"Sentem\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"Ayagi ad isfe\\u1E0D akk ta\\u0263zut n usune\\u0263. Tet\\u1E25eqqe\\u1E0D?\",\n  couldNotCreateShareableLink: \"D awez\\u0263i asnulfu n use\\u0263wen n be\\u1E6D\\u1E6Du.\",\n  couldNotCreateShareableLinkTooBig: \"D awez\\u0263i asnulfu n use\\u0263wen n be\\u1E6D\\u1E6Du. Asayes \\u0263ezzif a\\u1E6Das\",\n  couldNotLoadInvalidFile: \"D awez\\u0263i asali n ufaylu arme\\u0263tu\",\n  importBackendFailed: \"Takter\\u1E0D seg u\\u0263awas n deffir ur teddi ara.\",\n  cannotExportEmptyCanvas: \"D awez\\u0263i asife\\u1E0D n te\\u0263zut n usune\\u0263 tilemt.\",\n  couldNotCopyToClipboard: \"Ulamek an\\u0263al \\u0263er tecfawit.\",\n  decryptFailed: \"D awez\\u0263i tukksa n uwgelhen i yisefka.\",\n  uploadedSecurly: \"Asili yettwas\\u0263elles s uwgelhen ixef s ixef, ayagi yeb\\u0263a ad d-yini belli aqeddac n Excalidraw akked medden ur zmiren ara ad \\u0263ren agbur.\",\n  loadSceneOverridePrompt: \"Asali n wunu\\u0263 uffi\\u0263 ad isemselsi agbur-inek (m) yellan. Teb\\u0263i\\u1E0D ad tkemmele\\u1E0D?\",\n  collabStopOverridePrompt: \"A\\u1E25bas n t\\u0263imit ad yesefsex unu\\u0263-inek (m) yettwa\\u1E25erzen yakan s wudem adigan. Tet\\u1E25eqqe\\u1E0D?\\n(Ma teb\\u0263i\\u1E0D ad te\\u01E7\\u01E7e\\u1E0D unu\\u0263-inek (m) adigan, mdel iccer n yiminig, deg um\\u1E0Diq.)\",\n  errorAddingToLibrary: \"Ulamek ara yettwarnu uferdis \\u0263er temkar\\u1E0Dit\",\n  errorRemovingFromLibrary: \"Ulamek ara yettwakkes uferdis si temkar\\u1E0Dit\",\n  confirmAddLibrary: \"Ayagi adirnu tal\\u0263a (win) {{numShapes}} \\u0263er temkar\\u1E0Dit-inek (m). Tet\\u1E25eqqe\\u1E0D?\",\n  imageDoesNotContainScene: \"Tugna-agi tettban-d ur tes\\u025Bi ara isefka n usayes. Tesremde\\u1E0D aseddu n usayes deg usife\\u1E0D?\",\n  cannotRestoreFromImage: \"Asayes ulamek ara d-yettwarr seg ufaylu-agi n tugna\",\n  invalidSceneUrl: \"Ulamek taktert n usayes seg URL i d-ittunefken. Ahat ma\\u010D\\u010Di d tame\\u0263tut ne\\u0263 ur tegbir ara isefka JSON n Excalidraw.\",\n  resetLibrary: \"Ayagi ad isfe\\u1E0D tamkar\\u1E0Dit-inek\\u2022m. Tet\\u1E25eqqe\\u1E0D?\",\n  removeItemsFromsLibrary: \"Ad tekkse\\u1E0D {{count}} n uferdis (en) si temkar\\u1E0Dit?\",\n  invalidEncryptionKey: \"Tasarut n uwgelhen isefk ad tes\\u025Bu 22 n yiekkilen. Am\\u025Biwen srid yensa.\",\n  collabOfflineWarning: \"Ulac tuqqna n internet.\\nIbedilen-ik ur ttwaklasen ara!\"\n};\nvar errors = {\n  unsupportedFileType: \"Anaw n ufaylu ur yettwasefrak ara.\",\n  imageInsertError: \"D awez\\u0263i tugra n tugna. E\\u025Bre\\u1E0D tikkelt-nni\\u1E0Den ardeqqal...\",\n  fileTooBig: \"Afaylu meqqer a\\u1E6Das. Tiddi tafellayt yurgen d {{maxSize}}.\",\n  svgImageInsertError: \"D awez\\u0263i tugra n tugna SVG. Acra\\u1E0D SVG yettban-d d arme\\u0263tu.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"SVG arme\\u0263tu.\",\n  cannotResolveCollabServer: \"Ulamek tuqqna s aqeddac n umyalel. Ma ulac u\\u0263ilif ales asali n usebter sakin e\\u025Bre\\u1E0D tikkelt-nni\\u1E0Den.\",\n  importLibraryError: \"Ur d-ssalay ara tamkar\\u1E0Dit\",\n  collabSaveFailed: \"Ulamek asekles deg uzadur n yisefka deg ugilal. Ma ikemmel wugur, isefk ad teskelse\\u1E0D afaylu s wudem adigan akken ad tet\\u1E25eqqe\\u1E0D ur tesru\\u1E25uye\\u1E0D ara amahil-inek\\u2022inem.\",\n  collabSaveFailed_sizeExceeded: \"Ulamek asekles deg uzadur n yisefka deg ugilal, ta\\u0263zut n usune\\u0263 tettban-d temqer a\\u1E6Das. Isefk ad teskelse\\u1E0D afaylu s wudem adigan akken ad tet\\u1E25eqqe\\u1E0D ur tesru\\u1E25uye\\u1E0D ara amahil-inek\\u2022inem.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"Ayagi yezmer ad d-iglu s tru\\u1E93i n<bold>Iferdisen n u\\u1E0Dris</bold>deg wunu\\u0263en-inek.\",\n    line3: \"Ad k-nsemter ad tsexsi\\u1E0D a\\u0263ewwar-agi. Tzemre\\u1E0D ad t\\u1E0Defre\\u1E0D<link>isurifen-agi</link> \\u0263ef wamek ara txedme\\u1E0D.\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Tafrayt\",\n  image: \"Ger tugna\",\n  rectangle: \"Asrem\",\n  diamond: \"Ame\\u0263\\u1E5Bun\",\n  ellipse: \"Taglayt\",\n  arrow: \"Taneccabt\",\n  line: \"Izirig\",\n  freedraw: \"Sune\\u0263\",\n  text: \"A\\u1E0Dris\",\n  library: \"Tamkar\\u1E0Dit\",\n  lock: \"E\\u01E7\\u01E7 afecku n tefrayt yermed mba\\u025Bd asune\\u0263\",\n  penMode: \"Askar n yimru - gdel tanalit\",\n  link: \"Rnu/leqqem ase\\u0263wen i tal\\u0263a yettwafernen\",\n  eraser: \"Sfe\\u1E0D\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"Afus (afecku n usmutti n tmu\\u0263li)\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Tigawin n te\\u0263zut n usune\\u0263\",\n  selectedShapeActions: \"Tigawin n tal\\u0263a yettwafernen\",\n  shapes: \"Tal\\u0263iwin\"\n};\nvar hints = {\n  canvasPanning: \"Akken ad tesmutti\\u1E0D ta\\u0263zut n usune\\u0263, \\u1E6D\\u1E6Def \\u1E5B\\u1E5Buda n umumed, ne\\u0263 seqdec afecku Afus\",\n  linearElement: \"Ssit akken ad tebdu\\u1E0D a\\u1E6Das n tenqi\\u1E0Din, zu\\u0263er i yiwen n yizirig\",\n  freeDraw: \"Ssit yerna zu\\u0263er, serre\\u1E25 ticki tfuke\\u1E0D\",\n  text: \"Tixidest: tzemre\\u1E0D da\\u0263en ad ternu\\u1E0D a\\u1E0Dris s usiti snat n tikkal anida teb\\u0263i\\u1E0D s ufecku n tefrayt\",\n  embeddable: \"\",\n  text_selected: \"Ssit snat n tikkal ne\\u0263 ssed taqeffalt Kcem akken ad t\\u1E93erge\\u1E0D a\\u1E0Dris\",\n  text_editing: \"Ssit Escape ne\\u0263 CtrlOrCmd+ENTER akken ad tfakke\\u1E0D asi\\u1E93reg\",\n  linearElementMulti: \"Ssit \\u0263ef tenqi\\u1E0Dt taneggarut ne\\u0263 ssed taqeffalt Escape ne\\u0263 taqeffalt Kcem akken ad tfakke\\u1E0D\",\n  lockAngle: \"Tzemre\\u1E0D ad t\\u1E25ettme\\u1E0D ti\\u0263mert s tu\\u1E6D\\u1E6Dfa n tqeffalt SHIFT\",\n  resize: \"Tzemre\\u1E0D ad t\\u1E25etteme\\u1E0D assa\\u0263 s tu\\u1E6D\\u1E6Dfa n tqeffalt SHIFT mi ara tettbeddile\\u1E0D tiddi,\\nma te\\u1E6D\\u1E6Dfe\\u1E0D ALT abeddel n tiddi ad yili si tlemmast\",\n  resizeImage: \"Tzemre\\u1E0D ad talse\\u1E0D tiddi s tilelli s tu\\u1E6D\\u1E6Dfa n SHIFT,\\n\\u1E6D\\u1E6Def ALT akken ad talse\\u1E0D tiddi si tlemmast\",\n  rotate: \"Tzemre\\u1E0D ad t\\u1E25etteme\\u1E0D ti\\u0263emmar s tu\\u1E6D\\u1E6Dfa n SHIFT di tuzzya\",\n  lineEditor_info: \"Ssed \\u0263ef CtrlOrCmd yerna ssit snat n tikkal ne\\u0263 ssed \\u0263ef CtrlOrCmd + Kcem akken ad t\\u1E93erge\\u1E0D tineqqi\\u1E0Din\",\n  lineEditor_pointSelected: \"Ssed taqeffalt kkes akken ad tekkse\\u1E0D tanqi\\u1E0D (tinqi\\u1E0Din),\\nCtrlOrCmd+D akken ad tsiselge\\u1E0D, ne\\u0263 zu\\u0263er akken ad tesmutti\\u1E0D\",\n  lineEditor_nothingSelected: \"Fren tanqi\\u1E0Dt akken ad t\\u1E93erge\\u1E0D (\\u1E6D\\u1E6Def SHIFT akken ad tferne\\u1E0D a\\u1E6Das),\\nne\\u0263 \\u1E6D\\u1E6Def Alt akken ad ternu\\u1E0D tinqi\\u1E0Din timaynutin\",\n  placeImage: \"Ssit akken ad tserse\\u1E0D tugna, ne\\u0263 ssit u zu\\u0263er akken ad tesbadu\\u1E0D tiddi-ines s ufus\",\n  publishLibrary: \"Si\\u1E93reg tamkar\\u1E0Dit-inek\\u2022inem\",\n  bindTextToElement: \"Ssed \\u0263ef kcem akken ad ternu\\u1E0D a\\u1E0Dris\",\n  deepBoxSelect: \"\\u1E6C\\u1E6Def CtrlOrCmd akken ad tferne\\u1E0D s telqey, yerna ad trewle\\u1E0D i uzu\\u0263er\",\n  eraserRevert: \"Ssed Alt akken ad tsefsxe\\u1E0D iferdisen yettwacer\\u1E0Den i tukksa\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Ulamek abeqqe\\u1E0D n teskant\",\n  canvasTooBig: \"Ta\\u0263zut n usune\\u0263 tezmer ad tili temeqqer a\\u1E6Das.\",\n  canvasTooBigTip: \"Tixidest: e\\u025Bre\\u1E0D ad tesqerbe\\u1E0D ci\\u1E6D iferdisen yemba\\u025Baden.\"\n};\nvar errorSplash = {\n  headingMain: \"Te\\u1E0Dra-d tucc\\u1E0Da. E\\u025Bre\\u1E0D <button>asali n usebter tikkelt-nni\\u1E0Den.</button>\",\n  clearCanvasMessage: \"Ma yella tulsa n usali ur tefri ara ugur, e\\u025Bre\\u1E0D <button>asfa\\u1E0D n te\\u0263zut n usune\\u0263.</button>\",\n  clearCanvasCaveat: \" Ayagi ad d-iglu s us\\u1E5Bu\\u1E25u n umahil \",\n  trackedToSentry: \"Tucc\\u1E0Da akked umesmagi {{eventId}} tettwasekles deg unagraw-nne\\u0263.\",\n  openIssueMessage: \"N\\u1E25uder a\\u1E6Das akken ur nseddu ara tal\\u0263ut n usayes-inek (m) di tucc\\u1E0Da. Ma yella asayes-inek (m) ma\\u010D\\u010Di d ama\\u1E93lay, ttxil-k (m) xemmem ad \\u1E0Defre\\u1E0D <button>afecku n we\\u1E0Dfar n yibugen.</button> Ma ulac u\\u0263ilif seddu tal\\u0263ut ukessar-agi s wen\\u0263al akked usen\\u1E6De\\u1E0D di GitHub issue.\",\n  sceneContent: \"Agbur n usayes:\"\n};\nvar roomDialog = {\n  desc_intro: \"Tzemre\\u1E0D ad d-te\\u025Ber\\u1E0De\\u1E0D medden \\u0263er usayes-inek (m) amiran akken ad ttekkin yid-k.\",\n  desc_privacy: \"Ur tqelliq ara, ti\\u0263imit tsseqdac awgelhen ixef s ixef, d\\u0263a ayen ara tsun\\u0263e\\u1E0D ad iqqim d ama\\u1E93lay. Ula d aqeddac-nne\\u0263 ur yezmir ara ad iwali acu txeddeme\\u1E0D.\",\n  button_startSession: \"Bdu ti\\u0263imit\",\n  button_stopSession: \"\\u1E24bes ti\\u0263imit\",\n  desc_inProgressIntro: \"Ti\\u0263imit n um\\u025Bawen s srid tetteddu akka tura.\",\n  desc_shareLink: \"B\\u1E0Du ase\\u0263wen-agi akked medden ukud teb\\u0263i\\u1E0D ad tem\\u025Bawane\\u1E0D:\",\n  desc_exitSession: \"A\\u1E25bas n t\\u0263imit ad k (m) yesenser si texxamt, maca ad tizmire\\u1E0D ad tkemmele\\u1E0D amahil s usayes, s wudem adigan. \\u1E92er belli ayagi ur yett\\u1E25az ara imdanen-nni\\u1E0Den, yerna ad izmiren ad kemmelen ad m\\u025Bawanen di tsuffe\\u0263t-nnsen.\",\n  shareTitle: \"Rnu \\u0263er t\\u0263imit n um\\u025Biwen s srid n Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Tucc\\u1E0Da\"\n};\nvar exportDialog = {\n  disk_title: \"Sekles deg u\\u1E0Debsi\",\n  disk_details: \"Sekles isefka n usayes deg ufaylu ansi ara tizmire\\u1E0D ad d-tketre\\u1E0D are\\u1E0Dqal.\",\n  disk_button: \"Sekles deg ufaylu\",\n  link_title: \"Ase\\u0263wen n be\\u1E6D\\u1E6Du\",\n  link_details: \"Sife\\u1E0D am use\\u0263wen n t\\u0263uri kan.\",\n  link_button: \"Sife\\u1E0D deg use\\u0263wen\",\n  excalidrawplus_description: \"Sekles asayes-inek\\u2022inem di tallunt n umahil Excalidraw+.\",\n  excalidrawplus_button: \"Sife\\u1E0D\",\n  excalidrawplus_exportError: \"Ulamek asife\\u1E0D \\u0263er Excalidraw+ akka tura...\"\n};\nvar helpDialog = {\n  blog: \"\\u0194e\\u1E5B ablug-nne\\u0263\",\n  click: \"ssit\",\n  deepSelect: \"Afran s telqey\",\n  deepBoxSelect: \"Afran s telqey s tnaka, yerna ad tyrewle\\u1E0D i uzu\\u0263er\",\n  curvedArrow: \"Taneccabt izelgen\",\n  curvedLine: \"Izirig izelgen\",\n  documentation: \"Tasemlit\",\n  doubleClick: \"ssit snat n tikkal\",\n  drag: \"zu\\u0263er\",\n  editor: \"Ama\\u1E93rag\",\n  editLineArrowPoints: \"\\u1E92reg tinqi\\u1E0Din n yizirig/taneccabt\",\n  editText: \"\\u1E92reg a\\u1E0Dris/rnu tabzimt\",\n  github: \"Tufi\\u1E0D-d ugur? Azen-a\\u0263-d\",\n  howto: \"\\u1E0Cfer imniren-nne\\u0263\",\n  or: \"ne\\u0263\",\n  preventBinding: \"Se\\u1E25bes tuqqna n tneccabin\",\n  tools: \"Ifecka\",\n  shortcuts: \"Inegzumen n unasiw\",\n  textFinish: \"Fak asi\\u1E93reg (ama\\u1E93rag n u\\u1E0Dris)\",\n  textNewLine: \"Rnu ajerri\\u1E0D amaynut (ama\\u1E93rag n u\\u1E0Dris)\",\n  title: \"Tallelt\",\n  view: \"Tamu\\u0263li\",\n  zoomToFit: \"Sim\\u0263ur akken ad twli\\u1E0D akk iferdisen\",\n  zoomToSelection: \"Sim\\u0263ur \\u0263er tefrayt\",\n  toggleElementLock: \"Sekke\\u1E5B/kkes asekker i tefrayt\",\n  movePageUpDown: \"Smutti asebter d asawen/akessar\",\n  movePageLeftRight: \"Smutti asebter s azelma\\u1E0D/ayfus\"\n};\nvar clearCanvasDialog = {\n  title: \"Sfe\\u1E0D ta\\u0263zut n usune\\u0263\"\n};\nvar publishDialog = {\n  title: \"Suffe\\u0263-d tamkar\\u1E0Dit\",\n  itemName: \"Isem n uferdis\",\n  authorName: \"Isem n umeskar\",\n  githubUsername: \"Isem n useqdac n GitHub\",\n  twitterUsername: \"Isem n useqdac n Twitter\",\n  libraryName: \"Isem n temkar\\u1E0Dit\",\n  libraryDesc: \"Aglam n temkar\\u1E0Dit\",\n  website: \"Asmel n web\",\n  placeholder: {\n    authorName: \"Isem ne\\u0263 isem n useqdac inek\\u2022inem\",\n    libraryName: \"Isem n temkar\\u1E0Dit-inek\\u2022inem\",\n    libraryDesc: \"Aglam n temkar\\u1E0Dit-inek\\u2022inem akken ad t\\u025Biwne\\u1E0D medden ad fehmen aseqdec-inec\",\n    githubHandle: \"Isem n useqdac n GitHub ( d anefrunan) akken ad tizmire\\u1E0D ad tis\\u1E93rige\\u1E0D tamkar\\u1E0Dit ticki tuzne\\u1E0D-tt i uselken\",\n    twitterHandle: \"Isem n useqdac n Twitter (d anefrunan) akken ad n\\u1E93er anwa ara nsenmer deg udellel di Twitter\",\n    website: \"Ase\\u0263wen \\u0263er usmel-inek\\u2022inem ne\\u0263 waye\\u1E0D (d anefrunan)\"\n  },\n  errors: {\n    required: \"Yettwasra\",\n    website: \"Sekcem URL ame\\u0263tu\"\n  },\n  noteDescription: \"Azen tamkar\\u1E0Dit-inek\\u2022inem akken ad teddu di <link>akaram azayez n temkar\\u1E0Dit</link>i yimdanen-nni\\u1E0Den ara isqedcen deg wunu\\u0263en-nnsen.\",\n  noteGuidelines: \"Tamkar\\u1E0Dit te\\u1E25wa\\u01E7 ad tettwaqbel s ufus qbel. Ma ulac u\\u0263ilif \\u0263er <link>iwellihen</link> send ad tazne\\u1E0D. Tesri\\u1E0D ami\\u1E0Dan n GitHub akken ad tmmeslaye\\u1E0D yerna ad tge\\u1E0D ibeddilen ma yelaq, maca ma\\u010D\\u010Di d ayen yettwa\\u1E25etmen.\",\n  noteLicense: \"Mi tuzne\\u1E0D ad tqeble\\u1E0D akken tamkar\\u1E0Dit ad d-teffe\\u0263 s <link>Turagt MIT, </link>ayen yeb\\u0263an ad d-yini belli yal yiwen izmer ad ten-iseqdec war tilist.\",\n  noteItems: \"Yal aferdis n temkar\\u1E0Dit isefk ad is\\u025Bu isem-is i yiman-is akken ad yili wamek ara yettusizdeg. Iferdisen-agi n temkar\\u1E0Dit ad ddun:\",\n  atleastOneLibItem: \"Ma ulac u\\u0263ilif fern ma drus yiwen n uferdis n temkar\\u1E0Dit akken ad tebdu\\u1E0D\",\n  republishWarning: \"Tamawt: kra n yiferdisen yettwafernen ttwacer\\u1E0Den ffe\\u0263en-d/ttwaznen. Isefk ad talse\\u1E0D tuzzna n yiferdisen anagar mi ara tleqqeme\\u1E0D tamkar\\u1E0Dit ne\\u0263 tuzzna yellan.\"\n};\nvar publishSuccessDialog = {\n  title: \"Tamkar\\u1E0Dit tettwazen\",\n  content: \"Tanemmirt-ik\\u2022im {{authorName}}. Tamkar\\u1E0Dit-inek\\u2022inem tettwazen i weselken. Tzemre\\u1E0D ad t\\u1E0Defre\\u1E0D a\\u1E93ayer<link>dagi</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Ales awennez n temkar\\u1E0Dit\",\n  removeItemsFromLib: \"Kkes iferdisen yettafernen si temkar\\u1E0Dit\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Unu\\u0263en-inek (m) ttuwgelhnen seg yixef s ixef d\\u0263a iqeddacen n Excalidraw wer\\u01E7in ad ten-walin. \",\n  link: \"Amagrad \\u0263ef uwgelhen ixef s ixef di Excalidraw\"\n};\nvar stats = {\n  angle: \"Ti\\u0263me\\u1E5Bt\",\n  element: \"Aferdis\",\n  elements: \"Iferdisen\",\n  height: \"Tattayt\",\n  scene: \"Asayes\",\n  selected: \"Yettwafren\",\n  storage: \"A\\u1E25raz\",\n  title: \"\",\n  total: \"A\\u0263rud\",\n  version: \"Alqem\",\n  versionCopy: \"Sit ad tne\\u0263le\\u1E0D\",\n  versionNotAvailable: \"Ur inu\\u1E25 ulqem\",\n  width: \"Tehri\"\n};\nvar toast = {\n  addedToLibrary: \"Yettwarna \\u0263er temkar\\u1E0Dit\",\n  copyStyles: \"I\\u0263unab yettwane\\u0263len.\",\n  copyToClipboard: \"Yettwa\\u0263el \\u0263er tecfawit.\",\n  copyToClipboardAsPng: \"{{exportSelection}} yettwan\\u0263el \\u0263er tecfawit am PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Afaylu yettwasekles.\",\n  fileSavedToFilename: \"Yettwasekles di {filename}\",\n  canvas: \"ta\\u0263zut n usune\\u0263\",\n  selection: \"tafrayt\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Afrawan\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Akk isefka-inek\\u2022inem ttwakelsen s wudem adigan deg yiminig-inek\\u2022inem.\",\n    center_heading_plus: \"Teb\\u0263i\\u1E0D ad teddu\\u1E0D \\u0263er Excalidraw+ deg um\\u1E0Diq?\",\n    menuHint: \"Asife\\u1E0D, ismenyifen, tutlayin, ...\"\n  },\n  defaults: {\n    menuHint: \"Asife\\u1E0D, ismenyifen, d wayen-nni\\u1E0Den...\",\n    center_heading: \"\",\n    toolbarHint: \"Fren afecku tebdu\\u1E0D asune\\u0263!\",\n    helpHint: \"Inegzumen akked tallelt\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar kab_KAB_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=kab-KAB-2S7ZURK7.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/kab-KAB-2S7ZURK7.js\n"));

/***/ })

}]);