"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_tr-TR-7QYBXDBO_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/tr-TR-7QYBXDBO.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/tr-TR-7QYBXDBO.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ tr_TR_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/tr-TR.json\nvar labels = {\n  paste: \"Yap\\u0131\\u015Ft\\u0131r\",\n  pasteAsPlaintext: \"D\\xFCz metin olarak yap\\u0131\\u015Ft\\u0131r\",\n  pasteCharts: \"Grafikleri yap\\u0131\\u015Ft\\u0131r\",\n  selectAll: \"T\\xFCm\\xFCn\\xFC se\\xE7\",\n  multiSelect: \"Se\\xE7ime \\xF6ge ekle\",\n  moveCanvas: \"Tuvali ta\\u015F\\u0131\",\n  cut: \"Kes\",\n  copy: \"Kopyala\",\n  copyAsPng: \"Panoya PNG olarak kopyala\",\n  copyAsSvg: \"Panoya SVG olarak kopyala\",\n  copyText: \"Panoya metin olarak kopyala\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"Bir \\xF6ne getir\",\n  sendToBack: \"Arkaya g\\xF6nder\",\n  bringToFront: \"En \\xF6ne getir\",\n  sendBackward: \"Bir geriye g\\xF6nder\",\n  delete: \"Sil\",\n  copyStyles: \"Stilleri kopyala\",\n  pasteStyles: \"Stilleri yap\\u0131\\u015Ft\\u0131r\",\n  stroke: \"Vurgu\",\n  background: \"Arka plan\",\n  fill: \"Doldur\",\n  strokeWidth: \"Kontur geni\\u015Fli\\u011Fi\",\n  strokeStyle: \"Kontur stili\",\n  strokeStyle_solid: \"Dolu\",\n  strokeStyle_dashed: \"Kesik \\xE7izgili\",\n  strokeStyle_dotted: \"Noktal\\u0131\",\n  sloppiness: \"\\xDCst\\xFCn k\\xF6r\\xFCl\\xFCk\",\n  opacity: \"Opakl\\u0131k\",\n  textAlign: \"Metin hizala\",\n  edges: \"Kenarlar\",\n  sharp: \"Keskin\",\n  round: \"Yuvarlak\",\n  arrowheads: \"Ok u\\xE7lar\\u0131\",\n  arrowhead_none: \"Yok\",\n  arrowhead_arrow: \"Ok\",\n  arrowhead_bar: \"\\xC7izgi\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"\\xDC\\xE7gen\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"Yaz\\u0131 tipi boyutu\",\n  fontFamily: \"Yaz\\u0131 tipi ailesi\",\n  addWatermark: '\"Excalidraw ile yap\\u0131ld\\u0131\" yaz\\u0131s\\u0131n\\u0131 ekle',\n  handDrawn: \"El-yaz\\u0131s\\u0131\",\n  normal: \"Normal\",\n  code: \"Kod\",\n  small: \"K\\xFC\\xE7\\xFCk\",\n  medium: \"Orta\",\n  large: \"B\\xFCy\\xFCk\",\n  veryLarge: \"\\xC7ok geni\\u015F\",\n  solid: \"Dolu\",\n  hachure: \"Taral\\u0131\",\n  zigzag: \"Zikzak\",\n  crossHatch: \"\\xC7apraz-taral\\u0131\",\n  thin: \"\\u0130nce\",\n  bold: \"Kal\\u0131n\",\n  left: \"Sol\",\n  center: \"Ortala\",\n  right: \"Sa\\u011F\",\n  extraBold: \"Ekstra kal\\u0131n\",\n  architect: \"Mimar\",\n  artist: \"Sanat\\xE7\\u0131\",\n  cartoonist: \"Karikat\\xFCrist\",\n  fileTitle: \"Dosya ad\\u0131\",\n  colorPicker: \"Renk se\\xE7ici\",\n  canvasColors: \"Tuvalin \\xFCzerinde kullan\\u0131ld\\u0131\",\n  canvasBackground: \"Tuval arka plan\\u0131\",\n  drawingCanvas: \"\\xC7izim tuvali\",\n  layers: \"Katmanlar\",\n  actions: \"Eylemler\",\n  language: \"Dil\",\n  liveCollaboration: \"Canl\\u0131 ortak \\xE7al\\u0131\\u015Fma alan\\u0131...\",\n  duplicateSelection: \"\\xC7o\\u011Falt\",\n  untitled: \"Ads\\u0131z\",\n  name: \"\\u0130sim\",\n  yourName: \"\\u0130sminiz\",\n  madeWithExcalidraw: \"Excalidraw ile yap\\u0131ld\\u0131\",\n  group: \"Se\\xE7imi grup yap\",\n  ungroup: \"Se\\xE7ilen grubu da\\u011F\\u0131t\",\n  collaborators: \"Ortaklar\",\n  showGrid: \"Izgaray\\u0131 g\\xF6ster\",\n  addToLibrary: \"K\\xFCt\\xFCphaneye ekle\",\n  removeFromLibrary: \"K\\xFCt\\xFCphaneden kald\\u0131r\",\n  libraryLoadingMessage: \"K\\xFCt\\xFCphane y\\xFCkleniyor\\u2026\",\n  libraries: \"K\\xFCt\\xFCphanelere g\\xF6zat\",\n  loadingScene: \"Sahne y\\xFCkleniyor\\u2026\",\n  align: \"Hizala\",\n  alignTop: \"Yukar\\u0131 hizala\",\n  alignBottom: \"A\\u015Fa\\u011F\\u0131 hizala\",\n  alignLeft: \"Sola hizala\",\n  alignRight: \"Sa\\u011Fa hizala\",\n  centerVertically: \"Dikeyde ortala\",\n  centerHorizontally: \"Yatayda ortala\",\n  distributeHorizontally: \"Yatay da\\u011F\\u0131t\",\n  distributeVertically: \"Dikey da\\u011F\\u0131t\",\n  flipHorizontal: \"Yatay d\\xF6nd\\xFCr\",\n  flipVertical: \"Dikey d\\xF6nd\\xFCr\",\n  viewMode: \"G\\xF6r\\xFCn\\xFCm modu\",\n  share: \"Payla\\u015F\",\n  showStroke: \"Kontur i\\xE7in renk se\\xE7iciyi g\\xF6ster\",\n  showBackground: \"Arkaplan i\\xE7in renk se\\xE7iciyi g\\xF6ster\",\n  toggleTheme: \"Temay\\u0131 etkinle\\u015Ftir/devre d\\u0131\\u015F\\u0131 b\\u0131rak\",\n  personalLib: \"Ki\\u015Fisel Kitapl\\u0131k\",\n  excalidrawLib: \"Excalidraw Kitapl\\u0131\\u011F\\u0131\",\n  decreaseFontSize: \"Yaz\\u0131 Tipi Boyutunu K\\xFC\\xE7\\xFClt\",\n  increaseFontSize: \"Yaz\\u0131 Tipi Boyutunu B\\xFCy\\xFClt\",\n  unbindText: \"Metni \\xE7\\xF6z\",\n  bindText: \"Metni ta\\u015F\\u0131y\\u0131c\\u0131ya ba\\u011Fla\",\n  createContainerFromText: \"Metni bile\\u015Fen i\\xE7inde sar\",\n  link: {\n    edit: \"Ba\\u011Flant\\u0131y\\u0131 d\\xFCzenle\",\n    editEmbed: \"Ba\\u011Flant\\u0131y\\u0131 d\\xFCzenle & yerle\\u015Ftir\",\n    create: \"Ba\\u011Flant\\u0131 olu\\u015Ftur\",\n    createEmbed: \"Ba\\u011Flant\\u0131 olu\\u015Ftur & yerle\\u015Ftir\",\n    label: \"Ba\\u011Flant\\u0131\",\n    labelEmbed: \"Ba\\u011Flant\\u0131 & yerle\\u015Ftirme\",\n    empty: \"Herhangi bir ba\\u011Flant\\u0131 olu\\u015Fturulmad\\u0131\"\n  },\n  lineEditor: {\n    edit: \"\\xC7izgiyi d\\xFCzenle\",\n    exit: \"\\xC7izgi d\\xFCzenlemeden \\xE7\\u0131k\"\n  },\n  elementLock: {\n    lock: \"Kilitle\",\n    unlock: \"Kilidi Kald\\u0131r\",\n    lockAll: \"Hepsini kilitle\",\n    unlockAll: \"Hepsinin kilidini kald\\u0131r\"\n  },\n  statusPublished: \"Yay\\u0131nland\\u0131\",\n  sidebarLock: \"Kenar \\xE7ubu\\u011Fu a\\xE7\\u0131k kals\\u0131n\",\n  selectAllElementsInFrame: \"\\xC7er\\xE7evedeki t\\xFCm bile\\u015Fenleri se\\xE7\",\n  removeAllElementsFromFrame: \"\\xC7er\\xE7evedeki t\\xFCm bile\\u015Fenleri sil\",\n  eyeDropper: \"Tuvalden renk se\\xE7\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\\xD6\\u011Fe eklenmedi...\",\n  hint_emptyLibrary: \"\\xD6\\u011Felerden birini eklemek i\\xE7in \\xF6\\u011Feyi se\\xE7iniz veya a\\u015Fa\\u011F\\u0131daki genel k\\xFCt\\xFCphaneden \\xF6\\u011Feleri ekleyin.\",\n  hint_emptyPrivateLibrary: \"Tuvalden bir eleman se\\xE7erek sayfaya ekleyin.\"\n};\nvar buttons = {\n  clearReset: \"Tuvali s\\u0131f\\u0131rla\",\n  exportJSON: \"Dosyaya aktar\",\n  exportImage: \"Resimleri d\\u0131\\u015Fa aktar...\",\n  export: \"\\u015Euraya kaydet...\",\n  copyToClipboard: \"Panoya kopyala\",\n  save: \"Ge\\xE7erli dosyaya kaydet\",\n  saveAs: \"Farkl\\u0131 kaydet\",\n  load: \"A\\xE7\",\n  getShareableLink: \"Payla\\u015F\\u0131labilir ba\\u011Flant\\u0131 al\",\n  close: \"Kapat\",\n  selectLanguage: \"Dil se\\xE7in\",\n  scrollBackToContent: \"\\u0130\\xE7eri\\u011Fe geri d\\xF6n\",\n  zoomIn: \"Yak\\u0131nla\\u015Ft\\u0131r\",\n  zoomOut: \"Uzakla\\u015Ft\\u0131r\",\n  resetZoom: \"Yak\\u0131nla\\u015Ft\\u0131rmay\\u0131 s\\u0131f\\u0131rla\",\n  menu: \"Men\\xFC\",\n  done: \"Tamam\",\n  edit: \"D\\xFCzenle\",\n  undo: \"Geri Al\",\n  redo: \"Yeniden yap\",\n  resetLibrary: \"K\\xFCt\\xFCphaneyi s\\u0131f\\u0131rla\",\n  createNewRoom: \"Yeni oda olu\\u015Ftur\",\n  fullScreen: \"Tam ekran\",\n  darkMode: \"Koyu tema\",\n  lightMode: \"A\\xE7\\u0131k tema\",\n  zenMode: \"Zen modu\",\n  objectsSnapMode: \"Nesnelere hizala\",\n  exitZenMode: \"Zen modundan \\xE7\\u0131k\",\n  cancel: \"\\u0130ptal\",\n  clear: \"Temizle\",\n  remove: \"Kald\\u0131r\",\n  embed: \"\",\n  publishLibrary: \"Yay\\u0131nla\",\n  submit: \"G\\xF6nder\",\n  confirm: \"Onayla\",\n  embeddableInteractionButton: \"Etkile\\u015Fime girmek i\\xE7in t\\u0131kla\"\n};\nvar alerts = {\n  clearReset: \"Tuvalin tamam\\u0131 temizlenecek. Emin misiniz?\",\n  couldNotCreateShareableLink: \"Payla\\u015F\\u0131labilir ba\\u011Flant\\u0131 olu\\u015Fturulamad\\u0131.\",\n  couldNotCreateShareableLinkTooBig: \"Payla\\u015F\\u0131labilir ba\\u011Flant\\u0131 olu\\u015Fturulamad\\u0131: sahne \\xE7ok b\\xFCy\\xFCk\",\n  couldNotLoadInvalidFile: \"Bilinmeyen dosya y\\xFCklenemiyor\",\n  importBackendFailed: \"Sunucudan i\\xE7e aktarma ba\\u015Far\\u0131s\\u0131z.\",\n  cannotExportEmptyCanvas: \"Bo\\u015F tuval d\\u0131\\u015Far\\u0131ya aktar\\u0131lamaz.\",\n  couldNotCopyToClipboard: \"Panoya kopyalanam\\u0131yor.\",\n  decryptFailed: \"\\u015Eifrelenmi\\u015F veri \\xE7\\xF6z\\xFCmlenemedi.\",\n  uploadedSecurly: \"Y\\xFCkleme u\\xE7tan uca \\u015Fifreleme ile korunmaktad\\u0131r. Excalidraw sunucusu ve \\xFC\\xE7\\xFCnc\\xFCl \\u015Fah\\u0131slar i\\xE7eri\\u011Fi okuyamayacakt\\u0131r.\",\n  loadSceneOverridePrompt: \"Harici \\xE7izimler y\\xFCklemek mevcut olan i\\xE7eri\\u011Fi de\\u011Fi\\u015Ftirecektir. Devam etmek istiyor musunuz?\",\n  collabStopOverridePrompt: \"Oturumu sonland\\u0131rmak daha \\xF6nceki, yerel olarak kaydedilmi\\u015F \\xE7izimin \\xFCzerine kaydedilmesine sebep olacak. Emin misiniz?\\n\\n(Yerel \\xE7iziminizi kaybetmemek i\\xE7in taray\\u0131c\\u0131 sekmesini kapatabilirsiniz.)\",\n  errorAddingToLibrary: \"\\xD6\\u011Fe k\\xFCt\\xFCphaneye eklenemedi\",\n  errorRemovingFromLibrary: \"\\xD6\\u011Fe k\\xFCt\\xFCphaneden silinemedi\",\n  confirmAddLibrary: \"Bu, kitapl\\u0131\\u011F\\u0131n\\u0131za {{numShapes}} tane \\u015Fekil ekleyecek. Emin misiniz?\",\n  imageDoesNotContainScene: \"Bu g\\xF6r\\xFCnt\\xFC herhangi bir sahne verisi i\\xE7ermiyor gibi g\\xF6r\\xFCn\\xFCyor. D\\u0131\\u015Fa aktarma s\\u0131ras\\u0131nda sahne yerle\\u015Ftirmeyi etkinle\\u015Ftirdiniz mi?\",\n  cannotRestoreFromImage: \"Sahne bu resim dosyas\\u0131ndan geri y\\xFCklenemedi\",\n  invalidSceneUrl: \"Verilen ba\\u011Flant\\u0131dan \\xE7al\\u0131\\u015Fma alan\\u0131 y\\xFCklenemedi. Dosya bozuk olabilir veya ge\\xE7erli bir Excalidraw JSON verisi bulundurmuyor olabilir.\",\n  resetLibrary: \"Bu i\\u015Flem k\\xFCt\\xFCphanenizi s\\u0131f\\u0131rlayacak. Emin misiniz?\",\n  removeItemsFromsLibrary: \"{{count}} \\xF6\\u011Fe(ler) kitapl\\u0131ktan kald\\u0131r\\u0131ls\\u0131n m\\u0131?\",\n  invalidEncryptionKey: \"\\u015Eifreleme anahtar\\u0131 22 karakter olmal\\u0131. Canl\\u0131 i\\u015Fbirli\\u011Fi devre d\\u0131\\u015F\\u0131 b\\u0131rak\\u0131ld\\u0131.\",\n  collabOfflineWarning: \"\\u0130nternet ba\\u011Flant\\u0131s\\u0131 bulunamad\\u0131. De\\u011Fi\\u015Fiklikleriniz kaydedilmeyecek!\"\n};\nvar errors = {\n  unsupportedFileType: \"Desteklenmeyen dosya t\\xFCr\\xFC.\",\n  imageInsertError: \"G\\xF6rsel eklenemedi. Daha sonra tekrar deneyin...\",\n  fileTooBig: \"Dosya \\xE7ok b\\xFCy\\xFCk. \\u0130zin verilen maksimum boyut {{maxSize}}.\",\n  svgImageInsertError: \"SVG resmi eklenemedi. SVG i\\u015Faretlemesi ge\\xE7ersiz g\\xF6r\\xFCn\\xFCyor.\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"Ge\\xE7ersiz SVG.\",\n  cannotResolveCollabServer: \"\\u0130\\u015F birli\\u011Fi sunucusuna ba\\u011Flan\\u0131lam\\u0131yor. L\\xFCtfen sayfay\\u0131 yenileyip tekrar deneyin.\",\n  importLibraryError: \"K\\xFCt\\xFCphane y\\xFCklenemedi\",\n  collabSaveFailed: \"Backend veritaban\\u0131na kaydedilemedi. E\\u011Fer problem devam ederse, \\xE7al\\u0131\\u015Fman\\u0131z\\u0131 korumak i\\xE7in dosyay\\u0131 yerel olarak kaydetmelisiniz.\",\n  collabSaveFailed_sizeExceeded: \"Backend veritaban\\u0131na kaydedilemedi; tuval \\xE7ok b\\xFCy\\xFCk. \\xC7al\\u0131\\u015Fman\\u0131z\\u0131 korumak i\\xE7in dosyay\\u0131 yerel olarak kaydetmelisiniz.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"Resimleri k\\xFCt\\xFCphaneye ekleme deste\\u011Fi yak\\u0131nda geliyor!\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"Se\\xE7me\",\n  image: \"G\\xF6rsel ekle\",\n  rectangle: \"Dikd\\xF6rtgen\",\n  diamond: \"Elmas\",\n  ellipse: \"Elips\",\n  arrow: \"Ok\",\n  line: \"\\xC7izgi\",\n  freedraw: \"\\xC7iz\",\n  text: \"Yaz\\u0131\",\n  library: \"K\\xFCt\\xFCphane\",\n  lock: \"Se\\xE7ilen arac\\u0131 \\xE7izimden sonra aktif tut\",\n  penMode: \"Kalem modu - dokunmay\\u0131 engelle\",\n  link: \"Se\\xE7ilen \\u015Fekil i\\xE7in ba\\u011Flant\\u0131 Ekle/G\\xFCncelle\",\n  eraser: \"Silgi\",\n  frame: \"\\xC7er\\xE7eve arac\\u0131\",\n  magicframe: \"\",\n  embeddable: \"Web Yerle\\u015Ftirme\",\n  laser: \"Lazer i\\u015Faret\\xE7isi\",\n  hand: \"\",\n  extraTools: \"Daha fazla ara\\xE7\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"Tuval eylemleri\",\n  selectedShapeActions: \"Se\\xE7ilen \\u015Fekil aksiyonlar\\u0131\",\n  shapes: \"\\u015Eekiller\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"Birden fazla nokta i\\xE7in t\\u0131klay\\u0131n, tek \\xE7izgi i\\xE7in s\\xFCr\\xFCkleyin\",\n  freeDraw: \"T\\u0131kla ve s\\xFCr\\xFCkle, bitirdi\\u011Finde serbest b\\u0131rak\",\n  text: \"\\u0130pucu: se\\xE7me arac\\u0131yla herhangi bir yere \\xE7ift t\\u0131klayarak da yaz\\u0131 ekleyebilirsin\",\n  embeddable: \"Web sitesi yerle\\u015Ftirmek i\\xE7in s\\xFCr\\xFCkle b\\u0131rak\",\n  text_selected: \"Metni d\\xFCzenlemek i\\xE7in \\xE7ift t\\u0131klay\\u0131n veya ENTER'a bas\\u0131n\",\n  text_editing: \"D\\xFCzenlemeyi bitirmek i\\xE7in ESC veya Ctrl/Cmd+ENTER tu\\u015Flar\\u0131na bas\\u0131n\",\n  linearElementMulti: \"Bitirmek i\\xE7in son noktaya t\\u0131klay\\u0131n ya da Escape veya Enter tu\\u015Funa bas\\u0131n\",\n  lockAngle: \"SHIFT tu\\u015Funa bas\\u0131l\\u0131 tutarak a\\xE7\\u0131y\\u0131 koruyabilirsiniz\",\n  resize: \"Yeniden boyutland\\u0131r\\u0131rken SHIFT tu\\u015Funu bas\\u0131l\\u0131 tutarak oranlar\\u0131 s\\u0131n\\u0131rlayabilirsiniz,\\nmerkezden yeniden boyutland\\u0131rmak i\\xE7in ALT tu\\u015Funu bas\\u0131l\\u0131 tutun\",\n  resizeImage: \"SHIFT'e bas\\u0131l\\u0131 tutarak serbest\\xE7e yeniden boyutland\\u0131rabilirsiniz, merkezden yeniden boyutland\\u0131rmak i\\xE7in ALT tu\\u015Funu bas\\u0131l\\u0131 tutun\",\n  rotate: \"D\\xF6nd\\xFCr\\xFCrken SHIFT tu\\u015Funa bas\\u0131l\\u0131 tutarak a\\xE7\\u0131lar\\u0131 koruyabilirsiniz\",\n  lineEditor_info: \"Puanlar\\u0131 d\\xFCzenlemek i\\xE7in ctrl veya cmd tu\\u015Funa bas\\u0131l\\u0131 tutup \\xE7ift t\\u0131klay\\u0131n veya enter tu\\u015Funa bas\\u0131n\",\n  lineEditor_pointSelected: \"Sil tu\\u015Funa basarak noktalar\\u0131 silin,\\nCtrl/Cmd + D ile \\xE7o\\u011Falt\\u0131n, ya da s\\xFCr\\xFCkleyerek ta\\u015F\\u0131y\\u0131n\",\n  lineEditor_nothingSelected: \"D\\xFCzenlemek i\\xE7in bir nokta se\\xE7in (birden fazla se\\xE7mek i\\xE7in SHIFT tu\\u015Funu bas\\u0131l\\u0131 tutun),\\nveya Alt tu\\u015Funu bas\\u0131l\\u0131 tutun ve yeni noktalar eklemek i\\xE7in t\\u0131klay\\u0131n\",\n  placeImage: \"Resmi yerle\\u015Ftirmek i\\xE7in t\\u0131klay\\u0131n ya da boyutunu manuel olarak ayarlamak i\\xE7in t\\u0131klay\\u0131p s\\xFCr\\xFCkleyin\",\n  publishLibrary: \"Kendi kitapl\\u0131\\u011F\\u0131n\\u0131z\\u0131 yay\\u0131nlay\\u0131n\",\n  bindTextToElement: \"Enter tu\\u015Funa basarak metin ekleyin\",\n  deepBoxSelect: \"Ctrl/Cmd tu\\u015Funa bas\\u0131l\\u0131 tutarak derin se\\xE7im yap\\u0131n ya da s\\xFCr\\xFCklemeyi engelleyin\",\n  eraserRevert: \"Alt tu\\u015Funa bas\\u0131l\\u0131 tutarak silinme i\\xE7in i\\u015Faretlenmi\\u015F \\xF6geleri tersine \\xE7evirin\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\\xD6nizleme g\\xF6sterilemiyor\",\n  canvasTooBig: \"Kanvas \\xE7ok b\\xFCy\\xFCk olabilir.\",\n  canvasTooBigTip: \"\\u0130pucu: En uzaktaki elemanlar\\u0131 birbirine yak\\u0131nla\\u015Ft\\u0131rmay\\u0131 deneyin.\"\n};\nvar errorSplash = {\n  headingMain: \"Hata olu\\u015Ftu. L\\xFCtfen <button>sayfay\\u0131 yenilemeyi deneyin.</button>\",\n  clearCanvasMessage: \"Yenileme sonras\\u0131 sorun devam ediyorsa, l\\xFCtfen <button>\\xE7izim alan\\u0131n\\u0131 temizlemeyi deneyin.</button>\",\n  clearCanvasCaveat: \" Bu, yapt\\u0131\\u011F\\u0131n\\u0131z de\\u011Fi\\u015Fiklikleri s\\u0131f\\u0131rlayacak \",\n  trackedToSentry: \"Tan\\u0131mlay\\u0131c\\u0131 ile ilgili hata {{eventId}} sistemimize yakaland\\u0131.\",\n  openIssueMessage: \"Sahne bilginizi hata mesaj\\u0131na yans\\u0131tmamak i\\xE7in olduk\\xE7a dikkatli davrand\\u0131k. E\\u011Fer sahneniz gizli de\\u011Filse hatay\\u0131 l\\xFCtfen \\u015Furadan takip edin <button>hata takibi.</button> L\\xFCtfen a\\u015Fa\\u011F\\u0131ya GitHub sorununa kopyalayarak ve yap\\u0131\\u015Ft\\u0131rarak bilgi ekleyin.\",\n  sceneContent: \"Sahne i\\xE7eri\\u011Fi:\"\n};\nvar roomDialog = {\n  desc_intro: \"\\xC7al\\u0131\\u015Fma alan\\u0131n\\u0131za, sizinle birlikte \\xE7al\\u0131\\u015Fabilmeleri i\\xE7in ba\\u015Fkalar\\u0131n\\u0131 da ekleyebilirsiniz.\",\n  desc_privacy: \"\\xC7al\\u0131\\u015Fma ortam\\u0131nda yapt\\u0131klar\\u0131n\\u0131z ve \\xE7izimleriniz u\\xE7tan uca \\u015Fifrelemeyle saklanmaktad\\u0131r. Sunucular\\u0131m\\u0131z dahi bu verileri \\u015Fifrelenmemi\\u015F haliyle g\\xF6remez.\",\n  button_startSession: \"Oturumu ba\\u015Flat\",\n  button_stopSession: \"Oturumu sonland\\u0131r\",\n  desc_inProgressIntro: \"Ortak \\xE7al\\u0131\\u015Fma ortam\\u0131 olu\\u015Fturuldu.\",\n  desc_shareLink: \"Bu ba\\u011Flant\\u0131y\\u0131 birlikte \\xE7al\\u0131\\u015Faca\\u011F\\u0131n\\u0131z ki\\u015Filerle payla\\u015Fabilirsiniz:\",\n  desc_exitSession: \"\\xC7al\\u0131\\u015Fma ortam\\u0131n\\u0131 kapatt\\u0131\\u011F\\u0131n\\u0131zda ortak \\xE7al\\u0131\\u015Fmadan ayr\\u0131lm\\u0131\\u015F olursunuz ancak kendi versiyonunuzda \\xE7al\\u0131\\u015Fmaya devam edebilirsiniz. Bu durumda ortak \\xE7al\\u0131\\u015Ft\\u0131\\u011F\\u0131n\\u0131z di\\u011Fer ki\\u015Filer etkilenmeyecek, \\xE7al\\u0131\\u015Fma ortam\\u0131ndaki versiyon \\xFCzerinden \\xE7al\\u0131\\u015Fmaya devam edebilecekler.\",\n  shareTitle: \"Excalidraw'da canl\\u0131 ortak cal\\u0131\\u015Fma oturumuna kat\\u0131l\"\n};\nvar errorDialog = {\n  title: \"Hata\"\n};\nvar exportDialog = {\n  disk_title: \"Belle\\u011Fe kaydet\",\n  disk_details: \"Sahne verilerini daha sonra i\\xE7e aktarabilece\\u011Finiz bir dosyaya aktar\\u0131n.\",\n  disk_button: \"Dosyaya kaydet\",\n  link_title: \"Payla\\u015F\\u0131labilir ba\\u011Flant\\u0131\",\n  link_details: \"Salt okunur bir ba\\u011Flant\\u0131 olarak d\\u0131\\u015Fa aktar\\u0131n.\",\n  link_button: \"Ba\\u011Flant\\u0131 olarak d\\u0131\\u015Fa aktar\",\n  excalidrawplus_description: \"Sahneyi Excalidraw+ \\xE7al\\u0131\\u015Fma alan\\u0131n\\u0131za kaydedin.\",\n  excalidrawplus_button: \"D\\u0131\\u015Fa aktar\",\n  excalidrawplus_exportError: \"\\u015Eu anda Excalidraw+'a aktar\\u0131lamad\\u0131...\"\n};\nvar helpDialog = {\n  blog: \"Blog'umuzu okuyun\",\n  click: \"t\\u0131kla\",\n  deepSelect: \"Derin se\\xE7im\",\n  deepBoxSelect: \"Kutu i\\xE7erisinde derin se\\xE7im yap\\u0131n, s\\xFCr\\xFCklemeyi engelleyin\",\n  curvedArrow: \"E\\u011Fri ok\",\n  curvedLine: \"E\\u011Fri \\xE7izgi\",\n  documentation: \"Dok\\xFCmantasyon\",\n  doubleClick: \"\\xE7ift-t\\u0131klama\",\n  drag: \"s\\xFCr\\xFCkle\",\n  editor: \"D\\xFCzenleyici\",\n  editLineArrowPoints: \"\\xC7izgi/ok noktalar\\u0131n\\u0131 d\\xFCzenle\",\n  editText: \"Etiket / metin d\\xFCzenle\",\n  github: \"Bir hata m\\u0131 buldun? Bildir\",\n  howto: \"Rehberlerimizi takip edin\",\n  or: \"veya\",\n  preventBinding: \"Ok ba\\u011Flamay\\u0131 \\xF6nleyin\",\n  tools: \"Ara\\xE7lar\",\n  shortcuts: \"Klavye k\\u0131sayollar\\u0131\",\n  textFinish: \"D\\xFCzenlemeyi bitir (metin d\\xFCzenleyici)\",\n  textNewLine: \"Yeni sat\\u0131r ekle (metin d\\xFCzenleyici)\",\n  title: \"Yard\\u0131m\",\n  view: \"G\\xF6r\\xFCn\\xFCm\",\n  zoomToFit: \"T\\xFCm \\xF6\\u011Feleri s\\u0131\\u011Fd\\u0131rmak i\\xE7in yak\\u0131nla\\u015Ft\\u0131r\",\n  zoomToSelection: \"Se\\xE7ime yak\\u0131nla\\u015F\",\n  toggleElementLock: \"Se\\xE7imi Kilitle/\\xE7\\xF6z\",\n  movePageUpDown: \"Sayfay\\u0131 yukar\\u0131/a\\u015Fa\\u011F\\u0131 kayd\\u0131r\",\n  movePageLeftRight: \"Sayfay\\u0131 sola/sa\\u011Fa kayd\\u0131r\"\n};\nvar clearCanvasDialog = {\n  title: \"Tuvali temizle\"\n};\nvar publishDialog = {\n  title: \"Kitapl\\u0131\\u011F\\u0131 yay\\u0131nla\",\n  itemName: \"\\xD6\\u011Fe ad\\u0131\",\n  authorName: \"Yazar ad\\u0131\",\n  githubUsername: \"G\\u0131tHub kullan\\u0131c\\u0131 ad\\u0131\",\n  twitterUsername: \"Twitter kullan\\u0131c\\u0131 ad\\u0131\",\n  libraryName: \"Kitapl\\u0131k ad\\u0131\",\n  libraryDesc: \"Kitapl\\u0131k a\\xE7\\u0131klamas\\u0131\",\n  website: \"Web sitesi\",\n  placeholder: {\n    authorName: \"Ad\\u0131n\\u0131z ya da kullan\\u0131c\\u0131 ad\\u0131n\\u0131z\",\n    libraryName: \"Kitapl\\u0131\\u011F\\u0131n\\u0131z\\u0131n ad\\u0131\",\n    libraryDesc: \"\\u0130nsanlar\\u0131n kullan\\u0131m\\u0131n\\u0131 anlamas\\u0131na yard\\u0131mc\\u0131 olmak i\\xE7in kitapl\\u0131\\u011F\\u0131n\\u0131z\\u0131n a\\xE7\\u0131klamas\\u0131\",\n    githubHandle: \"Github ba\\u011Flant\\u0131s\\u0131 ( tercihe ba\\u011Fl\\u0131), k\\xFCt\\xFCphane g\\xF6zden ge\\xE7irme i\\xE7in onayland\\u0131\\u011F\\u0131nda d\\xFCzenleyebiliesiniz diye\",\n    twitterHandle: \"Twitter kullan\\u0131c\\u0131 ad\\u0131 ( tercihe ba\\u011Fl\\u0131), bu sayede Twitter \\xFCzerinde payla\\u015F\\u0131ren \\xE7al\\u0131\\u015Fman\\u0131z\\u0131 size atfedebiliriz\",\n    website: \"Ki\\u015Fisel web sayfan\\u0131z\\u0131 ya da ba\\u015Fka bir yeri ba\\u011Flay\\u0131n (tercihe ba\\u011Fl\\u0131)\"\n  },\n  errors: {\n    required: \"Gerekli\",\n    website: \"Ge\\xE7erli bir URL girin\"\n  },\n  noteDescription: \"Submit your library to be included in the <link>genel k\\xFCt\\xFCphane reposu</link>di\\u011Fer insanlar \\xE7izimlerinde kullanabilsin diye.\",\n  noteGuidelines: \"\\xD6nce k\\xFCt\\xFCphane elle onaylanmal\\u0131. \\u015Funu okuyun <link>y\\xF6nergeler</link> onaylamadan \\xF6nce. gerekli olmas\\u0131 halinde ileti\\u015Fim kurmak i\\xE7in ve de\\u011Fi\\u015Fiklik i\\xE7in Github hesab\\u0131 gerekli, ama \\xE7ok da illaki olmal\\u0131 de\\u011Fil.\",\n  noteLicense: \"Bunu onaylayarak, k\\xFCt\\xFC\\u011Fhanenin \\u015Fu lisansla yay\\u0131nlanmas\\u0131n\\u0131 onayl\\u0131yorsunuz <link>MIT Lisans, </link>ki bu k\\u0131saca herkesin onu k\\u0131s\\u0131tlama olmaks\\u0131z\\u0131n kullanabilece\\u011Fi anlam\\u0131na gelmektedir.\",\n  noteItems: \"Her k\\xFCt\\xFCphane kendi ismine sahip olmal\\u0131 ki tarama yapabilelim. \\u015Eu k\\xFCt\\xFCphane \\xF6geleri dahil edilecek:\",\n  atleastOneLibItem: \"L\\xFCtfen ba\\u015Flamak i\\xE7in en az bir tane k\\xFCt\\xFCphane \\xF6gesi se\\xE7in\",\n  republishWarning: \"Not: se\\xE7ilen \\xF6gelerden bir k\\u0131sm\\u0131 zaten yay\\u0131nlanm\\u0131\\u015F/g\\xF6nderilmi\\u015F. Yaln\\u0131zca mevcut k\\xFCt\\xFCphane ve g\\xF6nderileri g\\xFCncellerken yeniden g\\xF6nderme i\\u015Flemi yapmal\\u0131s\\u0131n\\u0131z.\"\n};\nvar publishSuccessDialog = {\n  title: \"K\\xFCt\\xFCphane g\\xF6nderildi\",\n  content: \"Te\\u015Fekk\\xFCrler {{authorName}}. K\\xFCt\\xFCphaneniz g\\xF6zden ge\\xE7irme i\\xE7in al\\u0131nd\\u0131. Durumu takip edebilirsiniz<link>burada</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"K\\xFCt\\xFCphaneyi s\\u0131f\\u0131rla\",\n  removeItemsFromLib: \"Se\\xE7ilen \\xF6geleri k\\xFCt\\xFCphaneden kald\\u0131r\"\n};\nvar imageExportDialog = {\n  header: \"Resmi d\\u0131\\u015Fa aktar\",\n  label: {\n    withBackground: \"Arka plan\",\n    onlySelected: \"Sadece se\\xE7ilen\",\n    darkMode: \"Karanl\\u0131k mod\",\n    embedScene: \"Sahne yerle\\u015Ftir\",\n    scale: \"\\xD6l\\xE7eklendir\",\n    padding: \"D\\u0131\\u015F bo\\u015Fluk\"\n  },\n  tooltip: {\n    embedScene: \"Sahne verisi, sahnenin geri y\\xFCklenebilmesi i\\xE7in d\\u0131\\u015Far\\u0131 aktar\\u0131lan PNG/SVG dosyas\\u0131na kaydedilecektir. Bu, d\\u0131\\u015Fa aktar\\u0131lan dosya boyutunu artt\\u0131racakt\\u0131r.\"\n  },\n  title: {\n    exportToPng: \"PNG olarak d\\u0131\\u015Fa aktar\",\n    exportToSvg: \"SVG olarak d\\u0131\\u015Fa aktar\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Panoya kopyala\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\\xC7izimleriniz u\\xE7tan-uca \\u015Fifrelenmi\\u015Ftir, Excalidraw'\\u0131n sunucular\\u0131 bile onlar\\u0131 g\\xF6remez.\",\n  link: \"Excalidraw'da u\\xE7tan uca \\u015Fifreleme hakk\\u0131nda blog yaz\\u0131s\\u0131\"\n};\nvar stats = {\n  angle: \"A\\xE7\\u0131\",\n  element: \"Bile\\u015Fen\",\n  elements: \"Bile\\u015Fenler\",\n  height: \"Y\\xFCkseklik\",\n  scene: \"Sahne\",\n  selected: \"Se\\xE7ili\",\n  storage: \"Depolama\",\n  title: \"\\u0130nekler i\\xE7in istatistikler\",\n  total: \"Toplam\",\n  version: \"S\\xFCr\\xFCm\",\n  versionCopy: \"Kopyalamak i\\xE7in t\\u0131kla\",\n  versionNotAvailable: \"S\\xFCr\\xFCm mevcut de\\u011Fil\",\n  width: \"Geni\\u015Flik\"\n};\nvar toast = {\n  addedToLibrary: \"K\\xFCt\\xFCphaneye eklendi\",\n  copyStyles: \"Stiller kopyaland\\u0131.\",\n  copyToClipboard: \"Panoya kopyaland\\u0131.\",\n  copyToClipboardAsPng: \"{{exportSelection}} panoya PNG olarak\\n({{exportColorScheme}}) kopyaland\\u0131\",\n  fileSaved: \"Dosya kaydedildi.\",\n  fileSavedToFilename: \"{filename} kaydedildi\",\n  canvas: \"tuval\",\n  selection: \"se\\xE7im\",\n  pasteAsSingleElement: \"Tekil obje olarak yap\\u0131\\u015Ft\\u0131rmak i\\xE7in veya var olan bir metin edit\\xF6r\\xFCne yap\\u0131\\u015Ft\\u0131rmak i\\xE7in {{shortcut}} kullan\\u0131n\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\\u015Eeffaf\",\n  black: \"Siyah\",\n  white: \"Beyaz\",\n  red: \"K\\u0131rm\\u0131z\\u0131\",\n  pink: \"Pembe\",\n  grape: \"Koyu Mor\",\n  violet: \"Menek\\u015Fe rengi\",\n  gray: \"Gri\",\n  blue: \"Mavi\",\n  cyan: \"Camg\\xF6be\\u011Fi\",\n  teal: \"Deniz mavisi\",\n  green: \"Ye\\u015Fil\",\n  yellow: \"Sar\\u0131\",\n  orange: \"Turuncu\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"Ecalidraw+'a m\\u0131 gitmek istediniz?\",\n    menuHint: \"D\\u0131\\u015Fa aktar, se\\xE7enekler, diller, ...\"\n  },\n  defaults: {\n    menuHint: \"D\\u0131\\u015Fa aktar, se\\xE7enekler, ve daha fazlas\\u0131...\",\n    center_heading: \"\",\n    toolbarHint: \"Bir ara\\xE7 se\\xE7in ve \\xE7izime ba\\u015Flay\\u0131n!\",\n    helpHint: \"K\\u0131sayollar & yard\\u0131m\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"En \\xE7ok kullan\\u0131lan \\xF6zel renkler\",\n  colors: \"Renkler\",\n  shades: \"\",\n  hexCode: \"Hex kodu\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"Diske Kaydet\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar tr_TR_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=tr-TR-7QYBXDBO.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/tr-TR-7QYBXDBO.js\n"));

/***/ })

}]);