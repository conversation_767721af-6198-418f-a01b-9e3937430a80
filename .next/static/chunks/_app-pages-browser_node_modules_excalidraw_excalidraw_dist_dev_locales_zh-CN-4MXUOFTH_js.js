"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_zh-CN-4MXUOFTH_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-CN-4MXUOFTH.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-CN-4MXUOFTH.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ zh_CN_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/zh-CN.json\nvar labels = {\n  paste: \"\\u7C98\\u8D34\",\n  pasteAsPlaintext: \"\\u7C98\\u8D34\\u4E3A\\u7EAF\\u6587\\u672C\",\n  pasteCharts: \"\\u7C98\\u8D34\\u56FE\\u8868\",\n  selectAll: \"\\u5168\\u90E8\\u9009\\u4E2D\",\n  multiSelect: \"\\u6DFB\\u52A0\\u5143\\u7D20\\u5230\\u9009\\u533A\",\n  moveCanvas: \"\\u79FB\\u52A8\\u753B\\u5E03\",\n  cut: \"\\u526A\\u5207\",\n  copy: \"\\u62F7\\u8D1D\",\n  copyAsPng: \"\\u590D\\u5236\\u4E3A PNG \\u5230\\u526A\\u8D34\\u677F\",\n  copyAsSvg: \"\\u590D\\u5236\\u4E3A SVG \\u5230\\u526A\\u8D34\\u677F\",\n  copyText: \"\\u590D\\u5236\\u6587\\u672C\\u5230\\u526A\\u8D34\\u677F\",\n  copySource: \"\\u590D\\u5236\\u6E90\\u7801\\u5230\\u526A\\u8D34\\u677F\",\n  convertToCode: \"\\u8F6C\\u6362\\u6210\\u4EE3\\u7801\",\n  bringForward: \"\\u4E0A\\u79FB\\u4E00\\u5C42\",\n  sendToBack: \"\\u7F6E\\u4E8E\\u5E95\\u5C42\",\n  bringToFront: \"\\u7F6E\\u4E8E\\u9876\\u5C42\",\n  sendBackward: \"\\u4E0B\\u79FB\\u4E00\\u5C42\",\n  delete: \"\\u5220\\u9664\",\n  copyStyles: \"\\u62F7\\u8D1D\\u6837\\u5F0F\",\n  pasteStyles: \"\\u7C98\\u8D34\\u6837\\u5F0F\",\n  stroke: \"\\u63CF\\u8FB9\",\n  background: \"\\u80CC\\u666F\",\n  fill: \"\\u586B\\u5145\",\n  strokeWidth: \"\\u63CF\\u8FB9\\u5BBD\\u5EA6\",\n  strokeStyle: \"\\u8FB9\\u6846\\u6837\\u5F0F\",\n  strokeStyle_solid: \"\\u5B9E\\u7EBF\",\n  strokeStyle_dashed: \"\\u865A\\u7EBF\",\n  strokeStyle_dotted: \"\\u70B9\\u865A\\u7EBF\",\n  sloppiness: \"\\u7EBF\\u6761\\u98CE\\u683C\",\n  opacity: \"\\u900F\\u660E\\u5EA6\",\n  textAlign: \"\\u6587\\u672C\\u5BF9\\u9F50\",\n  edges: \"\\u8FB9\\u89D2\",\n  sharp: \"\\u5C16\\u9510\",\n  round: \"\\u5706\\u6DA6\",\n  arrowheads: \"\\u7AEF\\u70B9\",\n  arrowhead_none: \"\\u65E0\",\n  arrowhead_arrow: \"\\u7BAD\\u5934\",\n  arrowhead_bar: \"\\u6761\\u72B6\",\n  arrowhead_circle: \"\\u5706\\u70B9\",\n  arrowhead_circle_outline: \"\\u5706\\u70B9\\uFF08\\u7A7A\\u5FC3\\uFF09\",\n  arrowhead_triangle: \"\\u4E09\\u89D2\\u7BAD\\u5934\",\n  arrowhead_triangle_outline: \"\\u4E09\\u89D2\\u7BAD\\u5934\\uFF08\\u7A7A\\u5FC3\\uFF09\",\n  arrowhead_diamond: \"\\u83F1\\u5F62\",\n  arrowhead_diamond_outline: \"\\u83F1\\u5F62\\uFF08\\u7A7A\\u5FC3\\uFF09\",\n  fontSize: \"\\u5B57\\u4F53\\u5927\\u5C0F\",\n  fontFamily: \"\\u5B57\\u4F53\",\n  addWatermark: \"\\u6DFB\\u52A0 \\u201C\\u4F7F\\u7528 Excalidraw \\u521B\\u5EFA\\u201D \\u6C34\\u5370\",\n  handDrawn: \"\\u624B\\u5199\",\n  normal: \"\\u666E\\u901A\",\n  code: \"\\u4EE3\\u7801\",\n  small: \"\\u5C0F\",\n  medium: \"\\u4E2D\",\n  large: \"\\u5927\",\n  veryLarge: \"\\u52A0\\u5927\",\n  solid: \"\\u5B9E\\u5FC3\",\n  hachure: \"\\u7EBF\\u6761\",\n  zigzag: \"\\u4E4B\\u5B57\\u5F62\\u6298\\u7EBF\",\n  crossHatch: \"\\u4EA4\\u53C9\\u7EBF\\u6761\",\n  thin: \"\\u7EC6\",\n  bold: \"\\u7C97\",\n  left: \"\\u5DE6\\u5BF9\\u9F50\",\n  center: \"\\u5C45\\u4E2D\",\n  right: \"\\u53F3\\u5BF9\\u9F50\",\n  extraBold: \"\\u7279\\u7C97\",\n  architect: \"\\u6734\\u7D20\",\n  artist: \"\\u827A\\u672F\",\n  cartoonist: \"\\u6F2B\\u753B\\u5BB6\",\n  fileTitle: \"\\u6587\\u4EF6\\u540D\",\n  colorPicker: \"\\u53D6\\u8272\\u5668\",\n  canvasColors: \"\\u753B\\u5E03\\u4E0A\\u7684\",\n  canvasBackground: \"\\u753B\\u5E03\\u80CC\\u666F\",\n  drawingCanvas: \"\\u7ED8\\u5236 Canvas\",\n  layers: \"\\u56FE\\u5C42\",\n  actions: \"\\u64CD\\u4F5C\",\n  language: \"\\u8BED\\u8A00\",\n  liveCollaboration: \"\\u5B9E\\u65F6\\u534F\\u4F5C...\",\n  duplicateSelection: \"\\u590D\\u5236\",\n  untitled: \"\\u65E0\\u6807\\u9898\",\n  name: \"\\u540D\\u5B57\",\n  yourName: \"\\u60A8\\u7684\\u59D3\\u540D\",\n  madeWithExcalidraw: \"\\u4F7F\\u7528 Excalidraw \\u521B\\u5EFA\",\n  group: \"\\u7F16\\u7EC4\",\n  ungroup: \"\\u89E3\\u9664\\u7F16\\u7EC4\",\n  collaborators: \"\\u534F\\u4F5C\\u8005\",\n  showGrid: \"\\u663E\\u793A\\u7F51\\u683C\",\n  addToLibrary: \"\\u6DFB\\u52A0\\u5230\\u7D20\\u6750\\u5E93\\u4E2D\",\n  removeFromLibrary: \"\\u4ECE\\u7D20\\u6750\\u5E93\\u4E2D\\u79FB\\u9664\",\n  libraryLoadingMessage: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u7D20\\u6750\\u5E93\\u2026\",\n  libraries: \"\\u6D4F\\u89C8\\u7D20\\u6750\\u5E93\",\n  loadingScene: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u7ED8\\u56FE\\u2026\",\n  align: \"\\u5BF9\\u9F50\",\n  alignTop: \"\\u9876\\u90E8\\u5BF9\\u9F50\",\n  alignBottom: \"\\u5E95\\u7AEF\\u5BF9\\u9F50\",\n  alignLeft: \"\\u5DE6\\u5BF9\\u9F50\",\n  alignRight: \"\\u53F3\\u5BF9\\u9F50\",\n  centerVertically: \"\\u5782\\u76F4\\u5C45\\u4E2D\",\n  centerHorizontally: \"\\u6C34\\u5E73\\u5C45\\u4E2D\",\n  distributeHorizontally: \"\\u6C34\\u5E73\\u7B49\\u8DDD\\u5206\\u5E03\",\n  distributeVertically: \"\\u5782\\u76F4\\u7B49\\u8DDD\\u5206\\u5E03\",\n  flipHorizontal: \"\\u6C34\\u5E73\\u7FFB\\u8F6C\",\n  flipVertical: \"\\u5782\\u76F4\\u7FFB\\u8F6C\",\n  viewMode: \"\\u67E5\\u770B\\u6A21\\u5F0F\",\n  share: \"\\u5206\\u4EAB\",\n  showStroke: \"\\u663E\\u793A\\u63CF\\u8FB9\\u989C\\u8272\\u9009\\u62E9\\u5668\",\n  showBackground: \"\\u663E\\u793A\\u80CC\\u666F\\u989C\\u8272\\u9009\\u62E9\\u5668\",\n  toggleTheme: \"\\u5207\\u6362\\u4E3B\\u9898\",\n  personalLib: \"\\u4E2A\\u4EBA\\u7D20\\u6750\\u5E93\",\n  excalidrawLib: \"Excalidraw \\u7D20\\u6750\\u5E93\",\n  decreaseFontSize: \"\\u7F29\\u5C0F\\u5B57\\u4F53\\u5927\\u5C0F\",\n  increaseFontSize: \"\\u653E\\u5927\\u5B57\\u4F53\\u5927\\u5C0F\",\n  unbindText: \"\\u53D6\\u6D88\\u6587\\u672C\\u7ED1\\u5B9A\",\n  bindText: \"\\u5C06\\u6587\\u672C\\u7ED1\\u5B9A\\u5230\\u5BB9\\u5668\",\n  createContainerFromText: \"\\u5C06\\u6587\\u672C\\u5305\\u56F4\\u5728\\u5BB9\\u5668\\u4E2D\",\n  link: {\n    edit: \"\\u7F16\\u8F91\\u94FE\\u63A5\",\n    editEmbed: \"\\u7F16\\u8F91\\u94FE\\u63A5\\u4E0E\\u5D4C\\u5165\",\n    create: \"\\u65B0\\u5EFA\\u94FE\\u63A5\",\n    createEmbed: \"\\u521B\\u5EFA\\u94FE\\u63A5\\u4E0E\\u5D4C\\u5165\",\n    label: \"\\u94FE\\u63A5\",\n    labelEmbed: \"\\u94FE\\u63A5\\u4E0E\\u5D4C\\u5165\",\n    empty: \"\\u672A\\u8BBE\\u5B9A\\u94FE\\u63A5\"\n  },\n  lineEditor: {\n    edit: \"\\u7F16\\u8F91\\u7EBF\\u6761\",\n    exit: \"\\u9000\\u51FA\\u7EBF\\u6761\\u7F16\\u8F91\"\n  },\n  elementLock: {\n    lock: \"\\u9501\\u5B9A\",\n    unlock: \"\\u89E3\\u9664\\u9501\\u5B9A\",\n    lockAll: \"\\u5168\\u90E8\\u9501\\u5B9A\",\n    unlockAll: \"\\u5168\\u90E8\\u89E3\\u9501\"\n  },\n  statusPublished: \"\\u5DF2\\u53D1\\u5E03\",\n  sidebarLock: \"\\u4FA7\\u8FB9\\u680F\\u5E38\\u9A7B\",\n  selectAllElementsInFrame: \"\\u9009\\u62E9\\u753B\\u6846\\u4E2D\\u7684\\u6240\\u6709\\u5143\\u7D20\",\n  removeAllElementsFromFrame: \"\\u5206\\u79BB\\u51FA\\u753B\\u6846\\u4E2D\\u7684\\u6240\\u6709\\u5143\\u7D20\",\n  eyeDropper: \"\\u4ECE\\u753B\\u5E03\\u4E0A\\u53D6\\u8272\",\n  textToDiagram: \"\\u6587\\u5B57\\u81F3\\u56FE\\u8868\",\n  prompt: \"Prompt\"\n};\nvar library = {\n  noItems: \"\\u5C1A\\u672A\\u6DFB\\u52A0\\u4EFB\\u4F55\\u9879\\u76EE\\u2026\\u2026\",\n  hint_emptyLibrary: \"\\u9009\\u4E2D\\u753B\\u5E03\\u4E0A\\u7684\\u9879\\u76EE\\u6DFB\\u52A0\\u5230\\u6B64\\u5904\\uFF0C\\u6216\\u4ECE\\u4E0B\\u65B9\\u7684\\u516C\\u5171\\u7D20\\u6750\\u5E93\\u4E2D\\u5BFC\\u5165\\u3002\",\n  hint_emptyPrivateLibrary: \"\\u9009\\u4E2D\\u753B\\u5E03\\u4E0A\\u7684\\u9879\\u76EE\\u6DFB\\u52A0\\u5230\\u6B64\\u5904\\u3002\"\n};\nvar buttons = {\n  clearReset: \"\\u91CD\\u7F6E\\u753B\\u5E03\",\n  exportJSON: \"\\u5BFC\\u51FA\\u4E3A\\u6587\\u4EF6\",\n  exportImage: \"\\u5BFC\\u51FA\\u56FE\\u7247...\",\n  export: \"\\u4FDD\\u5B58\\u5230...\",\n  copyToClipboard: \"\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\",\n  save: \"\\u4FDD\\u5B58\\u81F3\\u5F53\\u524D\\u6587\\u4EF6\",\n  saveAs: \"\\u4FDD\\u5B58\\u4E3A\",\n  load: \"\\u6253\\u5F00\",\n  getShareableLink: \"\\u83B7\\u53D6\\u5171\\u4EAB\\u94FE\\u63A5\",\n  close: \"\\u5173\\u95ED\",\n  selectLanguage: \"\\u9009\\u62E9\\u8BED\\u8A00\",\n  scrollBackToContent: \"\\u6EDA\\u52A8\\u56DE\\u5230\\u5185\\u5BB9\",\n  zoomIn: \"\\u653E\\u5927\",\n  zoomOut: \"\\u7F29\\u5C0F\",\n  resetZoom: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n  menu: \"\\u83DC\\u5355\",\n  done: \"\\u5B8C\\u6210\",\n  edit: \"\\u7F16\\u8F91\",\n  undo: \"\\u64A4\\u9500\",\n  redo: \"\\u91CD\\u505A\",\n  resetLibrary: \"\\u91CD\\u7F6E\\u7D20\\u6750\\u5E93\",\n  createNewRoom: \"\\u65B0\\u5EFA\\u4F1A\\u8BAE\\u5BA4\",\n  fullScreen: \"\\u5168\\u5C4F\",\n  darkMode: \"\\u6DF1\\u8272\\u6A21\\u5F0F\",\n  lightMode: \"\\u6D45\\u8272\\u6A21\\u5F0F\",\n  zenMode: \"\\u7985\\u6A21\\u5F0F\",\n  objectsSnapMode: \"\\u5438\\u9644\\u81F3\\u5BF9\\u8C61\",\n  exitZenMode: \"\\u9000\\u51FA\\u7985\\u6A21\\u5F0F\",\n  cancel: \"\\u53D6\\u6D88\",\n  clear: \"\\u6E05\\u9664\",\n  remove: \"\\u5220\\u9664\",\n  embed: \"\\u5207\\u6362\\u5D4C\\u5165\",\n  publishLibrary: \"\\u53D1\\u5E03\",\n  submit: \"\\u63D0\\u4EA4\",\n  confirm: \"\\u786E\\u5B9A\",\n  embeddableInteractionButton: \"\\u70B9\\u51FB\\u4EE5\\u5F00\\u59CB\\u4EA4\\u4E92\"\n};\nvar alerts = {\n  clearReset: \"\\u8FD9\\u5C06\\u4F1A\\u6E05\\u9664\\u6574\\u4E2A\\u753B\\u5E03\\u3002\\u60A8\\u662F\\u5426\\u8981\\u7EE7\\u7EED?\",\n  couldNotCreateShareableLink: \"\\u65E0\\u6CD5\\u521B\\u5EFA\\u5171\\u4EAB\\u94FE\\u63A5\",\n  couldNotCreateShareableLinkTooBig: \"\\u65E0\\u6CD5\\u521B\\u5EFA\\u53EF\\u5171\\u4EAB\\u94FE\\u63A5\\uFF1A\\u753B\\u5E03\\u8FC7\\u5927\",\n  couldNotLoadInvalidFile: \"\\u65E0\\u6CD5\\u52A0\\u8F7D\\u65E0\\u6548\\u7684\\u6587\\u4EF6\",\n  importBackendFailed: \"\\u4ECE\\u540E\\u7AEF\\u5BFC\\u5165\\u5931\\u8D25\\u3002\",\n  cannotExportEmptyCanvas: \"\\u65E0\\u6CD5\\u5BFC\\u51FA\\u7A7A\\u767D\\u753B\\u5E03\\u3002\",\n  couldNotCopyToClipboard: \"\\u65E0\\u6CD5\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\\u3002\",\n  decryptFailed: \"\\u65E0\\u6CD5\\u89E3\\u5BC6\\u6570\\u636E\\u3002\",\n  uploadedSecurly: \"\\u4E0A\\u4F20\\u5DF2\\u88AB\\u7AEF\\u5230\\u7AEF\\u52A0\\u5BC6\\u4FDD\\u62A4\\uFF0C\\u8FD9\\u610F\\u5473\\u7740 Excalidraw \\u7684\\u670D\\u52A1\\u5668\\u548C\\u7B2C\\u4E09\\u65B9\\u90FD\\u65E0\\u6CD5\\u8BFB\\u53D6\\u5185\\u5BB9\\u3002\",\n  loadSceneOverridePrompt: \"\\u52A0\\u8F7D\\u5916\\u90E8\\u7ED8\\u56FE\\u5C06\\u53D6\\u4EE3\\u60A8\\u73B0\\u6709\\u7684\\u5185\\u5BB9\\u3002\\u60A8\\u60F3\\u8981\\u7EE7\\u7EED\\u5417\\uFF1F\",\n  collabStopOverridePrompt: \"\\u505C\\u6B62\\u4F1A\\u8BDD\\u5C06\\u8986\\u76D6\\u60A8\\u5148\\u524D\\u672C\\u5730\\u5B58\\u50A8\\u7684\\u7ED8\\u56FE\\u3002 \\u60A8\\u786E\\u5B9A\\u5417\\uFF1F\\n\\n(\\u5982\\u679C\\u60A8\\u60F3\\u4FDD\\u6301\\u672C\\u5730\\u7ED8\\u56FE\\uFF0C\\u53EA\\u9700\\u5173\\u95ED\\u6D4F\\u89C8\\u5668\\u9009\\u9879\\u5361\\u3002)\",\n  errorAddingToLibrary: \"\\u65E0\\u6CD5\\u5C06\\u9879\\u76EE\\u6DFB\\u52A0\\u5230\\u7D20\\u6750\\u5E93\\u4E2D\",\n  errorRemovingFromLibrary: \"\\u65E0\\u6CD5\\u4ECE\\u7D20\\u6750\\u5E93\\u4E2D\\u79FB\\u9664\\u9879\\u76EE\",\n  confirmAddLibrary: \"\\u8FD9\\u5C06\\u6DFB\\u52A0 {{numShapes}} \\u4E2A\\u5F62\\u72B6\\u5230\\u60A8\\u7684\\u7D20\\u6750\\u5E93\\u4E2D\\u3002\\u60A8\\u786E\\u5B9A\\u5417\\uFF1F\",\n  imageDoesNotContainScene: \"\\u6B64\\u56FE\\u50CF\\u4F3C\\u4E4E\\u4E0D\\u5305\\u542B\\u4EFB\\u4F55\\u753B\\u5E03\\u6570\\u636E\\u3002\\u60A8\\u662F\\u5426\\u5728\\u5BFC\\u51FA\\u65F6\\u542F\\u7528\\u4E86\\u753B\\u5E03\\u5D4C\\u5165\\u529F\\u80FD\\uFF1F\",\n  cannotRestoreFromImage: \"\\u65E0\\u6CD5\\u4ECE\\u6B64\\u56FE\\u50CF\\u6587\\u4EF6\\u6062\\u590D\\u753B\\u5E03\",\n  invalidSceneUrl: \"\\u65E0\\u6CD5\\u4ECE\\u63D0\\u4F9B\\u7684 URL \\u5BFC\\u5165\\u573A\\u666F\\u3002\\u5B83\\u6216\\u8005\\u683C\\u5F0F\\u4E0D\\u6B63\\u786E\\uFF0C\\u6216\\u8005\\u4E0D\\u5305\\u542B\\u6709\\u6548\\u7684 Excalidraw JSON \\u6570\\u636E\\u3002\",\n  resetLibrary: \"\\u8FD9\\u5C06\\u4F1A\\u6E05\\u9664\\u4F60\\u7684\\u7D20\\u6750\\u5E93\\u3002\\u4F60\\u786E\\u5B9A\\u8981\\u8FD9\\u4E48\\u505A\\u5417\\uFF1F\",\n  removeItemsFromsLibrary: \"\\u786E\\u5B9A\\u8981\\u4ECE\\u7D20\\u6750\\u5E93\\u4E2D\\u5220\\u9664 {{count}} \\u4E2A\\u9879\\u76EE\\u5417\\uFF1F\",\n  invalidEncryptionKey: \"\\u5BC6\\u94A5\\u5FC5\\u987B\\u5305\\u542B22\\u4E2A\\u5B57\\u7B26\\u3002\\u5B9E\\u65F6\\u534F\\u4F5C\\u5DF2\\u88AB\\u7981\\u7528\\u3002\",\n  collabOfflineWarning: \"\\u65E0\\u7F51\\u7EDC\\u8FDE\\u63A5\\u3002\\n\\u60A8\\u7684\\u6539\\u52A8\\u5C06\\u4E0D\\u4F1A\\u88AB\\u4FDD\\u5B58\\uFF01\"\n};\nvar errors = {\n  unsupportedFileType: \"\\u4E0D\\u652F\\u6301\\u7684\\u6587\\u4EF6\\u683C\\u5F0F\\u3002\",\n  imageInsertError: \"\\u65E0\\u6CD5\\u63D2\\u5165\\u56FE\\u50CF\\u3002\\u8BF7\\u7A0D\\u540E\\u518D\\u8BD5\\u2026\\u2026\",\n  fileTooBig: \"\\u6587\\u4EF6\\u8FC7\\u5927\\u3002\\u6700\\u5927\\u5141\\u8BB8\\u7684\\u5927\\u5C0F\\u4E3A {{maxSize}}\\u3002\",\n  svgImageInsertError: \"\\u65E0\\u6CD5\\u63D2\\u5165 SVG \\u56FE\\u50CF\\u3002\\u8BE5 SVG \\u6807\\u8BB0\\u4F3C\\u4E4E\\u662F\\u65E0\\u6548\\u7684\\u3002\",\n  failedToFetchImage: \"\\u65E0\\u6CD5\\u83B7\\u53D6\\u56FE\\u7247\\u3002\",\n  invalidSVGString: \"\\u65E0\\u6548\\u7684 SVG\\u3002\",\n  cannotResolveCollabServer: \"\\u65E0\\u6CD5\\u8FDE\\u63A5\\u5230\\u5B9E\\u65F6\\u534F\\u4F5C\\u670D\\u52A1\\u5668\\u3002\\u8BF7\\u91CD\\u65B0\\u52A0\\u8F7D\\u9875\\u9762\\u5E76\\u91CD\\u8BD5\\u3002\",\n  importLibraryError: \"\\u65E0\\u6CD5\\u52A0\\u8F7D\\u7D20\\u6750\\u5E93\",\n  collabSaveFailed: \"\\u65E0\\u6CD5\\u4FDD\\u5B58\\u5230\\u540E\\u7AEF\\u6570\\u636E\\u5E93\\u3002\\u5982\\u679C\\u95EE\\u9898\\u6301\\u7EED\\u5B58\\u5728\\uFF0C\\u60A8\\u5E94\\u8BE5\\u4FDD\\u5B58\\u6587\\u4EF6\\u5230\\u672C\\u5730\\uFF0C\\u4EE5\\u786E\\u4FDD\\u60A8\\u7684\\u5DE5\\u4F5C\\u4E0D\\u4F1A\\u4E22\\u5931\\u3002\",\n  collabSaveFailed_sizeExceeded: \"\\u65E0\\u6CD5\\u4FDD\\u5B58\\u5230\\u540E\\u7AEF\\u6570\\u636E\\u5E93\\uFF0C\\u753B\\u5E03\\u4F3C\\u4E4E\\u8FC7\\u5927\\u3002\\u60A8\\u5E94\\u8BE5\\u4FDD\\u5B58\\u6587\\u4EF6\\u5230\\u672C\\u5730\\uFF0C\\u4EE5\\u786E\\u4FDD\\u60A8\\u7684\\u5DE5\\u4F5C\\u4E0D\\u4F1A\\u4E22\\u5931\\u3002\",\n  imageToolNotSupported: \"\\u56FE\\u7247\\u5DF2\\u88AB\\u7981\\u7528\\u3002\",\n  brave_measure_text_error: {\n    line1: \"\\u60A8\\u4F3C\\u4E4E\\u6B63\\u5728\\u4F7F\\u7528 Brave \\u6D4F\\u89C8\\u5668\\u5E76\\u542F\\u7528\\u4E86<bold>\\u79EF\\u6781\\u963B\\u6B62\\u6307\\u7EB9\\u8BC6\\u522B</bold>\\u7684\\u8BBE\\u7F6E\\u3002\",\n    line2: \"\\u8FD9\\u53EF\\u80FD\\u4F1A\\u7834\\u574F\\u7ED8\\u56FE\\u4E2D\\u7684 <bold>\\u6587\\u672C\\u5143\\u7D20</bold>\\u3002\",\n    line3: \"\\u6211\\u4EEC\\u5F3A\\u70C8\\u5EFA\\u8BAE\\u7981\\u7528\\u6B64\\u8BBE\\u7F6E\\u3002\\u60A8\\u53EF\\u4EE5\\u6309\\u7167<link>\\u8FD9\\u4E9B\\u6B65\\u9AA4</link>\\u6765\\u8BBE\\u7F6E\\u3002\",\n    line4: \"\\u5982\\u679C\\u7981\\u7528\\u6B64\\u8BBE\\u7F6E\\u65E0\\u6CD5\\u4FEE\\u590D\\u6587\\u672C\\u5143\\u7D20\\u7684\\u663E\\u793A\\uFF0C\\u8BF7\\u5728 GitHub \\u4E0A\\u63D0\\u4EA4\\u4E00\\u4E2A <issueLink>issue</issueLink> \\uFF0C\\u6216\\u8005\\u5728 <discordLink>Discord</discordLink> \\u4E0A\\u53CD\\u9988\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\\u5D4C\\u5165\\u7684\\u5143\\u7D20\\u4E0D\\u80FD\\u88AB\\u6DFB\\u52A0\\u5230\\u7D20\\u6750\\u5E93\\u3002\",\n    iframe: \"\\u4E0D\\u80FD\\u5C06 IFrame \\u5143\\u7D20\\u6DFB\\u52A0\\u5230\\u7D20\\u6750\\u5E93\\u4E2D\\u3002\",\n    image: \"\\u6211\\u4EEC\\u4E0D\\u4E45\\u5C06\\u652F\\u6301\\u6DFB\\u52A0\\u56FE\\u7247\\u5230\\u7D20\\u6750\\u5E93\"\n  },\n  asyncPasteFailedOnRead: \"\\u65E0\\u6CD5\\u7C98\\u8D34\\uFF08\\u65E0\\u6CD5\\u8BFB\\u53D6\\u7CFB\\u7EDF\\u526A\\u8D34\\u677F\\uFF09\\u3002\",\n  asyncPasteFailedOnParse: \"\\u65E0\\u6CD5\\u7C98\\u8D34\\u3002\",\n  copyToSystemClipboardFailed: \"\\u65E0\\u6CD5\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\\u3002\"\n};\nvar toolBar = {\n  selection: \"\\u9009\\u62E9\",\n  image: \"\\u63D2\\u5165\\u56FE\\u50CF\",\n  rectangle: \"\\u77E9\\u5F62\",\n  diamond: \"\\u83F1\\u5F62\",\n  ellipse: \"\\u692D\\u5706\",\n  arrow: \"\\u7BAD\\u5934\",\n  line: \"\\u7EBF\\u6761\",\n  freedraw: \"\\u81EA\\u7531\\u4E66\\u5199\",\n  text: \"\\u6587\\u5B57\",\n  library: \"\\u7D20\\u6750\\u5E93\",\n  lock: \"\\u7ED8\\u5236\\u540E\\u4FDD\\u6301\\u6240\\u9009\\u7684\\u5DE5\\u5177\\u680F\\u72B6\\u6001\",\n  penMode: \"\\u7B14\\u6A21\\u5F0F \\u2013 \\u907F\\u514D\\u8BEF\\u89E6\",\n  link: \"\\u4E3A\\u9009\\u4E2D\\u7684\\u5F62\\u72B6\\u6DFB\\u52A0/\\u66F4\\u65B0\\u94FE\\u63A5\",\n  eraser: \"\\u6A61\\u76AE\",\n  frame: \"\\u753B\\u6846\\u5DE5\\u5177\",\n  magicframe: \"\\u7EBF\\u6846\\u56FE\\u81F3\\u4EE3\\u7801\",\n  embeddable: \"\\u5D4C\\u5165\\u7F51\\u9875\",\n  laser: \"\\u6FC0\\u5149\\u7B14\",\n  hand: \"\\u6293\\u624B\\uFF08\\u5E73\\u79FB\\u5DE5\\u5177\\uFF09\",\n  extraTools: \"\\u66F4\\u591A\\u5DE5\\u5177\",\n  mermaidToExcalidraw: \"Mermaid \\u81F3 Excalidraw\",\n  magicSettings: \"AI \\u8BBE\\u7F6E\"\n};\nvar headings = {\n  canvasActions: \"\\u753B\\u5E03\\u52A8\\u4F5C\",\n  selectedShapeActions: \"\\u9009\\u5B9A\\u5F62\\u72B6\\u64CD\\u4F5C\",\n  shapes: \"\\u5F62\\u72B6\"\n};\nvar hints = {\n  canvasPanning: \"\\u8981\\u79FB\\u52A8\\u753B\\u5E03\\uFF0C\\u8BF7\\u6309\\u4F4F\\u9F20\\u6807\\u6EDA\\u8F6E\\u6216\\u7A7A\\u683C\\u952E\\u540C\\u65F6\\u62D6\\u62FD\\u9F20\\u6807\\uFF0C\\u6216\\u4F7F\\u7528\\u6293\\u624B\\u5DE5\\u5177\\u3002\",\n  linearElement: \"\\u70B9\\u51FB\\u521B\\u5EFA\\u591A\\u4E2A\\u70B9 \\u62D6\\u52A8\\u521B\\u5EFA\\u76F4\\u7EBF\",\n  freeDraw: \"\\u70B9\\u51FB\\u5E76\\u62D6\\u52A8\\uFF0C\\u5B8C\\u6210\\u65F6\\u677E\\u5F00\",\n  text: \"\\u63D0\\u793A\\uFF1A\\u60A8\\u4E5F\\u53EF\\u4EE5\\u4F7F\\u7528\\u9009\\u62E9\\u5DE5\\u5177\\u53CC\\u51FB\\u4EFB\\u610F\\u4F4D\\u7F6E\\u6765\\u6DFB\\u52A0\\u6587\\u5B57\",\n  embeddable: \"\\u70B9\\u51FB\\u5E76\\u62D6\\u52A8\\u4EE5\\u521B\\u5EFA\\u5D4C\\u5165\\u7F51\\u9875\",\n  text_selected: \"\\u53CC\\u51FB\\u6216\\u6309\\u56DE\\u8F66\\u952E\\u4EE5\\u7F16\\u8F91\\u6587\\u672C\",\n  text_editing: \"\\u6309\\u4E0B Escape \\u6216 CtrlOrCmd+ENTER \\u5B8C\\u6210\\u7F16\\u8F91\",\n  linearElementMulti: \"\\u70B9\\u51FB\\u6700\\u540E\\u4E00\\u4E2A\\u70B9\\u6216\\u6309\\u4E0B Esc/Enter \\u6765\\u5B8C\\u6210\",\n  lockAngle: \"\\u53EF\\u4EE5\\u6309\\u4F4F Shift \\u6765\\u7EA6\\u675F\\u89D2\\u5EA6\",\n  resize: \"\\u60A8\\u53EF\\u4EE5\\u6309\\u4F4FSHIFT\\u6765\\u9650\\u5236\\u6BD4\\u4F8B\\u5927\\u5C0F\\uFF0C\\n\\u6309\\u4F4FALT\\u6765\\u8C03\\u6574\\u4E2D\\u5FC3\\u5927\\u5C0F\",\n  resizeImage: \"\\u6309\\u4F4FSHIFT\\u53EF\\u4EE5\\u81EA\\u7531\\u7F29\\u653E\\uFF0C\\n\\u6309\\u4F4FALT\\u53EF\\u4EE5\\u4ECE\\u4E2D\\u95F4\\u7F29\\u653E\",\n  rotate: \"\\u65CB\\u8F6C\\u65F6\\u53EF\\u4EE5\\u6309\\u4F4F Shift \\u6765\\u7EA6\\u675F\\u89D2\\u5EA6\",\n  lineEditor_info: \"\\u6309\\u4F4F CtrlOrCmd \\u5E76\\u53CC\\u51FB\\u6216\\u6309 CtrlOrCmd + Enter \\u6765\\u7F16\\u8F91\\u70B9\",\n  lineEditor_pointSelected: \"\\u6309\\u4E0B Delete \\u79FB\\u9664\\u70B9\\uFF0CCtrlOrCmd+D \\u4EE5\\u590D\\u5236\\uFF0C\\u62D6\\u52A8\\u4EE5\\u79FB\\u52A8\",\n  lineEditor_nothingSelected: \"\\u9009\\u62E9\\u8981\\u7F16\\u8F91\\u7684\\u70B9 (\\u6309\\u4F4F SHIFT \\u9009\\u62E9\\u591A\\u4E2A)\\uFF0C\\n\\u6216\\u6309\\u4F4F Alt \\u5E76\\u70B9\\u51FB\\u4EE5\\u6DFB\\u52A0\\u65B0\\u70B9\",\n  placeImage: \"\\u70B9\\u51FB\\u653E\\u7F6E\\u56FE\\u50CF\\uFF0C\\u6216\\u8005\\u70B9\\u51FB\\u5E76\\u62D6\\u52A8\\u4EE5\\u624B\\u52A8\\u8BBE\\u7F6E\\u56FE\\u50CF\\u5927\\u5C0F\",\n  publishLibrary: \"\\u53D1\\u5E03\\u60A8\\u81EA\\u5DF1\\u7684\\u7D20\\u6750\\u5E93\",\n  bindTextToElement: \"\\u6309\\u4E0B Enter \\u4EE5\\u6DFB\\u52A0\\u6587\\u672C\",\n  deepBoxSelect: \"\\u6309\\u4F4F CtrlOrCmd \\u4EE5\\u6DF1\\u5EA6\\u9009\\u62E9\\uFF0C\\u5E76\\u907F\\u514D\\u62D6\\u62FD\",\n  eraserRevert: \"\\u6309\\u4F4F Alt \\u4EE5\\u53CD\\u9009\\u88AB\\u6807\\u8BB0\\u5220\\u9664\\u7684\\u5143\\u7D20\",\n  firefox_clipboard_write: \"\\u5C06\\u9AD8\\u7EA7\\u914D\\u7F6E\\u9996\\u9009\\u9879\\u201Cdom.events.asyncClipboard.clipboardItem\\u201D\\u8BBE\\u7F6E\\u4E3A\\u201Ctrue\\u201D\\u53EF\\u4EE5\\u542F\\u7528\\u6B64\\u529F\\u80FD\\u3002\\u8981\\u66F4\\u6539 Firefox \\u7684\\u9AD8\\u7EA7\\u914D\\u7F6E\\u9996\\u9009\\u9879\\uFF0C\\u8BF7\\u524D\\u5F80\\u201Cabout:config\\u201D\\u9875\\u9762\\u3002\",\n  disableSnapping: \"\\u6309\\u4F4F Ctrl \\u6216 Cmd \\u4EE5\\u7981\\u7528\\u5438\\u9644\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\\u65E0\\u6CD5\\u663E\\u793A\\u9884\\u89C8\",\n  canvasTooBig: \"\\u753B\\u5E03\\u53EF\\u80FD\\u8FC7\\u5927\\u3002\",\n  canvasTooBigTip: \"\\u63D0\\u793A\\uFF1A\\u5C1D\\u8BD5\\u5C06\\u6700\\u8FDC\\u7684\\u5143\\u7D20\\u79FB\\u52A8\\u5230\\u548C\\u5176\\u5B83\\u5143\\u7D20\\u66F4\\u8FD1\\u4E00\\u4E9B\\u3002\"\n};\nvar errorSplash = {\n  headingMain: \"\\u9047\\u5230\\u5F02\\u5E38\\u3002\\u8BF7\\u5C1D\\u8BD5<button>\\u91CD\\u65B0\\u52A0\\u8F7D\\u9875\\u9762</button>\\u3002\",\n  clearCanvasMessage: \"\\u5982\\u679C\\u91CD\\u65B0\\u52A0\\u8F7D\\u9875\\u9762\\u65E0\\u6548\\uFF0C\\u8BF7\\u5C1D\\u8BD5<button>\\u6E05\\u9664\\u753B\\u5E03</button>\\u3002\",\n  clearCanvasCaveat: \"\\u8FD9\\u4F1A\\u9020\\u6210\\u5F53\\u524D\\u5DE5\\u4F5C\\u4E22\\u5931\",\n  trackedToSentry: \"\\u6807\\u8BC6\\u7B26\\u4E3A{{eventId}}\\u7684\\u9519\\u8BEF\\u5DF2\\u5728\\u6211\\u4EEC\\u7684\\u7CFB\\u7EDF\\u4E2D\\u88AB\\u8BB0\\u5F55\",\n  openIssueMessage: \"\\u6211\\u4EEC\\u975E\\u5E38\\u8C28\\u614E\\u5730\\u5904\\u7406\\u9519\\u8BEF\\u4FE1\\u606F\\uFF0C\\u60A8\\u7684\\u753B\\u5E03\\u5185\\u5BB9\\u4E0D\\u4F1A\\u88AB\\u5305\\u542B\\u5728\\u9519\\u8BEF\\u62A5\\u544A\\u4E2D\\u3002\\u5982\\u679C\\u60A8\\u7684\\u753B\\u5E03\\u5185\\u5BB9\\u4E0D\\u9700\\u8981\\u4FDD\\u6301\\u79C1\\u5BC6\\uFF0C\\u8BF7\\u8003\\u8651\\u5728\\u6211\\u4EEC\\u7684 <button>bug \\u8DDF\\u8E2A\\u7CFB\\u7EDF</button>\\u4E0A\\u63D0\\u4F9B\\u66F4\\u591A\\u4FE1\\u606F\\u3002\\u8BF7\\u590D\\u5236\\u7C98\\u8D34\\u4EE5\\u4E0B\\u4FE1\\u606F\\u5230 GitHub Issue \\u4E2D\\u3002\",\n  sceneContent: \"\\u753B\\u5E03\\u5185\\u5BB9:\"\n};\nvar roomDialog = {\n  desc_intro: \"\\u4F60\\u53EF\\u4EE5\\u9080\\u8BF7\\u5176\\u4ED6\\u4EBA\\u5230\\u76EE\\u524D\\u7684\\u753B\\u9762\\u4E2D\\u4E0E\\u4F60\\u534F\\u4F5C\\u3002\",\n  desc_privacy: \"\\u522B\\u62C5\\u5FC3\\uFF0C\\u8BE5\\u4F1A\\u8BDD\\u4F7F\\u7528\\u7AEF\\u5230\\u7AEF\\u52A0\\u5BC6\\uFF0C\\u65E0\\u8BBA\\u7ED8\\u5236\\u4EC0\\u4E48\\u90FD\\u5C06\\u4FDD\\u6301\\u79C1\\u5BC6\\uFF0C\\u751A\\u81F3\\u8FDE\\u6211\\u4EEC\\u7684\\u670D\\u52A1\\u5668\\u4E5F\\u65E0\\u6CD5\\u67E5\\u770B\\u3002\",\n  button_startSession: \"\\u5F00\\u59CB\\u4F1A\\u8BDD\",\n  button_stopSession: \"\\u7ED3\\u675F\\u4F1A\\u8BDD\",\n  desc_inProgressIntro: \"\\u5B9E\\u65F6\\u534F\\u4F5C\\u4F1A\\u8BDD\\u8FDB\\u884C\\u4E2D\\u3002\",\n  desc_shareLink: \"\\u5206\\u4EAB\\u6B64\\u94FE\\u63A5\\u7ED9\\u4F60\\u8981\\u534F\\u4F5C\\u7684\\u7528\\u6237\",\n  desc_exitSession: \"\\u505C\\u6B62\\u4F1A\\u8BDD\\u5C06\\u4E2D\\u65AD\\u60A8\\u4E0E\\u623F\\u95F4\\u7684\\u8FDE\\u63A5\\uFF0C\\u4F46\\u60A8\\u4F9D\\u7136\\u53EF\\u4EE5\\u5728\\u672C\\u5730\\u7EE7\\u7EED\\u4F7F\\u7528\\u753B\\u5E03\\u3002\\u8BF7\\u6CE8\\u610F\\uFF0C\\u8FD9\\u4E0D\\u4F1A\\u5F71\\u54CD\\u5230\\u5176\\u4ED6\\u7528\\u6237\\uFF0C\\u4ED6\\u4EEC\\u4ECD\\u53EF\\u4EE5\\u5728\\u4ED6\\u4EEC\\u7684\\u7248\\u672C\\u4E0A\\u7EE7\\u7EED\\u534F\\u4F5C\\u3002\",\n  shareTitle: \"\\u52A0\\u5165 Excalidraw \\u5B9E\\u65F6\\u534F\\u4F5C\\u4F1A\\u8BDD\"\n};\nvar errorDialog = {\n  title: \"\\u9519\\u8BEF\"\n};\nvar exportDialog = {\n  disk_title: \"\\u4FDD\\u5B58\\u5230\\u672C\\u5730\",\n  disk_details: \"\\u5C06\\u753B\\u5E03\\u6570\\u636E\\u5BFC\\u51FA\\u4E3A\\u6587\\u4EF6\\uFF0C\\u4EE5\\u4FBF\\u4EE5\\u540E\\u5BFC\\u5165\",\n  disk_button: \"\\u4FDD\\u5B58\\u4E3A\\u6587\\u4EF6\",\n  link_title: \"\\u5206\\u4EAB\\u94FE\\u63A5\",\n  link_details: \"\\u5BFC\\u51FA\\u4E3A\\u53EA\\u8BFB\\u94FE\\u63A5\\u3002\",\n  link_button: \"\\u5BFC\\u51FA\\u94FE\\u63A5\",\n  excalidrawplus_description: \"\\u5C06\\u753B\\u5E03\\u4FDD\\u5B58\\u5230\\u60A8\\u7684 Excalidraw+ \\u5DE5\\u4F5C\\u533A\\u3002\",\n  excalidrawplus_button: \"\\u5BFC\\u51FA\",\n  excalidrawplus_exportError: \"\\u6682\\u65F6\\u65E0\\u6CD5\\u5BFC\\u51FA\\u5230 Excalidraw+ ...\"\n};\nvar helpDialog = {\n  blog: \"\\u6D4F\\u89C8\\u6211\\u4EEC\\u7684\\u535A\\u5BA2\",\n  click: \"\\u5355\\u51FB\",\n  deepSelect: \"\\u6DF1\\u5EA6\\u9009\\u62E9\",\n  deepBoxSelect: \"\\u5728\\u65B9\\u6846\\u5185\\u6DF1\\u5EA6\\u9009\\u62E9\\u5E76\\u907F\\u514D\\u62D6\\u62FD\",\n  curvedArrow: \"\\u66F2\\u7EBF\\u7BAD\\u5934\",\n  curvedLine: \"\\u66F2\\u7EBF\",\n  documentation: \"\\u6587\\u6863\",\n  doubleClick: \"\\u53CC\\u51FB\",\n  drag: \"\\u62D6\\u52A8\",\n  editor: \"\\u7F16\\u8F91\\u5668\",\n  editLineArrowPoints: \"\\u7F16\\u8F91\\u7EBF\\u6761\\u6216\\u7BAD\\u5934\\u7684\\u70B9\",\n  editText: \"\\u6DFB\\u52A0\\u6216\\u7F16\\u8F91\\u6587\\u672C\",\n  github: \"\\u53D1\\u73B0\\u95EE\\u9898\\uFF1F\\u63D0\\u4EA4\\u53CD\\u9988\",\n  howto: \"\\u5E2E\\u52A9\\u6587\\u6863\",\n  or: \"\\u6216\",\n  preventBinding: \"\\u7981\\u7528\\u7BAD\\u5934\\u5438\\u9644\",\n  tools: \"\\u5DE5\\u5177\",\n  shortcuts: \"\\u5FEB\\u6377\\u952E\\u5217\\u8868\",\n  textFinish: \"\\u5B8C\\u6210\\u7F16\\u8F91 (\\u6587\\u672C\\u7F16\\u8F91\\u5668)\",\n  textNewLine: \"\\u6DFB\\u52A0\\u65B0\\u884C(\\u6587\\u672C\\u7F16\\u8F91\\u5668)\",\n  title: \"\\u5E2E\\u52A9\",\n  view: \"\\u89C6\\u56FE\",\n  zoomToFit: \"\\u7F29\\u653E\\u4EE5\\u9002\\u5E94\\u6240\\u6709\\u5143\\u7D20\",\n  zoomToSelection: \"\\u7F29\\u653E\\u5230\\u9009\\u533A\",\n  toggleElementLock: \"\\u9501\\u5B9A/\\u89E3\\u9501\",\n  movePageUpDown: \"\\u4E0A\\u4E0B\\u79FB\\u52A8\\u9875\\u9762\",\n  movePageLeftRight: \"\\u5DE6\\u53F3\\u79FB\\u52A8\\u9875\\u9762\"\n};\nvar clearCanvasDialog = {\n  title: \"\\u6E05\\u9664\\u753B\\u5E03\"\n};\nvar publishDialog = {\n  title: \"\\u53D1\\u5E03\\u7D20\\u6750\\u5E93\",\n  itemName: \"\\u9879\\u76EE\\u540D\\u79F0\",\n  authorName: \"\\u4F5C\\u8005\\u540D\",\n  githubUsername: \"GitHub \\u7528\\u6237\\u540D\",\n  twitterUsername: \"Twitter \\u7528\\u6237\\u540D\",\n  libraryName: \"\\u540D\\u79F0\",\n  libraryDesc: \"\\u7B80\\u4ECB\",\n  website: \"\\u7F51\\u5740\",\n  placeholder: {\n    authorName: \"\\u60A8\\u7684\\u540D\\u5B57\\u6216\\u7528\\u6237\\u540D\",\n    libraryName: \"\\u7D20\\u6750\\u5E93\\u540D\\u79F0\",\n    libraryDesc: \"\\u4ECB\\u7ECD\\u60A8\\u7684\\u7D20\\u6750\\u5E93\\uFF0C\\u8BA9\\u4EBA\\u4EEC\\u4E86\\u89E3\\u5176\\u7528\\u9014\",\n    githubHandle: \"GitHub \\u7528\\u6237\\u540D\\uFF08\\u53EF\\u9009\\uFF09\\uFF0C\\u586B\\u5199\\u540E\\uFF0C\\u60A8\\u53EF\\u4EE5\\u7F16\\u8F91\\u5DF2\\u63D0\\u4EA4\\u5F85\\u5BA1\\u7684\\u7D20\\u6750\\u5E93\",\n    twitterHandle: \"Twitter \\u7528\\u6237\\u540D\\uFF08\\u53EF\\u9009\\uFF09\\uFF0C\\u586B\\u5199\\u540E\\uFF0C\\u5F53\\u6211\\u4EEC\\u5728Twitter\\u53D1\\u5E03\\u63A8\\u5E7F\\u4FE1\\u606F\\u65F6\\u4FBF\\u53EF\\u63D0\\u53CA\\u60A8\",\n    website: \"\\u60A8\\u4E2A\\u4EBA\\u7F51\\u7AD9\\u7684\\u6216\\u4EFB\\u610F\\u7684\\u94FE\\u63A5\\uFF08\\u53EF\\u9009\\uFF09\"\n  },\n  errors: {\n    required: \"\\u5FC5\\u586B\",\n    website: \"\\u8F93\\u5165\\u4E00\\u4E2A\\u6709\\u6548\\u7684URL\"\n  },\n  noteDescription: \"\\u63D0\\u4EA4\\u540E\\uFF0C\\u60A8\\u7684\\u7D20\\u6750\\u5E93\\u5C06\\u88AB\\u5305\\u542B\\u5728<link>\\u516C\\u5171\\u7D20\\u6750\\u5E93\\u5E7F\\u573A</link>\\u4EE5\\u4F9B\\u5176\\u4ED6\\u4EBA\\u5728\\u7ED8\\u56FE\\u4E2D\\u4F7F\\u7528\\u3002\",\n  noteGuidelines: \"\\u63D0\\u4EA4\\u7684\\u7D20\\u6750\\u5E93\\u9700\\u5148\\u7ECF\\u4EBA\\u5DE5\\u5BA1\\u6838\\u3002\\u5728\\u63D0\\u4EA4\\u4E4B\\u524D\\uFF0C\\u8BF7\\u5148\\u9605\\u8BFB<link>\\u6307\\u5357</link> \\u3002\\u540E\\u7EED\\u6C9F\\u901A\\u548C\\u5BF9\\u5E93\\u7684\\u4FEE\\u6539\\u9700\\u8981 GitHub \\u8D26\\u53F7\\uFF0C\\u4F46\\u8FD9\\u4E0D\\u662F\\u5FC5\\u987B\\u7684\\u3002\",\n  noteLicense: \"\\u63D0\\u4EA4\\u5373\\u8868\\u660E\\u60A8\\u5DF2\\u540C\\u610F\\u7D20\\u6750\\u5E93\\u5C06\\u9075\\u5FAA <link>MIT \\u8BB8\\u53EF\\u8BC1</link>\\uFF0C\\u7B80\\u800C\\u8A00\\u4E4B\\uFF0C\\u4EFB\\u4F55\\u4EBA\\u90FD\\u53EF\\u4EE5\\u4E0D\\u53D7\\u9650\\u5236\\u5730\\u4F7F\\u7528\\u5B83\\u4EEC\\u3002\",\n  noteItems: \"\\u7D20\\u6750\\u5E93\\u4E2D\\u6BCF\\u4E2A\\u9879\\u76EE\\u90FD\\u6709\\u5404\\u81EA\\u7684\\u540D\\u79F0\\u4EE5\\u4F9B\\u7B5B\\u9009\\u3002\\u4EE5\\u4E0B\\u9879\\u76EE\\u5C06\\u88AB\\u5305\\u542B\\uFF1A\",\n  atleastOneLibItem: \"\\u8BF7\\u9009\\u62E9\\u81F3\\u5C11\\u4E00\\u4E2A\\u7D20\\u6750\\u5E93\\u4EE5\\u5F00\\u59CB\",\n  republishWarning: \"\\u6CE8\\u610F\\uFF1A\\u90E8\\u5206\\u9009\\u4E2D\\u7684\\u9879\\u76EE\\u5DF2\\u7ECF\\u53D1\\u5E03\\u6216\\u63D0\\u4EA4\\u3002\\u8BF7\\u4EC5\\u5728\\u66F4\\u65B0\\u5DF2\\u6709\\u6216\\u5DF2\\u63D0\\u4EA4\\u7684\\u7D20\\u6750\\u5E93\\u65F6\\u91CD\\u590D\\u63D0\\u4EA4\\u9879\\u76EE\\u3002\"\n};\nvar publishSuccessDialog = {\n  title: \"\\u7D20\\u6750\\u5E93\\u5DF2\\u63D0\\u4EA4\",\n  content: \"\\u8C22\\u8C22\\u4F60 {{authorName}}\\u3002\\u60A8\\u7684\\u7D20\\u6750\\u5E93\\u5DF2\\u88AB\\u63D0\\u4EA4\\u5BA1\\u6838\\u3002\\u8BF7\\u70B9\\u51FB<link>\\u6B64\\u5904</link>\\u8DDF\\u8FDB\\u6B64\\u6B21\\u63D0\\u4EA4\\u7684\\u72B6\\u6001\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\\u91CD\\u7F6E\\u7D20\\u6750\\u5E93\",\n  removeItemsFromLib: \"\\u4ECE\\u7D20\\u6750\\u5E93\\u4E2D\\u5220\\u9664\\u9009\\u4E2D\\u7684\\u9879\\u76EE\"\n};\nvar imageExportDialog = {\n  header: \"\\u5BFC\\u51FA\\u56FE\\u7247\",\n  label: {\n    withBackground: \"\\u80CC\\u666F\",\n    onlySelected: \"\\u4EC5\\u9009\\u4E2D\",\n    darkMode: \"\\u6DF1\\u8272\\u6A21\\u5F0F\",\n    embedScene: \"\\u5305\\u542B\\u753B\\u5E03\\u6570\\u636E\",\n    scale: \"\\u7F29\\u653E\\u6BD4\\u4F8B\",\n    padding: \"\\u5185\\u8FB9\\u8DDD\"\n  },\n  tooltip: {\n    embedScene: \"\\u753B\\u5E03\\u6570\\u636E\\u5C06\\u88AB\\u4FDD\\u5B58\\u5230\\u5BFC\\u51FA\\u7684 PNG/SVG \\u6587\\u4EF6\\uFF0C\\u4EE5\\u4FBF\\u6062\\u590D\\u3002\\n\\u5C06\\u4F1A\\u589E\\u52A0\\u5BFC\\u51FA\\u6587\\u4EF6\\u7684\\u5927\\u5C0F\\u3002\"\n  },\n  title: {\n    exportToPng: \"\\u5BFC\\u51FA\\u4E3A PNG\",\n    exportToSvg: \"\\u5BFC\\u51FA\\u4E3A SVG\",\n    copyPngToClipboard: \"\\u590D\\u5236 PNG \\u5230\\u526A\\u5207\\u677F\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\\u60A8\\u7684\\u7ED8\\u56FE\\u91C7\\u7528\\u7AEF\\u5230\\u7AEF\\u52A0\\u5BC6\\uFF0C\\u5176\\u5185\\u5BB9\\u5BF9\\u4E8E Excalidraw \\u670D\\u52A1\\u5668\\u662F\\u4E0D\\u53EF\\u89C1\\u7684\\u3002\",\n  link: \"Excalidraw \\u4E2D\\u5173\\u4E8E\\u7AEF\\u5230\\u7AEF\\u52A0\\u5BC6\\u7684\\u535A\\u5BA2\"\n};\nvar stats = {\n  angle: \"\\u89D2\\u5EA6\",\n  element: \"\\u5143\\u7D20\",\n  elements: \"\\u5143\\u7D20\",\n  height: \"\\u9AD8\\u5EA6\",\n  scene: \"\\u753B\\u5E03\",\n  selected: \"\\u9009\\u4E2D\",\n  storage: \"\\u5B58\\u50A8\",\n  title: \"\\u8BE6\\u7EC6\\u7EDF\\u8BA1\\u4FE1\\u606F\",\n  total: \"\\u603B\\u8BA1\",\n  version: \"\\u7248\\u672C\",\n  versionCopy: \"\\u70B9\\u51FB\\u590D\\u5236\",\n  versionNotAvailable: \"\\u7248\\u672C\\u4E0D\\u53EF\\u7528\",\n  width: \"\\u5BBD\\u5EA6\"\n};\nvar toast = {\n  addedToLibrary: \"\\u6DFB\\u52A0\\u5230\\u7D20\\u6750\\u5E93\\u4E2D\",\n  copyStyles: \"\\u6837\\u5F0F\\u5DF2\\u62F7\\u8D1D\\u3002\",\n  copyToClipboard: \"\\u5DF2\\u590D\\u5236\\u5230\\u526A\\u5207\\u677F\\u3002\",\n  copyToClipboardAsPng: \"\\u5DF2\\u5C06 {{exportSelection}} \\u4F5C\\u4E3A PNG \\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F\\n({{exportColorScheme}})\",\n  fileSaved: \"\\u6587\\u4EF6\\u5DF2\\u4FDD\\u5B58\\u3002\",\n  fileSavedToFilename: \"\\u4FDD\\u5B58\\u5230 {filename}\",\n  canvas: \"\\u753B\\u5E03\",\n  selection: \"\\u6240\\u9009\\u9879\",\n  pasteAsSingleElement: \"\\u4F7F\\u7528 {{shortcut}} \\u7C98\\u8D34\\u4E3A\\u5355\\u4E2A\\u5143\\u7D20\\uFF0C\\n\\u6216\\u7C98\\u8D34\\u5230\\u73B0\\u6709\\u7684\\u6587\\u672C\\u7F16\\u8F91\\u5668\\u91CC\",\n  unableToEmbed: \"\\u76EE\\u524D\\u4E0D\\u5141\\u8BB8\\u5D4C\\u5165\\u6B64\\u7F51\\u5740\\u3002\\u8BF7\\u5728 GitHub \\u4E0A\\u63D0 issue \\u8BF7\\u6C42\\u5C06\\u6B64\\u7F51\\u5740\\u52A0\\u5165\\u767D\\u540D\\u5355\",\n  unrecognizedLinkFormat: \"\\u60A8\\u5D4C\\u5165\\u7684\\u94FE\\u63A5\\u4E0D\\u7B26\\u5408\\u683C\\u5F0F\\u8981\\u6C42\\u3002\\u8BF7\\u5C1D\\u8BD5\\u7C98\\u8D34\\u6E90\\u7F51\\u7AD9\\u63D0\\u4F9B\\u7684\\u201C\\u5D4C\\u5165 (embed)\\u201D\\u5B57\\u7B26\\u4E32\"\n};\nvar colors = {\n  transparent: \"\\u900F\\u660E\",\n  black: \"\\u9ED1\",\n  white: \"\\u767D\",\n  red: \"\\u7EA2\",\n  pink: \"\\u7C89\\u7EA2\",\n  grape: \"\\u7D2B\\u7EA2\",\n  violet: \"\\u84DD\\u7D2B\",\n  gray: \"\\u7070\",\n  blue: \"\\u84DD\",\n  cyan: \"\\u9752\",\n  teal: \"\\u84DD\\u7EFF\",\n  green: \"\\u7EFF\",\n  yellow: \"\\u9EC4\",\n  orange: \"\\u6A59\",\n  bronze: \"\\u53E4\\u94DC\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\\u60A8\\u7684\\u6240\\u6709\\u6570\\u636E\\u90FD\\u50A8\\u5B58\\u5728\\u6D4F\\u89C8\\u5668\\u672C\\u5730\\u3002\",\n    center_heading_plus: \"\\u662F\\u5426\\u524D\\u5F80 Excalidraw+ \\uFF1F\",\n    menuHint: \"\\u5BFC\\u51FA\\u3001\\u9996\\u9009\\u9879\\u3001\\u8BED\\u8A00\\u2026\\u2026\"\n  },\n  defaults: {\n    menuHint: \"\\u5BFC\\u51FA\\u3001\\u9996\\u9009\\u9879\\u2026\\u2026\",\n    center_heading: \"\\u56FE\\uFF0C\\u5316\\u7E41\\u4E3A\\u7B80\\u3002\",\n    toolbarHint: \"\\u9009\\u62E9\\u5DE5\\u5177\\u5E76\\u5F00\\u59CB\\u7ED8\\u56FE\\uFF01\",\n    helpHint: \"\\u5FEB\\u6377\\u952E\\u548C\\u5E2E\\u52A9\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\\u5E38\\u7528\\u81EA\\u5B9A\\u4E49\\u989C\\u8272\",\n  colors: \"\\u989C\\u8272\",\n  shades: \"\\u8272\\u8C03\\u660E\\u6697\",\n  hexCode: \"\\u5341\\u516D\\u8FDB\\u5236\\u503C\",\n  noShades: \"\\u6B64\\u989C\\u8272\\u6CA1\\u6709\\u53EF\\u7528\\u7684\\u660E\\u6697\\u53D8\\u5316\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\\u5BFC\\u51FA\\u4E3A\\u56FE\\u7247\",\n      button: \"\\u5BFC\\u51FA\\u4E3A\\u56FE\\u7247\",\n      description: \"\\u5C06\\u753B\\u5E03\\u6570\\u636E\\u5BFC\\u51FA\\u4E3A\\u56FE\\u7247\\uFF0C\\u4EE5\\u4FBF\\u4EE5\\u540E\\u5BFC\\u5165\\u3002\"\n    },\n    saveToDisk: {\n      title: \"\\u4FDD\\u5B58\\u5230\\u672C\\u5730\",\n      button: \"\\u4FDD\\u5B58\\u5230\\u672C\\u5730\",\n      description: \"\\u5C06\\u753B\\u5E03\\u6570\\u636E\\u5BFC\\u51FA\\u4E3A\\u6587\\u4EF6\\uFF0C\\u4EE5\\u4FBF\\u4EE5\\u540E\\u5BFC\\u5165\\u3002\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"\\u5BFC\\u51FA\\u5230 Excalidraw+\",\n      description: \"\\u5C06\\u753B\\u5E03\\u4FDD\\u5B58\\u5230\\u60A8\\u7684 Excalidraw+ \\u5DE5\\u4F5C\\u533A\\u3002\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\\u4ECE\\u6587\\u4EF6\\u52A0\\u8F7D\",\n      button: \"\\u4ECE\\u6587\\u4EF6\\u52A0\\u8F7D\",\n      description: \"\\u4ECE\\u6587\\u4EF6\\u52A0\\u8F7D\\u5C06<bold>\\u66FF\\u6362\\u60A8\\u73B0\\u6709\\u7684\\u5185\\u5BB9</bold>\\u3002<br></br>\\u60A8\\u53EF\\u4EE5\\u5148\\u4F7F\\u7528\\u4E0B\\u5217\\u65B9\\u5F0F\\u5907\\u4EFD\\u60A8\\u7684\\u7ED8\\u56FE\\u3002\"\n    },\n    shareableLink: {\n      title: \"\\u4ECE\\u94FE\\u63A5\\u52A0\\u8F7D\",\n      button: \"\\u66FF\\u6362\\u6211\\u7684\\u5185\\u5BB9\",\n      description: \"\\u52A0\\u8F7D\\u5916\\u90E8\\u7ED8\\u56FE\\u5C06<bold>\\u66FF\\u6362\\u60A8\\u73B0\\u6709\\u7684\\u5185\\u5BB9</bold>\\u3002<br></br>\\u60A8\\u53EF\\u4EE5\\u5148\\u4F7F\\u7528\\u4E0B\\u5217\\u65B9\\u5F0F\\u5907\\u4EFD\\u60A8\\u7684\\u7ED8\\u56FE\\u3002\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid \\u81F3 Excalidraw\",\n  button: \"\\u63D2\\u5165\",\n  description: \"\\u76EE\\u524D\\u4EC5\\u652F\\u6301<flowchartLink>\\u6D41\\u7A0B\\u56FE</flowchartLink>\\u3001<sequenceLink>\\u5E8F\\u5217\\u56FE</sequenceLink>\\u548C<classLink>\\u7C7B\\u56FE</classLink>\\u3002\\u5176\\u4ED6\\u7C7B\\u578B\\u5728 Excalidraw \\u4E2D\\u5C06\\u4EE5\\u56FE\\u50CF\\u5448\\u73B0\\u3002\",\n  syntax: \"Mermaid \\u8BED\\u6CD5\",\n  preview: \"\\u9884\\u89C8\"\n};\nvar zh_CN_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=zh-CN-4MXUOFTH.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvemgtQ04tNE1YVU9GVEguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELFlBQVk7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyRkFBMkYsUUFBUTtBQUNuRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnR0FBZ0csU0FBUztBQUN6RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsU0FBUztBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxZQUFZO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLGtCQUFrQiwwREFBMEQsbUJBQW1CO0FBQ3ZJO0FBQ0EsNENBQTRDLFNBQVM7QUFDckQ7QUFDQTtBQUNBLHdDQUF3QyxXQUFXO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBOEJFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvQGV4Y2FsaWRyYXcvZXhjYWxpZHJhdy9kaXN0L2Rldi9sb2NhbGVzL3poLUNOLTRNWFVPRlRILmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4uL2NodW5rLVhERkNVVVQ2LmpzXCI7XG5cbi8vIGxvY2FsZXMvemgtQ04uanNvblxudmFyIGxhYmVscyA9IHtcbiAgcGFzdGU6IFwiXFx1N0M5OFxcdThEMzRcIixcbiAgcGFzdGVBc1BsYWludGV4dDogXCJcXHU3Qzk4XFx1OEQzNFxcdTRFM0FcXHU3RUFGXFx1NjU4N1xcdTY3MkNcIixcbiAgcGFzdGVDaGFydHM6IFwiXFx1N0M5OFxcdThEMzRcXHU1NkZFXFx1ODg2OFwiLFxuICBzZWxlY3RBbGw6IFwiXFx1NTE2OFxcdTkwRThcXHU5MDA5XFx1NEUyRFwiLFxuICBtdWx0aVNlbGVjdDogXCJcXHU2REZCXFx1NTJBMFxcdTUxNDNcXHU3RDIwXFx1NTIzMFxcdTkwMDlcXHU1MzNBXCIsXG4gIG1vdmVDYW52YXM6IFwiXFx1NzlGQlxcdTUyQThcXHU3NTNCXFx1NUUwM1wiLFxuICBjdXQ6IFwiXFx1NTI2QVxcdTUyMDdcIixcbiAgY29weTogXCJcXHU2MkY3XFx1OEQxRFwiLFxuICBjb3B5QXNQbmc6IFwiXFx1NTkwRFxcdTUyMzZcXHU0RTNBIFBORyBcXHU1MjMwXFx1NTI2QVxcdThEMzRcXHU2NzdGXCIsXG4gIGNvcHlBc1N2ZzogXCJcXHU1OTBEXFx1NTIzNlxcdTRFM0EgU1ZHIFxcdTUyMzBcXHU1MjZBXFx1OEQzNFxcdTY3N0ZcIixcbiAgY29weVRleHQ6IFwiXFx1NTkwRFxcdTUyMzZcXHU2NTg3XFx1NjcyQ1xcdTUyMzBcXHU1MjZBXFx1OEQzNFxcdTY3N0ZcIixcbiAgY29weVNvdXJjZTogXCJcXHU1OTBEXFx1NTIzNlxcdTZFOTBcXHU3ODAxXFx1NTIzMFxcdTUyNkFcXHU4RDM0XFx1Njc3RlwiLFxuICBjb252ZXJ0VG9Db2RlOiBcIlxcdThGNkNcXHU2MzYyXFx1NjIxMFxcdTRFRTNcXHU3ODAxXCIsXG4gIGJyaW5nRm9yd2FyZDogXCJcXHU0RTBBXFx1NzlGQlxcdTRFMDBcXHU1QzQyXCIsXG4gIHNlbmRUb0JhY2s6IFwiXFx1N0Y2RVxcdTRFOEVcXHU1RTk1XFx1NUM0MlwiLFxuICBicmluZ1RvRnJvbnQ6IFwiXFx1N0Y2RVxcdTRFOEVcXHU5ODc2XFx1NUM0MlwiLFxuICBzZW5kQmFja3dhcmQ6IFwiXFx1NEUwQlxcdTc5RkJcXHU0RTAwXFx1NUM0MlwiLFxuICBkZWxldGU6IFwiXFx1NTIyMFxcdTk2NjRcIixcbiAgY29weVN0eWxlczogXCJcXHU2MkY3XFx1OEQxRFxcdTY4MzdcXHU1RjBGXCIsXG4gIHBhc3RlU3R5bGVzOiBcIlxcdTdDOThcXHU4RDM0XFx1NjgzN1xcdTVGMEZcIixcbiAgc3Ryb2tlOiBcIlxcdTYzQ0ZcXHU4RkI5XCIsXG4gIGJhY2tncm91bmQ6IFwiXFx1ODBDQ1xcdTY2NkZcIixcbiAgZmlsbDogXCJcXHU1ODZCXFx1NTE0NVwiLFxuICBzdHJva2VXaWR0aDogXCJcXHU2M0NGXFx1OEZCOVxcdTVCQkRcXHU1RUE2XCIsXG4gIHN0cm9rZVN0eWxlOiBcIlxcdThGQjlcXHU2ODQ2XFx1NjgzN1xcdTVGMEZcIixcbiAgc3Ryb2tlU3R5bGVfc29saWQ6IFwiXFx1NUI5RVxcdTdFQkZcIixcbiAgc3Ryb2tlU3R5bGVfZGFzaGVkOiBcIlxcdTg2NUFcXHU3RUJGXCIsXG4gIHN0cm9rZVN0eWxlX2RvdHRlZDogXCJcXHU3MEI5XFx1ODY1QVxcdTdFQkZcIixcbiAgc2xvcHBpbmVzczogXCJcXHU3RUJGXFx1Njc2MVxcdTk4Q0VcXHU2ODNDXCIsXG4gIG9wYWNpdHk6IFwiXFx1OTAwRlxcdTY2MEVcXHU1RUE2XCIsXG4gIHRleHRBbGlnbjogXCJcXHU2NTg3XFx1NjcyQ1xcdTVCRjlcXHU5RjUwXCIsXG4gIGVkZ2VzOiBcIlxcdThGQjlcXHU4OUQyXCIsXG4gIHNoYXJwOiBcIlxcdTVDMTZcXHU5NTEwXCIsXG4gIHJvdW5kOiBcIlxcdTU3MDZcXHU2REE2XCIsXG4gIGFycm93aGVhZHM6IFwiXFx1N0FFRlxcdTcwQjlcIixcbiAgYXJyb3doZWFkX25vbmU6IFwiXFx1NjVFMFwiLFxuICBhcnJvd2hlYWRfYXJyb3c6IFwiXFx1N0JBRFxcdTU5MzRcIixcbiAgYXJyb3doZWFkX2JhcjogXCJcXHU2NzYxXFx1NzJCNlwiLFxuICBhcnJvd2hlYWRfY2lyY2xlOiBcIlxcdTU3MDZcXHU3MEI5XCIsXG4gIGFycm93aGVhZF9jaXJjbGVfb3V0bGluZTogXCJcXHU1NzA2XFx1NzBCOVxcdUZGMDhcXHU3QTdBXFx1NUZDM1xcdUZGMDlcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlOiBcIlxcdTRFMDlcXHU4OUQyXFx1N0JBRFxcdTU5MzRcIixcbiAgYXJyb3doZWFkX3RyaWFuZ2xlX291dGxpbmU6IFwiXFx1NEUwOVxcdTg5RDJcXHU3QkFEXFx1NTkzNFxcdUZGMDhcXHU3QTdBXFx1NUZDM1xcdUZGMDlcIixcbiAgYXJyb3doZWFkX2RpYW1vbmQ6IFwiXFx1ODNGMVxcdTVGNjJcIixcbiAgYXJyb3doZWFkX2RpYW1vbmRfb3V0bGluZTogXCJcXHU4M0YxXFx1NUY2MlxcdUZGMDhcXHU3QTdBXFx1NUZDM1xcdUZGMDlcIixcbiAgZm9udFNpemU6IFwiXFx1NUI1N1xcdTRGNTNcXHU1OTI3XFx1NUMwRlwiLFxuICBmb250RmFtaWx5OiBcIlxcdTVCNTdcXHU0RjUzXCIsXG4gIGFkZFdhdGVybWFyazogXCJcXHU2REZCXFx1NTJBMCBcXHUyMDFDXFx1NEY3RlxcdTc1MjggRXhjYWxpZHJhdyBcXHU1MjFCXFx1NUVGQVxcdTIwMUQgXFx1NkMzNFxcdTUzNzBcIixcbiAgaGFuZERyYXduOiBcIlxcdTYyNEJcXHU1MTk5XCIsXG4gIG5vcm1hbDogXCJcXHU2NjZFXFx1OTAxQVwiLFxuICBjb2RlOiBcIlxcdTRFRTNcXHU3ODAxXCIsXG4gIHNtYWxsOiBcIlxcdTVDMEZcIixcbiAgbWVkaXVtOiBcIlxcdTRFMkRcIixcbiAgbGFyZ2U6IFwiXFx1NTkyN1wiLFxuICB2ZXJ5TGFyZ2U6IFwiXFx1NTJBMFxcdTU5MjdcIixcbiAgc29saWQ6IFwiXFx1NUI5RVxcdTVGQzNcIixcbiAgaGFjaHVyZTogXCJcXHU3RUJGXFx1Njc2MVwiLFxuICB6aWd6YWc6IFwiXFx1NEU0QlxcdTVCNTdcXHU1RjYyXFx1NjI5OFxcdTdFQkZcIixcbiAgY3Jvc3NIYXRjaDogXCJcXHU0RUE0XFx1NTNDOVxcdTdFQkZcXHU2NzYxXCIsXG4gIHRoaW46IFwiXFx1N0VDNlwiLFxuICBib2xkOiBcIlxcdTdDOTdcIixcbiAgbGVmdDogXCJcXHU1REU2XFx1NUJGOVxcdTlGNTBcIixcbiAgY2VudGVyOiBcIlxcdTVDNDVcXHU0RTJEXCIsXG4gIHJpZ2h0OiBcIlxcdTUzRjNcXHU1QkY5XFx1OUY1MFwiLFxuICBleHRyYUJvbGQ6IFwiXFx1NzI3OVxcdTdDOTdcIixcbiAgYXJjaGl0ZWN0OiBcIlxcdTY3MzRcXHU3RDIwXCIsXG4gIGFydGlzdDogXCJcXHU4MjdBXFx1NjcyRlwiLFxuICBjYXJ0b29uaXN0OiBcIlxcdTZGMkJcXHU3NTNCXFx1NUJCNlwiLFxuICBmaWxlVGl0bGU6IFwiXFx1NjU4N1xcdTRFRjZcXHU1NDBEXCIsXG4gIGNvbG9yUGlja2VyOiBcIlxcdTUzRDZcXHU4MjcyXFx1NTY2OFwiLFxuICBjYW52YXNDb2xvcnM6IFwiXFx1NzUzQlxcdTVFMDNcXHU0RTBBXFx1NzY4NFwiLFxuICBjYW52YXNCYWNrZ3JvdW5kOiBcIlxcdTc1M0JcXHU1RTAzXFx1ODBDQ1xcdTY2NkZcIixcbiAgZHJhd2luZ0NhbnZhczogXCJcXHU3RUQ4XFx1NTIzNiBDYW52YXNcIixcbiAgbGF5ZXJzOiBcIlxcdTU2RkVcXHU1QzQyXCIsXG4gIGFjdGlvbnM6IFwiXFx1NjRDRFxcdTRGNUNcIixcbiAgbGFuZ3VhZ2U6IFwiXFx1OEJFRFxcdThBMDBcIixcbiAgbGl2ZUNvbGxhYm9yYXRpb246IFwiXFx1NUI5RVxcdTY1RjZcXHU1MzRGXFx1NEY1Qy4uLlwiLFxuICBkdXBsaWNhdGVTZWxlY3Rpb246IFwiXFx1NTkwRFxcdTUyMzZcIixcbiAgdW50aXRsZWQ6IFwiXFx1NjVFMFxcdTY4MDdcXHU5ODk4XCIsXG4gIG5hbWU6IFwiXFx1NTQwRFxcdTVCNTdcIixcbiAgeW91ck5hbWU6IFwiXFx1NjBBOFxcdTc2ODRcXHU1OUQzXFx1NTQwRFwiLFxuICBtYWRlV2l0aEV4Y2FsaWRyYXc6IFwiXFx1NEY3RlxcdTc1MjggRXhjYWxpZHJhdyBcXHU1MjFCXFx1NUVGQVwiLFxuICBncm91cDogXCJcXHU3RjE2XFx1N0VDNFwiLFxuICB1bmdyb3VwOiBcIlxcdTg5RTNcXHU5NjY0XFx1N0YxNlxcdTdFQzRcIixcbiAgY29sbGFib3JhdG9yczogXCJcXHU1MzRGXFx1NEY1Q1xcdTgwMDVcIixcbiAgc2hvd0dyaWQ6IFwiXFx1NjYzRVxcdTc5M0FcXHU3RjUxXFx1NjgzQ1wiLFxuICBhZGRUb0xpYnJhcnk6IFwiXFx1NkRGQlxcdTUyQTBcXHU1MjMwXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFwiLFxuICByZW1vdmVGcm9tTGlicmFyeTogXCJcXHU0RUNFXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFxcdTc5RkJcXHU5NjY0XCIsXG4gIGxpYnJhcnlMb2FkaW5nTWVzc2FnZTogXCJcXHU2QjYzXFx1NTcyOFxcdTUyQTBcXHU4RjdEXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1MjAyNlwiLFxuICBsaWJyYXJpZXM6IFwiXFx1NkQ0RlxcdTg5QzhcXHU3RDIwXFx1Njc1MFxcdTVFOTNcIixcbiAgbG9hZGluZ1NjZW5lOiBcIlxcdTZCNjNcXHU1NzI4XFx1NTJBMFxcdThGN0RcXHU3RUQ4XFx1NTZGRVxcdTIwMjZcIixcbiAgYWxpZ246IFwiXFx1NUJGOVxcdTlGNTBcIixcbiAgYWxpZ25Ub3A6IFwiXFx1OTg3NlxcdTkwRThcXHU1QkY5XFx1OUY1MFwiLFxuICBhbGlnbkJvdHRvbTogXCJcXHU1RTk1XFx1N0FFRlxcdTVCRjlcXHU5RjUwXCIsXG4gIGFsaWduTGVmdDogXCJcXHU1REU2XFx1NUJGOVxcdTlGNTBcIixcbiAgYWxpZ25SaWdodDogXCJcXHU1M0YzXFx1NUJGOVxcdTlGNTBcIixcbiAgY2VudGVyVmVydGljYWxseTogXCJcXHU1NzgyXFx1NzZGNFxcdTVDNDVcXHU0RTJEXCIsXG4gIGNlbnRlckhvcml6b250YWxseTogXCJcXHU2QzM0XFx1NUU3M1xcdTVDNDVcXHU0RTJEXCIsXG4gIGRpc3RyaWJ1dGVIb3Jpem9udGFsbHk6IFwiXFx1NkMzNFxcdTVFNzNcXHU3QjQ5XFx1OERERFxcdTUyMDZcXHU1RTAzXCIsXG4gIGRpc3RyaWJ1dGVWZXJ0aWNhbGx5OiBcIlxcdTU3ODJcXHU3NkY0XFx1N0I0OVxcdThERERcXHU1MjA2XFx1NUUwM1wiLFxuICBmbGlwSG9yaXpvbnRhbDogXCJcXHU2QzM0XFx1NUU3M1xcdTdGRkJcXHU4RjZDXCIsXG4gIGZsaXBWZXJ0aWNhbDogXCJcXHU1NzgyXFx1NzZGNFxcdTdGRkJcXHU4RjZDXCIsXG4gIHZpZXdNb2RlOiBcIlxcdTY3RTVcXHU3NzBCXFx1NkEyMVxcdTVGMEZcIixcbiAgc2hhcmU6IFwiXFx1NTIwNlxcdTRFQUJcIixcbiAgc2hvd1N0cm9rZTogXCJcXHU2NjNFXFx1NzkzQVxcdTYzQ0ZcXHU4RkI5XFx1OTg5Q1xcdTgyNzJcXHU5MDA5XFx1NjJFOVxcdTU2NjhcIixcbiAgc2hvd0JhY2tncm91bmQ6IFwiXFx1NjYzRVxcdTc5M0FcXHU4MENDXFx1NjY2RlxcdTk4OUNcXHU4MjcyXFx1OTAwOVxcdTYyRTlcXHU1NjY4XCIsXG4gIHRvZ2dsZVRoZW1lOiBcIlxcdTUyMDdcXHU2MzYyXFx1NEUzQlxcdTk4OThcIixcbiAgcGVyc29uYWxMaWI6IFwiXFx1NEUyQVxcdTRFQkFcXHU3RDIwXFx1Njc1MFxcdTVFOTNcIixcbiAgZXhjYWxpZHJhd0xpYjogXCJFeGNhbGlkcmF3IFxcdTdEMjBcXHU2NzUwXFx1NUU5M1wiLFxuICBkZWNyZWFzZUZvbnRTaXplOiBcIlxcdTdGMjlcXHU1QzBGXFx1NUI1N1xcdTRGNTNcXHU1OTI3XFx1NUMwRlwiLFxuICBpbmNyZWFzZUZvbnRTaXplOiBcIlxcdTY1M0VcXHU1OTI3XFx1NUI1N1xcdTRGNTNcXHU1OTI3XFx1NUMwRlwiLFxuICB1bmJpbmRUZXh0OiBcIlxcdTUzRDZcXHU2RDg4XFx1NjU4N1xcdTY3MkNcXHU3RUQxXFx1NUI5QVwiLFxuICBiaW5kVGV4dDogXCJcXHU1QzA2XFx1NjU4N1xcdTY3MkNcXHU3RUQxXFx1NUI5QVxcdTUyMzBcXHU1QkI5XFx1NTY2OFwiLFxuICBjcmVhdGVDb250YWluZXJGcm9tVGV4dDogXCJcXHU1QzA2XFx1NjU4N1xcdTY3MkNcXHU1MzA1XFx1NTZGNFxcdTU3MjhcXHU1QkI5XFx1NTY2OFxcdTRFMkRcIixcbiAgbGluazoge1xuICAgIGVkaXQ6IFwiXFx1N0YxNlxcdThGOTFcXHU5NEZFXFx1NjNBNVwiLFxuICAgIGVkaXRFbWJlZDogXCJcXHU3RjE2XFx1OEY5MVxcdTk0RkVcXHU2M0E1XFx1NEUwRVxcdTVENENcXHU1MTY1XCIsXG4gICAgY3JlYXRlOiBcIlxcdTY1QjBcXHU1RUZBXFx1OTRGRVxcdTYzQTVcIixcbiAgICBjcmVhdGVFbWJlZDogXCJcXHU1MjFCXFx1NUVGQVxcdTk0RkVcXHU2M0E1XFx1NEUwRVxcdTVENENcXHU1MTY1XCIsXG4gICAgbGFiZWw6IFwiXFx1OTRGRVxcdTYzQTVcIixcbiAgICBsYWJlbEVtYmVkOiBcIlxcdTk0RkVcXHU2M0E1XFx1NEUwRVxcdTVENENcXHU1MTY1XCIsXG4gICAgZW1wdHk6IFwiXFx1NjcyQVxcdThCQkVcXHU1QjlBXFx1OTRGRVxcdTYzQTVcIlxuICB9LFxuICBsaW5lRWRpdG9yOiB7XG4gICAgZWRpdDogXCJcXHU3RjE2XFx1OEY5MVxcdTdFQkZcXHU2NzYxXCIsXG4gICAgZXhpdDogXCJcXHU5MDAwXFx1NTFGQVxcdTdFQkZcXHU2NzYxXFx1N0YxNlxcdThGOTFcIlxuICB9LFxuICBlbGVtZW50TG9jazoge1xuICAgIGxvY2s6IFwiXFx1OTUwMVxcdTVCOUFcIixcbiAgICB1bmxvY2s6IFwiXFx1ODlFM1xcdTk2NjRcXHU5NTAxXFx1NUI5QVwiLFxuICAgIGxvY2tBbGw6IFwiXFx1NTE2OFxcdTkwRThcXHU5NTAxXFx1NUI5QVwiLFxuICAgIHVubG9ja0FsbDogXCJcXHU1MTY4XFx1OTBFOFxcdTg5RTNcXHU5NTAxXCJcbiAgfSxcbiAgc3RhdHVzUHVibGlzaGVkOiBcIlxcdTVERjJcXHU1M0QxXFx1NUUwM1wiLFxuICBzaWRlYmFyTG9jazogXCJcXHU0RkE3XFx1OEZCOVxcdTY4MEZcXHU1RTM4XFx1OUE3QlwiLFxuICBzZWxlY3RBbGxFbGVtZW50c0luRnJhbWU6IFwiXFx1OTAwOVxcdTYyRTlcXHU3NTNCXFx1Njg0NlxcdTRFMkRcXHU3Njg0XFx1NjI0MFxcdTY3MDlcXHU1MTQzXFx1N0QyMFwiLFxuICByZW1vdmVBbGxFbGVtZW50c0Zyb21GcmFtZTogXCJcXHU1MjA2XFx1NzlCQlxcdTUxRkFcXHU3NTNCXFx1Njg0NlxcdTRFMkRcXHU3Njg0XFx1NjI0MFxcdTY3MDlcXHU1MTQzXFx1N0QyMFwiLFxuICBleWVEcm9wcGVyOiBcIlxcdTRFQ0VcXHU3NTNCXFx1NUUwM1xcdTRFMEFcXHU1M0Q2XFx1ODI3MlwiLFxuICB0ZXh0VG9EaWFncmFtOiBcIlxcdTY1ODdcXHU1QjU3XFx1ODFGM1xcdTU2RkVcXHU4ODY4XCIsXG4gIHByb21wdDogXCJQcm9tcHRcIlxufTtcbnZhciBsaWJyYXJ5ID0ge1xuICBub0l0ZW1zOiBcIlxcdTVDMUFcXHU2NzJBXFx1NkRGQlxcdTUyQTBcXHU0RUZCXFx1NEY1NVxcdTk4NzlcXHU3NkVFXFx1MjAyNlxcdTIwMjZcIixcbiAgaGludF9lbXB0eUxpYnJhcnk6IFwiXFx1OTAwOVxcdTRFMkRcXHU3NTNCXFx1NUUwM1xcdTRFMEFcXHU3Njg0XFx1OTg3OVxcdTc2RUVcXHU2REZCXFx1NTJBMFxcdTUyMzBcXHU2QjY0XFx1NTkwNFxcdUZGMENcXHU2MjE2XFx1NEVDRVxcdTRFMEJcXHU2NUI5XFx1NzY4NFxcdTUxNkNcXHU1MTcxXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFxcdTVCRkNcXHU1MTY1XFx1MzAwMlwiLFxuICBoaW50X2VtcHR5UHJpdmF0ZUxpYnJhcnk6IFwiXFx1OTAwOVxcdTRFMkRcXHU3NTNCXFx1NUUwM1xcdTRFMEFcXHU3Njg0XFx1OTg3OVxcdTc2RUVcXHU2REZCXFx1NTJBMFxcdTUyMzBcXHU2QjY0XFx1NTkwNFxcdTMwMDJcIlxufTtcbnZhciBidXR0b25zID0ge1xuICBjbGVhclJlc2V0OiBcIlxcdTkxQ0RcXHU3RjZFXFx1NzUzQlxcdTVFMDNcIixcbiAgZXhwb3J0SlNPTjogXCJcXHU1QkZDXFx1NTFGQVxcdTRFM0FcXHU2NTg3XFx1NEVGNlwiLFxuICBleHBvcnRJbWFnZTogXCJcXHU1QkZDXFx1NTFGQVxcdTU2RkVcXHU3MjQ3Li4uXCIsXG4gIGV4cG9ydDogXCJcXHU0RkREXFx1NUI1OFxcdTUyMzAuLi5cIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIlxcdTU5MERcXHU1MjM2XFx1NTIzMFxcdTUyNkFcXHU4RDM0XFx1Njc3RlwiLFxuICBzYXZlOiBcIlxcdTRGRERcXHU1QjU4XFx1ODFGM1xcdTVGNTNcXHU1MjREXFx1NjU4N1xcdTRFRjZcIixcbiAgc2F2ZUFzOiBcIlxcdTRGRERcXHU1QjU4XFx1NEUzQVwiLFxuICBsb2FkOiBcIlxcdTYyNTNcXHU1RjAwXCIsXG4gIGdldFNoYXJlYWJsZUxpbms6IFwiXFx1ODNCN1xcdTUzRDZcXHU1MTcxXFx1NEVBQlxcdTk0RkVcXHU2M0E1XCIsXG4gIGNsb3NlOiBcIlxcdTUxNzNcXHU5NUVEXCIsXG4gIHNlbGVjdExhbmd1YWdlOiBcIlxcdTkwMDlcXHU2MkU5XFx1OEJFRFxcdThBMDBcIixcbiAgc2Nyb2xsQmFja1RvQ29udGVudDogXCJcXHU2RURBXFx1NTJBOFxcdTU2REVcXHU1MjMwXFx1NTE4NVxcdTVCQjlcIixcbiAgem9vbUluOiBcIlxcdTY1M0VcXHU1OTI3XCIsXG4gIHpvb21PdXQ6IFwiXFx1N0YyOVxcdTVDMEZcIixcbiAgcmVzZXRab29tOiBcIlxcdTkxQ0RcXHU3RjZFXFx1N0YyOVxcdTY1M0VcIixcbiAgbWVudTogXCJcXHU4M0RDXFx1NTM1NVwiLFxuICBkb25lOiBcIlxcdTVCOENcXHU2MjEwXCIsXG4gIGVkaXQ6IFwiXFx1N0YxNlxcdThGOTFcIixcbiAgdW5kbzogXCJcXHU2NEE0XFx1OTUwMFwiLFxuICByZWRvOiBcIlxcdTkxQ0RcXHU1MDVBXCIsXG4gIHJlc2V0TGlicmFyeTogXCJcXHU5MUNEXFx1N0Y2RVxcdTdEMjBcXHU2NzUwXFx1NUU5M1wiLFxuICBjcmVhdGVOZXdSb29tOiBcIlxcdTY1QjBcXHU1RUZBXFx1NEYxQVxcdThCQUVcXHU1QkE0XCIsXG4gIGZ1bGxTY3JlZW46IFwiXFx1NTE2OFxcdTVDNEZcIixcbiAgZGFya01vZGU6IFwiXFx1NkRGMVxcdTgyNzJcXHU2QTIxXFx1NUYwRlwiLFxuICBsaWdodE1vZGU6IFwiXFx1NkQ0NVxcdTgyNzJcXHU2QTIxXFx1NUYwRlwiLFxuICB6ZW5Nb2RlOiBcIlxcdTc5ODVcXHU2QTIxXFx1NUYwRlwiLFxuICBvYmplY3RzU25hcE1vZGU6IFwiXFx1NTQzOFxcdTk2NDRcXHU4MUYzXFx1NUJGOVxcdThDNjFcIixcbiAgZXhpdFplbk1vZGU6IFwiXFx1OTAwMFxcdTUxRkFcXHU3OTg1XFx1NkEyMVxcdTVGMEZcIixcbiAgY2FuY2VsOiBcIlxcdTUzRDZcXHU2RDg4XCIsXG4gIGNsZWFyOiBcIlxcdTZFMDVcXHU5NjY0XCIsXG4gIHJlbW92ZTogXCJcXHU1MjIwXFx1OTY2NFwiLFxuICBlbWJlZDogXCJcXHU1MjA3XFx1NjM2MlxcdTVENENcXHU1MTY1XCIsXG4gIHB1Ymxpc2hMaWJyYXJ5OiBcIlxcdTUzRDFcXHU1RTAzXCIsXG4gIHN1Ym1pdDogXCJcXHU2M0QwXFx1NEVBNFwiLFxuICBjb25maXJtOiBcIlxcdTc4NkVcXHU1QjlBXCIsXG4gIGVtYmVkZGFibGVJbnRlcmFjdGlvbkJ1dHRvbjogXCJcXHU3MEI5XFx1NTFGQlxcdTRFRTVcXHU1RjAwXFx1NTlDQlxcdTRFQTRcXHU0RTkyXCJcbn07XG52YXIgYWxlcnRzID0ge1xuICBjbGVhclJlc2V0OiBcIlxcdThGRDlcXHU1QzA2XFx1NEYxQVxcdTZFMDVcXHU5NjY0XFx1NjU3NFxcdTRFMkFcXHU3NTNCXFx1NUUwM1xcdTMwMDJcXHU2MEE4XFx1NjYyRlxcdTU0MjZcXHU4OTgxXFx1N0VFN1xcdTdFRUQ/XCIsXG4gIGNvdWxkTm90Q3JlYXRlU2hhcmVhYmxlTGluazogXCJcXHU2NUUwXFx1NkNENVxcdTUyMUJcXHU1RUZBXFx1NTE3MVxcdTRFQUJcXHU5NEZFXFx1NjNBNVwiLFxuICBjb3VsZE5vdENyZWF0ZVNoYXJlYWJsZUxpbmtUb29CaWc6IFwiXFx1NjVFMFxcdTZDRDVcXHU1MjFCXFx1NUVGQVxcdTUzRUZcXHU1MTcxXFx1NEVBQlxcdTk0RkVcXHU2M0E1XFx1RkYxQVxcdTc1M0JcXHU1RTAzXFx1OEZDN1xcdTU5MjdcIixcbiAgY291bGROb3RMb2FkSW52YWxpZEZpbGU6IFwiXFx1NjVFMFxcdTZDRDVcXHU1MkEwXFx1OEY3RFxcdTY1RTBcXHU2NTQ4XFx1NzY4NFxcdTY1ODdcXHU0RUY2XCIsXG4gIGltcG9ydEJhY2tlbmRGYWlsZWQ6IFwiXFx1NEVDRVxcdTU0MEVcXHU3QUVGXFx1NUJGQ1xcdTUxNjVcXHU1OTMxXFx1OEQyNVxcdTMwMDJcIixcbiAgY2Fubm90RXhwb3J0RW1wdHlDYW52YXM6IFwiXFx1NjVFMFxcdTZDRDVcXHU1QkZDXFx1NTFGQVxcdTdBN0FcXHU3NjdEXFx1NzUzQlxcdTVFMDNcXHUzMDAyXCIsXG4gIGNvdWxkTm90Q29weVRvQ2xpcGJvYXJkOiBcIlxcdTY1RTBcXHU2Q0Q1XFx1NTkwRFxcdTUyMzZcXHU1MjMwXFx1NTI2QVxcdThEMzRcXHU2NzdGXFx1MzAwMlwiLFxuICBkZWNyeXB0RmFpbGVkOiBcIlxcdTY1RTBcXHU2Q0Q1XFx1ODlFM1xcdTVCQzZcXHU2NTcwXFx1NjM2RVxcdTMwMDJcIixcbiAgdXBsb2FkZWRTZWN1cmx5OiBcIlxcdTRFMEFcXHU0RjIwXFx1NURGMlxcdTg4QUJcXHU3QUVGXFx1NTIzMFxcdTdBRUZcXHU1MkEwXFx1NUJDNlxcdTRGRERcXHU2MkE0XFx1RkYwQ1xcdThGRDlcXHU2MTBGXFx1NTQ3M1xcdTc3NDAgRXhjYWxpZHJhdyBcXHU3Njg0XFx1NjcwRFxcdTUyQTFcXHU1NjY4XFx1NTQ4Q1xcdTdCMkNcXHU0RTA5XFx1NjVCOVxcdTkwRkRcXHU2NUUwXFx1NkNENVxcdThCRkJcXHU1M0Q2XFx1NTE4NVxcdTVCQjlcXHUzMDAyXCIsXG4gIGxvYWRTY2VuZU92ZXJyaWRlUHJvbXB0OiBcIlxcdTUyQTBcXHU4RjdEXFx1NTkxNlxcdTkwRThcXHU3RUQ4XFx1NTZGRVxcdTVDMDZcXHU1M0Q2XFx1NEVFM1xcdTYwQThcXHU3M0IwXFx1NjcwOVxcdTc2ODRcXHU1MTg1XFx1NUJCOVxcdTMwMDJcXHU2MEE4XFx1NjBGM1xcdTg5ODFcXHU3RUU3XFx1N0VFRFxcdTU0MTdcXHVGRjFGXCIsXG4gIGNvbGxhYlN0b3BPdmVycmlkZVByb21wdDogXCJcXHU1MDVDXFx1NkI2MlxcdTRGMUFcXHU4QkREXFx1NUMwNlxcdTg5ODZcXHU3NkQ2XFx1NjBBOFxcdTUxNDhcXHU1MjREXFx1NjcyQ1xcdTU3MzBcXHU1QjU4XFx1NTBBOFxcdTc2ODRcXHU3RUQ4XFx1NTZGRVxcdTMwMDIgXFx1NjBBOFxcdTc4NkVcXHU1QjlBXFx1NTQxN1xcdUZGMUZcXG5cXG4oXFx1NTk4MlxcdTY3OUNcXHU2MEE4XFx1NjBGM1xcdTRGRERcXHU2MzAxXFx1NjcyQ1xcdTU3MzBcXHU3RUQ4XFx1NTZGRVxcdUZGMENcXHU1M0VBXFx1OTcwMFxcdTUxNzNcXHU5NUVEXFx1NkQ0RlxcdTg5QzhcXHU1NjY4XFx1OTAwOVxcdTk4NzlcXHU1MzYxXFx1MzAwMilcIixcbiAgZXJyb3JBZGRpbmdUb0xpYnJhcnk6IFwiXFx1NjVFMFxcdTZDRDVcXHU1QzA2XFx1OTg3OVxcdTc2RUVcXHU2REZCXFx1NTJBMFxcdTUyMzBcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU0RTJEXCIsXG4gIGVycm9yUmVtb3ZpbmdGcm9tTGlicmFyeTogXCJcXHU2NUUwXFx1NkNENVxcdTRFQ0VcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU0RTJEXFx1NzlGQlxcdTk2NjRcXHU5ODc5XFx1NzZFRVwiLFxuICBjb25maXJtQWRkTGlicmFyeTogXCJcXHU4RkQ5XFx1NUMwNlxcdTZERkJcXHU1MkEwIHt7bnVtU2hhcGVzfX0gXFx1NEUyQVxcdTVGNjJcXHU3MkI2XFx1NTIzMFxcdTYwQThcXHU3Njg0XFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFxcdTMwMDJcXHU2MEE4XFx1Nzg2RVxcdTVCOUFcXHU1NDE3XFx1RkYxRlwiLFxuICBpbWFnZURvZXNOb3RDb250YWluU2NlbmU6IFwiXFx1NkI2NFxcdTU2RkVcXHU1MENGXFx1NEYzQ1xcdTRFNEVcXHU0RTBEXFx1NTMwNVxcdTU0MkJcXHU0RUZCXFx1NEY1NVxcdTc1M0JcXHU1RTAzXFx1NjU3MFxcdTYzNkVcXHUzMDAyXFx1NjBBOFxcdTY2MkZcXHU1NDI2XFx1NTcyOFxcdTVCRkNcXHU1MUZBXFx1NjVGNlxcdTU0MkZcXHU3NTI4XFx1NEU4NlxcdTc1M0JcXHU1RTAzXFx1NUQ0Q1xcdTUxNjVcXHU1MjlGXFx1ODBGRFxcdUZGMUZcIixcbiAgY2Fubm90UmVzdG9yZUZyb21JbWFnZTogXCJcXHU2NUUwXFx1NkNENVxcdTRFQ0VcXHU2QjY0XFx1NTZGRVxcdTUwQ0ZcXHU2NTg3XFx1NEVGNlxcdTYwNjJcXHU1OTBEXFx1NzUzQlxcdTVFMDNcIixcbiAgaW52YWxpZFNjZW5lVXJsOiBcIlxcdTY1RTBcXHU2Q0Q1XFx1NEVDRVxcdTYzRDBcXHU0RjlCXFx1NzY4NCBVUkwgXFx1NUJGQ1xcdTUxNjVcXHU1NzNBXFx1NjY2RlxcdTMwMDJcXHU1QjgzXFx1NjIxNlxcdTgwMDVcXHU2ODNDXFx1NUYwRlxcdTRFMERcXHU2QjYzXFx1Nzg2RVxcdUZGMENcXHU2MjE2XFx1ODAwNVxcdTRFMERcXHU1MzA1XFx1NTQyQlxcdTY3MDlcXHU2NTQ4XFx1NzY4NCBFeGNhbGlkcmF3IEpTT04gXFx1NjU3MFxcdTYzNkVcXHUzMDAyXCIsXG4gIHJlc2V0TGlicmFyeTogXCJcXHU4RkQ5XFx1NUMwNlxcdTRGMUFcXHU2RTA1XFx1OTY2NFxcdTRGNjBcXHU3Njg0XFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1MzAwMlxcdTRGNjBcXHU3ODZFXFx1NUI5QVxcdTg5ODFcXHU4RkQ5XFx1NEU0OFxcdTUwNUFcXHU1NDE3XFx1RkYxRlwiLFxuICByZW1vdmVJdGVtc0Zyb21zTGlicmFyeTogXCJcXHU3ODZFXFx1NUI5QVxcdTg5ODFcXHU0RUNFXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFxcdTUyMjBcXHU5NjY0IHt7Y291bnR9fSBcXHU0RTJBXFx1OTg3OVxcdTc2RUVcXHU1NDE3XFx1RkYxRlwiLFxuICBpbnZhbGlkRW5jcnlwdGlvbktleTogXCJcXHU1QkM2XFx1OTRBNVxcdTVGQzVcXHU5ODdCXFx1NTMwNVxcdTU0MkIyMlxcdTRFMkFcXHU1QjU3XFx1N0IyNlxcdTMwMDJcXHU1QjlFXFx1NjVGNlxcdTUzNEZcXHU0RjVDXFx1NURGMlxcdTg4QUJcXHU3OTgxXFx1NzUyOFxcdTMwMDJcIixcbiAgY29sbGFiT2ZmbGluZVdhcm5pbmc6IFwiXFx1NjVFMFxcdTdGNTFcXHU3RURDXFx1OEZERVxcdTYzQTVcXHUzMDAyXFxuXFx1NjBBOFxcdTc2ODRcXHU2NTM5XFx1NTJBOFxcdTVDMDZcXHU0RTBEXFx1NEYxQVxcdTg4QUJcXHU0RkREXFx1NUI1OFxcdUZGMDFcIlxufTtcbnZhciBlcnJvcnMgPSB7XG4gIHVuc3VwcG9ydGVkRmlsZVR5cGU6IFwiXFx1NEUwRFxcdTY1MkZcXHU2MzAxXFx1NzY4NFxcdTY1ODdcXHU0RUY2XFx1NjgzQ1xcdTVGMEZcXHUzMDAyXCIsXG4gIGltYWdlSW5zZXJ0RXJyb3I6IFwiXFx1NjVFMFxcdTZDRDVcXHU2M0QyXFx1NTE2NVxcdTU2RkVcXHU1MENGXFx1MzAwMlxcdThCRjdcXHU3QTBEXFx1NTQwRVxcdTUxOERcXHU4QkQ1XFx1MjAyNlxcdTIwMjZcIixcbiAgZmlsZVRvb0JpZzogXCJcXHU2NTg3XFx1NEVGNlxcdThGQzdcXHU1OTI3XFx1MzAwMlxcdTY3MDBcXHU1OTI3XFx1NTE0MVxcdThCQjhcXHU3Njg0XFx1NTkyN1xcdTVDMEZcXHU0RTNBIHt7bWF4U2l6ZX19XFx1MzAwMlwiLFxuICBzdmdJbWFnZUluc2VydEVycm9yOiBcIlxcdTY1RTBcXHU2Q0Q1XFx1NjNEMlxcdTUxNjUgU1ZHIFxcdTU2RkVcXHU1MENGXFx1MzAwMlxcdThCRTUgU1ZHIFxcdTY4MDdcXHU4QkIwXFx1NEYzQ1xcdTRFNEVcXHU2NjJGXFx1NjVFMFxcdTY1NDhcXHU3Njg0XFx1MzAwMlwiLFxuICBmYWlsZWRUb0ZldGNoSW1hZ2U6IFwiXFx1NjVFMFxcdTZDRDVcXHU4M0I3XFx1NTNENlxcdTU2RkVcXHU3MjQ3XFx1MzAwMlwiLFxuICBpbnZhbGlkU1ZHU3RyaW5nOiBcIlxcdTY1RTBcXHU2NTQ4XFx1NzY4NCBTVkdcXHUzMDAyXCIsXG4gIGNhbm5vdFJlc29sdmVDb2xsYWJTZXJ2ZXI6IFwiXFx1NjVFMFxcdTZDRDVcXHU4RkRFXFx1NjNBNVxcdTUyMzBcXHU1QjlFXFx1NjVGNlxcdTUzNEZcXHU0RjVDXFx1NjcwRFxcdTUyQTFcXHU1NjY4XFx1MzAwMlxcdThCRjdcXHU5MUNEXFx1NjVCMFxcdTUyQTBcXHU4RjdEXFx1OTg3NVxcdTk3NjJcXHU1RTc2XFx1OTFDRFxcdThCRDVcXHUzMDAyXCIsXG4gIGltcG9ydExpYnJhcnlFcnJvcjogXCJcXHU2NUUwXFx1NkNENVxcdTUyQTBcXHU4RjdEXFx1N0QyMFxcdTY3NTBcXHU1RTkzXCIsXG4gIGNvbGxhYlNhdmVGYWlsZWQ6IFwiXFx1NjVFMFxcdTZDRDVcXHU0RkREXFx1NUI1OFxcdTUyMzBcXHU1NDBFXFx1N0FFRlxcdTY1NzBcXHU2MzZFXFx1NUU5M1xcdTMwMDJcXHU1OTgyXFx1Njc5Q1xcdTk1RUVcXHU5ODk4XFx1NjMwMVxcdTdFRURcXHU1QjU4XFx1NTcyOFxcdUZGMENcXHU2MEE4XFx1NUU5NFxcdThCRTVcXHU0RkREXFx1NUI1OFxcdTY1ODdcXHU0RUY2XFx1NTIzMFxcdTY3MkNcXHU1NzMwXFx1RkYwQ1xcdTRFRTVcXHU3ODZFXFx1NEZERFxcdTYwQThcXHU3Njg0XFx1NURFNVxcdTRGNUNcXHU0RTBEXFx1NEYxQVxcdTRFMjJcXHU1OTMxXFx1MzAwMlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkX3NpemVFeGNlZWRlZDogXCJcXHU2NUUwXFx1NkNENVxcdTRGRERcXHU1QjU4XFx1NTIzMFxcdTU0MEVcXHU3QUVGXFx1NjU3MFxcdTYzNkVcXHU1RTkzXFx1RkYwQ1xcdTc1M0JcXHU1RTAzXFx1NEYzQ1xcdTRFNEVcXHU4RkM3XFx1NTkyN1xcdTMwMDJcXHU2MEE4XFx1NUU5NFxcdThCRTVcXHU0RkREXFx1NUI1OFxcdTY1ODdcXHU0RUY2XFx1NTIzMFxcdTY3MkNcXHU1NzMwXFx1RkYwQ1xcdTRFRTVcXHU3ODZFXFx1NEZERFxcdTYwQThcXHU3Njg0XFx1NURFNVxcdTRGNUNcXHU0RTBEXFx1NEYxQVxcdTRFMjJcXHU1OTMxXFx1MzAwMlwiLFxuICBpbWFnZVRvb2xOb3RTdXBwb3J0ZWQ6IFwiXFx1NTZGRVxcdTcyNDdcXHU1REYyXFx1ODhBQlxcdTc5ODFcXHU3NTI4XFx1MzAwMlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJcXHU2MEE4XFx1NEYzQ1xcdTRFNEVcXHU2QjYzXFx1NTcyOFxcdTRGN0ZcXHU3NTI4IEJyYXZlIFxcdTZENEZcXHU4OUM4XFx1NTY2OFxcdTVFNzZcXHU1NDJGXFx1NzUyOFxcdTRFODY8Ym9sZD5cXHU3OUVGXFx1Njc4MVxcdTk2M0JcXHU2QjYyXFx1NjMwN1xcdTdFQjlcXHU4QkM2XFx1NTIyQjwvYm9sZD5cXHU3Njg0XFx1OEJCRVxcdTdGNkVcXHUzMDAyXCIsXG4gICAgbGluZTI6IFwiXFx1OEZEOVxcdTUzRUZcXHU4MEZEXFx1NEYxQVxcdTc4MzRcXHU1NzRGXFx1N0VEOFxcdTU2RkVcXHU0RTJEXFx1NzY4NCA8Ym9sZD5cXHU2NTg3XFx1NjcyQ1xcdTUxNDNcXHU3RDIwPC9ib2xkPlxcdTMwMDJcIixcbiAgICBsaW5lMzogXCJcXHU2MjExXFx1NEVFQ1xcdTVGM0FcXHU3MEM4XFx1NUVGQVxcdThCQUVcXHU3OTgxXFx1NzUyOFxcdTZCNjRcXHU4QkJFXFx1N0Y2RVxcdTMwMDJcXHU2MEE4XFx1NTNFRlxcdTRFRTVcXHU2MzA5XFx1NzE2NzxsaW5rPlxcdThGRDlcXHU0RTlCXFx1NkI2NVxcdTlBQTQ8L2xpbms+XFx1Njc2NVxcdThCQkVcXHU3RjZFXFx1MzAwMlwiLFxuICAgIGxpbmU0OiBcIlxcdTU5ODJcXHU2NzlDXFx1Nzk4MVxcdTc1MjhcXHU2QjY0XFx1OEJCRVxcdTdGNkVcXHU2NUUwXFx1NkNENVxcdTRGRUVcXHU1OTBEXFx1NjU4N1xcdTY3MkNcXHU1MTQzXFx1N0QyMFxcdTc2ODRcXHU2NjNFXFx1NzkzQVxcdUZGMENcXHU4QkY3XFx1NTcyOCBHaXRIdWIgXFx1NEUwQVxcdTYzRDBcXHU0RUE0XFx1NEUwMFxcdTRFMkEgPGlzc3VlTGluaz5pc3N1ZTwvaXNzdWVMaW5rPiBcXHVGRjBDXFx1NjIxNlxcdTgwMDVcXHU1NzI4IDxkaXNjb3JkTGluaz5EaXNjb3JkPC9kaXNjb3JkTGluaz4gXFx1NEUwQVxcdTUzQ0RcXHU5OTg4XCJcbiAgfSxcbiAgbGlicmFyeUVsZW1lbnRUeXBlRXJyb3I6IHtcbiAgICBlbWJlZGRhYmxlOiBcIlxcdTVENENcXHU1MTY1XFx1NzY4NFxcdTUxNDNcXHU3RDIwXFx1NEUwRFxcdTgwRkRcXHU4OEFCXFx1NkRGQlxcdTUyQTBcXHU1MjMwXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1MzAwMlwiLFxuICAgIGlmcmFtZTogXCJcXHU0RTBEXFx1ODBGRFxcdTVDMDYgSUZyYW1lIFxcdTUxNDNcXHU3RDIwXFx1NkRGQlxcdTUyQTBcXHU1MjMwXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFxcdTMwMDJcIixcbiAgICBpbWFnZTogXCJcXHU2MjExXFx1NEVFQ1xcdTRFMERcXHU0RTQ1XFx1NUMwNlxcdTY1MkZcXHU2MzAxXFx1NkRGQlxcdTUyQTBcXHU1NkZFXFx1NzI0N1xcdTUyMzBcXHU3RDIwXFx1Njc1MFxcdTVFOTNcIlxuICB9LFxuICBhc3luY1Bhc3RlRmFpbGVkT25SZWFkOiBcIlxcdTY1RTBcXHU2Q0Q1XFx1N0M5OFxcdThEMzRcXHVGRjA4XFx1NjVFMFxcdTZDRDVcXHU4QkZCXFx1NTNENlxcdTdDRkJcXHU3RURGXFx1NTI2QVxcdThEMzRcXHU2NzdGXFx1RkYwOVxcdTMwMDJcIixcbiAgYXN5bmNQYXN0ZUZhaWxlZE9uUGFyc2U6IFwiXFx1NjVFMFxcdTZDRDVcXHU3Qzk4XFx1OEQzNFxcdTMwMDJcIixcbiAgY29weVRvU3lzdGVtQ2xpcGJvYXJkRmFpbGVkOiBcIlxcdTY1RTBcXHU2Q0Q1XFx1NTkwRFxcdTUyMzZcXHU1MjMwXFx1NTI2QVxcdThEMzRcXHU2NzdGXFx1MzAwMlwiXG59O1xudmFyIHRvb2xCYXIgPSB7XG4gIHNlbGVjdGlvbjogXCJcXHU5MDA5XFx1NjJFOVwiLFxuICBpbWFnZTogXCJcXHU2M0QyXFx1NTE2NVxcdTU2RkVcXHU1MENGXCIsXG4gIHJlY3RhbmdsZTogXCJcXHU3N0U5XFx1NUY2MlwiLFxuICBkaWFtb25kOiBcIlxcdTgzRjFcXHU1RjYyXCIsXG4gIGVsbGlwc2U6IFwiXFx1NjkyRFxcdTU3MDZcIixcbiAgYXJyb3c6IFwiXFx1N0JBRFxcdTU5MzRcIixcbiAgbGluZTogXCJcXHU3RUJGXFx1Njc2MVwiLFxuICBmcmVlZHJhdzogXCJcXHU4MUVBXFx1NzUzMVxcdTRFNjZcXHU1MTk5XCIsXG4gIHRleHQ6IFwiXFx1NjU4N1xcdTVCNTdcIixcbiAgbGlicmFyeTogXCJcXHU3RDIwXFx1Njc1MFxcdTVFOTNcIixcbiAgbG9jazogXCJcXHU3RUQ4XFx1NTIzNlxcdTU0MEVcXHU0RkREXFx1NjMwMVxcdTYyNDBcXHU5MDA5XFx1NzY4NFxcdTVERTVcXHU1MTc3XFx1NjgwRlxcdTcyQjZcXHU2MDAxXCIsXG4gIHBlbk1vZGU6IFwiXFx1N0IxNFxcdTZBMjFcXHU1RjBGIFxcdTIwMTMgXFx1OTA3RlxcdTUxNERcXHU4QkVGXFx1ODlFNlwiLFxuICBsaW5rOiBcIlxcdTRFM0FcXHU5MDA5XFx1NEUyRFxcdTc2ODRcXHU1RjYyXFx1NzJCNlxcdTZERkJcXHU1MkEwL1xcdTY2RjRcXHU2NUIwXFx1OTRGRVxcdTYzQTVcIixcbiAgZXJhc2VyOiBcIlxcdTZBNjFcXHU3NkFFXCIsXG4gIGZyYW1lOiBcIlxcdTc1M0JcXHU2ODQ2XFx1NURFNVxcdTUxNzdcIixcbiAgbWFnaWNmcmFtZTogXCJcXHU3RUJGXFx1Njg0NlxcdTU2RkVcXHU4MUYzXFx1NEVFM1xcdTc4MDFcIixcbiAgZW1iZWRkYWJsZTogXCJcXHU1RDRDXFx1NTE2NVxcdTdGNTFcXHU5ODc1XCIsXG4gIGxhc2VyOiBcIlxcdTZGQzBcXHU1MTQ5XFx1N0IxNFwiLFxuICBoYW5kOiBcIlxcdTYyOTNcXHU2MjRCXFx1RkYwOFxcdTVFNzNcXHU3OUZCXFx1NURFNVxcdTUxNzdcXHVGRjA5XCIsXG4gIGV4dHJhVG9vbHM6IFwiXFx1NjZGNFxcdTU5MUFcXHU1REU1XFx1NTE3N1wiLFxuICBtZXJtYWlkVG9FeGNhbGlkcmF3OiBcIk1lcm1haWQgXFx1ODFGMyBFeGNhbGlkcmF3XCIsXG4gIG1hZ2ljU2V0dGluZ3M6IFwiQUkgXFx1OEJCRVxcdTdGNkVcIlxufTtcbnZhciBoZWFkaW5ncyA9IHtcbiAgY2FudmFzQWN0aW9uczogXCJcXHU3NTNCXFx1NUUwM1xcdTUyQThcXHU0RjVDXCIsXG4gIHNlbGVjdGVkU2hhcGVBY3Rpb25zOiBcIlxcdTkwMDlcXHU1QjlBXFx1NUY2MlxcdTcyQjZcXHU2NENEXFx1NEY1Q1wiLFxuICBzaGFwZXM6IFwiXFx1NUY2MlxcdTcyQjZcIlxufTtcbnZhciBoaW50cyA9IHtcbiAgY2FudmFzUGFubmluZzogXCJcXHU4OTgxXFx1NzlGQlxcdTUyQThcXHU3NTNCXFx1NUUwM1xcdUZGMENcXHU4QkY3XFx1NjMwOVxcdTRGNEZcXHU5RjIwXFx1NjgwN1xcdTZFREFcXHU4RjZFXFx1NjIxNlxcdTdBN0FcXHU2ODNDXFx1OTUyRVxcdTU0MENcXHU2NUY2XFx1NjJENlxcdTYyRkRcXHU5RjIwXFx1NjgwN1xcdUZGMENcXHU2MjE2XFx1NEY3RlxcdTc1MjhcXHU2MjkzXFx1NjI0QlxcdTVERTVcXHU1MTc3XFx1MzAwMlwiLFxuICBsaW5lYXJFbGVtZW50OiBcIlxcdTcwQjlcXHU1MUZCXFx1NTIxQlxcdTVFRkFcXHU1OTFBXFx1NEUyQVxcdTcwQjkgXFx1NjJENlxcdTUyQThcXHU1MjFCXFx1NUVGQVxcdTc2RjRcXHU3RUJGXCIsXG4gIGZyZWVEcmF3OiBcIlxcdTcwQjlcXHU1MUZCXFx1NUU3NlxcdTYyRDZcXHU1MkE4XFx1RkYwQ1xcdTVCOENcXHU2MjEwXFx1NjVGNlxcdTY3N0VcXHU1RjAwXCIsXG4gIHRleHQ6IFwiXFx1NjNEMFxcdTc5M0FcXHVGRjFBXFx1NjBBOFxcdTRFNUZcXHU1M0VGXFx1NEVFNVxcdTRGN0ZcXHU3NTI4XFx1OTAwOVxcdTYyRTlcXHU1REU1XFx1NTE3N1xcdTUzQ0NcXHU1MUZCXFx1NEVGQlxcdTYxMEZcXHU0RjREXFx1N0Y2RVxcdTY3NjVcXHU2REZCXFx1NTJBMFxcdTY1ODdcXHU1QjU3XCIsXG4gIGVtYmVkZGFibGU6IFwiXFx1NzBCOVxcdTUxRkJcXHU1RTc2XFx1NjJENlxcdTUyQThcXHU0RUU1XFx1NTIxQlxcdTVFRkFcXHU1RDRDXFx1NTE2NVxcdTdGNTFcXHU5ODc1XCIsXG4gIHRleHRfc2VsZWN0ZWQ6IFwiXFx1NTNDQ1xcdTUxRkJcXHU2MjE2XFx1NjMwOVxcdTU2REVcXHU4RjY2XFx1OTUyRVxcdTRFRTVcXHU3RjE2XFx1OEY5MVxcdTY1ODdcXHU2NzJDXCIsXG4gIHRleHRfZWRpdGluZzogXCJcXHU2MzA5XFx1NEUwQiBFc2NhcGUgXFx1NjIxNiBDdHJsT3JDbWQrRU5URVIgXFx1NUI4Q1xcdTYyMTBcXHU3RjE2XFx1OEY5MVwiLFxuICBsaW5lYXJFbGVtZW50TXVsdGk6IFwiXFx1NzBCOVxcdTUxRkJcXHU2NzAwXFx1NTQwRVxcdTRFMDBcXHU0RTJBXFx1NzBCOVxcdTYyMTZcXHU2MzA5XFx1NEUwQiBFc2MvRW50ZXIgXFx1Njc2NVxcdTVCOENcXHU2MjEwXCIsXG4gIGxvY2tBbmdsZTogXCJcXHU1M0VGXFx1NEVFNVxcdTYzMDlcXHU0RjRGIFNoaWZ0IFxcdTY3NjVcXHU3RUE2XFx1Njc1RlxcdTg5RDJcXHU1RUE2XCIsXG4gIHJlc2l6ZTogXCJcXHU2MEE4XFx1NTNFRlxcdTRFRTVcXHU2MzA5XFx1NEY0RlNISUZUXFx1Njc2NVxcdTk2NTBcXHU1MjM2XFx1NkJENFxcdTRGOEJcXHU1OTI3XFx1NUMwRlxcdUZGMENcXG5cXHU2MzA5XFx1NEY0RkFMVFxcdTY3NjVcXHU4QzAzXFx1NjU3NFxcdTRFMkRcXHU1RkMzXFx1NTkyN1xcdTVDMEZcIixcbiAgcmVzaXplSW1hZ2U6IFwiXFx1NjMwOVxcdTRGNEZTSElGVFxcdTUzRUZcXHU0RUU1XFx1ODFFQVxcdTc1MzFcXHU3RjI5XFx1NjUzRVxcdUZGMENcXG5cXHU2MzA5XFx1NEY0RkFMVFxcdTUzRUZcXHU0RUU1XFx1NEVDRVxcdTRFMkRcXHU5NUY0XFx1N0YyOVxcdTY1M0VcIixcbiAgcm90YXRlOiBcIlxcdTY1Q0JcXHU4RjZDXFx1NjVGNlxcdTUzRUZcXHU0RUU1XFx1NjMwOVxcdTRGNEYgU2hpZnQgXFx1Njc2NVxcdTdFQTZcXHU2NzVGXFx1ODlEMlxcdTVFQTZcIixcbiAgbGluZUVkaXRvcl9pbmZvOiBcIlxcdTYzMDlcXHU0RjRGIEN0cmxPckNtZCBcXHU1RTc2XFx1NTNDQ1xcdTUxRkJcXHU2MjE2XFx1NjMwOSBDdHJsT3JDbWQgKyBFbnRlciBcXHU2NzY1XFx1N0YxNlxcdThGOTFcXHU3MEI5XCIsXG4gIGxpbmVFZGl0b3JfcG9pbnRTZWxlY3RlZDogXCJcXHU2MzA5XFx1NEUwQiBEZWxldGUgXFx1NzlGQlxcdTk2NjRcXHU3MEI5XFx1RkYwQ0N0cmxPckNtZCtEIFxcdTRFRTVcXHU1OTBEXFx1NTIzNlxcdUZGMENcXHU2MkQ2XFx1NTJBOFxcdTRFRTVcXHU3OUZCXFx1NTJBOFwiLFxuICBsaW5lRWRpdG9yX25vdGhpbmdTZWxlY3RlZDogXCJcXHU5MDA5XFx1NjJFOVxcdTg5ODFcXHU3RjE2XFx1OEY5MVxcdTc2ODRcXHU3MEI5IChcXHU2MzA5XFx1NEY0RiBTSElGVCBcXHU5MDA5XFx1NjJFOVxcdTU5MUFcXHU0RTJBKVxcdUZGMENcXG5cXHU2MjE2XFx1NjMwOVxcdTRGNEYgQWx0IFxcdTVFNzZcXHU3MEI5XFx1NTFGQlxcdTRFRTVcXHU2REZCXFx1NTJBMFxcdTY1QjBcXHU3MEI5XCIsXG4gIHBsYWNlSW1hZ2U6IFwiXFx1NzBCOVxcdTUxRkJcXHU2NTNFXFx1N0Y2RVxcdTU2RkVcXHU1MENGXFx1RkYwQ1xcdTYyMTZcXHU4MDA1XFx1NzBCOVxcdTUxRkJcXHU1RTc2XFx1NjJENlxcdTUyQThcXHU0RUU1XFx1NjI0QlxcdTUyQThcXHU4QkJFXFx1N0Y2RVxcdTU2RkVcXHU1MENGXFx1NTkyN1xcdTVDMEZcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiXFx1NTNEMVxcdTVFMDNcXHU2MEE4XFx1ODFFQVxcdTVERjFcXHU3Njg0XFx1N0QyMFxcdTY3NTBcXHU1RTkzXCIsXG4gIGJpbmRUZXh0VG9FbGVtZW50OiBcIlxcdTYzMDlcXHU0RTBCIEVudGVyIFxcdTRFRTVcXHU2REZCXFx1NTJBMFxcdTY1ODdcXHU2NzJDXCIsXG4gIGRlZXBCb3hTZWxlY3Q6IFwiXFx1NjMwOVxcdTRGNEYgQ3RybE9yQ21kIFxcdTRFRTVcXHU2REYxXFx1NUVBNlxcdTkwMDlcXHU2MkU5XFx1RkYwQ1xcdTVFNzZcXHU5MDdGXFx1NTE0RFxcdTYyRDZcXHU2MkZEXCIsXG4gIGVyYXNlclJldmVydDogXCJcXHU2MzA5XFx1NEY0RiBBbHQgXFx1NEVFNVxcdTUzQ0RcXHU5MDA5XFx1ODhBQlxcdTY4MDdcXHU4QkIwXFx1NTIyMFxcdTk2NjRcXHU3Njg0XFx1NTE0M1xcdTdEMjBcIixcbiAgZmlyZWZveF9jbGlwYm9hcmRfd3JpdGU6IFwiXFx1NUMwNlxcdTlBRDhcXHU3RUE3XFx1OTE0RFxcdTdGNkVcXHU5OTk2XFx1OTAwOVxcdTk4NzlcXHUyMDFDZG9tLmV2ZW50cy5hc3luY0NsaXBib2FyZC5jbGlwYm9hcmRJdGVtXFx1MjAxRFxcdThCQkVcXHU3RjZFXFx1NEUzQVxcdTIwMUN0cnVlXFx1MjAxRFxcdTUzRUZcXHU0RUU1XFx1NTQyRlxcdTc1MjhcXHU2QjY0XFx1NTI5RlxcdTgwRkRcXHUzMDAyXFx1ODk4MVxcdTY2RjRcXHU2NTM5IEZpcmVmb3ggXFx1NzY4NFxcdTlBRDhcXHU3RUE3XFx1OTE0RFxcdTdGNkVcXHU5OTk2XFx1OTAwOVxcdTk4NzlcXHVGRjBDXFx1OEJGN1xcdTUyNERcXHU1RjgwXFx1MjAxQ2Fib3V0OmNvbmZpZ1xcdTIwMURcXHU5ODc1XFx1OTc2MlxcdTMwMDJcIixcbiAgZGlzYWJsZVNuYXBwaW5nOiBcIlxcdTYzMDlcXHU0RjRGIEN0cmwgXFx1NjIxNiBDbWQgXFx1NEVFNVxcdTc5ODFcXHU3NTI4XFx1NTQzOFxcdTk2NDRcIlxufTtcbnZhciBjYW52YXNFcnJvciA9IHtcbiAgY2Fubm90U2hvd1ByZXZpZXc6IFwiXFx1NjVFMFxcdTZDRDVcXHU2NjNFXFx1NzkzQVxcdTk4ODRcXHU4OUM4XCIsXG4gIGNhbnZhc1Rvb0JpZzogXCJcXHU3NTNCXFx1NUUwM1xcdTUzRUZcXHU4MEZEXFx1OEZDN1xcdTU5MjdcXHUzMDAyXCIsXG4gIGNhbnZhc1Rvb0JpZ1RpcDogXCJcXHU2M0QwXFx1NzkzQVxcdUZGMUFcXHU1QzFEXFx1OEJENVxcdTVDMDZcXHU2NzAwXFx1OEZEQ1xcdTc2ODRcXHU1MTQzXFx1N0QyMFxcdTc5RkJcXHU1MkE4XFx1NTIzMFxcdTU0OENcXHU1MTc2XFx1NUI4M1xcdTUxNDNcXHU3RDIwXFx1NjZGNFxcdThGRDFcXHU0RTAwXFx1NEU5QlxcdTMwMDJcIlxufTtcbnZhciBlcnJvclNwbGFzaCA9IHtcbiAgaGVhZGluZ01haW46IFwiXFx1OTA0N1xcdTUyMzBcXHU1RjAyXFx1NUUzOFxcdTMwMDJcXHU4QkY3XFx1NUMxRFxcdThCRDU8YnV0dG9uPlxcdTkxQ0RcXHU2NUIwXFx1NTJBMFxcdThGN0RcXHU5ODc1XFx1OTc2MjwvYnV0dG9uPlxcdTMwMDJcIixcbiAgY2xlYXJDYW52YXNNZXNzYWdlOiBcIlxcdTU5ODJcXHU2NzlDXFx1OTFDRFxcdTY1QjBcXHU1MkEwXFx1OEY3RFxcdTk4NzVcXHU5NzYyXFx1NjVFMFxcdTY1NDhcXHVGRjBDXFx1OEJGN1xcdTVDMURcXHU4QkQ1PGJ1dHRvbj5cXHU2RTA1XFx1OTY2NFxcdTc1M0JcXHU1RTAzPC9idXR0b24+XFx1MzAwMlwiLFxuICBjbGVhckNhbnZhc0NhdmVhdDogXCJcXHU4RkQ5XFx1NEYxQVxcdTkwMjBcXHU2MjEwXFx1NUY1M1xcdTUyNERcXHU1REU1XFx1NEY1Q1xcdTRFMjJcXHU1OTMxXCIsXG4gIHRyYWNrZWRUb1NlbnRyeTogXCJcXHU2ODA3XFx1OEJDNlxcdTdCMjZcXHU0RTNBe3tldmVudElkfX1cXHU3Njg0XFx1OTUxOVxcdThCRUZcXHU1REYyXFx1NTcyOFxcdTYyMTFcXHU0RUVDXFx1NzY4NFxcdTdDRkJcXHU3RURGXFx1NEUyRFxcdTg4QUJcXHU4QkIwXFx1NUY1NVwiLFxuICBvcGVuSXNzdWVNZXNzYWdlOiBcIlxcdTYyMTFcXHU0RUVDXFx1OTc1RVxcdTVFMzhcXHU4QzI4XFx1NjE0RVxcdTU3MzBcXHU1OTA0XFx1NzQwNlxcdTk1MTlcXHU4QkVGXFx1NEZFMVxcdTYwNkZcXHVGRjBDXFx1NjBBOFxcdTc2ODRcXHU3NTNCXFx1NUUwM1xcdTUxODVcXHU1QkI5XFx1NEUwRFxcdTRGMUFcXHU4OEFCXFx1NTMwNVxcdTU0MkJcXHU1NzI4XFx1OTUxOVxcdThCRUZcXHU2MkE1XFx1NTQ0QVxcdTRFMkRcXHUzMDAyXFx1NTk4MlxcdTY3OUNcXHU2MEE4XFx1NzY4NFxcdTc1M0JcXHU1RTAzXFx1NTE4NVxcdTVCQjlcXHU0RTBEXFx1OTcwMFxcdTg5ODFcXHU0RkREXFx1NjMwMVxcdTc5QzFcXHU1QkM2XFx1RkYwQ1xcdThCRjdcXHU4MDAzXFx1ODY1MVxcdTU3MjhcXHU2MjExXFx1NEVFQ1xcdTc2ODQgPGJ1dHRvbj5idWcgXFx1OERERlxcdThFMkFcXHU3Q0ZCXFx1N0VERjwvYnV0dG9uPlxcdTRFMEFcXHU2M0QwXFx1NEY5QlxcdTY2RjRcXHU1OTFBXFx1NEZFMVxcdTYwNkZcXHUzMDAyXFx1OEJGN1xcdTU5MERcXHU1MjM2XFx1N0M5OFxcdThEMzRcXHU0RUU1XFx1NEUwQlxcdTRGRTFcXHU2MDZGXFx1NTIzMCBHaXRIdWIgSXNzdWUgXFx1NEUyRFxcdTMwMDJcIixcbiAgc2NlbmVDb250ZW50OiBcIlxcdTc1M0JcXHU1RTAzXFx1NTE4NVxcdTVCQjk6XCJcbn07XG52YXIgcm9vbURpYWxvZyA9IHtcbiAgZGVzY19pbnRybzogXCJcXHU0RjYwXFx1NTNFRlxcdTRFRTVcXHU5MDgwXFx1OEJGN1xcdTUxNzZcXHU0RUQ2XFx1NEVCQVxcdTUyMzBcXHU3NkVFXFx1NTI0RFxcdTc2ODRcXHU3NTNCXFx1OTc2MlxcdTRFMkRcXHU0RTBFXFx1NEY2MFxcdTUzNEZcXHU0RjVDXFx1MzAwMlwiLFxuICBkZXNjX3ByaXZhY3k6IFwiXFx1NTIyQlxcdTYyQzVcXHU1RkMzXFx1RkYwQ1xcdThCRTVcXHU0RjFBXFx1OEJERFxcdTRGN0ZcXHU3NTI4XFx1N0FFRlxcdTUyMzBcXHU3QUVGXFx1NTJBMFxcdTVCQzZcXHVGRjBDXFx1NjVFMFxcdThCQkFcXHU3RUQ4XFx1NTIzNlxcdTRFQzBcXHU0RTQ4XFx1OTBGRFxcdTVDMDZcXHU0RkREXFx1NjMwMVxcdTc5QzFcXHU1QkM2XFx1RkYwQ1xcdTc1MUFcXHU4MUYzXFx1OEZERVxcdTYyMTFcXHU0RUVDXFx1NzY4NFxcdTY3MERcXHU1MkExXFx1NTY2OFxcdTRFNUZcXHU2NUUwXFx1NkNENVxcdTY3RTVcXHU3NzBCXFx1MzAwMlwiLFxuICBidXR0b25fc3RhcnRTZXNzaW9uOiBcIlxcdTVGMDBcXHU1OUNCXFx1NEYxQVxcdThCRERcIixcbiAgYnV0dG9uX3N0b3BTZXNzaW9uOiBcIlxcdTdFRDNcXHU2NzVGXFx1NEYxQVxcdThCRERcIixcbiAgZGVzY19pblByb2dyZXNzSW50cm86IFwiXFx1NUI5RVxcdTY1RjZcXHU1MzRGXFx1NEY1Q1xcdTRGMUFcXHU4QkREXFx1OEZEQlxcdTg4NENcXHU0RTJEXFx1MzAwMlwiLFxuICBkZXNjX3NoYXJlTGluazogXCJcXHU1MjA2XFx1NEVBQlxcdTZCNjRcXHU5NEZFXFx1NjNBNVxcdTdFRDlcXHU0RjYwXFx1ODk4MVxcdTUzNEZcXHU0RjVDXFx1NzY4NFxcdTc1MjhcXHU2MjM3XCIsXG4gIGRlc2NfZXhpdFNlc3Npb246IFwiXFx1NTA1Q1xcdTZCNjJcXHU0RjFBXFx1OEJERFxcdTVDMDZcXHU0RTJEXFx1NjVBRFxcdTYwQThcXHU0RTBFXFx1NjIzRlxcdTk1RjRcXHU3Njg0XFx1OEZERVxcdTYzQTVcXHVGRjBDXFx1NEY0NlxcdTYwQThcXHU0RjlEXFx1NzEzNlxcdTUzRUZcXHU0RUU1XFx1NTcyOFxcdTY3MkNcXHU1NzMwXFx1N0VFN1xcdTdFRURcXHU0RjdGXFx1NzUyOFxcdTc1M0JcXHU1RTAzXFx1MzAwMlxcdThCRjdcXHU2Q0U4XFx1NjEwRlxcdUZGMENcXHU4RkQ5XFx1NEUwRFxcdTRGMUFcXHU1RjcxXFx1NTRDRFxcdTUyMzBcXHU1MTc2XFx1NEVENlxcdTc1MjhcXHU2MjM3XFx1RkYwQ1xcdTRFRDZcXHU0RUVDXFx1NEVDRFxcdTUzRUZcXHU0RUU1XFx1NTcyOFxcdTRFRDZcXHU0RUVDXFx1NzY4NFxcdTcyNDhcXHU2NzJDXFx1NEUwQVxcdTdFRTdcXHU3RUVEXFx1NTM0RlxcdTRGNUNcXHUzMDAyXCIsXG4gIHNoYXJlVGl0bGU6IFwiXFx1NTJBMFxcdTUxNjUgRXhjYWxpZHJhdyBcXHU1QjlFXFx1NjVGNlxcdTUzNEZcXHU0RjVDXFx1NEYxQVxcdThCRERcIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiXFx1OTUxOVxcdThCRUZcIlxufTtcbnZhciBleHBvcnREaWFsb2cgPSB7XG4gIGRpc2tfdGl0bGU6IFwiXFx1NEZERFxcdTVCNThcXHU1MjMwXFx1NjcyQ1xcdTU3MzBcIixcbiAgZGlza19kZXRhaWxzOiBcIlxcdTVDMDZcXHU3NTNCXFx1NUUwM1xcdTY1NzBcXHU2MzZFXFx1NUJGQ1xcdTUxRkFcXHU0RTNBXFx1NjU4N1xcdTRFRjZcXHVGRjBDXFx1NEVFNVxcdTRGQkZcXHU0RUU1XFx1NTQwRVxcdTVCRkNcXHU1MTY1XCIsXG4gIGRpc2tfYnV0dG9uOiBcIlxcdTRGRERcXHU1QjU4XFx1NEUzQVxcdTY1ODdcXHU0RUY2XCIsXG4gIGxpbmtfdGl0bGU6IFwiXFx1NTIwNlxcdTRFQUJcXHU5NEZFXFx1NjNBNVwiLFxuICBsaW5rX2RldGFpbHM6IFwiXFx1NUJGQ1xcdTUxRkFcXHU0RTNBXFx1NTNFQVxcdThCRkJcXHU5NEZFXFx1NjNBNVxcdTMwMDJcIixcbiAgbGlua19idXR0b246IFwiXFx1NUJGQ1xcdTUxRkFcXHU5NEZFXFx1NjNBNVwiLFxuICBleGNhbGlkcmF3cGx1c19kZXNjcmlwdGlvbjogXCJcXHU1QzA2XFx1NzUzQlxcdTVFMDNcXHU0RkREXFx1NUI1OFxcdTUyMzBcXHU2MEE4XFx1NzY4NCBFeGNhbGlkcmF3KyBcXHU1REU1XFx1NEY1Q1xcdTUzM0FcXHUzMDAyXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2J1dHRvbjogXCJcXHU1QkZDXFx1NTFGQVwiLFxuICBleGNhbGlkcmF3cGx1c19leHBvcnRFcnJvcjogXCJcXHU2NjgyXFx1NjVGNlxcdTY1RTBcXHU2Q0Q1XFx1NUJGQ1xcdTUxRkFcXHU1MjMwIEV4Y2FsaWRyYXcrIC4uLlwiXG59O1xudmFyIGhlbHBEaWFsb2cgPSB7XG4gIGJsb2c6IFwiXFx1NkQ0RlxcdTg5QzhcXHU2MjExXFx1NEVFQ1xcdTc2ODRcXHU1MzVBXFx1NUJBMlwiLFxuICBjbGljazogXCJcXHU1MzU1XFx1NTFGQlwiLFxuICBkZWVwU2VsZWN0OiBcIlxcdTZERjFcXHU1RUE2XFx1OTAwOVxcdTYyRTlcIixcbiAgZGVlcEJveFNlbGVjdDogXCJcXHU1NzI4XFx1NjVCOVxcdTY4NDZcXHU1MTg1XFx1NkRGMVxcdTVFQTZcXHU5MDA5XFx1NjJFOVxcdTVFNzZcXHU5MDdGXFx1NTE0RFxcdTYyRDZcXHU2MkZEXCIsXG4gIGN1cnZlZEFycm93OiBcIlxcdTY2RjJcXHU3RUJGXFx1N0JBRFxcdTU5MzRcIixcbiAgY3VydmVkTGluZTogXCJcXHU2NkYyXFx1N0VCRlwiLFxuICBkb2N1bWVudGF0aW9uOiBcIlxcdTY1ODdcXHU2ODYzXCIsXG4gIGRvdWJsZUNsaWNrOiBcIlxcdTUzQ0NcXHU1MUZCXCIsXG4gIGRyYWc6IFwiXFx1NjJENlxcdTUyQThcIixcbiAgZWRpdG9yOiBcIlxcdTdGMTZcXHU4RjkxXFx1NTY2OFwiLFxuICBlZGl0TGluZUFycm93UG9pbnRzOiBcIlxcdTdGMTZcXHU4RjkxXFx1N0VCRlxcdTY3NjFcXHU2MjE2XFx1N0JBRFxcdTU5MzRcXHU3Njg0XFx1NzBCOVwiLFxuICBlZGl0VGV4dDogXCJcXHU2REZCXFx1NTJBMFxcdTYyMTZcXHU3RjE2XFx1OEY5MVxcdTY1ODdcXHU2NzJDXCIsXG4gIGdpdGh1YjogXCJcXHU1M0QxXFx1NzNCMFxcdTk1RUVcXHU5ODk4XFx1RkYxRlxcdTYzRDBcXHU0RUE0XFx1NTNDRFxcdTk5ODhcIixcbiAgaG93dG86IFwiXFx1NUUyRVxcdTUyQTlcXHU2NTg3XFx1Njg2M1wiLFxuICBvcjogXCJcXHU2MjE2XCIsXG4gIHByZXZlbnRCaW5kaW5nOiBcIlxcdTc5ODFcXHU3NTI4XFx1N0JBRFxcdTU5MzRcXHU1NDM4XFx1OTY0NFwiLFxuICB0b29sczogXCJcXHU1REU1XFx1NTE3N1wiLFxuICBzaG9ydGN1dHM6IFwiXFx1NUZFQlxcdTYzNzdcXHU5NTJFXFx1NTIxN1xcdTg4NjhcIixcbiAgdGV4dEZpbmlzaDogXCJcXHU1QjhDXFx1NjIxMFxcdTdGMTZcXHU4RjkxIChcXHU2NTg3XFx1NjcyQ1xcdTdGMTZcXHU4RjkxXFx1NTY2OClcIixcbiAgdGV4dE5ld0xpbmU6IFwiXFx1NkRGQlxcdTUyQTBcXHU2NUIwXFx1ODg0QyhcXHU2NTg3XFx1NjcyQ1xcdTdGMTZcXHU4RjkxXFx1NTY2OClcIixcbiAgdGl0bGU6IFwiXFx1NUUyRVxcdTUyQTlcIixcbiAgdmlldzogXCJcXHU4OUM2XFx1NTZGRVwiLFxuICB6b29tVG9GaXQ6IFwiXFx1N0YyOVxcdTY1M0VcXHU0RUU1XFx1OTAwMlxcdTVFOTRcXHU2MjQwXFx1NjcwOVxcdTUxNDNcXHU3RDIwXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJcXHU3RjI5XFx1NjUzRVxcdTUyMzBcXHU5MDA5XFx1NTMzQVwiLFxuICB0b2dnbGVFbGVtZW50TG9jazogXCJcXHU5NTAxXFx1NUI5QS9cXHU4OUUzXFx1OTUwMVwiLFxuICBtb3ZlUGFnZVVwRG93bjogXCJcXHU0RTBBXFx1NEUwQlxcdTc5RkJcXHU1MkE4XFx1OTg3NVxcdTk3NjJcIixcbiAgbW92ZVBhZ2VMZWZ0UmlnaHQ6IFwiXFx1NURFNlxcdTUzRjNcXHU3OUZCXFx1NTJBOFxcdTk4NzVcXHU5NzYyXCJcbn07XG52YXIgY2xlYXJDYW52YXNEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIlxcdTZFMDVcXHU5NjY0XFx1NzUzQlxcdTVFMDNcIlxufTtcbnZhciBwdWJsaXNoRGlhbG9nID0ge1xuICB0aXRsZTogXCJcXHU1M0QxXFx1NUUwM1xcdTdEMjBcXHU2NzUwXFx1NUU5M1wiLFxuICBpdGVtTmFtZTogXCJcXHU5ODc5XFx1NzZFRVxcdTU0MERcXHU3OUYwXCIsXG4gIGF1dGhvck5hbWU6IFwiXFx1NEY1Q1xcdTgwMDVcXHU1NDBEXCIsXG4gIGdpdGh1YlVzZXJuYW1lOiBcIkdpdEh1YiBcXHU3NTI4XFx1NjIzN1xcdTU0MERcIixcbiAgdHdpdHRlclVzZXJuYW1lOiBcIlR3aXR0ZXIgXFx1NzUyOFxcdTYyMzdcXHU1NDBEXCIsXG4gIGxpYnJhcnlOYW1lOiBcIlxcdTU0MERcXHU3OUYwXCIsXG4gIGxpYnJhcnlEZXNjOiBcIlxcdTdCODBcXHU0RUNCXCIsXG4gIHdlYnNpdGU6IFwiXFx1N0Y1MVxcdTU3NDBcIixcbiAgcGxhY2Vob2xkZXI6IHtcbiAgICBhdXRob3JOYW1lOiBcIlxcdTYwQThcXHU3Njg0XFx1NTQwRFxcdTVCNTdcXHU2MjE2XFx1NzUyOFxcdTYyMzdcXHU1NDBEXCIsXG4gICAgbGlicmFyeU5hbWU6IFwiXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NTQwRFxcdTc5RjBcIixcbiAgICBsaWJyYXJ5RGVzYzogXCJcXHU0RUNCXFx1N0VDRFxcdTYwQThcXHU3Njg0XFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1RkYwQ1xcdThCQTlcXHU0RUJBXFx1NEVFQ1xcdTRFODZcXHU4OUUzXFx1NTE3NlxcdTc1MjhcXHU5MDE0XCIsXG4gICAgZ2l0aHViSGFuZGxlOiBcIkdpdEh1YiBcXHU3NTI4XFx1NjIzN1xcdTU0MERcXHVGRjA4XFx1NTNFRlxcdTkwMDlcXHVGRjA5XFx1RkYwQ1xcdTU4NkJcXHU1MTk5XFx1NTQwRVxcdUZGMENcXHU2MEE4XFx1NTNFRlxcdTRFRTVcXHU3RjE2XFx1OEY5MVxcdTVERjJcXHU2M0QwXFx1NEVBNFxcdTVGODVcXHU1QkExXFx1NzY4NFxcdTdEMjBcXHU2NzUwXFx1NUU5M1wiLFxuICAgIHR3aXR0ZXJIYW5kbGU6IFwiVHdpdHRlciBcXHU3NTI4XFx1NjIzN1xcdTU0MERcXHVGRjA4XFx1NTNFRlxcdTkwMDlcXHVGRjA5XFx1RkYwQ1xcdTU4NkJcXHU1MTk5XFx1NTQwRVxcdUZGMENcXHU1RjUzXFx1NjIxMVxcdTRFRUNcXHU1NzI4VHdpdHRlclxcdTUzRDFcXHU1RTAzXFx1NjNBOFxcdTVFN0ZcXHU0RkUxXFx1NjA2RlxcdTY1RjZcXHU0RkJGXFx1NTNFRlxcdTYzRDBcXHU1M0NBXFx1NjBBOFwiLFxuICAgIHdlYnNpdGU6IFwiXFx1NjBBOFxcdTRFMkFcXHU0RUJBXFx1N0Y1MVxcdTdBRDlcXHU3Njg0XFx1NjIxNlxcdTRFRkJcXHU2MTBGXFx1NzY4NFxcdTk0RkVcXHU2M0E1XFx1RkYwOFxcdTUzRUZcXHU5MDA5XFx1RkYwOVwiXG4gIH0sXG4gIGVycm9yczoge1xuICAgIHJlcXVpcmVkOiBcIlxcdTVGQzVcXHU1ODZCXCIsXG4gICAgd2Vic2l0ZTogXCJcXHU4RjkzXFx1NTE2NVxcdTRFMDBcXHU0RTJBXFx1NjcwOVxcdTY1NDhcXHU3Njg0VVJMXCJcbiAgfSxcbiAgbm90ZURlc2NyaXB0aW9uOiBcIlxcdTYzRDBcXHU0RUE0XFx1NTQwRVxcdUZGMENcXHU2MEE4XFx1NzY4NFxcdTdEMjBcXHU2NzUwXFx1NUU5M1xcdTVDMDZcXHU4OEFCXFx1NTMwNVxcdTU0MkJcXHU1NzI4PGxpbms+XFx1NTE2Q1xcdTUxNzFcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU1RTdGXFx1NTczQTwvbGluaz5cXHU0RUU1XFx1NEY5QlxcdTUxNzZcXHU0RUQ2XFx1NEVCQVxcdTU3MjhcXHU3RUQ4XFx1NTZGRVxcdTRFMkRcXHU0RjdGXFx1NzUyOFxcdTMwMDJcIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiXFx1NjNEMFxcdTRFQTRcXHU3Njg0XFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1OTcwMFxcdTUxNDhcXHU3RUNGXFx1NEVCQVxcdTVERTVcXHU1QkExXFx1NjgzOFxcdTMwMDJcXHU1NzI4XFx1NjNEMFxcdTRFQTRcXHU0RTRCXFx1NTI0RFxcdUZGMENcXHU4QkY3XFx1NTE0OFxcdTk2MDVcXHU4QkZCPGxpbms+XFx1NjMwN1xcdTUzNTc8L2xpbms+IFxcdTMwMDJcXHU1NDBFXFx1N0VFRFxcdTZDOUZcXHU5MDFBXFx1NTQ4Q1xcdTVCRjlcXHU1RTkzXFx1NzY4NFxcdTRGRUVcXHU2NTM5XFx1OTcwMFxcdTg5ODEgR2l0SHViIFxcdThEMjZcXHU1M0Y3XFx1RkYwQ1xcdTRGNDZcXHU4RkQ5XFx1NEUwRFxcdTY2MkZcXHU1RkM1XFx1OTg3QlxcdTc2ODRcXHUzMDAyXCIsXG4gIG5vdGVMaWNlbnNlOiBcIlxcdTYzRDBcXHU0RUE0XFx1NTM3M1xcdTg4NjhcXHU2NjBFXFx1NjBBOFxcdTVERjJcXHU1NDBDXFx1NjEwRlxcdTdEMjBcXHU2NzUwXFx1NUU5M1xcdTVDMDZcXHU5MDc1XFx1NUZBQSA8bGluaz5NSVQgXFx1OEJCOFxcdTUzRUZcXHU4QkMxPC9saW5rPlxcdUZGMENcXHU3QjgwXFx1ODAwQ1xcdThBMDBcXHU0RTRCXFx1RkYwQ1xcdTRFRkJcXHU0RjU1XFx1NEVCQVxcdTkwRkRcXHU1M0VGXFx1NEVFNVxcdTRFMERcXHU1M0Q3XFx1OTY1MFxcdTUyMzZcXHU1NzMwXFx1NEY3RlxcdTc1MjhcXHU1QjgzXFx1NEVFQ1xcdTMwMDJcIixcbiAgbm90ZUl0ZW1zOiBcIlxcdTdEMjBcXHU2NzUwXFx1NUU5M1xcdTRFMkRcXHU2QkNGXFx1NEUyQVxcdTk4NzlcXHU3NkVFXFx1OTBGRFxcdTY3MDlcXHU1NDA0XFx1ODFFQVxcdTc2ODRcXHU1NDBEXFx1NzlGMFxcdTRFRTVcXHU0RjlCXFx1N0I1QlxcdTkwMDlcXHUzMDAyXFx1NEVFNVxcdTRFMEJcXHU5ODc5XFx1NzZFRVxcdTVDMDZcXHU4OEFCXFx1NTMwNVxcdTU0MkJcXHVGRjFBXCIsXG4gIGF0bGVhc3RPbmVMaWJJdGVtOiBcIlxcdThCRjdcXHU5MDA5XFx1NjJFOVxcdTgxRjNcXHU1QzExXFx1NEUwMFxcdTRFMkFcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU0RUU1XFx1NUYwMFxcdTU5Q0JcIixcbiAgcmVwdWJsaXNoV2FybmluZzogXCJcXHU2Q0U4XFx1NjEwRlxcdUZGMUFcXHU5MEU4XFx1NTIwNlxcdTkwMDlcXHU0RTJEXFx1NzY4NFxcdTk4NzlcXHU3NkVFXFx1NURGMlxcdTdFQ0ZcXHU1M0QxXFx1NUUwM1xcdTYyMTZcXHU2M0QwXFx1NEVBNFxcdTMwMDJcXHU4QkY3XFx1NEVDNVxcdTU3MjhcXHU2NkY0XFx1NjVCMFxcdTVERjJcXHU2NzA5XFx1NjIxNlxcdTVERjJcXHU2M0QwXFx1NEVBNFxcdTc2ODRcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU2NUY2XFx1OTFDRFxcdTU5MERcXHU2M0QwXFx1NEVBNFxcdTk4NzlcXHU3NkVFXFx1MzAwMlwiXG59O1xudmFyIHB1Ymxpc2hTdWNjZXNzRGlhbG9nID0ge1xuICB0aXRsZTogXCJcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU1REYyXFx1NjNEMFxcdTRFQTRcIixcbiAgY29udGVudDogXCJcXHU4QzIyXFx1OEMyMlxcdTRGNjAge3thdXRob3JOYW1lfX1cXHUzMDAyXFx1NjBBOFxcdTc2ODRcXHU3RDIwXFx1Njc1MFxcdTVFOTNcXHU1REYyXFx1ODhBQlxcdTYzRDBcXHU0RUE0XFx1NUJBMVxcdTY4MzhcXHUzMDAyXFx1OEJGN1xcdTcwQjlcXHU1MUZCPGxpbms+XFx1NkI2NFxcdTU5MDQ8L2xpbms+XFx1OERERlxcdThGREJcXHU2QjY0XFx1NkIyMVxcdTYzRDBcXHU0RUE0XFx1NzY4NFxcdTcyQjZcXHU2MDAxXCJcbn07XG52YXIgY29uZmlybURpYWxvZyA9IHtcbiAgcmVzZXRMaWJyYXJ5OiBcIlxcdTkxQ0RcXHU3RjZFXFx1N0QyMFxcdTY3NTBcXHU1RTkzXCIsXG4gIHJlbW92ZUl0ZW1zRnJvbUxpYjogXCJcXHU0RUNFXFx1N0QyMFxcdTY3NTBcXHU1RTkzXFx1NEUyRFxcdTUyMjBcXHU5NjY0XFx1OTAwOVxcdTRFMkRcXHU3Njg0XFx1OTg3OVxcdTc2RUVcIlxufTtcbnZhciBpbWFnZUV4cG9ydERpYWxvZyA9IHtcbiAgaGVhZGVyOiBcIlxcdTVCRkNcXHU1MUZBXFx1NTZGRVxcdTcyNDdcIixcbiAgbGFiZWw6IHtcbiAgICB3aXRoQmFja2dyb3VuZDogXCJcXHU4MENDXFx1NjY2RlwiLFxuICAgIG9ubHlTZWxlY3RlZDogXCJcXHU0RUM1XFx1OTAwOVxcdTRFMkRcIixcbiAgICBkYXJrTW9kZTogXCJcXHU2REYxXFx1ODI3MlxcdTZBMjFcXHU1RjBGXCIsXG4gICAgZW1iZWRTY2VuZTogXCJcXHU1MzA1XFx1NTQyQlxcdTc1M0JcXHU1RTAzXFx1NjU3MFxcdTYzNkVcIixcbiAgICBzY2FsZTogXCJcXHU3RjI5XFx1NjUzRVxcdTZCRDRcXHU0RjhCXCIsXG4gICAgcGFkZGluZzogXCJcXHU1MTg1XFx1OEZCOVxcdThERERcIlxuICB9LFxuICB0b29sdGlwOiB7XG4gICAgZW1iZWRTY2VuZTogXCJcXHU3NTNCXFx1NUUwM1xcdTY1NzBcXHU2MzZFXFx1NUMwNlxcdTg4QUJcXHU0RkREXFx1NUI1OFxcdTUyMzBcXHU1QkZDXFx1NTFGQVxcdTc2ODQgUE5HL1NWRyBcXHU2NTg3XFx1NEVGNlxcdUZGMENcXHU0RUU1XFx1NEZCRlxcdTYwNjJcXHU1OTBEXFx1MzAwMlxcblxcdTVDMDZcXHU0RjFBXFx1NTg5RVxcdTUyQTBcXHU1QkZDXFx1NTFGQVxcdTY1ODdcXHU0RUY2XFx1NzY4NFxcdTU5MjdcXHU1QzBGXFx1MzAwMlwiXG4gIH0sXG4gIHRpdGxlOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiXFx1NUJGQ1xcdTUxRkFcXHU0RTNBIFBOR1wiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIlxcdTVCRkNcXHU1MUZBXFx1NEUzQSBTVkdcIixcbiAgICBjb3B5UG5nVG9DbGlwYm9hcmQ6IFwiXFx1NTkwRFxcdTUyMzYgUE5HIFxcdTUyMzBcXHU1MjZBXFx1NTIwN1xcdTY3N0ZcIlxuICB9LFxuICBidXR0b246IHtcbiAgICBleHBvcnRUb1BuZzogXCJQTkdcIixcbiAgICBleHBvcnRUb1N2ZzogXCJTVkdcIixcbiAgICBjb3B5UG5nVG9DbGlwYm9hcmQ6IFwiXFx1NTkwRFxcdTUyMzZcXHU1MjMwXFx1NTI2QVxcdThEMzRcXHU2NzdGXCJcbiAgfVxufTtcbnZhciBlbmNyeXB0ZWQgPSB7XG4gIHRvb2x0aXA6IFwiXFx1NjBBOFxcdTc2ODRcXHU3RUQ4XFx1NTZGRVxcdTkxQzdcXHU3NTI4XFx1N0FFRlxcdTUyMzBcXHU3QUVGXFx1NTJBMFxcdTVCQzZcXHVGRjBDXFx1NTE3NlxcdTUxODVcXHU1QkI5XFx1NUJGOVxcdTRFOEUgRXhjYWxpZHJhdyBcXHU2NzBEXFx1NTJBMVxcdTU2NjhcXHU2NjJGXFx1NEUwRFxcdTUzRUZcXHU4OUMxXFx1NzY4NFxcdTMwMDJcIixcbiAgbGluazogXCJFeGNhbGlkcmF3IFxcdTRFMkRcXHU1MTczXFx1NEU4RVxcdTdBRUZcXHU1MjMwXFx1N0FFRlxcdTUyQTBcXHU1QkM2XFx1NzY4NFxcdTUzNUFcXHU1QkEyXCJcbn07XG52YXIgc3RhdHMgPSB7XG4gIGFuZ2xlOiBcIlxcdTg5RDJcXHU1RUE2XCIsXG4gIGVsZW1lbnQ6IFwiXFx1NTE0M1xcdTdEMjBcIixcbiAgZWxlbWVudHM6IFwiXFx1NTE0M1xcdTdEMjBcIixcbiAgaGVpZ2h0OiBcIlxcdTlBRDhcXHU1RUE2XCIsXG4gIHNjZW5lOiBcIlxcdTc1M0JcXHU1RTAzXCIsXG4gIHNlbGVjdGVkOiBcIlxcdTkwMDlcXHU0RTJEXCIsXG4gIHN0b3JhZ2U6IFwiXFx1NUI1OFxcdTUwQThcIixcbiAgdGl0bGU6IFwiXFx1OEJFNlxcdTdFQzZcXHU3RURGXFx1OEJBMVxcdTRGRTFcXHU2MDZGXCIsXG4gIHRvdGFsOiBcIlxcdTYwM0JcXHU4QkExXCIsXG4gIHZlcnNpb246IFwiXFx1NzI0OFxcdTY3MkNcIixcbiAgdmVyc2lvbkNvcHk6IFwiXFx1NzBCOVxcdTUxRkJcXHU1OTBEXFx1NTIzNlwiLFxuICB2ZXJzaW9uTm90QXZhaWxhYmxlOiBcIlxcdTcyNDhcXHU2NzJDXFx1NEUwRFxcdTUzRUZcXHU3NTI4XCIsXG4gIHdpZHRoOiBcIlxcdTVCQkRcXHU1RUE2XCJcbn07XG52YXIgdG9hc3QgPSB7XG4gIGFkZGVkVG9MaWJyYXJ5OiBcIlxcdTZERkJcXHU1MkEwXFx1NTIzMFxcdTdEMjBcXHU2NzUwXFx1NUU5M1xcdTRFMkRcIixcbiAgY29weVN0eWxlczogXCJcXHU2ODM3XFx1NUYwRlxcdTVERjJcXHU2MkY3XFx1OEQxRFxcdTMwMDJcIixcbiAgY29weVRvQ2xpcGJvYXJkOiBcIlxcdTVERjJcXHU1OTBEXFx1NTIzNlxcdTUyMzBcXHU1MjZBXFx1NTIwN1xcdTY3N0ZcXHUzMDAyXCIsXG4gIGNvcHlUb0NsaXBib2FyZEFzUG5nOiBcIlxcdTVERjJcXHU1QzA2IHt7ZXhwb3J0U2VsZWN0aW9ufX0gXFx1NEY1Q1xcdTRFM0EgUE5HIFxcdTU5MERcXHU1MjM2XFx1NTIzMFxcdTUyNkFcXHU4RDM0XFx1Njc3Rlxcbih7e2V4cG9ydENvbG9yU2NoZW1lfX0pXCIsXG4gIGZpbGVTYXZlZDogXCJcXHU2NTg3XFx1NEVGNlxcdTVERjJcXHU0RkREXFx1NUI1OFxcdTMwMDJcIixcbiAgZmlsZVNhdmVkVG9GaWxlbmFtZTogXCJcXHU0RkREXFx1NUI1OFxcdTUyMzAge2ZpbGVuYW1lfVwiLFxuICBjYW52YXM6IFwiXFx1NzUzQlxcdTVFMDNcIixcbiAgc2VsZWN0aW9uOiBcIlxcdTYyNDBcXHU5MDA5XFx1OTg3OVwiLFxuICBwYXN0ZUFzU2luZ2xlRWxlbWVudDogXCJcXHU0RjdGXFx1NzUyOCB7e3Nob3J0Y3V0fX0gXFx1N0M5OFxcdThEMzRcXHU0RTNBXFx1NTM1NVxcdTRFMkFcXHU1MTQzXFx1N0QyMFxcdUZGMENcXG5cXHU2MjE2XFx1N0M5OFxcdThEMzRcXHU1MjMwXFx1NzNCMFxcdTY3MDlcXHU3Njg0XFx1NjU4N1xcdTY3MkNcXHU3RjE2XFx1OEY5MVxcdTU2NjhcXHU5MUNDXCIsXG4gIHVuYWJsZVRvRW1iZWQ6IFwiXFx1NzZFRVxcdTUyNERcXHU0RTBEXFx1NTE0MVxcdThCQjhcXHU1RDRDXFx1NTE2NVxcdTZCNjRcXHU3RjUxXFx1NTc0MFxcdTMwMDJcXHU4QkY3XFx1NTcyOCBHaXRIdWIgXFx1NEUwQVxcdTYzRDAgaXNzdWUgXFx1OEJGN1xcdTZDNDJcXHU1QzA2XFx1NkI2NFxcdTdGNTFcXHU1NzQwXFx1NTJBMFxcdTUxNjVcXHU3NjdEXFx1NTQwRFxcdTUzNTVcIixcbiAgdW5yZWNvZ25pemVkTGlua0Zvcm1hdDogXCJcXHU2MEE4XFx1NUQ0Q1xcdTUxNjVcXHU3Njg0XFx1OTRGRVxcdTYzQTVcXHU0RTBEXFx1N0IyNlxcdTU0MDhcXHU2ODNDXFx1NUYwRlxcdTg5ODFcXHU2QzQyXFx1MzAwMlxcdThCRjdcXHU1QzFEXFx1OEJENVxcdTdDOThcXHU4RDM0XFx1NkU5MFxcdTdGNTFcXHU3QUQ5XFx1NjNEMFxcdTRGOUJcXHU3Njg0XFx1MjAxQ1xcdTVENENcXHU1MTY1IChlbWJlZClcXHUyMDFEXFx1NUI1N1xcdTdCMjZcXHU0RTMyXCJcbn07XG52YXIgY29sb3JzID0ge1xuICB0cmFuc3BhcmVudDogXCJcXHU5MDBGXFx1NjYwRVwiLFxuICBibGFjazogXCJcXHU5RUQxXCIsXG4gIHdoaXRlOiBcIlxcdTc2N0RcIixcbiAgcmVkOiBcIlxcdTdFQTJcIixcbiAgcGluazogXCJcXHU3Qzg5XFx1N0VBMlwiLFxuICBncmFwZTogXCJcXHU3RDJCXFx1N0VBMlwiLFxuICB2aW9sZXQ6IFwiXFx1ODRERFxcdTdEMkJcIixcbiAgZ3JheTogXCJcXHU3MDcwXCIsXG4gIGJsdWU6IFwiXFx1ODRERFwiLFxuICBjeWFuOiBcIlxcdTk3NTJcIixcbiAgdGVhbDogXCJcXHU4NEREXFx1N0VGRlwiLFxuICBncmVlbjogXCJcXHU3RUZGXCIsXG4gIHllbGxvdzogXCJcXHU5RUM0XCIsXG4gIG9yYW5nZTogXCJcXHU2QTU5XCIsXG4gIGJyb256ZTogXCJcXHU1M0U0XFx1OTREQ1wiXG59O1xudmFyIHdlbGNvbWVTY3JlZW4gPSB7XG4gIGFwcDoge1xuICAgIGNlbnRlcl9oZWFkaW5nOiBcIlxcdTYwQThcXHU3Njg0XFx1NjI0MFxcdTY3MDlcXHU2NTcwXFx1NjM2RVxcdTkwRkRcXHU1MEE4XFx1NUI1OFxcdTU3MjhcXHU2RDRGXFx1ODlDOFxcdTU2NjhcXHU2NzJDXFx1NTczMFxcdTMwMDJcIixcbiAgICBjZW50ZXJfaGVhZGluZ19wbHVzOiBcIlxcdTY2MkZcXHU1NDI2XFx1NTI0RFxcdTVGODAgRXhjYWxpZHJhdysgXFx1RkYxRlwiLFxuICAgIG1lbnVIaW50OiBcIlxcdTVCRkNcXHU1MUZBXFx1MzAwMVxcdTk5OTZcXHU5MDA5XFx1OTg3OVxcdTMwMDFcXHU4QkVEXFx1OEEwMFxcdTIwMjZcXHUyMDI2XCJcbiAgfSxcbiAgZGVmYXVsdHM6IHtcbiAgICBtZW51SGludDogXCJcXHU1QkZDXFx1NTFGQVxcdTMwMDFcXHU5OTk2XFx1OTAwOVxcdTk4NzlcXHUyMDI2XFx1MjAyNlwiLFxuICAgIGNlbnRlcl9oZWFkaW5nOiBcIlxcdTU2RkVcXHVGRjBDXFx1NTMxNlxcdTdFNDFcXHU0RTNBXFx1N0I4MFxcdTMwMDJcIixcbiAgICB0b29sYmFySGludDogXCJcXHU5MDA5XFx1NjJFOVxcdTVERTVcXHU1MTc3XFx1NUU3NlxcdTVGMDBcXHU1OUNCXFx1N0VEOFxcdTU2RkVcXHVGRjAxXCIsXG4gICAgaGVscEhpbnQ6IFwiXFx1NUZFQlxcdTYzNzdcXHU5NTJFXFx1NTQ4Q1xcdTVFMkVcXHU1MkE5XCJcbiAgfVxufTtcbnZhciBjb2xvclBpY2tlciA9IHtcbiAgbW9zdFVzZWRDdXN0b21Db2xvcnM6IFwiXFx1NUUzOFxcdTc1MjhcXHU4MUVBXFx1NUI5QVxcdTRFNDlcXHU5ODlDXFx1ODI3MlwiLFxuICBjb2xvcnM6IFwiXFx1OTg5Q1xcdTgyNzJcIixcbiAgc2hhZGVzOiBcIlxcdTgyNzJcXHU4QzAzXFx1NjYwRVxcdTY2OTdcIixcbiAgaGV4Q29kZTogXCJcXHU1MzQxXFx1NTE2RFxcdThGREJcXHU1MjM2XFx1NTAzQ1wiLFxuICBub1NoYWRlczogXCJcXHU2QjY0XFx1OTg5Q1xcdTgyNzJcXHU2Q0ExXFx1NjcwOVxcdTUzRUZcXHU3NTI4XFx1NzY4NFxcdTY2MEVcXHU2Njk3XFx1NTNEOFxcdTUzMTZcIlxufTtcbnZhciBvdmVyd3JpdGVDb25maXJtID0ge1xuICBhY3Rpb246IHtcbiAgICBleHBvcnRUb0ltYWdlOiB7XG4gICAgICB0aXRsZTogXCJcXHU1QkZDXFx1NTFGQVxcdTRFM0FcXHU1NkZFXFx1NzI0N1wiLFxuICAgICAgYnV0dG9uOiBcIlxcdTVCRkNcXHU1MUZBXFx1NEUzQVxcdTU2RkVcXHU3MjQ3XCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcXHU1QzA2XFx1NzUzQlxcdTVFMDNcXHU2NTcwXFx1NjM2RVxcdTVCRkNcXHU1MUZBXFx1NEUzQVxcdTU2RkVcXHU3MjQ3XFx1RkYwQ1xcdTRFRTVcXHU0RkJGXFx1NEVFNVxcdTU0MEVcXHU1QkZDXFx1NTE2NVxcdTMwMDJcIlxuICAgIH0sXG4gICAgc2F2ZVRvRGlzazoge1xuICAgICAgdGl0bGU6IFwiXFx1NEZERFxcdTVCNThcXHU1MjMwXFx1NjcyQ1xcdTU3MzBcIixcbiAgICAgIGJ1dHRvbjogXCJcXHU0RkREXFx1NUI1OFxcdTUyMzBcXHU2NzJDXFx1NTczMFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXFx1NUMwNlxcdTc1M0JcXHU1RTAzXFx1NjU3MFxcdTYzNkVcXHU1QkZDXFx1NTFGQVxcdTRFM0FcXHU2NTg3XFx1NEVGNlxcdUZGMENcXHU0RUU1XFx1NEZCRlxcdTRFRTVcXHU1NDBFXFx1NUJGQ1xcdTUxNjVcXHUzMDAyXCJcbiAgICB9LFxuICAgIGV4Y2FsaWRyYXdQbHVzOiB7XG4gICAgICB0aXRsZTogXCJFeGNhbGlkcmF3K1wiLFxuICAgICAgYnV0dG9uOiBcIlxcdTVCRkNcXHU1MUZBXFx1NTIzMCBFeGNhbGlkcmF3K1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXFx1NUMwNlxcdTc1M0JcXHU1RTAzXFx1NEZERFxcdTVCNThcXHU1MjMwXFx1NjBBOFxcdTc2ODQgRXhjYWxpZHJhdysgXFx1NURFNVxcdTRGNUNcXHU1MzNBXFx1MzAwMlwiXG4gICAgfVxuICB9LFxuICBtb2RhbDoge1xuICAgIGxvYWRGcm9tRmlsZToge1xuICAgICAgdGl0bGU6IFwiXFx1NEVDRVxcdTY1ODdcXHU0RUY2XFx1NTJBMFxcdThGN0RcIixcbiAgICAgIGJ1dHRvbjogXCJcXHU0RUNFXFx1NjU4N1xcdTRFRjZcXHU1MkEwXFx1OEY3RFwiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXFx1NEVDRVxcdTY1ODdcXHU0RUY2XFx1NTJBMFxcdThGN0RcXHU1QzA2PGJvbGQ+XFx1NjZGRlxcdTYzNjJcXHU2MEE4XFx1NzNCMFxcdTY3MDlcXHU3Njg0XFx1NTE4NVxcdTVCQjk8L2JvbGQ+XFx1MzAwMjxicj48L2JyPlxcdTYwQThcXHU1M0VGXFx1NEVFNVxcdTUxNDhcXHU0RjdGXFx1NzUyOFxcdTRFMEJcXHU1MjE3XFx1NjVCOVxcdTVGMEZcXHU1OTA3XFx1NEVGRFxcdTYwQThcXHU3Njg0XFx1N0VEOFxcdTU2RkVcXHUzMDAyXCJcbiAgICB9LFxuICAgIHNoYXJlYWJsZUxpbms6IHtcbiAgICAgIHRpdGxlOiBcIlxcdTRFQ0VcXHU5NEZFXFx1NjNBNVxcdTUyQTBcXHU4RjdEXCIsXG4gICAgICBidXR0b246IFwiXFx1NjZGRlxcdTYzNjJcXHU2MjExXFx1NzY4NFxcdTUxODVcXHU1QkI5XCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcXHU1MkEwXFx1OEY3RFxcdTU5MTZcXHU5MEU4XFx1N0VEOFxcdTU2RkVcXHU1QzA2PGJvbGQ+XFx1NjZGRlxcdTYzNjJcXHU2MEE4XFx1NzNCMFxcdTY3MDlcXHU3Njg0XFx1NTE4NVxcdTVCQjk8L2JvbGQ+XFx1MzAwMjxicj48L2JyPlxcdTYwQThcXHU1M0VGXFx1NEVFNVxcdTUxNDhcXHU0RjdGXFx1NzUyOFxcdTRFMEJcXHU1MjE3XFx1NjVCOVxcdTVGMEZcXHU1OTA3XFx1NEVGRFxcdTYwQThcXHU3Njg0XFx1N0VEOFxcdTU2RkVcXHUzMDAyXCJcbiAgICB9XG4gIH1cbn07XG52YXIgbWVybWFpZCA9IHtcbiAgdGl0bGU6IFwiTWVybWFpZCBcXHU4MUYzIEV4Y2FsaWRyYXdcIixcbiAgYnV0dG9uOiBcIlxcdTYzRDJcXHU1MTY1XCIsXG4gIGRlc2NyaXB0aW9uOiBcIlxcdTc2RUVcXHU1MjREXFx1NEVDNVxcdTY1MkZcXHU2MzAxPGZsb3djaGFydExpbms+XFx1NkQ0MVxcdTdBMEJcXHU1NkZFPC9mbG93Y2hhcnRMaW5rPlxcdTMwMDE8c2VxdWVuY2VMaW5rPlxcdTVFOEZcXHU1MjE3XFx1NTZGRTwvc2VxdWVuY2VMaW5rPlxcdTU0OEM8Y2xhc3NMaW5rPlxcdTdDN0JcXHU1NkZFPC9jbGFzc0xpbms+XFx1MzAwMlxcdTUxNzZcXHU0RUQ2XFx1N0M3QlxcdTU3OEJcXHU1NzI4IEV4Y2FsaWRyYXcgXFx1NEUyRFxcdTVDMDZcXHU0RUU1XFx1NTZGRVxcdTUwQ0ZcXHU1NDQ4XFx1NzNCMFxcdTMwMDJcIixcbiAgc3ludGF4OiBcIk1lcm1haWQgXFx1OEJFRFxcdTZDRDVcIixcbiAgcHJldmlldzogXCJcXHU5ODg0XFx1ODlDOFwiXG59O1xudmFyIHpoX0NOX2RlZmF1bHQgPSB7XG4gIGxhYmVscyxcbiAgbGlicmFyeSxcbiAgYnV0dG9ucyxcbiAgYWxlcnRzLFxuICBlcnJvcnMsXG4gIHRvb2xCYXIsXG4gIGhlYWRpbmdzLFxuICBoaW50cyxcbiAgY2FudmFzRXJyb3IsXG4gIGVycm9yU3BsYXNoLFxuICByb29tRGlhbG9nLFxuICBlcnJvckRpYWxvZyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWxwRGlhbG9nLFxuICBjbGVhckNhbnZhc0RpYWxvZyxcbiAgcHVibGlzaERpYWxvZyxcbiAgcHVibGlzaFN1Y2Nlc3NEaWFsb2csXG4gIGNvbmZpcm1EaWFsb2csXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBlbmNyeXB0ZWQsXG4gIHN0YXRzLFxuICB0b2FzdCxcbiAgY29sb3JzLFxuICB3ZWxjb21lU2NyZWVuLFxuICBjb2xvclBpY2tlcixcbiAgb3ZlcndyaXRlQ29uZmlybSxcbiAgbWVybWFpZFxufTtcbmV4cG9ydCB7XG4gIGFsZXJ0cyxcbiAgYnV0dG9ucyxcbiAgY2FudmFzRXJyb3IsXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBjb2xvclBpY2tlcixcbiAgY29sb3JzLFxuICBjb25maXJtRGlhbG9nLFxuICB6aF9DTl9kZWZhdWx0IGFzIGRlZmF1bHQsXG4gIGVuY3J5cHRlZCxcbiAgZXJyb3JEaWFsb2csXG4gIGVycm9yU3BsYXNoLFxuICBlcnJvcnMsXG4gIGV4cG9ydERpYWxvZyxcbiAgaGVhZGluZ3MsXG4gIGhlbHBEaWFsb2csXG4gIGhpbnRzLFxuICBpbWFnZUV4cG9ydERpYWxvZyxcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBtZXJtYWlkLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgcm9vbURpYWxvZyxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICB0b29sQmFyLFxuICB3ZWxjb21lU2NyZWVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9emgtQ04tNE1YVU9GVEguanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-CN-4MXUOFTH.js\n"));

/***/ })

}]);