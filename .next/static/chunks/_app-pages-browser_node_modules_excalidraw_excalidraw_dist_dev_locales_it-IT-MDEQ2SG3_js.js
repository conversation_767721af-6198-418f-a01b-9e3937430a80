"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_it-IT-MDEQ2SG3_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/it-IT-MDEQ2SG3.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/it-IT-MDEQ2SG3.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ it_IT_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/it-IT.json\nvar labels = {\n  paste: \"Incolla\",\n  pasteAsPlaintext: \"Incolla come testo normale\",\n  pasteCharts: \"Incolla grafici\",\n  selectAll: \"Seleziona tutto\",\n  multiSelect: \"Aggiungi elemento alla selezione\",\n  moveCanvas: \"Sposta tela\",\n  cut: \"Taglia\",\n  copy: \"Copia\",\n  copyAsPng: \"Copia negli appunti come PNG\",\n  copyAsSvg: \"Copia negli appunti come SVG\",\n  copyText: \"Copia negli appunti come testo\",\n  copySource: \"Copia sorgente negli appunti\",\n  convertToCode: \"Converti in codice\",\n  bringForward: \"Porta avanti\",\n  sendToBack: \"Manda in fondo\",\n  bringToFront: \"Porta in cima\",\n  sendBackward: \"Manda dietro\",\n  delete: \"Elimina\",\n  copyStyles: \"Copia stili\",\n  pasteStyles: \"Incolla stili\",\n  stroke: \"Tratto\",\n  background: \"Sfondo\",\n  fill: \"Riempimento\",\n  strokeWidth: \"Spessore del tratto\",\n  strokeStyle: \"Stile del tratto\",\n  strokeStyle_solid: \"Pieno\",\n  strokeStyle_dashed: \"Tratteggiato\",\n  strokeStyle_dotted: \"Punteggiato\",\n  sloppiness: \"Imprecisione\",\n  opacity: \"Opacit\\xE0\",\n  textAlign: \"Allineamento del testo\",\n  edges: \"Bordi\",\n  sharp: \"Acuto\",\n  round: \"Rotondo\",\n  arrowheads: \"Punta della freccia\",\n  arrowhead_none: \"Nessuno\",\n  arrowhead_arrow: \"Freccia\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"Cerchio\",\n  arrowhead_circle_outline: \"Cerchio (contorno)\",\n  arrowhead_triangle: \"Triangolo\",\n  arrowhead_triangle_outline: \"Triangolo (contorno)\",\n  arrowhead_diamond: \"Diamante\",\n  arrowhead_diamond_outline: \"Diamante (contorno)\",\n  fontSize: \"Dimensione carattere\",\n  fontFamily: \"Carattere\",\n  addWatermark: 'Aggiungi \"Creato con Excalidraw\"',\n  handDrawn: \"A mano libera\",\n  normal: \"Normale\",\n  code: \"Codice\",\n  small: \"Piccolo\",\n  medium: \"Medio\",\n  large: \"Grande\",\n  veryLarge: \"Molto grande\",\n  solid: \"Pieno\",\n  hachure: \"Tratteggio obliquo\",\n  zigzag: \"Zig zag\",\n  crossHatch: \"Tratteggio incrociato\",\n  thin: \"Sottile\",\n  bold: \"Grassetto\",\n  left: \"Sinistra\",\n  center: \"Centro\",\n  right: \"Destra\",\n  extraBold: \"Extra Grassetto\",\n  architect: \"Architetto\",\n  artist: \"Artista\",\n  cartoonist: \"Fumettista\",\n  fileTitle: \"Nome del file\",\n  colorPicker: \"Selettore colore\",\n  canvasColors: \"Usato su tela\",\n  canvasBackground: \"Sfondo tela\",\n  drawingCanvas: \"Area di disegno\",\n  layers: \"Livelli\",\n  actions: \"Azioni\",\n  language: \"Lingua\",\n  liveCollaboration: \"Collaborazione dal vivo...\",\n  duplicateSelection: \"Duplica\",\n  untitled: \"Senza titolo\",\n  name: \"Nome\",\n  yourName: \"Il vostro nome\",\n  madeWithExcalidraw: \"Creato con Excalidraw\",\n  group: \"Crea gruppo da selezione\",\n  ungroup: \"Dividi gruppo da selezione\",\n  collaborators: \"Collaboratori\",\n  showGrid: \"Visualizza griglia\",\n  addToLibrary: \"Aggiungi alla libreria\",\n  removeFromLibrary: \"Rimuovi dalla libreria\",\n  libraryLoadingMessage: \"Caricamento libreria\\u2026\",\n  libraries: \"Sfoglia librerie\",\n  loadingScene: \"Caricamento della scena\\u2026\",\n  align: \"Allinea\",\n  alignTop: \"Allinea in alto\",\n  alignBottom: \"Allinea in basso\",\n  alignLeft: \"Allinea a sinistra\",\n  alignRight: \"Allinea a destra\",\n  centerVertically: \"Centra Verticalmente\",\n  centerHorizontally: \"Centra orizzontalmente\",\n  distributeHorizontally: \"Distribuisci orizzontalmente\",\n  distributeVertically: \"Distribuisci verticalmente\",\n  flipHorizontal: \"Capovolgi orizzontalmente\",\n  flipVertical: \"Capovolgi verticalmente\",\n  viewMode: \"Modalit\\xE0 visualizzazione\",\n  share: \"Condividi\",\n  showStroke: \"Mostra selettore colore del tratto\",\n  showBackground: \"Mostra selettore colore di sfondo\",\n  toggleTheme: \"Cambia tema\",\n  personalLib: \"Libreria Personale\",\n  excalidrawLib: \"Libreria di Excalidraw\",\n  decreaseFontSize: \"Riduci dimensione dei caratteri\",\n  increaseFontSize: \"Aumenta la dimensione dei caratteri\",\n  unbindText: \"Scollega testo\",\n  bindText: \"Associa il testo al container\",\n  createContainerFromText: \"Avvolgi il testo in un container\",\n  link: {\n    edit: \"Modifica link\",\n    editEmbed: \"Modifica collegamento e incorpora\",\n    create: \"Crea link\",\n    createEmbed: \"Crea collegamento e incorpora\",\n    label: \"Link\",\n    labelEmbed: \"Collega & incorpora\",\n    empty: \"Nessun collegamento impostato\"\n  },\n  lineEditor: {\n    edit: \"Modifica linea\",\n    exit: \"Esci dall'editor di linea\"\n  },\n  elementLock: {\n    lock: \"Blocca\",\n    unlock: \"Sblocca\",\n    lockAll: \"Blocca tutto\",\n    unlockAll: \"Sblocca tutto\"\n  },\n  statusPublished: \"Pubblicato\",\n  sidebarLock: \"Mantieni aperta la barra laterale\",\n  selectAllElementsInFrame: \"Seleziona tutti gli elementi nel riquadro\",\n  removeAllElementsFromFrame: \"Rimuovi tutti gli elementi dal riquadro\",\n  eyeDropper: \"Scegli il colore della tela\",\n  textToDiagram: \"Testo a diagramma\",\n  prompt: \"Prompt\"\n};\nvar library = {\n  noItems: \"Nessun elemento ancora aggiunto...\",\n  hint_emptyLibrary: \"Seleziona un elemento sulla tela per aggiungerlo qui, o installa una libreria dal repository pubblico qui sotto.\",\n  hint_emptyPrivateLibrary: \"Seleziona un elemento sulla tela per aggiungerlo qui.\"\n};\nvar buttons = {\n  clearReset: \"Svuota la tela\",\n  exportJSON: \"Esporta su file\",\n  exportImage: \"Esporta immagine...\",\n  export: \"Salva in...\",\n  copyToClipboard: \"Copia negli appunti\",\n  save: \"Salva sul file corrente\",\n  saveAs: \"Salva con nome\",\n  load: \"Apri\",\n  getShareableLink: \"Ottieni link condivisibile\",\n  close: \"Chiudi\",\n  selectLanguage: \"Seleziona lingua\",\n  scrollBackToContent: \"Scorri indietro fino al contenuto\",\n  zoomIn: \"Aumenta ingrandimento\",\n  zoomOut: \"Riduci ingrandimento\",\n  resetZoom: \"Ripristina ingrandimento\",\n  menu: \"Men\\xF9\",\n  done: \"Fatto\",\n  edit: \"Modifica\",\n  undo: \"Annulla\",\n  redo: \"Ripeti\",\n  resetLibrary: \"Ripristina libreria\",\n  createNewRoom: \"Crea nuova stanza\",\n  fullScreen: \"Schermo intero\",\n  darkMode: \"Tema scuro\",\n  lightMode: \"Tema chiaro\",\n  zenMode: \"Modalit\\xE0 Zen\",\n  objectsSnapMode: \"Aggancia agli oggetti\",\n  exitZenMode: \"Uscire dalla modalit\\xE0 zen\",\n  cancel: \"Annulla\",\n  clear: \"Cancella\",\n  remove: \"Rimuovi\",\n  embed: \"Attiva/disattiva incorporamento\",\n  publishLibrary: \"Pubblica\",\n  submit: \"Invia\",\n  confirm: \"Conferma\",\n  embeddableInteractionButton: \"Clicca per interagire\"\n};\nvar alerts = {\n  clearReset: \"Questa azione canceller\\xE0 l'intera tela. Sei sicuro?\",\n  couldNotCreateShareableLink: \"Non riesco a creare un link condivisibile.\",\n  couldNotCreateShareableLinkTooBig: \"Impossibile creare il link condivisibile: la scena \\xE8 troppo grande\",\n  couldNotLoadInvalidFile: \"Impossibile caricare un file no valido\",\n  importBackendFailed: \"Importazione dal server fallita.\",\n  cannotExportEmptyCanvas: \"Non \\xE8 possibile esportare una tela vuota.\",\n  couldNotCopyToClipboard: \"Impossibile copiare negli appunti.\",\n  decryptFailed: \"Impossibile decriptare i dati.\",\n  uploadedSecurly: \"L'upload \\xE8 stato protetto con la crittografia end-to-end, il che significa che il server Excalidraw e terze parti non possono leggere il contenuto.\",\n  loadSceneOverridePrompt: \"Se carichi questo disegno esterno, sostituir\\xE0 quello che hai. Vuoi continuare?\",\n  collabStopOverridePrompt: \"Interrompere la sessione sovrascriver\\xE0 il precedente disegno memorizzato localmente. Sei sicuro?\\n\\n(Se vuoi mantenere il tuo disegno locale, chiudi semplicemente la scheda del browser.)\",\n  errorAddingToLibrary: \"Impossibile aggiungere l'elemento alla libreria\",\n  errorRemovingFromLibrary: \"Impossibile rimuovere l'elemento dalla libreria\",\n  confirmAddLibrary: \"Questo aggiunger\\xE0 {{numShapes}} forma(e) alla tua libreria. Sei sicuro?\",\n  imageDoesNotContainScene: \"Questa immagine pare non contenere alcuna scena. Avevi incluso la scena durante l'esportazione?\",\n  cannotRestoreFromImage: \"Impossibile ripristinare la scena da questo file immagine\",\n  invalidSceneUrl: \"Impossibile importare la scena dall'URL fornito. Potrebbe essere malformato o non contenere dati JSON Excalidraw validi.\",\n  resetLibrary: \"Questa azione canceller\\xE0 l'intera libreria. Sei sicuro?\",\n  removeItemsFromsLibrary: \"Eliminare {{count}} elementi dalla libreria?\",\n  invalidEncryptionKey: \"La chiave di cifratura deve essere composta da 22 caratteri. La collaborazione live \\xE8 disabilitata.\",\n  collabOfflineWarning: \"Nessuna connessione internet disponibile.\\nLe tue modifiche non verranno salvate!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipo di file non supportato.\",\n  imageInsertError: \"Non \\xE8 stato possibile inserire l'immagine. Riprova pi\\xF9 tardi...\",\n  fileTooBig: \"Il file \\xE8 troppo grande. La dimensione massima consentita \\xE8 {{maxSize}}.\",\n  svgImageInsertError: \"Impossibile inserire l'immagine SVG. Il markup SVG non sembra corretto.\",\n  failedToFetchImage: \"Impossibile recuperare l'immagine.\",\n  invalidSVGString: \"SVG non valido.\",\n  cannotResolveCollabServer: \"Impossibile connettersi al server di collab. Ricarica la pagina e riprova.\",\n  importLibraryError: \"Impossibile caricare la libreria\",\n  collabSaveFailed: \"Impossibile salvare nel database di backend. Se i problemi persistono, dovresti salvare il tuo file localmente per assicurarti di non perdere il tuo lavoro.\",\n  collabSaveFailed_sizeExceeded: \"Impossibile salvare nel database di backend, la tela sembra essere troppo grande. Dovresti salvare il file localmente per assicurarti di non perdere il tuo lavoro.\",\n  imageToolNotSupported: \"Le immagini sono disabilitate.\",\n  brave_measure_text_error: {\n    line1: \"Sembra che tu stia utilizzando il browser Brave con l'impostazione <bold>Blocco aggressivo delle impronte digitali</bold> abilitata.\",\n    line2: \"Ci\\xF2 potrebbe causare la rottura degli <bold>Elementi di testo</bold> nei tuoi disegni.\",\n    line3: \"Consigliamo vivamente di disabilitare questa impostazione. Puoi seguire <link>questi passaggi</link> su come farlo.\",\n    line4: \"Se la disattivazione di questa impostazione non risolve la visualizzazione degli elementi di testo, apri un <issueLink>problema</issueLink> sul nostro GitHub o scrivici su <discordLink>Discord</discordLink>\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"Gli elementi incorporabili non possono essere aggiunti alla libreria.\",\n    iframe: \"Gli elementi IFrame non possono essere aggiunti alla libreria.\",\n    image: \"Il supporto per l'aggiunta d'immagini alla libreria verr\\xE0 aggiunto a breve!\"\n  },\n  asyncPasteFailedOnRead: \"Impossibile incollare (non \\xE8 possibile leggere dagli appunti di sistema).\",\n  asyncPasteFailedOnParse: \"Impossibile incollare.\",\n  copyToSystemClipboardFailed: \"Impossibile copiare negli appunti.\"\n};\nvar toolBar = {\n  selection: \"Selezione\",\n  image: \"Inserisci immagine\",\n  rectangle: \"Rettangolo\",\n  diamond: \"Rombo\",\n  ellipse: \"Ellisse\",\n  arrow: \"Freccia\",\n  line: \"Linea\",\n  freedraw: \"Disegno\",\n  text: \"Testo\",\n  library: \"Libreria\",\n  lock: \"Mantieni lo strumento selezionato attivo dopo aver disegnato\",\n  penMode: \"Modalit\\xE0 penna - previene il tocco\",\n  link: \"Aggiungi/ aggiorna il link per una forma selezionata\",\n  eraser: \"Gomma\",\n  frame: \"Strumento riquadro\",\n  magicframe: \"\",\n  embeddable: \"Incorporamento Web\",\n  laser: \"Puntatore laser\",\n  hand: \"Mano (strumento di panoramica)\",\n  extraTools: \"Altri strumenti\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"Impostazioni di IA\"\n};\nvar headings = {\n  canvasActions: \"Azioni sulla Tela\",\n  selectedShapeActions: \"Impostazioni della forma selezionata\",\n  shapes: \"Forme\"\n};\nvar hints = {\n  canvasPanning: \"Per spostare la tela, tieni premuta la rotellina del mouse o la barra spaziatrice mentre trascini oppure usa lo strumento mano\",\n  linearElement: \"Clicca per iniziare una linea in pi\\xF9 punti, trascina per singola linea\",\n  freeDraw: \"Clicca e trascina, rilascia quando avrai finito\",\n  text: \"Suggerimento: puoi anche aggiungere del testo facendo doppio clic ovunque con lo strumento di selezione\",\n  embeddable: \"Fare click e trascina per creare un incorporamento web\",\n  text_selected: \"Fai doppio click o premi INVIO per modificare il testo\",\n  text_editing: \"Premi ESC o CtrlOCmd+INVIO per completare le modifiche\",\n  linearElementMulti: \"Clicca sull'ultimo punto o premi Esc o Invio per finire\",\n  lockAngle: \"Puoi limitare l'angolo tenendo premuto SHIFT\",\n  resize: \"Per vincolare le proporzioni, tieni premuto MAIUSC durante il ridimensionamento;\\nper ridimensionare dal centro, tieni premuto ALT\",\n  resizeImage: \"Puoi ridimensionare liberamente tenendo premuto SHIFT,\\ntieni premuto ALT per ridimensionare dal centro\",\n  rotate: \"Puoi mantenere gli angoli tenendo premuto SHIFT durante la rotazione\",\n  lineEditor_info: \"Tieni premuto Ctrl o Cmd e doppio clic oppure premi Ctrl o Cmd + Invio per modificare i punti\",\n  lineEditor_pointSelected: \"Premi Elimina per rimuovere il punto(i),\\nCtrlOCmd+D per duplicare o trascinare per spostare\",\n  lineEditor_nothingSelected: \"Seleziona un punto da modificare (tieni premuto MAIUSC per selezionare pi\\xF9 punti),\\noppure tieni premuto Alt e fai clic per aggiungere nuovi punti\",\n  placeImage: \"Fai click per posizionare l'immagine, o click e trascina per impostarne la dimensione manualmente\",\n  publishLibrary: \"Pubblica la tua libreria\",\n  bindTextToElement: \"Premi invio per aggiungere il testo\",\n  deepBoxSelect: \"Tieni premuto CtrlOCmd per selezionare in profondit\\xE0 e per impedire il trascinamento\",\n  eraserRevert: \"Tieni premuto Alt per ripristinare gli elementi contrassegnati per l'eliminazione\",\n  firefox_clipboard_write: 'Questa funzione pu\\xF2 essere abilitata impostando il flag \"dom.events.asyncClipboard.clipboardItem\" su \"true\". Per modificare i flag del browser in Firefox, visitare la pagina \"about:config\".',\n  disableSnapping: \"Tieni premuto Ctrl o Cmd per disabilitare lo snap\"\n};\nvar canvasError = {\n  cannotShowPreview: \"Impossibile visualizzare l'anteprima\",\n  canvasTooBig: \"La tela potrebbe essere troppo grande.\",\n  canvasTooBigTip: \"Suggerimento: prova a spostare gli elementi pi\\xF9 lontani pi\\xF9 vicini tra loro.\"\n};\nvar errorSplash = {\n  headingMain: \"Si \\xE8 verificato un errore. Provare <button>ricaricando la pagina.</button>\",\n  clearCanvasMessage: \"Se ricaricare non funziona, prova <button>pulire la tela.</button>\",\n  clearCanvasCaveat: \" Questo risulter\\xE0 nella perdita del lavoro \",\n  trackedToSentry: \"L'errore con identificativo {{eventId}} \\xE8 stato tracciato nel nostro sistema.\",\n  openIssueMessage: \"Siamo stati molto cauti nel non includere informazioni della scena nell'errore. Se la tua scena non \\xE8 privata, ti preghiamo di considerare la sua inclusione nel nostro <button>bug tracker.</button> Per favore includi le informazioni riportate qui sotto copiandole e incollandole nella issue di GitHub.\",\n  sceneContent: \"Contenuto della scena:\"\n};\nvar roomDialog = {\n  desc_intro: \"Puoi invitare persone nella tua scena attuale per collaborare con te.\",\n  desc_privacy: \"Non preoccuparti, la sessione utilizza la crittografia end-to-end, quindi qualsiasi cosa disegni rimarr\\xE0 privata. Nemmeno il nostro server sar\\xE0 in grado di vedere cosa hai creato.\",\n  button_startSession: \"Avvia sessione\",\n  button_stopSession: \"Termina sessione\",\n  desc_inProgressIntro: \"La sessione di collaborazione \\xE8 attualmente in corso.\",\n  desc_shareLink: \"Condividi questo link con chiunque desideri collaborare:\",\n  desc_exitSession: \"Interrompere la sessione scollegher\\xE0 la tua stanza ma potrai continuare a lavorare con la scena, localmente. Tieni presente che questo non influir\\xE0 sulle altre persone, e che saranno ancora in grado di collaborare alla loro versione.\",\n  shareTitle: \"Partecipa a una sessione di collaborazione live su Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Errore\"\n};\nvar exportDialog = {\n  disk_title: \"Salva su disco\",\n  disk_details: \"Esporta i dati della scena su file, dal quale potrai importare in seguito.\",\n  disk_button: \"Salva su file\",\n  link_title: \"Link condivisibile\",\n  link_details: \"Esporta come link di sola lettura.\",\n  link_button: \"Esporta come Link\",\n  excalidrawplus_description: \"Salva la scena nel tuo spazio di lavoro Excalidraw+.\",\n  excalidrawplus_button: \"Esporta\",\n  excalidrawplus_exportError: \"Non \\xE8 stato possibile esportare su Excalidraw+ al questo momento...\"\n};\nvar helpDialog = {\n  blog: \"Leggi il nostro blog\",\n  click: \"click\",\n  deepSelect: \"Selezione profonda\",\n  deepBoxSelect: \"Seleziona in profondit\\xE0 all'interno della casella e previene il trascinamento\",\n  curvedArrow: \"Freccia curva\",\n  curvedLine: \"Linea curva\",\n  documentation: \"Documentazione\",\n  doubleClick: \"doppio-click\",\n  drag: \"trascina\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Modifica punti linea/freccia\",\n  editText: \"Modifica testo / aggiungi etichetta\",\n  github: \"Trovato un problema? Segnalalo\",\n  howto: \"Segui le nostre guide\",\n  or: \"oppure\",\n  preventBinding: \"Impedisci legame della freccia\",\n  tools: \"Stumenti\",\n  shortcuts: \"Scorciatoie da tastiera\",\n  textFinish: \"Completa la modifica (editor di testo)\",\n  textNewLine: \"Aggiungi nuova riga (editor di testo)\",\n  title: \"Guida\",\n  view: \"Vista\",\n  zoomToFit: \"Adatta zoom per mostrare tutti gli elementi\",\n  zoomToSelection: \"Zoom alla selezione\",\n  toggleElementLock: \"Blocca/sblocca selezione\",\n  movePageUpDown: \"Sposta la pagina su/gi\\xF9\",\n  movePageLeftRight: \"Sposta la pagina a sinistra/destra\"\n};\nvar clearCanvasDialog = {\n  title: \"Svuota la tela\"\n};\nvar publishDialog = {\n  title: \"Pubblica la libreria\",\n  itemName: \"Nome dell'elemento\",\n  authorName: \"Nome dell'autore\",\n  githubUsername: \"Nome utente di GitHub\",\n  twitterUsername: \"Nome utente di Twitter\",\n  libraryName: \"Nome della libreria\",\n  libraryDesc: \"Descrizione della libreria\",\n  website: \"Sito Web\",\n  placeholder: {\n    authorName: \"Il tuo nome o nome utente\",\n    libraryName: \"Nome della tua libreria\",\n    libraryDesc: \"Descrizione della tua libreria per aiutare le persone a comprenderne lo scopo\",\n    githubHandle: \"Handle di GitHub (opzionale), cos\\xEC che tu possa modificare la libreria una volta inviata per la revisione\",\n    twitterHandle: \"Nome utente di Twitter (opzionale), cos\\xEC che sappiamo chi accreditare promuovendo su Twitter\",\n    website: \"Link al tuo sito web personale o altro (opzionale)\"\n  },\n  errors: {\n    required: \"Obbligatorio\",\n    website: \"Inserisci un URL valido\"\n  },\n  noteDescription: \"Invia la tua libreria da includere nella <link>repository della libreria pubblica</link>perch\\xE9 sia usata da altri nei loro disegni.\",\n  noteGuidelines: \"La libreria dev'esser prima approvata manualmente. Sei pregato di leggere le <link>linee guida</link> prima di inviarla. Necessiterai di un profilo di GitHub per comunicare ed effettuare modifiche se richiesto, ma non \\xE8 strettamente necessario.\",\n  noteLicense: \"Inviando, acconsenti che la libreria sar\\xE0 pubblicata sotto la <link>Licenza MIT, </link>che in breve significa che chiunque possa usarla senza restrizioni.\",\n  noteItems: \"Ogni elemento della libreria deve avere il proprio nome, cos\\xEC che sia filtrabile. Gli elementi della seguente libreria saranno inclusi:\",\n  atleastOneLibItem: \"Sei pregato di selezionare almeno un elemento della libreria per iniziare\",\n  republishWarning: \"Nota: alcuni degli elementi selezionati sono contrassegnati come gi\\xE0 pubblicati/presentati. \\xC8 necessario reinviare gli elementi solo quando si aggiorna una libreria o una presentazione esistente.\"\n};\nvar publishSuccessDialog = {\n  title: \"Libreria inviata\",\n  content: \"Grazie {{authorName}}. La tua libreria \\xE8 stata inviata per la revisione. Puoi monitorarne lo stato<link>qui</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Ripristina la libreria\",\n  removeItemsFromLib: \"Rimuovi gli elementi selezionati dalla libreria\"\n};\nvar imageExportDialog = {\n  header: \"Esporta immagine\",\n  label: {\n    withBackground: \"Sfondo\",\n    onlySelected: \"Solo selezionato\",\n    darkMode: \"Tema scuro\",\n    embedScene: \"Includi scena\",\n    scale: \"Scala\",\n    padding: \"Rientro\"\n  },\n  tooltip: {\n    embedScene: \"I dati della scena saranno salvati nel file PNG/SVG esportato in modo che la scena possa essere ripristinata da esso.\\nQuesto aumenter\\xE0 la dimensione del file esportato.\"\n  },\n  title: {\n    exportToPng: \"Esporta come PNG\",\n    exportToSvg: \"Esporta come SVG\",\n    copyPngToClipboard: \"Copia PNG negli appunti\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copia negli appunti\"\n  }\n};\nvar encrypted = {\n  tooltip: \"I tuoi disegni sono crittografati end-to-end in modo che i server di Excalidraw non li possano mai vedere.\",\n  link: \"Articolo del blog sulla crittografia end-to-end di Excalidraw\"\n};\nvar stats = {\n  angle: \"Angolo\",\n  element: \"Elemento\",\n  elements: \"Elementi\",\n  height: \"Altezza\",\n  scene: \"Scena\",\n  selected: \"Selezionato\",\n  storage: \"Memoria\",\n  title: \"Statistiche per nerd\",\n  total: \"Totale\",\n  version: \"Versione\",\n  versionCopy: \"Clicca per copiare\",\n  versionNotAvailable: \"Versione non disponibile\",\n  width: \"Larghezza\"\n};\nvar toast = {\n  addedToLibrary: \"Aggiunto alla libreria\",\n  copyStyles: \"Stili copiati.\",\n  copyToClipboard: \"Copiato negli appunti.\",\n  copyToClipboardAsPng: \"{{exportSelection}} copiato negli appunti come PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"File salvato.\",\n  fileSavedToFilename: \"Salvato in {filename}\",\n  canvas: \"tela\",\n  selection: \"selezione\",\n  pasteAsSingleElement: \"Usa {{shortcut}} per incollare come un singolo elemento,\\no incollare in un editor di testo esistente\",\n  unableToEmbed: \"Incorporare questo url non \\xE8 permesso. Crea una issue su GitHub per richiedere che l'url sia autorizzato\",\n  unrecognizedLinkFormat: \"Il link che hai incorporato non corrisponde al formato previsto. Prova a incollare la stringa 'embed' fornita dal sito di origine\"\n};\nvar colors = {\n  transparent: \"Trasparente\",\n  black: \"Nero\",\n  white: \"Bianco\",\n  red: \"Rosso\",\n  pink: \"Rosa\",\n  grape: \"Uva\",\n  violet: \"Viola\",\n  gray: \"Grigio\",\n  blue: \"Blu\",\n  cyan: \"Ciano\",\n  teal: \"Verde acqua\",\n  green: \"Verde\",\n  yellow: \"Giallo\",\n  orange: \"Arancio\",\n  bronze: \"Bronzo\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Tutti i tuoi dati sono salvati localmente nel browser.\",\n    center_heading_plus: \"Volevi invece andare su Excalidraw+?\",\n    menuHint: \"Esporta, preferenze, lingue, ...\"\n  },\n  defaults: {\n    menuHint: \"Esporta, preferenze, e altro...\",\n    center_heading: \"Diagrammi. Fatto. Semplice.\",\n    toolbarHint: \"Scegli uno strumento & Inizia a disegnare!\",\n    helpHint: \"Scorciatoie & aiuto\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Colori personalizzati pi\\xF9 utilizzati\",\n  colors: \"Colori\",\n  shades: \"Sfumature\",\n  hexCode: \"Codice esadecimale\",\n  noShades: \"Nessuna sfumatura disponibile per questo colore\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Esporta come immagine\",\n      button: \"Esporta come immagine\",\n      description: \"Esporta i dati della scena come immagine, che potrai importare in seguito.\"\n    },\n    saveToDisk: {\n      title: \"Salva su disco\",\n      button: \"Salva su disco\",\n      description: \"Esporta i dati della scena su file, che potrai importare in seguito.\"\n    },\n    excalidrawPlus: {\n      title: \"Excalidraw+\",\n      button: \"Esporta su Excalidraw+\",\n      description: \"Salva la scena sul tuo spazio di lavoro Excalidraw+.\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Carica da file\",\n      button: \"Carica da file\",\n      description: \"Il caricamento da file sostituir\\xE0 <bold>il contenuto esistente</bold>.<br></br>Puoi salvare il tuo disegno prima usando una delle opzioni qui sotto.\"\n    },\n    shareableLink: {\n      title: \"Carica da link\",\n      button: \"Sostituisci il mio contenuto\",\n      description: \"Il caricamento da file sostituir\\xE0 <bold>il contenuto esistente</bold>.<br></br>Puoi salvare il tuo disegno prima usando una delle opzioni qui sotto.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"Inserisci\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"Anteprima\"\n};\nvar it_IT_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=it-IT-MDEQ2SG3.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/it-IT-MDEQ2SG3.js\n"));

/***/ })

}]);