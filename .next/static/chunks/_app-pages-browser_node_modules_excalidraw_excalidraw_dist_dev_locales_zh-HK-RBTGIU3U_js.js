"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_zh-HK-RBTGIU3U_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-HK-RBTGIU3U.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-HK-RBTGIU3U.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ zh_HK_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/zh-HK.json\nvar labels = {\n  paste: \"\\u8CBC\\u4E0A\",\n  pasteAsPlaintext: \"\",\n  pasteCharts: \"\\u8CBC\\u4E0A\\u5716\\u8868\",\n  selectAll: \"\\u5168\\u9078\",\n  multiSelect: \"\\u591A\\u91CD\\u9078\\u53D6\",\n  moveCanvas: \"\\u79FB\\u52D5\\u756B\\u5E03\",\n  cut: \"\\u526A\\u4E0B\",\n  copy: \"\\u8907\\u88FD\",\n  copyAsPng: \"\\u4EE5 PNG \\u683C\\u5F0F\\u8907\\u88FD\",\n  copyAsSvg: \"\\u4EE5 SVG \\u683C\\u5F0F\\u8907\\u88FD\",\n  copyText: \"\",\n  copySource: \"\",\n  convertToCode: \"\",\n  bringForward: \"\\u5F80\\u4E0A\\u4E00\\u5C64\\u79FB\\u52D5\",\n  sendToBack: \"\\u79FB\\u5230\\u6700\\u5E95\\u5C64\",\n  bringToFront: \"\\u79FB\\u5230\\u6700\\u4E0A\\u5C64\",\n  sendBackward: \"\\u5F80\\u4E0B\\u4E00\\u5C64\\u79FB\\u52D5\",\n  delete: \"\\u522A\\u9664\",\n  copyStyles: \"\\u8907\\u88FD\\u6A23\\u5F0F\",\n  pasteStyles: \"\\u5957\\u7528\\u6A23\\u5F0F\",\n  stroke: \"\\u7B46\\u8DE1\\u984F\\u8272\",\n  background: \"\\u586B\\u5145\\u984F\\u8272\",\n  fill: \"\\u80CC\\u666F\\u6A23\\u5F0F\",\n  strokeWidth: \"\\u7B46\\u8DE1\\u5BEC\\u5EA6\",\n  strokeStyle: \"\\u7B46\\u8DE1\\u7DDA\\u689D\",\n  strokeStyle_solid: \"\\u5BE6\\u7DDA\",\n  strokeStyle_dashed: \"\\u865B\\u7DDA\\uFF08\\u8F03\\u5BC6\\uFF09\",\n  strokeStyle_dotted: \"\\u865B\\u7DDA\\uFF08\\u8F03\\u758F\\uFF09\",\n  sloppiness: \"\\u7B46\\u8DE1\\u98A8\\u683C\",\n  opacity: \"\\u900F\\u660E\\u5EA6\",\n  textAlign: \"\\u6587\\u5B57\\u5C0D\\u9F4A\",\n  edges: \"\\u908A\\u89D2\\u6A23\\u5F0F\",\n  sharp: \"\\u92B3\\u89D2\",\n  round: \"\\u5713\\u89D2\",\n  arrowheads: \"\\u7BAD\\u5634\",\n  arrowhead_none: \"\\u7121\\u7BAD\\u5634\",\n  arrowhead_arrow: \"\\u666E\\u901A\\u7BAD\\u5634\",\n  arrowhead_bar: \"\\u5E73\\u982D\\u689D\\u72C0\",\n  arrowhead_circle: \"\",\n  arrowhead_circle_outline: \"\",\n  arrowhead_triangle: \"\\u4E09\\u89D2\\u7BAD\\u5634\",\n  arrowhead_triangle_outline: \"\",\n  arrowhead_diamond: \"\",\n  arrowhead_diamond_outline: \"\",\n  fontSize: \"\\u5B57\\u578B\\u5927\\u5C0F\",\n  fontFamily: \"\\u5B57\\u9AD4\",\n  addWatermark: \"\\u52A0\\u5165\\u300C\\u4F7F\\u7528 Excalidraw \\u88FD\\u5716\\u300D\\u6C34\\u5370\",\n  handDrawn: \"\\u624B\\u7E6A\\u9AD4\",\n  normal: \"\\u96FB\\u8166\\u5B57\\u9AD4\",\n  code: \"\\u7B49\\u5BEC\\u9AD4\",\n  small: \"\\u7D30\",\n  medium: \"\\u4E2D\",\n  large: \"\\u5927\",\n  veryLarge: \"\\u52C1\\u5927\",\n  solid: \"\\u5BE6\\u5FC3\",\n  hachure: \"\\u659C\\u7DDA\",\n  zigzag: \"\",\n  crossHatch: \"\\u4EA4\\u53C9\\u683C\\u4ED4\",\n  thin: \"\\u5E7C\",\n  bold: \"\\u7C97\",\n  left: \"\\u9760\\u5DE6\\u5C0D\\u9F4A\",\n  center: \"\\u7F6E\\u4E2D\\u5C0D\\u9F4A\",\n  right: \"\\u9760\\u53F3\\u5C0D\\u9F4A\",\n  extraBold: \"\\u52C1\\u7C97\",\n  architect: \"\\u624B\\u7E6A\\u98A8\\u683C\",\n  artist: \"\\u85DD\\u8853\\u5BB6\\u98A8\\u683C\",\n  cartoonist: \"\\u5361\\u901A\\u98A8\\u683C\",\n  fileTitle: \"\\u6A94\\u6848\\u540D\\u7A31\",\n  colorPicker: \"\",\n  canvasColors: \"\",\n  canvasBackground: \"\\u756B\\u5E03\\u80CC\\u666F\\u984F\\u8272\",\n  drawingCanvas: \"\\u756B\\u5E03\",\n  layers: \"\\u5716\\u5C64\",\n  actions: \"\\u52D5\\u4F5C\",\n  language: \"\\u{1F30F} \\u8A9E\\u8A00\",\n  liveCollaboration: \"\",\n  duplicateSelection: \"\\u88FD\\u4F5C\\u526F\\u672C\",\n  untitled: \"\\u672A\\u547D\\u540D\\u7684\\u4F5C\\u54C1\",\n  name: \"\",\n  yourName: \"\\u4F60\\u7684\\u540D\\u7A31\",\n  madeWithExcalidraw: \"\\u4F7F\\u7528 Excalidraw \\u88FD\\u5716\",\n  group: \"\\u5EFA\\u7ACB\\u7269\\u4EF6\\u7FA4\\u7D44\",\n  ungroup: \"\\u53D6\\u6D88\\u7269\\u4EF6\\u7FA4\\u7D44\",\n  collaborators: \"\\u5DF2\\u9023\\u7DDA\\u7684\\u5354\\u4F5C\\u8005\",\n  showGrid: \"\\u986F\\u793A\\u7DB2\\u683C\",\n  addToLibrary: \"\\u52A0\\u5165\\u4F5C\\u54C1\\u5EAB\",\n  removeFromLibrary: \"\\u5F9E\\u4F5C\\u54C1\\u5EAB\\u4E2D\\u79FB\\u9664\",\n  libraryLoadingMessage: \"\\u6B63\\u5728\\u8F09\\u5165\\u4F5C\\u54C1\\u5EAB\\u2026\",\n  libraries: \"\\u700F\\u89BD\\u4F5C\\u54C1\\u5EAB\",\n  loadingScene: \"\\u8F09\\u5165\\u756B\\u5E03\\u4E2D\\u2026\",\n  align: \"\\u7269\\u4EF6\\u5C0D\\u9F4A\",\n  alignTop: \"\\u6C34\\u5E73\\u7F6E\\u9802\",\n  alignBottom: \"\\u6C34\\u5E73\\u7F6E\\u5E95\",\n  alignLeft: \"\\u5782\\u76F4\\u9760\\u5DE6\\u5C0D\\u9F4A\",\n  alignRight: \"\\u5782\\u76F4\\u9760\\u53F3\\u5C0D\\u9F4A\",\n  centerVertically: \"\\u5782\\u76F4\\u7F6E\\u4E2D\",\n  centerHorizontally: \"\\u6C34\\u5E73\\u7F6E\\u4E2D\",\n  distributeHorizontally: \"\\u5DE6\\u53F3\\u7B49\\u8DDD\",\n  distributeVertically: \"\\u4E0A\\u4E0B\\u7B49\\u8DDD\",\n  flipHorizontal: \"\\u5DE6\\u53F3\\u53CD\\u8F49\",\n  flipVertical: \"\\u4E0A\\u4E0B\\u53CD\\u8F49\",\n  viewMode: \"\\u552F\\u8B80\\u6A21\\u5F0F\",\n  share: \"\",\n  showStroke: \"\",\n  showBackground: \"\",\n  toggleTheme: \"\",\n  personalLib: \"\",\n  excalidrawLib: \"\",\n  decreaseFontSize: \"\",\n  increaseFontSize: \"\",\n  unbindText: \"\",\n  bindText: \"\",\n  createContainerFromText: \"\",\n  link: {\n    edit: \"\",\n    editEmbed: \"\",\n    create: \"\",\n    createEmbed: \"\",\n    label: \"\",\n    labelEmbed: \"\",\n    empty: \"\"\n  },\n  lineEditor: {\n    edit: \"\",\n    exit: \"\"\n  },\n  elementLock: {\n    lock: \"\",\n    unlock: \"\",\n    lockAll: \"\",\n    unlockAll: \"\"\n  },\n  statusPublished: \"\",\n  sidebarLock: \"\",\n  selectAllElementsInFrame: \"\",\n  removeAllElementsFromFrame: \"\",\n  eyeDropper: \"\",\n  textToDiagram: \"\",\n  prompt: \"\"\n};\nvar library = {\n  noItems: \"\",\n  hint_emptyLibrary: \"\",\n  hint_emptyPrivateLibrary: \"\"\n};\nvar buttons = {\n  clearReset: \"\\u6E05\\u7A7A\\u756B\\u5E03\",\n  exportJSON: \"\",\n  exportImage: \"\",\n  export: \"\",\n  copyToClipboard: \"\",\n  save: \"\",\n  saveAs: \"\",\n  load: \"\",\n  getShareableLink: \"\",\n  close: \"\",\n  selectLanguage: \"\",\n  scrollBackToContent: \"\",\n  zoomIn: \"\",\n  zoomOut: \"\",\n  resetZoom: \"\",\n  menu: \"\",\n  done: \"\",\n  edit: \"\",\n  undo: \"\",\n  redo: \"\",\n  resetLibrary: \"\",\n  createNewRoom: \"\",\n  fullScreen: \"\",\n  darkMode: \"\",\n  lightMode: \"\",\n  zenMode: \"\",\n  objectsSnapMode: \"\",\n  exitZenMode: \"\",\n  cancel: \"\",\n  clear: \"\",\n  remove: \"\",\n  embed: \"\",\n  publishLibrary: \"\",\n  submit: \"\",\n  confirm: \"\",\n  embeddableInteractionButton: \"\"\n};\nvar alerts = {\n  clearReset: \"\\u3010\\u203C\\uFE0F \\u8B66\\u544A \\u203C\\uFE0F \\u7121\\u6CD5\\u5FA9\\u539F\\u7684\\u52D5\\u4F5C\\u3011\\u4F60\\u78BA\\u5B9A\\u8981\\u6E05\\u7A7A\\u5462\\u584A\\u756B\\u5E03\\u55CE\\uFF1F\",\n  couldNotCreateShareableLink: \"\",\n  couldNotCreateShareableLinkTooBig: \"\",\n  couldNotLoadInvalidFile: \"\",\n  importBackendFailed: \"\",\n  cannotExportEmptyCanvas: \"\\u7121\\u5622\\u53EF\\u4EE5\\u532F\\u51FA\\u558E\\uFF5E\\u756B\\u5413\\u5622\\u5148\\uFF1F\",\n  couldNotCopyToClipboard: \"\",\n  decryptFailed: \"\",\n  uploadedSecurly: \"\",\n  loadSceneOverridePrompt: \"\",\n  collabStopOverridePrompt: \"\",\n  errorAddingToLibrary: \"\",\n  errorRemovingFromLibrary: \"\",\n  confirmAddLibrary: \"\",\n  imageDoesNotContainScene: \"\",\n  cannotRestoreFromImage: \"\",\n  invalidSceneUrl: \"\",\n  resetLibrary: \"\",\n  removeItemsFromsLibrary: \"\",\n  invalidEncryptionKey: \"\",\n  collabOfflineWarning: \"\"\n};\nvar errors = {\n  unsupportedFileType: \"\",\n  imageInsertError: \"\",\n  fileTooBig: \"\",\n  svgImageInsertError: \"\",\n  failedToFetchImage: \"\",\n  invalidSVGString: \"\",\n  cannotResolveCollabServer: \"\",\n  importLibraryError: \"\",\n  collabSaveFailed: \"\",\n  collabSaveFailed_sizeExceeded: \"\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"\",\n    line2: \"\",\n    line3: \"\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"\",\n  asyncPasteFailedOnParse: \"\",\n  copyToSystemClipboardFailed: \"\"\n};\nvar toolBar = {\n  selection: \"\",\n  image: \"\",\n  rectangle: \"\",\n  diamond: \"\",\n  ellipse: \"\",\n  arrow: \"\",\n  line: \"\",\n  freedraw: \"\",\n  text: \"\",\n  library: \"\",\n  lock: \"\",\n  penMode: \"\",\n  link: \"\",\n  eraser: \"\",\n  frame: \"\",\n  magicframe: \"\",\n  embeddable: \"\",\n  laser: \"\",\n  hand: \"\",\n  extraTools: \"\",\n  mermaidToExcalidraw: \"\",\n  magicSettings: \"\"\n};\nvar headings = {\n  canvasActions: \"\\u756B\\u5E03\\u52D5\\u4F5C\",\n  selectedShapeActions: \"\",\n  shapes: \"\"\n};\nvar hints = {\n  canvasPanning: \"\",\n  linearElement: \"\",\n  freeDraw: \"\",\n  text: \"\",\n  embeddable: \"\",\n  text_selected: \"\",\n  text_editing: \"\",\n  linearElementMulti: \"\",\n  lockAngle: \"\",\n  resize: \"\",\n  resizeImage: \"\",\n  rotate: \"\",\n  lineEditor_info: \"\",\n  lineEditor_pointSelected: \"\",\n  lineEditor_nothingSelected: \"\",\n  placeImage: \"\",\n  publishLibrary: \"\",\n  bindTextToElement: \"\",\n  deepBoxSelect: \"\",\n  eraserRevert: \"\",\n  firefox_clipboard_write: \"\",\n  disableSnapping: \"\"\n};\nvar canvasError = {\n  cannotShowPreview: \"\\u7121\\u6CD5\\u986F\\u793A\\u9810\\u89BD\",\n  canvasTooBig: \"\\u584A\\u756B\\u5E03\\u592A\\u5927\\u5566\\uFF0C\\u7E2E\\u7D30\\u5572\\u5148\\u5566\\uFF1F\",\n  canvasTooBigTip: \"\"\n};\nvar errorSplash = {\n  headingMain: \"\",\n  clearCanvasMessage: \"\\u5982\\u679C\\u91CD\\u65B0\\u6574\\u7406\\u9801\\u9762\\u90FD\\u4FC2\\u7747\\u5514\\u5230\\uFF0C\\u4F60\\u53EF\\u4EE5<button>\\u6E05\\u7A7A\\u756B\\u5E03</button>\",\n  clearCanvasCaveat: \"\\uFF08\\u6CE8\\u610F\\uFF1A\\u5462\\u500B\\u52D5\\u4F5C\\u6703\\u76F4\\u63A5\\u4E1F\\u68C4\\u4F60\\u5605\\u4F5C\\u54C1\\uFF0C\\u4E26\\u4E14\\u7121\\u6CD5\\u5FA9\\u539F\\uFF09\",\n  trackedToSentry: \"\",\n  openIssueMessage: \"\",\n  sceneContent: \"\"\n};\nvar roomDialog = {\n  desc_intro: \"\",\n  desc_privacy: \"\",\n  button_startSession: \"\",\n  button_stopSession: \"\",\n  desc_inProgressIntro: \"\",\n  desc_shareLink: \"\",\n  desc_exitSession: \"\",\n  shareTitle: \"\"\n};\nvar errorDialog = {\n  title: \"\"\n};\nvar exportDialog = {\n  disk_title: \"\",\n  disk_details: \"\",\n  disk_button: \"\",\n  link_title: \"\",\n  link_details: \"\",\n  link_button: \"\",\n  excalidrawplus_description: \"\",\n  excalidrawplus_button: \"\",\n  excalidrawplus_exportError: \"\"\n};\nvar helpDialog = {\n  blog: \"\",\n  click: \"\",\n  deepSelect: \"\",\n  deepBoxSelect: \"\",\n  curvedArrow: \"\",\n  curvedLine: \"\",\n  documentation: \"\",\n  doubleClick: \"\",\n  drag: \"\",\n  editor: \"\",\n  editLineArrowPoints: \"\",\n  editText: \"\",\n  github: \"\",\n  howto: \"\",\n  or: \"\",\n  preventBinding: \"\",\n  tools: \"\",\n  shortcuts: \"\",\n  textFinish: \"\",\n  textNewLine: \"\",\n  title: \"\",\n  view: \"\",\n  zoomToFit: \"\",\n  zoomToSelection: \"\",\n  toggleElementLock: \"\",\n  movePageUpDown: \"\",\n  movePageLeftRight: \"\"\n};\nvar clearCanvasDialog = {\n  title: \"\"\n};\nvar publishDialog = {\n  title: \"\",\n  itemName: \"\",\n  authorName: \"\",\n  githubUsername: \"\",\n  twitterUsername: \"\",\n  libraryName: \"\",\n  libraryDesc: \"\",\n  website: \"\",\n  placeholder: {\n    authorName: \"\",\n    libraryName: \"\",\n    libraryDesc: \"\",\n    githubHandle: \"\",\n    twitterHandle: \"\",\n    website: \"\"\n  },\n  errors: {\n    required: \"\",\n    website: \"\"\n  },\n  noteDescription: \"\",\n  noteGuidelines: \"\",\n  noteLicense: \"\",\n  noteItems: \"\",\n  atleastOneLibItem: \"\",\n  republishWarning: \"\"\n};\nvar publishSuccessDialog = {\n  title: \"\",\n  content: \"\"\n};\nvar confirmDialog = {\n  resetLibrary: \"\",\n  removeItemsFromLib: \"\"\n};\nvar imageExportDialog = {\n  header: \"\",\n  label: {\n    withBackground: \"\",\n    onlySelected: \"\",\n    darkMode: \"\",\n    embedScene: \"\",\n    scale: \"\",\n    padding: \"\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  },\n  button: {\n    exportToPng: \"\",\n    exportToSvg: \"\",\n    copyPngToClipboard: \"\"\n  }\n};\nvar encrypted = {\n  tooltip: \"\",\n  link: \"\"\n};\nvar stats = {\n  angle: \"\",\n  element: \"\",\n  elements: \"\",\n  height: \"\",\n  scene: \"\",\n  selected: \"\",\n  storage: \"\",\n  title: \"\",\n  total: \"\",\n  version: \"\",\n  versionCopy: \"\",\n  versionNotAvailable: \"\",\n  width: \"\"\n};\nvar toast = {\n  addedToLibrary: \"\",\n  copyStyles: \"\",\n  copyToClipboard: \"\",\n  copyToClipboardAsPng: \"\",\n  fileSaved: \"\",\n  fileSavedToFilename: \"\",\n  canvas: \"\\u756B\\u5E03\",\n  selection: \"\",\n  pasteAsSingleElement: \"\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"\",\n  black: \"\",\n  white: \"\",\n  red: \"\",\n  pink: \"\",\n  grape: \"\",\n  violet: \"\",\n  gray: \"\",\n  blue: \"\",\n  cyan: \"\",\n  teal: \"\",\n  green: \"\",\n  yellow: \"\",\n  orange: \"\",\n  bronze: \"\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"\",\n    center_heading_plus: \"\",\n    menuHint: \"\"\n  },\n  defaults: {\n    menuHint: \"\",\n    center_heading: \"\",\n    toolbarHint: \"\",\n    helpHint: \"\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"\",\n  colors: \"\",\n  shades: \"\",\n  hexCode: \"\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"\",\n      button: \"\",\n      description: \"\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"\",\n  button: \"\",\n  description: \"\",\n  syntax: \"\",\n  preview: \"\"\n};\nvar zh_HK_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=zh-HK-RBTGIU3U.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/zh-HK-RBTGIU3U.js\n"));

/***/ })

}]);