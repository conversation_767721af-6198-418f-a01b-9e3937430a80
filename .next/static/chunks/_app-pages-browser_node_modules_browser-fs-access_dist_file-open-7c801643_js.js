"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_browser-fs-access_dist_file-open-7c801643_js"],{

/***/ "(app-pages-browser)/./node_modules/browser-fs-access/dist/file-open-7c801643.js":
/*!*******************************************************************!*\
  !*** ./node_modules/browser-fs-access/dist/file-open-7c801643.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ e)\n/* harmony export */ });\nvar e=async(e=[{}])=>(Array.isArray(e)||(e=[e]),new Promise((t,n)=>{const a=document.createElement(\"input\");a.type=\"file\";const i=[...e.map(e=>e.mimeTypes||[]),...e.map(e=>e.extensions||[])].join();a.multiple=e[0].multiple||!1,a.accept=i||\"\";const c=e=>{\"function\"==typeof l&&l(),t(e)},l=e[0].legacySetup&&e[0].legacySetup(c,()=>l(n),a);a.addEventListener(\"change\",()=>{c(a.multiple?Array.from(a.files):a.files[0])}),a.click()}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9icm93c2VyLWZzLWFjY2Vzcy9kaXN0L2ZpbGUtb3Blbi03YzgwMTY0My5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUJBQWlCLG1EQUFtRCx3Q0FBd0MsY0FBYyw0RUFBNEUsNENBQTRDLFlBQVksK0JBQStCLG9EQUFvRCxpQ0FBaUMsNkNBQTZDLFlBQVksR0FBd0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvYnJvd3Nlci1mcy1hY2Nlc3MvZGlzdC9maWxlLW9wZW4tN2M4MDE2NDMuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGU9YXN5bmMoZT1be31dKT0+KEFycmF5LmlzQXJyYXkoZSl8fChlPVtlXSksbmV3IFByb21pc2UoKHQsbik9Pntjb25zdCBhPWRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoXCJpbnB1dFwiKTthLnR5cGU9XCJmaWxlXCI7Y29uc3QgaT1bLi4uZS5tYXAoZT0+ZS5taW1lVHlwZXN8fFtdKSwuLi5lLm1hcChlPT5lLmV4dGVuc2lvbnN8fFtdKV0uam9pbigpO2EubXVsdGlwbGU9ZVswXS5tdWx0aXBsZXx8ITEsYS5hY2NlcHQ9aXx8XCJcIjtjb25zdCBjPWU9PntcImZ1bmN0aW9uXCI9PXR5cGVvZiBsJiZsKCksdChlKX0sbD1lWzBdLmxlZ2FjeVNldHVwJiZlWzBdLmxlZ2FjeVNldHVwKGMsKCk9PmwobiksYSk7YS5hZGRFdmVudExpc3RlbmVyKFwiY2hhbmdlXCIsKCk9PntjKGEubXVsdGlwbGU/QXJyYXkuZnJvbShhLmZpbGVzKTphLmZpbGVzWzBdKX0pLGEuY2xpY2soKX0pKTtleHBvcnR7ZSBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/browser-fs-access/dist/file-open-7c801643.js\n"));

/***/ })

}]);