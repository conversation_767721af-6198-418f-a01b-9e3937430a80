/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_canvas-roundrect-polyfill_roundRect_js"],{

/***/ "(app-pages-browser)/./node_modules/canvas-roundrect-polyfill/roundRect.js":
/*!*************************************************************!*\
  !*** ./node_modules/canvas-roundrect-polyfill/roundRect.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/*\n *  Implements the .roundRect() method of the CanvasPath mixin\n *  as introduced by https://github.com/whatwg/html/pull/6765\n */\n(() => {\n\n  \"use strict\";\n\n  Path2D.prototype.roundRect ??= roundRect;\n  if (globalThis.CanvasRenderingContext2D) {\n\n    globalThis.CanvasRenderingContext2D.prototype.roundRect ??= roundRect;\n\n  }\n  if (globalThis.OffscreenCanvasRenderingContext2D) {\n\n    globalThis.OffscreenCanvasRenderingContext2D.prototype.roundRect ??= roundRect;\n\n  }\n\n  function roundRect(x, y, w, h, radii) {\n\n    if (!([x, y, w, h].every((input) => Number.isFinite(input)))) {\n\n      return;\n\n    }\n\n    radii = parseRadiiArgument(radii);\n\n    let upperLeft, upperRight, lowerRight, lowerLeft;\n\n    if (radii.length === 4) {\n\n      upperLeft = toCornerPoint(radii[0]);\n      upperRight = toCornerPoint(radii[1]);\n      lowerRight = toCornerPoint(radii[2]);\n      lowerLeft = toCornerPoint(radii[3]);\n\n    } else if (radii.length === 3) {\n\n      upperLeft = toCornerPoint(radii[0]);\n      upperRight = toCornerPoint(radii[1]);\n      lowerLeft = toCornerPoint(radii[1]);\n      lowerRight = toCornerPoint(radii[2]);\n\n    } else if (radii.length === 2) {\n\n      upperLeft = toCornerPoint(radii[0]);\n      lowerRight = toCornerPoint(radii[0]);\n      upperRight = toCornerPoint(radii[1]);\n      lowerLeft = toCornerPoint(radii[1]);\n\n    } else if (radii.length === 1) {\n\n      upperLeft = toCornerPoint(radii[0]);\n      upperRight = toCornerPoint(radii[0]);\n      lowerRight = toCornerPoint(radii[0]);\n      lowerLeft = toCornerPoint(radii[0]);\n\n    } else {\n\n      throw new RangeError(`${ getErrorMessageHeader(this) } ${ radii.length } is not a valid size for radii sequence.`);\n\n    }\n\n    const corners = [upperLeft, upperRight, lowerRight, lowerLeft];\n    const negativeCorner = corners.find(({x, y}) => x < 0 || y < 0);\n    const negativeValue = negativeCorner?.x < 0 ? negativeCorner.x : negativeCorner?.y\n\n    if (corners.some(({x, y}) => !Number.isFinite(x) || !Number.isFinite(y))) {\n\n      return;\n\n    }\n\n    if (negativeCorner) {\n\n      throw new RangeError(`${ getErrorMessageHeader(this) } Radius value ${ negativeCorner } is negative.`);\n\n    }\n\n    fixOverlappingCorners(corners);\n\n    if (w < 0 && h < 0) {\n\n      this.moveTo(x - upperLeft.x, y);\n      this.ellipse(x + w + upperRight.x, y - upperRight.y, upperRight.x, upperRight.y, 0, -Math.PI * 1.5, -Math.PI);\n      this.ellipse(x + w + lowerRight.x, y + h + lowerRight.y, lowerRight.x, lowerRight.y, 0, -Math.PI, -Math.PI / 2);\n      this.ellipse(x - lowerLeft.x, y + h + lowerLeft.y, lowerLeft.x, lowerLeft.y, 0, -Math.PI / 2, 0);\n      this.ellipse(x - upperLeft.x, y - upperLeft.y, upperLeft.x, upperLeft.y, 0, 0, -Math.PI / 2);\n\n    } else if (w < 0) {\n\n      this.moveTo(x - upperLeft.x, y);\n      this.ellipse(x + w + upperRight.x, y + upperRight.y, upperRight.x, upperRight.y, 0, -Math.PI / 2, -Math.PI, 1);\n      this.ellipse(x + w + lowerRight.x, y + h - lowerRight.y, lowerRight.x, lowerRight.y, 0, -Math.PI, -Math.PI * 1.5, 1);\n      this.ellipse(x - lowerLeft.x, y + h - lowerLeft.y, lowerLeft.x, lowerLeft.y, 0, Math.PI / 2, 0, 1);\n      this.ellipse(x - upperLeft.x, y + upperLeft.y, upperLeft.x, upperLeft.y, 0, 0, -Math.PI / 2, 1);\n\n    } else if (h < 0) {\n\n      this.moveTo(x + upperLeft.x, y);\n      this.ellipse(x + w - upperRight.x, y - upperRight.y, upperRight.x, upperRight.y, 0, Math.PI / 2, 0, 1);\n      this.ellipse(x + w - lowerRight.x, y + h + lowerRight.y, lowerRight.x, lowerRight.y, 0, 0, -Math.PI / 2, 1);\n      this.ellipse(x + lowerLeft.x, y + h + lowerLeft.y, lowerLeft.x, lowerLeft.y, 0, -Math.PI / 2, -Math.PI, 1);\n      this.ellipse(x + upperLeft.x, y - upperLeft.y, upperLeft.x, upperLeft.y, 0, -Math.PI, -Math.PI * 1.5, 1);\n\n    } else {\n\n      this.moveTo(x + upperLeft.x, y);\n      this.ellipse(x + w - upperRight.x, y + upperRight.y, upperRight.x, upperRight.y, 0, -Math.PI / 2, 0);\n      this.ellipse(x + w - lowerRight.x, y + h - lowerRight.y, lowerRight.x, lowerRight.y, 0, 0, Math.PI / 2);\n      this.ellipse(x + lowerLeft.x, y + h - lowerLeft.y, lowerLeft.x, lowerLeft.y, 0, Math.PI / 2, Math.PI);\n      this.ellipse(x + upperLeft.x, y + upperLeft.y, upperLeft.x, upperLeft.y, 0, Math.PI, Math.PI * 1.5);\n\n    }\n\n    this.closePath();\n    this.moveTo(x, y);\n\n    function toDOMPointInit(value) {\n\n      const {x, y, z, w} = value;\n      return {x, y, z, w};\n\n    }\n\n    function parseRadiiArgument(value) {\n\n      // https://webidl.spec.whatwg.org/#es-union\n      // with 'optional (unrestricted double or DOMPointInit\n      //   or sequence<(unrestricted double or DOMPointInit)>) radii = 0'\n      const type = typeof value;\n\n      if (type === \"undefined\" || value === null) {\n\n        return [0];\n\n      }\n      if (type === \"function\") {\n\n        return [NaN];\n\n      }\n      if (type === \"object\") {\n\n        if (typeof value[Symbol.iterator] === \"function\") {\n\n          return [...value].map((elem) => {\n            // https://webidl.spec.whatwg.org/#es-union\n            // with '(unrestricted double or DOMPointInit)'\n            const elemType = typeof elem;\n            if (elemType === \"undefined\" || elem === null) {\n              return 0;\n            }\n            if (elemType === \"function\") {\n              return NaN;\n            }\n            if (elemType === \"object\") {\n              return toDOMPointInit(elem);\n            }\n            return toUnrestrictedNumber(elem);\n          });\n\n        }\n\n        return [toDOMPointInit(value)];\n\n      }\n\n      return [toUnrestrictedNumber(value)];\n\n    }\n\n    function toUnrestrictedNumber(value) {\n\n      return +value;\n\n    }\n\n    function toCornerPoint(value) {\n\n      const asNumber = toUnrestrictedNumber(value);\n      if (Number.isFinite(asNumber)) {\n\n        return {\n          x: asNumber,\n          y: asNumber\n        };\n\n      }\n      if (Object(value) === value) {\n\n        return {\n          x: toUnrestrictedNumber(value.x ?? 0),\n          y: toUnrestrictedNumber(value.y ?? 0)\n        };\n\n      }\n\n      return {\n        x: NaN,\n        y: NaN\n      };\n\n    }\n\n    function fixOverlappingCorners(corners) {\n\n      const [upperLeft, upperRight, lowerRight, lowerLeft] = corners;\n      const factors = [\n        Math.abs(w) / (upperLeft.x + upperRight.x),\n        Math.abs(h) / (upperRight.y + lowerRight.y),\n        Math.abs(w) / (lowerRight.x + lowerLeft.x),\n        Math.abs(h) / (upperLeft.y + lowerLeft.y)\n      ];\n      const minFactor = Math.min(...factors);\n      if (minFactor <= 1) {\n\n        for (const radii of corners) {\n\n          radii.x *= minFactor;\n          radii.y *= minFactor;\n\n        }\n\n      }\n\n    }\n\n  }\n\n  function getErrorMessageHeader(instance) {\n\n    return `Failed to execute 'roundRect' on '${ getConstructorName(instance) }':`;\n\n  }\n\n  function getConstructorName(instance) {\n\n    return Object(instance) === instance &&\n      instance instanceof Path2D ? \"Path2D\" :\n      instance instanceof globalThis?.CanvasRenderingContext2D ? \"CanvasRenderingContext2D\" :\n      instance instanceof globalThis?.OffscreenCanvasRenderingContext2D ? \"OffscreenCanvasRenderingContext2D\" :\n      instance?.constructor.name ||\n      instance;\n\n  }\n\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/canvas-roundrect-polyfill/roundRect.js\n"));

/***/ })

}]);