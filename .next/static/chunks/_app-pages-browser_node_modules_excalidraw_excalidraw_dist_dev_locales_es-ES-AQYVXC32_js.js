"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_excalidraw_excalidraw_dist_dev_locales_es-ES-AQYVXC32_js"],{

/***/ "(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/es-ES-AQYVXC32.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@excalidraw/excalidraw/dist/dev/locales/es-ES-AQYVXC32.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alerts: () => (/* binding */ alerts),\n/* harmony export */   buttons: () => (/* binding */ buttons),\n/* harmony export */   canvasError: () => (/* binding */ canvasError),\n/* harmony export */   clearCanvasDialog: () => (/* binding */ clearCanvasDialog),\n/* harmony export */   colorPicker: () => (/* binding */ colorPicker),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   confirmDialog: () => (/* binding */ confirmDialog),\n/* harmony export */   \"default\": () => (/* binding */ es_ES_default),\n/* harmony export */   encrypted: () => (/* binding */ encrypted),\n/* harmony export */   errorDialog: () => (/* binding */ errorDialog),\n/* harmony export */   errorSplash: () => (/* binding */ errorSplash),\n/* harmony export */   errors: () => (/* binding */ errors),\n/* harmony export */   exportDialog: () => (/* binding */ exportDialog),\n/* harmony export */   headings: () => (/* binding */ headings),\n/* harmony export */   helpDialog: () => (/* binding */ helpDialog),\n/* harmony export */   hints: () => (/* binding */ hints),\n/* harmony export */   imageExportDialog: () => (/* binding */ imageExportDialog),\n/* harmony export */   labels: () => (/* binding */ labels),\n/* harmony export */   library: () => (/* binding */ library),\n/* harmony export */   mermaid: () => (/* binding */ mermaid),\n/* harmony export */   overwriteConfirm: () => (/* binding */ overwriteConfirm),\n/* harmony export */   publishDialog: () => (/* binding */ publishDialog),\n/* harmony export */   publishSuccessDialog: () => (/* binding */ publishSuccessDialog),\n/* harmony export */   roomDialog: () => (/* binding */ roomDialog),\n/* harmony export */   stats: () => (/* binding */ stats),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   toolBar: () => (/* binding */ toolBar),\n/* harmony export */   welcomeScreen: () => (/* binding */ welcomeScreen)\n/* harmony export */ });\n/* harmony import */ var _chunk_XDFCUUT6_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../chunk-XDFCUUT6.js */ \"(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/chunk-XDFCUUT6.js\");\n\n\n// locales/es-ES.json\nvar labels = {\n  paste: \"Pegar\",\n  pasteAsPlaintext: \"Pegar como texto sin formato\",\n  pasteCharts: \"Pegar gr\\xE1ficos\",\n  selectAll: \"Seleccionar todo\",\n  multiSelect: \"A\\xF1adir elemento a la selecci\\xF3n\",\n  moveCanvas: \"Mover el lienzo\",\n  cut: \"Cortar\",\n  copy: \"Copiar\",\n  copyAsPng: \"Copiar al portapapeles como PNG\",\n  copyAsSvg: \"Copiar al portapapeles como SVG\",\n  copyText: \"Copiar al portapapeles como texto\",\n  copySource: \"Copiar fuente al portapapeles\",\n  convertToCode: \"Convertir a c\\xF3digo\",\n  bringForward: \"Traer hacia delante\",\n  sendToBack: \"Enviar al fondo\",\n  bringToFront: \"Traer al frente\",\n  sendBackward: \"Enviar atr\\xE1s\",\n  delete: \"Borrar\",\n  copyStyles: \"Copiar estilos\",\n  pasteStyles: \"Pegar estilos\",\n  stroke: \"Trazo\",\n  background: \"Fondo\",\n  fill: \"Rellenar\",\n  strokeWidth: \"Grosor del trazo\",\n  strokeStyle: \"Estilo del trazo\",\n  strokeStyle_solid: \"S\\xF3lido\",\n  strokeStyle_dashed: \"Discontinua\",\n  strokeStyle_dotted: \"Punteado\",\n  sloppiness: \"Estilo de trazo\",\n  opacity: \"Opacidad\",\n  textAlign: \"Alineado de texto\",\n  edges: \"Bordes\",\n  sharp: \"Afilado\",\n  round: \"Redondo\",\n  arrowheads: \"Puntas de flecha\",\n  arrowhead_none: \"Ninguna\",\n  arrowhead_arrow: \"Flecha\",\n  arrowhead_bar: \"Barra\",\n  arrowhead_circle: \"C\\xEDrculo\",\n  arrowhead_circle_outline: \"C\\xEDrculo (contorno)\",\n  arrowhead_triangle: \"Tri\\xE1ngulo\",\n  arrowhead_triangle_outline: \"Tri\\xE1ngulo (contorno)\",\n  arrowhead_diamond: \"Diamante\",\n  arrowhead_diamond_outline: \"Diamante (contorno)\",\n  fontSize: \"Tama\\xF1o de la fuente\",\n  fontFamily: \"Tipo de fuente\",\n  addWatermark: 'Agregar \"Hecho con Excalidraw\"',\n  handDrawn: \"Dibujado a mano\",\n  normal: \"Normal\",\n  code: \"C\\xF3digo\",\n  small: \"Peque\\xF1a\",\n  medium: \"Mediana\",\n  large: \"Grande\",\n  veryLarge: \"Muy grande\",\n  solid: \"S\\xF3lido\",\n  hachure: \"Folleto\",\n  zigzag: \"Zigzag\",\n  crossHatch: \"Rayado transversal\",\n  thin: \"Fino\",\n  bold: \"Grueso\",\n  left: \"Izquierda\",\n  center: \"Centrado\",\n  right: \"Derecha\",\n  extraBold: \"Extra negrita\",\n  architect: \"Arquitecto\",\n  artist: \"Artista\",\n  cartoonist: \"Caricatura\",\n  fileTitle: \"Nombre del archivo\",\n  colorPicker: \"Selector de color\",\n  canvasColors: \"Usado en lienzo\",\n  canvasBackground: \"Fondo del lienzo\",\n  drawingCanvas: \"Lienzo de dibujo\",\n  layers: \"Capas\",\n  actions: \"Acciones\",\n  language: \"Idioma\",\n  liveCollaboration: \"Colaboraci\\xF3n en directo...\",\n  duplicateSelection: \"Duplicar\",\n  untitled: \"Sin t\\xEDtulo\",\n  name: \"Nombre\",\n  yourName: \"Tu nombre\",\n  madeWithExcalidraw: \"Hecho con Excalidraw\",\n  group: \"Agrupar selecci\\xF3n\",\n  ungroup: \"Desagrupar selecci\\xF3n\",\n  collaborators: \"Colaboradores\",\n  showGrid: \"Mostrar cuadr\\xEDcula\",\n  addToLibrary: \"A\\xF1adir a la biblioteca\",\n  removeFromLibrary: \"Eliminar de la biblioteca\",\n  libraryLoadingMessage: \"Cargando biblioteca\\u2026\",\n  libraries: \"Explorar bibliotecas\",\n  loadingScene: \"Cargando escena\\u2026\",\n  align: \"Alinear\",\n  alignTop: \"Alineaci\\xF3n superior\",\n  alignBottom: \"Alineaci\\xF3n inferior\",\n  alignLeft: \"Alinear a la izquierda\",\n  alignRight: \"Alinear a la derecha\",\n  centerVertically: \"Centrar verticalmente\",\n  centerHorizontally: \"Centrar horizontalmente\",\n  distributeHorizontally: \"Distribuir horizontalmente\",\n  distributeVertically: \"Distribuir verticalmente\",\n  flipHorizontal: \"Girar horizontalmente\",\n  flipVertical: \"Girar verticalmente\",\n  viewMode: \"Modo presentaci\\xF3n\",\n  share: \"Compartir\",\n  showStroke: \"Mostrar selector de color de trazo\",\n  showBackground: \"Mostrar el selector de color de fondo\",\n  toggleTheme: \"Cambiar tema\",\n  personalLib: \"Biblioteca personal\",\n  excalidrawLib: \"Biblioteca Excalidraw\",\n  decreaseFontSize: \"Disminuir tama\\xF1o de letra\",\n  increaseFontSize: \"Aumentar el tama\\xF1o de letra\",\n  unbindText: \"Desvincular texto\",\n  bindText: \"Vincular texto al contenedor\",\n  createContainerFromText: \"Envolver el texto en un contenedor\",\n  link: {\n    edit: \"Editar enlace\",\n    editEmbed: \"Editar enlace e incrustar\",\n    create: \"Crear enlace\",\n    createEmbed: \"Crear enlace e incrustar\",\n    label: \"Enlace\",\n    labelEmbed: \"Enlazar e incrustar\",\n    empty: \"No se ha establecido un enlace\"\n  },\n  lineEditor: {\n    edit: \"Editar l\\xEDnea\",\n    exit: \"Salir del editor en l\\xEDnea\"\n  },\n  elementLock: {\n    lock: \"Bloquear\",\n    unlock: \"Desbloquear\",\n    lockAll: \"Bloquear todo\",\n    unlockAll: \"Desbloquear todo\"\n  },\n  statusPublished: \"Publicado\",\n  sidebarLock: \"Mantener barra lateral abierta\",\n  selectAllElementsInFrame: \"Seleccionar todos los elementos en el marco\",\n  removeAllElementsFromFrame: \"Eliminar todos los elementos del marco\",\n  eyeDropper: \"Seleccionar un color del lienzo\",\n  textToDiagram: \"Texto a diagrama\",\n  prompt: \"Sugerencia\"\n};\nvar library = {\n  noItems: \"No hay elementos a\\xF1adidos todav\\xEDa...\",\n  hint_emptyLibrary: \"Seleccione un elemento en el lienzo para a\\xF1adirlo aqu\\xED, o instale una biblioteca del repositorio p\\xFAblico, a continuaci\\xF3n.\",\n  hint_emptyPrivateLibrary: \"Seleccione un elemento del lienzo para a\\xF1adirlo aqu\\xED.\"\n};\nvar buttons = {\n  clearReset: \"Limpiar lienzo y reiniciar el color de fondo\",\n  exportJSON: \"Exportar a archivo\",\n  exportImage: \"Exportar imagen...\",\n  export: \"Guardar en...\",\n  copyToClipboard: \"Copiar al portapapeles\",\n  save: \"Guardar en archivo actual\",\n  saveAs: \"Guardar como\",\n  load: \"Abrir\",\n  getShareableLink: \"Obtener enlace para compartir\",\n  close: \"Cerrar\",\n  selectLanguage: \"Elegir idioma\",\n  scrollBackToContent: \"Volver al contenido\",\n  zoomIn: \"Acercarse\",\n  zoomOut: \"Alejarse\",\n  resetZoom: \"Restablecer zoom\",\n  menu: \"Men\\xFA\",\n  done: \"Hecho\",\n  edit: \"Editar\",\n  undo: \"Deshacer\",\n  redo: \"Rehacer\",\n  resetLibrary: \"Reiniciar biblioteca\",\n  createNewRoom: \"Crear nueva sala\",\n  fullScreen: \"Pantalla completa\",\n  darkMode: \"Modo oscuro\",\n  lightMode: \"Modo claro\",\n  zenMode: \"Modo Zen\",\n  objectsSnapMode: \"Ajustar a los objetos\",\n  exitZenMode: \"Salir del modo Zen\",\n  cancel: \"Cancelar\",\n  clear: \"Borrar\",\n  remove: \"Eliminar\",\n  embed: \"\",\n  publishLibrary: \"Publicar\",\n  submit: \"Enviar\",\n  confirm: \"Confirmar\",\n  embeddableInteractionButton: \"Pulsa para interactuar\"\n};\nvar alerts = {\n  clearReset: \"Esto limpiar\\xE1 todo el lienzo. Est\\xE1s seguro?\",\n  couldNotCreateShareableLink: \"No se pudo crear un enlace para compartir.\",\n  couldNotCreateShareableLinkTooBig: \"No se pudo crear el enlace para compartir: la escena es demasiado grande\",\n  couldNotLoadInvalidFile: \"No se pudo cargar el archivo no v\\xE1lido\",\n  importBackendFailed: \"La importaci\\xF3n fall\\xF3.\",\n  cannotExportEmptyCanvas: \"No se puede exportar un lienzo vaci\\xF3\",\n  couldNotCopyToClipboard: \"No se pudo copiar al portapapeles.\",\n  decryptFailed: \"No se pudieron descifrar los datos.\",\n  uploadedSecurly: \"La carga ha sido asegurada con cifrado de principio a fin, lo que significa que el servidor de Excalidraw y terceros no pueden leer el contenido.\",\n  loadSceneOverridePrompt: \"Si carga este dibujo externo, reemplazar\\xE1 el que tiene. \\xBFDesea continuar?\",\n  collabStopOverridePrompt: \"Detener la sesi\\xF3n sobrescribir\\xE1 su dibujo anterior almacenado localmente. \\xBFEst\\xE1 seguro?\\n\\n(Si desea mantener su dibujo local, simplemente cierre la pesta\\xF1a del navegador.)\",\n  errorAddingToLibrary: \"No se pudo agregar el elemento a la biblioteca\",\n  errorRemovingFromLibrary: \"No se pudo quitar el elemento de la biblioteca\",\n  confirmAddLibrary: \"Esto a\\xF1adir\\xE1 {{numShapes}} forma(s) a tu biblioteca. \\xBFEst\\xE1s seguro?\",\n  imageDoesNotContainScene: \"Esta imagen no parece contener datos de escena. \\xBFHa habilitado la inserci\\xF3n de la escena durante la exportaci\\xF3n?\",\n  cannotRestoreFromImage: \"No se pudo restaurar la escena desde este archivo de imagen\",\n  invalidSceneUrl: \"No se ha podido importar la escena desde la URL proporcionada. Est\\xE1 mal formada, o no contiene datos de Excalidraw JSON v\\xE1lidos.\",\n  resetLibrary: \"Esto borrar\\xE1 tu biblioteca. \\xBFEst\\xE1s seguro?\",\n  removeItemsFromsLibrary: \"\\xBFEliminar {{count}} elemento(s) de la biblioteca?\",\n  invalidEncryptionKey: \"La clave de cifrado debe tener 22 caracteres. La colaboraci\\xF3n en vivo est\\xE1 deshabilitada.\",\n  collabOfflineWarning: \"No hay conexi\\xF3n a internet disponible.\\n\\xA1No se guardar\\xE1n los cambios!\"\n};\nvar errors = {\n  unsupportedFileType: \"Tipo de archivo no admitido.\",\n  imageInsertError: \"No se pudo insertar la imagen. Int\\xE9ntelo de nuevo m\\xE1s tarde...\",\n  fileTooBig: \"Archivo demasiado grande. El tama\\xF1o m\\xE1ximo permitido es {{maxSize}}.\",\n  svgImageInsertError: \"No se pudo insertar la imagen SVG. El c\\xF3digo SVG parece inv\\xE1lido.\",\n  failedToFetchImage: \"Error al obtener la imagen.\",\n  invalidSVGString: \"SVG no v\\xE1lido.\",\n  cannotResolveCollabServer: \"No se pudo conectar al servidor colaborador. Por favor, vuelva a cargar la p\\xE1gina y vuelva a intentarlo.\",\n  importLibraryError: \"No se pudo cargar la librer\\xEDa\",\n  collabSaveFailed: \"No se pudo guardar en la base de datos del backend. Si los problemas persisten, deber\\xEDa guardar su archivo localmente para asegurarse de que no pierde su trabajo.\",\n  collabSaveFailed_sizeExceeded: \"No se pudo guardar en la base de datos del backend, el lienzo parece ser demasiado grande. Deber\\xEDa guardar el archivo localmente para asegurarse de que no pierde su trabajo.\",\n  imageToolNotSupported: \"\",\n  brave_measure_text_error: {\n    line1: \"Parece que est\\xE1s usando el navegador Brave con el ajuste <bold>Forzar el bloqueo de huellas digitales</bold> habilitado.\",\n    line2: \"Esto podr\\xEDa resultar en errores en los <bold>Elementos de Texto</bold> en tus dibujos.\",\n    line3: \"Recomendamos fuertemente deshabilitar esta configuraci\\xF3n. Puedes seguir <link>estos pasos</link> sobre c\\xF3mo hacerlo.\",\n    line4: \"\"\n  },\n  libraryElementTypeError: {\n    embeddable: \"\",\n    iframe: \"Los elementos IFrame no se pueden agregar a la biblioteca.\",\n    image: \"\"\n  },\n  asyncPasteFailedOnRead: \"No se pudo pegar (no se pudo leer desde el portapapeles del sistema).\",\n  asyncPasteFailedOnParse: \"No se pudo pegar.\",\n  copyToSystemClipboardFailed: \"No se pudo copiar al portapapeles.\"\n};\nvar toolBar = {\n  selection: \"Selecci\\xF3n\",\n  image: \"Insertar imagen\",\n  rectangle: \"Rect\\xE1ngulo\",\n  diamond: \"Diamante\",\n  ellipse: \"Elipse\",\n  arrow: \"Flecha\",\n  line: \"L\\xEDnea\",\n  freedraw: \"Dibujar\",\n  text: \"Texto\",\n  library: \"Biblioteca\",\n  lock: \"Mantener la herramienta seleccionada activa despu\\xE9s de dibujar\",\n  penMode: \"Modo L\\xE1piz - previene toque\",\n  link: \"A\\xF1adir/Actualizar enlace para una forma seleccionada\",\n  eraser: \"Borrar\",\n  frame: \"\",\n  magicframe: \"Esquema a c\\xF3digo\",\n  embeddable: \"Incrustar Web\",\n  laser: \"Puntero l\\xE1ser\",\n  hand: \"Mano (herramienta de panoramizaci\\xF3n)\",\n  extraTools: \"M\\xE1s herramientas\",\n  mermaidToExcalidraw: \"Mermaid a Excalidraw\",\n  magicSettings: \"Ajustes AI\"\n};\nvar headings = {\n  canvasActions: \"Acciones del lienzo\",\n  selectedShapeActions: \"Acciones de la forma seleccionada\",\n  shapes: \"Formas\"\n};\nvar hints = {\n  canvasPanning: \"Para mover el lienzo, mantenga la rueda del rat\\xF3n o la barra espaciadora mientras arrastra o utilice la herramienta de mano\",\n  linearElement: \"Haz clic para dibujar m\\xFAltiples puntos, arrastrar para solo una l\\xEDnea\",\n  freeDraw: \"Haz clic y arrastra, suelta al terminar\",\n  text: \"Consejo: tambi\\xE9n puedes a\\xF1adir texto haciendo doble clic en cualquier lugar con la herramienta de selecci\\xF3n\",\n  embeddable: \"Haga clic y arrastre para crear un sitio web incrustado\",\n  text_selected: \"Doble clic o pulse ENTER para editar el texto\",\n  text_editing: \"Pulse Escape o Ctrl/Cmd + ENTER para terminar de editar\",\n  linearElementMulti: \"Haz clic en el \\xFAltimo punto o presiona Escape o Enter para finalizar\",\n  lockAngle: \"Puedes restringir el \\xE1ngulo manteniendo presionado el bot\\xF3n SHIFT\",\n  resize: \"Para mantener las proporciones mant\\xE9n SHIFT presionado mientras modificas el tama\\xF1o, \\nmant\\xE9n presionado ALT para modificar el tama\\xF1o desde el centro\",\n  resizeImage: \"Puede redimensionar libremente pulsando SHIFT,\\npulse ALT para redimensionar desde el centro\",\n  rotate: \"Puedes restringir los \\xE1ngulos manteniendo presionado SHIFT mientras giras\",\n  lineEditor_info: \"Mantenga pulsado CtrlOrCmd y haga doble click o presione CtrlOrCmd + Enter para editar puntos\",\n  lineEditor_pointSelected: \"Presione Suprimir para eliminar el/los punto(s), CtrlOrCmd+D para duplicarlo, o arr\\xE1strelo para moverlo\",\n  lineEditor_nothingSelected: \"Seleccione un punto a editar (mantenga MAY\\xDASCULAS para seleccionar m\\xFAltiples),\\no mantenga pulsado Alt y haga click para a\\xF1adir nuevos puntos\",\n  placeImage: \"Haga clic para colocar la imagen o haga click y arrastre para establecer su tama\\xF1o manualmente\",\n  publishLibrary: \"Publica tu propia biblioteca\",\n  bindTextToElement: \"Presione Entrar para agregar\",\n  deepBoxSelect: \"Mant\\xE9n CtrlOrCmd para seleccionar en profundidad, y para evitar arrastrar\",\n  eraserRevert: \"Mantenga pulsado Alt para revertir los elementos marcados para su eliminaci\\xF3n\",\n  firefox_clipboard_write: 'Esta caracter\\xEDstica puede ser habilitada estableciendo la bandera \"dom.events.asyncClipboard.clipboardItem\" a \"true\". Para cambiar las banderas del navegador en Firefox, visite la p\\xE1gina \"about:config\".',\n  disableSnapping: \"Mant\\xE9n pulsado CtrlOrCmd para desactivar el ajuste\"\n};\nvar canvasError = {\n  cannotShowPreview: \"No se puede mostrar la vista previa\",\n  canvasTooBig: \"El lienzo podr\\xEDa ser demasiado grande.\",\n  canvasTooBigTip: \"Sugerencia: intenta acercar un poco m\\xE1s los elementos m\\xE1s lejanos.\"\n};\nvar errorSplash = {\n  headingMain: \"Se encontr\\xF3 un error. Intente <button>recargando la p\\xE1gina.</button>\",\n  clearCanvasMessage: \"Si la recarga no funciona, intente <button>limpiando el lienzo.</button>\",\n  clearCanvasCaveat: \" Esto provocar\\xE1 la p\\xE9rdida de su trabajo \",\n  trackedToSentry: \"El error con el identificador {{eventId}} fue rastreado en nuestro sistema.\",\n  openIssueMessage: \"Fuimos muy cautelosos de no incluir la informaci\\xF3n de tu escena en el error. Si tu escena no es privada, por favor considera seguir nuestro <button>rastreador de errores.</button> Por favor, incluya la siguiente informaci\\xF3n copi\\xE1ndola y peg\\xE1ndola en el issue de GitHub.\",\n  sceneContent: \"Contenido de la escena:\"\n};\nvar roomDialog = {\n  desc_intro: \"Puede invitar a otras personas a tu actual escena para que colaboren contigo.\",\n  desc_privacy: \"No te preocupes, la sesi\\xF3n usa encriptaci\\xF3n de punta a punta, por lo que todo lo que se dibuje se mantendr\\xE1 privadamente. Ni siquiera nuestro servidor podr\\xE1 ver lo que haces.\",\n  button_startSession: \"Iniciar sesi\\xF3n\",\n  button_stopSession: \"Detener sesi\\xF3n\",\n  desc_inProgressIntro: \"La sesi\\xF3n de colaboraci\\xF3n en vivo est\\xE1 ahora en progreso.\",\n  desc_shareLink: \"Comparte este enlace con cualquier persona con quien quieras colaborar:\",\n  desc_exitSession: \"Detener la sesi\\xF3n te desconectar\\xE1 de la sala, pero podr\\xE1s seguir trabajando con la escena en su computadora, esto es de modo local. Ten en cuenta que esto no afectar\\xE1 a otras personas, y que las mismas seguir\\xE1n siendo capaces de colaborar en tu escena.\",\n  shareTitle: \"\\xDAnase a una sesi\\xF3n colaborativa en vivo en Excalidraw\"\n};\nvar errorDialog = {\n  title: \"Error\"\n};\nvar exportDialog = {\n  disk_title: \"Guardar en disco\",\n  disk_details: \"Exportar los datos de la escena a un archivo desde el cual pueda importar m\\xE1s tarde.\",\n  disk_button: \"Guardar en archivo\",\n  link_title: \"Enlace para compartir\",\n  link_details: \"Exportar como enlace de s\\xF3lo lectura.\",\n  link_button: \"Exportar a Link\",\n  excalidrawplus_description: \"Guarde la escena en su espacio de trabajo de Excalidraw+.\",\n  excalidrawplus_button: \"Exportar\",\n  excalidrawplus_exportError: \"No se pudo exportar a Excalidraw+ en este momento...\"\n};\nvar helpDialog = {\n  blog: \"Lea nuestro blog\",\n  click: \"click\",\n  deepSelect: \"Selecci\\xF3n profunda\",\n  deepBoxSelect: \"Seleccione en profundidad dentro de la caja, y evite arrastrar\",\n  curvedArrow: \"Flecha curva\",\n  curvedLine: \"L\\xEDnea curva\",\n  documentation: \"Documentaci\\xF3n\",\n  doubleClick: \"doble clic\",\n  drag: \"arrastrar\",\n  editor: \"Editor\",\n  editLineArrowPoints: \"Editar puntos de l\\xEDnea/flecha\",\n  editText: \"Editar texto / a\\xF1adir etiqueta\",\n  github: \"\\xBFHa encontrado un problema? Env\\xEDelo\",\n  howto: \"Siga nuestras gu\\xEDas\",\n  or: \"o\",\n  preventBinding: \"Evitar enlace de flechas\",\n  tools: \"Herramientas\",\n  shortcuts: \"Atajos del teclado\",\n  textFinish: \"Finalizar edici\\xF3n (editor de texto)\",\n  textNewLine: \"A\\xF1adir nueva linea (editor de texto)\",\n  title: \"Ayuda\",\n  view: \"Vista\",\n  zoomToFit: \"Ajustar la vista para mostrar todos los elementos\",\n  zoomToSelection: \"Ampliar selecci\\xF3n\",\n  toggleElementLock: \"Bloquear/desbloquear selecci\\xF3n\",\n  movePageUpDown: \"Mover p\\xE1gina hacia arriba/abajo\",\n  movePageLeftRight: \"Mover p\\xE1gina hacia la izquierda/derecha\"\n};\nvar clearCanvasDialog = {\n  title: \"Borrar lienzo\"\n};\nvar publishDialog = {\n  title: \"Publicar biblioteca\",\n  itemName: \"Nombre del art\\xEDculo\",\n  authorName: \"Nombre del autor\",\n  githubUsername: \"Nombre de usuario de GitHub\",\n  twitterUsername: \"Nombre de usuario de Twitter\",\n  libraryName: \"Nombre de la biblioteca\",\n  libraryDesc: \"Descripci\\xF3n de la biblioteca\",\n  website: \"Sitio Web\",\n  placeholder: {\n    authorName: \"Nombre o nombre de usuario\",\n    libraryName: \"Nombre de tu biblioteca\",\n    libraryDesc: \"Descripci\\xF3n de su biblioteca para ayudar a la gente a entender su uso\",\n    githubHandle: \"Nombre de usuario de GitHub (opcional), as\\xED podr\\xE1 editar la biblioteca una vez enviada para su revisi\\xF3n\",\n    twitterHandle: \"Nombre de usuario de Twitter (opcional), as\\xED sabemos a qui\\xE9n acreditar cuando se promociona en Twitter\",\n    website: \"Enlace a su sitio web personal o en cualquier otro lugar (opcional)\"\n  },\n  errors: {\n    required: \"Requerido\",\n    website: \"Introduce una URL v\\xE1lida\"\n  },\n  noteDescription: \"Env\\xEDa tu biblioteca para ser incluida en el <link>repositorio de librer\\xEDa p\\xFAblica</link>para que otras personas utilicen en sus dibujos.\",\n  noteGuidelines: \"La biblioteca debe ser aprobada manualmente primero. Por favor, lea la <link>pautas</link> antes de enviar. Necesitar\\xE1 una cuenta de GitHub para comunicarse y hacer cambios si se solicita, pero no es estrictamente necesario.\",\n  noteLicense: \"Al enviar, usted acepta que la biblioteca se publicar\\xE1 bajo el <link>Licencia MIT </link>que en breve significa que cualquiera puede utilizarlos sin restricciones.\",\n  noteItems: \"Cada elemento de la biblioteca debe tener su propio nombre para que sea filtrable. Los siguientes elementos de la biblioteca ser\\xE1n incluidos:\",\n  atleastOneLibItem: \"Por favor, seleccione al menos un elemento de la biblioteca para empezar\",\n  republishWarning: \"Nota: algunos de los elementos seleccionados est\\xE1n marcados como ya publicados/enviados. S\\xF3lo deber\\xEDa volver a enviar elementos cuando se actualice una biblioteca o env\\xEDo.\"\n};\nvar publishSuccessDialog = {\n  title: \"Biblioteca enviada\",\n  content: \"Gracias {{authorName}}. Su biblioteca ha sido enviada para ser revisada. Puede seguir el estado<link>aqu\\xED</link>\"\n};\nvar confirmDialog = {\n  resetLibrary: \"Reiniciar biblioteca\",\n  removeItemsFromLib: \"Eliminar elementos seleccionados de la biblioteca\"\n};\nvar imageExportDialog = {\n  header: \"Exportar imagen\",\n  label: {\n    withBackground: \"Fondo\",\n    onlySelected: \"S\\xF3lo seleccionados\",\n    darkMode: \"Modo oscuro\",\n    embedScene: \"Incrustar escena\",\n    scale: \"Escalar\",\n    padding: \"Espaciado\"\n  },\n  tooltip: {\n    embedScene: \"\"\n  },\n  title: {\n    exportToPng: \"Exportar a PNG\",\n    exportToSvg: \"Exportar a SVG\",\n    copyPngToClipboard: \"Copiar PNG al portapapeles\"\n  },\n  button: {\n    exportToPng: \"PNG\",\n    exportToSvg: \"SVG\",\n    copyPngToClipboard: \"Copiar al portapapeles\"\n  }\n};\nvar encrypted = {\n  tooltip: \"Tus dibujos est\\xE1n cifrados de punto a punto, por lo que los servidores de Excalidraw nunca los ver\\xE1n.\",\n  link: \"Entrada en el blog sobre cifrado de extremo a extremo\"\n};\nvar stats = {\n  angle: \"\\xC1ngulo\",\n  element: \"Elemento\",\n  elements: \"Elementos\",\n  height: \"Alto\",\n  scene: \"Escena\",\n  selected: \"Seleccionado\",\n  storage: \"Almacenamiento\",\n  title: \"Estad\\xEDsticas para nerds\",\n  total: \"Total\",\n  version: \"Versi\\xF3n\",\n  versionCopy: \"Click para copiar\",\n  versionNotAvailable: \"Versi\\xF3n no disponible\",\n  width: \"Ancho\"\n};\nvar toast = {\n  addedToLibrary: \"A\\xF1adido a la biblioteca\",\n  copyStyles: \"Estilos copiados.\",\n  copyToClipboard: \"Copiado en el portapapeles.\",\n  copyToClipboardAsPng: \"Copiado {{exportSelection}} al portapapeles como PNG\\n({{exportColorScheme}})\",\n  fileSaved: \"Archivo guardado.\",\n  fileSavedToFilename: \"Guardado en {filename}\",\n  canvas: \"lienzo\",\n  selection: \"selecci\\xF3n\",\n  pasteAsSingleElement: \"Usa {{shortcut}} para pegar como un solo elemento,\\no pegar en un editor de texto existente\",\n  unableToEmbed: \"\",\n  unrecognizedLinkFormat: \"\"\n};\nvar colors = {\n  transparent: \"Transparente\",\n  black: \"Negro\",\n  white: \"Blanco\",\n  red: \"Rojo\",\n  pink: \"Rosa\",\n  grape: \"Uva\",\n  violet: \"Violeta\",\n  gray: \"Gris\",\n  blue: \"Azul\",\n  cyan: \"Cian\",\n  teal: \"Turquesa\",\n  green: \"Verde\",\n  yellow: \"Amarillo\",\n  orange: \"Naranja\",\n  bronze: \"Bronce\"\n};\nvar welcomeScreen = {\n  app: {\n    center_heading: \"Toda su informaci\\xF3n es guardada localmente en su navegador.\",\n    center_heading_plus: \"\\xBFQuieres ir a Excalidraw+?\",\n    menuHint: \"Exportar, preferencias, idiomas, ...\"\n  },\n  defaults: {\n    menuHint: \"Exportar, preferencias y m\\xE1s...\",\n    center_heading: \"Diagramas. Hecho. Simplemente.\",\n    toolbarHint: \"\\xA1Elige una herramienta y empieza a dibujar!\",\n    helpHint: \"Atajos y ayuda\"\n  }\n};\nvar colorPicker = {\n  mostUsedCustomColors: \"Colores personalizados m\\xE1s utilizados\",\n  colors: \"Colores\",\n  shades: \"\",\n  hexCode: \"C\\xF3digo Hexadecimal\",\n  noShades: \"\"\n};\nvar overwriteConfirm = {\n  action: {\n    exportToImage: {\n      title: \"Exportar como imagen\",\n      button: \"Exportar como imagen\",\n      description: \"\"\n    },\n    saveToDisk: {\n      title: \"Guardar en el disco\",\n      button: \"Guardar en el disco\",\n      description: \"Exporta los datos de la escena a un archivo desde el cual podr\\xE1s importar m\\xE1s tarde.\"\n    },\n    excalidrawPlus: {\n      title: \"\",\n      button: \"Exportar a Excalidraw+\",\n      description: \"\"\n    }\n  },\n  modal: {\n    loadFromFile: {\n      title: \"Cargar desde un archivo\",\n      button: \"Cargar desde un archivo\",\n      description: \"\"\n    },\n    shareableLink: {\n      title: \"Cargar desde un enlace\",\n      button: \"Reemplazar mi contenido\",\n      description: \"Cargar un dibujo externo <bold>reemplazar\\xE1 tu contenido existente</bold>.<br></br>Puedes primero hacer una copia de seguridad de tu dibujo usando una de las opciones de abajo.\"\n    }\n  }\n};\nvar mermaid = {\n  title: \"Mermaid a Excalidraw\",\n  button: \"Insertar\",\n  description: \"Actualmente s\\xF3lo <flowchartLink>Flowchart</flowchartLink>,<sequenceLink> Secuencia, </sequenceLink> y <classLink>Class </classLink>Diagramas son soportados. Los otros tipos se renderizar\\xE1n como imagen en Excalidraw.\",\n  syntax: \"Sintaxis Mermaid\",\n  preview: \"Vista previa\"\n};\nvar es_ES_default = {\n  labels,\n  library,\n  buttons,\n  alerts,\n  errors,\n  toolBar,\n  headings,\n  hints,\n  canvasError,\n  errorSplash,\n  roomDialog,\n  errorDialog,\n  exportDialog,\n  helpDialog,\n  clearCanvasDialog,\n  publishDialog,\n  publishSuccessDialog,\n  confirmDialog,\n  imageExportDialog,\n  encrypted,\n  stats,\n  toast,\n  colors,\n  welcomeScreen,\n  colorPicker,\n  overwriteConfirm,\n  mermaid\n};\n\n//# sourceMappingURL=es-ES-AQYVXC32.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZXhjYWxpZHJhdy9leGNhbGlkcmF3L2Rpc3QvZGV2L2xvY2FsZXMvZXMtRVMtQVFZVlhDMzIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7O0FBRTlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLFlBQVk7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsUUFBUTtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRUFBK0UsU0FBUztBQUN4RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0QsVUFBVTtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixZQUFZO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGtCQUFrQiw2QkFBNkIsbUJBQW1CO0FBQ3JHO0FBQ0EscUNBQXFDLFNBQVM7QUFDOUM7QUFDQTtBQUNBLCtCQUErQixXQUFXO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBOEJFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9ub2RlX21vZHVsZXMvQGV4Y2FsaWRyYXcvZXhjYWxpZHJhdy9kaXN0L2Rldi9sb2NhbGVzL2VzLUVTLUFRWVZYQzMyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBcIi4uL2NodW5rLVhERkNVVVQ2LmpzXCI7XG5cbi8vIGxvY2FsZXMvZXMtRVMuanNvblxudmFyIGxhYmVscyA9IHtcbiAgcGFzdGU6IFwiUGVnYXJcIixcbiAgcGFzdGVBc1BsYWludGV4dDogXCJQZWdhciBjb21vIHRleHRvIHNpbiBmb3JtYXRvXCIsXG4gIHBhc3RlQ2hhcnRzOiBcIlBlZ2FyIGdyXFx4RTFmaWNvc1wiLFxuICBzZWxlY3RBbGw6IFwiU2VsZWNjaW9uYXIgdG9kb1wiLFxuICBtdWx0aVNlbGVjdDogXCJBXFx4RjFhZGlyIGVsZW1lbnRvIGEgbGEgc2VsZWNjaVxceEYzblwiLFxuICBtb3ZlQ2FudmFzOiBcIk1vdmVyIGVsIGxpZW56b1wiLFxuICBjdXQ6IFwiQ29ydGFyXCIsXG4gIGNvcHk6IFwiQ29waWFyXCIsXG4gIGNvcHlBc1BuZzogXCJDb3BpYXIgYWwgcG9ydGFwYXBlbGVzIGNvbW8gUE5HXCIsXG4gIGNvcHlBc1N2ZzogXCJDb3BpYXIgYWwgcG9ydGFwYXBlbGVzIGNvbW8gU1ZHXCIsXG4gIGNvcHlUZXh0OiBcIkNvcGlhciBhbCBwb3J0YXBhcGVsZXMgY29tbyB0ZXh0b1wiLFxuICBjb3B5U291cmNlOiBcIkNvcGlhciBmdWVudGUgYWwgcG9ydGFwYXBlbGVzXCIsXG4gIGNvbnZlcnRUb0NvZGU6IFwiQ29udmVydGlyIGEgY1xceEYzZGlnb1wiLFxuICBicmluZ0ZvcndhcmQ6IFwiVHJhZXIgaGFjaWEgZGVsYW50ZVwiLFxuICBzZW5kVG9CYWNrOiBcIkVudmlhciBhbCBmb25kb1wiLFxuICBicmluZ1RvRnJvbnQ6IFwiVHJhZXIgYWwgZnJlbnRlXCIsXG4gIHNlbmRCYWNrd2FyZDogXCJFbnZpYXIgYXRyXFx4RTFzXCIsXG4gIGRlbGV0ZTogXCJCb3JyYXJcIixcbiAgY29weVN0eWxlczogXCJDb3BpYXIgZXN0aWxvc1wiLFxuICBwYXN0ZVN0eWxlczogXCJQZWdhciBlc3RpbG9zXCIsXG4gIHN0cm9rZTogXCJUcmF6b1wiLFxuICBiYWNrZ3JvdW5kOiBcIkZvbmRvXCIsXG4gIGZpbGw6IFwiUmVsbGVuYXJcIixcbiAgc3Ryb2tlV2lkdGg6IFwiR3Jvc29yIGRlbCB0cmF6b1wiLFxuICBzdHJva2VTdHlsZTogXCJFc3RpbG8gZGVsIHRyYXpvXCIsXG4gIHN0cm9rZVN0eWxlX3NvbGlkOiBcIlNcXHhGM2xpZG9cIixcbiAgc3Ryb2tlU3R5bGVfZGFzaGVkOiBcIkRpc2NvbnRpbnVhXCIsXG4gIHN0cm9rZVN0eWxlX2RvdHRlZDogXCJQdW50ZWFkb1wiLFxuICBzbG9wcGluZXNzOiBcIkVzdGlsbyBkZSB0cmF6b1wiLFxuICBvcGFjaXR5OiBcIk9wYWNpZGFkXCIsXG4gIHRleHRBbGlnbjogXCJBbGluZWFkbyBkZSB0ZXh0b1wiLFxuICBlZGdlczogXCJCb3JkZXNcIixcbiAgc2hhcnA6IFwiQWZpbGFkb1wiLFxuICByb3VuZDogXCJSZWRvbmRvXCIsXG4gIGFycm93aGVhZHM6IFwiUHVudGFzIGRlIGZsZWNoYVwiLFxuICBhcnJvd2hlYWRfbm9uZTogXCJOaW5ndW5hXCIsXG4gIGFycm93aGVhZF9hcnJvdzogXCJGbGVjaGFcIixcbiAgYXJyb3doZWFkX2JhcjogXCJCYXJyYVwiLFxuICBhcnJvd2hlYWRfY2lyY2xlOiBcIkNcXHhFRHJjdWxvXCIsXG4gIGFycm93aGVhZF9jaXJjbGVfb3V0bGluZTogXCJDXFx4RURyY3VsbyAoY29udG9ybm8pXCIsXG4gIGFycm93aGVhZF90cmlhbmdsZTogXCJUcmlcXHhFMW5ndWxvXCIsXG4gIGFycm93aGVhZF90cmlhbmdsZV9vdXRsaW5lOiBcIlRyaVxceEUxbmd1bG8gKGNvbnRvcm5vKVwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZDogXCJEaWFtYW50ZVwiLFxuICBhcnJvd2hlYWRfZGlhbW9uZF9vdXRsaW5lOiBcIkRpYW1hbnRlIChjb250b3JubylcIixcbiAgZm9udFNpemU6IFwiVGFtYVxceEYxbyBkZSBsYSBmdWVudGVcIixcbiAgZm9udEZhbWlseTogXCJUaXBvIGRlIGZ1ZW50ZVwiLFxuICBhZGRXYXRlcm1hcms6ICdBZ3JlZ2FyIFwiSGVjaG8gY29uIEV4Y2FsaWRyYXdcIicsXG4gIGhhbmREcmF3bjogXCJEaWJ1amFkbyBhIG1hbm9cIixcbiAgbm9ybWFsOiBcIk5vcm1hbFwiLFxuICBjb2RlOiBcIkNcXHhGM2RpZ29cIixcbiAgc21hbGw6IFwiUGVxdWVcXHhGMWFcIixcbiAgbWVkaXVtOiBcIk1lZGlhbmFcIixcbiAgbGFyZ2U6IFwiR3JhbmRlXCIsXG4gIHZlcnlMYXJnZTogXCJNdXkgZ3JhbmRlXCIsXG4gIHNvbGlkOiBcIlNcXHhGM2xpZG9cIixcbiAgaGFjaHVyZTogXCJGb2xsZXRvXCIsXG4gIHppZ3phZzogXCJaaWd6YWdcIixcbiAgY3Jvc3NIYXRjaDogXCJSYXlhZG8gdHJhbnN2ZXJzYWxcIixcbiAgdGhpbjogXCJGaW5vXCIsXG4gIGJvbGQ6IFwiR3J1ZXNvXCIsXG4gIGxlZnQ6IFwiSXpxdWllcmRhXCIsXG4gIGNlbnRlcjogXCJDZW50cmFkb1wiLFxuICByaWdodDogXCJEZXJlY2hhXCIsXG4gIGV4dHJhQm9sZDogXCJFeHRyYSBuZWdyaXRhXCIsXG4gIGFyY2hpdGVjdDogXCJBcnF1aXRlY3RvXCIsXG4gIGFydGlzdDogXCJBcnRpc3RhXCIsXG4gIGNhcnRvb25pc3Q6IFwiQ2FyaWNhdHVyYVwiLFxuICBmaWxlVGl0bGU6IFwiTm9tYnJlIGRlbCBhcmNoaXZvXCIsXG4gIGNvbG9yUGlja2VyOiBcIlNlbGVjdG9yIGRlIGNvbG9yXCIsXG4gIGNhbnZhc0NvbG9yczogXCJVc2FkbyBlbiBsaWVuem9cIixcbiAgY2FudmFzQmFja2dyb3VuZDogXCJGb25kbyBkZWwgbGllbnpvXCIsXG4gIGRyYXdpbmdDYW52YXM6IFwiTGllbnpvIGRlIGRpYnVqb1wiLFxuICBsYXllcnM6IFwiQ2FwYXNcIixcbiAgYWN0aW9uczogXCJBY2Npb25lc1wiLFxuICBsYW5ndWFnZTogXCJJZGlvbWFcIixcbiAgbGl2ZUNvbGxhYm9yYXRpb246IFwiQ29sYWJvcmFjaVxceEYzbiBlbiBkaXJlY3RvLi4uXCIsXG4gIGR1cGxpY2F0ZVNlbGVjdGlvbjogXCJEdXBsaWNhclwiLFxuICB1bnRpdGxlZDogXCJTaW4gdFxceEVEdHVsb1wiLFxuICBuYW1lOiBcIk5vbWJyZVwiLFxuICB5b3VyTmFtZTogXCJUdSBub21icmVcIixcbiAgbWFkZVdpdGhFeGNhbGlkcmF3OiBcIkhlY2hvIGNvbiBFeGNhbGlkcmF3XCIsXG4gIGdyb3VwOiBcIkFncnVwYXIgc2VsZWNjaVxceEYzblwiLFxuICB1bmdyb3VwOiBcIkRlc2FncnVwYXIgc2VsZWNjaVxceEYzblwiLFxuICBjb2xsYWJvcmF0b3JzOiBcIkNvbGFib3JhZG9yZXNcIixcbiAgc2hvd0dyaWQ6IFwiTW9zdHJhciBjdWFkclxceEVEY3VsYVwiLFxuICBhZGRUb0xpYnJhcnk6IFwiQVxceEYxYWRpciBhIGxhIGJpYmxpb3RlY2FcIixcbiAgcmVtb3ZlRnJvbUxpYnJhcnk6IFwiRWxpbWluYXIgZGUgbGEgYmlibGlvdGVjYVwiLFxuICBsaWJyYXJ5TG9hZGluZ01lc3NhZ2U6IFwiQ2FyZ2FuZG8gYmlibGlvdGVjYVxcdTIwMjZcIixcbiAgbGlicmFyaWVzOiBcIkV4cGxvcmFyIGJpYmxpb3RlY2FzXCIsXG4gIGxvYWRpbmdTY2VuZTogXCJDYXJnYW5kbyBlc2NlbmFcXHUyMDI2XCIsXG4gIGFsaWduOiBcIkFsaW5lYXJcIixcbiAgYWxpZ25Ub3A6IFwiQWxpbmVhY2lcXHhGM24gc3VwZXJpb3JcIixcbiAgYWxpZ25Cb3R0b206IFwiQWxpbmVhY2lcXHhGM24gaW5mZXJpb3JcIixcbiAgYWxpZ25MZWZ0OiBcIkFsaW5lYXIgYSBsYSBpenF1aWVyZGFcIixcbiAgYWxpZ25SaWdodDogXCJBbGluZWFyIGEgbGEgZGVyZWNoYVwiLFxuICBjZW50ZXJWZXJ0aWNhbGx5OiBcIkNlbnRyYXIgdmVydGljYWxtZW50ZVwiLFxuICBjZW50ZXJIb3Jpem9udGFsbHk6IFwiQ2VudHJhciBob3Jpem9udGFsbWVudGVcIixcbiAgZGlzdHJpYnV0ZUhvcml6b250YWxseTogXCJEaXN0cmlidWlyIGhvcml6b250YWxtZW50ZVwiLFxuICBkaXN0cmlidXRlVmVydGljYWxseTogXCJEaXN0cmlidWlyIHZlcnRpY2FsbWVudGVcIixcbiAgZmxpcEhvcml6b250YWw6IFwiR2lyYXIgaG9yaXpvbnRhbG1lbnRlXCIsXG4gIGZsaXBWZXJ0aWNhbDogXCJHaXJhciB2ZXJ0aWNhbG1lbnRlXCIsXG4gIHZpZXdNb2RlOiBcIk1vZG8gcHJlc2VudGFjaVxceEYzblwiLFxuICBzaGFyZTogXCJDb21wYXJ0aXJcIixcbiAgc2hvd1N0cm9rZTogXCJNb3N0cmFyIHNlbGVjdG9yIGRlIGNvbG9yIGRlIHRyYXpvXCIsXG4gIHNob3dCYWNrZ3JvdW5kOiBcIk1vc3RyYXIgZWwgc2VsZWN0b3IgZGUgY29sb3IgZGUgZm9uZG9cIixcbiAgdG9nZ2xlVGhlbWU6IFwiQ2FtYmlhciB0ZW1hXCIsXG4gIHBlcnNvbmFsTGliOiBcIkJpYmxpb3RlY2EgcGVyc29uYWxcIixcbiAgZXhjYWxpZHJhd0xpYjogXCJCaWJsaW90ZWNhIEV4Y2FsaWRyYXdcIixcbiAgZGVjcmVhc2VGb250U2l6ZTogXCJEaXNtaW51aXIgdGFtYVxceEYxbyBkZSBsZXRyYVwiLFxuICBpbmNyZWFzZUZvbnRTaXplOiBcIkF1bWVudGFyIGVsIHRhbWFcXHhGMW8gZGUgbGV0cmFcIixcbiAgdW5iaW5kVGV4dDogXCJEZXN2aW5jdWxhciB0ZXh0b1wiLFxuICBiaW5kVGV4dDogXCJWaW5jdWxhciB0ZXh0byBhbCBjb250ZW5lZG9yXCIsXG4gIGNyZWF0ZUNvbnRhaW5lckZyb21UZXh0OiBcIkVudm9sdmVyIGVsIHRleHRvIGVuIHVuIGNvbnRlbmVkb3JcIixcbiAgbGluazoge1xuICAgIGVkaXQ6IFwiRWRpdGFyIGVubGFjZVwiLFxuICAgIGVkaXRFbWJlZDogXCJFZGl0YXIgZW5sYWNlIGUgaW5jcnVzdGFyXCIsXG4gICAgY3JlYXRlOiBcIkNyZWFyIGVubGFjZVwiLFxuICAgIGNyZWF0ZUVtYmVkOiBcIkNyZWFyIGVubGFjZSBlIGluY3J1c3RhclwiLFxuICAgIGxhYmVsOiBcIkVubGFjZVwiLFxuICAgIGxhYmVsRW1iZWQ6IFwiRW5sYXphciBlIGluY3J1c3RhclwiLFxuICAgIGVtcHR5OiBcIk5vIHNlIGhhIGVzdGFibGVjaWRvIHVuIGVubGFjZVwiXG4gIH0sXG4gIGxpbmVFZGl0b3I6IHtcbiAgICBlZGl0OiBcIkVkaXRhciBsXFx4RURuZWFcIixcbiAgICBleGl0OiBcIlNhbGlyIGRlbCBlZGl0b3IgZW4gbFxceEVEbmVhXCJcbiAgfSxcbiAgZWxlbWVudExvY2s6IHtcbiAgICBsb2NrOiBcIkJsb3F1ZWFyXCIsXG4gICAgdW5sb2NrOiBcIkRlc2Jsb3F1ZWFyXCIsXG4gICAgbG9ja0FsbDogXCJCbG9xdWVhciB0b2RvXCIsXG4gICAgdW5sb2NrQWxsOiBcIkRlc2Jsb3F1ZWFyIHRvZG9cIlxuICB9LFxuICBzdGF0dXNQdWJsaXNoZWQ6IFwiUHVibGljYWRvXCIsXG4gIHNpZGViYXJMb2NrOiBcIk1hbnRlbmVyIGJhcnJhIGxhdGVyYWwgYWJpZXJ0YVwiLFxuICBzZWxlY3RBbGxFbGVtZW50c0luRnJhbWU6IFwiU2VsZWNjaW9uYXIgdG9kb3MgbG9zIGVsZW1lbnRvcyBlbiBlbCBtYXJjb1wiLFxuICByZW1vdmVBbGxFbGVtZW50c0Zyb21GcmFtZTogXCJFbGltaW5hciB0b2RvcyBsb3MgZWxlbWVudG9zIGRlbCBtYXJjb1wiLFxuICBleWVEcm9wcGVyOiBcIlNlbGVjY2lvbmFyIHVuIGNvbG9yIGRlbCBsaWVuem9cIixcbiAgdGV4dFRvRGlhZ3JhbTogXCJUZXh0byBhIGRpYWdyYW1hXCIsXG4gIHByb21wdDogXCJTdWdlcmVuY2lhXCJcbn07XG52YXIgbGlicmFyeSA9IHtcbiAgbm9JdGVtczogXCJObyBoYXkgZWxlbWVudG9zIGFcXHhGMWFkaWRvcyB0b2RhdlxceEVEYS4uLlwiLFxuICBoaW50X2VtcHR5TGlicmFyeTogXCJTZWxlY2Npb25lIHVuIGVsZW1lbnRvIGVuIGVsIGxpZW56byBwYXJhIGFcXHhGMWFkaXJsbyBhcXVcXHhFRCwgbyBpbnN0YWxlIHVuYSBiaWJsaW90ZWNhIGRlbCByZXBvc2l0b3JpbyBwXFx4RkFibGljbywgYSBjb250aW51YWNpXFx4RjNuLlwiLFxuICBoaW50X2VtcHR5UHJpdmF0ZUxpYnJhcnk6IFwiU2VsZWNjaW9uZSB1biBlbGVtZW50byBkZWwgbGllbnpvIHBhcmEgYVxceEYxYWRpcmxvIGFxdVxceEVELlwiXG59O1xudmFyIGJ1dHRvbnMgPSB7XG4gIGNsZWFyUmVzZXQ6IFwiTGltcGlhciBsaWVuem8geSByZWluaWNpYXIgZWwgY29sb3IgZGUgZm9uZG9cIixcbiAgZXhwb3J0SlNPTjogXCJFeHBvcnRhciBhIGFyY2hpdm9cIixcbiAgZXhwb3J0SW1hZ2U6IFwiRXhwb3J0YXIgaW1hZ2VuLi4uXCIsXG4gIGV4cG9ydDogXCJHdWFyZGFyIGVuLi4uXCIsXG4gIGNvcHlUb0NsaXBib2FyZDogXCJDb3BpYXIgYWwgcG9ydGFwYXBlbGVzXCIsXG4gIHNhdmU6IFwiR3VhcmRhciBlbiBhcmNoaXZvIGFjdHVhbFwiLFxuICBzYXZlQXM6IFwiR3VhcmRhciBjb21vXCIsXG4gIGxvYWQ6IFwiQWJyaXJcIixcbiAgZ2V0U2hhcmVhYmxlTGluazogXCJPYnRlbmVyIGVubGFjZSBwYXJhIGNvbXBhcnRpclwiLFxuICBjbG9zZTogXCJDZXJyYXJcIixcbiAgc2VsZWN0TGFuZ3VhZ2U6IFwiRWxlZ2lyIGlkaW9tYVwiLFxuICBzY3JvbGxCYWNrVG9Db250ZW50OiBcIlZvbHZlciBhbCBjb250ZW5pZG9cIixcbiAgem9vbUluOiBcIkFjZXJjYXJzZVwiLFxuICB6b29tT3V0OiBcIkFsZWphcnNlXCIsXG4gIHJlc2V0Wm9vbTogXCJSZXN0YWJsZWNlciB6b29tXCIsXG4gIG1lbnU6IFwiTWVuXFx4RkFcIixcbiAgZG9uZTogXCJIZWNob1wiLFxuICBlZGl0OiBcIkVkaXRhclwiLFxuICB1bmRvOiBcIkRlc2hhY2VyXCIsXG4gIHJlZG86IFwiUmVoYWNlclwiLFxuICByZXNldExpYnJhcnk6IFwiUmVpbmljaWFyIGJpYmxpb3RlY2FcIixcbiAgY3JlYXRlTmV3Um9vbTogXCJDcmVhciBudWV2YSBzYWxhXCIsXG4gIGZ1bGxTY3JlZW46IFwiUGFudGFsbGEgY29tcGxldGFcIixcbiAgZGFya01vZGU6IFwiTW9kbyBvc2N1cm9cIixcbiAgbGlnaHRNb2RlOiBcIk1vZG8gY2xhcm9cIixcbiAgemVuTW9kZTogXCJNb2RvIFplblwiLFxuICBvYmplY3RzU25hcE1vZGU6IFwiQWp1c3RhciBhIGxvcyBvYmpldG9zXCIsXG4gIGV4aXRaZW5Nb2RlOiBcIlNhbGlyIGRlbCBtb2RvIFplblwiLFxuICBjYW5jZWw6IFwiQ2FuY2VsYXJcIixcbiAgY2xlYXI6IFwiQm9ycmFyXCIsXG4gIHJlbW92ZTogXCJFbGltaW5hclwiLFxuICBlbWJlZDogXCJcIixcbiAgcHVibGlzaExpYnJhcnk6IFwiUHVibGljYXJcIixcbiAgc3VibWl0OiBcIkVudmlhclwiLFxuICBjb25maXJtOiBcIkNvbmZpcm1hclwiLFxuICBlbWJlZGRhYmxlSW50ZXJhY3Rpb25CdXR0b246IFwiUHVsc2EgcGFyYSBpbnRlcmFjdHVhclwiXG59O1xudmFyIGFsZXJ0cyA9IHtcbiAgY2xlYXJSZXNldDogXCJFc3RvIGxpbXBpYXJcXHhFMSB0b2RvIGVsIGxpZW56by4gRXN0XFx4RTFzIHNlZ3Vybz9cIixcbiAgY291bGROb3RDcmVhdGVTaGFyZWFibGVMaW5rOiBcIk5vIHNlIHB1ZG8gY3JlYXIgdW4gZW5sYWNlIHBhcmEgY29tcGFydGlyLlwiLFxuICBjb3VsZE5vdENyZWF0ZVNoYXJlYWJsZUxpbmtUb29CaWc6IFwiTm8gc2UgcHVkbyBjcmVhciBlbCBlbmxhY2UgcGFyYSBjb21wYXJ0aXI6IGxhIGVzY2VuYSBlcyBkZW1hc2lhZG8gZ3JhbmRlXCIsXG4gIGNvdWxkTm90TG9hZEludmFsaWRGaWxlOiBcIk5vIHNlIHB1ZG8gY2FyZ2FyIGVsIGFyY2hpdm8gbm8gdlxceEUxbGlkb1wiLFxuICBpbXBvcnRCYWNrZW5kRmFpbGVkOiBcIkxhIGltcG9ydGFjaVxceEYzbiBmYWxsXFx4RjMuXCIsXG4gIGNhbm5vdEV4cG9ydEVtcHR5Q2FudmFzOiBcIk5vIHNlIHB1ZWRlIGV4cG9ydGFyIHVuIGxpZW56byB2YWNpXFx4RjNcIixcbiAgY291bGROb3RDb3B5VG9DbGlwYm9hcmQ6IFwiTm8gc2UgcHVkbyBjb3BpYXIgYWwgcG9ydGFwYXBlbGVzLlwiLFxuICBkZWNyeXB0RmFpbGVkOiBcIk5vIHNlIHB1ZGllcm9uIGRlc2NpZnJhciBsb3MgZGF0b3MuXCIsXG4gIHVwbG9hZGVkU2VjdXJseTogXCJMYSBjYXJnYSBoYSBzaWRvIGFzZWd1cmFkYSBjb24gY2lmcmFkbyBkZSBwcmluY2lwaW8gYSBmaW4sIGxvIHF1ZSBzaWduaWZpY2EgcXVlIGVsIHNlcnZpZG9yIGRlIEV4Y2FsaWRyYXcgeSB0ZXJjZXJvcyBubyBwdWVkZW4gbGVlciBlbCBjb250ZW5pZG8uXCIsXG4gIGxvYWRTY2VuZU92ZXJyaWRlUHJvbXB0OiBcIlNpIGNhcmdhIGVzdGUgZGlidWpvIGV4dGVybm8sIHJlZW1wbGF6YXJcXHhFMSBlbCBxdWUgdGllbmUuIFxceEJGRGVzZWEgY29udGludWFyP1wiLFxuICBjb2xsYWJTdG9wT3ZlcnJpZGVQcm9tcHQ6IFwiRGV0ZW5lciBsYSBzZXNpXFx4RjNuIHNvYnJlc2NyaWJpclxceEUxIHN1IGRpYnVqbyBhbnRlcmlvciBhbG1hY2VuYWRvIGxvY2FsbWVudGUuIFxceEJGRXN0XFx4RTEgc2VndXJvP1xcblxcbihTaSBkZXNlYSBtYW50ZW5lciBzdSBkaWJ1am8gbG9jYWwsIHNpbXBsZW1lbnRlIGNpZXJyZSBsYSBwZXN0YVxceEYxYSBkZWwgbmF2ZWdhZG9yLilcIixcbiAgZXJyb3JBZGRpbmdUb0xpYnJhcnk6IFwiTm8gc2UgcHVkbyBhZ3JlZ2FyIGVsIGVsZW1lbnRvIGEgbGEgYmlibGlvdGVjYVwiLFxuICBlcnJvclJlbW92aW5nRnJvbUxpYnJhcnk6IFwiTm8gc2UgcHVkbyBxdWl0YXIgZWwgZWxlbWVudG8gZGUgbGEgYmlibGlvdGVjYVwiLFxuICBjb25maXJtQWRkTGlicmFyeTogXCJFc3RvIGFcXHhGMWFkaXJcXHhFMSB7e251bVNoYXBlc319IGZvcm1hKHMpIGEgdHUgYmlibGlvdGVjYS4gXFx4QkZFc3RcXHhFMXMgc2VndXJvP1wiLFxuICBpbWFnZURvZXNOb3RDb250YWluU2NlbmU6IFwiRXN0YSBpbWFnZW4gbm8gcGFyZWNlIGNvbnRlbmVyIGRhdG9zIGRlIGVzY2VuYS4gXFx4QkZIYSBoYWJpbGl0YWRvIGxhIGluc2VyY2lcXHhGM24gZGUgbGEgZXNjZW5hIGR1cmFudGUgbGEgZXhwb3J0YWNpXFx4RjNuP1wiLFxuICBjYW5ub3RSZXN0b3JlRnJvbUltYWdlOiBcIk5vIHNlIHB1ZG8gcmVzdGF1cmFyIGxhIGVzY2VuYSBkZXNkZSBlc3RlIGFyY2hpdm8gZGUgaW1hZ2VuXCIsXG4gIGludmFsaWRTY2VuZVVybDogXCJObyBzZSBoYSBwb2RpZG8gaW1wb3J0YXIgbGEgZXNjZW5hIGRlc2RlIGxhIFVSTCBwcm9wb3JjaW9uYWRhLiBFc3RcXHhFMSBtYWwgZm9ybWFkYSwgbyBubyBjb250aWVuZSBkYXRvcyBkZSBFeGNhbGlkcmF3IEpTT04gdlxceEUxbGlkb3MuXCIsXG4gIHJlc2V0TGlicmFyeTogXCJFc3RvIGJvcnJhclxceEUxIHR1IGJpYmxpb3RlY2EuIFxceEJGRXN0XFx4RTFzIHNlZ3Vybz9cIixcbiAgcmVtb3ZlSXRlbXNGcm9tc0xpYnJhcnk6IFwiXFx4QkZFbGltaW5hciB7e2NvdW50fX0gZWxlbWVudG8ocykgZGUgbGEgYmlibGlvdGVjYT9cIixcbiAgaW52YWxpZEVuY3J5cHRpb25LZXk6IFwiTGEgY2xhdmUgZGUgY2lmcmFkbyBkZWJlIHRlbmVyIDIyIGNhcmFjdGVyZXMuIExhIGNvbGFib3JhY2lcXHhGM24gZW4gdml2byBlc3RcXHhFMSBkZXNoYWJpbGl0YWRhLlwiLFxuICBjb2xsYWJPZmZsaW5lV2FybmluZzogXCJObyBoYXkgY29uZXhpXFx4RjNuIGEgaW50ZXJuZXQgZGlzcG9uaWJsZS5cXG5cXHhBMU5vIHNlIGd1YXJkYXJcXHhFMW4gbG9zIGNhbWJpb3MhXCJcbn07XG52YXIgZXJyb3JzID0ge1xuICB1bnN1cHBvcnRlZEZpbGVUeXBlOiBcIlRpcG8gZGUgYXJjaGl2byBubyBhZG1pdGlkby5cIixcbiAgaW1hZ2VJbnNlcnRFcnJvcjogXCJObyBzZSBwdWRvIGluc2VydGFyIGxhIGltYWdlbi4gSW50XFx4RTludGVsbyBkZSBudWV2byBtXFx4RTFzIHRhcmRlLi4uXCIsXG4gIGZpbGVUb29CaWc6IFwiQXJjaGl2byBkZW1hc2lhZG8gZ3JhbmRlLiBFbCB0YW1hXFx4RjFvIG1cXHhFMXhpbW8gcGVybWl0aWRvIGVzIHt7bWF4U2l6ZX19LlwiLFxuICBzdmdJbWFnZUluc2VydEVycm9yOiBcIk5vIHNlIHB1ZG8gaW5zZXJ0YXIgbGEgaW1hZ2VuIFNWRy4gRWwgY1xceEYzZGlnbyBTVkcgcGFyZWNlIGludlxceEUxbGlkby5cIixcbiAgZmFpbGVkVG9GZXRjaEltYWdlOiBcIkVycm9yIGFsIG9idGVuZXIgbGEgaW1hZ2VuLlwiLFxuICBpbnZhbGlkU1ZHU3RyaW5nOiBcIlNWRyBubyB2XFx4RTFsaWRvLlwiLFxuICBjYW5ub3RSZXNvbHZlQ29sbGFiU2VydmVyOiBcIk5vIHNlIHB1ZG8gY29uZWN0YXIgYWwgc2Vydmlkb3IgY29sYWJvcmFkb3IuIFBvciBmYXZvciwgdnVlbHZhIGEgY2FyZ2FyIGxhIHBcXHhFMWdpbmEgeSB2dWVsdmEgYSBpbnRlbnRhcmxvLlwiLFxuICBpbXBvcnRMaWJyYXJ5RXJyb3I6IFwiTm8gc2UgcHVkbyBjYXJnYXIgbGEgbGlicmVyXFx4RURhXCIsXG4gIGNvbGxhYlNhdmVGYWlsZWQ6IFwiTm8gc2UgcHVkbyBndWFyZGFyIGVuIGxhIGJhc2UgZGUgZGF0b3MgZGVsIGJhY2tlbmQuIFNpIGxvcyBwcm9ibGVtYXMgcGVyc2lzdGVuLCBkZWJlclxceEVEYSBndWFyZGFyIHN1IGFyY2hpdm8gbG9jYWxtZW50ZSBwYXJhIGFzZWd1cmFyc2UgZGUgcXVlIG5vIHBpZXJkZSBzdSB0cmFiYWpvLlwiLFxuICBjb2xsYWJTYXZlRmFpbGVkX3NpemVFeGNlZWRlZDogXCJObyBzZSBwdWRvIGd1YXJkYXIgZW4gbGEgYmFzZSBkZSBkYXRvcyBkZWwgYmFja2VuZCwgZWwgbGllbnpvIHBhcmVjZSBzZXIgZGVtYXNpYWRvIGdyYW5kZS4gRGViZXJcXHhFRGEgZ3VhcmRhciBlbCBhcmNoaXZvIGxvY2FsbWVudGUgcGFyYSBhc2VndXJhcnNlIGRlIHF1ZSBubyBwaWVyZGUgc3UgdHJhYmFqby5cIixcbiAgaW1hZ2VUb29sTm90U3VwcG9ydGVkOiBcIlwiLFxuICBicmF2ZV9tZWFzdXJlX3RleHRfZXJyb3I6IHtcbiAgICBsaW5lMTogXCJQYXJlY2UgcXVlIGVzdFxceEUxcyB1c2FuZG8gZWwgbmF2ZWdhZG9yIEJyYXZlIGNvbiBlbCBhanVzdGUgPGJvbGQ+Rm9yemFyIGVsIGJsb3F1ZW8gZGUgaHVlbGxhcyBkaWdpdGFsZXM8L2JvbGQ+IGhhYmlsaXRhZG8uXCIsXG4gICAgbGluZTI6IFwiRXN0byBwb2RyXFx4RURhIHJlc3VsdGFyIGVuIGVycm9yZXMgZW4gbG9zIDxib2xkPkVsZW1lbnRvcyBkZSBUZXh0bzwvYm9sZD4gZW4gdHVzIGRpYnVqb3MuXCIsXG4gICAgbGluZTM6IFwiUmVjb21lbmRhbW9zIGZ1ZXJ0ZW1lbnRlIGRlc2hhYmlsaXRhciBlc3RhIGNvbmZpZ3VyYWNpXFx4RjNuLiBQdWVkZXMgc2VndWlyIDxsaW5rPmVzdG9zIHBhc29zPC9saW5rPiBzb2JyZSBjXFx4RjNtbyBoYWNlcmxvLlwiLFxuICAgIGxpbmU0OiBcIlwiXG4gIH0sXG4gIGxpYnJhcnlFbGVtZW50VHlwZUVycm9yOiB7XG4gICAgZW1iZWRkYWJsZTogXCJcIixcbiAgICBpZnJhbWU6IFwiTG9zIGVsZW1lbnRvcyBJRnJhbWUgbm8gc2UgcHVlZGVuIGFncmVnYXIgYSBsYSBiaWJsaW90ZWNhLlwiLFxuICAgIGltYWdlOiBcIlwiXG4gIH0sXG4gIGFzeW5jUGFzdGVGYWlsZWRPblJlYWQ6IFwiTm8gc2UgcHVkbyBwZWdhciAobm8gc2UgcHVkbyBsZWVyIGRlc2RlIGVsIHBvcnRhcGFwZWxlcyBkZWwgc2lzdGVtYSkuXCIsXG4gIGFzeW5jUGFzdGVGYWlsZWRPblBhcnNlOiBcIk5vIHNlIHB1ZG8gcGVnYXIuXCIsXG4gIGNvcHlUb1N5c3RlbUNsaXBib2FyZEZhaWxlZDogXCJObyBzZSBwdWRvIGNvcGlhciBhbCBwb3J0YXBhcGVsZXMuXCJcbn07XG52YXIgdG9vbEJhciA9IHtcbiAgc2VsZWN0aW9uOiBcIlNlbGVjY2lcXHhGM25cIixcbiAgaW1hZ2U6IFwiSW5zZXJ0YXIgaW1hZ2VuXCIsXG4gIHJlY3RhbmdsZTogXCJSZWN0XFx4RTFuZ3Vsb1wiLFxuICBkaWFtb25kOiBcIkRpYW1hbnRlXCIsXG4gIGVsbGlwc2U6IFwiRWxpcHNlXCIsXG4gIGFycm93OiBcIkZsZWNoYVwiLFxuICBsaW5lOiBcIkxcXHhFRG5lYVwiLFxuICBmcmVlZHJhdzogXCJEaWJ1amFyXCIsXG4gIHRleHQ6IFwiVGV4dG9cIixcbiAgbGlicmFyeTogXCJCaWJsaW90ZWNhXCIsXG4gIGxvY2s6IFwiTWFudGVuZXIgbGEgaGVycmFtaWVudGEgc2VsZWNjaW9uYWRhIGFjdGl2YSBkZXNwdVxceEU5cyBkZSBkaWJ1amFyXCIsXG4gIHBlbk1vZGU6IFwiTW9kbyBMXFx4RTFwaXogLSBwcmV2aWVuZSB0b3F1ZVwiLFxuICBsaW5rOiBcIkFcXHhGMWFkaXIvQWN0dWFsaXphciBlbmxhY2UgcGFyYSB1bmEgZm9ybWEgc2VsZWNjaW9uYWRhXCIsXG4gIGVyYXNlcjogXCJCb3JyYXJcIixcbiAgZnJhbWU6IFwiXCIsXG4gIG1hZ2ljZnJhbWU6IFwiRXNxdWVtYSBhIGNcXHhGM2RpZ29cIixcbiAgZW1iZWRkYWJsZTogXCJJbmNydXN0YXIgV2ViXCIsXG4gIGxhc2VyOiBcIlB1bnRlcm8gbFxceEUxc2VyXCIsXG4gIGhhbmQ6IFwiTWFubyAoaGVycmFtaWVudGEgZGUgcGFub3JhbWl6YWNpXFx4RjNuKVwiLFxuICBleHRyYVRvb2xzOiBcIk1cXHhFMXMgaGVycmFtaWVudGFzXCIsXG4gIG1lcm1haWRUb0V4Y2FsaWRyYXc6IFwiTWVybWFpZCBhIEV4Y2FsaWRyYXdcIixcbiAgbWFnaWNTZXR0aW5nczogXCJBanVzdGVzIEFJXCJcbn07XG52YXIgaGVhZGluZ3MgPSB7XG4gIGNhbnZhc0FjdGlvbnM6IFwiQWNjaW9uZXMgZGVsIGxpZW56b1wiLFxuICBzZWxlY3RlZFNoYXBlQWN0aW9uczogXCJBY2Npb25lcyBkZSBsYSBmb3JtYSBzZWxlY2Npb25hZGFcIixcbiAgc2hhcGVzOiBcIkZvcm1hc1wiXG59O1xudmFyIGhpbnRzID0ge1xuICBjYW52YXNQYW5uaW5nOiBcIlBhcmEgbW92ZXIgZWwgbGllbnpvLCBtYW50ZW5nYSBsYSBydWVkYSBkZWwgcmF0XFx4RjNuIG8gbGEgYmFycmEgZXNwYWNpYWRvcmEgbWllbnRyYXMgYXJyYXN0cmEgbyB1dGlsaWNlIGxhIGhlcnJhbWllbnRhIGRlIG1hbm9cIixcbiAgbGluZWFyRWxlbWVudDogXCJIYXogY2xpYyBwYXJhIGRpYnVqYXIgbVxceEZBbHRpcGxlcyBwdW50b3MsIGFycmFzdHJhciBwYXJhIHNvbG8gdW5hIGxcXHhFRG5lYVwiLFxuICBmcmVlRHJhdzogXCJIYXogY2xpYyB5IGFycmFzdHJhLCBzdWVsdGEgYWwgdGVybWluYXJcIixcbiAgdGV4dDogXCJDb25zZWpvOiB0YW1iaVxceEU5biBwdWVkZXMgYVxceEYxYWRpciB0ZXh0byBoYWNpZW5kbyBkb2JsZSBjbGljIGVuIGN1YWxxdWllciBsdWdhciBjb24gbGEgaGVycmFtaWVudGEgZGUgc2VsZWNjaVxceEYzblwiLFxuICBlbWJlZGRhYmxlOiBcIkhhZ2EgY2xpYyB5IGFycmFzdHJlIHBhcmEgY3JlYXIgdW4gc2l0aW8gd2ViIGluY3J1c3RhZG9cIixcbiAgdGV4dF9zZWxlY3RlZDogXCJEb2JsZSBjbGljIG8gcHVsc2UgRU5URVIgcGFyYSBlZGl0YXIgZWwgdGV4dG9cIixcbiAgdGV4dF9lZGl0aW5nOiBcIlB1bHNlIEVzY2FwZSBvIEN0cmwvQ21kICsgRU5URVIgcGFyYSB0ZXJtaW5hciBkZSBlZGl0YXJcIixcbiAgbGluZWFyRWxlbWVudE11bHRpOiBcIkhheiBjbGljIGVuIGVsIFxceEZBbHRpbW8gcHVudG8gbyBwcmVzaW9uYSBFc2NhcGUgbyBFbnRlciBwYXJhIGZpbmFsaXphclwiLFxuICBsb2NrQW5nbGU6IFwiUHVlZGVzIHJlc3RyaW5naXIgZWwgXFx4RTFuZ3VsbyBtYW50ZW5pZW5kbyBwcmVzaW9uYWRvIGVsIGJvdFxceEYzbiBTSElGVFwiLFxuICByZXNpemU6IFwiUGFyYSBtYW50ZW5lciBsYXMgcHJvcG9yY2lvbmVzIG1hbnRcXHhFOW4gU0hJRlQgcHJlc2lvbmFkbyBtaWVudHJhcyBtb2RpZmljYXMgZWwgdGFtYVxceEYxbywgXFxubWFudFxceEU5biBwcmVzaW9uYWRvIEFMVCBwYXJhIG1vZGlmaWNhciBlbCB0YW1hXFx4RjFvIGRlc2RlIGVsIGNlbnRyb1wiLFxuICByZXNpemVJbWFnZTogXCJQdWVkZSByZWRpbWVuc2lvbmFyIGxpYnJlbWVudGUgcHVsc2FuZG8gU0hJRlQsXFxucHVsc2UgQUxUIHBhcmEgcmVkaW1lbnNpb25hciBkZXNkZSBlbCBjZW50cm9cIixcbiAgcm90YXRlOiBcIlB1ZWRlcyByZXN0cmluZ2lyIGxvcyBcXHhFMW5ndWxvcyBtYW50ZW5pZW5kbyBwcmVzaW9uYWRvIFNISUZUIG1pZW50cmFzIGdpcmFzXCIsXG4gIGxpbmVFZGl0b3JfaW5mbzogXCJNYW50ZW5nYSBwdWxzYWRvIEN0cmxPckNtZCB5IGhhZ2EgZG9ibGUgY2xpY2sgbyBwcmVzaW9uZSBDdHJsT3JDbWQgKyBFbnRlciBwYXJhIGVkaXRhciBwdW50b3NcIixcbiAgbGluZUVkaXRvcl9wb2ludFNlbGVjdGVkOiBcIlByZXNpb25lIFN1cHJpbWlyIHBhcmEgZWxpbWluYXIgZWwvbG9zIHB1bnRvKHMpLCBDdHJsT3JDbWQrRCBwYXJhIGR1cGxpY2FybG8sIG8gYXJyXFx4RTFzdHJlbG8gcGFyYSBtb3ZlcmxvXCIsXG4gIGxpbmVFZGl0b3Jfbm90aGluZ1NlbGVjdGVkOiBcIlNlbGVjY2lvbmUgdW4gcHVudG8gYSBlZGl0YXIgKG1hbnRlbmdhIE1BWVxceERBU0NVTEFTIHBhcmEgc2VsZWNjaW9uYXIgbVxceEZBbHRpcGxlcyksXFxubyBtYW50ZW5nYSBwdWxzYWRvIEFsdCB5IGhhZ2EgY2xpY2sgcGFyYSBhXFx4RjFhZGlyIG51ZXZvcyBwdW50b3NcIixcbiAgcGxhY2VJbWFnZTogXCJIYWdhIGNsaWMgcGFyYSBjb2xvY2FyIGxhIGltYWdlbiBvIGhhZ2EgY2xpY2sgeSBhcnJhc3RyZSBwYXJhIGVzdGFibGVjZXIgc3UgdGFtYVxceEYxbyBtYW51YWxtZW50ZVwiLFxuICBwdWJsaXNoTGlicmFyeTogXCJQdWJsaWNhIHR1IHByb3BpYSBiaWJsaW90ZWNhXCIsXG4gIGJpbmRUZXh0VG9FbGVtZW50OiBcIlByZXNpb25lIEVudHJhciBwYXJhIGFncmVnYXJcIixcbiAgZGVlcEJveFNlbGVjdDogXCJNYW50XFx4RTluIEN0cmxPckNtZCBwYXJhIHNlbGVjY2lvbmFyIGVuIHByb2Z1bmRpZGFkLCB5IHBhcmEgZXZpdGFyIGFycmFzdHJhclwiLFxuICBlcmFzZXJSZXZlcnQ6IFwiTWFudGVuZ2EgcHVsc2FkbyBBbHQgcGFyYSByZXZlcnRpciBsb3MgZWxlbWVudG9zIG1hcmNhZG9zIHBhcmEgc3UgZWxpbWluYWNpXFx4RjNuXCIsXG4gIGZpcmVmb3hfY2xpcGJvYXJkX3dyaXRlOiAnRXN0YSBjYXJhY3RlclxceEVEc3RpY2EgcHVlZGUgc2VyIGhhYmlsaXRhZGEgZXN0YWJsZWNpZW5kbyBsYSBiYW5kZXJhIFwiZG9tLmV2ZW50cy5hc3luY0NsaXBib2FyZC5jbGlwYm9hcmRJdGVtXCIgYSBcInRydWVcIi4gUGFyYSBjYW1iaWFyIGxhcyBiYW5kZXJhcyBkZWwgbmF2ZWdhZG9yIGVuIEZpcmVmb3gsIHZpc2l0ZSBsYSBwXFx4RTFnaW5hIFwiYWJvdXQ6Y29uZmlnXCIuJyxcbiAgZGlzYWJsZVNuYXBwaW5nOiBcIk1hbnRcXHhFOW4gcHVsc2FkbyBDdHJsT3JDbWQgcGFyYSBkZXNhY3RpdmFyIGVsIGFqdXN0ZVwiXG59O1xudmFyIGNhbnZhc0Vycm9yID0ge1xuICBjYW5ub3RTaG93UHJldmlldzogXCJObyBzZSBwdWVkZSBtb3N0cmFyIGxhIHZpc3RhIHByZXZpYVwiLFxuICBjYW52YXNUb29CaWc6IFwiRWwgbGllbnpvIHBvZHJcXHhFRGEgc2VyIGRlbWFzaWFkbyBncmFuZGUuXCIsXG4gIGNhbnZhc1Rvb0JpZ1RpcDogXCJTdWdlcmVuY2lhOiBpbnRlbnRhIGFjZXJjYXIgdW4gcG9jbyBtXFx4RTFzIGxvcyBlbGVtZW50b3MgbVxceEUxcyBsZWphbm9zLlwiXG59O1xudmFyIGVycm9yU3BsYXNoID0ge1xuICBoZWFkaW5nTWFpbjogXCJTZSBlbmNvbnRyXFx4RjMgdW4gZXJyb3IuIEludGVudGUgPGJ1dHRvbj5yZWNhcmdhbmRvIGxhIHBcXHhFMWdpbmEuPC9idXR0b24+XCIsXG4gIGNsZWFyQ2FudmFzTWVzc2FnZTogXCJTaSBsYSByZWNhcmdhIG5vIGZ1bmNpb25hLCBpbnRlbnRlIDxidXR0b24+bGltcGlhbmRvIGVsIGxpZW56by48L2J1dHRvbj5cIixcbiAgY2xlYXJDYW52YXNDYXZlYXQ6IFwiIEVzdG8gcHJvdm9jYXJcXHhFMSBsYSBwXFx4RTlyZGlkYSBkZSBzdSB0cmFiYWpvIFwiLFxuICB0cmFja2VkVG9TZW50cnk6IFwiRWwgZXJyb3IgY29uIGVsIGlkZW50aWZpY2Fkb3Ige3tldmVudElkfX0gZnVlIHJhc3RyZWFkbyBlbiBudWVzdHJvIHNpc3RlbWEuXCIsXG4gIG9wZW5Jc3N1ZU1lc3NhZ2U6IFwiRnVpbW9zIG11eSBjYXV0ZWxvc29zIGRlIG5vIGluY2x1aXIgbGEgaW5mb3JtYWNpXFx4RjNuIGRlIHR1IGVzY2VuYSBlbiBlbCBlcnJvci4gU2kgdHUgZXNjZW5hIG5vIGVzIHByaXZhZGEsIHBvciBmYXZvciBjb25zaWRlcmEgc2VndWlyIG51ZXN0cm8gPGJ1dHRvbj5yYXN0cmVhZG9yIGRlIGVycm9yZXMuPC9idXR0b24+IFBvciBmYXZvciwgaW5jbHV5YSBsYSBzaWd1aWVudGUgaW5mb3JtYWNpXFx4RjNuIGNvcGlcXHhFMW5kb2xhIHkgcGVnXFx4RTFuZG9sYSBlbiBlbCBpc3N1ZSBkZSBHaXRIdWIuXCIsXG4gIHNjZW5lQ29udGVudDogXCJDb250ZW5pZG8gZGUgbGEgZXNjZW5hOlwiXG59O1xudmFyIHJvb21EaWFsb2cgPSB7XG4gIGRlc2NfaW50cm86IFwiUHVlZGUgaW52aXRhciBhIG90cmFzIHBlcnNvbmFzIGEgdHUgYWN0dWFsIGVzY2VuYSBwYXJhIHF1ZSBjb2xhYm9yZW4gY29udGlnby5cIixcbiAgZGVzY19wcml2YWN5OiBcIk5vIHRlIHByZW9jdXBlcywgbGEgc2VzaVxceEYzbiB1c2EgZW5jcmlwdGFjaVxceEYzbiBkZSBwdW50YSBhIHB1bnRhLCBwb3IgbG8gcXVlIHRvZG8gbG8gcXVlIHNlIGRpYnVqZSBzZSBtYW50ZW5kclxceEUxIHByaXZhZGFtZW50ZS4gTmkgc2lxdWllcmEgbnVlc3RybyBzZXJ2aWRvciBwb2RyXFx4RTEgdmVyIGxvIHF1ZSBoYWNlcy5cIixcbiAgYnV0dG9uX3N0YXJ0U2Vzc2lvbjogXCJJbmljaWFyIHNlc2lcXHhGM25cIixcbiAgYnV0dG9uX3N0b3BTZXNzaW9uOiBcIkRldGVuZXIgc2VzaVxceEYzblwiLFxuICBkZXNjX2luUHJvZ3Jlc3NJbnRybzogXCJMYSBzZXNpXFx4RjNuIGRlIGNvbGFib3JhY2lcXHhGM24gZW4gdml2byBlc3RcXHhFMSBhaG9yYSBlbiBwcm9ncmVzby5cIixcbiAgZGVzY19zaGFyZUxpbms6IFwiQ29tcGFydGUgZXN0ZSBlbmxhY2UgY29uIGN1YWxxdWllciBwZXJzb25hIGNvbiBxdWllbiBxdWllcmFzIGNvbGFib3JhcjpcIixcbiAgZGVzY19leGl0U2Vzc2lvbjogXCJEZXRlbmVyIGxhIHNlc2lcXHhGM24gdGUgZGVzY29uZWN0YXJcXHhFMSBkZSBsYSBzYWxhLCBwZXJvIHBvZHJcXHhFMXMgc2VndWlyIHRyYWJhamFuZG8gY29uIGxhIGVzY2VuYSBlbiBzdSBjb21wdXRhZG9yYSwgZXN0byBlcyBkZSBtb2RvIGxvY2FsLiBUZW4gZW4gY3VlbnRhIHF1ZSBlc3RvIG5vIGFmZWN0YXJcXHhFMSBhIG90cmFzIHBlcnNvbmFzLCB5IHF1ZSBsYXMgbWlzbWFzIHNlZ3VpclxceEUxbiBzaWVuZG8gY2FwYWNlcyBkZSBjb2xhYm9yYXIgZW4gdHUgZXNjZW5hLlwiLFxuICBzaGFyZVRpdGxlOiBcIlxceERBbmFzZSBhIHVuYSBzZXNpXFx4RjNuIGNvbGFib3JhdGl2YSBlbiB2aXZvIGVuIEV4Y2FsaWRyYXdcIlxufTtcbnZhciBlcnJvckRpYWxvZyA9IHtcbiAgdGl0bGU6IFwiRXJyb3JcIlxufTtcbnZhciBleHBvcnREaWFsb2cgPSB7XG4gIGRpc2tfdGl0bGU6IFwiR3VhcmRhciBlbiBkaXNjb1wiLFxuICBkaXNrX2RldGFpbHM6IFwiRXhwb3J0YXIgbG9zIGRhdG9zIGRlIGxhIGVzY2VuYSBhIHVuIGFyY2hpdm8gZGVzZGUgZWwgY3VhbCBwdWVkYSBpbXBvcnRhciBtXFx4RTFzIHRhcmRlLlwiLFxuICBkaXNrX2J1dHRvbjogXCJHdWFyZGFyIGVuIGFyY2hpdm9cIixcbiAgbGlua190aXRsZTogXCJFbmxhY2UgcGFyYSBjb21wYXJ0aXJcIixcbiAgbGlua19kZXRhaWxzOiBcIkV4cG9ydGFyIGNvbW8gZW5sYWNlIGRlIHNcXHhGM2xvIGxlY3R1cmEuXCIsXG4gIGxpbmtfYnV0dG9uOiBcIkV4cG9ydGFyIGEgTGlua1wiLFxuICBleGNhbGlkcmF3cGx1c19kZXNjcmlwdGlvbjogXCJHdWFyZGUgbGEgZXNjZW5hIGVuIHN1IGVzcGFjaW8gZGUgdHJhYmFqbyBkZSBFeGNhbGlkcmF3Ky5cIixcbiAgZXhjYWxpZHJhd3BsdXNfYnV0dG9uOiBcIkV4cG9ydGFyXCIsXG4gIGV4Y2FsaWRyYXdwbHVzX2V4cG9ydEVycm9yOiBcIk5vIHNlIHB1ZG8gZXhwb3J0YXIgYSBFeGNhbGlkcmF3KyBlbiBlc3RlIG1vbWVudG8uLi5cIlxufTtcbnZhciBoZWxwRGlhbG9nID0ge1xuICBibG9nOiBcIkxlYSBudWVzdHJvIGJsb2dcIixcbiAgY2xpY2s6IFwiY2xpY2tcIixcbiAgZGVlcFNlbGVjdDogXCJTZWxlY2NpXFx4RjNuIHByb2Z1bmRhXCIsXG4gIGRlZXBCb3hTZWxlY3Q6IFwiU2VsZWNjaW9uZSBlbiBwcm9mdW5kaWRhZCBkZW50cm8gZGUgbGEgY2FqYSwgeSBldml0ZSBhcnJhc3RyYXJcIixcbiAgY3VydmVkQXJyb3c6IFwiRmxlY2hhIGN1cnZhXCIsXG4gIGN1cnZlZExpbmU6IFwiTFxceEVEbmVhIGN1cnZhXCIsXG4gIGRvY3VtZW50YXRpb246IFwiRG9jdW1lbnRhY2lcXHhGM25cIixcbiAgZG91YmxlQ2xpY2s6IFwiZG9ibGUgY2xpY1wiLFxuICBkcmFnOiBcImFycmFzdHJhclwiLFxuICBlZGl0b3I6IFwiRWRpdG9yXCIsXG4gIGVkaXRMaW5lQXJyb3dQb2ludHM6IFwiRWRpdGFyIHB1bnRvcyBkZSBsXFx4RURuZWEvZmxlY2hhXCIsXG4gIGVkaXRUZXh0OiBcIkVkaXRhciB0ZXh0byAvIGFcXHhGMWFkaXIgZXRpcXVldGFcIixcbiAgZ2l0aHViOiBcIlxceEJGSGEgZW5jb250cmFkbyB1biBwcm9ibGVtYT8gRW52XFx4RURlbG9cIixcbiAgaG93dG86IFwiU2lnYSBudWVzdHJhcyBndVxceEVEYXNcIixcbiAgb3I6IFwib1wiLFxuICBwcmV2ZW50QmluZGluZzogXCJFdml0YXIgZW5sYWNlIGRlIGZsZWNoYXNcIixcbiAgdG9vbHM6IFwiSGVycmFtaWVudGFzXCIsXG4gIHNob3J0Y3V0czogXCJBdGFqb3MgZGVsIHRlY2xhZG9cIixcbiAgdGV4dEZpbmlzaDogXCJGaW5hbGl6YXIgZWRpY2lcXHhGM24gKGVkaXRvciBkZSB0ZXh0bylcIixcbiAgdGV4dE5ld0xpbmU6IFwiQVxceEYxYWRpciBudWV2YSBsaW5lYSAoZWRpdG9yIGRlIHRleHRvKVwiLFxuICB0aXRsZTogXCJBeXVkYVwiLFxuICB2aWV3OiBcIlZpc3RhXCIsXG4gIHpvb21Ub0ZpdDogXCJBanVzdGFyIGxhIHZpc3RhIHBhcmEgbW9zdHJhciB0b2RvcyBsb3MgZWxlbWVudG9zXCIsXG4gIHpvb21Ub1NlbGVjdGlvbjogXCJBbXBsaWFyIHNlbGVjY2lcXHhGM25cIixcbiAgdG9nZ2xlRWxlbWVudExvY2s6IFwiQmxvcXVlYXIvZGVzYmxvcXVlYXIgc2VsZWNjaVxceEYzblwiLFxuICBtb3ZlUGFnZVVwRG93bjogXCJNb3ZlciBwXFx4RTFnaW5hIGhhY2lhIGFycmliYS9hYmFqb1wiLFxuICBtb3ZlUGFnZUxlZnRSaWdodDogXCJNb3ZlciBwXFx4RTFnaW5hIGhhY2lhIGxhIGl6cXVpZXJkYS9kZXJlY2hhXCJcbn07XG52YXIgY2xlYXJDYW52YXNEaWFsb2cgPSB7XG4gIHRpdGxlOiBcIkJvcnJhciBsaWVuem9cIlxufTtcbnZhciBwdWJsaXNoRGlhbG9nID0ge1xuICB0aXRsZTogXCJQdWJsaWNhciBiaWJsaW90ZWNhXCIsXG4gIGl0ZW1OYW1lOiBcIk5vbWJyZSBkZWwgYXJ0XFx4RURjdWxvXCIsXG4gIGF1dGhvck5hbWU6IFwiTm9tYnJlIGRlbCBhdXRvclwiLFxuICBnaXRodWJVc2VybmFtZTogXCJOb21icmUgZGUgdXN1YXJpbyBkZSBHaXRIdWJcIixcbiAgdHdpdHRlclVzZXJuYW1lOiBcIk5vbWJyZSBkZSB1c3VhcmlvIGRlIFR3aXR0ZXJcIixcbiAgbGlicmFyeU5hbWU6IFwiTm9tYnJlIGRlIGxhIGJpYmxpb3RlY2FcIixcbiAgbGlicmFyeURlc2M6IFwiRGVzY3JpcGNpXFx4RjNuIGRlIGxhIGJpYmxpb3RlY2FcIixcbiAgd2Vic2l0ZTogXCJTaXRpbyBXZWJcIixcbiAgcGxhY2Vob2xkZXI6IHtcbiAgICBhdXRob3JOYW1lOiBcIk5vbWJyZSBvIG5vbWJyZSBkZSB1c3VhcmlvXCIsXG4gICAgbGlicmFyeU5hbWU6IFwiTm9tYnJlIGRlIHR1IGJpYmxpb3RlY2FcIixcbiAgICBsaWJyYXJ5RGVzYzogXCJEZXNjcmlwY2lcXHhGM24gZGUgc3UgYmlibGlvdGVjYSBwYXJhIGF5dWRhciBhIGxhIGdlbnRlIGEgZW50ZW5kZXIgc3UgdXNvXCIsXG4gICAgZ2l0aHViSGFuZGxlOiBcIk5vbWJyZSBkZSB1c3VhcmlvIGRlIEdpdEh1YiAob3BjaW9uYWwpLCBhc1xceEVEIHBvZHJcXHhFMSBlZGl0YXIgbGEgYmlibGlvdGVjYSB1bmEgdmV6IGVudmlhZGEgcGFyYSBzdSByZXZpc2lcXHhGM25cIixcbiAgICB0d2l0dGVySGFuZGxlOiBcIk5vbWJyZSBkZSB1c3VhcmlvIGRlIFR3aXR0ZXIgKG9wY2lvbmFsKSwgYXNcXHhFRCBzYWJlbW9zIGEgcXVpXFx4RTluIGFjcmVkaXRhciBjdWFuZG8gc2UgcHJvbW9jaW9uYSBlbiBUd2l0dGVyXCIsXG4gICAgd2Vic2l0ZTogXCJFbmxhY2UgYSBzdSBzaXRpbyB3ZWIgcGVyc29uYWwgbyBlbiBjdWFscXVpZXIgb3RybyBsdWdhciAob3BjaW9uYWwpXCJcbiAgfSxcbiAgZXJyb3JzOiB7XG4gICAgcmVxdWlyZWQ6IFwiUmVxdWVyaWRvXCIsXG4gICAgd2Vic2l0ZTogXCJJbnRyb2R1Y2UgdW5hIFVSTCB2XFx4RTFsaWRhXCJcbiAgfSxcbiAgbm90ZURlc2NyaXB0aW9uOiBcIkVudlxceEVEYSB0dSBiaWJsaW90ZWNhIHBhcmEgc2VyIGluY2x1aWRhIGVuIGVsIDxsaW5rPnJlcG9zaXRvcmlvIGRlIGxpYnJlclxceEVEYSBwXFx4RkFibGljYTwvbGluaz5wYXJhIHF1ZSBvdHJhcyBwZXJzb25hcyB1dGlsaWNlbiBlbiBzdXMgZGlidWpvcy5cIixcbiAgbm90ZUd1aWRlbGluZXM6IFwiTGEgYmlibGlvdGVjYSBkZWJlIHNlciBhcHJvYmFkYSBtYW51YWxtZW50ZSBwcmltZXJvLiBQb3IgZmF2b3IsIGxlYSBsYSA8bGluaz5wYXV0YXM8L2xpbms+IGFudGVzIGRlIGVudmlhci4gTmVjZXNpdGFyXFx4RTEgdW5hIGN1ZW50YSBkZSBHaXRIdWIgcGFyYSBjb211bmljYXJzZSB5IGhhY2VyIGNhbWJpb3Mgc2kgc2Ugc29saWNpdGEsIHBlcm8gbm8gZXMgZXN0cmljdGFtZW50ZSBuZWNlc2FyaW8uXCIsXG4gIG5vdGVMaWNlbnNlOiBcIkFsIGVudmlhciwgdXN0ZWQgYWNlcHRhIHF1ZSBsYSBiaWJsaW90ZWNhIHNlIHB1YmxpY2FyXFx4RTEgYmFqbyBlbCA8bGluaz5MaWNlbmNpYSBNSVQgPC9saW5rPnF1ZSBlbiBicmV2ZSBzaWduaWZpY2EgcXVlIGN1YWxxdWllcmEgcHVlZGUgdXRpbGl6YXJsb3Mgc2luIHJlc3RyaWNjaW9uZXMuXCIsXG4gIG5vdGVJdGVtczogXCJDYWRhIGVsZW1lbnRvIGRlIGxhIGJpYmxpb3RlY2EgZGViZSB0ZW5lciBzdSBwcm9waW8gbm9tYnJlIHBhcmEgcXVlIHNlYSBmaWx0cmFibGUuIExvcyBzaWd1aWVudGVzIGVsZW1lbnRvcyBkZSBsYSBiaWJsaW90ZWNhIHNlclxceEUxbiBpbmNsdWlkb3M6XCIsXG4gIGF0bGVhc3RPbmVMaWJJdGVtOiBcIlBvciBmYXZvciwgc2VsZWNjaW9uZSBhbCBtZW5vcyB1biBlbGVtZW50byBkZSBsYSBiaWJsaW90ZWNhIHBhcmEgZW1wZXphclwiLFxuICByZXB1Ymxpc2hXYXJuaW5nOiBcIk5vdGE6IGFsZ3Vub3MgZGUgbG9zIGVsZW1lbnRvcyBzZWxlY2Npb25hZG9zIGVzdFxceEUxbiBtYXJjYWRvcyBjb21vIHlhIHB1YmxpY2Fkb3MvZW52aWFkb3MuIFNcXHhGM2xvIGRlYmVyXFx4RURhIHZvbHZlciBhIGVudmlhciBlbGVtZW50b3MgY3VhbmRvIHNlIGFjdHVhbGljZSB1bmEgYmlibGlvdGVjYSBvIGVudlxceEVEby5cIlxufTtcbnZhciBwdWJsaXNoU3VjY2Vzc0RpYWxvZyA9IHtcbiAgdGl0bGU6IFwiQmlibGlvdGVjYSBlbnZpYWRhXCIsXG4gIGNvbnRlbnQ6IFwiR3JhY2lhcyB7e2F1dGhvck5hbWV9fS4gU3UgYmlibGlvdGVjYSBoYSBzaWRvIGVudmlhZGEgcGFyYSBzZXIgcmV2aXNhZGEuIFB1ZWRlIHNlZ3VpciBlbCBlc3RhZG88bGluaz5hcXVcXHhFRDwvbGluaz5cIlxufTtcbnZhciBjb25maXJtRGlhbG9nID0ge1xuICByZXNldExpYnJhcnk6IFwiUmVpbmljaWFyIGJpYmxpb3RlY2FcIixcbiAgcmVtb3ZlSXRlbXNGcm9tTGliOiBcIkVsaW1pbmFyIGVsZW1lbnRvcyBzZWxlY2Npb25hZG9zIGRlIGxhIGJpYmxpb3RlY2FcIlxufTtcbnZhciBpbWFnZUV4cG9ydERpYWxvZyA9IHtcbiAgaGVhZGVyOiBcIkV4cG9ydGFyIGltYWdlblwiLFxuICBsYWJlbDoge1xuICAgIHdpdGhCYWNrZ3JvdW5kOiBcIkZvbmRvXCIsXG4gICAgb25seVNlbGVjdGVkOiBcIlNcXHhGM2xvIHNlbGVjY2lvbmFkb3NcIixcbiAgICBkYXJrTW9kZTogXCJNb2RvIG9zY3Vyb1wiLFxuICAgIGVtYmVkU2NlbmU6IFwiSW5jcnVzdGFyIGVzY2VuYVwiLFxuICAgIHNjYWxlOiBcIkVzY2FsYXJcIixcbiAgICBwYWRkaW5nOiBcIkVzcGFjaWFkb1wiXG4gIH0sXG4gIHRvb2x0aXA6IHtcbiAgICBlbWJlZFNjZW5lOiBcIlwiXG4gIH0sXG4gIHRpdGxlOiB7XG4gICAgZXhwb3J0VG9Qbmc6IFwiRXhwb3J0YXIgYSBQTkdcIixcbiAgICBleHBvcnRUb1N2ZzogXCJFeHBvcnRhciBhIFNWR1wiLFxuICAgIGNvcHlQbmdUb0NsaXBib2FyZDogXCJDb3BpYXIgUE5HIGFsIHBvcnRhcGFwZWxlc1wiXG4gIH0sXG4gIGJ1dHRvbjoge1xuICAgIGV4cG9ydFRvUG5nOiBcIlBOR1wiLFxuICAgIGV4cG9ydFRvU3ZnOiBcIlNWR1wiLFxuICAgIGNvcHlQbmdUb0NsaXBib2FyZDogXCJDb3BpYXIgYWwgcG9ydGFwYXBlbGVzXCJcbiAgfVxufTtcbnZhciBlbmNyeXB0ZWQgPSB7XG4gIHRvb2x0aXA6IFwiVHVzIGRpYnVqb3MgZXN0XFx4RTFuIGNpZnJhZG9zIGRlIHB1bnRvIGEgcHVudG8sIHBvciBsbyBxdWUgbG9zIHNlcnZpZG9yZXMgZGUgRXhjYWxpZHJhdyBudW5jYSBsb3MgdmVyXFx4RTFuLlwiLFxuICBsaW5rOiBcIkVudHJhZGEgZW4gZWwgYmxvZyBzb2JyZSBjaWZyYWRvIGRlIGV4dHJlbW8gYSBleHRyZW1vXCJcbn07XG52YXIgc3RhdHMgPSB7XG4gIGFuZ2xlOiBcIlxceEMxbmd1bG9cIixcbiAgZWxlbWVudDogXCJFbGVtZW50b1wiLFxuICBlbGVtZW50czogXCJFbGVtZW50b3NcIixcbiAgaGVpZ2h0OiBcIkFsdG9cIixcbiAgc2NlbmU6IFwiRXNjZW5hXCIsXG4gIHNlbGVjdGVkOiBcIlNlbGVjY2lvbmFkb1wiLFxuICBzdG9yYWdlOiBcIkFsbWFjZW5hbWllbnRvXCIsXG4gIHRpdGxlOiBcIkVzdGFkXFx4RURzdGljYXMgcGFyYSBuZXJkc1wiLFxuICB0b3RhbDogXCJUb3RhbFwiLFxuICB2ZXJzaW9uOiBcIlZlcnNpXFx4RjNuXCIsXG4gIHZlcnNpb25Db3B5OiBcIkNsaWNrIHBhcmEgY29waWFyXCIsXG4gIHZlcnNpb25Ob3RBdmFpbGFibGU6IFwiVmVyc2lcXHhGM24gbm8gZGlzcG9uaWJsZVwiLFxuICB3aWR0aDogXCJBbmNob1wiXG59O1xudmFyIHRvYXN0ID0ge1xuICBhZGRlZFRvTGlicmFyeTogXCJBXFx4RjFhZGlkbyBhIGxhIGJpYmxpb3RlY2FcIixcbiAgY29weVN0eWxlczogXCJFc3RpbG9zIGNvcGlhZG9zLlwiLFxuICBjb3B5VG9DbGlwYm9hcmQ6IFwiQ29waWFkbyBlbiBlbCBwb3J0YXBhcGVsZXMuXCIsXG4gIGNvcHlUb0NsaXBib2FyZEFzUG5nOiBcIkNvcGlhZG8ge3tleHBvcnRTZWxlY3Rpb259fSBhbCBwb3J0YXBhcGVsZXMgY29tbyBQTkdcXG4oe3tleHBvcnRDb2xvclNjaGVtZX19KVwiLFxuICBmaWxlU2F2ZWQ6IFwiQXJjaGl2byBndWFyZGFkby5cIixcbiAgZmlsZVNhdmVkVG9GaWxlbmFtZTogXCJHdWFyZGFkbyBlbiB7ZmlsZW5hbWV9XCIsXG4gIGNhbnZhczogXCJsaWVuem9cIixcbiAgc2VsZWN0aW9uOiBcInNlbGVjY2lcXHhGM25cIixcbiAgcGFzdGVBc1NpbmdsZUVsZW1lbnQ6IFwiVXNhIHt7c2hvcnRjdXR9fSBwYXJhIHBlZ2FyIGNvbW8gdW4gc29sbyBlbGVtZW50byxcXG5vIHBlZ2FyIGVuIHVuIGVkaXRvciBkZSB0ZXh0byBleGlzdGVudGVcIixcbiAgdW5hYmxlVG9FbWJlZDogXCJcIixcbiAgdW5yZWNvZ25pemVkTGlua0Zvcm1hdDogXCJcIlxufTtcbnZhciBjb2xvcnMgPSB7XG4gIHRyYW5zcGFyZW50OiBcIlRyYW5zcGFyZW50ZVwiLFxuICBibGFjazogXCJOZWdyb1wiLFxuICB3aGl0ZTogXCJCbGFuY29cIixcbiAgcmVkOiBcIlJvam9cIixcbiAgcGluazogXCJSb3NhXCIsXG4gIGdyYXBlOiBcIlV2YVwiLFxuICB2aW9sZXQ6IFwiVmlvbGV0YVwiLFxuICBncmF5OiBcIkdyaXNcIixcbiAgYmx1ZTogXCJBenVsXCIsXG4gIGN5YW46IFwiQ2lhblwiLFxuICB0ZWFsOiBcIlR1cnF1ZXNhXCIsXG4gIGdyZWVuOiBcIlZlcmRlXCIsXG4gIHllbGxvdzogXCJBbWFyaWxsb1wiLFxuICBvcmFuZ2U6IFwiTmFyYW5qYVwiLFxuICBicm9uemU6IFwiQnJvbmNlXCJcbn07XG52YXIgd2VsY29tZVNjcmVlbiA9IHtcbiAgYXBwOiB7XG4gICAgY2VudGVyX2hlYWRpbmc6IFwiVG9kYSBzdSBpbmZvcm1hY2lcXHhGM24gZXMgZ3VhcmRhZGEgbG9jYWxtZW50ZSBlbiBzdSBuYXZlZ2Fkb3IuXCIsXG4gICAgY2VudGVyX2hlYWRpbmdfcGx1czogXCJcXHhCRlF1aWVyZXMgaXIgYSBFeGNhbGlkcmF3Kz9cIixcbiAgICBtZW51SGludDogXCJFeHBvcnRhciwgcHJlZmVyZW5jaWFzLCBpZGlvbWFzLCAuLi5cIlxuICB9LFxuICBkZWZhdWx0czoge1xuICAgIG1lbnVIaW50OiBcIkV4cG9ydGFyLCBwcmVmZXJlbmNpYXMgeSBtXFx4RTFzLi4uXCIsXG4gICAgY2VudGVyX2hlYWRpbmc6IFwiRGlhZ3JhbWFzLiBIZWNoby4gU2ltcGxlbWVudGUuXCIsXG4gICAgdG9vbGJhckhpbnQ6IFwiXFx4QTFFbGlnZSB1bmEgaGVycmFtaWVudGEgeSBlbXBpZXphIGEgZGlidWphciFcIixcbiAgICBoZWxwSGludDogXCJBdGFqb3MgeSBheXVkYVwiXG4gIH1cbn07XG52YXIgY29sb3JQaWNrZXIgPSB7XG4gIG1vc3RVc2VkQ3VzdG9tQ29sb3JzOiBcIkNvbG9yZXMgcGVyc29uYWxpemFkb3MgbVxceEUxcyB1dGlsaXphZG9zXCIsXG4gIGNvbG9yczogXCJDb2xvcmVzXCIsXG4gIHNoYWRlczogXCJcIixcbiAgaGV4Q29kZTogXCJDXFx4RjNkaWdvIEhleGFkZWNpbWFsXCIsXG4gIG5vU2hhZGVzOiBcIlwiXG59O1xudmFyIG92ZXJ3cml0ZUNvbmZpcm0gPSB7XG4gIGFjdGlvbjoge1xuICAgIGV4cG9ydFRvSW1hZ2U6IHtcbiAgICAgIHRpdGxlOiBcIkV4cG9ydGFyIGNvbW8gaW1hZ2VuXCIsXG4gICAgICBidXR0b246IFwiRXhwb3J0YXIgY29tbyBpbWFnZW5cIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfSxcbiAgICBzYXZlVG9EaXNrOiB7XG4gICAgICB0aXRsZTogXCJHdWFyZGFyIGVuIGVsIGRpc2NvXCIsXG4gICAgICBidXR0b246IFwiR3VhcmRhciBlbiBlbCBkaXNjb1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwiRXhwb3J0YSBsb3MgZGF0b3MgZGUgbGEgZXNjZW5hIGEgdW4gYXJjaGl2byBkZXNkZSBlbCBjdWFsIHBvZHJcXHhFMXMgaW1wb3J0YXIgbVxceEUxcyB0YXJkZS5cIlxuICAgIH0sXG4gICAgZXhjYWxpZHJhd1BsdXM6IHtcbiAgICAgIHRpdGxlOiBcIlwiLFxuICAgICAgYnV0dG9uOiBcIkV4cG9ydGFyIGEgRXhjYWxpZHJhdytcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiXG4gICAgfVxuICB9LFxuICBtb2RhbDoge1xuICAgIGxvYWRGcm9tRmlsZToge1xuICAgICAgdGl0bGU6IFwiQ2FyZ2FyIGRlc2RlIHVuIGFyY2hpdm9cIixcbiAgICAgIGJ1dHRvbjogXCJDYXJnYXIgZGVzZGUgdW4gYXJjaGl2b1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwiXCJcbiAgICB9LFxuICAgIHNoYXJlYWJsZUxpbms6IHtcbiAgICAgIHRpdGxlOiBcIkNhcmdhciBkZXNkZSB1biBlbmxhY2VcIixcbiAgICAgIGJ1dHRvbjogXCJSZWVtcGxhemFyIG1pIGNvbnRlbmlkb1wiLFxuICAgICAgZGVzY3JpcHRpb246IFwiQ2FyZ2FyIHVuIGRpYnVqbyBleHRlcm5vIDxib2xkPnJlZW1wbGF6YXJcXHhFMSB0dSBjb250ZW5pZG8gZXhpc3RlbnRlPC9ib2xkPi48YnI+PC9icj5QdWVkZXMgcHJpbWVybyBoYWNlciB1bmEgY29waWEgZGUgc2VndXJpZGFkIGRlIHR1IGRpYnVqbyB1c2FuZG8gdW5hIGRlIGxhcyBvcGNpb25lcyBkZSBhYmFqby5cIlxuICAgIH1cbiAgfVxufTtcbnZhciBtZXJtYWlkID0ge1xuICB0aXRsZTogXCJNZXJtYWlkIGEgRXhjYWxpZHJhd1wiLFxuICBidXR0b246IFwiSW5zZXJ0YXJcIixcbiAgZGVzY3JpcHRpb246IFwiQWN0dWFsbWVudGUgc1xceEYzbG8gPGZsb3djaGFydExpbms+Rmxvd2NoYXJ0PC9mbG93Y2hhcnRMaW5rPiw8c2VxdWVuY2VMaW5rPiBTZWN1ZW5jaWEsIDwvc2VxdWVuY2VMaW5rPiB5IDxjbGFzc0xpbms+Q2xhc3MgPC9jbGFzc0xpbms+RGlhZ3JhbWFzIHNvbiBzb3BvcnRhZG9zLiBMb3Mgb3Ryb3MgdGlwb3Mgc2UgcmVuZGVyaXphclxceEUxbiBjb21vIGltYWdlbiBlbiBFeGNhbGlkcmF3LlwiLFxuICBzeW50YXg6IFwiU2ludGF4aXMgTWVybWFpZFwiLFxuICBwcmV2aWV3OiBcIlZpc3RhIHByZXZpYVwiXG59O1xudmFyIGVzX0VTX2RlZmF1bHQgPSB7XG4gIGxhYmVscyxcbiAgbGlicmFyeSxcbiAgYnV0dG9ucyxcbiAgYWxlcnRzLFxuICBlcnJvcnMsXG4gIHRvb2xCYXIsXG4gIGhlYWRpbmdzLFxuICBoaW50cyxcbiAgY2FudmFzRXJyb3IsXG4gIGVycm9yU3BsYXNoLFxuICByb29tRGlhbG9nLFxuICBlcnJvckRpYWxvZyxcbiAgZXhwb3J0RGlhbG9nLFxuICBoZWxwRGlhbG9nLFxuICBjbGVhckNhbnZhc0RpYWxvZyxcbiAgcHVibGlzaERpYWxvZyxcbiAgcHVibGlzaFN1Y2Nlc3NEaWFsb2csXG4gIGNvbmZpcm1EaWFsb2csXG4gIGltYWdlRXhwb3J0RGlhbG9nLFxuICBlbmNyeXB0ZWQsXG4gIHN0YXRzLFxuICB0b2FzdCxcbiAgY29sb3JzLFxuICB3ZWxjb21lU2NyZWVuLFxuICBjb2xvclBpY2tlcixcbiAgb3ZlcndyaXRlQ29uZmlybSxcbiAgbWVybWFpZFxufTtcbmV4cG9ydCB7XG4gIGFsZXJ0cyxcbiAgYnV0dG9ucyxcbiAgY2FudmFzRXJyb3IsXG4gIGNsZWFyQ2FudmFzRGlhbG9nLFxuICBjb2xvclBpY2tlcixcbiAgY29sb3JzLFxuICBjb25maXJtRGlhbG9nLFxuICBlc19FU19kZWZhdWx0IGFzIGRlZmF1bHQsXG4gIGVuY3J5cHRlZCxcbiAgZXJyb3JEaWFsb2csXG4gIGVycm9yU3BsYXNoLFxuICBlcnJvcnMsXG4gIGV4cG9ydERpYWxvZyxcbiAgaGVhZGluZ3MsXG4gIGhlbHBEaWFsb2csXG4gIGhpbnRzLFxuICBpbWFnZUV4cG9ydERpYWxvZyxcbiAgbGFiZWxzLFxuICBsaWJyYXJ5LFxuICBtZXJtYWlkLFxuICBvdmVyd3JpdGVDb25maXJtLFxuICBwdWJsaXNoRGlhbG9nLFxuICBwdWJsaXNoU3VjY2Vzc0RpYWxvZyxcbiAgcm9vbURpYWxvZyxcbiAgc3RhdHMsXG4gIHRvYXN0LFxuICB0b29sQmFyLFxuICB3ZWxjb21lU2NyZWVuXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZXMtRVMtQVFZVlhDMzIuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@excalidraw/excalidraw/dist/dev/locales/es-ES-AQYVXC32.js\n"));

/***/ })

}]);