/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./app/globals.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  background: #f8fafc;
  min-height: 100vh;
  overflow: hidden;
}

/* Architecture Workspace Layout */
.architecture-workspace {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.workspace-header {
  background: linear-gradient(135deg, #232F3E 0%, #131A22 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.aws-logo {
  background: #FF9900;
  color: #232F3E;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-weight: bold;
  font-size: 1rem;
}

.header-content p {
  opacity: 0.9;
  font-size: 0.9rem;
}

.new-session-btn {
  background: #FF9900;
  color: #232F3E;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-session-btn:hover {
  background: #ffb84d;
  transform: translateY(-1px);
}

.workspace-content {
  flex: 1 1;
  display: flex;
  height: calc(100vh - 80px);
}

/* Chat Panel */
.chat-panel {
  width: 40%;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.chat-header h2 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #1e293b;
}

.chat-header p {
  color: #64748b;
  font-size: 0.9rem;
}

.chat-messages {
  flex: 1 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.welcome-message {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.welcome-message h3 {
  color: #1e293b;
  margin-bottom: 1rem;
}

.welcome-message ul {
  text-align: left;
  max-width: 300px;
  margin: 1rem auto;
}

.welcome-message li {
  margin-bottom: 0.5rem;
  font-style: italic;
}

/* Chat Messages */
.chat-message {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.chat-message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.chat-message.user .message-avatar {
  background: #3b82f6;
}

.chat-message.assistant .message-avatar {
  background: #10b981;
}

.message-content {
  flex: 1 1;
  max-width: 80%;
}

.chat-message.user .message-content {
  text-align: right;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
  color: #64748b;
}

.chat-message.user .message-header {
  justify-content: flex-end;
}

.message-role {
  font-weight: 600;
}

.message-text {
  background: #f1f5f9;
  padding: 0.75rem 1rem;
  border-radius: 1rem;
  line-height: 1.5;
}

.chat-message.user .message-text {
  background: #3b82f6;
  color: white;
}

.message-diagram-indicator {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #ecfdf5;
  color: #059669;
  border-radius: 0.5rem;
  font-size: 0.8rem;
  text-align: center;
}

/* Loading Message */
.loading-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 1rem;
  color: #64748b;
}

.typing-indicator {
  display: flex;
  gap: 0.25rem;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #94a3b8;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Chat Input */
.chat-input {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background: white;
}

.input-container {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.message-input {
  flex: 1 1;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  padding: 0.75rem;
  font-family: inherit;
  font-size: 0.9rem;
  resize: none;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.message-input:disabled {
  background: #f9fafb;
  color: #9ca3af;
}

.send-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.2rem;
  min-width: 48px;
  height: 48px;
}

.send-button:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  transform: none;
}

.input-hints {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #9ca3af;
}

@media (max-width: 768px) {
  .input-hints {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/* Diagram Panel */
.diagram-panel {
  flex: 1 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.excalidraw-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.excalidraw-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.excalidraw-header h2 {
  font-size: 1.25rem;
  color: #1e293b;
}

.excalidraw-controls {
  display: flex;
  gap: 0.5rem;
}

.control-button {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #d1d5db;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
}

.control-button:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.excalidraw-wrapper {
  flex: 1 1;
  position: relative;
}

.excalidraw-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

.placeholder-content {
  text-align: center;
  color: #64748b;
}

.placeholder-content h3 {
  color: #1e293b;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.placeholder-content p {
  margin-bottom: 2rem;
}

.placeholder-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 200px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
}

.feature span:first-child {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .workspace-content {
    flex-direction: column;
  }

  .chat-panel {
    width: 100%;
    height: 50%;
  }

  .diagram-panel {
    height: 50%;
  }
}

@media (max-width: 768px) {
  .workspace-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .chat-messages {
    padding: 0.75rem;
  }

  .chat-message {
    gap: 0.5rem;
  }

  .message-content {
    max-width: 85%;
  }

  .excalidraw-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .excalidraw-controls {
    justify-content: center;
  }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation for new messages */
.chat-message {
  animation: slideInMessage 0.3s ease-out;
}

@keyframes slideInMessage {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus states for accessibility */
.send-button:focus,
.message-input:focus,
.control-button:focus,
.new-session-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

