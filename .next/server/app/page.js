/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/Users/<USER>/workspace/system-design-llm/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/workspace/system-design-llm/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp%2Fglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp%2Fglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FDiagramGenerator.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FDiagramGenerator.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/DiagramGenerator.tsx */ \"(ssr)/./components/DiagramGenerator.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZqdXN0aW5tZW5lc3RyaW5hJTJGd29ya3NwYWNlJTJGc3lzdGVtLWRlc2lnbi1sbG0lMkZjb21wb25lbnRzJTJGRGlhZ3JhbUdlbmVyYXRvci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXdzLWFyY2hpdGVjdHVyZS1kaWFncmFtLWdlbmVyYXRvci8/OGU0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9jb21wb25lbnRzL0RpYWdyYW1HZW5lcmF0b3IudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FDiagramGenerator.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/DiagramGenerator.tsx":
/*!*****************************************!*\
  !*** ./components/DiagramGenerator.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DiagramGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingSpinner */ \"(ssr)/./components/LoadingSpinner.tsx\");\n/* harmony import */ var _ErrorMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ErrorMessage */ \"(ssr)/./components/ErrorMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DiagramGenerator() {\n    const [description, setDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSubmit = async ()=>{\n        if (!description.trim()) {\n            setError(\"Please enter an architecture description\");\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        setResult(null);\n        try {\n            const data = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.generateDiagram)({\n                description: description.trim(),\n                style: \"standard\"\n            });\n            setResult(data);\n        } catch (err) {\n            setError(`Error: ${err instanceof Error ? err.message : \"Unknown error\"}. Make sure the backend server is running.`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && e.ctrlKey) {\n            handleSubmit();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"content\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"input-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        htmlFor: \"description\",\n                        children: \"Describe your AWS architecture:\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        id: \"description\",\n                        value: description,\n                        onChange: (e)=>setDescription(e.target.value),\n                        onKeyDown: handleKeyDown,\n                        placeholder: \"Example: I need a scalable web application with a load balancer, auto-scaling EC2 instances, RDS database, and S3 for static content...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"examples\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Try these examples:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginLeft: \"20px\",\n                                    marginTop: \"5px\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A serverless API with Lambda, API Gateway, and DynamoDB\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A three-tier web application with load balancer, EC2 instances, and RDS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A data processing pipeline with Kinesis, Lambda, and S3\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"A microservices architecture with ECS, ALB, and multiple databases\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleSubmit,\n                disabled: isLoading,\n                children: \"Generate Architecture Diagram\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 70,\n                columnNumber: 21\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ErrorMessage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                message: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 71,\n                columnNumber: 17\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"result\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"Generated Architecture\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"diagram-container\",\n                        children: result.diagram_path.endsWith(\".excalidraw\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"excalidraw-preview\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"excalidraw-info\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"\\uD83C\\uDFA8 Excalidraw Diagram Generated!\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Your architecture diagram has been created in Excalidraw format.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"excalidraw-actions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getDiagramUrl)(result.diagram_path),\n                                                download: true,\n                                                className: \"download-btn\",\n                                                children: \"\\uD83D\\uDCE5 Download .excalidraw file\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://excalidraw.com\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"open-btn\",\n                                                children: \"\\uD83C\\uDF10 Open in Excalidraw\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"instructions\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"How to view your diagram:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Download the .excalidraw file above\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            \"Go to \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"https://excalidraw.com\",\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                children: \"excalidraw.com\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: 'Click \"Open\" and select your downloaded file'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Edit, export, or share as needed!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getDiagramUrl)(result.diagram_path),\n                            alt: \"AWS Architecture Diagram\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"components-info\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Architecture Components\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: result.explanation\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"component-list\",\n                                children: result.components?.components?.map((comp, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"component-tag\",\n                                        children: [\n                                            comp.name,\n                                            \" (\",\n                                            comp.type.toUpperCase(),\n                                            \")\"\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0RpYWdyYW1HZW5lcmF0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVpQztBQUVnRDtBQUNuQztBQUNKO0FBRTNCLFNBQVNLO0lBQ3RCLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNRLFdBQVdDLGFBQWEsR0FBR1QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDVSxPQUFPQyxTQUFTLEdBQUdYLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ1ksUUFBUUMsVUFBVSxHQUFHYiwrQ0FBUUEsQ0FBeUI7SUFFN0QsTUFBTWMsZUFBZTtRQUNuQixJQUFJLENBQUNSLFlBQVlTLElBQUksSUFBSTtZQUN2QkosU0FBUztZQUNUO1FBQ0Y7UUFFQUYsYUFBYTtRQUNiRSxTQUFTO1FBQ1RFLFVBQVU7UUFFVixJQUFJO1lBQ0YsTUFBTUcsT0FBTyxNQUFNZix5REFBZUEsQ0FBQztnQkFDakNLLGFBQWFBLFlBQVlTLElBQUk7Z0JBQzdCRSxPQUFPO1lBQ1Q7WUFDQUosVUFBVUc7UUFDWixFQUFFLE9BQU9FLEtBQUs7WUFDWlAsU0FBUyxDQUFDLE9BQU8sRUFBRU8sZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHLGdCQUFnQiwwQ0FBMEMsQ0FBQztRQUNySCxTQUFVO1lBQ1JYLGFBQWE7UUFDZjtJQUNGO0lBRUEsTUFBTVksZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXRCxFQUFFRSxPQUFPLEVBQUU7WUFDbENWO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDVztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBTUMsU0FBUTtrQ0FBYzs7Ozs7O2tDQUM3Qiw4REFBQ0M7d0JBQ0NDLElBQUc7d0JBQ0hDLE9BQU96Qjt3QkFDUDBCLFVBQVUsQ0FBQ1YsSUFBTWYsZUFBZWUsRUFBRVcsTUFBTSxDQUFDRixLQUFLO3dCQUM5Q0csV0FBV2I7d0JBQ1hjLGFBQVk7Ozs7OztrQ0FFZCw4REFBQ1Y7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDVTswQ0FBTzs7Ozs7OzBDQUNSLDhEQUFDQztnQ0FBR3BCLE9BQU87b0NBQUVxQixZQUFZO29DQUFRQyxXQUFXO2dDQUFNOztrREFDaEQsOERBQUNDO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNBO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNBO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNBO2tEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS1YsOERBQUNDO2dCQUFPQyxTQUFTNUI7Z0JBQWM2QixVQUFVbkM7MEJBQVc7Ozs7OztZQUluREEsMkJBQWEsOERBQUNMLHVEQUFjQTs7Ozs7WUFDNUJPLHVCQUFTLDhEQUFDTixxREFBWUE7Z0JBQUNnQixTQUFTVjs7Ozs7O1lBRWhDRSx3QkFDQyw4REFBQ2E7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDa0I7a0NBQUc7Ozs7OztrQ0FDSiw4REFBQ25CO3dCQUFJQyxXQUFVO2tDQUNaZCxPQUFPaUMsWUFBWSxDQUFDQyxRQUFRLENBQUMsK0JBQzVCLDhEQUFDckI7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3FCO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNDO2tEQUFFOzs7Ozs7a0RBQ0gsOERBQUN2Qjt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN1QjtnREFDQ0MsTUFBTWhELHVEQUFhQSxDQUFDVSxPQUFPaUMsWUFBWTtnREFDdkNNLFFBQVE7Z0RBQ1J6QixXQUFVOzBEQUNYOzs7Ozs7MERBR0QsOERBQUN1QjtnREFDQ0MsTUFBSztnREFDTGpCLFFBQU87Z0RBQ1BtQixLQUFJO2dEQUNKMUIsV0FBVTswREFDWDs7Ozs7Ozs7Ozs7O2tEQUlILDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMyQjswREFBRzs7Ozs7OzBEQUNKLDhEQUFDQzs7a0VBQ0MsOERBQUNkO2tFQUFHOzs7Ozs7a0VBQ0osOERBQUNBOzs0REFBRzswRUFBTSw4REFBQ1M7Z0VBQUVDLE1BQUs7Z0VBQXlCakIsUUFBTztnRUFBU21CLEtBQUk7MEVBQXNCOzs7Ozs7Ozs7Ozs7a0VBQ3JGLDhEQUFDWjtrRUFBRzs7Ozs7O2tFQUNKLDhEQUFDQTtrRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztpREFNWiw4REFBQ2U7NEJBQ0NDLEtBQUt0RCx1REFBYUEsQ0FBQ1UsT0FBT2lDLFlBQVk7NEJBQ3RDWSxLQUFJOzs7Ozs7Ozs7OztrQ0FJViw4REFBQ2hDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3FCOzBDQUFHOzs7Ozs7MENBQ0osOERBQUNDOzBDQUFHcEMsT0FBTzhDLFdBQVc7Ozs7OzswQ0FDdEIsOERBQUNqQztnQ0FBSUMsV0FBVTswQ0FDWmQsT0FBTytDLFVBQVUsRUFBRUEsWUFBWUMsSUFBSSxDQUFDQyxNQUFNQyxzQkFDekMsOERBQUNyQzt3Q0FBZ0JDLFdBQVU7OzRDQUN4Qm1DLEtBQUtFLElBQUk7NENBQUM7NENBQUdGLEtBQUtHLElBQUksQ0FBQ0MsV0FBVzs0Q0FBRzs7dUNBRDlCSDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVUxQiIsInNvdXJjZXMiOlsid2VicGFjazovL2F3cy1hcmNoaXRlY3R1cmUtZGlhZ3JhbS1nZW5lcmF0b3IvLi9jb21wb25lbnRzL0RpYWdyYW1HZW5lcmF0b3IudHN4PzZjNTUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IERpYWdyYW1SZXNwb25zZSB9IGZyb20gJ0AvdHlwZXMnO1xuaW1wb3J0IHsgZ2VuZXJhdGVEaWFncmFtLCBnZXREaWFncmFtVXJsLCBnZXRFeGNhbGlkcmF3Vmlld1VybCB9IGZyb20gJ0AvbGliL2FwaSc7XG5pbXBvcnQgTG9hZGluZ1NwaW5uZXIgZnJvbSAnLi9Mb2FkaW5nU3Bpbm5lcic7XG5pbXBvcnQgRXJyb3JNZXNzYWdlIGZyb20gJy4vRXJyb3JNZXNzYWdlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGlhZ3JhbUdlbmVyYXRvcigpIHtcbiAgY29uc3QgW2Rlc2NyaXB0aW9uLCBzZXREZXNjcmlwdGlvbl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbcmVzdWx0LCBzZXRSZXN1bHRdID0gdXNlU3RhdGU8RGlhZ3JhbVJlc3BvbnNlIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghZGVzY3JpcHRpb24udHJpbSgpKSB7XG4gICAgICBzZXRFcnJvcignUGxlYXNlIGVudGVyIGFuIGFyY2hpdGVjdHVyZSBkZXNjcmlwdGlvbicpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcignJyk7XG4gICAgc2V0UmVzdWx0KG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBnZW5lcmF0ZURpYWdyYW0oe1xuICAgICAgICBkZXNjcmlwdGlvbjogZGVzY3JpcHRpb24udHJpbSgpLFxuICAgICAgICBzdHlsZTogJ3N0YW5kYXJkJ1xuICAgICAgfSk7XG4gICAgICBzZXRSZXN1bHQoZGF0YSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihgRXJyb3I6ICR7ZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ30uIE1ha2Ugc3VyZSB0aGUgYmFja2VuZCBzZXJ2ZXIgaXMgcnVubmluZy5gKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBSZWFjdC5LZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmIGUuY3RybEtleSkge1xuICAgICAgaGFuZGxlU3VibWl0KCk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJjb250ZW50XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImlucHV0LXNlY3Rpb25cIj5cbiAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJkZXNjcmlwdGlvblwiPkRlc2NyaWJlIHlvdXIgQVdTIGFyY2hpdGVjdHVyZTo8L2xhYmVsPlxuICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICBpZD1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICB2YWx1ZT17ZGVzY3JpcHRpb259XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXREZXNjcmlwdGlvbihlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRXhhbXBsZTogSSBuZWVkIGEgc2NhbGFibGUgd2ViIGFwcGxpY2F0aW9uIHdpdGggYSBsb2FkIGJhbGFuY2VyLCBhdXRvLXNjYWxpbmcgRUMyIGluc3RhbmNlcywgUkRTIGRhdGFiYXNlLCBhbmQgUzMgZm9yIHN0YXRpYyBjb250ZW50Li4uXCJcbiAgICAgICAgLz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJleGFtcGxlc1wiPlxuICAgICAgICAgIDxzdHJvbmc+VHJ5IHRoZXNlIGV4YW1wbGVzOjwvc3Ryb25nPlxuICAgICAgICAgIDx1bCBzdHlsZT17eyBtYXJnaW5MZWZ0OiAnMjBweCcsIG1hcmdpblRvcDogJzVweCcgfX0+XG4gICAgICAgICAgICA8bGk+QSBzZXJ2ZXJsZXNzIEFQSSB3aXRoIExhbWJkYSwgQVBJIEdhdGV3YXksIGFuZCBEeW5hbW9EQjwvbGk+XG4gICAgICAgICAgICA8bGk+QSB0aHJlZS10aWVyIHdlYiBhcHBsaWNhdGlvbiB3aXRoIGxvYWQgYmFsYW5jZXIsIEVDMiBpbnN0YW5jZXMsIGFuZCBSRFM8L2xpPlxuICAgICAgICAgICAgPGxpPkEgZGF0YSBwcm9jZXNzaW5nIHBpcGVsaW5lIHdpdGggS2luZXNpcywgTGFtYmRhLCBhbmQgUzM8L2xpPlxuICAgICAgICAgICAgPGxpPkEgbWljcm9zZXJ2aWNlcyBhcmNoaXRlY3R1cmUgd2l0aCBFQ1MsIEFMQiwgYW5kIG11bHRpcGxlIGRhdGFiYXNlczwvbGk+XG4gICAgICAgICAgPC91bD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTdWJtaXR9IGRpc2FibGVkPXtpc0xvYWRpbmd9PlxuICAgICAgICBHZW5lcmF0ZSBBcmNoaXRlY3R1cmUgRGlhZ3JhbVxuICAgICAgPC9idXR0b24+XG5cbiAgICAgIHtpc0xvYWRpbmcgJiYgPExvYWRpbmdTcGlubmVyIC8+fVxuICAgICAge2Vycm9yICYmIDxFcnJvck1lc3NhZ2UgbWVzc2FnZT17ZXJyb3J9IC8+fVxuXG4gICAgICB7cmVzdWx0ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZXN1bHRcIj5cbiAgICAgICAgICA8aDI+R2VuZXJhdGVkIEFyY2hpdGVjdHVyZTwvaDI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkaWFncmFtLWNvbnRhaW5lclwiPlxuICAgICAgICAgICAge3Jlc3VsdC5kaWFncmFtX3BhdGguZW5kc1dpdGgoJy5leGNhbGlkcmF3JykgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZXhjYWxpZHJhdy1wcmV2aWV3XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJleGNhbGlkcmF3LWluZm9cIj5cbiAgICAgICAgICAgICAgICAgIDxoMz7wn46oIEV4Y2FsaWRyYXcgRGlhZ3JhbSBHZW5lcmF0ZWQhPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwPllvdXIgYXJjaGl0ZWN0dXJlIGRpYWdyYW0gaGFzIGJlZW4gY3JlYXRlZCBpbiBFeGNhbGlkcmF3IGZvcm1hdC48L3A+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImV4Y2FsaWRyYXctYWN0aW9uc1wiPlxuICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgIGhyZWY9e2dldERpYWdyYW1VcmwocmVzdWx0LmRpYWdyYW1fcGF0aCl9XG4gICAgICAgICAgICAgICAgICAgICAgZG93bmxvYWRcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJkb3dubG9hZC1idG5cIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg8J+TpSBEb3dubG9hZCAuZXhjYWxpZHJhdyBmaWxlXG4gICAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiaHR0cHM6Ly9leGNhbGlkcmF3LmNvbVwiXG4gICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvcGVuLWJ0blwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDwn4yQIE9wZW4gaW4gRXhjYWxpZHJhd1xuICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaW5zdHJ1Y3Rpb25zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoND5Ib3cgdG8gdmlldyB5b3VyIGRpYWdyYW06PC9oND5cbiAgICAgICAgICAgICAgICAgICAgPG9sPlxuICAgICAgICAgICAgICAgICAgICAgIDxsaT5Eb3dubG9hZCB0aGUgLmV4Y2FsaWRyYXcgZmlsZSBhYm92ZTwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgPGxpPkdvIHRvIDxhIGhyZWY9XCJodHRwczovL2V4Y2FsaWRyYXcuY29tXCIgdGFyZ2V0PVwiX2JsYW5rXCIgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiPmV4Y2FsaWRyYXcuY29tPC9hPjwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgPGxpPkNsaWNrIFwiT3BlblwiIGFuZCBzZWxlY3QgeW91ciBkb3dubG9hZGVkIGZpbGU8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgIDxsaT5FZGl0LCBleHBvcnQsIG9yIHNoYXJlIGFzIG5lZWRlZCE8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8L29sPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgIHNyYz17Z2V0RGlhZ3JhbVVybChyZXN1bHQuZGlhZ3JhbV9wYXRoKX1cbiAgICAgICAgICAgICAgICBhbHQ9XCJBV1MgQXJjaGl0ZWN0dXJlIERpYWdyYW1cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbXBvbmVudHMtaW5mb1wiPlxuICAgICAgICAgICAgPGgzPkFyY2hpdGVjdHVyZSBDb21wb25lbnRzPC9oMz5cbiAgICAgICAgICAgIDxwPntyZXN1bHQuZXhwbGFuYXRpb259PC9wPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb21wb25lbnQtbGlzdFwiPlxuICAgICAgICAgICAgICB7cmVzdWx0LmNvbXBvbmVudHM/LmNvbXBvbmVudHM/Lm1hcCgoY29tcCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImNvbXBvbmVudC10YWdcIj5cbiAgICAgICAgICAgICAgICAgIHtjb21wLm5hbWV9ICh7Y29tcC50eXBlLnRvVXBwZXJDYXNlKCl9KVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJnZW5lcmF0ZURpYWdyYW0iLCJnZXREaWFncmFtVXJsIiwiTG9hZGluZ1NwaW5uZXIiLCJFcnJvck1lc3NhZ2UiLCJEaWFncmFtR2VuZXJhdG9yIiwiZGVzY3JpcHRpb24iLCJzZXREZXNjcmlwdGlvbiIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJyZXN1bHQiLCJzZXRSZXN1bHQiLCJoYW5kbGVTdWJtaXQiLCJ0cmltIiwiZGF0YSIsInN0eWxlIiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlS2V5RG93biIsImUiLCJrZXkiLCJjdHJsS2V5IiwiZGl2IiwiY2xhc3NOYW1lIiwibGFiZWwiLCJodG1sRm9yIiwidGV4dGFyZWEiLCJpZCIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJvbktleURvd24iLCJwbGFjZWhvbGRlciIsInN0cm9uZyIsInVsIiwibWFyZ2luTGVmdCIsIm1hcmdpblRvcCIsImxpIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiaDIiLCJkaWFncmFtX3BhdGgiLCJlbmRzV2l0aCIsImgzIiwicCIsImEiLCJocmVmIiwiZG93bmxvYWQiLCJyZWwiLCJoNCIsIm9sIiwiaW1nIiwic3JjIiwiYWx0IiwiZXhwbGFuYXRpb24iLCJjb21wb25lbnRzIiwibWFwIiwiY29tcCIsImluZGV4IiwibmFtZSIsInR5cGUiLCJ0b1VwcGVyQ2FzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/DiagramGenerator.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ErrorMessage.tsx":
/*!*************************************!*\
  !*** ./components/ErrorMessage.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ErrorMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction ErrorMessage({ message }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"error\",\n        children: message\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ErrorMessage.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0Vycm9yTWVzc2FnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUllLFNBQVNBLGFBQWEsRUFBRUMsT0FBTyxFQUFxQjtJQUNqRSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDWkY7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXdzLWFyY2hpdGVjdHVyZS1kaWFncmFtLWdlbmVyYXRvci8uL2NvbXBvbmVudHMvRXJyb3JNZXNzYWdlLnRzeD8zNTg1Il0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBFcnJvck1lc3NhZ2VQcm9wcyB7XG4gIG1lc3NhZ2U6IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRXJyb3JNZXNzYWdlKHsgbWVzc2FnZSB9OiBFcnJvck1lc3NhZ2VQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZXJyb3JcIj5cbiAgICAgIHttZXNzYWdlfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkVycm9yTWVzc2FnZSIsIm1lc3NhZ2UiLCJkaXYiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ErrorMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./components/LoadingSpinner.tsx":
/*!***************************************!*\
  !*** ./components/LoadingSpinner.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingSpinner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"loading\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/LoadingSpinner.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Analyzing requirements and generating diagram...\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/LoadingSpinner.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/LoadingSpinner.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0E7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUNmLDhEQUFDQzswQkFBRTs7Ozs7Ozs7Ozs7O0FBR1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hd3MtYXJjaGl0ZWN0dXJlLWRpYWdyYW0tZ2VuZXJhdG9yLy4vY29tcG9uZW50cy9Mb2FkaW5nU3Bpbm5lci50c3g/MTA3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nU3Bpbm5lcigpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmdcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3Bpbm5lclwiPjwvZGl2PlxuICAgICAgPHA+QW5hbHl6aW5nIHJlcXVpcmVtZW50cyBhbmQgZ2VuZXJhdGluZyBkaWFncmFtLi4uPC9wPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/LoadingSpinner.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateDiagram: () => (/* binding */ generateDiagram),\n/* harmony export */   getDiagramUrl: () => (/* binding */ getDiagramUrl),\n/* harmony export */   getExcalidrawViewUrl: () => (/* binding */ getExcalidrawViewUrl)\n/* harmony export */ });\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:8000\";\nasync function generateDiagram(request) {\n    const response = await fetch(`${API_URL}/generate`, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(request)\n    });\n    if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    return response.json();\n}\nfunction getDiagramUrl(diagramPath) {\n    const filename = diagramPath.split(\"/\").pop();\n    return `${API_URL}/diagram/${filename}`;\n}\nfunction getExcalidrawViewUrl(diagramPath) {\n    // For Excalidraw files, we can create a direct link to view in Excalidraw\n    const filename = diagramPath.split(\"/\").pop();\n    if (filename?.endsWith(\".excalidraw\")) {\n        return `https://excalidraw.com/`;\n    }\n    return getDiagramUrl(diagramPath);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c0d5a2b55432\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hd3MtYXJjaGl0ZWN0dXJlLWRpYWdyYW0tZ2VuZXJhdG9yLy4vYXBwL2dsb2JhbHMuY3NzPzYwYzAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjMGQ1YTJiNTU0MzJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"AWS Architecture Diagram Generator - Excalidraw Edition\",\n    description: \"Describe your architecture in plain English and let Claude AI create beautiful Excalidraw diagrams\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/system-design-llm/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFBTUg7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hd3MtYXJjaGl0ZWN0dXJlLWRpYWdyYW0tZ2VuZXJhdG9yLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBV1MgQXJjaGl0ZWN0dXJlIERpYWdyYW0gR2VuZXJhdG9yIC0gRXhjYWxpZHJhdyBFZGl0aW9uJyxcbiAgZGVzY3JpcHRpb246ICdEZXNjcmliZSB5b3VyIGFyY2hpdGVjdHVyZSBpbiBwbGFpbiBFbmdsaXNoIGFuZCBsZXQgQ2xhdWRlIEFJIGNyZWF0ZSBiZWF1dGlmdWwgRXhjYWxpZHJhdyBkaWFncmFtcycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_DiagramGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/DiagramGenerator */ \"(rsc)/./components/DiagramGenerator.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"aws-logo\",\n                                children: \"AWS\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n                                lineNumber: 8,\n                                columnNumber: 11\n                            }, this),\n                            \"Architecture Diagram Generator\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Describe your architecture in plain English and let Claude AI create beautiful Excalidraw diagrams\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DiagramGenerator__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDOzswQ0FDQyw4REFBQ0Y7Z0NBQUlDLFdBQVU7MENBQVc7Ozs7Ozs0QkFBUzs7Ozs7OztrQ0FHckMsOERBQUNFO2tDQUFFOzs7Ozs7Ozs7Ozs7MEJBRUwsOERBQUNMLG9FQUFnQkE7Ozs7Ozs7Ozs7O0FBR3ZCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXdzLWFyY2hpdGVjdHVyZS1kaWFncmFtLWdlbmVyYXRvci8uL2FwcC9wYWdlLnRzeD83NjAzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBEaWFncmFtR2VuZXJhdG9yIGZyb20gJ0AvY29tcG9uZW50cy9EaWFncmFtR2VuZXJhdG9yJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZWFkZXJcIj5cbiAgICAgICAgPGgxPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXdzLWxvZ29cIj5BV1M8L2Rpdj5cbiAgICAgICAgICBBcmNoaXRlY3R1cmUgRGlhZ3JhbSBHZW5lcmF0b3JcbiAgICAgICAgPC9oMT5cbiAgICAgICAgPHA+RGVzY3JpYmUgeW91ciBhcmNoaXRlY3R1cmUgaW4gcGxhaW4gRW5nbGlzaCBhbmQgbGV0IENsYXVkZSBBSSBjcmVhdGUgYmVhdXRpZnVsIEV4Y2FsaWRyYXcgZGlhZ3JhbXM8L3A+XG4gICAgICA8L2Rpdj5cbiAgICAgIDxEaWFncmFtR2VuZXJhdG9yIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiRGlhZ3JhbUdlbmVyYXRvciIsIkhvbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/DiagramGenerator.tsx":
/*!*****************************************!*\
  !*** ./components/DiagramGenerator.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/workspace/system-design-llm/components/DiagramGenerator.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();