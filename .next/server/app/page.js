/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"90f68145591a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MGY2ODE0NTU5MWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'AWS Architecture Designer - Chat with Claude AI',\n    description: 'Interactive chat interface to create and iterate on AWS architecture diagrams with Claude AI and Excalidraw'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspace/system-design-llm/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztzQkFBTUg7Ozs7Ozs7Ozs7O0FBR2IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9qdXN0aW5tZW5lc3RyaW5hL3dvcmtzcGFjZS9zeXN0ZW0tZGVzaWduLWxsbS9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBV1MgQXJjaGl0ZWN0dXJlIERlc2lnbmVyIC0gQ2hhdCB3aXRoIENsYXVkZSBBSScsXG4gIGRlc2NyaXB0aW9uOiAnSW50ZXJhY3RpdmUgY2hhdCBpbnRlcmZhY2UgdG8gY3JlYXRlIGFuZCBpdGVyYXRlIG9uIEFXUyBhcmNoaXRlY3R1cmUgZGlhZ3JhbXMgd2l0aCBDbGF1ZGUgQUkgYW5kIEV4Y2FsaWRyYXcnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ArchitectureWorkspace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ArchitectureWorkspace */ \"(rsc)/./components/ArchitectureWorkspace.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ArchitectureWorkspace__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBdUU7QUFFeEQsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELHlFQUFxQkE7Ozs7O0FBQy9CIiwic291cmNlcyI6WyIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vYXBwL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBBcmNoaXRlY3R1cmVXb3Jrc3BhY2UgZnJvbSAnQC9jb21wb25lbnRzL0FyY2hpdGVjdHVyZVdvcmtzcGFjZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiA8QXJjaGl0ZWN0dXJlV29ya3NwYWNlIC8+O1xufVxuIl0sIm5hbWVzIjpbIkFyY2hpdGVjdHVyZVdvcmtzcGFjZSIsIkhvbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ArchitectureWorkspace.tsx":
/*!**********************************************!*\
  !*** ./components/ArchitectureWorkspace.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/workspace/system-design-llm/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/workspace/system-design-llm/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/workspace/system-design-llm/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ArchitectureWorkspace.tsx */ \"(rsc)/./components/ArchitectureWorkspace.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGanVzdGlubWVuZXN0cmluYSUyRndvcmtzcGFjZSUyRnN5c3RlbS1kZXNpZ24tbGxtJTJGY29tcG9uZW50cyUyRkFyY2hpdGVjdHVyZVdvcmtzcGFjZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBMEoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vY29tcG9uZW50cy9BcmNoaXRlY3R1cmVXb3Jrc3BhY2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/ArchitectureWorkspace.tsx":
/*!**********************************************!*\
  !*** ./components/ArchitectureWorkspace.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArchitectureWorkspace)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatInterface */ \"(ssr)/./components/ChatInterface.tsx\");\n/* harmony import */ var _ExcalidrawCanvas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ExcalidrawCanvas */ \"(ssr)/./components/ExcalidrawCanvas.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ArchitectureWorkspace() {\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: Date.now().toString(),\n        messages: [],\n        current_diagram: null,\n        created_at: new Date(),\n        updated_at: new Date()\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Save session to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArchitectureWorkspace.useEffect\": ()=>{\n            localStorage.setItem('architecture-session', JSON.stringify(session));\n        }\n    }[\"ArchitectureWorkspace.useEffect\"], [\n        session\n    ]);\n    // Load session from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ArchitectureWorkspace.useEffect\": ()=>{\n            const savedSession = localStorage.getItem('architecture-session');\n            if (savedSession) {\n                try {\n                    const parsed = JSON.parse(savedSession);\n                    // Convert date strings back to Date objects\n                    parsed.created_at = new Date(parsed.created_at);\n                    parsed.updated_at = new Date(parsed.updated_at);\n                    parsed.messages = parsed.messages.map({\n                        \"ArchitectureWorkspace.useEffect\": (msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            })\n                    }[\"ArchitectureWorkspace.useEffect\"]);\n                    setSession(parsed);\n                } catch (error) {\n                    console.error('Error loading saved session:', error);\n                }\n            }\n        }\n    }[\"ArchitectureWorkspace.useEffect\"], []);\n    const handleSessionUpdate = (updatedSession)=>{\n        setSession(updatedSession);\n    };\n    const handleDiagramUpdate = (diagramData)=>{\n        setSession((prev)=>({\n                ...prev,\n                current_diagram: diagramData,\n                updated_at: new Date()\n            }));\n    };\n    const handleDiagramChange = (elements, appState)=>{\n        // Update the current diagram when user manually edits\n        const updatedDiagram = {\n            ...session.current_diagram,\n            elements,\n            appState\n        };\n        setSession((prev)=>({\n                ...prev,\n                current_diagram: updatedDiagram,\n                updated_at: new Date()\n            }));\n    };\n    const handleNewSession = ()=>{\n        const newSession = {\n            id: Date.now().toString(),\n            messages: [],\n            current_diagram: null,\n            created_at: new Date(),\n            updated_at: new Date()\n        };\n        setSession(newSession);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"architecture-workspace\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"workspace-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"aws-logo\",\n                                        children: \"AWS\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Architecture Designer\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Chat with Claude AI to create and iterate on architecture diagrams\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-actions\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleNewSession,\n                            className: \"new-session-btn\",\n                            children: \"✨ New Session\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"workspace-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-panel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            session: session,\n                            onSessionUpdate: handleSessionUpdate,\n                            onDiagramUpdate: handleDiagramUpdate,\n                            isLoading: isLoading,\n                            setIsLoading: setIsLoading\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"diagram-panel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExcalidrawCanvas__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            diagramData: session.current_diagram,\n                            onDiagramChange: handleDiagramChange\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ArchitectureWorkspace.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ArchitectureWorkspace.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ChatInput.tsx":
/*!**********************************!*\
  !*** ./components/ChatInput.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ChatInput({ onSendMessage, disabled = false }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSend = ()=>{\n        if (message.trim() && !disabled) {\n            onSendMessage(message);\n            setMessage('');\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"chat-input\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"input-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: message,\n                        onChange: (e)=>setMessage(e.target.value),\n                        onKeyPress: handleKeyPress,\n                        placeholder: disabled ? \"Generating diagram...\" : \"Describe your architecture or ask for changes...\",\n                        disabled: disabled,\n                        rows: 3,\n                        className: \"message-input\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleSend,\n                        disabled: disabled || !message.trim(),\n                        className: \"send-button\",\n                        children: disabled ? '⏳' : '📤'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"input-hints\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: '\\uD83D\\uDCA1 Try: \"Add a cache layer\" or \"Replace with microservices\"'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"⌨️ Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInput.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ChatInput.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ChatInterface.tsx":
/*!**************************************!*\
  !*** ./components/ChatInterface.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ChatMessage */ \"(ssr)/./components/ChatMessage.tsx\");\n/* harmony import */ var _ChatInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ChatInput */ \"(ssr)/./components/ChatInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatInterface({ session, onSessionUpdate, onDiagramUpdate, isLoading, setIsLoading }) {\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        session.messages\n    ]);\n    const handleSendMessage = async (content)=>{\n        if (!content.trim() || isLoading) return;\n        // Add user message\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: content.trim(),\n            timestamp: new Date()\n        };\n        const updatedSession = {\n            ...session,\n            messages: [\n                ...session.messages,\n                userMessage\n            ],\n            updated_at: new Date()\n        };\n        onSessionUpdate(updatedSession);\n        setIsLoading(true);\n        try {\n            // Generate/update diagram\n            const response = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.generateDiagram)({\n                description: content.trim(),\n                style: 'excalidraw',\n                existing_diagram: session.current_diagram,\n                chat_history: session.messages\n            });\n            // Parse the Excalidraw data from the response\n            let diagramData = null;\n            if (response.excalidraw_path) {\n                try {\n                    // Fetch the actual Excalidraw data from the backend\n                    const filename = response.excalidraw_path.split('/').pop();\n                    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n                    const diagramResponse = await fetch(`${API_URL}/diagram/${filename}`);\n                    if (diagramResponse.ok) {\n                        diagramData = await diagramResponse.json();\n                    }\n                } catch (error) {\n                    console.error('Error fetching diagram data:', error);\n                }\n            }\n            // Add assistant message\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: response.explanation,\n                timestamp: new Date(),\n                diagram_data: diagramData\n            };\n            const finalSession = {\n                ...updatedSession,\n                messages: [\n                    ...updatedSession.messages,\n                    assistantMessage\n                ],\n                current_diagram: diagramData || session.current_diagram,\n                updated_at: new Date()\n            };\n            onSessionUpdate(finalSession);\n            if (diagramData) {\n                onDiagramUpdate(diagramData);\n            }\n        } catch (error) {\n            console.error('Error generating diagram:', error);\n            // Add error message\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                role: 'assistant',\n                content: `Sorry, I encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`,\n                timestamp: new Date()\n            };\n            const errorSession = {\n                ...updatedSession,\n                messages: [\n                    ...updatedSession.messages,\n                    errorMessage\n                ],\n                updated_at: new Date()\n            };\n            onSessionUpdate(errorSession);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"chat-interface\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCAC Architecture Chat\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Describe your AWS architecture and I'll create and iterate on diagrams\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-messages\",\n                children: [\n                    session.messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"welcome-message\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"\\uD83D\\uDC4B Welcome!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Start by describing your AWS architecture. For example:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '\"Create a serverless API with Lambda and DynamoDB\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '\"Add a load balancer to the front\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: '\"Replace DynamoDB with RDS for better consistency\"'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this) : session.messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-message\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"typing-indicator\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Analyzing and updating diagram...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onSendMessage: handleSendMessage,\n                disabled: isLoading\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatInterface.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ChatMessage.tsx":
/*!************************************!*\
  !*** ./components/ChatMessage.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatMessageComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ChatMessageComponent({ message }) {\n    const isUser = message.role === 'user';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `chat-message ${isUser ? 'user' : 'assistant'}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-avatar\",\n                children: isUser ? '👤' : '🤖'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"message-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"message-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"message-role\",\n                                children: isUser ? 'You' : 'Claude'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"message-time\",\n                                children: message.timestamp.toLocaleTimeString([], {\n                                    hour: '2-digit',\n                                    minute: '2-digit'\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"message-text\",\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    message.diagram_data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"message-diagram-indicator\",\n                        children: \"\\uD83C\\uDFA8 Diagram updated\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ChatMessage.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ChatMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ExcalidrawCanvas.tsx":
/*!*****************************************!*\
  !*** ./components/ExcalidrawCanvas.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ExcalidrawCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Dynamically import Excalidraw with SSR disabled\nconst Excalidraw = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components/ExcalidrawCanvas.tsx -> \" + \"@excalidraw/excalidraw\"\n        ]\n    },\n    ssr: false\n});\nfunction ExcalidrawCanvas({ diagramData, onDiagramChange }) {\n    const [excalidrawAPI, setExcalidrawAPI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isUpdatingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Update Excalidraw when diagram data changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ExcalidrawCanvas.useEffect\": ()=>{\n            if (excalidrawAPI && diagramData) {\n                try {\n                    isUpdatingRef.current = true;\n                    // Update the scene with new diagram data\n                    excalidrawAPI.updateScene({\n                        elements: diagramData.elements || [],\n                        appState: {\n                            ...diagramData.appState,\n                            viewBackgroundColor: '#ffffff'\n                        }\n                    });\n                    // Reset the flag after a short delay to allow the update to complete\n                    setTimeout({\n                        \"ExcalidrawCanvas.useEffect\": ()=>{\n                            isUpdatingRef.current = false;\n                        }\n                    }[\"ExcalidrawCanvas.useEffect\"], 0);\n                } catch (error) {\n                    console.error('Error updating Excalidraw scene:', error);\n                    isUpdatingRef.current = false;\n                }\n            }\n        }\n    }[\"ExcalidrawCanvas.useEffect\"], [\n        diagramData,\n        excalidrawAPI\n    ]);\n    const handleChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ExcalidrawCanvas.useCallback[handleChange]\": (elements, appState)=>{\n            if (onDiagramChange && !isUpdatingRef.current) {\n                onDiagramChange(elements, appState);\n            }\n        }\n    }[\"ExcalidrawCanvas.useCallback[handleChange]\"], [\n        onDiagramChange\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"excalidraw-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83C\\uDFA8 Architecture Diagram\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"excalidraw-controls\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (excalidrawAPI) {\n                                        excalidrawAPI.resetScene();\n                                    }\n                                },\n                                className: \"control-button\",\n                                title: \"Clear diagram\",\n                                children: \"\\uD83D\\uDDD1️ Clear\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (excalidrawAPI) {\n                                        const elements = excalidrawAPI.getSceneElements();\n                                        const appState = excalidrawAPI.getAppState();\n                                        const dataURL = excalidrawAPI.getSceneElementsIncludingDeleted();\n                                        // Create download link\n                                        const link = document.createElement('a');\n                                        link.download = `architecture-${Date.now()}.excalidraw`;\n                                        link.href = URL.createObjectURL(new Blob([\n                                            JSON.stringify({\n                                                elements,\n                                                appState\n                                            })\n                                        ], {\n                                            type: 'application/json'\n                                        }));\n                                        link.click();\n                                    }\n                                },\n                                className: \"control-button\",\n                                title: \"Download diagram\",\n                                children: \"\\uD83D\\uDCBE Download\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-wrapper\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Excalidraw, {\n                    ref: (api)=>setExcalidrawAPI(api),\n                    onChange: handleChange,\n                    initialData: {\n                        elements: diagramData?.elements || [],\n                        appState: {\n                            viewBackgroundColor: '#ffffff',\n                            currentItemFontFamily: 1,\n                            currentItemFontSize: 16,\n                            ...diagramData?.appState\n                        }\n                    },\n                    UIOptions: {\n                        canvasActions: {\n                            loadScene: false,\n                            saveToActiveFile: false,\n                            export: {\n                                saveFileToDisk: true\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            !diagramData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"excalidraw-placeholder\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"placeholder-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"\\uD83C\\uDFAF Ready to Create\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Start a conversation to generate your first architecture diagram!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"placeholder-features\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI-generated diagrams\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDD04\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Iterative updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"feature\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✏️\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Manual editing\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspace/system-design-llm/components/ExcalidrawCanvas.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ExcalidrawCanvas.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateDiagram: () => (/* binding */ generateDiagram),\n/* harmony export */   getDiagramUrl: () => (/* binding */ getDiagramUrl),\n/* harmony export */   getExcalidrawViewUrl: () => (/* binding */ getExcalidrawViewUrl)\n/* harmony export */ });\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\nasync function generateDiagram(request) {\n    // Prepare the request with chat context\n    const requestBody = {\n        description: request.description,\n        export_format: request.style || 'excalidraw',\n        existing_diagram: request.existing_diagram,\n        chat_history: request.chat_history?.map((msg)=>({\n                role: msg.role,\n                content: msg.content,\n                timestamp: msg.timestamp.toISOString()\n            }))\n    };\n    const response = await fetch(`${API_URL}/generate`, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(requestBody)\n    });\n    if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n    }\n    return response.json();\n}\nfunction getDiagramUrl(diagramPath) {\n    const filename = diagramPath.split('/').pop();\n    return `${API_URL}/diagram/${filename}`;\n}\nfunction getExcalidrawViewUrl(diagramPath) {\n    // For Excalidraw files, we can create a direct link to view in Excalidraw\n    const filename = diagramPath.split('/').pop();\n    if (filename?.endsWith('.excalidraw')) {\n        return `https://excalidraw.com/`;\n    }\n    return getDiagramUrl(diagramPath);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ArchitectureWorkspace.tsx */ \"(ssr)/./components/ArchitectureWorkspace.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGanVzdGlubWVuZXN0cmluYSUyRndvcmtzcGFjZSUyRnN5c3RlbS1kZXNpZ24tbGxtJTJGY29tcG9uZW50cyUyRkFyY2hpdGVjdHVyZVdvcmtzcGFjZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBMEoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCIvVXNlcnMvanVzdGlubWVuZXN0cmluYS93b3Jrc3BhY2Uvc3lzdGVtLWRlc2lnbi1sbG0vY29tcG9uZW50cy9BcmNoaXRlY3R1cmVXb3Jrc3BhY2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fcomponents%2FArchitectureWorkspace.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjustinmenestrina%2Fworkspace%2Fsystem-design-llm&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();