<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS Architecture Diagram Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 900px;
            width: 100%;
            overflow: hidden;
            animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #232F3E 0%, #131A22 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .aws-logo {
            width: 50px;
            height: 50px;
            background: #FF9900;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 20px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .input-section {
            margin-bottom: 30px;
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: #333;
            font-size: 1.1em;
        }
        
        textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e4e8;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            min-height: 150px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .examples {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
        
        .examples strong {
            color: #333;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 18px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 30px auto;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            animation: fadeIn 0.5s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .result h2 {
            margin-bottom: 20px;
            color: #333;
        }
        
        .diagram-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .diagram-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
        }
        
        .error {
            display: none;
            padding: 15px;
            background: #fee;
            border: 1px solid #fcc;
            border-radius: 10px;
            color: #c33;
            margin-top: 20px;
        }
        
        .components-info {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .components-info h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .component-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .component-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <div class="aws-logo">AWS</div>
                Architecture Diagram Generator
            </h1>
            <p>Describe your architecture in plain English and let AI create the diagram</p>
        </div>
        
        <div class="content">
            <div class="input-section">
                <label for="description">Describe your AWS architecture:</label>
                <textarea 
                    id="description" 
                    placeholder="Example: I need a scalable web application with a load balancer, auto-scaling EC2 instances, RDS database, and S3 for static content..."
                ></textarea>
                <div class="examples">
                    <strong>Try these examples:</strong>
                    <ul style="margin-left: 20px; margin-top: 5px;">
                        <li>A serverless API with Lambda, API Gateway, and DynamoDB</li>
                        <li>A three-tier web application with load balancer, EC2 instances, and RDS</li>
                        <li>A data processing pipeline with Kinesis, Lambda, and S3</li>
                        <li>A microservices architecture with ECS, ALB, and multiple databases</li>
                    </ul>
                </div>
            </div>
            
            <button onclick="generateDiagram()">Generate Architecture Diagram</button>
            
            <div class="loading">
                <div class="spinner"></div>
                <p>Analyzing requirements and generating diagram...</p>
            </div>
            
            <div class="error"></div>
            
            <div class="result">
                <h2>Generated Architecture</h2>
                <div class="diagram-container">
                    <img id="diagram" alt="AWS Architecture Diagram">
                </div>
                <div class="components-info">
                    <h3>Architecture Components</h3>
                    <p id="explanation"></p>
                    <div class="component-list" id="componentList"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8000';
        
        async function generateDiagram() {
            const description = document.getElementById('description').value.trim();
            
            if (!description) {
                showError('Please enter an architecture description');
                return;
            }
            
            const button = document.querySelector('button');
            const loading = document.querySelector('.loading');
            const result = document.querySelector('.result');
            const error = document.querySelector('.error');
            
            // Reset states
            button.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';
            error.style.display = 'none';
            
            try {
                const response = await fetch(`${API_URL}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        description: description,
                        style: 'standard'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Display the diagram
                const diagramImg = document.getElementById('diagram');
                diagramImg.src = `${API_URL}/diagram/${data.diagram_path.split('/').pop()}`;
                
                // Display explanation
                document.getElementById('explanation').textContent = data.explanation;
                
                // Display components
                const componentList = document.getElementById('componentList');
                componentList.innerHTML = '';
                
                if (data.components && data.components.components) {
                    data.components.components.forEach(comp => {
                        const tag = document.createElement('div');
                        tag.className = 'component-tag';
                        tag.textContent = `${comp.name} (${comp.type.toUpperCase()})`;
                        componentList.appendChild(tag);
                    });
                }
                
                result.style.display = 'block';
                
            } catch (err) {
                showError(`Error: ${err.message}. Make sure the backend server is running.`);
            } finally {
                button.disabled = false;
                loading.style.display = 'none';
            }
        }
        
        function showError(message) {
            const error = document.querySelector('.error');
            error.textContent = message;
            error.style.display = 'block';
        }
        
        // Allow Enter key to submit (Ctrl+Enter)
        document.getElementById('description').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                generateDiagram();
            }
        });
    </script>
</body>
</html>