import os
import json
import base64
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import re
import subprocess
import platform

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import anthropic

app = FastAPI()

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Claude client (optional - will use patterns if not set)
anthropic_client = None
anthropic_api_key = "************************************************************************************************************"
if anthropic_api_key:
    try:
        # Try with default parameters
        anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
    except TypeError as e:
        if "proxies" in str(e):
            # If error is about proxies, try importing with specific parameters
            import httpx
            anthropic_client = anthropic.Anthropic(
                api_key=anthropic_api_key,
                http_client=httpx.Client()
            )
        else:
            # Re-raise if it's a different error
            raise

class ArchitectureRequest(BaseModel):
    description: str
    export_format: Optional[str] = "drawio"  # drawio, png, svg
    open_desktop: Optional[bool] = False

class ArchitectureResponse(BaseModel):
    diagram_path: str
    drawio_path: str
    components: Dict
    explanation: str
    embed_url: Optional[str] = None

# AWS service to draw.io shape mapping
AWS_SHAPES = {
    "ec2": {
        "style": "shape=mxgraph.aws4.ec2;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "EC2"
    },
    "lambda": {
        "style": "shape=mxgraph.aws4.lambda;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "Lambda"
    },
    "rds": {
        "style": "shape=mxgraph.aws4.rds;fillColor=#4B9EDA;",
        "width": 78,
        "height": 78,
        "label": "RDS"
    },
    "dynamodb": {
        "style": "shape=mxgraph.aws4.dynamodb;fillColor=#4B9EDA;",
        "width": 78,
        "height": 78,
        "label": "DynamoDB"
    },
    "s3": {
        "style": "shape=mxgraph.aws4.s3;fillColor=#569A31;",
        "width": 78,
        "height": 78,
        "label": "S3"
    },
    "alb": {
        "style": "shape=mxgraph.aws4.application_load_balancer;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "ALB"
    },
    "cloudfront": {
        "style": "shape=mxgraph.aws4.cloudfront;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "CloudFront"
    },
    "elasticache": {
        "style": "shape=mxgraph.aws4.elasticache;fillColor=#4B9EDA;",
        "width": 78,
        "height": 78,
        "label": "ElastiCache"
    },
    "sqs": {
        "style": "shape=mxgraph.aws4.sqs;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "SQS"
    },
    "sns": {
        "style": "shape=mxgraph.aws4.sns;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "SNS"
    },
    "kinesis": {
        "style": "shape=mxgraph.aws4.kinesis;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "Kinesis"
    },
    "ecs": {
        "style": "shape=mxgraph.aws4.ecs;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "ECS"
    },
    "cognito": {
        "style": "shape=mxgraph.aws4.cognito;fillColor=#DD2A5B;",
        "width": 78,
        "height": 78,
        "label": "Cognito"
    },
    "api_gateway": {
        "style": "shape=mxgraph.aws4.api_gateway;fillColor=#FF9900;",
        "width": 78,
        "height": 78,
        "label": "API Gateway"
    }
}

def parse_architecture_with_llm(description: str) -> Dict:
    """Use Claude to parse the architecture description"""

    if not anthropic_client:
        return parse_architecture_with_patterns(description)

    system_prompt = """You are an AWS Solutions Architect. Parse the given description and return a JSON object with the following structure:
    {
        "title": "Architecture name",
        "components": [
            {
                "type": "component_type",  // ec2, rds, lambda, s3, alb, dynamodb, etc.
                "name": "Component name",
                "count": 1,
                "cluster": "cluster_name"  // optional, for grouping
            }
        ],
        "connections": [
            {
                "from": "component_name_1",
                "to": "component_name_2",
                "label": "connection_label"  // optional
            }
        ],
        "clusters": [
            {
                "name": "cluster_name",
                "label": "Cluster Label"
            }
        ]
    }

    Available component types: ec2, lambda, ecs, rds, dynamodb, elasticache, s3, alb, cloudfront, sqs, sns, kinesis, cognito, api_gateway

    Focus on creating a clean, logical architecture based on the description."""

    try:
        response = anthropic_client.messages.create(
            model="claude-sonnet-4-20250514",
            max_tokens=1000,
            temperature=0.7,
            system=system_prompt,
            messages=[
                {"role": "user", "content": f"Parse this architecture description: {description}"}
            ]
        )

        content = response.content[0].text
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())
        else:
            raise ValueError("No valid JSON found in response")

    except Exception as e:
        print(f"LLM parsing failed: {e}")
        return parse_architecture_with_patterns(description)

def parse_architecture_with_patterns(description: str) -> Dict:
    """Fallback pattern-based parsing"""
    description_lower = description.lower()
    
    if "serverless" in description_lower or "lambda" in description_lower:
        return {
            "title": "Serverless Architecture",
            "components": [
                {"type": "api_gateway", "name": "API Gateway", "count": 1},
                {"type": "lambda", "name": "Lambda Function", "count": 3, "cluster": "compute"},
                {"type": "dynamodb", "name": "DynamoDB", "count": 1, "cluster": "data"}
            ],
            "connections": [
                {"from": "API Gateway", "to": "Lambda Function"},
                {"from": "Lambda Function", "to": "DynamoDB"}
            ],
            "clusters": [
                {"name": "compute", "label": "Compute"},
                {"name": "data", "label": "Data Storage"}
            ]
        }
    else:
        # Default web architecture
        return {
            "title": "Web Application Architecture",
            "components": [
                {"type": "alb", "name": "Load Balancer", "count": 1},
                {"type": "ec2", "name": "Web Server", "count": 2, "cluster": "web"},
                {"type": "rds", "name": "Database", "count": 1, "cluster": "data"}
            ],
            "connections": [
                {"from": "Load Balancer", "to": "Web Server"},
                {"from": "Web Server", "to": "Database"}
            ],
            "clusters": [
                {"name": "web", "label": "Web Tier"},
                {"name": "data", "label": "Data Tier"}
            ]
        }

def calculate_positions(architecture: Dict) -> Dict[str, Tuple[int, int]]:
    """Calculate positions for components in the diagram"""
    positions = {}
    
    # Group components by cluster
    clustered = {}
    unclustered = []
    
    for comp in architecture.get("components", []):
        cluster = comp.get("cluster")
        if cluster:
            if cluster not in clustered:
                clustered[cluster] = []
            clustered[cluster].append(comp)
        else:
            unclustered.append(comp)
    
    y_offset = 100
    
    # Position unclustered components first
    x_offset = 100
    for comp in unclustered:
        positions[comp["name"]] = (x_offset, y_offset)
        x_offset += 200
    
    # Position clustered components
    y_offset += 200
    for cluster_name, components in clustered.items():
        x_offset = 100
        for comp in components:
            if comp.get("count", 1) > 1:
                # For multiple instances, position them horizontally
                for i in range(comp["count"]):
                    name = f"{comp['name']} {i+1}"
                    positions[name] = (x_offset + i * 120, y_offset)
            else:
                positions[comp["name"]] = (x_offset, y_offset)
            x_offset += 200
        y_offset += 200
    
    return positions

def generate_drawio_xml(architecture: Dict) -> str:
    """Generate draw.io XML format"""
    
    # Create root element
    root = ET.Element("mxfile", {
        "host": "app.diagrams.net",
        "modified": datetime.now().isoformat(),
        "agent": "AWS Architecture Generator",
        "version": "21.1.2",
        "type": "device"
    })
    
    diagram = ET.SubElement(root, "diagram", {
        "name": architecture.get("title", "AWS Architecture"),
        "id": "generated-diagram"
    })
    
    graph_model = ET.SubElement(diagram, "mxGraphModel", {
        "dx": "0",
        "dy": "0",
        "grid": "1",
        "gridSize": "10",
        "guides": "1",
        "tooltips": "1",
        "connect": "1",
        "arrows": "1",
        "fold": "1",
        "page": "1",
        "pageScale": "1",
        "pageWidth": "1169",
        "pageHeight": "827",
        "math": "0",
        "shadow": "0"
    })
    
    root_cell = ET.SubElement(graph_model, "root")
    
    # Add default cells
    ET.SubElement(root_cell, "mxCell", {"id": "0"})
    ET.SubElement(root_cell, "mxCell", {"id": "1", "parent": "0"})
    
    # Calculate positions
    positions = calculate_positions(architecture)
    
    # Track created elements
    element_ids = {}
    current_id = 2
    
    # Create clusters (groups)
    cluster_ids = {}
    for cluster_info in architecture.get("clusters", []):
        cluster_id = str(current_id)
        cluster_ids[cluster_info["name"]] = cluster_id
        
        # Find components in this cluster to calculate group bounds
        cluster_components = [c for c in architecture["components"] if c.get("cluster") == cluster_info["name"]]
        if cluster_components:
            min_x = min(positions.get(c["name"], (0, 0))[0] for c in cluster_components) - 50
            min_y = min(positions.get(c["name"], (0, 0))[1] for c in cluster_components) - 50
            max_x = max(positions.get(c["name"], (0, 0))[0] for c in cluster_components) + 150
            max_y = max(positions.get(c["name"], (0, 0))[1] for c in cluster_components) + 150
            
            group = ET.SubElement(root_cell, "mxCell", {
                "id": cluster_id,
                "value": cluster_info["label"],
                "style": "group;fillColor=#f5f5f5;strokeColor=#666666;rounded=1;",
                "vertex": "1",
                "connectable": "0",
                "parent": "1"
            })
            
            ET.SubElement(group, "mxGeometry", {
                "x": str(min_x),
                "y": str(min_y),
                "width": str(max_x - min_x),
                "height": str(max_y - min_y),
                "as": "geometry"
            })
            
        current_id += 1
    
    # Create components
    for component in architecture.get("components", []):
        comp_type = component["type"].lower()
        comp_name = component["name"]
        comp_count = component.get("count", 1)
        cluster = component.get("cluster")
        
        if comp_type in AWS_SHAPES:
            shape_info = AWS_SHAPES[comp_type]
            
            if comp_count > 1:
                # Create multiple instances
                for i in range(comp_count):
                    instance_name = f"{comp_name} {i+1}"
                    x, y = positions.get(instance_name, (100 + i * 120, 100))
                    
                    cell_id = str(current_id)
                    element_ids[instance_name] = cell_id
                    parent_id = cluster_ids.get(cluster, "1") if cluster else "1"
                    
                    cell = ET.SubElement(root_cell, "mxCell", {
                        "id": cell_id,
                        "value": instance_name,
                        "style": shape_info["style"] + "strokeColor=#000000;",
                        "vertex": "1",
                        "parent": parent_id
                    })
                    
                    # Adjust position if in cluster
                    if cluster and cluster in cluster_ids:
                        cluster_comps = [c for c in architecture["components"] if c.get("cluster") == cluster]
                        min_x = min(positions.get(c["name"], (0, 0))[0] for c in cluster_comps) - 50
                        min_y = min(positions.get(c["name"], (0, 0))[1] for c in cluster_comps) - 50
                        x -= min_x
                        y -= min_y
                    
                    ET.SubElement(cell, "mxGeometry", {
                        "x": str(x),
                        "y": str(y),
                        "width": str(shape_info["width"]),
                        "height": str(shape_info["height"]),
                        "as": "geometry"
                    })
                    
                    current_id += 1
                # Also track the base name for connections
                element_ids[comp_name] = element_ids[f"{comp_name} 1"]
            else:
                x, y = positions.get(comp_name, (100, 100))
                
                cell_id = str(current_id)
                element_ids[comp_name] = cell_id
                parent_id = cluster_ids.get(cluster, "1") if cluster else "1"
                
                cell = ET.SubElement(root_cell, "mxCell", {
                    "id": cell_id,
                    "value": comp_name,
                    "style": shape_info["style"] + "strokeColor=#000000;",
                    "vertex": "1",
                    "parent": parent_id
                })
                
                # Adjust position if in cluster
                if cluster and cluster in cluster_ids:
                    cluster_comps = [c for c in architecture["components"] if c.get("cluster") == cluster]
                    min_x = min(positions.get(c["name"], (0, 0))[0] for c in cluster_comps) - 50
                    min_y = min(positions.get(c["name"], (0, 0))[1] for c in cluster_comps) - 50
                    x -= min_x
                    y -= min_y
                
                ET.SubElement(cell, "mxGeometry", {
                    "x": str(x),
                    "y": str(y),
                    "width": str(shape_info["width"]),
                    "height": str(shape_info["height"]),
                    "as": "geometry"
                })
                
                current_id += 1
    
    # Create connections
    for connection in architecture.get("connections", []):
        from_comp = connection["from"]
        to_comp = connection["to"]
        label = connection.get("label", "")
        
        if from_comp in element_ids and to_comp in element_ids:
            edge_id = str(current_id)
            
            edge = ET.SubElement(root_cell, "mxCell", {
                "id": edge_id,
                "value": label,
                "style": "edgeStyle=orthogonalEdgeStyle;rounded=1;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;",
                "edge": "1",
                "parent": "1",
                "source": element_ids[from_comp],
                "target": element_ids[to_comp]
            })
            
            geometry = ET.SubElement(edge, "mxGeometry", {
                "relative": "1",
                "as": "geometry"
            })
            
            current_id += 1
    
    # Convert to string
    return ET.tostring(root, encoding='unicode')

def open_in_drawio_desktop(file_path: str):
    """Open the diagram in draw.io desktop app if installed"""
    try:
        system = platform.system()
        if system == "Darwin":  # macOS
            subprocess.run(["open", "-a", "draw.io", file_path], check=True)
        elif system == "Windows":
            subprocess.run(["start", "draw.io", file_path], shell=True, check=True)
        elif system == "Linux":
            subprocess.run(["xdg-open", file_path], check=True)
        return True
    except Exception as e:
        print(f"Could not open in draw.io desktop: {e}")
        return False

@app.post("/generate", response_model=ArchitectureResponse)
async def generate_architecture(request: ArchitectureRequest):
    """Generate an AWS architecture diagram in draw.io format"""
    
    try:
        # Parse the architecture
        architecture = parse_architecture_with_llm(request.description)
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_path = f"diagrams/aws_architecture_{timestamp}"
        
        # Create diagrams directory if it doesn't exist
        os.makedirs("diagrams", exist_ok=True)
        
        # Generate draw.io XML
        drawio_xml = generate_drawio_xml(architecture)
        
        # Save draw.io file
        drawio_path = f"{base_path}.drawio"
        with open(drawio_path, 'w', encoding='utf-8') as f:
            f.write(drawio_xml)
        
        # Open in desktop app if requested
        if request.open_desktop:
            open_in_drawio_desktop(os.path.abspath(drawio_path))
        
        # Create embed URL for web preview
        embed_url = f"https://viewer.diagrams.net/?highlight=0000ff&edit=_blank&layers=1&nav=1&title={os.path.basename(drawio_path)}"
        
        # Create explanation
        explanation = f"Generated AWS architecture with {len(architecture['components'])} components"
        if architecture.get('clusters'):
            explanation += f" organized in {len(architecture['clusters'])} tiers"
        
        return ArchitectureResponse(
            diagram_path=drawio_path,
            drawio_path=drawio_path,
            components=architecture,
            explanation=explanation,
            embed_url=embed_url
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/diagram/{filename}")
async def get_diagram(filename: str):
    """Serve the generated diagram file"""
    file_path = f"diagrams/{filename}"
    if os.path.exists(file_path):
        return FileResponse(
            file_path, 
            media_type="application/vnd.jgraph.mxfile",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    else:
        raise HTTPException(status_code=404, detail="Diagram not found")

@app.post("/export")
async def export_diagram(request: Dict):
    """Export diagram to different formats using draw.io desktop (if available)"""
    try:
        drawio_path = request.get("drawio_path")
        format = request.get("format", "png")
        
        if not os.path.exists(drawio_path):
            raise HTTPException(status_code=404, detail="Diagram file not found")
        
        output_path = drawio_path.replace(".drawio", f".{format}")
        
        # Try to use draw.io desktop CLI for export
        try:
            subprocess.run([
                "drawio",
                "--export",
                "--format", format,
                "--output", output_path,
                drawio_path
            ], check=True)
            
            return {"exported_path": output_path, "format": format}
        except:
            return {"error": "Draw.io desktop CLI not available. Please export manually."}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    return {
        "message": "AWS Architecture Diagram Generator - Draw.io Version",
        "features": [
            "Generate draw.io diagrams from text descriptions",
            "Open directly in draw.io desktop app",
            "Export to multiple formats",
            "Web preview support"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
