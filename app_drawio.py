import os
import json
import uuid
from typing import Dict, List, Optional, <PERSON><PERSON>
from datetime import datetime
import re
import subprocess
import platform

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from pydantic import BaseModel
import anthropic

app = FastAPI()

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Claude client (optional - will use patterns if not set)
anthropic_client = None
anthropic_api_key = "************************************************************************************************************"
if anthropic_api_key:
    try:
        # Try with default parameters
        anthropic_client = anthropic.Anthropic(api_key=anthropic_api_key)
    except TypeError as e:
        if "proxies" in str(e):
            # If error is about proxies, try importing with specific parameters
            import httpx
            anthropic_client = anthropic.Anthropic(
                api_key=anthropic_api_key,
                http_client=httpx.Client()
            )
        else:
            # Re-raise if it's a different error
            raise

class ArchitectureRequest(BaseModel):
    description: str
    export_format: Optional[str] = "excalidraw"  # excalidraw, png, svg
    open_desktop: Optional[bool] = False

class ArchitectureResponse(BaseModel):
    diagram_path: str
    excalidraw_path: str
    components: Dict
    explanation: str
    embed_url: Optional[str] = None

# AWS service to Excalidraw shape mapping
AWS_SHAPES = {
    "ec2": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "EC2",
        "icon": "💻"
    },
    "lambda": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "Lambda",
        "icon": "λ"
    },
    "rds": {
        "type": "ellipse",
        "backgroundColor": "#4B9EDA",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "RDS",
        "icon": "🗄️"
    },
    "dynamodb": {
        "type": "ellipse",
        "backgroundColor": "#4B9EDA",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "DynamoDB",
        "icon": "📊"
    },
    "s3": {
        "type": "rectangle",
        "backgroundColor": "#569A31",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "S3",
        "icon": "🪣"
    },
    "alb": {
        "type": "diamond",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "ALB",
        "icon": "⚖️"
    },
    "cloudfront": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "CloudFront",
        "icon": "🌐"
    },
    "elasticache": {
        "type": "ellipse",
        "backgroundColor": "#4B9EDA",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "ElastiCache",
        "icon": "⚡"
    },
    "sqs": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "SQS",
        "icon": "📬"
    },
    "sns": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "SNS",
        "icon": "📢"
    },
    "kinesis": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "Kinesis",
        "icon": "🌊"
    },
    "ecs": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "ECS",
        "icon": "🐳"
    },
    "cognito": {
        "type": "rectangle",
        "backgroundColor": "#DD2A5B",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "Cognito",
        "icon": "👤"
    },
    "api_gateway": {
        "type": "rectangle",
        "backgroundColor": "#FF9900",
        "strokeColor": "#000000",
        "width": 120,
        "height": 80,
        "label": "API Gateway",
        "icon": "🚪"
    }
}

def parse_architecture_with_llm(description: str) -> Dict:
    """Use Claude to parse the architecture description"""

    system_prompt = """You are an AWS Solutions Architect. Parse the given description and return a JSON object with the following structure:
    {
        "title": "Architecture name",
        "components": [
            {
                "type": "component_type",  // ec2, rds, lambda, s3, alb, dynamodb, etc.
                "name": "Component name",
                "count": 1,
                "cluster": "cluster_name"  // optional, for grouping
            }
        ],
        "connections": [
            {
                "from": "component_name_1",
                "to": "component_name_2",
                "label": "connection_label"  // optional
            }
        ],
        "clusters": [
            {
                "name": "cluster_name",
                "label": "Cluster Label"
            }
        ]
    }

    Available component types: ec2, lambda, ecs, rds, dynamodb, elasticache, s3, alb, cloudfront, sqs, sns, kinesis, cognito, api_gateway

    Focus on creating a clean, logical architecture based on the description."""

    try:
        response = anthropic_client.messages.create(
            model="claude-sonnet-4-20250514",
            max_tokens=1000,
            temperature=0.7,
            system=system_prompt,
            messages=[
                {"role": "user", "content": f"Parse this architecture description: {description}"}
            ]
        )

        content = response.content[0].text
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            return json.loads(json_match.group())
        else:
            raise ValueError("No valid JSON found in response")

    except Exception as e:
        print(f"LLM parsing failed: {e}")
        return parse_architecture_with_patterns(description)

def calculate_positions(architecture: Dict) -> Dict[str, Tuple[int, int]]:
    """Calculate positions for components in the diagram using a smart layout algorithm"""
    positions = {}

    # Constants for layout
    COMPONENT_WIDTH = 140  # Slightly larger than AWS_SHAPES width
    COMPONENT_HEIGHT = 120  # Height including text label
    CLUSTER_PADDING = 80
    INTER_CLUSTER_SPACING = 200
    INSTANCE_SPACING = 160

    # Group components by cluster
    clustered = {}
    unclustered = []

    for comp in architecture.get("components", []):
        cluster = comp.get("cluster")
        if cluster:
            if cluster not in clustered:
                clustered[cluster] = []
            clustered[cluster].append(comp)
        else:
            unclustered.append(comp)

    # Calculate layout based on architecture type
    clusters = list(clustered.keys())
    total_clusters = len(clusters)

    # Determine layout strategy
    if total_clusters == 0:
        # Simple horizontal layout for unclustered components
        x_offset = 100
        y_offset = 200
        for comp in unclustered:
            if comp.get("count", 1) > 1:
                # Multiple instances in a vertical stack
                for i in range(comp["count"]):
                    name = f"{comp['name']} {i+1}"
                    positions[name] = (x_offset, y_offset + i * (COMPONENT_HEIGHT + 20))
            else:
                positions[comp["name"]] = (x_offset, y_offset)
            x_offset += COMPONENT_WIDTH + 50

    elif total_clusters <= 3:
        # Horizontal cluster layout (typical 3-tier architecture)
        cluster_x_positions = {}
        current_x = 100

        # Position clusters horizontally
        for i, cluster_name in enumerate(clusters):
            cluster_x_positions[cluster_name] = current_x

            # Calculate cluster width based on components
            cluster_components = clustered[cluster_name]
            max_instances = max(comp.get("count", 1) for comp in cluster_components)
            cluster_width = max(
                len(cluster_components) * COMPONENT_WIDTH,
                max_instances * INSTANCE_SPACING
            )

            current_x += cluster_width + INTER_CLUSTER_SPACING

        # Position components within clusters
        for cluster_name, components in clustered.items():
            cluster_x = cluster_x_positions[cluster_name]
            cluster_y = 200

            # Sort components by type for better visual grouping
            components.sort(key=lambda c: (c.get("type", ""), c.get("name", "")))

            comp_x = cluster_x + CLUSTER_PADDING
            for comp in components:
                if comp.get("count", 1) > 1:
                    # Multiple instances - arrange in a grid
                    instances_per_row = min(3, comp["count"])  # Max 3 per row
                    for i in range(comp["count"]):
                        row = i // instances_per_row
                        col = i % instances_per_row
                        name = f"{comp['name']} {i+1}"
                        positions[name] = (
                            comp_x + col * INSTANCE_SPACING,
                            cluster_y + row * COMPONENT_HEIGHT
                        )
                else:
                    positions[comp["name"]] = (comp_x, cluster_y)

                # Move to next position
                if comp.get("count", 1) > 1:
                    instances_per_row = min(3, comp["count"])
                    comp_x += instances_per_row * INSTANCE_SPACING
                else:
                    comp_x += COMPONENT_WIDTH + 30

                # If we're getting too wide, move to next row
                if comp_x > cluster_x + 600:  # Max cluster width
                    comp_x = cluster_x + CLUSTER_PADDING
                    cluster_y += COMPONENT_HEIGHT + 30

    else:
        # Grid layout for many clusters
        clusters_per_row = 2
        cluster_width = 400
        cluster_height = 300

        for i, cluster_name in enumerate(clusters):
            row = i // clusters_per_row
            col = i % clusters_per_row

            cluster_x = 100 + col * (cluster_width + INTER_CLUSTER_SPACING)
            cluster_y = 200 + row * (cluster_height + INTER_CLUSTER_SPACING)

            # Position components within cluster
            components = clustered[cluster_name]
            comp_x = cluster_x + CLUSTER_PADDING
            comp_y = cluster_y + 50  # Leave space for cluster label

            for j, comp in enumerate(components):
                if j > 0 and j % 2 == 0:  # New row every 2 components
                    comp_x = cluster_x + CLUSTER_PADDING
                    comp_y += COMPONENT_HEIGHT + 20

                if comp.get("count", 1) > 1:
                    # Stack multiple instances vertically
                    for k in range(comp["count"]):
                        name = f"{comp['name']} {k+1}"
                        positions[name] = (comp_x, comp_y + k * 60)
                else:
                    positions[comp["name"]] = (comp_x, comp_y)

                comp_x += COMPONENT_WIDTH + 30

    # Position any unclustered components at the top
    if unclustered and clustered:
        x_offset = 100
        y_offset = 50
        for comp in unclustered:
            if comp.get("count", 1) > 1:
                for i in range(comp["count"]):
                    name = f"{comp['name']} {i+1}"
                    positions[name] = (x_offset + i * INSTANCE_SPACING, y_offset)
            else:
                positions[comp["name"]] = (x_offset, y_offset)
            x_offset += COMPONENT_WIDTH + 50

    return positions

def generate_excalidraw_json(architecture: Dict) -> Dict:
    """Generate Excalidraw JSON format"""

    # Initialize Excalidraw document structure
    excalidraw_data = {
        "type": "excalidraw",
        "version": 2,
        "source": "https://excalidraw.com",
        "elements": [],
        "appState": {
            "gridSize": None,
            "viewBackgroundColor": "#ffffff"
        },
        "files": {}
    }

    # Calculate positions
    positions = calculate_positions(architecture)

    # Track created elements
    element_ids = {}
    elements = []

    # Create clusters (groups) as rectangles with light background
    cluster_elements = {}
    for cluster_info in architecture.get("clusters", []):
        cluster_name = cluster_info["name"]
        cluster_label = cluster_info["label"]

        # Find components in this cluster to calculate group bounds
        cluster_components = [c for c in architecture["components"] if c.get("cluster") == cluster_name]
        if cluster_components:
            # Get all positions for components in this cluster (including multiple instances)
            all_positions = []
            for c in cluster_components:
                if c.get("count", 1) > 1:
                    for i in range(c["count"]):
                        instance_name = f"{c['name']} {i+1}"
                        if instance_name in positions:
                            all_positions.append(positions[instance_name])
                else:
                    if c["name"] in positions:
                        all_positions.append(positions[c["name"]])

            if all_positions:
                min_x = min(pos[0] for pos in all_positions) - 60
                min_y = min(pos[1] for pos in all_positions) - 60
                max_x = max(pos[0] for pos in all_positions) + 180
                max_y = max(pos[1] for pos in all_positions) + 140
            else:
                # Fallback if no positions found
                min_x, min_y, max_x, max_y = 100, 200, 400, 350

            cluster_element = {
                "type": "rectangle",
                "version": 1,
                "versionNonce": 1,
                "isDeleted": False,
                "id": str(uuid.uuid4()),
                "fillStyle": "solid",
                "strokeWidth": 2,
                "strokeStyle": "solid",
                "roughness": 1,
                "opacity": 30,
                "angle": 0,
                "x": min_x,
                "y": min_y,
                "strokeColor": "#666666",
                "backgroundColor": "#f5f5f5",
                "width": max_x - min_x,
                "height": max_y - min_y,
                "seed": 1,
                "groupIds": [],
                "frameId": None,
                "roundness": {"type": 3},
                "boundElements": [],
                "updated": 1,
                "link": None,
                "locked": False
            }

            # Add cluster label
            label_element = {
                "type": "text",
                "version": 1,
                "versionNonce": 1,
                "isDeleted": False,
                "id": str(uuid.uuid4()),
                "fillStyle": "solid",
                "strokeWidth": 1,
                "strokeStyle": "solid",
                "roughness": 1,
                "opacity": 100,
                "angle": 0,
                "x": min_x + 10,
                "y": min_y + 10,
                "strokeColor": "#1e1e1e",
                "backgroundColor": "transparent",
                "width": 100,
                "height": 25,
                "seed": 1,
                "groupIds": [],
                "frameId": None,
                "roundness": None,
                "boundElements": [],
                "updated": 1,
                "link": None,
                "locked": False,
                "fontSize": 16,
                "fontFamily": 1,
                "text": cluster_label,
                "textAlign": "left",
                "verticalAlign": "top",
                "containerId": None,
                "originalText": cluster_label,
                "lineHeight": 1.25,
                "baseline": 18
            }

            elements.append(cluster_element)
            elements.append(label_element)
            cluster_elements[cluster_name] = cluster_element["id"]

    # Create components
    for component in architecture.get("components", []):
        comp_type = component["type"].lower()
        comp_name = component["name"]
        comp_count = component.get("count", 1)
        cluster = component.get("cluster")

        if comp_type in AWS_SHAPES:
            shape_info = AWS_SHAPES[comp_type]

            if comp_count > 1:
                # Create multiple instances
                for i in range(comp_count):
                    instance_name = f"{comp_name} {i+1}"
                    x, y = positions.get(instance_name, (100 + i * 120, 100))

                    element_id = str(uuid.uuid4())
                    element_ids[instance_name] = element_id

                    # Create shape element
                    shape_element = {
                        "type": shape_info["type"],
                        "version": 1,
                        "versionNonce": 1,
                        "isDeleted": False,
                        "id": element_id,
                        "fillStyle": "solid",
                        "strokeWidth": 2,
                        "strokeStyle": "solid",
                        "roughness": 1,
                        "opacity": 100,
                        "angle": 0,
                        "x": x,
                        "y": y,
                        "strokeColor": shape_info["strokeColor"],
                        "backgroundColor": shape_info["backgroundColor"],
                        "width": shape_info["width"],
                        "height": shape_info["height"],
                        "seed": 1,
                        "groupIds": [],
                        "frameId": None,
                        "roundness": {"type": 3} if shape_info["type"] == "rectangle" else None,
                        "boundElements": [],
                        "updated": 1,
                        "link": None,
                        "locked": False
                    }

                    # Create text label
                    text_element = {
                        "type": "text",
                        "version": 1,
                        "versionNonce": 1,
                        "isDeleted": False,
                        "id": str(uuid.uuid4()),
                        "fillStyle": "solid",
                        "strokeWidth": 1,
                        "strokeStyle": "solid",
                        "roughness": 1,
                        "opacity": 100,
                        "angle": 0,
                        "x": x + 10,
                        "y": y + shape_info["height"] + 5,
                        "strokeColor": "#1e1e1e",
                        "backgroundColor": "transparent",
                        "width": shape_info["width"],
                        "height": 20,
                        "seed": 1,
                        "groupIds": [],
                        "frameId": None,
                        "roundness": None,
                        "boundElements": [],
                        "updated": 1,
                        "link": None,
                        "locked": False,
                        "fontSize": 12,
                        "fontFamily": 1,
                        "text": f"{shape_info['icon']} {instance_name}",
                        "textAlign": "center",
                        "verticalAlign": "top",
                        "containerId": None,
                        "originalText": f"{shape_info['icon']} {instance_name}",
                        "lineHeight": 1.25,
                        "baseline": 16
                    }

                    elements.append(shape_element)
                    elements.append(text_element)

                # Also track the base name for connections
                element_ids[comp_name] = element_ids[f"{comp_name} 1"]
            else:
                x, y = positions.get(comp_name, (100, 100))

                element_id = str(uuid.uuid4())
                element_ids[comp_name] = element_id

                # Create shape element
                shape_element = {
                    "type": shape_info["type"],
                    "version": 1,
                    "versionNonce": 1,
                    "isDeleted": False,
                    "id": element_id,
                    "fillStyle": "solid",
                    "strokeWidth": 2,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "angle": 0,
                    "x": x,
                    "y": y,
                    "strokeColor": shape_info["strokeColor"],
                    "backgroundColor": shape_info["backgroundColor"],
                    "width": shape_info["width"],
                    "height": shape_info["height"],
                    "seed": 1,
                    "groupIds": [],
                    "frameId": None,
                    "roundness": {"type": 3} if shape_info["type"] == "rectangle" else None,
                    "boundElements": [],
                    "updated": 1,
                    "link": None,
                    "locked": False
                }

                # Create text label
                text_element = {
                    "type": "text",
                    "version": 1,
                    "versionNonce": 1,
                    "isDeleted": False,
                    "id": str(uuid.uuid4()),
                    "fillStyle": "solid",
                    "strokeWidth": 1,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "angle": 0,
                    "x": x + 10,
                    "y": y + shape_info["height"] + 5,
                    "strokeColor": "#1e1e1e",
                    "backgroundColor": "transparent",
                    "width": shape_info["width"],
                    "height": 20,
                    "seed": 1,
                    "groupIds": [],
                    "frameId": None,
                    "roundness": None,
                    "boundElements": [],
                    "updated": 1,
                    "link": None,
                    "locked": False,
                    "fontSize": 12,
                    "fontFamily": 1,
                    "text": f"{shape_info['icon']} {comp_name}",
                    "textAlign": "center",
                    "verticalAlign": "top",
                    "containerId": None,
                    "originalText": f"{shape_info['icon']} {comp_name}",
                    "lineHeight": 1.25,
                    "baseline": 16
                }

                elements.append(shape_element)
                elements.append(text_element)

    # Create connections (arrows)
    for connection in architecture.get("connections", []):
        from_comp = connection["from"]
        to_comp = connection["to"]
        label = connection.get("label", "")

        if from_comp in element_ids and to_comp in element_ids:
            # Create arrow element
            arrow_element = {
                "type": "arrow",
                "version": 1,
                "versionNonce": 1,
                "isDeleted": False,
                "id": str(uuid.uuid4()),
                "fillStyle": "solid",
                "strokeWidth": 2,
                "strokeStyle": "solid",
                "roughness": 1,
                "opacity": 100,
                "angle": 0,
                "x": 0,  # Will be calculated based on connected elements
                "y": 0,  # Will be calculated based on connected elements
                "strokeColor": "#1e1e1e",
                "backgroundColor": "transparent",
                "width": 0,
                "height": 0,
                "seed": 1,
                "groupIds": [],
                "frameId": None,
                "roundness": {"type": 2},
                "boundElements": [],
                "updated": 1,
                "link": None,
                "locked": False,
                "startBinding": {
                    "elementId": element_ids[from_comp],
                    "focus": 0,
                    "gap": 1
                },
                "endBinding": {
                    "elementId": element_ids[to_comp],
                    "focus": 0,
                    "gap": 1
                },
                "lastCommittedPoint": None,
                "startArrowhead": None,
                "endArrowhead": "arrow",
                "points": [[0, 0], [100, 0]]  # Simple horizontal line, Excalidraw will adjust
            }

            elements.append(arrow_element)

            # Add label if provided
            if label:
                label_element = {
                    "type": "text",
                    "version": 1,
                    "versionNonce": 1,
                    "isDeleted": False,
                    "id": str(uuid.uuid4()),
                    "fillStyle": "solid",
                    "strokeWidth": 1,
                    "strokeStyle": "solid",
                    "roughness": 1,
                    "opacity": 100,
                    "angle": 0,
                    "x": 50,  # Midpoint of arrow
                    "y": -10,  # Above the arrow
                    "strokeColor": "#1e1e1e",
                    "backgroundColor": "#ffffff",
                    "width": len(label) * 8,
                    "height": 20,
                    "seed": 1,
                    "groupIds": [],
                    "frameId": None,
                    "roundness": None,
                    "boundElements": [],
                    "updated": 1,
                    "link": None,
                    "locked": False,
                    "fontSize": 12,
                    "fontFamily": 1,
                    "text": label,
                    "textAlign": "center",
                    "verticalAlign": "middle",
                    "containerId": None,
                    "originalText": label,
                    "lineHeight": 1.25,
                    "baseline": 16
                }
                elements.append(label_element)

    # Add all elements to the excalidraw data
    excalidraw_data["elements"] = elements

    return excalidraw_data

def open_in_excalidraw_desktop(file_path: str):
    """Open the diagram in Excalidraw desktop app if installed, otherwise open in browser"""
    try:
        system = platform.system()
        if system == "Darwin":  # macOS
            # Try Excalidraw desktop first, fallback to browser
            try:
                subprocess.run(["open", "-a", "Excalidraw", file_path], check=True)
            except:
                subprocess.run(["open", "https://excalidraw.com"], check=True)
        elif system == "Windows":
            # Try opening with default app, fallback to browser
            try:
                subprocess.run(["start", file_path], shell=True, check=True)
            except:
                subprocess.run(["start", "https://excalidraw.com"], shell=True, check=True)
        elif system == "Linux":
            # Try opening with default app, fallback to browser
            try:
                subprocess.run(["xdg-open", file_path], check=True)
            except:
                subprocess.run(["xdg-open", "https://excalidraw.com"], check=True)
        return True
    except Exception as e:
        print(f"Could not open Excalidraw: {e}")
        return False

@app.post("/generate", response_model=ArchitectureResponse)
async def generate_architecture(request: ArchitectureRequest):
    """Generate an AWS architecture diagram in Excalidraw format"""

    try:
        # Parse the architecture
        architecture = parse_architecture_with_llm(request.description)

        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_path = f"diagrams/aws_architecture_{timestamp}"

        # Create diagrams directory if it doesn't exist
        os.makedirs("diagrams", exist_ok=True)

        # Generate Excalidraw JSON
        excalidraw_data = generate_excalidraw_json(architecture)

        # Save Excalidraw file
        excalidraw_path = f"{base_path}.excalidraw"
        with open(excalidraw_path, 'w', encoding='utf-8') as f:
            json.dump(excalidraw_data, f, indent=2)

        # Open in desktop app if requested
        if request.open_desktop:
            open_in_excalidraw_desktop(os.path.abspath(excalidraw_path))

        # Create embed URL for web preview
        embed_url = f"https://excalidraw.com/#json={base_path.split('/')[-1]}"

        # Create explanation
        explanation = f"Generated AWS architecture with {len(architecture['components'])} components"
        if architecture.get('clusters'):
            explanation += f" organized in {len(architecture['clusters'])} tiers"

        return ArchitectureResponse(
            diagram_path=excalidraw_path,
            excalidraw_path=excalidraw_path,
            components=architecture,
            explanation=explanation,
            embed_url=embed_url
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/diagram/{filename}")
async def get_diagram(filename: str):
    """Serve the generated diagram file"""
    file_path = f"diagrams/{filename}"
    if os.path.exists(file_path):
        media_type = "application/json" if filename.endswith(".excalidraw") else "application/octet-stream"
        return FileResponse(
            file_path,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    else:
        raise HTTPException(status_code=404, detail="Diagram not found")

@app.post("/export")
async def export_diagram(request: Dict):
    """Export diagram to different formats (note: Excalidraw exports require manual action)"""
    try:
        excalidraw_path = request.get("excalidraw_path")
        format = request.get("format", "png")

        if not os.path.exists(excalidraw_path):
            raise HTTPException(status_code=404, detail="Diagram file not found")

        # For Excalidraw, we can't automatically export to other formats
        # Users need to open the file in Excalidraw and export manually
        return {
            "message": "To export to other formats, please:",
            "steps": [
                f"1. Open {excalidraw_path} in Excalidraw",
                "2. Use File > Export image to save as PNG/SVG",
                "3. Or copy the URL and open in browser: https://excalidraw.com"
            ],
            "excalidraw_path": excalidraw_path,
            "requested_format": format
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/")
async def root():
    return {
        "message": "AWS Architecture Diagram Generator - Excalidraw Version",
        "features": [
            "Generate Excalidraw diagrams from text descriptions",
            "Open directly in Excalidraw desktop app or browser",
            "Beautiful hand-drawn style diagrams",
            "JSON format for easy sharing and editing",
            "Claude AI-powered architecture parsing"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
