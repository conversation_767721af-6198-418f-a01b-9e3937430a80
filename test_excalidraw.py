#!/usr/bin/env python3
"""
Test script for the Excalidraw diagram generation
"""

import json
import sys
import os

# Add the current directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app_drawio import generate_excalidraw_json, parse_architecture_with_patterns

def test_excalidraw_generation():
    """Test the Excalidraw generation with a simple architecture"""
    
    # Test architecture
    test_architecture = {
        "title": "Simple Web Application",
        "components": [
            {"type": "alb", "name": "Load Balancer", "count": 1},
            {"type": "ec2", "name": "Web Server", "count": 2, "cluster": "web_tier"},
            {"type": "rds", "name": "Database", "count": 1, "cluster": "data_tier"}
        ],
        "connections": [
            {"from": "Load Balancer", "to": "Web Server", "label": "HTTP"},
            {"from": "Web Server", "to": "Database", "label": "SQL"}
        ],
        "clusters": [
            {"name": "web_tier", "label": "Web Tier"},
            {"name": "data_tier", "label": "Data Tier"}
        ]
    }
    
    print("🧪 Testing Excalidraw generation...")
    
    try:
        # Generate the Excalidraw JSON
        excalidraw_data = generate_excalidraw_json(test_architecture)
        
        # Save to a test file
        output_file = "test_diagram.excalidraw"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(excalidraw_data, f, indent=2)
        
        print(f"✅ Successfully generated Excalidraw diagram: {output_file}")
        print(f"📊 Generated {len(excalidraw_data['elements'])} elements")
        
        # Print some stats
        element_types = {}
        for element in excalidraw_data['elements']:
            element_type = element['type']
            element_types[element_type] = element_types.get(element_type, 0) + 1
        
        print("📈 Element breakdown:")
        for elem_type, count in element_types.items():
            print(f"   - {elem_type}: {count}")
        
        print(f"\n🌐 You can open this file at: https://excalidraw.com")
        print(f"📁 Or open the local file: {os.path.abspath(output_file)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating Excalidraw diagram: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pattern_parsing():
    """Test the pattern-based parsing"""
    
    print("\n🧪 Testing pattern-based parsing...")
    
    test_descriptions = [
        "A serverless API with Lambda functions and DynamoDB",
        "A web application with load balancer and database",
        "A microservices architecture with containers"
    ]
    
    for desc in test_descriptions:
        print(f"\n📝 Testing: '{desc}'")
        try:
            architecture = parse_architecture_with_patterns(desc)
            print(f"✅ Parsed successfully: {architecture['title']}")
            print(f"   Components: {len(architecture['components'])}")
            print(f"   Connections: {len(architecture['connections'])}")
        except Exception as e:
            print(f"❌ Error parsing: {e}")

if __name__ == "__main__":
    print("🚀 Excalidraw Diagram Generator Test")
    print("=" * 50)
    
    # Test pattern parsing
    test_pattern_parsing()
    
    # Test Excalidraw generation
    success = test_excalidraw_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("💥 Some tests failed!")
        sys.exit(1)
