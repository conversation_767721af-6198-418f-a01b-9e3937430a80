# AWS Architecture Diagram Generator - Excalidraw Version

Generate beautiful, hand-drawn style AWS architecture diagrams from plain English descriptions using Claude AI and Excalidraw.

## 🎨 Features

- **Natural Language Input**: Describe your AWS architecture in plain English
- **Claude AI-Powered**: Uses Anthrop<PERSON>'s Claude for intelligent architecture parsing
- **Excalidraw Format**: Generates beautiful hand-drawn style diagrams
- **Interactive Diagrams**: Open directly in Excalidraw for editing
- **Component Library**: Supports major AWS services with icons
- **Cluster Support**: Automatically groups related components
- **JSON Export**: Easy to share and version control

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Anthropic API key (for <PERSON>)

### Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up your API key:**
   ```bash
   export ANTHROPIC_API_KEY="your-claude-api-key-here"
   ```
   
   Or create a `.env` file:
   ```
   ANTHROPIC_API_KEY=your-claude-api-key-here
   ```

3. **Run the server:**
   ```bash
   python app_drawio.py
   ```

4. **Test the generation:**
   ```bash
   python test_excalidraw.py
   ```

## 📖 Usage

### API Endpoints

#### Generate Diagram
```bash
POST /generate
```

**Request:**
```json
{
  "description": "A serverless API with Lambda, API Gateway, and DynamoDB",
  "export_format": "excalidraw",
  "open_desktop": false
}
```

**Response:**
```json
{
  "diagram_path": "diagrams/aws_architecture_20231201_143022.excalidraw",
  "excalidraw_path": "diagrams/aws_architecture_20231201_143022.excalidraw",
  "components": {...},
  "explanation": "Generated AWS architecture with 3 components organized in 2 tiers",
  "embed_url": "https://excalidraw.com/#json=aws_architecture_20231201_143022"
}
```

#### Get Diagram
```bash
GET /diagram/{filename}
```

### Example Descriptions

Try these example descriptions:

- **Serverless API**: "A serverless API with Lambda, API Gateway, and DynamoDB"
- **Web Application**: "A three-tier web application with load balancer, EC2 instances, and RDS"
- **Data Pipeline**: "A data processing pipeline with Kinesis, Lambda, and S3"
- **Microservices**: "A microservices architecture with ECS, ALB, and multiple databases"

### Supported AWS Services

| Service | Icon | Type | Color |
|---------|------|------|-------|
| EC2 | 💻 | Rectangle | Orange |
| Lambda | λ | Rectangle | Orange |
| RDS | 🗄️ | Ellipse | Blue |
| DynamoDB | 📊 | Ellipse | Blue |
| S3 | 🪣 | Rectangle | Green |
| ALB | ⚖️ | Diamond | Orange |
| CloudFront | 🌐 | Rectangle | Orange |
| ElastiCache | ⚡ | Ellipse | Blue |
| SQS | 📬 | Rectangle | Orange |
| SNS | 📢 | Rectangle | Orange |
| Kinesis | 🌊 | Rectangle | Orange |
| ECS | 🐳 | Rectangle | Orange |
| Cognito | 👤 | Rectangle | Pink |
| API Gateway | 🚪 | Rectangle | Orange |

## 🎯 Opening Diagrams

### In Excalidraw Web App
1. Go to [excalidraw.com](https://excalidraw.com)
2. Click "Open" and select your `.excalidraw` file
3. Edit, export, or share as needed

### In Excalidraw Desktop (if installed)
The API can automatically open diagrams in the desktop app by setting `open_desktop: true`.

### Manual Export
From Excalidraw:
1. File → Export image
2. Choose PNG, SVG, or other formats
3. Download or copy to clipboard

## 🔧 Configuration

### Environment Variables

- `ANTHROPIC_API_KEY`: Your Claude API key (required for AI parsing)
- `PORT`: Server port (default: 8000)

### Fallback Mode

If no Claude API key is provided, the system falls back to pattern-based parsing using keywords.

## 🧪 Testing

Run the test script to verify everything works:

```bash
python test_excalidraw.py
```

This will:
- Test pattern-based parsing
- Generate a sample Excalidraw diagram
- Show element statistics
- Create `test_diagram.excalidraw` file

## 📁 File Structure

```
├── app_drawio.py              # Main FastAPI application
├── test_excalidraw.py         # Test script
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── diagrams/                 # Generated diagrams directory
└── README_EXCALIDRAW.md      # This file
```

## 🎨 Diagram Features

### Visual Elements
- **Hand-drawn style**: Excalidraw's signature sketchy appearance
- **Color coding**: Different colors for compute, storage, networking
- **Icons**: Emoji icons for easy service identification
- **Clusters**: Grouped components with labeled boundaries
- **Arrows**: Connections between services with optional labels

### Layout
- **Automatic positioning**: Smart component placement
- **Cluster grouping**: Related services grouped together
- **Spacing**: Proper spacing for readability
- **Scalability**: Handles multiple instances of services

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with `python test_excalidraw.py`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Excalidraw](https://excalidraw.com) for the amazing diagramming tool
- [Anthropic](https://anthropic.com) for Claude AI
- [FastAPI](https://fastapi.tiangolo.com) for the web framework
