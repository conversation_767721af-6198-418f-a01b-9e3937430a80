import os
import re
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
from diagrams import Diagram, Cluster, Edge
from diagrams.aws.compute import EC2, Lamb<PERSON>, ECS, Fargate
from diagrams.aws.database import RDS, Dynamodb, ElastiCache
from diagrams.aws.network import ELB, ALB, CloudFront, Route53
from diagrams.aws.storage import S3
from diagrams.aws.integration import SQS, SNS
from diagrams.aws.analytics import Kinesis
from diagrams.aws.security import IAM, Cognito

app = FastAPI()

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ArchitectureRequest(BaseModel):
    description: str
    pattern: Optional[str] = None  # optional pattern hint

class ArchitectureResponse(BaseModel):
    diagram_path: str
    components: Dict
    explanation: str

# Predefined architecture patterns
ARCHITECTURE_PATTERNS = {
    "web_app": {
        "title": "Scalable Web Application",
        "components": [
            {"type": "alb", "name": "Application Load Balancer", "count": 1},
            {"type": "ec2", "name": "Web Server", "count": 3, "cluster": "web_tier"},
            {"type": "rds", "name": "Primary Database", "count": 1, "cluster": "data_tier"},
            {"type": "rds", "name": "Read Replica", "count": 1, "cluster": "data_tier"},
            {"type": "elasticache", "name": "Redis Cache", "count": 1, "cluster": "cache_tier"},
            {"type": "s3", "name": "Static Assets", "count": 1}
        ],
        "connections": [
            {"from": "Application Load Balancer", "to": "Web Server"},
            {"from": "Web Server", "to": "Primary Database", "label": "read/write"},
            {"from": "Web Server", "to": "Read Replica", "label": "read"},
            {"from": "Web Server", "to": "Redis Cache"},
            {"from": "Web Server", "to": "Static Assets"}
        ],
        "clusters": [
            {"name": "web_tier", "label": "Web Tier"},
            {"name": "data_tier", "label": "Data Tier"},
            {"name": "cache_tier", "label": "Cache Layer"}
        ]
    },
    "serverless_api": {
        "title": "Serverless API Architecture",
        "components": [
            {"type": "cloudfront", "name": "CloudFront CDN", "count": 1},
            {"type": "alb", "name": "API Gateway", "count": 1},
            {"type": "lambda", "name": "API Function", "count": 3, "cluster": "compute"},
            {"type": "dynamodb", "name": "NoSQL Database", "count": 1, "cluster": "data"},
            {"type": "s3", "name": "File Storage", "count": 1, "cluster": "storage"},
            {"type": "cognito", "name": "User Authentication", "count": 1}
        ],
        "connections": [
            {"from": "CloudFront CDN", "to": "API Gateway"},
            {"from": "API Gateway", "to": "API Function"},
            {"from": "API Function", "to": "NoSQL Database"},
            {"from": "API Function", "to": "File Storage"},
            {"from": "API Gateway", "to": "User Authentication", "label": "auth"}
        ],
        "clusters": [
            {"name": "compute", "label": "Compute Layer"},
            {"name": "data", "label": "Data Layer"},
            {"name": "storage", "label": "Storage"}
        ]
    },
    "data_pipeline": {
        "title": "Real-time Data Processing Pipeline",
        "components": [
            {"type": "kinesis", "name": "Data Stream", "count": 1, "cluster": "ingestion"},
            {"type": "lambda", "name": "Stream Processor", "count": 2, "cluster": "processing"},
            {"type": "dynamodb", "name": "Real-time Store", "count": 1, "cluster": "storage"},
            {"type": "s3", "name": "Data Lake", "count": 1, "cluster": "storage"},
            {"type": "lambda", "name": "Batch Processor", "count": 1, "cluster": "processing"}
        ],
        "connections": [
            {"from": "Data Stream", "to": "Stream Processor", "label": "real-time"},
            {"from": "Stream Processor", "to": "Real-time Store"},
            {"from": "Stream Processor", "to": "Data Lake"},
            {"from": "Data Lake", "to": "Batch Processor", "label": "batch"},
            {"from": "Batch Processor", "to": "Real-time Store", "label": "aggregated"}
        ],
        "clusters": [
            {"name": "ingestion", "label": "Data Ingestion"},
            {"name": "processing", "label": "Processing"},
            {"name": "storage", "label": "Storage"}
        ]
    },
    "microservices": {
        "title": "Microservices Architecture",
        "components": [
            {"type": "alb", "name": "API Gateway", "count": 1},
            {"type": "ecs", "name": "User Service", "count": 1, "cluster": "services"},
            {"type": "ecs", "name": "Order Service", "count": 1, "cluster": "services"},
            {"type": "ecs", "name": "Product Service", "count": 1, "cluster": "services"},
            {"type": "rds", "name": "User DB", "count": 1, "cluster": "databases"},
            {"type": "rds", "name": "Order DB", "count": 1, "cluster": "databases"},
            {"type": "dynamodb", "name": "Product DB", "count": 1, "cluster": "databases"},
            {"type": "sqs", "name": "Message Queue", "count": 1, "cluster": "messaging"},
            {"type": "sns", "name": "Event Bus", "count": 1, "cluster": "messaging"}
        ],
        "connections": [
            {"from": "API Gateway", "to": "User Service"},
            {"from": "API Gateway", "to": "Order Service"},
            {"from": "API Gateway", "to": "Product Service"},
            {"from": "User Service", "to": "User DB"},
            {"from": "Order Service", "to": "Order DB"},
            {"from": "Product Service", "to": "Product DB"},
            {"from": "Order Service", "to": "Message Queue"},
            {"from": "Message Queue", "to": "Product Service"},
            {"from": "Order Service", "to": "Event Bus"},
            {"from": "Event Bus", "to": "User Service"}
        ],
        "clusters": [
            {"name": "services", "label": "Microservices"},
            {"name": "databases", "label": "Databases"},
            {"name": "messaging", "label": "Messaging"}
        ]
    }
}

def detect_pattern(description: str) -> str:
    """Simple pattern detection based on keywords"""
    description_lower = description.lower()
    
    if any(word in description_lower for word in ["serverless", "lambda", "api gateway"]):
        return "serverless_api"
    elif any(word in description_lower for word in ["microservice", "ecs", "containers"]):
        return "microservices"
    elif any(word in description_lower for word in ["stream", "kinesis", "real-time", "pipeline"]):
        return "data_pipeline"
    else:
        return "web_app"  # default

def generate_diagram(architecture: Dict, output_path: str):
    """Generate the actual diagram using the diagrams library"""
    
    # Component class mapping
    component_classes = {
        "ec2": EC2,
        "lambda": Lambda,
        "ecs": ECS,
        "fargate": Fargate,
        "rds": RDS,
        "dynamodb": Dynamodb,
        "elasticache": ElastiCache,
        "s3": S3,
        "elb": ELB,
        "alb": ALB,
        "cloudfront": CloudFront,
        "route53": Route53,
        "sqs": SQS,
        "sns": SNS,
        "kinesis": Kinesis,
        "iam": IAM,
        "cognito": Cognito,
    }
    
    with Diagram(architecture.get("title", "AWS Architecture"), 
                 filename=output_path, 
                 show=False, 
                 direction="TB",
                 graph_attr={"fontsize": "12", "bgcolor": "white"}):
        
        components_dict = {}
        clusters_dict = {}
        
        # Create clusters first
        for cluster_info in architecture.get("clusters", []):
            cluster_name = cluster_info["name"]
            cluster_label = cluster_info["label"]
            clusters_dict[cluster_name] = Cluster(cluster_label)
        
        # Create components
        for component in architecture.get("components", []):
            comp_type = component["type"].lower()
            comp_name = component["name"]
            comp_count = component.get("count", 1)
            cluster_name = component.get("cluster")
            
            if comp_type in component_classes:
                ComponentClass = component_classes[comp_type]
                
                if cluster_name and cluster_name in clusters_dict:
                    with clusters_dict[cluster_name]:
                        if comp_count > 1:
                            instances = [ComponentClass(f"{comp_name} {i+1}") for i in range(comp_count)]
                            components_dict[comp_name] = instances
                        else:
                            components_dict[comp_name] = ComponentClass(comp_name)
                else:
                    if comp_count > 1:
                        instances = [ComponentClass(f"{comp_name} {i+1}") for i in range(comp_count)]
                        components_dict[comp_name] = instances
                    else:
                        components_dict[comp_name] = ComponentClass(comp_name)
        
        # Create connections
        for connection in architecture.get("connections", []):
            from_comp = connection["from"]
            to_comp = connection["to"]
            label = connection.get("label", "")
            
            if from_comp in components_dict and to_comp in components_dict:
                from_node = components_dict[from_comp]
                to_node = components_dict[to_comp]
                
                if isinstance(from_node, list):
                    from_node = from_node[0]
                if isinstance(to_node, list):
                    for node in to_node:
                        if label:
                            from_node >> Edge(label=label) >> node
                        else:
                            from_node >> node
                else:
                    if label:
                        from_node >> Edge(label=label) >> to_node
                    else:
                        from_node >> to_node

@app.post("/generate", response_model=ArchitectureResponse)
async def generate_architecture(request: ArchitectureRequest):
    """Generate an AWS architecture diagram from a description"""
    
    try:
        # Detect pattern from description or use provided pattern
        pattern_name = request.pattern or detect_pattern(request.description)
        
        # Get the architecture pattern
        if pattern_name not in ARCHITECTURE_PATTERNS:
            pattern_name = "web_app"  # fallback to default
        
        architecture = ARCHITECTURE_PATTERNS[pattern_name]
        
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"diagrams/aws_architecture_{timestamp}"
        
        # Create diagrams directory if it doesn't exist
        os.makedirs("diagrams", exist_ok=True)
        
        # Generate the diagram
        generate_diagram(architecture, output_path)
        
        # Create explanation
        explanation = f"Generated {architecture['title']} with {len(architecture['components'])} components"
        if architecture.get('clusters'):
            explanation += f" organized in {len(architecture['clusters'])} tiers"
        
        return ArchitectureResponse(
            diagram_path=f"{output_path}.png",
            components=architecture,
            explanation=explanation
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/diagram/{filename}")
async def get_diagram(filename: str):
    """Serve the generated diagram"""
    file_path = f"diagrams/{filename}"
    if os.path.exists(file_path):
        return FileResponse(file_path, media_type="image/png")
    else:
        raise HTTPException(status_code=404, detail="Diagram not found")

@app.get("/patterns")
async def list_patterns():
    """List available architecture patterns"""
    return {
        "patterns": [
            {
                "id": key,
                "title": value["title"],
                "component_count": len(value["components"])
            }
            for key, value in ARCHITECTURE_PATTERNS.items()
        ]
    }

@app.get("/")
async def root():
    return {"message": "AWS Architecture Diagram Generator API (Simple Version)"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)